// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Typography Snapshots for small breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders width desktopBreakpoint as LARGE state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders width desktopBreakpoint as LARGE state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders width desktopBreakpoint as LARGE state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders width desktopBreakpoint as LARGE state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders width desktopBreakpoint as LARGE state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders width desktopBreakpoint as LARGE state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders width desktopBreakpoint as LARGE state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders width desktopBreakpoint as LARGE state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders width desktopBreakpoint as LARGE state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders width desktopBreakpoint as LARGE state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders width desktopBreakpoint as LARGE state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.08960000000000001vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders width desktopBreakpoint as LARGE state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.08960000000000001vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body1 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body1 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body1 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body1 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body1 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body1 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body1 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body1 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body1 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body1 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body1 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body1 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body2 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body2 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body2 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body2 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body2 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body2 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body2 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body2 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body2 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body2 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body2 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body2 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body3 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.6px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body3 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.6px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body3 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body3 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body3 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body3 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body3 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body3 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body3 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body3 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body3 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body3 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body4 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body4 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body4 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body4 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body4 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body4 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.6666666666666667;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body4 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body4 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body4 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body4 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body4 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body4 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body5 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body5 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body5 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body5 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body5 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body5 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body5 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body5 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body5 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body5 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body5 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with body5 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with custom HTML element state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with custom HTML element state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with custom HTML element state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with custom HTML element state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with custom HTML element state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with custom HTML element state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with custom HTML element state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with custom HTML element state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with custom HTML element state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with custom HTML element state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with custom HTML element state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with custom HTML element state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow1 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow1 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow1 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow1 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow1 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow1 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow1 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow1 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow1 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow1 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow1 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow1 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow2 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 2;
  letter-spacing: 1.1px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow2 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 2;
  letter-spacing: 1.1px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow2 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow2 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow2 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow2 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow2 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow2 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow2 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow2 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow2 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 19px;
  line-height: 1;
  letter-spacing: 0.76px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow2 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 19px;
  line-height: 1;
  letter-spacing: 0.76px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow3 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.68px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow3 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.68px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow3 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow3 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow3 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow3 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow3 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow3 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow3 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow3 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow3 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.56px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with eyebrow3 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.56px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline1 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 1.37px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline1 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 1.37px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline1 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline1 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline1 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline1 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline1 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline1 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline1 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline1 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline1 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 68px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline1 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 68px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline2 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: 1.7px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline2 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: 1.7px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline2 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline2 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline2 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline2 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline2 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline2 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline2 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline2 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline2 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline2 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline3 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline3 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline3 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline3 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline3 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline3 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.0625;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline3 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline3 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline3 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline3 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline3 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 43px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline3 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 43px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline4 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 1.8px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline4 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 1.8px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline4 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline4 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline4 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline4 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline4 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline4 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline4 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline4 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline4 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 0.72px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline4 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 0.72px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline5 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.1818181818181819;
  letter-spacing: 2.2px;
  text-transform: uppercase;
  font-weight: 600;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline5 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.1818181818181819;
  letter-spacing: 2.2px;
  text-transform: uppercase;
  font-weight: 600;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline5 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline5 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline5 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline5 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline5 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline5 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline5 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline5 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline5 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline5 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline6 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.8px;
  text-transform: none;
  font-weight: 600;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline6 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.8px;
  text-transform: none;
  font-weight: 600;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline6 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline6 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline6 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline6 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline6 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline6 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline6 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline6 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline6 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.96px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline6 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.96px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline7 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.1px;
  text-transform: uppercase;
  font-weight: 700;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline7 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.1px;
  text-transform: uppercase;
  font-weight: 700;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline7 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline7 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline7 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline7 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.5px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline7 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline7 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline7 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline7 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline7 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.48px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with headline7 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.48px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo1 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo1 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo1 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo1 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo1 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo1 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 400;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo1 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo1 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo1 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo1 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo1 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo1 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo2 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo2 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo2 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo2 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo2 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo2 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo2 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo2 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo2 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo2 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo2 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promo2 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promoAlt1 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promoAlt1 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promoAlt1 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promoAlt1 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promoAlt1 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promoAlt1 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promoAlt1 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promoAlt1 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promoAlt1 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promoAlt1 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promoAlt1 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with promoAlt1 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom max fontSize state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom max fontSize state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom max fontSize state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom max fontSize state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom max fontSize state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom max fontSize state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom max fontSize state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom max fontSize state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom max fontSize state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom max fontSize state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom max fontSize state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.08960000000000001vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom max fontSize state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.08960000000000001vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom min fontSize state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom min fontSize state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom min fontSize state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom min fontSize state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom min fontSize state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom min fontSize state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom min fontSize state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom min fontSize state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom min fontSize state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom min fontSize state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom min fontSize state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.08960000000000001vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and custom min fontSize state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.08960000000000001vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and disabling infinite scaling state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.21333333333333335vw, 0.8px);
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and disabling infinite scaling state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.21333333333333335vw, 0.8px);
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and disabling infinite scaling state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and disabling infinite scaling state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and disabling infinite scaling state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and disabling infinite scaling state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and disabling infinite scaling state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and disabling infinite scaling state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and disabling infinite scaling state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and disabling infinite scaling state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and disabling infinite scaling state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 0.336px);
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable and disabling infinite scaling state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 0.336px);
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.21333333333333335vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable text state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable text state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable text state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable text state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable text state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.08960000000000001vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with scalable text state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5;
  letter-spacing: 0.08960000000000001vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead1 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 0.32px;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead1 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 0.32px;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead1 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead1 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead1 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead1 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead1 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead1 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead1 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead1 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead1 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead1 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead2 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead2 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead2 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead2 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead2 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead2 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead2 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead2 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead2 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead2 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead2 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead2 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead3 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 13px;
  line-height: 1;
  letter-spacing: 0.97px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead3 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 13px;
  line-height: 1;
  letter-spacing: 0.97px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead3 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead3 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead3 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead3 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead3 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead3 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead3 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead3 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead3 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for small breakpoint renders with subhead3 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0.72px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0.72px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders width desktopBreakpoint as LARGE state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0703125vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders width desktopBreakpoint as LARGE state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0703125vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders width desktopBreakpoint as LARGE state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders width desktopBreakpoint as LARGE state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders width desktopBreakpoint as LARGE state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders width desktopBreakpoint as LARGE state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders width desktopBreakpoint as LARGE state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders width desktopBreakpoint as LARGE state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders width desktopBreakpoint as LARGE state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders width desktopBreakpoint as LARGE state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders width desktopBreakpoint as LARGE state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5;
  letter-spacing: 0.056249999999999994vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders width desktopBreakpoint as LARGE state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5;
  letter-spacing: 0.056249999999999994vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body1 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body1 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body1 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body1 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body1 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body1 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body1 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body1 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body1 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body1 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body1 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0.72px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body1 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0.72px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body2 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body2 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body2 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body2 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body2 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body2 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.4444444444444444;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body2 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body2 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body2 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body2 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body2 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.64px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body2 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.64px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body3 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body3 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body3 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body3 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body3 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body3 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body3 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body3 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body3 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body3 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body3 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.56px;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body3 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.56px;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body4 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body4 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body4 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body4 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body4 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body4 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body4 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body4 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body4 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body4 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body4 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body4 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body5 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body5 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body5 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body5 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body5 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body5 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body5 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body5 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body5 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body5 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body5 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with body5 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with custom HTML element state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with custom HTML element state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with custom HTML element state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with custom HTML element state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with custom HTML element state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with custom HTML element state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with custom HTML element state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with custom HTML element state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with custom HTML element state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with custom HTML element state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with custom HTML element state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0.72px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with custom HTML element state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0.72px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <p
      class="emotion-0"
    >
      Hello World!
    </p>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow1 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1.588235294117647;
  letter-spacing: 1.7px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow1 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1.588235294117647;
  letter-spacing: 1.7px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow1 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow1 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow1 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow1 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow1 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow1 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow1 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow1 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow1 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow1 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow2 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 1.6px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow2 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 1.6px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow2 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow2 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow2 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow2 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow2 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow2 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow2 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow2 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow2 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow2 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow3 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.8333333333333333;
  letter-spacing: 0.82px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow3 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.8333333333333333;
  letter-spacing: 0.82px;
  font-weight: 700;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow3 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow3 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow3 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow3 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1;
  letter-spacing: 1.5px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow3 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow3 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow3 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow3 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow3 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1;
  letter-spacing: 0.68px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with eyebrow3 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1;
  letter-spacing: 0.68px;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline1 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 2.4px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline1 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 2.4px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline1 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline1 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline1 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline1 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 96px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline1 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline1 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline1 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline1 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline1 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 118px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline1 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 118px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline2 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 56px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline2 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 56px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline2 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline2 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline2 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline2 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline2 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline2 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline2 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline2 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline2 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline2 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline3 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 2.5px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline3 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 2.5px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline3 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline3 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline3 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline3 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline3 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline3 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline3 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline3 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline3 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 72px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline3 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 72px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline4 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline4 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline4 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline4 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline4 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline4 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline4 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline4 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline4 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline4 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline4 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 1.28px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline4 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: 1.28px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline5 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 3.6px;
  text-transform: uppercase;
  font-weight: 600;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline5 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 3.6px;
  text-transform: uppercase;
  font-weight: 600;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline5 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline5 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline5 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline5 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1.125;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline5 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline5 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline5 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline5 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline5 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 2.88px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline5 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 48px;
  line-height: 1;
  letter-spacing: 2.88px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline6 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.1428571428571428;
  letter-spacing: 1.51px;
  text-transform: none;
  font-weight: 600;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline6 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.1428571428571428;
  letter-spacing: 1.51px;
  text-transform: none;
  font-weight: 600;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline6 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline6 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline6 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline6 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline6 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline6 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline6 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline6 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline6 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1.1;
  letter-spacing: 2.4px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline6 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1.1;
  letter-spacing: 2.4px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline7 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.2;
  letter-spacing: 1.3px;
  text-transform: uppercase;
  font-weight: 700;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline7 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.2;
  letter-spacing: 1.3px;
  text-transform: uppercase;
  font-weight: 700;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline7 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline7 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline7 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline7 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.4;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline7 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline7 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline7 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline7 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline7 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1;
  letter-spacing: 1.28px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with headline7 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  line-height: 1;
  letter-spacing: 1.28px;
  font-weight: 700;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo1 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo1 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo1 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo1 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo1 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo1 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo1 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo1 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo1 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo1 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo1 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo1 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo2 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo2 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo2 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo2 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo2 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo2 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo2 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo2 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo2 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo2 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo2 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promo2 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promoAlt1 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promoAlt1 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promoAlt1 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promoAlt1 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promoAlt1 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promoAlt1 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promoAlt1 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promoAlt1 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promoAlt1 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promoAlt1 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promoAlt1 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with promoAlt1 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom max fontSize state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0703125vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom max fontSize state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0703125vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom max fontSize state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom max fontSize state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom max fontSize state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom max fontSize state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom max fontSize state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom max fontSize state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom max fontSize state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom max fontSize state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom max fontSize state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5;
  letter-spacing: 0.056249999999999994vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom max fontSize state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5;
  letter-spacing: 0.056249999999999994vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom min fontSize state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0703125vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom min fontSize state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0703125vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom min fontSize state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom min fontSize state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom min fontSize state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom min fontSize state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom min fontSize state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom min fontSize state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom min fontSize state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom min fontSize state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom min fontSize state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5;
  letter-spacing: 0.056249999999999994vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and custom min fontSize state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5;
  letter-spacing: 0.056249999999999994vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and disabling infinite scaling state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.0703125vw, 0.9px);
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and disabling infinite scaling state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.0703125vw, 0.9px);
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and disabling infinite scaling state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and disabling infinite scaling state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and disabling infinite scaling state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and disabling infinite scaling state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and disabling infinite scaling state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and disabling infinite scaling state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and disabling infinite scaling state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and disabling infinite scaling state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and disabling infinite scaling state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable and disabling infinite scaling state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.40625vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.056249999999999994vw, 0.72px);
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable text state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0703125vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable text state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0703125vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable text state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable text state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable text state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable text state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable text state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable text state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable text state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable text state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable text state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5;
  letter-spacing: 0.056249999999999994vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with scalable text state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.40625vw);
  line-height: 1.5;
  letter-spacing: 0.056249999999999994vw;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead1 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 1.2px;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead1 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 1.2px;
  font-weight: 400;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead1 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead1 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead1 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead1 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.3333333333333333;
  letter-spacing: 0.5px;
  font-weight: 300;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead1 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead1 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead1 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead1 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead1 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead1 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead2 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.36px;
  font-weight: 500;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead2 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.36px;
  font-weight: 500;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead2 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead2 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead2 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead2 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead2 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead2 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead2 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead2 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead2 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead2 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead3 variant state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.7px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead3 variant state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.7px;
  font-weight: 600;
  text-transform: uppercase;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead3 variant state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead3 variant state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead3 variant state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead3 variant state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.5px;
  font-weight: 350;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead3 variant state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead3 variant state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead3 variant state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead3 variant state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead3 variant state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;

exports[`Typography Snapshots for x-large breakpoint renders with subhead3 variant state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <span
      class="emotion-0"
    >
      Hello World!
    </span>
  </div>
</DocumentFragment>
`;
