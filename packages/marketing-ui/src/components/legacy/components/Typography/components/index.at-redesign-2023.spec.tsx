// @ts-nocheck
import React from 'react';
import { snapshotsForBreakpoints, act } from 'test-utils';
import { SMALL, LARGE, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { StitchStyleProvider, Brands } from '@ecom-next/core/react-stitch';
import { Body, Eyebrow, Headline, HeadlineAlt, Subhead } from './index';

// this is being mocked so tests are only run for Athleta
jest.mock('@ecom-next/core/react-stitch', () => ({
  ...jest.requireActual('@ecom-next/core/react-stitch'),
  Brands: {
    Athleta: 'at' as const,
  },
}));

const atRedesign2023EnabledFeatures = { 'at-redesign-2023': true };

const ComponentWithAtRedesign2023FF =
  // eslint-disable-next-line @typescript-eslint/no-explicit-any


    Comp =>
    ({ children, ...props }) => (
      <StitchStyleProvider brand={Brands.Athleta} enabledFeatures={atRedesign2023EnabledFeatures}>
        <Comp {...props}>{children}</Comp>
      </StitchStyleProvider>
    );

describe("at-redesign-2023 - Typography Variants 'Helpers'", () => {
  describe('Snapshots', () => {
    const mockTest = 'Hello World!';

    describe('Eyebrow', () => {
      snapshotsForBreakpoints(
        [SMALL, XLARGE],
        ComponentWithAtRedesign2023FF(Eyebrow) as unknown as React.ComponentType,
        [
          ['default', {}],
          ['customStyles', { customStyles: { color: 'black' } }],
          ['variant', { variant: 'eyebrow2' }],
          ['scalable text', { scalableText: { enable: true } }],
          ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
        ],
        { children: mockTest }
      );
    });

    describe('Headline', () =>
      snapshotsForBreakpoints(
        [SMALL, XLARGE],
        ComponentWithAtRedesign2023FF(Headline) as unknown as React.ComponentType,
        [
          ['default', {}],
          ['customStyles', { customStyles: { color: 'black' } }],
          ['variant', { variant: 'headline2' }],
          ['scalable text', { scalableText: { enable: true } }],
          ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
        ],
        { children: mockTest }
      ));

    describe('Headline Alt', () =>
      snapshotsForBreakpoints(
        [SMALL, XLARGE],
        ComponentWithAtRedesign2023FF(HeadlineAlt) as unknown as React.ComponentType,
        [
          ['default', {}],
          ['customStyles', { customStyles: { color: 'black' } }],
          ['variant', { variant: 'headlineAlt2' }],
          ['scalable text', { scalableText: { enable: true } }],
          ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
        ],
        { children: mockTest }
      ));

    describe('Headline Alt 4 with underline', () =>
      snapshotsForBreakpoints([SMALL, XLARGE], ComponentWithAtRedesign2023FF(HeadlineAlt), [['variant', { variant: 'headlineAlt4' }]]));

    describe('Subhead', () =>
      snapshotsForBreakpoints(
        [SMALL, XLARGE],
        ComponentWithAtRedesign2023FF(Subhead) as unknown as React.ComponentType,
        [
          ['default', {}],
          ['customStyles', { customStyles: { color: 'black' } }],
          ['variant', { variant: 'subhead2' }],
          ['scalable text', { scalableText: { enable: true } }],
          ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
        ],
        { children: mockTest }
      ));

    describe('Body', () =>
      snapshotsForBreakpoints(
        [SMALL, XLARGE],
        ComponentWithAtRedesign2023FF(Body) as unknown as React.ComponentType,
        [
          ['default', {}],
          ['customStyles', { customStyles: { color: 'black' } }],
          ['variant', { variant: 'body2' }],
          ['scalable text', { scalableText: { enable: true } }],
          ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
        ],
        { children: mockTest }
      ));

    // prove that desktopBreakpoint changes when desktop begins
    describe('desktopBreakpoint', () =>
      snapshotsForBreakpoints(
        [LARGE, XLARGE],
        ComponentWithAtRedesign2023FF(Headline) as unknown as React.ComponentType,
        [["set to 'x-large' should show mobile styles in views below XLARGE", { scalableText: { enable: true, desktopBreakpoint: 'x-large' } }]],
        { children: mockTest }
      ));
  });
});
