// @ts-nocheck
import { LARGE, SMALL, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { snapshotsForBreakpoints, act } from 'test-utils';
import React from 'react';
import { Body, Eyebrow, GapTypography, Headline, Promo, Subhead } from './index';

jest.mock('@ecom-next/core/react-stitch', () => ({
  ...jest.requireActual('@ecom-next/core/react-stitch'),
  Brands: {
    Gap: 'gap',
    GapFactoryStore: 'gapfs',
  },
}));

const gapRedesign2024EnabledFeatures = { 'gap-redesign-2024': true };

const ComponentWithGapRedesign2024FF =
  (
    Comp: React.FC<any>, // eslint-disable-line @typescript-eslint/no-explicit-any
    brand: Brands
  ): React.ComponentType =>
  ({ children, ...props }) => (
    <StitchStyleProvider brand={brand} enabledFeatures={gapRedesign2024EnabledFeatures}>
      <Comp {...props}>{children}</Comp>
    </StitchStyleProvider>
  );

[Brands.Gap, Brands.GapFactoryStore].forEach(brand => {
  describe(`gap-redesign-2024 - Typography Variants 'Helpers' - ${brand}`, () => {
    describe('Snapshots', () => {
      const mockTest = 'Hello World!';

      describe('Eyebrow', () => {
        snapshotsForBreakpoints(
          [SMALL, XLARGE],
          ComponentWithGapRedesign2024FF(Eyebrow, brand),
          [
            ['default', {}],
            ['customStyles', { customStyles: { color: 'black' } }],
            ['variant', { variant: 'eyebrow2' }],
            ['scalable text', { scalableText: { enable: true } }],
            ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
          ],
          { children: mockTest }
        );
      });

      describe('Headline', () =>
        snapshotsForBreakpoints(
          [SMALL, XLARGE],
          ComponentWithGapRedesign2024FF(Headline, brand),
          [
            ['default', {}],
            ['customStyles', { customStyles: { color: 'black' } }],
            ['variant', { variant: 'headline2' }],
            ['scalable text', { scalableText: { enable: true } }],
            ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
          ],
          { children: mockTest }
        ));

      describe('Subhead', () =>
        snapshotsForBreakpoints(
          [SMALL, XLARGE],
          ComponentWithGapRedesign2024FF(Subhead, brand),
          [
            ['default', {}],
            ['customStyles', { customStyles: { color: 'black' } }],
            ['variant', { variant: 'subhead2' }],
            ['scalable text', { scalableText: { enable: true } }],
            ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
          ],
          { children: mockTest }
        ));

      describe('Body', () =>
        snapshotsForBreakpoints(
          [SMALL, XLARGE],
          ComponentWithGapRedesign2024FF(Body, brand),
          [
            ['default', {}],
            ['customStyles', { customStyles: { color: 'black' } }],
            ['variant', { variant: 'body2' }],
            ['scalable text', { scalableText: { enable: true } }],
            ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
          ],
          { children: mockTest }
        ));

      describe('Promo', () =>
        snapshotsForBreakpoints(
          [SMALL, XLARGE],
          ComponentWithGapRedesign2024FF(Promo, brand),
          [
            ['default', {}],
            ['customStyles', { customStyles: { color: 'black' } }],
            ['variant', { variant: 'promo2' }],
            ['scalable text', { scalableText: { enable: true } }],
            ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
          ],
          { children: mockTest }
        ));

      describe('desktopBreakpoint', () =>
        snapshotsForBreakpoints(
          [LARGE, XLARGE],
          ComponentWithGapRedesign2024FF(Headline, brand),
          [["set to 'x-large' should show mobile styles in views below XLARGE", { scalableText: { enable: true, desktopBreakpoint: 'x-large' } }]],
          { children: mockTest }
        ));

      describe('New Variants', () => {
        snapshotsForBreakpoints(
          [SMALL, XLARGE],
          ComponentWithGapRedesign2024FF(GapTypography, brand),
          [
            ['default', {}],
            ['variant F2', { variant: 'F2' }],
            ['variant F1', { variant: 'F1' }],
            ['variant F0', { variant: 'F0' }],
            ['variant FN1', { variant: 'FN1' }],
          ],
          { children: mockTest }
        );
      });
    });
  });
});
