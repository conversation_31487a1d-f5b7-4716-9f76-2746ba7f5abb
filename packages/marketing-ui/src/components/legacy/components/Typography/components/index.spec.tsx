// @ts-nocheck
import React from 'react';
import { render, screen, act } from 'test-utils';
import { snapshotsForBreakpoints } from 'test-utils';
import { SMALL, LARGE, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { Body, Eyebrow, Headline, HeadlineAlt, Subhead } from './index';

describe("Typography Variants 'Helpers'", () => {
  describe('Snapshots', () => {
    const mockTest = 'Hello World!';

    describe('Eyebrow', () =>
      snapshotsForBreakpoints(
        [SMALL, XLARGE],
        Eyebrow as unknown as React.ComponentType,
        [
          ['default', {}],
          ['customStyles', { customStyles: { color: 'black' } }],
          ['variant', { variant: 'eyebrow2' }],
          ['scalable text', { scalableText: { enable: true } }],
          ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
        ],
        { children: mockTest }
      ));

    describe('Headline', () =>
      snapshotsForBreakpoints(
        [SMALL, XLARGE],
        Headline as unknown as React.ComponentType,
        [
          ['default', {}],
          ['customStyles', { customStyles: { color: 'black' } }],
          ['variant', { variant: 'headline2' }],
          ['scalable text', { scalableText: { enable: true } }],
          ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
        ],
        { children: mockTest }
      ));

    describe('Headline Alt', () =>
      snapshotsForBreakpoints(
        [SMALL, XLARGE],
        HeadlineAlt as unknown as React.ComponentType,
        [
          ['default', {}],
          ['customStyles', { customStyles: { color: 'black' } }],
          ['variant', { variant: 'headlineAlt2' }],
          ['scalable text', { scalableText: { enable: true } }],
          ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
        ],
        { children: mockTest }
      ));

    describe('Subhead', () =>
      snapshotsForBreakpoints(
        [SMALL, XLARGE],
        Subhead as unknown as React.ComponentType,
        [
          ['default', {}],
          ['customStyles', { customStyles: { color: 'black' } }],
          ['variant', { variant: 'subhead2' }],
          ['scalable text', { scalableText: { enable: true } }],
          ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
        ],
        { children: mockTest }
      ));

    describe('Body', () =>
      snapshotsForBreakpoints(
        [SMALL, XLARGE],
        Body as unknown as React.ComponentType,
        [
          ['default', {}],
          ['customStyles', { customStyles: { color: 'black' } }],
          ['variant', { variant: 'body2' }],
          ['scalable text', { scalableText: { enable: true } }],
          ['scalable text with a max width', { scalableText: { enable: true, parentMaxWidthPx: 1100 } }],
        ],
        { children: mockTest }
      ));

    // prove that desktopBreakpoint changes when desktop begins
    describe('desktopBreakpoint', () =>
      snapshotsForBreakpoints(
        [LARGE, XLARGE],
        Headline as unknown as React.ComponentType,
        [["set to 'x-large' should show mobile styles in views below XLARGE", { scalableText: { enable: true, desktopBreakpoint: 'x-large' } }]],
        { children: mockTest }
      ));
  });

  describe('variations', () => {
    it('Eyebrow should be in the document', () => {
      const text = 'Hello World Eyebrow!';
      render(<Eyebrow variant='eyebrow1'>{text}</Eyebrow>);
      expect(screen.getByText(text)).toBeInTheDocument();
    });
    it('Headline should be in the document', () => {
      const text = 'Hello World Headline!';
      render(<Headline variant='headline1'>{text}</Headline>);
      expect(screen.getByText(text)).toBeInTheDocument();
    });
    it('Headline alt should be in the document', () => {
      const text = 'Hello World Headline Alt!';
      render(<HeadlineAlt variant='headlineAlt1'>{text}</HeadlineAlt>);
      expect(screen.getByText(text)).toBeInTheDocument();
    });
    it('Subhead should be in the document', () => {
      const text = 'Hello World Subhead!';
      render(<Subhead variant='subhead1'>{text}</Subhead>);
      expect(screen.getByText(text)).toBeInTheDocument();
    });
    it('Body should be in the document', () => {
      const text = 'Hello World Body!';
      render(<Body variant='body1'>{text}</Body>);
      expect(screen.getByText(text)).toBeInTheDocument();
    });
  });
});
