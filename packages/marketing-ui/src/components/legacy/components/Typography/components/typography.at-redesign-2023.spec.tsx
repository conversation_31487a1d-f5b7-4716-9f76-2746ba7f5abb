// @ts-nocheck
import React from 'react';
import { snapshotsForBreakpoints, act } from 'test-utils';

import { SMALL, LARGE, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { Typography } from '.';
import { TypographyProps, TypographyVariant } from '..';

// this is being mocked so tests are only run for Athleta
jest.mock('@ecom-next/core/react-stitch', () => ({
  ...jest.requireActual('@ecom-next/core/react-stitch'),
  Brands: {
    Athleta: 'at' as const,
  },
}));

const atRedesign2023EnabledFeatures = {
  'at-redesign-2023': true,
};

const TYPOGRAPHY_VARIANTS: TypographyVariant[] = [
  'body1',
  'body2',
  'body3',
  'body4',
  'body5',
  'eyebrow1',
  'eyebrow2',
  'eyebrow3',
  'headline1',
  'headline2',
  'headline3',
  'headline4',
  'headline5',
  'headline6',
  'headline7',
  'subhead1',
  'subhead2',
  'subhead3',
];

const TypographyWithAtRedesign2023FF = ({ variant, children }) => (
  <StitchStyleProvider brand={Brands.Athleta} enabledFeatures={atRedesign2023EnabledFeatures}>
    <Typography variant={variant}>{children}</Typography>
  </StitchStyleProvider>
);

describe('at-redesign-2023 Typography', () => {
  describe('Snapshots', () => {
    const mockText = 'Hello World!';
    snapshotsForBreakpoints(
      [SMALL, XLARGE],
      TypographyWithAtRedesign2023FF as unknown as React.ComponentType,
      [
        ['default', {}],
        ['with custom HTML element', { element: 'p' }],
        // snapshot for each variant
        ...TYPOGRAPHY_VARIANTS.map((variant): [string, TypographyProps] => [`with ${variant} variant`, { variant }]),
        ['with scalable text', { scalableText: { enable: true } }],
        ['with scalable and custom min fontSize', { scalableText: { enable: true, minSizePx: 10 } }],
        ['with scalable and custom max fontSize', { scalableText: { enable: true, maxSizePx: 10 } }],
        ['with scalable and disabling infinite scaling', { scalableText: { enable: true, disableInfiniteScaling: true } }],
        ['width desktopBreakpoint as LARGE', { scalableText: { desktopBreakpoint: LARGE, enable: true } }],
      ],
      {
        variant: 'body1',
        children: mockText,
      }
    );
  });
});
