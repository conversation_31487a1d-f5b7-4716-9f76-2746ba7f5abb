// @ts-nocheck
import React from 'react';
import { snapshotsForBreakpoints, act } from 'test-utils';
import { render, screen } from 'test-utils';

import { SMALL, LARGE, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { Brand<PERSON>, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { Typography } from '.';
import { ScalableText } from '../types';
import { TypographyProps, TypographyVariant } from '..';

const TYPOGRAPHY_VARIANTS: TypographyVariant[] = [
  'body1',
  'body2',
  'body3',
  'body4',
  'body5',
  'eyebrow1',
  'eyebrow2',
  'eyebrow3',
  'headline1',
  'headline2',
  'headline3',
  'headline4',
  'headline5',
  'headline6',
  'headline7',
  'subhead1',
  'subhead2',
  'subhead3',
  'promo1',
  'promo2',
  'promoAlt1',
];

describe('Typography', () => {
  describe('Snapshots', () => {
    const mockText = 'Hello World!';
    snapshotsForBreakpoints(
      [SMALL, XLARGE],
      Typography as unknown as React.ComponentType,
      [
        ['default', {}],
        ['with custom HTML element', { element: 'p' }],
        // snapshot for each variant
        ...TYPOGRAPHY_VARIANTS.map((variant): [string, TypographyProps] => [`with ${variant} variant`, { variant }]),
        ['with scalable text', { scalableText: { enable: true } }],
        ['with scalable and custom min fontSize', { scalableText: { enable: true, minSizePx: 10 } }],
        ['with scalable and custom max fontSize', { scalableText: { enable: true, maxSizePx: 10 } }],
        ['with scalable and disabling infinite scaling', { scalableText: { enable: true, disableInfiniteScaling: true } }],
        ['width desktopBreakpoint as LARGE', { scalableText: { desktopBreakpoint: LARGE, enable: true } }],
      ],
      {
        variant: 'body1',
        children: mockText,
      }
    );
    it('should have a default scaling point if none is defined', () => {
      const variant: TypographyVariant = 'headlineAlt1';
      const scalingOptions: ScalableText = {
        enable: true,
      };
      const typoProps = {
        variant,
        scalableText: scalingOptions,
      };
      const { container } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <Typography {...typoProps}>Test</Typography>
        </StitchStyleProvider>
      );
      const testElement = screen.getByText('Test');
      expect(testElement).toHaveStyleRule('font-size', 'max(14px, 9.6875vw)');
    });

    it('a 1280 scaling point should match default', () => {
      const variant: TypographyVariant = 'headlineAlt1';
      const scalingOptions: ScalableText = {
        enable: true,
        desktopScalingPoint: 1280,
      };
      const typoProps = {
        variant,
        scalableText: scalingOptions,
      };
      const { container } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <Typography {...typoProps}>Test</Typography>
        </StitchStyleProvider>
      );
      const testElement = screen.getByText('Test');
      expect(testElement).toHaveStyleRule('font-size', 'max(14px, 9.6875vw)');
    });
    it('should have a smaller font size if the scaling point is larger than 1280', () => {
      const variant: TypographyVariant = 'headlineAlt1';
      const scalingOptions: ScalableText = {
        enable: true,
        desktopScalingPoint: 1440,
      };
      const typoProps = {
        variant,
        scalableText: scalingOptions,
      };
      const { container } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <Typography {...typoProps}>Test</Typography>
        </StitchStyleProvider>
      );
      const testElement = screen.getByText('Test');
      expect(testElement).toHaveStyleRule('font-size', 'max(14px, 8.61111111111111vw)');
    });

    it('should have a larger font size if the scaling point is smaller than 1280', () => {
      const variant: TypographyVariant = 'headlineAlt1';
      const scalingOptions: ScalableText = {
        enable: true,
        desktopScalingPoint: 1000,
      };
      const typoProps = {
        variant,
        scalableText: scalingOptions,
      };
      const { container } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <Typography {...typoProps}>Test</Typography>
        </StitchStyleProvider>
      );
      const testElement = screen.getByText('Test');
      expect(testElement).toHaveStyleRule('font-size', 'max(14px, 12.4vw)');
    });
    describe('for BR/BRfs', () => {
      const typographyWithProvider = typographyProps => {
        return render(
          <StitchStyleProvider brand={Brands.BananaRepublic}>
            <Typography {...typographyProps}>Test</Typography>
          </StitchStyleProvider>
        );
      };
      it('should render headline1 variant with BananaSerif font', () => {
        const variant: TypographyVariant = 'headline1';
        const scalingOptions: ScalableText = {
          enable: false,
          desktopScalingPoint: 1920,
        };
        const typographyProps = {
          variant,
          scalableText: scalingOptions,
        };
        const { container } = typographyWithProvider(typographyProps);
        const testElement = screen.getByText('Test');
        expect(testElement).toHaveStyleRule('font-family', 'var(--font-banana-serif),Times New Roman,serif');
      });
      it('should render headlineAlt1 variant with Lynstone font', () => {
        const variant: TypographyVariant = 'headlineAlt1';
        const scalingOptions: ScalableText = {
          enable: false,
          desktopScalingPoint: 1920,
        };
        const typographyProps = {
          variant,
          scalableText: scalingOptions,
        };
        const { container } = typographyWithProvider(typographyProps);
        const testElement = screen.getByText('Test');
        expect(testElement).toHaveStyleRule('font-family', 'var(--font-lynstone),Helvetica,Arial,sans-serif');
      });
      it('should render body1 variant with Lynstone font', () => {
        const variant: TypographyVariant = 'body1';
        const scalingOptions: ScalableText = {
          enable: false,
          desktopScalingPoint: 1920,
        };
        const typographyProps = {
          variant,
          scalableText: scalingOptions,
        };
        const { container } = typographyWithProvider(typographyProps);
        const testElement = screen.getByText('Test');
        expect(testElement).toHaveStyleRule('font-family', 'var(--font-lynstone),Helvetica,Arial,sans-serif');
      });
    });
  });
});
