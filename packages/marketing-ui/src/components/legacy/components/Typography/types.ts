// @ts-nocheck
'use client';
import { CSSObject } from '@ecom-next/core/react-stitch';

/**
 * property: [mobileValue, desktopValue]
 */
export interface TypographyVariantConfig {
  /**
   * fontSize: [mobile, desktop]
   * the base font size, used as default when not scaling
   *
   * ex. 10 (will be 10px)
   */
  readonly fontSize: [number, number];

  /**
   * lineHeight: [mobile, desktop]
   * The line-height in px for the base font-size
   *
   * UI/UX may refer to leading, this value will be computed to line-height: leading / fontSize
   *
   * ex. 1.5 (10px font-size would calculate 15px line-height)
   */
  readonly lineHeight: [number, number];

  /**
   * maxFontSize: [mobile, desktop]
   * used to set the minimum computed font value
   *
   * ex. 12 (will grow to a max of 12px)
   */
  readonly minFontSize?: [number, number];

  /**
   * letterSpacing: [mobile, desktop]
   * UI/UX may refer to this as tracking (the distance between characters),
   * this value can be computed with: (fontSize * (tracking / 100)) / 10
   *
   * ex. 1.3 (the px width, can be calculated, can be +/-)
   */
  readonly letterSpacing?: [number, number];

  readonly fontWeight?: [number, number];

  readonly textUnderlineOffset?: [number, number];
}

export type BrandTypographyConfig = Partial<Record<TypographyVariant, TypographyVariantConfig>>;

export interface TypographyConfigOpts {
  any?: unknown;
}
/**
 * @returns BrandTypographyConfig - the typography config for the brand. Values are in px. The first index is mobile and the second is desktop.
 */
export type BrandTypographyConfigFactory = (opts?: TypographyConfigOpts) => BrandTypographyConfig;

//
// --- React Component Types ---

export type ScalableText = {
  /**
   * when true, text will scale with the window width. if false, none of the other optionas will apply.
   *
   * Defaults to true
   */
  enable?: boolean;
  /**
   * text will scale until the window width reaches this number.
   * the idea is for the text to match the components, even once they stop growing.
   * if both parentMaxWidthPx and maxSizePx,
   * the smaller calculated value for the font will be used.
   */
  parentMaxWidthPx?: number;
  /**
   * for passing down the parent breakpoint.
   * text should change at the same breakpoint that the parent does.
   */
  desktopBreakpoint?: 'large' | 'x-large';
  /**
   * text will scale up until the font reaches this px size.
   * if both parentMaxWidthPx and maxSizePx,
   * the smaller calculated value for the font will be used.
   *
   */
  maxSizePx?: number;
  /**
   * text will scale down until the font reaches this px size.
   *
   * Internal only, not available as a React component prop
   */
  minSizePx?: number;

  /**
   * enabling will cap the max font-size to the deplared font-size for the brand's config
   */
  disableInfiniteScaling?: boolean;
  // sets the view width point when scaling size matches static size in desktop
  // if not set, defaults to 1280
  // caution: this will change the scaling size of all the desktop typography!
  desktopScalingPoint?: number;
};

export type ScalingProps = {
  scalableText?: ScalableText;
};

export type TypographyProps = {
  className?: string;
  variant: TypographyVariant;
  children?: React.ReactNode[] | React.ReactNode;
  customStyles?: CSSObject;
  element?: React.ElementType;
} & ScalingProps;

export type TypographyVariant = EyebrowVariant | HeadlineVariant | HeadlineAltVariant | SubheadVariant | PromoVariant | BodyVariant | FVariant;

export type EyebrowVariant = 'eyebrow1' | 'eyebrow2' | 'eyebrow3';

export type HeadlineVariant = 'headline1' | 'headline2' | 'headline3' | 'headline4' | 'headline5' | 'headline6' | 'headline7';

export type HeadlineAltVariant = 'headlineAlt1' | 'headlineAlt2' | 'headlineAlt3' | 'headlineAlt4' | 'headlineAlt5' | 'headlineAlt6' | 'headlineAlt7';

export type BodyVariant = 'body1' | 'body2' | 'body3' | 'body4' | 'body5';

export type SubheadVariant = 'subhead1' | 'subhead2' | 'subhead3';

export type PromoVariant = 'promo1' | 'promo2' | 'promoAlt1';

export type FVariant = 'F2' | 'F1' | 'F0' | 'FN1';

export interface BodyProps extends TypographyProps {
  variant: BodyVariant;
}

export interface EyebrowProps extends TypographyProps {
  variant: EyebrowVariant;
}

export interface HeadlineProps extends TypographyProps {
  variant: HeadlineVariant;
}

export interface HeadlineAltProps extends TypographyProps {
  variant: HeadlineAltVariant;
}

export interface SubheadProps extends TypographyProps {
  variant: SubheadVariant;
}

export interface PromoProps extends TypographyProps {
  variant: PromoVariant;
}

export interface FProps extends TypographyProps {
  variant: FVariant;
}
