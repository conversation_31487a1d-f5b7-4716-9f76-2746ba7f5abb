# Video Component

This component allows you to add either videos from youtube, vimeo etc. using https://www.npmjs.com/package/react-player
or add videos with a direct link that points to mp4 etc. file using https://www.npmjs.com/package/video-react

| prop                    | type                     | default value | description                                                                                                                                 |
| ----------------------- | ------------------------ | ------------- | ------------------------------------------------------------------------------------------------------------------------------------------- |
| `forceAsReady`          | `boolean?`               | `false`       | Force video as `readyToPlay` to render and test player custom controls                                                                      |
| `url`                   | `string?`                | `undefined`   | `Video src to be used on all breakpoints if no desktopUrl. Otherwise only at mobile breakpoint`                                             |
| `desktopUrl`            | `string?`                | `undefined`   | `Video src to be used at desktop breakpoint`                                                                                                |
| `containerStyle`        | `CSSObject?`             | `undefined`   | `Object desktop/mobile`                                                                                                                     |
| `playerWrapperStyle`    | `CSSObject?`             | `undefined`   | `Styling the entire DIV that wraps the VideoComponent`                                                                                      |
| `playIcon`              | `Object?`                | `undefined`   | `Object alt/src`                                                                                                                            |
| `playerStyles`          | `CSSObject?`             | `undefined`   | `Object desktop/mobile`                                                                                                                     |
| `fallbackImage`         | `CSSObject?`             | `undefined`   | `Object alt/src/desktopSrc`                                                                                                                 |
| `controls`              | `boolean?`               | `undefined`   | `native video player controls (when `customControls` is true, native controls will be hidden by default)`                                   |
| `customControls`        | `boolean?`               | `undefined`   | `when selected will hide native controls and show ours`                                                                                     |
| `playing`               | `boolean?`               | `undefined`   | `used to autoplay video when set to true`                                                                                                   |
| `customControlStyles`   | `CSSObject?`             | `undefined`   | `The parent object where individual controls are nested. (Play/Mute/Volume) See example below.`                                             |
| `light`                 | `boolean or string?`     | `undefined`   | `Set to true to show just the video thumbnail, which loads the full player on click. Or Pass in an image URL to override the preview image` |
| `hideMuteControl`       | `boolean`                | `false`       | `Hide mute (and volume) control. Default volume to 0 and mute to true.`                                                                     |
| `useCustomPlayIcon`     | `boolean`                | `false`       | When set to `true`, enables the use of a custom play icon. Requires `useCustomPlayIconSrc` to be specified.                                 |
| `useCustomPlayIconSrc`  | `string`                 | `undefined`   | The URL for the custom play icon image. Required if `useCustomPlayIcon` is `true`.                                                          |
| `useCustomPauseIcon`    | `boolean`                | `false`       | When set to `true`, enables the use of a custom pause icon. Requires `useCustomPauseIconSrc` to be specified.                               |
| `useCustomPauseIconSrc` | `string`                 | `undefined`   | The URL for the custom pause icon image. Required if `useCustomPauseIcon` is `true`.                                                        |
| `playOnHover`           | `boolean`                | `false`       | When set to `true`, the video will start playing when the mouse hovers over it.                                                             |
| `pauseOnHover`          | `boolean`                | `false`       | When set to `true`, the video will pause when the mouse leaves it.                                                                          |
| `controlsIconsColor`    | `primary` \| `secondary` | `primary`     | The color of the icons in the controls.                                                                                                     |

## Custom Controls

Author have the option to use custom controls in a video by setting `customControls` to `true`. Selecting this option will hide native controls and show custom controls.

**Note** - Behavior of the volume control. Volume control remembers its last setting after user click the mute/unmute button. The only exception is when volume is at level 0 before muting.
In that case, after the user clicks the unmute button, the volume will default to 0.13, instead of staying at 0.

customControlStyles:

```
{
    "name": "VideoComponent",
    "type": "sitewide",
    "data": {
      "playerStyles": {
        "margin": "0 auto",
        "border": "solid 1px #000"
      }
      "url": "https://player.vimeo.com/video/722346721?h=f087695bf5",
      "controls": true,
      "muted": true,
      "playing": false,
      "loop": false,
      "width": "640px",
      "height": "360px",
      "customControls": true,
      "customControlStyles": {
        "width": "50%",
        "position": absolute,
        "top": "50%",
        "left": "25%",
        "button:first-child":{
          "outline": "4px dashed red"},
        "@media (min-width: 767px)": {
          "button:last-child":{
            "outline": "4px dashed orange"
          },
        }
      },
    },
  }
```

## Custom Play and Pause Icons

To further customize the video player's UI, you can use your own icons for play and pause actions. This is particularly useful for maintaining consistent branding or meeting specific design requirements.

### Using Custom Play Icon

Specify a custom play icon by setting `useCustomPlayIcon` to `true` and providing a URL to your icon image using `useCustomPlayIconSrc`.

Example:

```
  useCustomPlayIcon: true,
  useCustomPlayIconSrc:
    "https://cdn.iconscout.com/icon/free/png-256/free-play-button-462095.png",
  useCustomPauseIcon: true,
  useCustomPauseIconSrc:
    "https://cdn.iconscout.com/icon/premium/png-256-thumb/play-pause-button-3222303-2850321.png",
```

## Accessibility

For accessibility criterion follow: https://www.w3.org/TR/WCAG20-TECHS/F93.html

Be mindful about accessibility while using the component

Examples of bad accessibility

- "playing": true and "muted": false

- "loop": true and "controls":false,
  etc.

## Autoplay

You may autoplay videos by using the "playing" property, although it is not recommended for UX and accessibility.

All videos that are autoplayed will be muted and play inline (i.e. not fullscreen), no matter what you may have put in those properties in the JSON. This is because otherwise most browsers will not allow the video to play at all. Even with these constraints, be aware that some customers may have even stricter settings that prevent _all_ autoplay, and there is no configuration that will bypass that.

## customControls

- `customControls` object prop: When set to `true`, Brands custom controls (play, pause, mute, volume) will be visible.

## playerStyles

- `playerStyles` object prop: Defines styles to be applied to the reactPlayer

\*Note: Width and height of video should be defined within the data object as described in the react-player documentation shown here: https://www.npmjs.com/package/react-player

## playIcon

- `playIcon` object prop: Defines a custom play button icon. This is optional. However if you decide to use a custom play button icon you must supply both `src` and `alt` values in the `playIcon` object and the `light` value in the video player prop options:
  - `alt` string (required): text for the alt attribute for the playIcon image tag
  - `src` string (required): path to your image file
- `light` string (required): an image URL to the video thumbnail. This is only required if you want to use a custom play button. Otherwise this prop is optional.

```
  "light": "path/to/your/video-thumbnail.png",
  "playIcon": {
    "alt": "Play",
    "src": "path/to/your/image.svg"
  }
```

## fallbackImage

- `fallbackImage` object prop: Defines a back-up image when video may error out, or if url is invalid. It is optional. If you decide to use the back up image, you supply the `alt`, `src`, and `desktopSource` values in the `fallbackImage` object:
  - `alt` string (required): text for the alt attribute for the playIcon image tag
  - `src` string (required): path to your image file; will be used for both desktop and mobile breakpoints if `desktopSrc` is not provided
  - `desktopSrc` string (optional): path to your image file; will be used at desktop breakpoint only if provided

\*\*\*Please note: This image will display in most cases where there is an error with the video, but may not cover edge cases. If you are experiencing an issue where the fallback image is not working as expected, please reach out to FoundationUI and they can help you troubleshoot the problem.

```
  "fallbackImage": {
    "alt": "video error",
    "src": "path/to/your/image.svg"
  }
```

### Poster Image

The provided `fallbackImage` will also be used as the poster image. This image will display before the provided video to improve load speeds.

## Auto play unmuted video

- To play a video that is unmuted you must use either _direct path host_ or with _custom play button_. Unmuted video can only occur if the user clicked to play a video.

Setup from **one of those** two conditions as your **initial load**:

1. `light` set to a string (path to poster) or boolean.
2. `playIcon` set to have a src. (path to icon)

Now you can set `playing: true` and `muted: false` and after user click on the video, it will play with sound.

## Video Player Prop options

**Other Props possible in Video Component**

- `url` string (optional) or array of strings: path to your video file. Note that `url` will be used for both desktop and mobile breakpoints if `desktopSrc` is not provided

  `"url": "https://www.youtube.com/watch?v=QohH89Eu5iM"`

- `desktopUrl` string (optional) or array of strings: path to your video file. Note that `desktopUrl` Will be used at desktop breakpoint only if provided

  `"url": "https://www.youtube.com/watch?v=QohH89Eu5iM"`

- `playing` Boolean (optional) if specified, the video will automatically begins to play. (like autoplay). Also sets muted to `true` if unspecified. For browsers that do not allow autoplay, this prop will have no effect.

  `"playing": true`

- `loop` Boolean (optional) the browser will automatically seek back to the start upon reaching the end of the video. By default, loop = false.

  `"loop": true`

- `controls` Boolean (optional) the browser will offer native controls to allow the user to control video playback, including volume, seeking, and pause/resume playback.

  `"controls": true`

- `muted` Boolean (optional) Mutes the player. Only works if volume is set.

  `"muted": true`

- `playbackRate` Number (optional) The speed in which the video plays. Only supported by YouTube, Wistia, and file paths.

  `"playbackRate": 4`

- `width` Number or String (optional) Set the width of the player.

  `"width": "560px"`

- `height` Number or String (optional) Set the height of the player.

  `"height": "360px"`

- `style` Object (optional) Add inline styles to the root element.

  `"style": { "boxShadow": "13px 13px 33px grey" }`

- `playsinline` Boolean (optional) indicating that the video is to be played "inline", that is within the element's playback area. (iOS supported).

  `"playsinline": true`

- `pip` Boolean (optional) to enable or disable picture-in-picture mode.
  If set to true, the element should automatically toggle picture-in-picture mode when the user switches back and forth between this document and another document or application.
  (Only available when playing file URLs in certain browsers.)

`"pip": true`

- `light` Boolean or String (optional) show just the video thumbnail, which loads the full player on click. (Pass in an image URL to override the preview image). NOTE: This is required if you want to use a custom play button.

  `"light": true | "http://path/to/video-thumbnail.jpg"`

For more React Player props [ReactPlayer](https://www.npmjs.com/package/react-player#props)

### Sample json using ReactPlayer props and playerStyles

```
      {
        "instanceName": "html5-video-component",
        "name": "VideoComponent",
        "type": "sitewide",
        "data": {
          "playerStyles": {
            "margin": "0 auto",
            "border": "solid 1px #000"
          }
          "url": "https://www.youtube.com/watch?v=QohH89Eu5iM",
          "controls": true,
          "muted": true,
          "playing": false,
          "loop": false,
          "width": "640px",
          "height": "360px"
        }
      }
```

### Sample json workaround for [FUI-3008: Hosted Video Defect issue - iPad](https://gapinc.atlassian.net/browse/FUI-3008).

\*\*\* Note: Poster Image is undocumented in the ReactPlayer API or in the types.

\*\*\* Currently there is a defect with iPad devices on Safari that require us to use a Poster Image. The workaround below allows WCD to code desktop with posterimage and mobile without posterimage.

```
Desktop JSON
{
   "instanceName":"full-width-video",
   "name":"VideoComponent",
   "type":"sitewide",
   "tileStyle":{
      "desktop":{
         "width":"100%",
         "height":"100%",
         "padding":"0",
         "position":"relative"
      },
      "mobile":{
         "width":"100%",
         "padding":"0",
         "margin":"0 auto"
      }
   },
   "data":{
      "url":"/Asset_Archive/videocampaign/ON_September_Fleece_Desktop_wCopy_1400x766.mp4",
      "light":"/Asset_Archive/ONWeb/content/0019/262/972/assets/200921_41_FleeceWorld_Hero_Static_HP_CA_XL.png",
      "controls":true,
      "muted":true,
      "playing":true,
      "loop":false,
      "width":"100%",
      "height":"100%",
      "containerStyle":{
         "mobile":{
            "margin":"0 0 1.5rem 0"
         },
         "desktop":{
            "margin":"0 auto 1rem",
            "height":"calc(380px + (767 - 380) * ((100vw - 768px) / (1440 - 768)))",
            "maxWidth":"1400px",
            "maxHeight":"767px"
         }
      },
      "analytics":{
         "onPlayer":{
            "video_name_play":"",
            "event_name":"video_play_click"
         }
      },
      "playIcon":{
         "alt":"play",
         "src":"/Asset_Archive/ONWeb/content/0019/262/972/assets/200921_41_FleeceWorld_Hero_PlayButton_HP_US.png"
      }
   }
}

Mobile JSON
{
   "instanceName":"full-width-video",
   "name":"VideoComponent",
   "type":"sitewide",
   "tileStyle":{
      "desktop":{
         "width":"100%",
         "height":"100%",
         "padding":"0",
         "position":"relative"
      },
      "mobile":{
         "width":"100%",
         "padding":"0",
         "margin":"0 auto"
      }
   },
   "data":{
      "posterImage":"",
      "url":"/Asset_Archive/videocampaign/ON_September_Fleece_Mobile_wCopy_720x766.mp4",
      "light":"",
      "controls":true,
      "muted":true,
      "playing":false,
      "loop":false,
      "width":"100vw",
      "containerStyle":{
         "mobile":{
            "margin":"0",
            "height":""
         },
         "desktop":{
            "margin":"0 auto 1rem",
            "height":"calc(380px + (712 - 380) * ((100vw - 768px) / (1440 - 768)))",
            "maxHeight":"712px"
         }
      },
      "analytics":{
         "onPlayer":{
            "video_name_play":"",
            "event_name":"video_play_click"
         }
      }
   }
}
```

### Video Components uses Analytics to track on pause and on play events. Either props are optional.

\*\*\*Please note: using `onPause` event is not recommended unless asked specifically from WCD analytic team.

```
    "analytics": {
      "onPlay": {
        "video_name_play": "active_track_jacket_desktop_video_played_content",
        "event_name": "video_play_click"
      },
      "onPause": {
        "video_name_pause": "active_track_jacket_desktop_video_paused_content",
        "event_name": "video_pause_click"
      }
    }

```

The prop `video_name_play` should have a unique video name. This help tracking each video seperetley.
e.g For new arrivals video on desktop and mobile: "new_arrivals_sweatshirt_video_played_content".
