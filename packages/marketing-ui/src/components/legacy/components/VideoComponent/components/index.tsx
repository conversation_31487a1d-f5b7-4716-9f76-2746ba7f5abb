'use client';
import React, { forwardRef, useCallback, useEffect, useRef, useState } from 'react';
import { CSSObject, Brands, useTheme, useEnabledFeatures } from '@ecom-next/core/react-stitch';
// @ts-ignore
import { GapTheme } from '@ecom-next/core/legacy/react-stitch/types/theme/brands/gap/types';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { MuteIcon as CoreUiMuteIcon, UnmuteIcon as CoreUiUnmuteIcon, PauseSquareIcon, PlaySquareIcon } from '@ecom-next/core/legacy/icons';
import {
  InputRangeVolumeWithShadowDecorator,
  MuteActive as MuiMuteActive,
  MuteIcon as MuiMuteIcon,
  PauseActive,
  PauseIcon,
  PlayActive,
  PlayIcon,
  Spinner,
  StyledButton,
  StyledDivForInputVolume,
  StyledDivWrapperForMuteControls,
  UnmuteActive as MuiUnmuteActive,
  UnmuteIcon as MuiUnmuteIcon,
} from './icons';
import { CustomCoreUiIconProps, InputRangeVolumeProps, MuteControlProps, PlayerCustomControlProps, PlayPauseControlProps } from '../types';
import { useViewportIsLarge } from '../../../hooks';

const defaultPositionForPlayPause: CSSObject = {
  position: 'absolute',
  bottom: '8px',
  right: '112px',
  '@media (min-width: 767px)': {
    right: '120px',
  },
  width: '24px',
  height: '24px',
};

const defaultPositionForMute: CSSObject = {
  position: 'absolute',
  bottom: '8px',
  right: '72px',
  zIndex: 11,
  '@media (min-width: 767px)': {
    right: '80px',
  },
};

const disabledColor = 'rgba(29, 29, 29, 0.4)';

export const Pause = forwardRef<HTMLButtonElement, PlayPauseControlProps & CustomCoreUiIconProps>(
  (
    { onBuffer, ariaLabel, handleBlur, playPauseHandleFunc, controlColor, disableCoreUiControlIcons = false, enabledFeatures, isDesktop, theme, colorTheme },
    ref
  ): JSX.Element => {
    const [active, setActive] = useState(false);
    const isGap = [Brands.Gap, Brands.GapFactoryStore].includes(theme.brand);
    const use2024GapCoreUiIcons = isGap && !!enabledFeatures?.['fui-gap-video-icons-2024'];

    const size = isDesktop ? { width: '30px', height: '30px' } : { width: '28px', height: '28px' };

    const renderPauseActive = use2024GapCoreUiIcons ? (
      <PauseSquareIcon
        brandName={Brands.Gap}
        brandProp={Brands.Gap}
        colorTheme={colorTheme}
        // @ts-ignore
        css={{
          'svg rect': { fill: theme.color.br1 },
          'svg path': { fill: theme.color.wh },
          ...(disableCoreUiControlIcons
            ? {
                'svg rect': { fill: disabledColor },
                'svg path': { fill: theme.color.wh },
              }
            : {}),
        }}
        size={size}
      />
    ) : (
      <PauseActive controlColor={controlColor} />
    );

    const renderPauseIcon = use2024GapCoreUiIcons ? (
      <PauseSquareIcon
        brandName={Brands.Gap}
        brandProp={Brands.Gap}
        colorTheme={colorTheme}
        // @ts-ignore
        css={{
          'svg:hover rect': { fill: theme.color.br1 },
          'svg:hover path': { fill: theme.color.wh },
          ...(disableCoreUiControlIcons
            ? {
                'svg rect': { fill: disabledColor },
                'svg path': { fill: theme.color.wh },
              }
            : {}),
        }}
        size={size}
      />
    ) : (
      <PauseIcon controlColor={controlColor} />
    );
    return (
      <StyledButton
        ref={ref}
        aria-label={ariaLabel}
        // @ts-ignore
        css={{
          ...defaultPositionForPlayPause,
          bottom: use2024GapCoreUiIcons ? 11 : defaultPositionForPlayPause.bottom,
        }}
        disabled={use2024GapCoreUiIcons && disableCoreUiControlIcons}
        onBlur={() => {
          if (use2024GapCoreUiIcons && disableCoreUiControlIcons) {
            return;
          }
          handleBlur();
          React.startTransition(() => setActive(false));
        }}
        onClick={(event: React.MouseEvent<Node>) => {
          if (use2024GapCoreUiIcons && disableCoreUiControlIcons) {
            return;
          }
          playPauseHandleFunc(false);
          event.preventDefault();
        }}
        onFocus={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(true)))}
        onMouseEnter={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(true)))}
        onMouseLeave={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(false)))}
        onMouseOver={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(true)))}
      >
        {onBuffer && <Spinner />}
        {active ? renderPauseActive : renderPauseIcon}
      </StyledButton>
    );
  }
);

export const Play = forwardRef<HTMLButtonElement, PlayPauseControlProps & CustomCoreUiIconProps>(
  (
    { onBuffer, handleBlur, ariaLabel, playPauseHandleFunc, controlColor, disableCoreUiControlIcons = false, enabledFeatures, isDesktop, theme, colorTheme },
    ref
  ): JSX.Element => {
    const [active, setActive] = useState(false);
    const isGap = [Brands.Gap, Brands.GapFactoryStore].includes(theme.brand);
    const use2024GapCoreUiIcons = isGap && !!enabledFeatures?.['fui-gap-video-icons-2024'];
    const size = isDesktop ? { width: '30px', height: '30px' } : { width: '28px', height: '28px' };

    const renderPlayActive = use2024GapCoreUiIcons ? (
      <PlaySquareIcon
        brandName={Brands.Gap}
        brandProp={Brands.Gap}
        colorTheme={colorTheme}
        // @ts-ignore
        css={{
          'svg rect': { fill: theme.color.br1 },
          'svg path': { fill: theme.color.wh },
          ...(disableCoreUiControlIcons
            ? {
                'svg rect': { fill: disabledColor },
                'svg path': { fill: theme.color.wh },
              }
            : {}),
        }}
        size={size}
      />
    ) : (
      <PlayActive controlColor={controlColor} />
    );

    const renderPlayIcon = use2024GapCoreUiIcons ? (
      <PlaySquareIcon
        brandName={Brands.Gap}
        brandProp={Brands.Gap}
        colorTheme={colorTheme}
        // @ts-ignore
        css={{
          'svg:hover rect': { fill: theme.color.br1 },
          'svg:hover path': { fill: theme.color.wh },
          ...(disableCoreUiControlIcons
            ? {
                'svg rect': { fill: disabledColor },
                'svg path': { fill: theme.color.wh },
              }
            : {}),
        }}
        size={size}
      />
    ) : (
      <PlayIcon controlColor={controlColor} />
    );

    return (
      <StyledButton
        ref={ref}
        aria-label={ariaLabel}
        aria-pressed={active}
        // @ts-ignore
        css={{
          ...defaultPositionForPlayPause,
          bottom: use2024GapCoreUiIcons ? 11 : defaultPositionForPlayPause.bottom,
        }}
        disabled={use2024GapCoreUiIcons && disableCoreUiControlIcons}
        onBlur={() => {
          if (use2024GapCoreUiIcons && disableCoreUiControlIcons) {
            return;
          }
          handleBlur();
          React.startTransition(() => setActive(false));
        }}
        onClick={(event: React.MouseEvent<Node>) => {
          if (use2024GapCoreUiIcons && disableCoreUiControlIcons) {
            return;
          }
          playPauseHandleFunc(true);
          event.preventDefault();
        }}
        onFocus={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(true)))}
        onMouseEnter={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(true)))}
        onMouseLeave={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(false)))}
        onMouseOver={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(true)))}
      >
        {onBuffer && <Spinner />}
        {active ? renderPlayActive : renderPlayIcon}
      </StyledButton>
    );
  }
);

export const Mute = forwardRef<HTMLButtonElement, MuteControlProps & CustomCoreUiIconProps>(
  ({ ariaLabel, handleMute, handleBlur, controlColor, disableCoreUiControlIcons = false, enabledFeatures, isDesktop, theme, colorTheme }, ref): JSX.Element => {
    const [active, setActive] = useState(false);

    const isGap = [Brands.Gap, Brands.GapFactoryStore].includes(theme.brand);
    const use2024GapCoreUiIcons = isGap && !!enabledFeatures?.['fui-gap-video-icons-2024'];

    const size = isDesktop ? { width: '30px', height: '30px' } : { width: '28px', height: '28px' };

    const renderedMuteActive = use2024GapCoreUiIcons ? (
      <CoreUiMuteIcon
        brandName={Brands.Gap}
        brandProp={Brands.Gap}
        colorTheme={colorTheme}
        // @ts-ignore
        css={{
          'svg rect': { fill: theme.color.br1 },
          'svg path': { fill: theme.color.wh },
        }}
        size={size}
      />
    ) : (
      <MuiMuteActive controlColor={controlColor} />
    );

    const renderedMuteIcon = use2024GapCoreUiIcons ? (
      <CoreUiMuteIcon
        brandName={Brands.Gap}
        brandProp={Brands.Gap}
        colorTheme={colorTheme}
        // @ts-ignore
        css={{
          'svg:hover rect': { fill: theme.color.br1 },
          'svg:hover path': { fill: theme.color.wh },
          ...(disableCoreUiControlIcons
            ? {
                'svg rect': { fill: disabledColor },
                'svg path': { fill: theme.color.wh },
              }
            : {}),
        }}
        size={size}
      />
    ) : (
      <MuiMuteIcon controlColor={controlColor} />
    );

    return (
      <StyledButton
        ref={ref}
        aria-label={ariaLabel}
        aria-pressed={active}
        // @ts-ignore
        css={{
          ...defaultPositionForMute,
          bottom: use2024GapCoreUiIcons ? 11 : defaultPositionForMute.bottom,
        }}
        disabled={use2024GapCoreUiIcons && disableCoreUiControlIcons}
        onBlur={() => {
          if (use2024GapCoreUiIcons && disableCoreUiControlIcons) {
            return;
          }
          handleBlur();
          React.startTransition(() => setActive(false));
        }}
        onClick={(event: React.MouseEvent<Node>) => {
          if (use2024GapCoreUiIcons && disableCoreUiControlIcons) {
            return;
          }
          handleMute(true);
          event.preventDefault();
        }}
        onFocus={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(true)))}
        onMouseEnter={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(true)))}
        onMouseLeave={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(false)))}
        onMouseOver={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(true)))}
      >
        {active ? renderedMuteActive : renderedMuteIcon}
      </StyledButton>
    );
  }
);

export const Unmute = forwardRef<HTMLButtonElement, MuteControlProps & CustomCoreUiIconProps>(
  ({ ariaLabel, handleBlur, handleMute, controlColor, disableCoreUiControlIcons = false, enabledFeatures, isDesktop, theme, colorTheme }, ref): JSX.Element => {
    const [active, setActive] = useState(false);

    const isGap = [Brands.Gap, Brands.GapFactoryStore].includes(theme.brand);
    const use2024GapCoreUiIcons = isGap && !!enabledFeatures?.['fui-gap-video-icons-2024'];

    const size = isDesktop ? { width: '30px', height: '30px' } : { width: '28px', height: '28px' };

    const renderedUnmuteActive = use2024GapCoreUiIcons ? (
      <CoreUiUnmuteIcon
        brandName={Brands.Gap}
        brandProp={Brands.Gap}
        colorTheme={colorTheme}
        // @ts-ignore
        css={{
          'svg rect': { fill: theme.color.br1 },
          'svg path': { fill: theme.color.wh },
        }}
        size={size}
      />
    ) : (
      <MuiUnmuteActive controlColor={controlColor} />
    );

    const renderedUnmuteIcon = use2024GapCoreUiIcons ? (
      <CoreUiUnmuteIcon
        brandName={Brands.Gap}
        brandProp={Brands.Gap}
        colorTheme={colorTheme}
        // @ts-ignore
        css={{
          'svg:hover rect': { fill: theme.color.br1 },
          'svg:hover path': { fill: theme.color.wh },
          ...(disableCoreUiControlIcons
            ? {
                'svg rect': { fill: disabledColor },
                'svg path': { fill: theme.color.wh },
              }
            : {}),
        }}
        size={size}
      />
    ) : (
      <MuiUnmuteIcon controlColor={controlColor} />
    );

    return (
      <StyledButton
        ref={ref}
        aria-label={ariaLabel}
        aria-pressed={active}
        // @ts-ignore
        css={{
          ...defaultPositionForMute,
          bottom: use2024GapCoreUiIcons ? 11 : defaultPositionForMute.bottom,
        }}
        disabled={use2024GapCoreUiIcons && disableCoreUiControlIcons}
        onBlur={() => {
          if (use2024GapCoreUiIcons && disableCoreUiControlIcons) {
            return;
          }
          handleBlur();
          React.startTransition(() => setActive(false));
        }}
        onClick={(event: React.MouseEvent<Node>) => {
          if (use2024GapCoreUiIcons && disableCoreUiControlIcons) {
            return;
          }
          handleMute(true);
          event.preventDefault();
        }}
        onFocus={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(true)))}
        onMouseEnter={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(true)))}
        onMouseLeave={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(false)))}
        onMouseOver={() => (use2024GapCoreUiIcons && disableCoreUiControlIcons ? null : React.startTransition(() => setActive(true)))}
      >
        {active ? renderedUnmuteActive : renderedUnmuteIcon}
      </StyledButton>
    );
  }
);

export const PLAYER_CUSTOM_CONTROLS_ID = 'player-custom-controls';

export const PlayerCustomControls = ({
  isCustomControls,
  readyToPlay,
  onBuffer,
  handlePause,
  handlePlay,
  handleMute,
  handleVolume,
  hideMuteControl,
  isPlaying,
  controlColor,
  customControlStyles,
  muted,
  disableCoreUiControlIcons,
  colorTheme,
}: PlayerCustomControlProps): JSX.Element | null => {
  const theme = useTheme() as unknown as GapTheme;
  const enabledFeatures = useEnabledFeatures();
  const isDesktop = useViewportIsLarge();
  const use2024GapAudioIcons = theme.brand === Brands.Gap && !!enabledFeatures['fui-gap-video-icons-2024'];

  const { localize } = useLocalize();
  const ariaLabelPlay = localize('marketing.VideoComponent.ariaLabelPlay') as PlayPauseControlProps['ariaLabel'];
  const ariaLabelPause = localize('marketing.VideoComponent.ariaLabelPause') as PlayPauseControlProps['ariaLabel'];
  const ariaLabelMute = localize('marketing.VideoComponent.ariaLabelMute') as MuteControlProps['ariaLabel'];
  const ariaLabelUnmute = localize('marketing.VideoComponent.ariaLabelUnmute') as MuteControlProps['ariaLabel'];
  const ariaLabelVolume = localize('marketing.VideoComponent.ariaLabelVolume') as InputRangeVolumeProps['ariaLabel'];
  const playRef = useRef<HTMLButtonElement>(null);
  const pauseRef = useRef<HTMLButtonElement>(null);
  const muteRef = useRef<HTMLButtonElement>(null);
  const unMuteRef = useRef<HTMLButtonElement>(null);
  const [inFocusPlayPause, setInPlayPauseFocus] = useState(false);
  const [inFocusMute, setInMuteFocus] = useState(false);
  const muteRefVolumeParent = useRef<HTMLDivElement>(null);
  const topParentRef = useRef<HTMLDivElement>(null);
  const backgroundForVolumeSlider = controlColor && controlColor?.fillColor ? controlColor?.fillColor : '#ffffff';

  const handlePlayClick = (state: boolean) => {
    handlePlay(state);
    if (!inFocusPlayPause) setInPlayPauseFocus(true);
  };

  const handlePauseClick = (state: boolean) => {
    handlePause(state);
    if (!inFocusPlayPause) setInPlayPauseFocus(true);
  };

  const handleMuteClick = (state: boolean) => {
    handleMute(state);
    if (!inFocusMute) setInMuteFocus(true);
  };

  const handleBlur = () => {
    setInPlayPauseFocus(false);
    setInMuteFocus(false);
  };

  const closeVolumeAfterTabOnPlayPause = (event: React.KeyboardEvent<Element>) => {
    if (event.key === 'Tab') {
      const isLandedOnPlay =
        playRef.current !== null &&
        event.currentTarget.getAttribute('aria-label') === ariaLabelPlay &&
        muteRefVolumeParent?.current?.classList.contains('keepOpen');
      const isLandedOnPause =
        pauseRef.current !== null &&
        event.currentTarget.getAttribute('aria-label') === ariaLabelPause &&
        muteRefVolumeParent?.current?.classList.contains('keepOpen');

      if (isLandedOnPlay) {
        muteRefVolumeParent?.current?.classList.remove('keepOpen');
      }
      if (isLandedOnPause) {
        muteRefVolumeParent?.current?.classList.remove('keepOpen');
      }
    }
  };

  const detectMouseOutOnButtonToFixBlur = useCallback(
    (e: React.MouseEvent<Node>) => {
      const playBtnElemTarget = e.currentTarget.firstChild as HTMLElement;
      const muteBtnElemTarget = e.currentTarget.lastChild?.firstChild as HTMLElement;

      playBtnElemTarget?.blur();
      muteBtnElemTarget?.blur();
    },
    [!inFocusPlayPause, !inFocusMute, muted]
  );

  useEffect(() => {
    if (playRef && inFocusPlayPause && !isPlaying) {
      playRef.current?.focus();
    }
    if (pauseRef && inFocusPlayPause && isPlaying) {
      pauseRef.current?.focus();
    }
    if (muteRef && inFocusMute) {
      muteRef.current?.focus();
    }
    if (unMuteRef && inFocusMute) {
      unMuteRef.current?.focus();
    }
  }, [inFocusMute, inFocusPlayPause, isPlaying, muted]);

  useEffect(
    () => () => {
      // before unmounting, remove all event listeners
      topParentRef.current?.removeEventListener('keyup', closeVolumeAfterTabOnPlayPause as any, false);
    },
    [topParentRef]
  );

  if (!isCustomControls || !readyToPlay) return null;

  const coreUiAudioControlStyles: CSSObject = {
    bottom: '35px',
    width: isDesktop ? '30px' : '28px',
  };

  return (
    <div
      ref={topParentRef}
      className={PLAYER_CUSTOM_CONTROLS_ID}
      // @ts-ignore
      css={customControlStyles}
      data-testid={PLAYER_CUSTOM_CONTROLS_ID}
      onFocus={(e: React.FocusEvent<Node>) => {
        e.target.addEventListener(
          'keyup',
          (e: Event) => {
            closeVolumeAfterTabOnPlayPause(e as any);
          },
          false
        );
      }}
      onMouseLeave={(e: React.MouseEvent<Node>) => detectMouseOutOnButtonToFixBlur(e)}
    >
      {isPlaying ? (
        <Pause
          ref={pauseRef}
          ariaLabel={ariaLabelPause}
          colorTheme={colorTheme}
          controlColor={controlColor}
          disableCoreUiControlIcons={disableCoreUiControlIcons}
          enabledFeatures={enabledFeatures}
          handleBlur={handleBlur}
          isDesktop={isDesktop}
          onBuffer={onBuffer}
          playPauseHandleFunc={handlePauseClick}
          theme={theme}
        />
      ) : (
        <Play
          ref={playRef}
          ariaLabel={ariaLabelPlay}
          colorTheme={colorTheme}
          controlColor={controlColor}
          disableCoreUiControlIcons={disableCoreUiControlIcons}
          enabledFeatures={enabledFeatures}
          handleBlur={handleBlur}
          isDesktop={isDesktop}
          onBuffer={onBuffer}
          playPauseHandleFunc={handlePlayClick}
          theme={theme}
        />
      )}
      {hideMuteControl ? null : (
        <StyledDivWrapperForMuteControls
          data-testid='styled-mute-controls'
          onFocus={() => {
            if (use2024GapAudioIcons && disableCoreUiControlIcons) {
              return;
            }
            if (muteRefVolumeParent.current !== null) {
              muteRefVolumeParent.current.classList.add('keepOpen');
            }
          }}
          onMouseEnter={() => {
            if (use2024GapAudioIcons && disableCoreUiControlIcons) {
              return;
            }
            if (muteRefVolumeParent.current !== null) {
              muteRefVolumeParent.current.classList.add('staysOpen');
            }
          }}
          onMouseLeave={() => {
            if (use2024GapAudioIcons && disableCoreUiControlIcons) {
              return;
            }
            if (muteRefVolumeParent.current !== null) {
              muteRefVolumeParent.current.classList.remove('keepOpen');
              muteRefVolumeParent.current.classList.remove('staysOpen');
            }
          }}
        >
          {muted ? (
            <Mute
              ref={muteRef}
              ariaLabel={ariaLabelMute}
              colorTheme={colorTheme}
              controlColor={controlColor}
              disableCoreUiControlIcons={disableCoreUiControlIcons}
              enabledFeatures={enabledFeatures}
              handleBlur={handleBlur}
              handleMute={handleMuteClick}
              isDesktop={isDesktop}
              theme={theme}
            />
          ) : (
            <Unmute
              ref={unMuteRef}
              ariaLabel={ariaLabelUnmute}
              colorTheme={colorTheme}
              controlColor={controlColor}
              disableCoreUiControlIcons={disableCoreUiControlIcons}
              enabledFeatures={enabledFeatures}
              handleBlur={handleBlur}
              handleMute={handleMuteClick}
              isDesktop={isDesktop}
              theme={theme}
            />
          )}
          <StyledDivForInputVolume
            ref={muteRefVolumeParent}
            customStyles={use2024GapAudioIcons ? coreUiAudioControlStyles : {}}
            data-testid='styled-input-volume'
            isDesktop={isDesktop}
            onBlur={() => {
              if (use2024GapAudioIcons && disableCoreUiControlIcons) {
                return;
              }
              if (muteRefVolumeParent.current !== null) {
                muteRefVolumeParent.current.classList.remove('keepOpen');
              }
            }}
            onMouseEnter={() => {
              if (use2024GapAudioIcons && disableCoreUiControlIcons) {
                return;
              }
              if (muteRefVolumeParent.current !== null) {
                muteRefVolumeParent.current.classList.add('keepOpen');
              }
            }}
            onMouseLeave={() => {
              if (use2024GapAudioIcons && disableCoreUiControlIcons) {
                return;
              }
              if (muteRefVolumeParent.current !== null) {
                muteRefVolumeParent.current.classList.remove('keepOpen');
              }
            }}
            style={{ background: backgroundForVolumeSlider }}
          >
            <InputRangeVolumeWithShadowDecorator
              ariaLabel={ariaLabelVolume}
              controlColor={controlColor}
              handleVolume={e => handleVolume(e)}
              isMuted={muted}
              orient='vertical'
            />
          </StyledDivForInputVolume>
        </StyledDivWrapperForMuteControls>
      )}
    </div>
  );
};
