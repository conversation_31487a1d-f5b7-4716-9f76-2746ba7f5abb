// @ts-nocheck
'use client';
import { ComponentMeta, StoryFn } from '@storybook/react';
import React from 'react';
import { VideoComponent, VideoComponentProps } from '.';
import {
  autoPlayData,
  defaultThirdPartyData,
  defaultWithFallback,
  directPathData,
  directPathWithPlayButton,
  fallbackWithInvalidVideoUrlData,
  loopData,
  mutedData,
  noAutoPlayData,
  noLoopData,
  nonMutedData,
  responsiveData,
  withControls,
  withCustomControls,
  withCustomPlayIcon,
  withGapCoreUiControlIcons,
  withGapCoreUiControlIconsDisabled,
  withoutControls,
  withBackgroundImage,
} from './__fixtures__';
import README from './README.mdx';

export default {
  title: 'Common/JSON Components (Marketing)/VideoComponent',
  component: VideoComponent,
  parameters: {
    docs: {
      page: README,
    },
    eyes: { include: false },
    knobs: { disable: true },
  },
  tags: ['exclude'],
  table: {
    summary: 'ControlColorTypes',
    defaultValue: { summary: 'ControlColorTypes.PRIMARY' },
  },
  argTypes: {
    controlsIconsColor: {
      control: { type: 'select' },
      options: ['primary', 'secondary'],
      table: {
        type: {
          type: { required: false },
          summary: 'ControlColorTypes',
          detail: 'PRIMARY -> primary\nSECONDARY -> secondary',
        },
        defaultValue: { summary: 'primary' },
      },
    },
  },
} as ComponentMeta<typeof VideoComponent>;

const VideoComponentTemplate: StoryFn<typeof VideoComponent> = args => <VideoComponent {...args} />;

export const Default = VideoComponentTemplate.bind({});
Default.args = defaultThirdPartyData;

export const DefaultWithFallbackImage = VideoComponentTemplate.bind({});
DefaultWithFallbackImage.args = defaultWithFallback;

export const Muted = VideoComponentTemplate.bind({});
Muted.args = mutedData;

export const NonMuted = VideoComponentTemplate.bind({});
NonMuted.args = nonMutedData;

export const AutoPlay = VideoComponentTemplate.bind({});
AutoPlay.args = autoPlayData;

export const NoAutoPlay = VideoComponentTemplate.bind({});
NoAutoPlay.args = noAutoPlayData;

export const InfiniteLoop = VideoComponentTemplate.bind({});
InfiniteLoop.args = loopData;

export const NoInfiniteLoop = VideoComponentTemplate.bind({});
NoInfiniteLoop.args = noLoopData;

export const WithControls = VideoComponentTemplate.bind({});
WithControls.args = withControls;

export const WithoutControls = VideoComponentTemplate.bind({});
WithoutControls.args = withoutControls;

export const Responsive = VideoComponentTemplate.bind({});
Responsive.args = responsiveData as VideoComponentProps;

export const DirectPathHosted = VideoComponentTemplate.bind({});
DirectPathHosted.args = directPathData;

export const WithCustomStartButton = VideoComponentTemplate.bind({});
WithCustomStartButton.args = directPathWithPlayButton;

export const WithCustomControls = VideoComponentTemplate.bind({});
WithCustomControls.args = withCustomControls as VideoComponentProps;

export const WithGapCoreUIControlIcons = VideoComponentTemplate.bind({});
WithGapCoreUIControlIcons.args = withGapCoreUiControlIcons as VideoComponentProps;

export const WithGapCoreUIControlIconsDisabled = VideoComponentTemplate.bind({});
WithGapCoreUIControlIconsDisabled.args = withGapCoreUiControlIconsDisabled as VideoComponentProps;

export const fallbackImageWithInvalidVideoUrl = VideoComponentTemplate.bind({});
fallbackImageWithInvalidVideoUrl.args = fallbackWithInvalidVideoUrlData;

export const WithCustomPlayIcon = VideoComponentTemplate.bind({});
WithCustomPlayIcon.args = withCustomPlayIcon as VideoComponentProps;

export const BackgroundImageTest = VideoComponentTemplate.bind({});
BackgroundImageTest.args = withBackgroundImage as VideoComponentProps;
