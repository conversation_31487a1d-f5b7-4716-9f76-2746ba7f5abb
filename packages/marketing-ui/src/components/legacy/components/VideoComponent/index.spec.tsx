// @ts-nocheck
jest.mock('next/dynamic', () => {
  const reactPlayer = require('react-player');
  return () => reactPlayer;
});
import React from 'react';
import { axe } from 'jest-axe';
import { CSSObject } from '@ecom-next/core/react-stitch';
import { mountWithContext, ReactWrapper, MountOptions, act } from 'test-utils';
import { screen, render, fireEvent, waitFor } from 'test-utils';
import { SMALL, LARGE, XLARGE } from '@ecom-next/core/breakpoint-provider';
import ReactPlayer from 'react-player';
import { noop } from 'lodash';
import { VideoComponent, VideoComponentProps } from '.';

const renderVideoPlayer = (props?: VideoComponentProps, options?: MountOptions): ReactWrapper<VideoComponentProps> =>
  mountWithContext(<VideoComponent hideMuteControl={false} url='https://www.youtube.com/watch?v=dQw4w9WgXcQ' {...props} />, options);

describe('VideoComponent', () => {
  describe('containerStyle prop', () => {
    test('sets desktop container styles correctly', () => {
      const desktopStyle = { height: '10px' };
      const videoPlayer = renderVideoPlayer(
        {
          containerStyle: { desktop: desktopStyle },
        },
        { breakpoint: LARGE }
      );
      expect(videoPlayer).toHaveStyleRule('height', desktopStyle.height);
    });

    test('sets mobile container styles correctly', () => {
      const mobileStyle = { width: '20px' };
      const videoPlayer = renderVideoPlayer(
        {
          containerStyle: { mobile: mobileStyle },
        },
        { breakpoint: SMALL }
      );
      expect(videoPlayer).toHaveStyleRule('width', mobileStyle.width);
    });
  });

  describe('Use props correctly', () => {
    test('should pass correct config to react-player when provided', () => {
      const props = {
        playing: true,
        url: 'https://www.youtube.com/watch?v=QohH89Eu5iM',
        muted: true,
        loop: true,
      };
      const reactPlayer = renderVideoPlayer(props).find(ReactPlayer);
      expect(reactPlayer).toHaveProp({
        playing: props.playing,
        url: props.url,
        muted: props.muted,
        loop: props.loop,
      });
    });

    test('should use custom play icon when provided', () => {
      const props = {
        playIcon: {
          alt: 'play',
          src: 'arrow.jpg',
        },
      };
      const reactPlayer = renderVideoPlayer(props).find(ReactPlayer);
      expect(reactPlayer.props().playIcon?.props).toEqual({
        alt: props.playIcon.alt,
        src: props.playIcon.src,
      });
    });

    test('should render with correct height when provided', () => {
      const props = {
        height: '10px',
      };
      const reactPlayer = renderVideoPlayer(props).find(ReactPlayer);
      expect(reactPlayer).toHaveProp({ height: '10px' });
    });

    test('hover effects should work for react-player container when playOnHover pauseOnHover provided', () => {
      const props = {
        playOnHover: true,
        pauseOnHover: true,
        isCustomControls: false,
        isLarge: false,
        containerStyle: {},
        videoContainerAbsolute: false,
        controlColor: {},
        playerProps: {
          url: 'https://www.youtube.com/watch?v=QohH89Eu5iM',
          playing: true,
          muted: true,
          loop: true,
        },
        useCustomPlayIcon: false,
        useCustomPlayIconSrc: '',
        useCustomPauseIconSrc: '',
        useCustomPauseIcon: false,
      };
      const handlePlayOnHover = jest.fn();
      const handlePauseOnHover = jest.fn();

      render(<VideoComponent {...props} />);

      const reactPlayer = screen.getByTestId('videocomponent-container');

      fireEvent.mouseOver(reactPlayer);
      waitFor(() => expect(handlePlayOnHover).toBeCalled());

      fireEvent.mouseLeave(reactPlayer);
      waitFor(() => expect(handlePauseOnHover).toBeCalled());

      jest.restoreAllMocks();
    });

    describe('desktop props provided', () => {
      describe('desktopUrl', () => {
        test('provided - show desktopUrl at desktop; url at mobile', () => {
          const props = {
            url: 'https://www.youtube.com/watch?v=QohH89Eu5iM',
            desktopUrl: 'https://www.youtube.com/watch?v=VBKNoLcj8jA',
          };
          const reactPlayer1 = renderVideoPlayer(props, {
            breakpoint: XLARGE,
          }).find(ReactPlayer);
          const reactPlayer2 = renderVideoPlayer(props, {
            breakpoint: SMALL,
          }).find(ReactPlayer);
          expect(reactPlayer1).toHaveProp({
            url: props.desktopUrl,
          });
          expect(reactPlayer2).toHaveProp({
            url: props.url,
          });
        });
        test('not provided - show url at desktop and mobile', () => {
          const props = {
            url: 'https://www.youtube.com/watch?v=QohH89Eu5iM',
          };
          const reactPlayer1 = renderVideoPlayer(props, {
            breakpoint: XLARGE,
          }).find(ReactPlayer);
          const reactPlayer2 = renderVideoPlayer(props, {
            breakpoint: SMALL,
          }).find(ReactPlayer);
          expect(reactPlayer1).toHaveProp({
            url: props.url,
          });
          expect(reactPlayer2).toHaveProp({
            url: props.url,
          });
        });
      });
      describe('fallbackImage desktopSrc', () => {
        test('provided - show desktopSrc at desktop; src at mobile', () => {
          const props = {
            url: '123',
            fallbackImage: {
              alt: 'Video Failed to Load',
              src: '/Asset_Archive/GPWeb/content/0029/578/915/assets/HOL223281_FallBackimg_MOB.jpg',
              desktopSrc: '/Asset_Archive/GPWeb/content/0029/578/915/assets/HOL223281_imgRFallBack_DESK.jpg',
            },
          };
          const fallbackImage1 = renderVideoPlayer(props, {
            breakpoint: XLARGE,
          }).find('img');
          const fallbackImage2 = renderVideoPlayer(props, {
            breakpoint: SMALL,
          }).find('img');
          expect(fallbackImage1.props().src).toContain(props.fallbackImage.desktopSrc);
          expect(fallbackImage2.props().src).toContain(props.fallbackImage.src);
        });
        test('not provided - show src at desktop and mobile', () => {
          const props = {
            url: '123',
            fallbackImage: {
              alt: 'Video Failed to Load',
              src: '/Asset_Archive/GPWeb/content/0029/578/915/assets/HOL223281_FallBackimg_MOB.jpg',
            },
          };
          const fallbackImage1 = renderVideoPlayer(props, {
            breakpoint: XLARGE,
          }).find('img');
          const fallbackImage2 = renderVideoPlayer(props, {
            breakpoint: SMALL,
          }).find('img');
          expect(fallbackImage1.props().src).toContain(props.fallbackImage.src);
          expect(fallbackImage2.props().src).toContain(props.fallbackImage.src);
        });
      });
    });
  });

  test('should pass onPlay and onPause to react-player correctly', () => {
    const props = {
      onPlay: noop,
      onPause: noop,
    };
    const reactPlayer = renderVideoPlayer(props).find(ReactPlayer);
    expect(reactPlayer.props().onPlay).not.toBeFalsy();
    expect(reactPlayer.props().onPause).not.toBeFalsy();
  });

  test('should pass fallback image alt and src if url is not valid', () => {
    const props = {
      url: 'https://www.youtube.com/watch?v=potato',
      fallbackImage: {
        alt: 'fall back',
        src: '/pathname/to/image',
      },
    };
    const reactPlayer = renderVideoPlayer(props).find('img');
    expect(reactPlayer).toHaveProp({
      alt: 'fall back',
      src: '/pathname/to/image',
    });
  });

  describe('Autoplay', () => {
    test('should mute the video if auto-playing', () => {
      const props = {
        playing: true,
      };

      const reactPlayer = renderVideoPlayer(props).find(ReactPlayer);
      expect(reactPlayer.props().muted).toBe(true);
    });

    test('should mute the video if auto-playing, even if muted explicitly false', () => {
      const props = {
        playing: true,
        muted: false,
      };

      const reactPlayer = renderVideoPlayer(props).find(ReactPlayer);
      expect(reactPlayer.props().muted).toBe(true);
    });

    test('should allow unmute when not auto-playing', () => {
      const props = {
        playing: false,
        muted: false,
      };

      const reactPlayer = renderVideoPlayer(props).find(ReactPlayer);

      expect(reactPlayer.props().muted).toBe(false);
    });

    test('should inline the video if autoplaying for Safari compatibility', () => {
      const props = {
        playing: true,
        playsinline: false,
      };

      const reactPlayer = renderVideoPlayer(props).find(ReactPlayer);

      expect(reactPlayer.props().playsinline).toBe(true);
    });
  });

  describe('Custom controls in VideoComponent', () => {
    test('uses custom CSS with customControls', () => {
      const props = {
        url: 'https://player.vimeo.com/video/702172644?badge=0&autoplay=1&loop=1&muted=1&player_id=0&app_id=58479&h=1cf1da973d',
        customControls: true,
        containerStyle: {
          desktop: { width: '50%', position: 'fixed' } as CSSObject,
        },
      };
      const videoPlayer = renderVideoPlayer(props, { breakpoint: LARGE });
      expect(videoPlayer).toHaveStyleRule('position', props.containerStyle.desktop.position);
      expect(videoPlayer).toHaveStyleRule('width', props.containerStyle.desktop.width);
    });

    test('custom controls default is false', () => {
      const props = {
        url: 'https://player.vimeo.com/video/702172644?badge=0&autoplay=1&loop=1&muted=1&player_id=0&app_id=58479&h=1cf1da973d',
        customControls: true,
        muted: true,
        playing: false,
        loop: false,
        width: '640px',
        height: '360px',
        handlePause: noop,
        handlePlay: noop,
        isCustomControls: false,
        onBuffer: false,
        isPlaying: true,
        readyToPlay: false,
        controls: true,
      };
      const reactPlayer = renderVideoPlayer(props).find(ReactPlayer);
      expect(reactPlayer.props().url).toBe(props.url);
    });

    test('ready to play', () => {
      const props = {
        url: 'https://player.vimeo.com/video/702172644?badge=0&autoplay=1&loop=1&muted=1&player_id=0&app_id=58479&h=1cf1da973d',
        customControls: true,
        muted: true,
        playing: false,
        loop: false,
        width: '640px',
        height: '360px',
        handlePause: noop,
        handlePlay: noop,
        isCustomControls: false,
        onBuffer: true,
        isPlaying: true,
        readyToPlay: true,
        controls: true,
      };
      const reactPlayer = renderVideoPlayer(props).find(ReactPlayer);
      expect(reactPlayer.props().url).toBe(props.url);

      const readyToPlay = jest.fn(() => props.readyToPlay);

      expect(reactPlayer.props().readyToPlay).toBe(readyToPlay());
    });

    test('custom controls onBuffer false', () => {
      const onBufferHandler = jest.fn((buffer: boolean): string => {
        if (buffer) return 'buffer ready';
        return 'buffer not ready';
      });
      const props = {
        url: 'https://player.vimeo.com/video/702172644?badge=0&autoplay=1&loop=1&muted=1&player_id=0&app_id=58479&h=1cf1da973d',
        customControls: true,
        muted: true,
        playing: false,
        loop: false,
        width: '640px',
        height: '360px',
        handlePause: noop,
        handlePlay: noop,
        isCustomControls: false,
        onBuffer: false,
        isPlaying: true,
        readyToPlay: false,
        controls: true,
      };
      const reactPlayer = renderVideoPlayer(props).find(ReactPlayer);
      expect(reactPlayer.props().url).toBe(props.url);
      onBufferHandler(props.onBuffer);
      expect(onBufferHandler).toHaveReturnedWith('buffer not ready');
    });

    test('custom controls onBuffer true', () => {
      const onBufferHandler = jest.fn((buffer: boolean): string => {
        if (buffer) return 'buffer ready';
        return 'buffer not ready';
      });
      const props = {
        url: 'https://player.vimeo.com/video/702172644?badge=0&autoplay=1&loop=1&muted=1&player_id=0&app_id=58479&h=1cf1da973d',
        customControls: true,
        muted: true,
        playing: false,
        loop: false,
        width: '640px',
        height: '360px',
        handlePause: noop,
        handlePlay: noop,
        isCustomControls: false,
        onBuffer: true,
        isPlaying: true,
        readyToPlay: false,
        controls: true,
      };
      const reactPlayer = renderVideoPlayer(props).find(ReactPlayer);
      expect(reactPlayer.props().url).toBe(props.url);
      onBufferHandler(props.onBuffer);
      expect(onBufferHandler).toHaveReturnedWith('buffer ready');
    });

    test('non valid URL', () => {
      const props = {
        url: 'https://www.youtube.com/watch?v=QoEu5iM',
      };
      render(<VideoComponent {...props} />);
      expect(screen.getByAltText('video not available')).toBeInTheDocument();
    });

    it('check volume set to 0 ', () => {
      const propsNoMute = {
        url: 'https://player.vimeo.com/video/702172644?badge=0&autoplay=1&loop=1&muted=1&player_id=0&app_id=58479&h=1cf1da973d',
        customControls: true,
        loop: false,
        width: '640px',
        height: '360px',
        handlePause: noop,
        handlePlay: noop,
        isCustomControls: true,
        onBuffer: false,
        readyToPlay: true,
        controls: true,
      };

      const reactPlayer = renderVideoPlayer(propsNoMute).find(ReactPlayer);
      expect(reactPlayer.props().volume).toBe(0);
    });
  });

  describe('a11y', () => {
    it('should not have a11y violations', async () => {
      const props: VideoComponentProps = {
        isCustomControls: true,
        onBuffer: false,
        isPlaying: false,
        readyToPlay: true,
        url: 'https://player.vimeo.com/video/702172644?badge=0&autoplay=1&loop=1&muted=1&player_id=0&app_id=58479&h=1cf1da973d',
        customControls: true,
        muted: true,
        playing: false,
        loop: false,
        width: '640px',
        height: '360px',
        handlePause: jest.fn(),
        handlePlay: jest.fn(),
        controls: true,
      };
      const { container } = render(<VideoComponent {...props} />);

      expect(await axe(container)).toHaveNoViolations();
    });
  });

  describe('Placeholder Image', () => {
    test('should preload fallback image with high fetch priority', () => {
      const props = {
        url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        fallbackImage: {
          alt: 'Fallback Image',
          src: '/path/to/fallback.jpg',
        },
      };

      const { container } = render(<VideoComponent {...props} />);

      // Check if the img with fetchPriority="high" attribute exists
      const priorityImage = screen.getByRole('img', { hidden: true });
      expect(priorityImage).toBeInTheDocument();
      expect(priorityImage).toHaveAttribute('src', '/path/to/fallback.jpg');
      expect(priorityImage).toHaveAttribute('fetchPriority', 'high');
    });
  });
});
