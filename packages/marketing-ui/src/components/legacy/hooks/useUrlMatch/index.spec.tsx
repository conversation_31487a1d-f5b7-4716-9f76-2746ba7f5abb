// @ts-nocheck
import { useUrlMatch } from './index';

describe('The useUrlMatch Hook Test Suite', () => {
  const fakeHashWithSizeInfo = '#department=165&size=74-1:929|75-1:939|76-1:949|82-1:1086|78-1:992|104-1:1433&banner=vi_sbs_tg';
  const fakeHashWithNoSizeInfo = '#LoremIpsumAndAllThatJazz';
  const setUrlHash = (hash: string) => {
    window.location.hash = hash;
  };

  afterEach(() => {
    setUrlHash('');
  });

  it('should return true when URL has Size info', () => {
    setUrlHash(fakeHashWithSizeInfo);
    const doesCurrentUrlMatch = useUrlMatch();

    expect(doesCurrentUrlMatch(fakeHashWithSizeInfo, ['size'])).toBe(true);
  });

  it('should return false when URL does not have Size info', () => {
    setUrlHash(fakeHashWithNoSizeInfo);
    const doesCurrentUrlMatch = useUrlMatch();

    expect(doesCurrentUrlMatch(fakeHashWithSizeInfo, ['size'])).toBe(false);
  });
});
