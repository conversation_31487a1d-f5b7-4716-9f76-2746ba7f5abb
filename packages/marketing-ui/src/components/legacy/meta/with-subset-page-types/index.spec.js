// @ts-nocheck
import React from 'react';
import { FeatureFlagsProvider } from '@ecom-next/core/legacy/feature-flags';
import { render, screen } from 'test-utils';
import WithSubsetPageTypes from './index';

const testChildText = 'Test Component';
const MarketingComponent = () => <div>{testChildText}</div>;

const MarketingComponentWrapper = WithSubsetPageTypes(MarketingComponent);

describe('Include or exclude components from Page Types', () => {
  describe('excludePageTypes for non-authoring content', () => {
    const renderWithExcludedPages = (excludePageTypes, pageType) =>
      render(
        <FeatureFlagsProvider enabledFeatures={{ 'mui-component-include-exclude-from-page': true }}>
          <MarketingComponentWrapper
            data={{
              excludePageTypes,
            }}
          />
        </FeatureFlagsProvider>,
        { appState: { pageType } }
      );

    it('should not render component if page present in  excludePageTypes array', () => {
      renderWithExcludedPages(['home', 'category'], 'home');
      expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
    });

    it('should render component if page not present in excludePageTypes array', () => {
      renderWithExcludedPages(['home', 'category'], 'product');
      expect(screen.queryByText(testChildText)).toBeInTheDocument();
    });
  });

  describe('includePageTypes for non-authoring content', () => {
    const renderWithIncludedPages = (includePageTypes, pageType) =>
      render(
        <FeatureFlagsProvider enabledFeatures={{ 'mui-component-include-exclude-from-page': true }}>
          <MarketingComponentWrapper
            data={{
              includePageTypes,
            }}
          />
        </FeatureFlagsProvider>,
        { appState: { pageType } }
      );

    it('should render the component for pages present in includePageTypes array', () => {
      renderWithIncludedPages(['home', 'category'], 'home');
      expect(screen.queryByText(testChildText)).toBeInTheDocument();
    });

    it('should not render component if it is not present in includePageTypes array', () => {
      renderWithIncludedPages(['home', 'category'], 'product');
      expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
    });
  });

  //NON - Authorable content - excluded & includedPageTypes
  describe('Usecases with both includePageTypes and excludePageTypes arrays having values', () => {
    it('should hide component on the page if not present in includePageTypes and present in excludePagetypes array', () => {
      render(
        <FeatureFlagsProvider enabledFeatures={{ 'mui-component-include-exclude-from-page': true }}>
          <MarketingComponentWrapper
            data={{
              includePageTypes: ['home', 'category'],
              excludePageTypes: ['product'],
            }}
          />
        </FeatureFlagsProvider>,
        { appState: { pageType: 'product' } }
      );
      expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
    });

    const subsetObj = {};
    const checkSubsetDataStructures = (subset, pageType) => {
      subsetObj[subset.key] = subset.value;
      const structures = [
        {
          meta: subsetObj,
        },
        {
          data: subsetObj,
        },
        {
          data: {
            meta: subsetObj,
          },
        },
        {
          data: {
            options: subsetObj,
          },
        },
      ];
      structures.forEach(struct => {
        render(
          <FeatureFlagsProvider enabledFeatures={{ 'mui-component-include-exclude-from-page': false }}>
            <MarketingComponentWrapper {...struct} />
          </FeatureFlagsProvider>,
          {
            appState: { pageType },
          }
        );
        expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
      });
    };

    describe('Using mock data - includePageTypes', () => {
      it('should not render component if not in includePageTypes array', () => {
        checkSubsetDataStructures({ key: 'includePageTypes', value: ['home', 'category'] }, 'product');
      });
    });

    describe('no exclude and includePageTypes array', () => {
      it('should render the component regardless of page if bot exclude and include arrays are empty', () => {
        render(
          <FeatureFlagsProvider enabledFeatures={{ 'mui-component-include-exclude-from-page': true }}>
            <MarketingComponentWrapper />
          </FeatureFlagsProvider>,
          { appState: { pageType: 'home' } }
        );
        expect(screen.queryByText(testChildText)).toBeInTheDocument();
      });
    });

    //Feature flag turned off
    describe('excludePageTypes for non-authoring content - featureFlag turned off', () => {
      const renderWithExcludedPages = (excludePageTypes, pageType) =>
        render(
          <FeatureFlagsProvider enabledFeatures={{ 'mui-component-include-exclude-from-page': false }}>
            <MarketingComponentWrapper
              data={{
                excludePageTypes,
              }}
            />
          </FeatureFlagsProvider>,
          { appState: { pageType } }
        );

      it('should not render component if page present in  excludePageTypes array', () => {
        renderWithExcludedPages(['home', 'category'], 'home');
        expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
      });

      it('should render component if page not present in excludePageTypes array', () => {
        renderWithExcludedPages(['home', 'category'], 'product');
        expect(screen.queryByText(testChildText)).toBeInTheDocument();
      });
    });

    describe('includePageTypes for non-authoring content - feature flag turned off', () => {
      const renderWithIncludedPages = (includePageTypes, pageType) =>
        render(
          <FeatureFlagsProvider enabledFeatures={{ 'mui-component-include-exclude-from-page': false }}>
            <MarketingComponentWrapper
              data={{
                includePageTypes,
              }}
            />
          </FeatureFlagsProvider>,
          { appState: { pageType } }
        );

      it('should render the component for pages present in includePageTypes array', () => {
        renderWithIncludedPages(['home', 'category'], 'home');
        expect(screen.queryByText(testChildText)).toBeInTheDocument();
      });

      it('should not render component if it is not present in includePageTypes array', () => {
        renderWithIncludedPages(['home', 'category'], 'product');
        expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
      });
    });

    //NON - Authorable content - excluded & includedPageTypes
    describe('Usecases with both includePageTypes and excludePageTypes arrays having values - feature flag turned off', () => {
      it('should hide component on the page if not present in includePageTypes and present in excludePagetypes array', () => {
        render(
          <FeatureFlagsProvider enabledFeatures={{ 'mui-component-include-exclude-from-page': false }}>
            <MarketingComponentWrapper
              data={{
                includePageTypes: ['home', 'category'],
                excludePageTypes: ['product'],
              }}
            />
          </FeatureFlagsProvider>,
          { appState: { pageType: 'product' } }
        );
        expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
      });
    });

    //Authorable content - excluded & includedPageTypes
    describe('excludePageTypes for Authorable content', () => {
      const renderWithExcludedAuthorablePages = (excludedPageTypes, pageType) =>
        render(
          <FeatureFlagsProvider enabledFeatures={{ 'mui-component-include-exclude-from-page': true }}>
            <MarketingComponentWrapper
              exclusions={{
                excludedPageTypes,
              }}
            />
          </FeatureFlagsProvider>,
          { appState: { pageType } }
        );

      it('should not render component if the page is present in  excludedPageTypes array', () => {
        renderWithExcludedAuthorablePages([{ pageType: 'home' }, { pageType: 'category' }], 'home');
        expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
      });

      it('should render component if page not in excludedPageTypes array', () => {
        renderWithExcludedAuthorablePages([{ pageType: 'home' }, { pageType: 'category' }], 'product');
        expect(screen.queryByText(testChildText)).toBeInTheDocument();
      });

      it('should render component if excludedPageTypes array is empty', () => {
        renderWithExcludedAuthorablePages([], 'home');
        expect(screen.queryByText(testChildText)).toBeInTheDocument();
      });

      it('should render component if excludedPageTypes is not an array', () => {
        renderWithExcludedAuthorablePages(null, 'home');
        expect(screen.queryByText(testChildText)).toBeInTheDocument();
      });

      it('should render component if excludedPageTypes contains empty pageType', () => {
        renderWithExcludedAuthorablePages([{ pageType: '' }], 'home');
        expect(screen.queryByText(testChildText)).toBeInTheDocument();
      });
    });

    describe('includedPageTypes for authorable content', () => {
      const renderWithIncludedAuthorablePages = (includedPageTypes, pageType) =>
        render(
          <FeatureFlagsProvider enabledFeatures={{ 'mui-component-include-exclude-from-page': true }}>
            <MarketingComponentWrapper
              inclusions={{
                includedPageTypes,
              }}
            />
          </FeatureFlagsProvider>,
          { appState: { pageType } }
        );

      it('should render component for page if present in includedPageTypes', () => {
        renderWithIncludedAuthorablePages([{ pageType: 'home' }, { pageType: 'category' }], 'home');
        expect(screen.queryByText(testChildText)).toBeInTheDocument();
      });

      it('should not render component if the page is not in includeDPageTypes', () => {
        renderWithIncludedAuthorablePages([{ pageType: 'home' }, { pageType: 'category' }], 'product');
        expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
      });

      it('should render  component if includedPageTypes is empty', () => {
        renderWithIncludedAuthorablePages([], 'home');
        expect(screen.queryByText(testChildText)).toBeInTheDocument();
      });

      it('should render  component if includedPageTypes is not an array', () => {
        renderWithIncludedAuthorablePages(null, 'home');
        expect(screen.queryByText(testChildText)).toBeInTheDocument();
      });

      it('should render component if includedPageTypes contains empty pageType', () => {
        renderWithIncludedAuthorablePages([{ pageType: '' }], 'home');
        expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
      });
    });

    describe('Exclusion vs inclusion - includedPageTypes for authorable content with different pages', () => {
      const renderWithIncludedExcludedAuthorablePages = pageType =>
        render(
          <FeatureFlagsProvider enabledFeatures={{ 'mui-component-include-exclude-from-page': true }}>
            <MarketingComponentWrapper
              exclusions={{ excludedPageTypes: [{ pageType: 'product' }] }}
              inclusions={{
                includedPageTypes: [{ pageType: 'home' }, { pageType: 'category' }],
              }}
            />
          </FeatureFlagsProvider>,
          { appState: { pageType } }
        );

      it('should render component for included authorable pages', () => {
        renderWithIncludedExcludedAuthorablePages('home');
        expect(screen.queryByText(testChildText)).toBeInTheDocument();
      });
      it('should not render component for excluded authorable pages', () => {
        renderWithIncludedExcludedAuthorablePages('product');
        expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
      });
      it('should not render component for excluded authorable pages', () => {
        renderWithIncludedExcludedAuthorablePages('shoppingbag');
        expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
      });
    });
    describe('Exclusion vs inclusion - includedPageTypes for authorable content with Same pages for both exclude and include', () => {
      const renderWithIncludedExcludedAuthorablePages = pageType =>
        render(
          <FeatureFlagsProvider enabledFeatures={{ 'mui-component-include-exclude-from-page': true }}>
            <MarketingComponentWrapper
              exclusions={{ excludedPageTypes: [{ pageType: 'home' }] }}
              inclusions={{
                includedPageTypes: [{ pageType: 'home' }, { pageType: 'category' }],
              }}
            />
          </FeatureFlagsProvider>,
          { appState: { pageType } }
        );

      it('should not render component for excluded authorable pages', () => {
        renderWithIncludedExcludedAuthorablePages('home');
        expect(screen.queryByText(testChildText)).not.toBeInTheDocument();
      });
    });
  });
});
