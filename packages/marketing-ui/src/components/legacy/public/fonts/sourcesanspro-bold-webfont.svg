<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="source_sans_probold" horiz-adv-x="528" >
<font-face units-per-em="1000" ascent="750" descent="-250" />
<missing-glyph horiz-adv-x="200" />
<glyph unicode="&#xfb00;" horiz-adv-x="657" d="M406 0v381h-169v-381h-147v381h-66v109l66 5v23q0 38 10.5 72t33 59t58 39.5t86.5 14.5q32 0 59 -5.5t44 -12.5l-27 -109q-12 5 -25 8t-33 3q-26 0 -42.5 -16.5t-16.5 -51.5v-23h169v26q0 39 9.5 74t31.5 61t57 41t85 15q31 0 56.5 -6t42.5 -12l-27 -108q-28 10 -51 10 q-27 0 -42 -16.5t-15 -54.5v-30h89v-115h-89v-381h-147z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="333" />
<glyph unicode=" "  horiz-adv-x="200" />
<glyph unicode="&#x09;" horiz-adv-x="200" />
<glyph unicode="&#xa0;" horiz-adv-x="200" />
<glyph unicode="!" horiz-adv-x="340" d="M122 231l-21 306l-5 133h148l-5 -133l-21 -306h-96zM170 -12q-38 0 -63.5 26.5t-25.5 65.5q0 40 25.5 66.5t63.5 26.5t63.5 -26.5t25.5 -66.5q0 -39 -25.5 -65.5t-63.5 -26.5z" />
<glyph unicode="&#x22;" horiz-adv-x="536" d="M110 354l-29 199l-5 133h148l-5 -133l-29 -199h-80zM346 354l-29 199l-5 133h148l-5 -133l-29 -199h-80z" />
<glyph unicode="#" d="M86 0l22 187h-74v94h85l14 110h-79v94h91l21 165h84l-20 -165h104l21 165h84l-20 -165h79v-94h-91l-13 -110h84v-94h-95l-23 -187h-85l22 187h-103l-23 -187h-85zM205 281h103l14 110h-104z" />
<glyph unicode="$" d="M215 -110v100q-44 5 -92.5 24.5t-83.5 51.5l64 99q38 -29 72.5 -43.5t70.5 -14.5q42 0 61.5 17t19.5 52q0 26 -19.5 44t-49 34.5t-63.5 33t-63.5 39.5t-49 54t-19.5 76q0 74 40.5 122.5t111.5 62.5v103h97v-101q48 -7 84 -29t65 -53l-74 -84q-27 25 -52 37.5t-57 12.5 q-36 0 -55 -15t-19 -49q0 -24 19.5 -40.5t49 -31.5t63.5 -31t63.5 -39t49 -55.5t19.5 -80.5q0 -72 -39 -123.5t-117 -68.5v-104h-97z" />
<glyph unicode="%" horiz-adv-x="857" d="M188 250q-35 0 -65 14t-51.5 41t-33.5 65.5t-12 87.5t12 87t33.5 64.5t51.5 40.5t65 14t65 -14t51.5 -40.5t34 -64.5t12.5 -87t-12.5 -87.5t-34 -65.5t-51.5 -41t-65 -14zM188 332q25 0 42.5 28t17.5 98t-17.5 97t-42.5 27t-42.5 -27t-17.5 -97t17.5 -98t42.5 -28z M210 -12l352 676h85l-352 -676h-85zM669 -12q-35 0 -65 14t-51.5 41t-33.5 65.5t-12 87.5t12 87t33.5 64.5t51.5 40.5t65 14t65 -14t51.5 -40.5t34 -64.5t12.5 -87t-12.5 -87.5t-34 -65.5t-51.5 -41t-65 -14zM669 70q25 0 42.5 28t17.5 98t-17.5 97t-42.5 27t-42.5 -27 t-17.5 -97t17.5 -98t42.5 -28z" />
<glyph unicode="&#x26;" horiz-adv-x="667" d="M242 -12q-53 0 -93.5 15t-68 40.5t-41.5 59t-14 71.5q0 34 9.5 61.5t26 49.5t38 40t45.5 33q-19 35 -29.5 69.5t-10.5 65.5q0 35 12.5 66t35.5 54.5t55.5 37t73.5 13.5q74 0 117 -40t43 -108q0 -32 -11.5 -58.5t-31 -49t-44 -41.5t-49.5 -36q28 -32 61.5 -62.5 t69.5 -56.5q25 32 44 71t31 85h134q-18 -60 -44.5 -114.5t-64.5 -105.5q31 -16 59.5 -26.5t53.5 -14.5l-35 -119q-41 8 -83 25t-84 41q-42 -31 -92.5 -48.5t-112.5 -17.5zM225 494q0 -18 5.5 -38t15.5 -40q35 21 59 44t24 56q0 23 -11 37t-34 14q-25 0 -42 -19.5t-17 -53.5z M261 100q41 0 82 26q-38 31 -72 65t-63 70q-20 -17 -32 -36t-12 -42q0 -37 26.5 -60t70.5 -23z" />
<glyph unicode="'" horiz-adv-x="300" d="M110 354l-29 199l-5 133h148l-5 -133l-29 -199h-80z" />
<glyph unicode="(" horiz-adv-x="344" d="M204 -179q-63 103 -97.5 214t-34.5 243t34.5 243t97.5 214l92 -38q-55 -98 -80 -205t-25 -214t25 -214t80 -205z" />
<glyph unicode=")" horiz-adv-x="344" d="M140 -179l-92 38q56 98 81 205t25 214t-25 214t-81 205l92 38q63 -103 97.5 -214t34.5 -243t-34.5 -243t-97.5 -214z" />
<glyph unicode="*" horiz-adv-x="457" d="M148 349l-61 44l59 104l-108 49l23 72l116 -24l13 118h77l13 -119l115 25l24 -72l-108 -49l59 -104l-61 -44l-80 89z" />
<glyph unicode="+" d="M210 94v184h-176v104h176v184h108v-184h176v-104h-176v-184h-108z" />
<glyph unicode="," horiz-adv-x="300" d="M76 -194l-30 78q56 20 85 52t28 70h-8q-35 0 -62 21.5t-27 61.5q0 38 27 61t65 23q50 0 76 -36.5t26 -102.5q0 -83 -46 -142.5t-134 -85.5z" />
<glyph unicode="-" horiz-adv-x="332" d="M43 201v104h246v-104h-246z" />
<glyph unicode="." horiz-adv-x="300" d="M150 -12q-38 0 -63.5 26.5t-25.5 65.5q0 40 25.5 66.5t63.5 26.5t63.5 -26.5t25.5 -66.5q0 -39 -25.5 -65.5t-63.5 -26.5z" />
<glyph unicode="/" horiz-adv-x="339" d="M13 -160l201 870h96l-201 -870h-96z" />
<glyph unicode="0" d="M264 -12q-51 0 -93 21.5t-71.5 63.5t-46 104t-16.5 143t16.5 142t46 102.5t71.5 62t93 20.5t93 -20.5t71.5 -62t46 -102.5t16.5 -142t-16.5 -143t-46 -104t-71.5 -63.5t-93 -21.5zM264 102q19 0 35 9.5t28 34.5t18.5 67t6.5 107t-6.5 106.5t-18.5 65t-28 32.5t-35 9 q-18 0 -34 -9t-28 -32.5t-19 -65t-7 -106.5t7 -107t19 -67t28 -34.5t34 -9.5z" />
<glyph unicode="1" d="M70 0v119h138v367h-119v91q52 10 88 24t70 34h108v-516h119v-119h-404z" />
<glyph unicode="2" d="M37 0v84q61 57 111.5 106.5t87 93t57 81t20.5 71.5q0 47 -24 71.5t-66 24.5q-35 0 -62 -19.5t-51 -45.5l-80 79q47 50 96 75.5t117 25.5q47 0 85.5 -14.5t66.5 -41t43 -63.5t15 -83q0 -40 -17 -81.5t-45.5 -83t-65 -83t-75.5 -81.5q24 3 53.5 5.5t52.5 2.5h128v-124h-447 z" />
<glyph unicode="3" d="M243 -12q-78 0 -132 24.5t-89 65.5l68 93q29 -28 63.5 -46t75.5 -18q47 0 75 19.5t28 55.5q0 21 -7.5 38t-26.5 28.5t-52 18t-85 6.5v104q43 0 71.5 6t46 17.5t25 27t7.5 34.5q0 33 -20 51.5t-58 18.5q-34 0 -61.5 -15t-57.5 -41l-74 90q44 38 92.5 59.5t107.5 21.5 q49 0 89.5 -11.5t69 -34t44.5 -54.5t16 -74q0 -50 -27.5 -84.5t-78.5 -56.5v-4q55 -16 90.5 -54.5t35.5 -100.5q0 -44 -19 -78.5t-51 -58t-75 -36t-91 -12.5z" />
<glyph unicode="4" d="M294 0v152h-275v101l232 382h180v-371h73v-112h-73v-152h-137zM160 264h134v103q0 31 2 71.5t4 70.5h-4q-12 -26 -25 -53t-27 -54z" />
<glyph unicode="5" d="M247 -12q-78 0 -132 25.5t-92 63.5l66 93q29 -26 62.5 -44.5t74.5 -18.5q49 0 78 25t29 74q0 48 -27.5 73t-72.5 25q-14 0 -25 -1.5t-21.5 -5.5t-22 -10t-25.5 -15l-66 42l18 321h360v-124h-233l-11 -119q18 8 34.5 11.5t36.5 3.5q41 0 77.5 -12t64.5 -36t44 -61t16 -88 q0 -53 -19 -94t-51 -69.5t-74.5 -43.5t-88.5 -15z" />
<glyph unicode="6" d="M281 -12q-46 0 -89.5 18t-77 56t-53.5 97.5t-20 143.5q0 89 21.5 154t57 107t82 62.5t96.5 20.5q63 0 109 -22.5t76 -53.5l-77 -88q-17 18 -44 31.5t-55 13.5q-26 0 -49 -10t-40.5 -33.5t-28.5 -61.5t-13 -93q12 16 28 28.5t33 21t34 13t32 4.5q41 0 75.5 -12t60 -36 t39.5 -61t14 -87t-17 -89t-45.5 -67t-67 -42.5t-81.5 -14.5zM278 98q33 0 56.5 24.5t23.5 78.5q0 51 -23.5 71.5t-59.5 20.5q-25 0 -50.5 -14.5t-45.5 -48.5q5 -38 15 -63t23 -40.5t28.5 -22t32.5 -6.5z" />
<glyph unicode="7" d="M159 0q4 79 14.5 145.5t30 127t50 118.5t75.5 120h-285v124h442v-90q-55 -67 -88.5 -125.5t-52 -121t-26.5 -134t-12 -164.5h-148z" />
<glyph unicode="8" d="M263 -12q-48 0 -88.5 12.5t-70 35.5t-46 55t-16.5 71q0 58 31.5 95t76.5 61v4q-37 28 -61 63.5t-24 86.5q0 40 15 72.5t42 55.5t64.5 35t81.5 12q43 0 79 -12.5t61 -35t39 -55t14 -71.5q0 -46 -25 -81.5t-58 -57.5v-4q46 -25 78 -64t32 -102q0 -38 -16 -70t-45.5 -55.5 t-71 -37t-92.5 -13.5zM303 370q39 45 39 93q0 37 -20.5 59.5t-56.5 22.5q-29 0 -50 -18t-21 -55q0 -39 30 -61t79 -41zM266 90q37 0 61 19.5t24 59.5q0 21 -9.5 36t-27.5 27.5t-42.5 23.5t-54.5 24q-21 -20 -35 -46t-14 -55q0 -42 29 -65.5t69 -23.5z" />
<glyph unicode="9" d="M252 342q25 0 50.5 14.5t44.5 48.5q-10 75 -37.5 103.5t-61.5 28.5q-32 0 -56 -24.5t-24 -78.5q0 -51 24 -71.5t60 -20.5zM229 -12q-63 0 -109.5 23t-76.5 53l78 88q16 -18 43 -31.5t55 -13.5q27 0 50 10t40.5 33.5t28 61t12.5 93.5q-12 -16 -28 -28.5t-33 -21t-34 -13 t-32 -4.5q-41 0 -75.5 12t-59.5 36t-39.5 61t-14.5 87q0 49 17 88.5t45.5 67.5t67 42.5t81.5 14.5q46 0 89.5 -18t77 -56t54 -97.5t20.5 -143.5q0 -89 -21.5 -154t-57 -107t-82 -62.5t-96.5 -20.5z" />
<glyph unicode=":" horiz-adv-x="300" d="M150 312q-38 0 -63.5 26.5t-25.5 65.5q0 40 25.5 66.5t63.5 26.5t63.5 -26.5t25.5 -66.5q0 -39 -25.5 -65.5t-63.5 -26.5zM150 -12q-38 0 -63.5 26.5t-25.5 65.5q0 40 25.5 66.5t63.5 26.5t63.5 -26.5t25.5 -66.5q0 -39 -25.5 -65.5t-63.5 -26.5z" />
<glyph unicode=";" horiz-adv-x="300" d="M150 312q-38 0 -63.5 26.5t-25.5 65.5q0 40 25.5 66.5t63.5 26.5t63.5 -26.5t25.5 -66.5q0 -39 -25.5 -65.5t-63.5 -26.5zM76 -194l-30 78q56 20 85 52t28 70h-8q-35 0 -62 21.5t-27 61.5q0 38 27 61t65 23q50 0 76 -36.5t26 -102.5q0 -83 -46 -142.5t-134 -85.5z" />
<glyph unicode="&#x3c;" d="M494 104l-460 177v102l460 177v-121l-176 -58l-135 -47v-4l135 -47l176 -58v-121z" />
<glyph unicode="=" d="M34 394v104h460v-104h-460zM34 163v104h460v-104h-460z" />
<glyph unicode="&#x3e;" d="M34 104v121l176 58l135 47v4l-135 47l-176 58v121l460 -177v-102z" />
<glyph unicode="?" horiz-adv-x="463" d="M154 231q-5 34 2.5 62t21.5 52t31.5 44t33.5 38.5t26.5 36t10.5 36.5q0 31 -18.5 47t-48.5 16q-28 0 -49.5 -13t-41.5 -33l-81 74q35 41 83.5 66t107.5 25q40 0 74.5 -10t60 -31t40 -53.5t14.5 -76.5q0 -31 -11 -55.5t-27.5 -45.5t-35 -40.5t-34 -40.5t-24.5 -44.5 t-6 -53.5h-129zM218 -12q-38 0 -63 26.5t-25 65.5q0 40 25 66.5t63 26.5t63.5 -26.5t25.5 -66.5q0 -39 -25.5 -65.5t-63.5 -26.5z" />
<glyph unicode="@" horiz-adv-x="903" d="M428 -172q-75 0 -143.5 23t-121 70t-83.5 117.5t-31 166.5q0 105 36 190.5t97.5 146.5t143.5 94t174 33q81 0 146.5 -26t111.5 -73.5t71 -113t25 -143.5q0 -69 -21 -120.5t-54 -86t-73.5 -51.5t-79.5 -17q-43 0 -75 20t-40 56h-2q-21 -29 -55 -47.5t-65 -18.5 q-60 0 -96 40.5t-36 109.5q0 46 15.5 90t42.5 78.5t64 55.5t80 21q25 0 44 -11.5t32 -38.5h2l14 42h91l-46 -220q-21 -86 46 -86q22 0 43.5 12.5t38.5 36t27 57t10 74.5q0 54 -15 103t-48 86.5t-85 59.5t-125 22q-64 0 -125 -26.5t-109 -75t-77 -117t-29 -152.5 q0 -74 23.5 -129t64 -91t93 -54t111.5 -18q39 0 79.5 11t72.5 28l32 -80q-46 -25 -94 -36.5t-102 -11.5zM420 140q16 0 31.5 10t34.5 35l24 135q-15 29 -46 29q-23 0 -40.5 -14t-30 -35t-19 -46t-6.5 -47q0 -67 52 -67z" />
<glyph unicode="A" horiz-adv-x="573" d="M230 330l-16 -60h139l-15 60q-14 49 -27 104t-26 106h-4q-12 -52 -24.5 -106.5t-26.5 -103.5zM-6 0l204 652h177l204 -652h-156l-40 155h-199l-40 -155h-150z" />
<glyph unicode="B" horiz-adv-x="605" d="M77 0v652h223q51 0 94.5 -7.5t76 -26.5t51 -50t18.5 -78q0 -22 -6 -44t-17 -41t-26.5 -34t-35.5 -22v-4q25 -6 46 -18.5t37 -31.5t25 -44.5t9 -58.5q0 -50 -19.5 -86t-54 -59.5t-80.5 -35t-99 -11.5h-242zM224 391h69q54 0 78.5 21t24.5 56q0 38 -25 54t-77 16h-70v-147z M224 114h83q121 0 121 88q0 43 -30 61.5t-91 18.5h-83v-168z" />
<glyph unicode="C" horiz-adv-x="582" d="M349 -12q-62 0 -117 21.5t-96.5 63.5t-65.5 104.5t-24 144.5q0 81 25 144.5t67 107.5t98 67t118 23q63 0 113 -25.5t83 -59.5l-78 -94q-25 23 -52.5 37.5t-62.5 14.5q-33 0 -62.5 -14.5t-51 -41.5t-34 -66t-12.5 -88q0 -101 43 -156.5t115 -55.5q40 0 71.5 17.5 t55.5 43.5l78 -92q-82 -96 -211 -96z" />
<glyph unicode="D" horiz-adv-x="635" d="M77 0v652h184q75 0 135 -19t103 -58.5t66 -100.5t23 -145t-23 -146t-65 -102.5t-100.5 -60.5t-130.5 -20h-192zM224 119h28q42 0 76 11t58 35.5t37.5 64.5t13.5 99q0 58 -13.5 97.5t-37.5 63t-58 33.5t-76 10h-28v-414z" />
<glyph unicode="E" horiz-adv-x="548" d="M77 0v652h408v-124h-261v-131h222v-123h-222v-150h271v-124h-418z" />
<glyph unicode="F" horiz-adv-x="524" d="M77 0v652h411v-124h-264v-151h226v-124h-226v-253h-147z" />
<glyph unicode="G" horiz-adv-x="638" d="M365 -12q-67 0 -125.5 21.5t-101.5 63.5t-67.5 104.5t-24.5 144.5q0 81 25 144.5t68.5 107.5t101.5 67t123 23q71 0 122 -26t83 -59l-77 -94q-25 22 -53 37t-70 15q-38 0 -69.5 -14.5t-54.5 -41.5t-35.5 -66t-12.5 -88q0 -101 45.5 -156.5t137.5 -55.5q20 0 39 5t31 15 v112h-108v120h238v-300q-34 -33 -90 -56t-125 -23z" />
<glyph unicode="H" horiz-adv-x="674" d="M77 0v652h147v-251h225v251h148v-652h-148v272h-225v-272h-147z" />
<glyph unicode="I" horiz-adv-x="301" d="M77 0v652h147v-652h-147z" />
<glyph unicode="J" horiz-adv-x="509" d="M226 -12q-75 0 -125.5 29t-84.5 89l100 74q18 -33 42 -49t49 -16q40 0 60.5 24.5t20.5 89.5v423h147v-435q0 -47 -12.5 -89t-38 -73t-65 -49t-93.5 -18z" />
<glyph unicode="K" horiz-adv-x="614" d="M77 0v652h147v-267h4l194 267h162l-197 -260l233 -392h-161l-159 275l-76 -100v-175h-147z" />
<glyph unicode="L" horiz-adv-x="518" d="M77 0v652h147v-528h258v-124h-405z" />
<glyph unicode="M" horiz-adv-x="762" d="M77 0v652h161l105 -290q10 -29 19 -59.5t19 -60.5h4q10 30 18.5 60.5t18.5 59.5l103 290h160v-652h-134v239q0 24 2 53t5 58.5t6 58t6 52.5h-4l-53 -155l-93 -249h-81l-93 249l-52 155h-4q3 -24 6 -52.5t6 -58t5 -58.5t2 -53v-239h-132z" />
<glyph unicode="N" horiz-adv-x="665" d="M77 0v652h151l170 -324l64 -144h4q-5 52 -11.5 116t-6.5 122v230h140v-652h-151l-170 325l-64 142h-4q5 -54 11.5 -116t6.5 -120v-231h-140z" />
<glyph unicode="O" horiz-adv-x="684" d="M342 -12q-66 0 -120.5 23.5t-93.5 67.5t-60.5 107t-21.5 143t21.5 142.5t60.5 105t93.5 65t120.5 22.5t120.5 -22.5t93.5 -65.5t60.5 -105.5t21.5 -141.5q0 -80 -21.5 -143t-60.5 -107t-93.5 -67.5t-120.5 -23.5zM342 115q67 0 106 57.5t39 156.5q0 98 -39 153t-106 55 t-106 -55t-39 -153q0 -99 39 -156.5t106 -57.5z" />
<glyph unicode="P" horiz-adv-x="596" d="M77 0v652h224q54 0 101 -10.5t82 -35t55.5 -65t20.5 -99.5q0 -57 -20.5 -99t-55.5 -69t-81 -40t-98 -13h-81v-221h-147zM224 338h72q120 0 120 104q0 51 -32 72t-92 21h-68v-197z" />
<glyph unicode="Q" horiz-adv-x="684" d="M342 108q67 0 106 58t39 163q0 98 -39 153t-106 55t-106 -55t-39 -153q0 -105 39 -163t106 -58zM557 -182q-52 0 -96 13.5t-79.5 37t-62.5 56t-44 70.5q-52 11 -94.5 38.5t-72.5 70.5t-46 99.5t-16 125.5q0 80 21.5 142.5t60.5 105t93.5 65t120.5 22.5t120.5 -22.5 t93.5 -65.5t60.5 -105.5t21.5 -141.5q0 -132 -56 -217t-151 -112q23 -35 62 -49.5t81 -14.5q21 0 39.5 3t32.5 8l26 -107q-18 -9 -47.5 -15.5t-67.5 -6.5z" />
<glyph unicode="R" horiz-adv-x="613" d="M77 0v652h235q52 0 97.5 -10t79.5 -33.5t54 -62.5t20 -97q0 -72 -31.5 -118.5t-84.5 -69.5l148 -261h-165l-125 235h-81v-235h-147zM224 352h76q58 0 88.5 24.5t30.5 72.5t-30.5 67t-88.5 19h-76v-183z" />
<glyph unicode="S" horiz-adv-x="556" d="M276 -12q-64 0 -127.5 23.5t-113.5 70.5l84 101q35 -30 77.5 -49t83.5 -19q46 0 68.5 17t22.5 46q0 15 -6.5 26t-19 19.5t-30 16l-38.5 16.5l-85 36q-25 10 -48.5 26t-42 37.5t-29.5 50t-11 64.5q0 40 17 75.5t48 62t73.5 41.5t93.5 15q57 0 112.5 -22t97.5 -64l-75 -93 q-32 25 -64 38.5t-71 13.5q-38 0 -60.5 -15.5t-22.5 -43.5q0 -15 7.5 -25.5t21 -19t31.5 -16t39 -16.5l84 -34q60 -24 94.5 -66t34.5 -111q0 -41 -16.5 -77.5t-48 -64t-77.5 -44t-104 -16.5z" />
<glyph unicode="T" horiz-adv-x="556" d="M204 0v528h-179v124h506v-124h-179v-528h-148z" />
<glyph unicode="U" horiz-adv-x="665" d="M334 -12q-127 0 -194 75t-67 239v350h148v-365q0 -96 29 -134t84 -38t85 38t30 134v365h142v-350q0 -164 -65.5 -239t-191.5 -75z" />
<glyph unicode="V" horiz-adv-x="556" d="M190 0l-197 652h156l78 -304q15 -54 26.5 -104.5t26.5 -104.5h4q14 54 26 104.5t26 104.5l77 304h150l-196 -652h-177z" />
<glyph unicode="W" horiz-adv-x="813" d="M135 0l-121 652h151l45 -305q6 -51 13 -102.5t13 -103.5h4q10 52 19.5 103.5t19.5 102.5l70 305h125l70 -305q10 -50 19.5 -101.5t19.5 -104.5h4q7 53 13.5 104t13.5 102l45 305h141l-116 -652h-185l-64 296q-8 38 -14.5 76.5t-11.5 74.5h-4q-6 -36 -12.5 -74.5 t-14.5 -76.5l-61 -296h-182z" />
<glyph unicode="X" horiz-adv-x="567" d="M11 0l181 335l-170 317h164l57 -123q11 -23 22 -48.5t25 -56.5h4q11 31 21.5 56.5t20.5 48.5l52 123h157l-169 -324l180 -328h-164l-65 133q-12 26 -23.5 51.5t-25.5 55.5h-4q-11 -30 -22 -55.5t-22 -51.5l-61 -133h-158z" />
<glyph unicode="Y" horiz-adv-x="525" d="M189 0v232l-197 420h158l58 -150q14 -37 26.5 -71.5t26.5 -72.5h4q14 38 27.5 72.5t27.5 71.5l59 150h154l-197 -420v-232h-147z" />
<glyph unicode="Z" horiz-adv-x="541" d="M36 0v89l288 439h-261v124h442v-89l-288 -439h291v-124h-472z" />
<glyph unicode="[" horiz-adv-x="344" d="M87 -152v860h211v-78h-101v-704h101v-78h-211z" />
<glyph unicode="\" horiz-adv-x="339" d="M230 -160l-202 870h96l202 -870h-96z" />
<glyph unicode="]" horiz-adv-x="344" d="M46 -152v78h102v704h-102v78h211v-860h-211z" />
<glyph unicode="^" d="M50 274l155 396h118l155 -396h-121l-46 128l-45 135h-4l-45 -135l-46 -128h-121z" />
<glyph unicode="_" horiz-adv-x="500" d="M12 -140v83h476v-83h-476z" />
<glyph unicode="`" horiz-adv-x="555" d="M268 572l-181 157l99 97l154 -182z" />
<glyph unicode="a" horiz-adv-x="527" d="M190 -12q-34 0 -61.5 11.5t-46.5 32t-29.5 47.5t-10.5 59q0 78 66 122t213 59q-2 33 -19.5 52.5t-58.5 19.5q-32 0 -63.5 -12t-67.5 -33l-53 97q48 29 100.5 47t111.5 18q96 0 146.5 -54.5t50.5 -169.5v-284h-120l-10 51h-4q-32 -28 -67 -45.5t-77 -17.5zM240 102 q25 0 43 11t38 31v87q-78 -11 -108 -32t-30 -50q0 -24 15.5 -35.5t41.5 -11.5z" />
<glyph unicode="b" horiz-adv-x="573" d="M325 -12q-33 0 -66.5 16.5t-62.5 48.5h-4l-12 -53h-115v701h147v-172l-4 -76q29 26 62.5 40.5t68.5 14.5q45 0 81 -18t61.5 -50.5t39 -79t13.5 -103.5q0 -64 -17.5 -114t-47 -84.5t-67 -52.5t-77.5 -18zM291 108q38 0 65 34.5t27 111.5q0 134 -86 134q-44 0 -85 -45v-203 q20 -18 40 -25t39 -7z" />
<glyph unicode="c" horiz-adv-x="467" d="M281 -12q-52 0 -96.5 17.5t-77.5 50.5t-52 81.5t-19 110.5t21 110.5t56.5 81.5t82.5 50.5t98 17.5q46 0 81.5 -15t63.5 -38l-69 -95q-35 29 -67 29q-54 0 -85 -38t-31 -103t31.5 -103t79.5 -38q24 0 46.5 10.5t42.5 25.5l58 -96q-37 -32 -80 -45.5t-84 -13.5z" />
<glyph unicode="d" horiz-adv-x="573" d="M242 -12q-46 0 -83.5 18t-64 51.5t-41 81.5t-14.5 109t17.5 109t46 81.5t66 51.5t76.5 18q42 0 69 -14t53 -38l-6 76v169h147v-701h-120l-10 49h-4q-26 -26 -61 -43.5t-71 -17.5zM280 108q24 0 43.5 10t37.5 35v203q-20 18 -41 25t-41 7q-35 0 -62 -33.5t-27 -104.5 q0 -74 23.5 -108t66.5 -34z" />
<glyph unicode="e" horiz-adv-x="518" d="M287 -12q-53 0 -99 17.5t-80 51t-53 81.5t-19 110q0 60 20 108.5t52.5 82t74.5 51.5t87 18q53 0 92.5 -18t66 -50t39.5 -75.5t13 -94.5q0 -20 -2 -38t-4 -27h-297q10 -54 44.5 -79.5t84.5 -25.5q53 0 107 33l49 -89q-38 -26 -84.5 -41t-91.5 -15zM177 302h179 q0 42 -19 68t-64 26q-35 0 -61.5 -23t-34.5 -71z" />
<glyph unicode="f" horiz-adv-x="341" d="M90 0v381h-66v109l66 5v27q0 39 9.5 74t31.5 61t57 41t85 15q31 0 56.5 -6t42.5 -12l-27 -108q-28 10 -51 10q-27 0 -42 -16.5t-15 -54.5v-30h89v-115h-89v-381h-147z" />
<glyph unicode="g" horiz-adv-x="534" d="M242 -211q-44 0 -81.5 7.5t-66 22.5t-44.5 39t-16 57q0 60 71 100v4q-20 13 -33 33t-13 51q0 27 16 51.5t40 41.5v4q-26 18 -46.5 51.5t-20.5 77.5q0 45 17 78.5t45.5 56t65.5 33.5t77 11q44 0 77 -12h181v-107h-79q7 -11 11.5 -28t4.5 -37q0 -43 -15 -74.5t-41.5 -52 t-62 -30.5t-76.5 -10q-29 0 -60 10q-10 -8 -14 -16t-4 -21q0 -19 16.5 -28t58.5 -9h80q92 0 140.5 -29.5t48.5 -96.5q0 -39 -19.5 -71.5t-55.5 -56t-87 -37t-115 -13.5zM253 246q29 0 48.5 20.5t19.5 62.5q0 39 -19.5 59.5t-48.5 20.5t-48.5 -20t-19.5 -60q0 -42 19.5 -62.5 t48.5 -20.5zM265 -119q50 0 82 17.5t32 43.5q0 23 -19.5 31t-56.5 8h-51q-26 0 -42 1.5t-28 4.5q-27 -24 -27 -49q0 -28 30 -42.5t80 -14.5z" />
<glyph unicode="h" horiz-adv-x="571" d="M65 0v701h147v-172l-7 -89q28 25 65 46.5t88 21.5q81 0 117.5 -53t36.5 -147v-308h-147v289q0 54 -14.5 74t-46.5 20q-28 0 -48 -12.5t-44 -35.5v-335h-147z" />
<glyph unicode="i" horiz-adv-x="276" d="M65 0v496h147v-496h-147zM138 569q-37 0 -61 21.5t-24 55.5t24 55t61 21q38 0 61.5 -21t23.5 -55t-23.5 -55.5t-61.5 -21.5z" />
<glyph unicode="j" horiz-adv-x="278" d="M36 -196q-31 0 -51.5 4t-35.5 10l26 108q10 -3 19 -5t20 -2q29 0 40.5 19t11.5 61v497h147v-493q0 -41 -8.5 -77t-29 -63t-54.5 -43t-85 -16zM140 569q-37 0 -61 21.5t-24 55.5t24 55t61 21t61 -21t24 -55t-24 -55.5t-61 -21.5z" />
<glyph unicode="k" horiz-adv-x="548" d="M65 0v701h143v-402h4l157 197h160l-174 -204l187 -292h-159l-112 193l-63 -71v-122h-143z" />
<glyph unicode="l" horiz-adv-x="286" d="M200 -12q-38 0 -64 12t-41.5 33.5t-22.5 52t-7 68.5v547h147v-553q0 -23 8.5 -32t17.5 -9h8.5t9.5 2l18 -109q-12 -5 -30.5 -8.5t-43.5 -3.5z" />
<glyph unicode="m" horiz-adv-x="857" d="M65 0v496h120l10 -64h4q31 31 66 53.5t84 22.5q53 0 85.5 -21.5t51.5 -61.5q33 34 69.5 58.5t86.5 24.5q80 0 117.5 -53.5t37.5 -146.5v-308h-147v289q0 54 -14.5 74t-46.5 20q-37 0 -85 -48v-335h-147v289q0 54 -14.5 74t-46.5 20q-38 0 -84 -48v-335h-147z" />
<glyph unicode="n" horiz-adv-x="572" d="M65 0v496h120l10 -63h4q32 30 70 52.5t89 22.5q81 0 117.5 -53t36.5 -147v-308h-147v289q0 54 -14.5 74t-46.5 20q-28 0 -48 -12.5t-44 -35.5v-335h-147z" />
<glyph unicode="o" horiz-adv-x="555" d="M278 -12q-47 0 -91 17.5t-77.5 50.5t-53.5 81.5t-20 110.5t20 110.5t53.5 81.5t77.5 50.5t91 17.5t90.5 -17.5t77 -50.5t53.5 -81.5t20 -110.5t-20 -110.5t-53.5 -81.5t-77 -50.5t-90.5 -17.5zM278 107q45 0 68 38t23 103t-23 103t-68 38q-46 0 -68.5 -38t-22.5 -103 t22.5 -103t68.5 -38z" />
<glyph unicode="p" horiz-adv-x="573" d="M65 -184v680h120l10 -49h4q29 26 65.5 43.5t75.5 17.5q45 0 81 -18t61 -51t38.5 -79.5t13.5 -103.5q0 -64 -17.5 -113.5t-47 -84t-67 -52.5t-77.5 -18q-32 0 -62 13.5t-56 38.5l5 -79v-145h-147zM291 108q38 0 65 34.5t27 111.5q0 134 -86 134q-43 0 -85 -45v-203 q20 -18 40 -25t39 -7z" />
<glyph unicode="q" horiz-adv-x="573" d="M361 -184v151l6 76q-25 -24 -58 -39.5t-67 -15.5q-46 0 -83.5 18t-64 51.5t-41 81.5t-14.5 109t17.5 109t46 81.5t66 51.5t76.5 18q41 0 72 -14.5t60 -46.5h4l12 49h115v-680h-147zM280 108q24 0 43.5 10t37.5 35v203q-20 18 -41 25t-41 7q-35 0 -62 -33.5t-27 -104.5 q0 -74 23.5 -108t66.5 -34z" />
<glyph unicode="r" horiz-adv-x="398" d="M65 0v496h120l10 -87h4q27 51 65 75t76 24q21 0 34.5 -2.5t24.5 -7.5l-24 -127q-14 4 -26 6t-28 2q-28 0 -58.5 -20t-50.5 -71v-288h-147z" />
<glyph unicode="s" horiz-adv-x="443" d="M211 -12q-49 0 -100.5 19t-89.5 50l66 92q34 -26 65 -39t63 -13q33 0 48 11t15 31q0 12 -8.5 21.5t-23 17.5t-32.5 14.5t-37 14.5q-23 9 -46 21t-42 29.5t-31 41t-12 54.5q0 34 13.5 63t38.5 49t60 31.5t78 11.5q57 0 100 -19.5t75 -43.5l-66 -88q-27 20 -53 31t-52 11 q-56 0 -56 -39q0 -12 8 -20.5t21.5 -15.5t31 -13.5t36.5 -13.5q24 -9 47.5 -20.5t43 -28.5t31.5 -41.5t12 -58.5t-13 -63t-39 -50.5t-64 -34t-88 -12.5z" />
<glyph unicode="t" horiz-adv-x="383" d="M256 -12q-46 0 -78.5 14t-53 39t-30 60.5t-9.5 78.5v201h-68v109l76 6l17 132h122v-132h119v-115h-119v-199q0 -42 17.5 -60.5t46.5 -18.5q12 0 24.5 3t22.5 7l23 -107q-20 -6 -47 -12t-63 -6z" />
<glyph unicode="u" horiz-adv-x="568" d="M214 -12q-81 0 -117.5 53t-36.5 147v308h147v-289q0 -54 15 -74t47 -20q28 0 47 13t41 42v328h147v-496h-120l-11 69h-3q-32 -38 -68.5 -59.5t-87.5 -21.5z" />
<glyph unicode="v" horiz-adv-x="523" d="M179 0l-167 496h148l64 -234q10 -37 19.5 -75.5t19.5 -77.5h4q9 39 18.5 77.5t19.5 75.5l65 234h141l-162 -496h-170z" />
<glyph unicode="w" horiz-adv-x="776" d="M148 0l-124 496h146l48 -230q6 -37 11 -73t11 -74h4q7 38 13.5 75t15.5 72l55 230h127l56 -230q9 -37 16 -73t15 -74h4q7 38 11.5 74t11.5 73l47 230h136l-119 -496h-172l-45 198q-8 35 -14 70t-13 75h-4q-7 -40 -12.5 -75t-12.5 -70l-44 -198h-168z" />
<glyph unicode="x" horiz-adv-x="514" d="M14 0l152 258l-143 238h158l44 -80q10 -21 20.5 -42.5t21.5 -42.5h4q8 21 17 42.5t17 42.5l34 80h152l-144 -255l153 -241h-158l-48 82q-12 21 -23.5 43t-23.5 43h-4q-9 -21 -19 -42.5t-19 -43.5l-39 -82h-152z" />
<glyph unicode="y" horiz-adv-x="521" d="M120 -194q-23 0 -39 2.5t-31 7.5l26 112q7 -2 16 -4t17 -2q37 0 57 18t30 47l7 26l-191 483h148l71 -213q11 -34 20 -69t19 -72h4q8 35 16.5 70.5t17.5 70.5l60 213h141l-172 -501q-18 -47 -37.5 -82.5t-45 -59t-58 -35.5t-76.5 -12z" />
<glyph unicode="z" horiz-adv-x="460" d="M38 0v79l208 302h-185v115h368v-78l-208 -303h215v-115h-398z" />
<glyph unicode="{" horiz-adv-x="344" d="M235 -152q-66 0 -97 30.5t-31 111.5q0 27 1.5 48t3.5 40.5t3.5 39t1.5 43.5q0 13 -4.5 26t-14.5 23.5t-26.5 17t-40.5 7.5v86q24 1 40.5 7.5t26.5 17t14.5 23.5t4.5 26q0 24 -1.5 43.5t-3.5 39t-3.5 40.5t-1.5 48q0 81 31 111.5t97 30.5h63v-78h-19q-31 0 -42 -14.5 t-11 -55.5t2 -79t2 -84q0 -57 -18 -82.5t-56 -34.5v-4q38 -9 56 -34.5t18 -82.5q0 -46 -2 -84t-2 -79t11 -55.5t42 -14.5h19v-78h-63z" />
<glyph unicode="|" horiz-adv-x="268" d="M86 -250v1000h96v-1000h-96z" />
<glyph unicode="}" horiz-adv-x="344" d="M46 -152v78h19q31 0 42 14.5t11 55.5t-2 79t-2 84q0 57 18 82.5t56 34.5v4q-38 9 -56 34.5t-18 82.5q0 46 2 84t2 79t-11 55.5t-42 14.5h-19v78h63q66 0 97 -30.5t31 -111.5q0 -27 -1.5 -48t-3.5 -40.5t-3.5 -39t-1.5 -43.5q0 -13 4.5 -26t14.5 -23.5t26.5 -17t40.5 -7.5 v-86q-24 -1 -40.5 -7.5t-26.5 -17t-14.5 -23.5t-4.5 -26q0 -24 1.5 -43.5t3.5 -39t3.5 -40.5t1.5 -48q0 -81 -31 -111.5t-97 -30.5h-63z" />
<glyph unicode="~" d="M349 240q-31 0 -54 12t-42.5 26t-37 26t-38.5 12q-20 0 -38 -15t-34 -43l-76 59q37 57 74.5 80t75.5 23q31 0 54 -12t42.5 -26t37 -26t38.5 -12t38.5 15t33.5 43l76 -59q-37 -57 -74.5 -80t-75.5 -23z" />
<glyph unicode="&#xa1;" horiz-adv-x="340" d="M96 -174l5 133l21 306h96l21 -306l5 -133h-148zM170 323q-38 0 -63.5 26.5t-25.5 66.5q0 39 25.5 65.5t63.5 26.5t63.5 -26.5t25.5 -65.5q0 -40 -25.5 -66.5t-63.5 -26.5z" />
<glyph unicode="&#xa2;" d="M203 307q0 -45 18.5 -77.5t53.5 -47.5v250q-36 -16 -54 -48t-18 -77zM275 -41v95q-49 6 -88.5 26t-67.5 52.5t-43.5 76.5t-15.5 98q0 53 16 96t45 74.5t68 52t86 28.5v97h81v-93q39 -4 70.5 -18.5t53.5 -37.5l-67 -90q-30 24 -57 27v-272q21 4 39 13.5t33 21.5l59 -93 q-29 -25 -63 -39.5t-68 -19.5v-95h-81z" />
<glyph unicode="&#xa3;" d="M48 0v91q45 20 75.5 59.5t30.5 93.5q0 7 -0.5 13.5t-1.5 13.5h-100v86l68 5h6q-7 20 -11.5 40t-4.5 40q0 47 16 85t44.5 64.5t67.5 41t86 14.5q56 0 97.5 -21t75.5 -60l-80 -80q-17 21 -36 31.5t-43 10.5q-40 0 -63.5 -23t-23.5 -71q0 -18 3 -36t8 -36h148v-91h-129 q1 -7 1.5 -13.5t0.5 -14.5q0 -36 -10.5 -62t-34.5 -53v-4h259v-124h-449z" />
<glyph unicode="&#xa4;" d="M93 83l-73 74l61 62q-34 46 -34 111q0 32 8.5 59.5t24.5 49.5l-60 62l73 74l69 -70q48 25 102 25q52 0 102 -25l69 70l73 -74l-61 -62q34 -45 34 -109q0 -33 -9 -60.5t-25 -50.5l61 -62l-73 -74l-70 71q-23 -12 -49 -18t-52 -6q-55 0 -101 24zM264 232q36 0 61 26.5 t25 71.5t-25 71.5t-61 26.5t-61 -26.5t-25 -71.5t25 -71.5t61 -26.5z" />
<glyph unicode="&#xa5;" d="M190 0v147h-153v70h153v55h-153v70h122l-147 293h151l51 -133l24 -66t26 -65h4q13 33 25 66t25 65l50 133h148l-147 -293h122v-70h-154v-55h154v-70h-154v-147h-147z" />
<glyph unicode="&#xa6;" horiz-adv-x="268" d="M86 312v438h96v-438h-96zM86 -250v454h96v-454h-96z" />
<glyph unicode="&#xa7;" d="M161 337q0 -24 16 -40.5t41 -30t55.5 -26t59.5 -27.5q34 21 34 59q0 24 -16 40.5t-40.5 30t-55 25.5t-59.5 27q-35 -23 -35 -58zM240 -84q-55 0 -107.5 20.5t-86.5 63.5l85 75q49 -51 109 -51q31 0 45.5 12t14.5 31t-19.5 33t-48.5 27t-63.5 28t-63.5 37.5t-48.5 54.5 t-19.5 79q0 38 21.5 71.5t58.5 56.5q-14 16 -21.5 36.5t-7.5 46.5q0 68 46.5 110.5t131.5 42.5q58 0 103.5 -20.5t76.5 -44.5l-67 -92q-23 20 -51 34t-54 14q-54 0 -54 -39q0 -18 20 -31.5t50 -27t65.5 -29.5t65.5 -38.5t50 -54.5t20 -77q0 -44 -21.5 -76.5t-60.5 -56.5 q11 -16 17 -35t6 -42q0 -34 -12.5 -63t-36.5 -50t-60 -33t-83 -12z" />
<glyph unicode="&#xa8;" horiz-adv-x="555" d="M169 569q-32 0 -52 20.5t-20 51.5q0 30 20 50.5t52 20.5t51.5 -20.5t19.5 -50.5q0 -31 -19.5 -51.5t-51.5 -20.5zM387 569q-32 0 -51.5 20.5t-19.5 51.5q0 30 19.5 50.5t51.5 20.5t52 -20.5t20 -50.5q0 -31 -20 -51.5t-52 -20.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="750" d="M375 -9q-66 0 -126 23t-105.5 66.5t-72 105t-26.5 139.5t26.5 139t72 103.5t105.5 65t126 22.5t126 -22.5t105 -65t71.5 -104t26.5 -138.5q0 -78 -26.5 -139.5t-71.5 -105t-105 -66.5t-126 -23zM375 52q55 0 103 19.5t83 55.5t55.5 86t20.5 112q0 61 -20.5 111 t-55.5 85.5t-83 54.5t-103 19t-103 -19t-83 -54.5t-55.5 -85.5t-20.5 -111q0 -62 20.5 -112t55.5 -86t83 -55.5t103 -19.5zM387 127q-42 0 -77.5 14t-61 39.5t-40 62t-14.5 81.5q0 44 16 79.5t42.5 60.5t61.5 38.5t73 13.5q46 0 77 -17.5t55 -41.5l-55 -61q-16 17 -32 25.5 t-36 8.5q-48 0 -72.5 -30.5t-24.5 -75.5q0 -51 25 -81t67 -30q25 0 43 9.5t37 24.5l48 -68q-29 -23 -61 -37.5t-71 -14.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="359" d="M130 367q-47 0 -74 29.5t-27 70.5q0 53 44 82t143 38q-2 21 -13.5 32.5t-35.5 11.5q-20 0 -43 -8.5t-47 -21.5l-37 68q33 20 69.5 32t75.5 12q64 0 99.5 -37t35.5 -112v-189h-83l-10 34h-4q-18 -18 -41 -30t-52 -12zM165 446q15 0 26.5 7t24.5 19v55q-50 -5 -69 -19.5 t-19 -31.5q0 -30 37 -30z" />
<glyph unicode="&#xab;" horiz-adv-x="480" d="M183 56l-134 144v104l134 144l55 -44l-111 -152l111 -152zM371 56l-134 144v104l134 144l55 -44l-111 -152l111 -152z" />
<glyph unicode="&#xac;" d="M386 94v184h-352v104h460v-288h-108z" />
<glyph unicode="&#xad;" horiz-adv-x="332" d="M43 201v104h246v-104h-246z" />
<glyph unicode="&#xae;" horiz-adv-x="462" d="M231 311q-41 0 -77.5 15t-63.5 42t-42.5 64t-15.5 81t15.5 81t42.5 64t63.5 42t77.5 15t77 -15t63 -42t42.5 -64t15.5 -81t-15.5 -81t-42.5 -64t-63 -42t-77 -15zM231 360q63 0 103 41.5t40 111.5t-40 111.5t-103 41.5q-32 0 -58 -11t-45.5 -31t-30 -48t-10.5 -63 t10.5 -63t30 -48t45.5 -31t58 -11zM150 416v197h89q35 0 58 -16t23 -50q0 -37 -35 -54l41 -77h-60l-30 60h-29v-60h-57zM207 516h21q15 0 22.5 8.5t7.5 19.5t-7.5 18.5t-22.5 7.5h-21v-54z" />
<glyph unicode="&#xaf;" horiz-adv-x="555" d="M130 592v93h296v-93h-296z" />
<glyph unicode="&#xb0;" horiz-adv-x="365" d="M183 398q-30 0 -56.5 10.5t-46 30t-30.5 45.5t-11 58q0 31 11 57.5t30.5 46t46 30t56.5 10.5t56.5 -10.5t46 -30t31 -46t11.5 -57.5q0 -32 -11.5 -58t-31 -45.5t-46 -30t-56.5 -10.5zM183 468q30 0 48.5 20.5t18.5 53.5q0 32 -18.5 52.5t-48.5 20.5q-29 0 -48 -20.5 t-19 -52.5q0 -33 19 -53.5t48 -20.5z" />
<glyph unicode="&#xb1;" d="M210 158v136h-176v104h176v168h108v-168h176v-104h-176v-136h-108zM34 0v104h460v-104h-460z" />
<glyph unicode="&#xb2;" horiz-adv-x="376" d="M45 375v58q75 62 123 109.5t48 84.5q0 29 -15 45t-42 16q-20 0 -37 -12.5t-34 -34.5l-61 56q60 80 149 80q65 0 104.5 -34.5t39.5 -97.5q0 -24 -8.5 -46.5t-23 -44.5t-33 -44t-39.5 -45h118v-90h-289z" />
<glyph unicode="&#xb3;" horiz-adv-x="376" d="M186 363q-51 0 -92 20.5t-67 58.5l65 51q35 -48 83 -48q23 0 39.5 12t16.5 35q0 51 -93 51v62q36 0 57.5 11.5t21.5 36.5q0 20 -13 31t-36 11q-19 0 -34.5 -11t-29.5 -28l-61 54q32 35 65 51t79 16q27 0 51.5 -7.5t43.5 -21.5t30 -34.5t11 -45.5q0 -31 -15 -52.5 t-42 -37.5q30 -12 50.5 -35.5t20.5 -58.5q0 -27 -12.5 -49.5t-33 -38t-48 -24.5t-57.5 -9z" />
<glyph unicode="&#xb4;" horiz-adv-x="555" d="M288 572l-72 72l154 182l99 -97z" />
<glyph unicode="&#xb5;" horiz-adv-x="594" d="M65 -177v673h147v-289q0 -53 14.5 -73.5t46.5 -20.5q25 0 45 14.5t40 50.5v318h148q-1 -44 -3 -91.5t-3.5 -94t-2.5 -88.5t-1 -73q0 -24 12 -33t32 -9q15 0 32 6l17 -109q-16 -7 -35.5 -11.5t-51.5 -4.5q-104 0 -128 83h-4q-37 -78 -105 -78q-18 0 -34 4.5t-28 18.5 q0 -28 1 -52t2 -46.5t2.5 -45.5t4.5 -49h-148z" />
<glyph unicode="&#xb6;" horiz-adv-x="636" d="M393 -80v732h147v-732h-147zM297 197q-53 0 -100 14.5t-82 44t-55.5 73t-20.5 100.5q0 63 20 105.5t54.5 69t81 37.5t98.5 11h44v-455h-40z" />
<glyph unicode="&#xb7;" horiz-adv-x="300" d="M150 228q-38 0 -63.5 26.5t-25.5 65.5q0 40 25.5 66.5t63.5 26.5t63.5 -26.5t25.5 -66.5q0 -39 -25.5 -65.5t-63.5 -26.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="555" d="M177 -239l-11 59q59 3 81 13t22 31q0 15 -15.5 25.5t-61.5 16.5l48 98h86l-28 -65q38 -9 57 -26t19 -49q0 -51 -51.5 -75.5t-145.5 -27.5z" />
<glyph unicode="&#xb9;" horiz-adv-x="376" d="M165 375v281h-89v66q20 3 35.5 7t28.5 9t24.5 11.5t23.5 15.5h86v-390h-109z" />
<glyph unicode="&#xba;" horiz-adv-x="373" d="M186 367q-32 0 -61 11.5t-51.5 34t-36 54t-13.5 72.5t13.5 73t36 54.5t51.5 34.5t61 12t61 -12t51.5 -34.5t36 -54.5t13.5 -73t-13.5 -72.5t-36 -54t-51.5 -34t-61 -11.5zM186 449q29 0 43 24.5t14 65.5q0 42 -14 67t-43 25t-42.5 -25t-13.5 -67q0 -41 13.5 -65.5 t42.5 -24.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="480" d="M109 56l-55 44l111 152l-111 152l55 44l134 -144v-104zM297 56l-55 44l111 152l-111 152l55 44l134 -144v-104z" />
<glyph unicode="&#xbc;" horiz-adv-x="809" d="M136 262v281h-89v66q20 3 35.5 7t28.5 9t24.5 11.5t23.5 15.5h86v-390h-109zM185 -12l352 676h85l-352 -676h-85zM647 0v87h-179v54l141 249h134v-233h58v-70h-58v-87h-96zM567 157h80v41l6 117h-4l-40 -77z" />
<glyph unicode="&#xbd;" horiz-adv-x="846" d="M136 262v281h-89v66q20 3 35.5 7t28.5 9t24.5 11.5t23.5 15.5h86v-390h-109zM170 -12l352 676h85l-352 -676h-85zM514 0v58q75 62 123 109.5t48 84.5q0 29 -15 45t-42 16q-20 0 -37 -12.5t-34 -34.5l-61 56q60 80 149 80q65 0 104.5 -34.5t39.5 -97.5q0 -24 -8.5 -46.5 t-23 -44.5t-33 -44t-39.5 -45h118v-90h-289z" />
<glyph unicode="&#xbe;" horiz-adv-x="818" d="M184 250q-51 0 -92 20.5t-67 58.5l65 51q35 -48 83 -48q23 0 39.5 12t16.5 35q0 51 -93 51v62q36 0 57.5 11.5t21.5 36.5q0 20 -13 31t-36 11q-19 0 -34.5 -11t-29.5 -28l-61 54q32 35 65 51t79 16q27 0 51.5 -7.5t43.5 -21.5t30 -34.5t11 -45.5q0 -31 -15 -52.5 t-42 -37.5q30 -12 50.5 -35.5t20.5 -58.5q0 -27 -12.5 -49.5t-33 -38t-48 -24.5t-57.5 -9zM207 -12l352 676h85l-352 -676h-85zM655 0v87h-179v54l141 249h134v-233h58v-70h-58v-87h-96zM575 157h80v41l6 117h-4l-40 -77z" />
<glyph unicode="&#xbf;" horiz-adv-x="463" d="M232 -186q-41 0 -75.5 10t-60 31t-40 53.5t-14.5 77.5q0 31 11 55.5t27.5 45.5t35 40.5t34 40t25 44t6.5 53.5h128q5 -34 -2.5 -62t-21.5 -52t-31.5 -44t-33.5 -38.5t-26.5 -36t-10.5 -36.5q0 -31 19 -47t48 -16q28 0 49.5 13t41.5 33l81 -74q-36 -41 -83.5 -66 t-106.5 -25zM245 323q-38 0 -63.5 26.5t-25.5 66.5q0 39 25.5 65.5t63.5 26.5t63.5 -26.5t25.5 -65.5q0 -40 -25.5 -66.5t-63.5 -26.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="573" d="M230 330l-16 -60h139l-15 60q-14 49 -27 104t-26 106h-4q-12 -52 -24.5 -106.5t-26.5 -103.5zM-6 0l204 652h177l204 -652h-156l-40 155h-199l-40 -155h-150zM293 680l-183 119l78 95l171 -140z" />
<glyph unicode="&#xc1;" horiz-adv-x="573" d="M230 330l-16 -60h139l-15 60q-14 49 -27 104t-26 106h-4q-12 -52 -24.5 -106.5t-26.5 -103.5zM-6 0l204 652h177l204 -652h-156l-40 155h-199l-40 -155h-150zM273 680l-66 74l171 140l78 -95z" />
<glyph unicode="&#xc2;" horiz-adv-x="573" d="M230 330l-16 -60h139l-15 60q-14 49 -27 104t-26 106h-4q-12 -52 -24.5 -106.5t-26.5 -103.5zM-6 0l204 652h177l204 -652h-156l-40 155h-199l-40 -155h-150zM113 724l90 111h160l90 -111l-64 -39l-104 84h-4l-104 -84z" />
<glyph unicode="&#xc3;" horiz-adv-x="573" d="M230 330l-16 -60h139l-15 60q-14 49 -27 104t-26 106h-4q-12 -52 -24.5 -106.5t-26.5 -103.5zM-6 0l204 652h177l204 -652h-156l-40 155h-199l-40 -155h-150zM354 692q-27 0 -47 9t-36 20t-29 20t-25 9q-14 0 -24 -11t-13 -38l-95 7q4 76 39 110t88 34q27 0 47 -9t36 -20 t29 -20t25 -9q14 0 24 11t13 38l95 -7q-4 -75 -39 -109.5t-88 -34.5z" />
<glyph unicode="&#xc4;" horiz-adv-x="573" d="M230 330l-16 -60h139l-15 60q-14 49 -27 104t-26 106h-4q-12 -52 -24.5 -106.5t-26.5 -103.5zM-6 0l204 652h177l204 -652h-156l-40 155h-199l-40 -155h-150zM172 700q-31 0 -50.5 20t-19.5 50t19.5 50t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20zM394 700 q-31 0 -50.5 20t-19.5 50t19.5 50t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20z" />
<glyph unicode="&#xc5;" horiz-adv-x="573" d="M230 330l-16 -60h139l-15 60q-14 49 -27 104t-26 106h-4q-12 -52 -24.5 -106.5t-26.5 -103.5zM-6 0l204 652h177l204 -652h-156l-40 155h-199l-40 -155h-150zM283 697q-51 0 -82.5 27t-31.5 72t31.5 71.5t82.5 26.5t82.5 -26.5t31.5 -71.5t-31.5 -72t-82.5 -27zM283 752 q17 0 29.5 12t12.5 32t-12.5 31.5t-29.5 11.5t-29.5 -11.5t-12.5 -31.5t12.5 -32t29.5 -12z" />
<glyph unicode="&#xc6;" horiz-adv-x="845" d="M303 356l-42 -93h125v278h-4q-20 -48 -39.5 -95t-39.5 -90zM-14 0l310 652h487v-124h-250v-131h211v-123h-211v-150h260v-124h-407v149h-177l-68 -149h-155z" />
<glyph unicode="&#xc7;" horiz-adv-x="582" d="M349 -12q-62 0 -117 21.5t-96.5 63.5t-65.5 104.5t-24 144.5q0 81 25 144.5t67 107.5t98 67t118 23q63 0 113 -25.5t83 -59.5l-78 -94q-25 23 -52.5 37.5t-62.5 14.5q-33 0 -62.5 -14.5t-51 -41.5t-34 -66t-12.5 -88q0 -101 43 -156.5t115 -55.5q40 0 71.5 17.5 t55.5 43.5l78 -92q-82 -96 -211 -96zM248 -239l-11 59q59 3 81 13t22 31q0 15 -15.5 25.5t-61.5 16.5l48 98h86l-28 -65q38 -9 57 -26t19 -49q0 -51 -51.5 -75.5t-145.5 -27.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="548" d="M77 0v652h408v-124h-261v-131h222v-123h-222v-150h271v-124h-418zM298 680l-183 119l78 95l171 -140z" />
<glyph unicode="&#xc9;" horiz-adv-x="548" d="M77 0v652h408v-124h-261v-131h222v-123h-222v-150h271v-124h-418zM278 680l-66 74l171 140l78 -95z" />
<glyph unicode="&#xca;" horiz-adv-x="548" d="M77 0v652h408v-124h-261v-131h222v-123h-222v-150h271v-124h-418zM118 724l90 111h160l90 -111l-64 -39l-104 84h-4l-104 -84z" />
<glyph unicode="&#xcb;" horiz-adv-x="548" d="M77 0v652h408v-124h-261v-131h222v-123h-222v-150h271v-124h-418zM177 700q-31 0 -50.5 20t-19.5 50t19.5 50t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20zM399 700q-31 0 -50.5 20t-19.5 50t19.5 50t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20z" />
<glyph unicode="&#xcc;" horiz-adv-x="301" d="M77 0v652h147v-652h-147zM160 680l-183 119l78 95l171 -140z" />
<glyph unicode="&#xcd;" horiz-adv-x="301" d="M77 0v652h147v-652h-147zM140 680l-66 74l171 140l78 -95z" />
<glyph unicode="&#xce;" horiz-adv-x="301" d="M77 0v652h147v-652h-147zM-20 724l90 111h160l90 -111l-64 -39l-104 84h-4l-104 -84z" />
<glyph unicode="&#xcf;" horiz-adv-x="301" d="M77 0v652h147v-652h-147zM39 700q-31 0 -50.5 20t-19.5 50t19.5 50t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20zM261 700q-31 0 -50.5 20t-19.5 50t19.5 50t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20z" />
<glyph unicode="&#xd0;" horiz-adv-x="660" d="M26 301v66l76 5v280h184q75 0 135 -19t103 -58.5t66 -100.5t23 -145t-23 -146t-65 -102.5t-100.5 -60.5t-130.5 -20h-192v301h-76zM249 119h28q42 0 76 11t58 35.5t37.5 64.5t13.5 99q0 58 -13.5 97.5t-37.5 63t-58 33.5t-76 10h-28v-161h125v-71h-125v-182z" />
<glyph unicode="&#xd1;" horiz-adv-x="665" d="M77 0v652h151l170 -324l64 -144h4q-5 52 -11.5 116t-6.5 122v230h140v-652h-151l-170 325l-64 142h-4q5 -54 11.5 -116t6.5 -120v-231h-140zM404 692q-27 0 -47 9t-36 20t-29 20t-25 9q-14 0 -24 -11t-13 -38l-95 7q4 76 39 110t88 34q27 0 47 -9t36 -20t29 -20t25 -9 q14 0 24 11t13 38l95 -7q-4 -75 -39 -109.5t-88 -34.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="684" d="M342 -12q-66 0 -120.5 23.5t-93.5 67.5t-60.5 107t-21.5 143t21.5 142.5t60.5 105t93.5 65t120.5 22.5t120.5 -22.5t93.5 -65.5t60.5 -105.5t21.5 -141.5q0 -80 -21.5 -143t-60.5 -107t-93.5 -67.5t-120.5 -23.5zM342 115q67 0 106 57.5t39 156.5q0 98 -39 153t-106 55 t-106 -55t-39 -153q0 -99 39 -156.5t106 -57.5zM352 680l-183 119l78 95l171 -140z" />
<glyph unicode="&#xd3;" horiz-adv-x="684" d="M342 -12q-66 0 -120.5 23.5t-93.5 67.5t-60.5 107t-21.5 143t21.5 142.5t60.5 105t93.5 65t120.5 22.5t120.5 -22.5t93.5 -65.5t60.5 -105.5t21.5 -141.5q0 -80 -21.5 -143t-60.5 -107t-93.5 -67.5t-120.5 -23.5zM342 115q67 0 106 57.5t39 156.5q0 98 -39 153t-106 55 t-106 -55t-39 -153q0 -99 39 -156.5t106 -57.5zM332 680l-66 74l171 140l78 -95z" />
<glyph unicode="&#xd4;" horiz-adv-x="684" d="M342 -12q-66 0 -120.5 23.5t-93.5 67.5t-60.5 107t-21.5 143t21.5 142.5t60.5 105t93.5 65t120.5 22.5t120.5 -22.5t93.5 -65.5t60.5 -105.5t21.5 -141.5q0 -80 -21.5 -143t-60.5 -107t-93.5 -67.5t-120.5 -23.5zM342 115q67 0 106 57.5t39 156.5q0 98 -39 153t-106 55 t-106 -55t-39 -153q0 -99 39 -156.5t106 -57.5zM172 724l90 111h160l90 -111l-64 -39l-104 84h-4l-104 -84z" />
<glyph unicode="&#xd5;" horiz-adv-x="684" d="M342 -12q-66 0 -120.5 23.5t-93.5 67.5t-60.5 107t-21.5 143t21.5 142.5t60.5 105t93.5 65t120.5 22.5t120.5 -22.5t93.5 -65.5t60.5 -105.5t21.5 -141.5q0 -80 -21.5 -143t-60.5 -107t-93.5 -67.5t-120.5 -23.5zM342 115q67 0 106 57.5t39 156.5q0 98 -39 153t-106 55 t-106 -55t-39 -153q0 -99 39 -156.5t106 -57.5zM413 692q-27 0 -47 9t-36 20t-29 20t-25 9q-14 0 -24 -11t-13 -38l-95 7q4 76 39 110t88 34q27 0 47 -9t36 -20t29 -20t25 -9q14 0 24 11t13 38l95 -7q-4 -75 -39 -109.5t-88 -34.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="684" d="M342 -12q-66 0 -120.5 23.5t-93.5 67.5t-60.5 107t-21.5 143t21.5 142.5t60.5 105t93.5 65t120.5 22.5t120.5 -22.5t93.5 -65.5t60.5 -105.5t21.5 -141.5q0 -80 -21.5 -143t-60.5 -107t-93.5 -67.5t-120.5 -23.5zM342 115q67 0 106 57.5t39 156.5q0 98 -39 153t-106 55 t-106 -55t-39 -153q0 -99 39 -156.5t106 -57.5zM231 700q-31 0 -50.5 20t-19.5 50t19.5 50t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20zM453 700q-31 0 -50.5 20t-19.5 50t19.5 50t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20z" />
<glyph unicode="&#xd7;" d="M121 112l-73 74l142 143l-142 144l73 74l143 -144l143 144l73 -74l-142 -144l142 -143l-73 -74l-143 144z" />
<glyph unicode="&#xd8;" horiz-adv-x="684" d="M343 -12q-94 0 -165 48l-62 -77l-76 58l69 86q-29 43 -45.5 100t-16.5 126q0 80 21.5 142.5t60.5 105t93.5 65t120.5 22.5q102 0 175 -53l66 82l76 -59l-76 -94q26 -41 40.5 -93.5t14.5 -117.5q0 -80 -21.5 -143t-60.5 -107t-93.5 -67.5t-120.5 -23.5zM198 329 q0 -28 3.5 -53t9.5 -46l220 273q-36 34 -88 34q-67 0 -106 -55t-39 -153zM343 115q67 0 106 57.5t39 156.5q0 45 -9 81l-215 -267q34 -28 79 -28z" />
<glyph unicode="&#xd9;" horiz-adv-x="665" d="M334 -12q-127 0 -194 75t-67 239v350h148v-365q0 -96 29 -134t84 -38t85 38t30 134v365h142v-350q0 -164 -65.5 -239t-191.5 -75zM342 680l-183 119l78 95l171 -140z" />
<glyph unicode="&#xda;" horiz-adv-x="665" d="M334 -12q-127 0 -194 75t-67 239v350h148v-365q0 -96 29 -134t84 -38t85 38t30 134v365h142v-350q0 -164 -65.5 -239t-191.5 -75zM322 680l-66 74l171 140l78 -95z" />
<glyph unicode="&#xdb;" horiz-adv-x="665" d="M334 -12q-127 0 -194 75t-67 239v350h148v-365q0 -96 29 -134t84 -38t85 38t30 134v365h142v-350q0 -164 -65.5 -239t-191.5 -75zM162 724l90 111h160l90 -111l-64 -39l-104 84h-4l-104 -84z" />
<glyph unicode="&#xdc;" horiz-adv-x="665" d="M334 -12q-127 0 -194 75t-67 239v350h148v-365q0 -96 29 -134t84 -38t85 38t30 134v365h142v-350q0 -164 -65.5 -239t-191.5 -75zM221 700q-31 0 -50.5 20t-19.5 50t19.5 50t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20zM443 700q-31 0 -50.5 20t-19.5 50t19.5 50 t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20z" />
<glyph unicode="&#xdd;" horiz-adv-x="525" d="M189 0v232l-197 420h158l58 -150q14 -37 26.5 -71.5t26.5 -72.5h4q14 38 27.5 72.5t27.5 71.5l59 150h154l-197 -420v-232h-147zM252 680l-66 74l171 140l78 -95z" />
<glyph unicode="&#xde;" horiz-adv-x="617" d="M77 0v652h147v-99h91q53 0 99.5 -10.5t81 -35t54.5 -64.5t20 -99q0 -58 -20.5 -99.5t-55.5 -68.5t-81 -40t-98 -13h-91v-123h-147zM224 240h82q120 0 120 104q0 51 -30.5 71.5t-89.5 20.5h-82v-196z" />
<glyph unicode="&#xdf;" horiz-adv-x="632" d="M421 -12q-45 0 -80 12t-71 33l51 100q49 -36 95 -36q26 0 40.5 13.5t14.5 34.5q0 19 -13 32.5t-32.5 25t-42.5 24t-42.5 29t-32.5 39t-13 55.5q0 29 11 50t24.5 40t24.5 39.5t11 47.5q0 29 -16.5 48.5t-47.5 19.5q-46 0 -69 -33.5t-23 -91.5v-470h-145v487q0 48 14.5 89 t43.5 71t74 47t105 17q51 0 89.5 -14t64 -37.5t38.5 -53.5t13 -62q0 -36 -12 -60.5t-27 -44t-27 -36t-12 -35.5q0 -17 13 -28.5t32.5 -22.5t42.5 -24t42.5 -31.5t32.5 -45t13 -64.5q0 -34 -11.5 -64t-35 -52t-58 -35t-79.5 -13z" />
<glyph unicode="&#xe0;" horiz-adv-x="527" d="M190 -12q-34 0 -61.5 11.5t-46.5 32t-29.5 47.5t-10.5 59q0 78 66 122t213 59q-2 33 -19.5 52.5t-58.5 19.5q-32 0 -63.5 -12t-67.5 -33l-53 97q48 29 100.5 47t111.5 18q96 0 146.5 -54.5t50.5 -169.5v-284h-120l-10 51h-4q-32 -28 -67 -45.5t-77 -17.5zM240 102 q25 0 43 11t38 31v87q-78 -11 -108 -32t-30 -50q0 -24 15.5 -35.5t41.5 -11.5zM265 572l-181 157l99 97l154 -182z" />
<glyph unicode="&#xe1;" horiz-adv-x="527" d="M190 -12q-34 0 -61.5 11.5t-46.5 32t-29.5 47.5t-10.5 59q0 78 66 122t213 59q-2 33 -19.5 52.5t-58.5 19.5q-32 0 -63.5 -12t-67.5 -33l-53 97q48 29 100.5 47t111.5 18q96 0 146.5 -54.5t50.5 -169.5v-284h-120l-10 51h-4q-32 -28 -67 -45.5t-77 -17.5zM240 102 q25 0 43 11t38 31v87q-78 -11 -108 -32t-30 -50q0 -24 15.5 -35.5t41.5 -11.5zM285 572l-72 72l154 182l99 -97z" />
<glyph unicode="&#xe2;" horiz-adv-x="527" d="M190 -12q-34 0 -61.5 11.5t-46.5 32t-29.5 47.5t-10.5 59q0 78 66 122t213 59q-2 33 -19.5 52.5t-58.5 19.5q-32 0 -63.5 -12t-67.5 -33l-53 97q48 29 100.5 47t111.5 18q96 0 146.5 -54.5t50.5 -169.5v-284h-120l-10 51h-4q-32 -28 -67 -45.5t-77 -17.5zM240 102 q25 0 43 11t38 31v87q-78 -11 -108 -32t-30 -50q0 -24 15.5 -35.5t41.5 -11.5zM92 613l108 131h150l108 -131l-56 -52l-125 92h-4l-125 -92z" />
<glyph unicode="&#xe3;" horiz-adv-x="527" d="M190 -12q-34 0 -61.5 11.5t-46.5 32t-29.5 47.5t-10.5 59q0 78 66 122t213 59q-2 33 -19.5 52.5t-58.5 19.5q-32 0 -63.5 -12t-67.5 -33l-53 97q48 29 100.5 47t111.5 18q96 0 146.5 -54.5t50.5 -169.5v-284h-120l-10 51h-4q-32 -28 -67 -45.5t-77 -17.5zM240 102 q25 0 43 11t38 31v87q-78 -11 -108 -32t-30 -50q0 -24 15.5 -35.5t41.5 -11.5zM341 573q-24 0 -42.5 10t-34 22t-28 22t-24.5 10q-15 0 -23.5 -13t-11.5 -42l-92 5q2 78 34 115t90 37q24 0 42.5 -10t34 -22t28 -22t24.5 -10q29 0 35 55l92 -5q-2 -78 -34 -115t-90 -37z" />
<glyph unicode="&#xe4;" horiz-adv-x="527" d="M190 -12q-34 0 -61.5 11.5t-46.5 32t-29.5 47.5t-10.5 59q0 78 66 122t213 59q-2 33 -19.5 52.5t-58.5 19.5q-32 0 -63.5 -12t-67.5 -33l-53 97q48 29 100.5 47t111.5 18q96 0 146.5 -54.5t50.5 -169.5v-284h-120l-10 51h-4q-32 -28 -67 -45.5t-77 -17.5zM240 102 q25 0 43 11t38 31v87q-78 -11 -108 -32t-30 -50q0 -24 15.5 -35.5t41.5 -11.5zM166 569q-32 0 -52 20.5t-20 51.5q0 30 20 50.5t52 20.5t51.5 -20.5t19.5 -50.5q0 -31 -19.5 -51.5t-51.5 -20.5zM384 569q-32 0 -51.5 20.5t-19.5 51.5q0 30 19.5 50.5t51.5 20.5t52 -20.5 t20 -50.5q0 -31 -20 -51.5t-52 -20.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="527" d="M190 -12q-34 0 -61.5 11.5t-46.5 32t-29.5 47.5t-10.5 59q0 78 66 122t213 59q-2 33 -19.5 52.5t-58.5 19.5q-32 0 -63.5 -12t-67.5 -33l-53 97q48 29 100.5 47t111.5 18q96 0 146.5 -54.5t50.5 -169.5v-284h-120l-10 51h-4q-32 -28 -67 -45.5t-77 -17.5zM240 102 q25 0 43 11t38 31v87q-78 -11 -108 -32t-30 -50q0 -24 15.5 -35.5t41.5 -11.5zM275 564q-66 0 -99 30t-33 74t33 74t99 30t99 -30t33 -74t-33 -74t-99 -30zM275 619q18 0 30.5 13.5t12.5 35.5t-12.5 35.5t-30.5 13.5t-30.5 -13.5t-12.5 -35.5t12.5 -35.5t30.5 -13.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="786" d="M194 -12q-34 0 -61 11.5t-46 32t-29.5 47.5t-10.5 58q0 79 65 123.5t211 59.5q-2 32 -18.5 51.5t-58.5 19.5q-30 0 -62 -12t-68 -33l-52 97q48 29 98.5 47t104.5 18q46 0 81 -20t57 -55q32 36 67 55.5t80 19.5q49 0 86 -18.5t61.5 -51t37 -76.5t12.5 -95q0 -20 -2 -36.5 t-4 -25.5h-286q8 -52 41 -77t78 -25q28 0 53 10t52 26l50 -95q-38 -26 -84.5 -41t-90.5 -15q-56 0 -97.5 21t-73.5 56q-53 -42 -97 -59.5t-94 -17.5zM244 102q26 0 49.5 11t43.5 31q-11 31 -14 66l-1 21q-73 -11 -104 -32t-31 -50q0 -24 15.5 -35.5t41.5 -11.5zM457 295h167 q0 45 -18 73t-59 28q-35 0 -59.5 -24.5t-30.5 -76.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="467" d="M281 -12q-52 0 -96.5 17.5t-77.5 50.5t-52 81.5t-19 110.5t21 110.5t56.5 81.5t82.5 50.5t98 17.5q46 0 81.5 -15t63.5 -38l-69 -95q-35 29 -67 29q-54 0 -85 -38t-31 -103t31.5 -103t79.5 -38q24 0 46.5 10.5t42.5 25.5l58 -96q-37 -32 -80 -45.5t-84 -13.5zM180 -239 l-11 59q59 3 81 13t22 31q0 15 -15.5 25.5t-61.5 16.5l48 98h86l-28 -65q38 -9 57 -26t19 -49q0 -51 -51.5 -75.5t-145.5 -27.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="518" d="M287 -12q-53 0 -99 17.5t-80 51t-53 81.5t-19 110q0 60 20 108.5t52.5 82t74.5 51.5t87 18q53 0 92.5 -18t66 -50t39.5 -75.5t13 -94.5q0 -20 -2 -38t-4 -27h-297q10 -54 44.5 -79.5t84.5 -25.5q53 0 107 33l49 -89q-38 -26 -84.5 -41t-91.5 -15zM177 302h179 q0 42 -19 68t-64 26q-35 0 -61.5 -23t-34.5 -71zM260 572l-181 157l99 97l154 -182z" />
<glyph unicode="&#xe9;" horiz-adv-x="518" d="M287 -12q-53 0 -99 17.5t-80 51t-53 81.5t-19 110q0 60 20 108.5t52.5 82t74.5 51.5t87 18q53 0 92.5 -18t66 -50t39.5 -75.5t13 -94.5q0 -20 -2 -38t-4 -27h-297q10 -54 44.5 -79.5t84.5 -25.5q53 0 107 33l49 -89q-38 -26 -84.5 -41t-91.5 -15zM177 302h179 q0 42 -19 68t-64 26q-35 0 -61.5 -23t-34.5 -71zM280 572l-72 72l154 182l99 -97z" />
<glyph unicode="&#xea;" horiz-adv-x="518" d="M287 -12q-53 0 -99 17.5t-80 51t-53 81.5t-19 110q0 60 20 108.5t52.5 82t74.5 51.5t87 18q53 0 92.5 -18t66 -50t39.5 -75.5t13 -94.5q0 -20 -2 -38t-4 -27h-297q10 -54 44.5 -79.5t84.5 -25.5q53 0 107 33l49 -89q-38 -26 -84.5 -41t-91.5 -15zM177 302h179 q0 42 -19 68t-64 26q-35 0 -61.5 -23t-34.5 -71zM87 613l108 131h150l108 -131l-56 -52l-125 92h-4l-125 -92z" />
<glyph unicode="&#xeb;" horiz-adv-x="518" d="M287 -12q-53 0 -99 17.5t-80 51t-53 81.5t-19 110q0 60 20 108.5t52.5 82t74.5 51.5t87 18q53 0 92.5 -18t66 -50t39.5 -75.5t13 -94.5q0 -20 -2 -38t-4 -27h-297q10 -54 44.5 -79.5t84.5 -25.5q53 0 107 33l49 -89q-38 -26 -84.5 -41t-91.5 -15zM177 302h179 q0 42 -19 68t-64 26q-35 0 -61.5 -23t-34.5 -71zM161 569q-32 0 -52 20.5t-20 51.5q0 30 20 50.5t52 20.5t51.5 -20.5t19.5 -50.5q0 -31 -19.5 -51.5t-51.5 -20.5zM379 569q-32 0 -51.5 20.5t-19.5 51.5q0 30 19.5 50.5t51.5 20.5t52 -20.5t20 -50.5q0 -31 -20 -51.5 t-52 -20.5z" />
<glyph unicode="&#xec;" horiz-adv-x="276" d="M65 0v496h147v-496h-147zM128 572l-181 157l99 97l154 -182z" />
<glyph unicode="&#xed;" horiz-adv-x="276" d="M65 0v496h147v-496h-147zM148 572l-72 72l154 182l99 -97z" />
<glyph unicode="&#xee;" horiz-adv-x="276" d="M65 0v496h147v-496h-147zM-45 613l108 131h150l108 -131l-56 -52l-125 92h-4l-125 -92z" />
<glyph unicode="&#xef;" horiz-adv-x="276" d="M65 0v496h147v-496h-147zM29 569q-32 0 -52 20.5t-20 51.5q0 30 20 50.5t52 20.5t51.5 -20.5t19.5 -50.5q0 -31 -19.5 -51.5t-51.5 -20.5zM247 569q-32 0 -51.5 20.5t-19.5 51.5q0 30 19.5 50.5t51.5 20.5t52 -20.5t20 -50.5q0 -31 -20 -51.5t-52 -20.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="560" d="M273 -12q-47 0 -88.5 17t-73.5 48t-50.5 75.5t-18.5 99.5q0 54 17 96t45.5 71t65 44.5t75.5 15.5q29 0 57 -9.5t49 -32.5q-26 77 -85 133l-142 -71l-38 65l116 58q-20 14 -42.5 27t-47.5 26l64 89q35 -18 70 -39t68 -46l143 72l38 -65l-120 -61q59 -60 97 -141.5 t38 -193.5q0 -60 -16.5 -111t-47 -88t-74.5 -58t-99 -21zM278 107q42 0 68.5 36.5t26.5 114.5v23t-1 22q-23 25 -47 34t-50 9q-44 0 -71.5 -29t-27.5 -89q0 -59 30.5 -90t71.5 -31z" />
<glyph unicode="&#xf1;" horiz-adv-x="572" d="M65 0v496h120l10 -63h4q32 30 70 52.5t89 22.5q81 0 117.5 -53t36.5 -147v-308h-147v289q0 54 -14.5 74t-46.5 20q-28 0 -48 -12.5t-44 -35.5v-335h-147zM374 573q-24 0 -42.5 10t-34 22t-28 22t-24.5 10q-15 0 -23.5 -13t-11.5 -42l-92 5q2 78 34 115t90 37 q24 0 42.5 -10t34 -22t28 -22t24.5 -10q29 0 35 55l92 -5q-2 -78 -34 -115t-90 -37z" />
<glyph unicode="&#xf2;" horiz-adv-x="555" d="M278 -12q-47 0 -91 17.5t-77.5 50.5t-53.5 81.5t-20 110.5t20 110.5t53.5 81.5t77.5 50.5t91 17.5t90.5 -17.5t77 -50.5t53.5 -81.5t20 -110.5t-20 -110.5t-53.5 -81.5t-77 -50.5t-90.5 -17.5zM278 107q45 0 68 38t23 103t-23 103t-68 38q-46 0 -68.5 -38t-22.5 -103 t22.5 -103t68.5 -38zM267 572l-181 157l99 97l154 -182z" />
<glyph unicode="&#xf3;" horiz-adv-x="555" d="M278 -12q-47 0 -91 17.5t-77.5 50.5t-53.5 81.5t-20 110.5t20 110.5t53.5 81.5t77.5 50.5t91 17.5t90.5 -17.5t77 -50.5t53.5 -81.5t20 -110.5t-20 -110.5t-53.5 -81.5t-77 -50.5t-90.5 -17.5zM278 107q45 0 68 38t23 103t-23 103t-68 38q-46 0 -68.5 -38t-22.5 -103 t22.5 -103t68.5 -38zM287 572l-72 72l154 182l99 -97z" />
<glyph unicode="&#xf4;" horiz-adv-x="555" d="M278 -12q-47 0 -91 17.5t-77.5 50.5t-53.5 81.5t-20 110.5t20 110.5t53.5 81.5t77.5 50.5t91 17.5t90.5 -17.5t77 -50.5t53.5 -81.5t20 -110.5t-20 -110.5t-53.5 -81.5t-77 -50.5t-90.5 -17.5zM278 107q45 0 68 38t23 103t-23 103t-68 38q-46 0 -68.5 -38t-22.5 -103 t22.5 -103t68.5 -38zM94 613l108 131h150l108 -131l-56 -52l-125 92h-4l-125 -92z" />
<glyph unicode="&#xf5;" horiz-adv-x="555" d="M278 -12q-47 0 -91 17.5t-77.5 50.5t-53.5 81.5t-20 110.5t20 110.5t53.5 81.5t77.5 50.5t91 17.5t90.5 -17.5t77 -50.5t53.5 -81.5t20 -110.5t-20 -110.5t-53.5 -81.5t-77 -50.5t-90.5 -17.5zM278 107q45 0 68 38t23 103t-23 103t-68 38q-46 0 -68.5 -38t-22.5 -103 t22.5 -103t68.5 -38zM343 573q-24 0 -42.5 10t-34 22t-28 22t-24.5 10q-15 0 -23.5 -13t-11.5 -42l-92 5q2 78 34 115t90 37q24 0 42.5 -10t34 -22t28 -22t24.5 -10q29 0 35 55l92 -5q-2 -78 -34 -115t-90 -37z" />
<glyph unicode="&#xf6;" horiz-adv-x="555" d="M278 -12q-47 0 -91 17.5t-77.5 50.5t-53.5 81.5t-20 110.5t20 110.5t53.5 81.5t77.5 50.5t91 17.5t90.5 -17.5t77 -50.5t53.5 -81.5t20 -110.5t-20 -110.5t-53.5 -81.5t-77 -50.5t-90.5 -17.5zM278 107q45 0 68 38t23 103t-23 103t-68 38q-46 0 -68.5 -38t-22.5 -103 t22.5 -103t68.5 -38zM168 569q-32 0 -52 20.5t-20 51.5q0 30 20 50.5t52 20.5t51.5 -20.5t19.5 -50.5q0 -31 -19.5 -51.5t-51.5 -20.5zM386 569q-32 0 -51.5 20.5t-19.5 51.5q0 30 19.5 50.5t51.5 20.5t52 -20.5t20 -50.5q0 -31 -20 -51.5t-52 -20.5z" />
<glyph unicode="&#xf7;" d="M34 278v104h460v-104h-460zM264 73q-35 0 -58 21.5t-23 55.5t23 55.5t58 21.5t58 -21.5t23 -55.5t-23 -55.5t-58 -21.5zM264 433q-35 0 -58 21.5t-23 55.5t23 55.5t58 21.5t58 -21.5t23 -55.5t-23 -55.5t-58 -21.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="555" d="M278 -12q-74 0 -134 40l-43 -53l-55 42l47 58q-26 32 -41.5 75.5t-15.5 97.5q0 62 20 110.5t53.5 81.5t77.5 50.5t91 17.5q35 0 69 -10t63 -29l44 54l55 -43l-47 -58q26 -33 41.5 -76.5t15.5 -97.5q0 -62 -20 -110.5t-53.5 -81.5t-77 -50.5t-90.5 -17.5zM178 256 q0 -40 7 -67l149 186q-22 19 -56 19q-46 0 -73 -37.5t-27 -100.5zM278 102q45 0 72 37.5t27 100.5q0 41 -7 68l-150 -186q23 -20 58 -20z" />
<glyph unicode="&#xf9;" horiz-adv-x="568" d="M214 -12q-81 0 -117.5 53t-36.5 147v308h147v-289q0 -54 15 -74t47 -20q28 0 47 13t41 42v328h147v-496h-120l-11 69h-3q-32 -38 -68.5 -59.5t-87.5 -21.5zM275 572l-181 157l99 97l154 -182z" />
<glyph unicode="&#xfa;" horiz-adv-x="568" d="M214 -12q-81 0 -117.5 53t-36.5 147v308h147v-289q0 -54 15 -74t47 -20q28 0 47 13t41 42v328h147v-496h-120l-11 69h-3q-32 -38 -68.5 -59.5t-87.5 -21.5zM295 572l-72 72l154 182l99 -97z" />
<glyph unicode="&#xfb;" horiz-adv-x="568" d="M214 -12q-81 0 -117.5 53t-36.5 147v308h147v-289q0 -54 15 -74t47 -20q28 0 47 13t41 42v328h147v-496h-120l-11 69h-3q-32 -38 -68.5 -59.5t-87.5 -21.5zM102 613l108 131h150l108 -131l-56 -52l-125 92h-4l-125 -92z" />
<glyph unicode="&#xfc;" horiz-adv-x="568" d="M214 -12q-81 0 -117.5 53t-36.5 147v308h147v-289q0 -54 15 -74t47 -20q28 0 47 13t41 42v328h147v-496h-120l-11 69h-3q-32 -38 -68.5 -59.5t-87.5 -21.5zM176 569q-32 0 -52 20.5t-20 51.5q0 30 20 50.5t52 20.5t51.5 -20.5t19.5 -50.5q0 -31 -19.5 -51.5t-51.5 -20.5z M394 569q-32 0 -51.5 20.5t-19.5 51.5q0 30 19.5 50.5t51.5 20.5t52 -20.5t20 -50.5q0 -31 -20 -51.5t-52 -20.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="521" d="M120 -194q-23 0 -39 2.5t-31 7.5l26 112q7 -2 16 -4t17 -2q37 0 57 18t30 47l7 26l-191 483h148l71 -213q11 -34 20 -69t19 -72h4q8 35 16.5 70.5t17.5 70.5l60 213h141l-172 -501q-18 -47 -37.5 -82.5t-45 -59t-58 -35.5t-76.5 -12zM276 572l-72 72l154 182l99 -97z" />
<glyph unicode="&#xfe;" horiz-adv-x="573" d="M65 -184v885h147v-172l-4 -70q26 23 58 36t66 13q47 0 84.5 -18t63.5 -51t40 -79.5t14 -103.5q0 -64 -17.5 -113.5t-47 -84t-67 -52.5t-77.5 -18q-36 0 -63.5 12t-53.5 35l4 -74v-145h-147zM291 108q38 0 65 34.5t27 111.5q0 134 -86 134q-43 0 -85 -45v-203 q20 -18 40 -25t39 -7z" />
<glyph unicode="&#xff;" horiz-adv-x="521" d="M120 -194q-23 0 -39 2.5t-31 7.5l26 112q7 -2 16 -4t17 -2q37 0 57 18t30 47l7 26l-191 483h148l71 -213q11 -34 20 -69t19 -72h4q8 35 16.5 70.5t17.5 70.5l60 213h141l-172 -501q-18 -47 -37.5 -82.5t-45 -59t-58 -35.5t-76.5 -12zM157 569q-32 0 -52 20.5t-20 51.5 q0 30 20 50.5t52 20.5t51.5 -20.5t19.5 -50.5q0 -31 -19.5 -51.5t-51.5 -20.5zM375 569q-32 0 -51.5 20.5t-19.5 51.5q0 30 19.5 50.5t51.5 20.5t52 -20.5t20 -50.5q0 -31 -20 -51.5t-52 -20.5z" />
<glyph unicode="&#x131;" horiz-adv-x="276" d="M65 0v496h147v-496h-147z" />
<glyph unicode="&#x152;" horiz-adv-x="871" d="M373 0q-70 0 -129.5 20t-103.5 60.5t-69 102.5t-25 146t25 145t69.5 100.5t106 58.5t134.5 19h427v-124h-247v-131h208v-123h-208v-150h257v-124h-445zM390 119h23v414h-23q-43 0 -78.5 -10t-61 -33.5t-39.5 -62.5t-14 -98t14 -99.5t39.5 -65t61 -35t78.5 -10.5z" />
<glyph unicode="&#x153;" horiz-adv-x="822" d="M267 -12q-48 0 -90 17.5t-73.5 50.5t-49.5 81.5t-18 110.5t19 110.5t51 81.5t74.5 50.5t89.5 17.5q53 0 92.5 -23t68.5 -64q30 42 71 64.5t86 22.5q49 0 86 -18.5t62 -51t37.5 -76.5t12.5 -95q0 -20 -2 -36.5t-4 -25.5h-287q8 -52 41 -77t78 -25q28 0 53 10t52 26l51 -95 q-38 -26 -85 -41t-91 -15q-45 0 -87 22.5t-73 64.5q-31 -43 -70.5 -65t-94.5 -22zM270 107q41 0 64 38t23 103t-23 103t-64 38t-63 -38t-22 -103t22 -103t63 -38zM493 295h167q0 45 -18 73t-59 28q-35 0 -59.5 -24.5t-30.5 -76.5z" />
<glyph unicode="&#x178;" horiz-adv-x="525" d="M189 0v232l-197 420h158l58 -150q14 -37 26.5 -71.5t26.5 -72.5h4q14 38 27.5 72.5t27.5 71.5l59 150h154l-197 -420v-232h-147zM151 700q-31 0 -50.5 20t-19.5 50t19.5 50t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20zM373 700q-31 0 -50.5 20t-19.5 50t19.5 50 t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20z" />
<glyph unicode="&#x2c6;" horiz-adv-x="555" d="M95 613l108 131h150l108 -131l-56 -52l-125 92h-4l-125 -92z" />
<glyph unicode="&#x2dc;" horiz-adv-x="555" d="M344 573q-24 0 -42.5 10t-34 22t-28 22t-24.5 10q-15 0 -23.5 -13t-11.5 -42l-92 5q2 78 34 115t90 37q24 0 42.5 -10t34 -22t28 -22t24.5 -10q29 0 35 55l92 -5q-2 -78 -34 -115t-90 -37z" />
<glyph unicode="&#x300;" horiz-adv-x="0" d="M-10 572l-181 157l99 97l154 -182z" />
<glyph unicode="&#x301;" horiz-adv-x="0" d="M10 572l-72 72l154 182l99 -97z" />
<glyph unicode="&#x302;" horiz-adv-x="0" d="M-183 613l108 131h150l108 -131l-56 -52l-125 92h-4l-125 -92z" />
<glyph unicode="&#x303;" horiz-adv-x="0" d="M66 573q-24 0 -42.5 10t-34 22t-28 22t-24.5 10q-15 0 -23.5 -13t-11.5 -42l-92 5q2 78 34 115t90 37q24 0 42.5 -10t34 -22t28 -22t24.5 -10q29 0 35 55l92 -5q-2 -78 -34 -115t-90 -37z" />
<glyph unicode="&#x304;" horiz-adv-x="0" d="M-148 592v93h296v-93h-296z" />
<glyph unicode="&#x308;" horiz-adv-x="0" d="M-109 569q-32 0 -52 20.5t-20 51.5q0 30 20 50.5t52 20.5t51.5 -20.5t19.5 -50.5q0 -31 -19.5 -51.5t-51.5 -20.5zM109 569q-32 0 -51.5 20.5t-19.5 51.5q0 30 19.5 50.5t51.5 20.5t52 -20.5t20 -50.5q0 -31 -20 -51.5t-52 -20.5z" />
<glyph unicode="&#x30a;" horiz-adv-x="0" d="M0 564q-66 0 -99 30t-33 74t33 74t99 30t99 -30t33 -74t-33 -74t-99 -30zM0 619q18 0 30.5 13.5t12.5 35.5t-12.5 35.5t-30.5 13.5t-30.5 -13.5t-12.5 -35.5t12.5 -35.5t30.5 -13.5z" />
<glyph unicode="&#x327;" horiz-adv-x="0" d="M-102 -239l-11 59q59 3 81 13t22 31q0 15 -15.5 25.5t-61.5 16.5l48 98h86l-28 -65q38 -9 57 -26t19 -49q0 -51 -51.5 -75.5t-145.5 -27.5z" />
<glyph unicode="&#x3bc;" horiz-adv-x="594" d="M65 -177v673h147v-289q0 -53 14.5 -73.5t46.5 -20.5q25 0 45 14.5t40 50.5v318h148q-1 -44 -3 -91.5t-3.5 -94t-2.5 -88.5t-1 -73q0 -24 12 -33t32 -9q15 0 32 6l17 -109q-16 -7 -35.5 -11.5t-51.5 -4.5q-104 0 -128 83h-4q-37 -78 -105 -78q-18 0 -34 4.5t-28 18.5 q0 -28 1 -52t2 -46.5t2.5 -45.5t4.5 -49h-148z" />
<glyph unicode="&#x1d43;" horiz-adv-x="359" d="M130 367q-47 0 -74 29.5t-27 70.5q0 53 44 82t143 38q-2 21 -13.5 32.5t-35.5 11.5q-20 0 -43 -8.5t-47 -21.5l-37 68q33 20 69.5 32t75.5 12q64 0 99.5 -37t35.5 -112v-189h-83l-10 34h-4q-18 -18 -41 -30t-52 -12zM165 446q15 0 26.5 7t24.5 19v55q-50 -5 -69 -19.5 t-19 -31.5q0 -30 37 -30z" />
<glyph unicode="&#x1d52;" horiz-adv-x="373" d="M186 367q-32 0 -61 11.5t-51.5 34t-36 54t-13.5 72.5t13.5 73t36 54.5t51.5 34.5t61 12t61 -12t51.5 -34.5t36 -54.5t13.5 -73t-13.5 -72.5t-36 -54t-51.5 -34t-61 -11.5zM186 449q29 0 43 24.5t14 65.5q0 42 -14 67t-43 25t-42.5 -25t-13.5 -67q0 -41 13.5 -65.5 t42.5 -24.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="447" />
<glyph unicode="&#x2001;" horiz-adv-x="894" />
<glyph unicode="&#x2002;" horiz-adv-x="447" />
<glyph unicode="&#x2003;" horiz-adv-x="894" />
<glyph unicode="&#x2004;" horiz-adv-x="298" />
<glyph unicode="&#x2005;" horiz-adv-x="223" />
<glyph unicode="&#x2006;" horiz-adv-x="149" />
<glyph unicode="&#x2007;" horiz-adv-x="149" />
<glyph unicode="&#x2008;" horiz-adv-x="111" />
<glyph unicode="&#x2009;" horiz-adv-x="178" />
<glyph unicode="&#x200a;" horiz-adv-x="49" />
<glyph unicode="&#x2010;" horiz-adv-x="332" d="M43 201v104h246v-104h-246z" />
<glyph unicode="&#x2011;" horiz-adv-x="332" d="M43 201v104h246v-104h-246z" />
<glyph unicode="&#x2012;" horiz-adv-x="332" d="M43 201v104h246v-104h-246z" />
<glyph unicode="&#x2013;" horiz-adv-x="480" d="M43 206v94h394v-94h-394z" />
<glyph unicode="&#x2014;" horiz-adv-x="800" d="M43 206v94h714v-94h-714z" />
<glyph unicode="&#x2018;" horiz-adv-x="300" d="M149 338q-47 0 -70.5 34.5t-23.5 96.5q0 71 35 125t107 89l32 -64q-45 -23 -67.5 -52.5t-22.5 -74.5q3 1 10 1q32 0 54.5 -19.5t22.5 -53.5q0 -38 -22 -60t-55 -22z" />
<glyph unicode="&#x2019;" horiz-adv-x="300" d="M103 352l-32 64q45 23 67.5 52.5t22.5 74.5q-3 -1 -9 -1q-32 0 -54.5 19.5t-22.5 53.5q0 38 21.5 60t55.5 22q46 0 70 -34t24 -97q0 -71 -35.5 -125t-107.5 -89z" />
<glyph unicode="&#x201a;" horiz-adv-x="300" d="M103 -168l-32 64q45 23 67.5 52.5t22.5 74.5q-3 -1 -9 -1q-32 0 -54.5 19.5t-22.5 53.5q0 38 21.5 60t55.5 22q46 0 70 -34t24 -97q0 -71 -35.5 -125t-107.5 -89z" />
<glyph unicode="&#x201c;" horiz-adv-x="536" d="M149 338q-47 0 -70.5 34.5t-23.5 96.5q0 71 35 125t107 89l32 -64q-45 -23 -67.5 -52.5t-22.5 -74.5q3 1 10 1q32 0 54.5 -19.5t22.5 -53.5q0 -38 -22 -60t-55 -22zM385 338q-47 0 -70.5 34.5t-23.5 96.5q0 71 35 125t107 89l32 -64q-45 -23 -67.5 -52.5t-22.5 -74.5 q3 1 10 1q32 0 54.5 -19.5t22.5 -53.5q0 -38 -22 -60t-55 -22z" />
<glyph unicode="&#x201d;" horiz-adv-x="536" d="M103 352l-32 64q45 23 67.5 52.5t22.5 74.5q-3 -1 -9 -1q-32 0 -54.5 19.5t-22.5 53.5q0 38 21.5 60t55.5 22q46 0 70 -34t24 -97q0 -71 -35.5 -125t-107.5 -89zM339 352l-32 64q45 23 67.5 52.5t22.5 74.5q-3 -1 -9 -1q-32 0 -54.5 19.5t-22.5 53.5q0 38 21.5 60 t55.5 22q46 0 70 -34t24 -97q0 -71 -35.5 -125t-107.5 -89z" />
<glyph unicode="&#x201e;" horiz-adv-x="536" d="M103 -168l-32 64q45 23 67.5 52.5t22.5 74.5q-3 -1 -9 -1q-32 0 -54.5 19.5t-22.5 53.5q0 38 21.5 60t55.5 22q46 0 70 -34t24 -97q0 -71 -35.5 -125t-107.5 -89zM339 -168l-32 64q45 23 67.5 52.5t22.5 74.5q-3 -1 -9 -1q-32 0 -54.5 19.5t-22.5 53.5q0 38 21.5 60 t55.5 22q46 0 70 -34t24 -97q0 -71 -35.5 -125t-107.5 -89z" />
<glyph unicode="&#x2022;" horiz-adv-x="345" d="M172 123q-28 0 -52 10.5t-42 29.5t-28 44t-10 55t10 55.5t28 44t42 29t52 10.5t52.5 -10.5t42.5 -29t28 -44t10 -55.5t-10 -55t-28 -44t-42.5 -29.5t-52.5 -10.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="977" d="M165 -12q-38 0 -63.5 26.5t-25.5 65.5q0 40 25.5 66.5t63.5 26.5t63.5 -26.5t25.5 -66.5q0 -39 -25.5 -65.5t-63.5 -26.5zM496 -12q-38 0 -63.5 26.5t-25.5 65.5q0 40 25.5 66.5t63.5 26.5t63.5 -26.5t25.5 -66.5q0 -39 -25.5 -65.5t-63.5 -26.5zM827 -12 q-38 0 -63.5 26.5t-25.5 65.5q0 40 25.5 66.5t63.5 26.5t63.5 -26.5t25.5 -66.5q0 -39 -25.5 -65.5t-63.5 -26.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="178" />
<glyph unicode="&#x2039;" horiz-adv-x="292" d="M183 56l-134 144v104l134 144l55 -44l-111 -152l111 -152z" />
<glyph unicode="&#x203a;" horiz-adv-x="292" d="M109 56l-55 44l111 152l-111 152l55 44l134 -144v-104z" />
<glyph unicode="&#x2044;" horiz-adv-x="96" d="M-171 -12l352 676h85l-352 -676h-85z" />
<glyph unicode="&#x205f;" horiz-adv-x="223" />
<glyph unicode="&#x20ac;" d="M337 -12q-94 0 -162.5 57.5t-91.5 169.5h-62v69l54 4q-1 7 -1 13v13v13.5t1 12.5h-54v68l62 5q12 56 36 99.5t58.5 73.5t77.5 45.5t93 15.5q45 0 88 -20t75 -58l-82 -79q-19 19 -39.5 31t-46.5 12q-43 0 -71.5 -31t-40.5 -88h214v-74h-223v-21v-15t1 -14h182v-75h-172 q14 -55 42.5 -84t70.5 -29q29 0 50.5 14t41.5 40l81 -76q-36 -45 -82 -68.5t-100 -23.5z" />
<glyph unicode="&#x2122;" horiz-adv-x="682" d="M91 362v227h-87v87h269v-87h-87v-227h-95zM316 362v314h108l39 -99l22 -66h4l23 66l38 99h109v-314h-88v96l11 115h-4l-57 -165h-68l-56 165h-4l11 -115v-96h-88z" />
<glyph unicode="&#x2212;" d="M34 278v104h460v-104h-460z" />
<glyph unicode="&#x25fc;" horiz-adv-x="495" d="M0 495h495v-495h-495v495z" />
<glyph unicode="&#xfb01;" horiz-adv-x="635" d="M90 0v381h-66v109l66 5v27q0 39 9.5 74t31.5 61t57 41t85 15q31 0 56.5 -6t42.5 -12l-27 -108q-28 10 -51 10q-27 0 -42 -16.5t-15 -54.5v-30h89v-115h-89v-381h-147zM424 0v496h147v-496h-147zM497 569q-37 0 -61 21.5t-24 55.5t24 55t61 21q38 0 61.5 -21t23.5 -55 t-23.5 -55.5t-61.5 -21.5z" />
<glyph unicode="&#xfb02;" horiz-adv-x="627" d="M90 0v381h-66v109l66 5v27q0 39 9.5 74t31.5 61t57 41t85 15q31 0 56.5 -6t42.5 -12l-27 -108q-28 10 -51 10q-27 0 -42 -16.5t-15 -54.5v-30h89v-115h-89v-381h-147zM541 -12q-38 0 -64 12t-41.5 33.5t-22.5 52t-7 68.5v547h147v-553q0 -23 8.5 -32t17.5 -9h8.5t9.5 2 l18 -109q-12 -5 -30.5 -8.5t-43.5 -3.5z" />
<glyph unicode="&#xfb03;" horiz-adv-x="933" d="M406 0v381h-169v-381h-147v381h-66v109l66 5v23q0 38 10.5 72t33 59t58 39.5t86.5 14.5q32 0 59 -5.5t44 -12.5l-27 -109q-12 5 -25 8t-33 3q-26 0 -42.5 -16.5t-16.5 -51.5v-23h169v26q0 39 9.5 74t31.5 61t57 41t85 15q31 0 56.5 -6t42.5 -12l-27 -108q-28 10 -51 10 q-27 0 -42 -16.5t-15 -54.5v-30h89v-115h-89v-381h-147zM722 0v496h147v-496h-147zM795 569q-37 0 -61 21.5t-24 55.5t24 55t61 21q38 0 61.5 -21t23.5 -55t-23.5 -55.5t-61.5 -21.5z" />
<glyph unicode="&#xfb04;" horiz-adv-x="943" d="M406 0v381h-169v-381h-147v381h-66v109l66 5v23q0 38 10.5 72t33 59t58 39.5t86.5 14.5q32 0 59 -5.5t44 -12.5l-27 -109q-12 5 -25 8t-33 3q-26 0 -42.5 -16.5t-16.5 -51.5v-23h169v26q0 39 9.5 74t31.5 61t57 41t85 15q31 0 56.5 -6t42.5 -12l-27 -108q-28 10 -51 10 q-27 0 -42 -16.5t-15 -54.5v-30h89v-115h-89v-381h-147zM857 -12q-38 0 -64 12t-41.5 33.5t-22.5 52t-7 68.5v547h147v-553q0 -23 8.5 -32t17.5 -9h8.5t9.5 2l18 -109q-12 -5 -30.5 -8.5t-43.5 -3.5z" />
<glyph horiz-adv-x="376" d="M188 -12q-35 0 -65 14t-51.5 41t-33.5 65.5t-12 87.5t12 87t33.5 64.5t51.5 40.5t65 14t65 -14t51.5 -40.5t34 -64.5t12.5 -87t-12.5 -87.5t-34 -65.5t-51.5 -41t-65 -14zM188 70q25 0 42.5 28t17.5 98t-17.5 97t-42.5 27t-42.5 -27t-17.5 -97t17.5 -98t42.5 -28z" />
<glyph horiz-adv-x="376" d="M165 0v281h-89v66q20 3 35.5 7t28.5 9t24.5 11.5t23.5 15.5h86v-390h-109z" />
<glyph horiz-adv-x="376" d="M45 0v58q75 62 123 109.5t48 84.5q0 29 -15 45t-42 16q-20 0 -37 -12.5t-34 -34.5l-61 56q60 80 149 80q65 0 104.5 -34.5t39.5 -97.5q0 -24 -8.5 -46.5t-23 -44.5t-33 -44t-39.5 -45h118v-90h-289z" />
<glyph horiz-adv-x="376" d="M186 -12q-51 0 -92 20.5t-67 58.5l65 51q35 -48 83 -48q23 0 39.5 12t16.5 35q0 51 -93 51v62q36 0 57.5 11.5t21.5 36.5q0 20 -13 31t-36 11q-19 0 -34.5 -11t-29.5 -28l-61 54q32 35 65 51t79 16q27 0 51.5 -7.5t43.5 -21.5t30 -34.5t11 -45.5q0 -31 -15 -52.5 t-42 -37.5q30 -12 50.5 -35.5t20.5 -58.5q0 -27 -12.5 -49.5t-33 -38t-48 -24.5t-57.5 -9z" />
<glyph horiz-adv-x="376" d="M214 0v87h-179v54l141 249h134v-233h58v-70h-58v-87h-96zM134 157h80v41l6 117h-4l-40 -77z" />
<glyph horiz-adv-x="0" d="M10 680l-183 119l78 95l171 -140z" />
<glyph horiz-adv-x="0" d="M-10 680l-66 74l171 140l78 -95z" />
<glyph horiz-adv-x="0" d="M-170 724l90 111h160l90 -111l-64 -39l-104 84h-4l-104 -84z" />
<glyph horiz-adv-x="0" d="M71 692q-27 0 -47 9t-36 20t-29 20t-25 9q-14 0 -24 -11t-13 -38l-95 7q4 76 39 110t88 34q27 0 47 -9t36 -20t29 -20t25 -9q14 0 24 11t13 38l95 -7q-4 -75 -39 -109.5t-88 -34.5z" />
<glyph horiz-adv-x="0" d="M-111 700q-31 0 -50.5 20t-19.5 50t19.5 50t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20zM111 700q-31 0 -50.5 20t-19.5 50t19.5 50t50.5 20t50.5 -20t19.5 -50t-19.5 -50t-50.5 -20z" />
<glyph horiz-adv-x="0" d="M0 697q-51 0 -82.5 27t-31.5 72t31.5 71.5t82.5 26.5t82.5 -26.5t31.5 -71.5t-31.5 -72t-82.5 -27zM0 752q17 0 29.5 12t12.5 32t-12.5 31.5t-29.5 11.5t-29.5 -11.5t-12.5 -31.5t12.5 -32t29.5 -12z" />
<glyph horiz-adv-x="0" d="M-102 -239l-11 59q59 3 81 13t22 31q0 15 -15.5 25.5t-61.5 16.5l48 98h86l-28 -65q38 -9 57 -26t19 -49q0 -51 -51.5 -75.5t-145.5 -27.5z" />
<hkern u1="&#x2f;" u2="&#xef;" k="-47" />
<hkern u1="&#x2f;" u2="&#xee;" k="-47" />
<hkern u1="&#x2f;" u2="&#xec;" k="-33" />
<hkern u1="F" u2="&#xef;" k="-32" />
<hkern u1="F" u2="&#xee;" k="-15" />
<hkern u1="V" u2="&#xef;" k="-68" />
<hkern u1="V" u2="&#xee;" k="-44" />
<hkern u1="V" u2="&#xed;" k="-4" />
<hkern u1="V" u2="&#xec;" k="-20" />
<hkern u1="x" u2="&#x3b;" k="-16" />
<hkern u1="x" u2="&#x2c;" k="-16" />
<hkern g1="backslash" 	g2="Eth" 	k="15" />
<hkern g1="backslash" 	g2="g" 	k="-24" />
<hkern g1="backslash" 	g2="j" 	k="-64" />
<hkern g1="backslash" 	g2="T" 	k="67" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="15" />
<hkern g1="backslash" 	g2="v" 	k="20" />
<hkern g1="backslash" 	g2="V" 	k="44" />
<hkern g1="backslash" 	g2="w" 	k="10" />
<hkern g1="backslash" 	g2="W" 	k="15" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="-4" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="64" />
<hkern g1="exclamdown" 	g2="j" 	k="-24" />
<hkern g1="exclamdown" 	g2="V" 	k="52" />
<hkern g1="exclamdown" 	g2="W" 	k="26" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="periodcentered" 	g2="T" 	k="68" />
<hkern g1="periodcentered" 	g2="V" 	k="36" />
<hkern g1="periodcentered" 	g2="Y,Yacute,Ydieresis" 	k="88" />
<hkern g1="periodcentered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="28" />
<hkern g1="periodcentered" 	g2="S" 	k="28" />
<hkern g1="periodcentered" 	g2="x" 	k="20" />
<hkern g1="periodcentered" 	g2="X" 	k="36" />
<hkern g1="periodcentered" 	g2="Z" 	k="32" />
<hkern g1="questiondown" 	g2="j" 	k="-45" />
<hkern g1="questiondown" 	g2="T" 	k="96" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="54" />
<hkern g1="questiondown" 	g2="V" 	k="88" />
<hkern g1="questiondown" 	g2="W" 	k="54" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="126" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="89" />
<hkern g1="questiondown" 	g2="S" 	k="60" />
<hkern g1="questiondown" 	g2="X" 	k="69" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="53" />
<hkern g1="questiondown" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="89" />
<hkern g1="questiondown" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="85" />
<hkern g1="slash" 	g2="g" 	k="10" />
<hkern g1="slash" 	g2="j" 	k="-7" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="28" />
<hkern g1="slash" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="slash" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-7" />
<hkern g1="slash" 	g2="J" 	k="80" />
<hkern g1="slash" 	g2="t" 	k="5" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="t" 	k="18" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="T" 	k="28" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="V" 	k="26" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="W" 	k="8" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="Y,Yacute,Ydieresis" 	k="28" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="32" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="26" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="44" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="73" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="36" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="59" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="84" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="periodcentered" 	k="28" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="-14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="11" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="x" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="84" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="73" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="40" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="registered" 	k="94" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="72" />
<hkern g1="B" 	g2="t" 	k="10" />
<hkern g1="B" 	g2="T" 	k="28" />
<hkern g1="B" 	g2="V" 	k="13" />
<hkern g1="B" 	g2="W" 	k="8" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="B" 	g2="asterisk" 	k="30" />
<hkern g1="B" 	g2="periodcentered" 	k="20" />
<hkern g1="B" 	g2="v" 	k="18" />
<hkern g1="B" 	g2="w" 	k="10" />
<hkern g1="B" 	g2="x" 	k="10" />
<hkern g1="B" 	g2="X" 	k="8" />
<hkern g1="B" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="B" 	g2="trademark" 	k="20" />
<hkern g1="B" 	g2="J" 	k="19" />
<hkern g1="B" 	g2="S" 	k="18" />
<hkern g1="B" 	g2="Z" 	k="8" />
<hkern g1="c,ccedilla" 	g2="t" 	k="10" />
<hkern g1="c,ccedilla" 	g2="T" 	k="20" />
<hkern g1="c,ccedilla" 	g2="V" 	k="18" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="28" />
<hkern g1="c,ccedilla" 	g2="periodcentered" 	k="18" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="c,ccedilla" 	g2="x" 	k="5" />
<hkern g1="c,ccedilla" 	g2="registered" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="g" 	k="10" />
<hkern g1="c,ccedilla" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="28" />
<hkern g1="c,ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="34" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="36" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="12" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="8" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="8" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="C,Ccedilla" 	g2="periodcentered" 	k="72" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="8" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="registered" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="trademark" 	k="-14" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="S" 	k="28" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="36" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="28" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="26" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="36" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk" 	k="24" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="periodcentered" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="11" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="34" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="registered" 	k="15" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="26" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="J" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="S" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="g" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="hyphen,uni00AD,endash,emdash" 	k="-10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="guillemotleft,guilsinglleft" 	k="15" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="7" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="28" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="x" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="f,uniFB00" 	g2="T" 	k="-42" />
<hkern g1="f,uniFB00" 	g2="V" 	k="-62" />
<hkern g1="f,uniFB00" 	g2="W" 	k="-42" />
<hkern g1="f,uniFB00" 	g2="Y,Yacute,Ydieresis" 	k="-45" />
<hkern g1="f,uniFB00" 	g2="question" 	k="-22" />
<hkern g1="f,uniFB00" 	g2="quoteright,quotedblright" 	k="-24" />
<hkern g1="f,uniFB00" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="64" />
<hkern g1="f,uniFB00" 	g2="periodcentered" 	k="20" />
<hkern g1="f,uniFB00" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="f,uniFB00" 	g2="s" 	k="8" />
<hkern g1="f,uniFB00" 	g2="v" 	k="-4" />
<hkern g1="f,uniFB00" 	g2="x" 	k="8" />
<hkern g1="f,uniFB00" 	g2="X" 	k="-24" />
<hkern g1="f,uniFB00" 	g2="quoteleft,quotedblleft" 	k="-24" />
<hkern g1="f,uniFB00" 	g2="quotedbl,quotesingle" 	k="-40" />
<hkern g1="f,uniFB00" 	g2="backslash" 	k="-32" />
<hkern g1="f,uniFB00" 	g2="registered" 	k="-50" />
<hkern g1="f,uniFB00" 	g2="trademark" 	k="-72" />
<hkern g1="f,uniFB00" 	g2="g" 	k="11" />
<hkern g1="f,uniFB00" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="f,uniFB00" 	g2="hyphen,uni00AD,endash,emdash" 	k="18" />
<hkern g1="f,uniFB00" 	g2="z" 	k="18" />
<hkern g1="f,uniFB00" 	g2="j" 	k="10" />
<hkern g1="f,uniFB00" 	g2="parenright,bracketright,braceright" 	k="-35" />
<hkern g1="f,uniFB00" 	g2="exclam" 	k="-4" />
<hkern g1="f,uniFB00" 	g2="slash" 	k="4" />
<hkern g1="F" 	g2="t" 	k="10" />
<hkern g1="F" 	g2="V" 	k="6" />
<hkern g1="F" 	g2="W" 	k="6" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="92" />
<hkern g1="F" 	g2="periodcentered" 	k="18" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="38" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="F" 	g2="s" 	k="27" />
<hkern g1="F" 	g2="v" 	k="34" />
<hkern g1="F" 	g2="w" 	k="26" />
<hkern g1="F" 	g2="x" 	k="36" />
<hkern g1="F" 	g2="X" 	k="28" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="26" />
<hkern g1="F" 	g2="registered" 	k="-6" />
<hkern g1="F" 	g2="trademark" 	k="-11" />
<hkern g1="F" 	g2="J" 	k="134" />
<hkern g1="F" 	g2="S" 	k="27" />
<hkern g1="F" 	g2="Z" 	k="30" />
<hkern g1="F" 	g2="g" 	k="28" />
<hkern g1="F" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="19" />
<hkern g1="F" 	g2="z" 	k="52" />
<hkern g1="F" 	g2="slash" 	k="48" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="39" />
<hkern g1="F" 	g2="m,n,p,r,ntilde,dotlessi" 	k="20" />
<hkern g1="germandbls" 	g2="t" 	k="19" />
<hkern g1="germandbls" 	g2="question" 	k="26" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="88" />
<hkern g1="germandbls" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="8" />
<hkern g1="germandbls" 	g2="v" 	k="26" />
<hkern g1="germandbls" 	g2="w" 	k="18" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="26" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="71" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="76" />
<hkern g1="germandbls" 	g2="backslash" 	k="36" />
<hkern g1="germandbls" 	g2="registered" 	k="53" />
<hkern g1="g" 	g2="T" 	k="36" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="g" 	g2="question" 	k="46" />
<hkern g1="g" 	g2="asterisk" 	k="24" />
<hkern g1="g" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="19" />
<hkern g1="g" 	g2="v" 	k="8" />
<hkern g1="g" 	g2="w" 	k="8" />
<hkern g1="g" 	g2="y,yacute,ydieresis" 	k="-4" />
<hkern g1="g" 	g2="registered" 	k="-4" />
<hkern g1="g" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="g" 	g2="z" 	k="18" />
<hkern g1="g" 	g2="j" 	k="-46" />
<hkern g1="g" 	g2="parenright,bracketright,braceright" 	k="-4" />
<hkern g1="g" 	g2="slash" 	k="-28" />
<hkern g1="G" 	g2="T" 	k="20" />
<hkern g1="G" 	g2="V" 	k="18" />
<hkern g1="G" 	g2="W" 	k="8" />
<hkern g1="G" 	g2="asterisk" 	k="22" />
<hkern g1="G" 	g2="registered" 	k="12" />
<hkern g1="G" 	g2="trademark" 	k="11" />
<hkern g1="G" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="8" />
<hkern g1="J" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="28" />
<hkern g1="J" 	g2="J" 	k="40" />
<hkern g1="k" 	g2="t" 	k="35" />
<hkern g1="k" 	g2="T" 	k="53" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="k" 	g2="question" 	k="32" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="36" />
<hkern g1="k" 	g2="asterisk" 	k="20" />
<hkern g1="k" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="k" 	g2="periodcentered" 	k="36" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="k" 	g2="x" 	k="16" />
<hkern g1="k" 	g2="registered" 	k="15" />
<hkern g1="k" 	g2="trademark" 	k="20" />
<hkern g1="k" 	g2="g" 	k="10" />
<hkern g1="k" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="28" />
<hkern g1="k" 	g2="hyphen,uni00AD,endash,emdash" 	k="85" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="36" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="18" />
<hkern g1="k" 	g2="z" 	k="16" />
<hkern g1="k" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="k" 	g2="j" 	k="10" />
<hkern g1="K" 	g2="t" 	k="53" />
<hkern g1="K" 	g2="T" 	k="26" />
<hkern g1="K" 	g2="V" 	k="18" />
<hkern g1="K" 	g2="W" 	k="10" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="26" />
<hkern g1="K" 	g2="question" 	k="16" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="48" />
<hkern g1="K" 	g2="asterisk" 	k="40" />
<hkern g1="K" 	g2="periodcentered" 	k="56" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="18" />
<hkern g1="K" 	g2="v" 	k="34" />
<hkern g1="K" 	g2="w" 	k="26" />
<hkern g1="K" 	g2="x" 	k="26" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="34" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="48" />
<hkern g1="K" 	g2="quotedbl,quotesingle" 	k="35" />
<hkern g1="K" 	g2="registered" 	k="28" />
<hkern g1="K" 	g2="trademark" 	k="5" />
<hkern g1="K" 	g2="S" 	k="20" />
<hkern g1="K" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="52" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="11" />
<hkern g1="K" 	g2="z" 	k="18" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="8" />
<hkern g1="K" 	g2="j" 	k="10" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="l,uniFB02" 	g2="j" 	k="-11" />
<hkern g1="L" 	g2="t" 	k="27" />
<hkern g1="L" 	g2="T" 	k="120" />
<hkern g1="L" 	g2="V" 	k="79" />
<hkern g1="L" 	g2="W" 	k="59" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="86" />
<hkern g1="L" 	g2="question" 	k="52" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="108" />
<hkern g1="L" 	g2="asterisk" 	k="172" />
<hkern g1="L" 	g2="periodcentered" 	k="112" />
<hkern g1="L" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="18" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="36" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="37" />
<hkern g1="L" 	g2="v" 	k="53" />
<hkern g1="L" 	g2="w" 	k="38" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="53" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="108" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="140" />
<hkern g1="L" 	g2="backslash" 	k="80" />
<hkern g1="L" 	g2="registered" 	k="112" />
<hkern g1="L" 	g2="trademark" 	k="128" />
<hkern g1="L" 	g2="S" 	k="28" />
<hkern g1="L" 	g2="g" 	k="8" />
<hkern g1="L" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="L" 	g2="hyphen,uni00AD,endash,emdash" 	k="44" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="24" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="h,m,n,ntilde" 	g2="T" 	k="28" />
<hkern g1="h,m,n,ntilde" 	g2="V" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="26" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="16" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk" 	k="24" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="20" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="t" 	k="26" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="59" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V" 	k="20" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="56" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="question" 	k="32" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="36" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk" 	k="7" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="11" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="5" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="s" 	k="5" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v" 	k="8" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="w" 	k="8" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="26" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="X" 	k="8" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="y,yacute,ydieresis" 	k="8" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="15" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="36" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="registered" 	k="16" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="13" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="28" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="asterisk" 	k="24" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="18" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="26" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="36" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="54" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="16" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="12" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="P" 	g2="T" 	k="14" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="132" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="34" />
<hkern g1="P" 	g2="s" 	k="10" />
<hkern g1="P" 	g2="x" 	k="18" />
<hkern g1="P" 	g2="X" 	k="28" />
<hkern g1="P" 	g2="registered" 	k="-16" />
<hkern g1="P" 	g2="J" 	k="142" />
<hkern g1="P" 	g2="S" 	k="10" />
<hkern g1="P" 	g2="Z" 	k="51" />
<hkern g1="P" 	g2="g" 	k="16" />
<hkern g1="P" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="21" />
<hkern g1="P" 	g2="hyphen,uni00AD,endash,emdash" 	k="24" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="P" 	g2="z" 	k="20" />
<hkern g1="P" 	g2="slash" 	k="50" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="50" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="9" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-7" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="88" />
<hkern g1="r" 	g2="periodcentered" 	k="12" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="28" />
<hkern g1="r" 	g2="s" 	k="8" />
<hkern g1="r" 	g2="v" 	k="-7" />
<hkern g1="r" 	g2="w" 	k="-5" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-7" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-24" />
<hkern g1="r" 	g2="backslash" 	k="-4" />
<hkern g1="r" 	g2="registered" 	k="-32" />
<hkern g1="r" 	g2="J" 	k="73" />
<hkern g1="r" 	g2="Z" 	k="10" />
<hkern g1="r" 	g2="g" 	k="10" />
<hkern g1="r" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="r" 	g2="hyphen,uni00AD,endash,emdash" 	k="28" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="r" 	g2="z" 	k="8" />
<hkern g1="r" 	g2="slash" 	k="24" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="18" />
<hkern g1="R" 	g2="T" 	k="18" />
<hkern g1="R" 	g2="V" 	k="13" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="15" />
<hkern g1="R" 	g2="asterisk" 	k="10" />
<hkern g1="R" 	g2="periodcentered" 	k="10" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="8" />
<hkern g1="R" 	g2="x" 	k="16" />
<hkern g1="R" 	g2="X" 	k="8" />
<hkern g1="R" 	g2="registered" 	k="-7" />
<hkern g1="R" 	g2="J" 	k="12" />
<hkern g1="R" 	g2="S" 	k="14" />
<hkern g1="R" 	g2="Z" 	k="10" />
<hkern g1="R" 	g2="g" 	k="8" />
<hkern g1="R" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="R" 	g2="hyphen,uni00AD,endash,emdash" 	k="30" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="30" />
<hkern g1="R" 	g2="z" 	k="16" />
<hkern g1="R" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="8" />
<hkern g1="s" 	g2="t" 	k="20" />
<hkern g1="s" 	g2="T" 	k="28" />
<hkern g1="s" 	g2="V" 	k="10" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="s" 	g2="question" 	k="25" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="25" />
<hkern g1="s" 	g2="asterisk" 	k="28" />
<hkern g1="s" 	g2="hyphen,uni00AD,endash,emdash" 	k="-10" />
<hkern g1="S" 	g2="t" 	k="28" />
<hkern g1="S" 	g2="T" 	k="20" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="25" />
<hkern g1="S" 	g2="asterisk" 	k="12" />
<hkern g1="S" 	g2="periodcentered" 	k="10" />
<hkern g1="S" 	g2="registered" 	k="6" />
<hkern g1="S" 	g2="J" 	k="18" />
<hkern g1="S" 	g2="S" 	k="18" />
<hkern g1="S" 	g2="hyphen,uni00AD,endash,emdash" 	k="-18" />
<hkern g1="S" 	g2="z" 	k="14" />
<hkern g1="Thorn" 	g2="asterisk" 	k="39" />
<hkern g1="Thorn" 	g2="backslash" 	k="40" />
<hkern g1="Thorn" 	g2="trademark" 	k="28" />
<hkern g1="Thorn" 	g2="slash" 	k="31" />
<hkern g1="t" 	g2="t" 	k="20" />
<hkern g1="t" 	g2="T" 	k="18" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="8" />
<hkern g1="t" 	g2="question" 	k="36" />
<hkern g1="t" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="13" />
<hkern g1="t" 	g2="periodcentered" 	k="20" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="t" 	g2="s" 	k="10" />
<hkern g1="t" 	g2="x" 	k="12" />
<hkern g1="t" 	g2="registered" 	k="-24" />
<hkern g1="t" 	g2="g" 	k="10" />
<hkern g1="t" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="t" 	g2="hyphen,uni00AD,endash,emdash" 	k="36" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="t" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="t" 	g2="slash" 	k="4" />
<hkern g1="T" 	g2="t" 	k="12" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="116" />
<hkern g1="T" 	g2="periodcentered" 	k="68" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="64" />
<hkern g1="T" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="12" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="28" />
<hkern g1="T" 	g2="s" 	k="60" />
<hkern g1="T" 	g2="v" 	k="24" />
<hkern g1="T" 	g2="w" 	k="24" />
<hkern g1="T" 	g2="x" 	k="25" />
<hkern g1="T" 	g2="X" 	k="20" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="24" />
<hkern g1="T" 	g2="registered" 	k="-20" />
<hkern g1="T" 	g2="trademark" 	k="-24" />
<hkern g1="T" 	g2="J" 	k="122" />
<hkern g1="T" 	g2="S" 	k="25" />
<hkern g1="T" 	g2="Z" 	k="44" />
<hkern g1="T" 	g2="g" 	k="64" />
<hkern g1="T" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="76" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="64" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="56" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="42" />
<hkern g1="T" 	g2="z" 	k="64" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="40" />
<hkern g1="T" 	g2="slash" 	k="68" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="T" 	g2="m,n,p,r,ntilde,dotlessi" 	k="42" />
<hkern g1="T" 	g2="AE" 	k="67" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,dotlessi" 	g2="T" 	k="20" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,dotlessi" 	g2="V" 	k="18" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,dotlessi" 	g2="Y,Yacute,Ydieresis" 	k="30" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,dotlessi" 	g2="asterisk" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="8" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="26" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="8" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="8" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="56" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="S" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="12" />
<hkern g1="v" 	g2="T" 	k="20" />
<hkern g1="v" 	g2="V" 	k="10" />
<hkern g1="v" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="v" 	g2="asterisk" 	k="10" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="60" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="v" 	g2="registered" 	k="-27" />
<hkern g1="v" 	g2="trademark" 	k="5" />
<hkern g1="v" 	g2="J" 	k="40" />
<hkern g1="v" 	g2="Z" 	k="10" />
<hkern g1="v" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="v" 	g2="hyphen,uni00AD,endash,emdash" 	k="16" />
<hkern g1="v" 	g2="z" 	k="28" />
<hkern g1="v" 	g2="j" 	k="10" />
<hkern g1="v" 	g2="slash" 	k="20" />
<hkern g1="v" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="13" />
<hkern g1="V" 	g2="V" 	k="-10" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-4" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="76" />
<hkern g1="V" 	g2="periodcentered" 	k="3" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="V" 	g2="s" 	k="18" />
<hkern g1="V" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="V" 	g2="v" 	k="10" />
<hkern g1="V" 	g2="w" 	k="10" />
<hkern g1="V" 	g2="x" 	k="19" />
<hkern g1="V" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="V" 	g2="registered" 	k="-51" />
<hkern g1="V" 	g2="trademark" 	k="-58" />
<hkern g1="V" 	g2="J" 	k="64" />
<hkern g1="V" 	g2="S" 	k="10" />
<hkern g1="V" 	g2="Z" 	k="20" />
<hkern g1="V" 	g2="g" 	k="20" />
<hkern g1="V" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="19" />
<hkern g1="V" 	g2="hyphen,uni00AD,endash,emdash" 	k="20" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="30" />
<hkern g1="V" 	g2="z" 	k="21" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="27" />
<hkern g1="V" 	g2="slash" 	k="27" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="18" />
<hkern g1="V" 	g2="m,n,p,r,ntilde,dotlessi" 	k="20" />
<hkern g1="w" 	g2="T" 	k="28" />
<hkern g1="w" 	g2="V" 	k="10" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="28" />
<hkern g1="w" 	g2="asterisk" 	k="10" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="83" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="w" 	g2="X" 	k="18" />
<hkern g1="w" 	g2="registered" 	k="-11" />
<hkern g1="w" 	g2="trademark" 	k="4" />
<hkern g1="w" 	g2="J" 	k="30" />
<hkern g1="w" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="w" 	g2="z" 	k="26" />
<hkern g1="w" 	g2="j" 	k="10" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="8" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-4" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="38" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="12" />
<hkern g1="W" 	g2="registered" 	k="-25" />
<hkern g1="W" 	g2="trademark" 	k="-38" />
<hkern g1="W" 	g2="J" 	k="47" />
<hkern g1="W" 	g2="g" 	k="4" />
<hkern g1="W" 	g2="hyphen,uni00AD,endash,emdash" 	k="10" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="W" 	g2="z" 	k="10" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="26" />
<hkern g1="W" 	g2="slash" 	k="8" />
<hkern g1="x" 	g2="t" 	k="28" />
<hkern g1="x" 	g2="T" 	k="24" />
<hkern g1="x" 	g2="V" 	k="19" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="38" />
<hkern g1="x" 	g2="asterisk" 	k="21" />
<hkern g1="x" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="16" />
<hkern g1="x" 	g2="periodcentered" 	k="20" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="x" 	g2="X" 	k="10" />
<hkern g1="x" 	g2="y,yacute,ydieresis" 	k="15" />
<hkern g1="x" 	g2="registered" 	k="-11" />
<hkern g1="x" 	g2="trademark" 	k="5" />
<hkern g1="x" 	g2="S" 	k="8" />
<hkern g1="x" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="26" />
<hkern g1="x" 	g2="hyphen,uni00AD,endash,emdash" 	k="18" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="28" />
<hkern g1="x" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="x" 	g2="exclam" 	k="18" />
<hkern g1="X" 	g2="t" 	k="26" />
<hkern g1="X" 	g2="T" 	k="20" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="X" 	g2="asterisk" 	k="10" />
<hkern g1="X" 	g2="periodcentered" 	k="40" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="X" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="26" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="26" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="X" 	g2="v" 	k="26" />
<hkern g1="X" 	g2="w" 	k="18" />
<hkern g1="X" 	g2="x" 	k="10" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="26" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="X" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="X" 	g2="registered" 	k="12" />
<hkern g1="X" 	g2="trademark" 	k="-20" />
<hkern g1="X" 	g2="J" 	k="15" />
<hkern g1="X" 	g2="S" 	k="18" />
<hkern g1="X" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="X" 	g2="hyphen,uni00AD,endash,emdash" 	k="36" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="X" 	g2="z" 	k="18" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="V" 	k="8" />
<hkern g1="y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="60" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="x" 	k="18" />
<hkern g1="y,yacute,ydieresis" 	g2="X" 	k="8" />
<hkern g1="y,yacute,ydieresis" 	g2="registered" 	k="-27" />
<hkern g1="y,yacute,ydieresis" 	g2="trademark" 	k="-4" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="40" />
<hkern g1="y,yacute,ydieresis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="y,yacute,ydieresis" 	g2="z" 	k="28" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotright,guilsinglright" 	k="8" />
<hkern g1="y,yacute,ydieresis" 	g2="j" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="slash" 	k="4" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="5" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="36" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="112" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="periodcentered" 	k="56" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="55" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="28" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="38" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="registered" 	k="-24" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="trademark" 	k="-42" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="19" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="22" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="60" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="55" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="73" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="38" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="56" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="54" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde,dotlessi" 	k="40" />
<hkern g1="z" 	g2="T" 	k="22" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="15" />
<hkern g1="z" 	g2="periodcentered" 	k="10" />
<hkern g1="z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="z" 	g2="v" 	k="8" />
<hkern g1="z" 	g2="y,yacute,ydieresis" 	k="8" />
<hkern g1="z" 	g2="registered" 	k="-7" />
<hkern g1="z" 	g2="trademark" 	k="-4" />
<hkern g1="z" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="z" 	g2="hyphen,uni00AD,endash,emdash" 	k="26" />
<hkern g1="z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="Z" 	g2="t" 	k="18" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="Z" 	g2="periodcentered" 	k="60" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="12" />
<hkern g1="Z" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="20" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="Z" 	g2="v" 	k="12" />
<hkern g1="Z" 	g2="w" 	k="12" />
<hkern g1="Z" 	g2="x" 	k="28" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="12" />
<hkern g1="Z" 	g2="registered" 	k="-20" />
<hkern g1="Z" 	g2="trademark" 	k="-20" />
<hkern g1="Z" 	g2="J" 	k="24" />
<hkern g1="Z" 	g2="S" 	k="30" />
<hkern g1="Z" 	g2="Z" 	k="12" />
<hkern g1="Z" 	g2="g" 	k="11" />
<hkern g1="Z" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="13" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="24" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="24" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="uni03BC" 	g2="colon,semicolon" 	k="-16" />
<hkern g1="uni03BC" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-16" />
<hkern g1="uni03BC" 	g2="quoteright,quotedblright" 	k="36" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="j" 	k="-80" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="J" 	k="20" />
<hkern g1="colon,semicolon" 	g2="j" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="36" />
<hkern g1="colon,semicolon" 	g2="asterisk" 	k="28" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-36" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="112" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk" 	k="168" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="t" 	k="63" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="100" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="27" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="60" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="76" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="38" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="125" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="145" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="128" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="J" 	k="20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="76" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="t" 	k="22" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="56" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="v" 	k="16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="V" 	k="20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="W" 	k="10" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="S" 	k="26" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="x" 	k="18" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="X" 	k="36" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Z" 	k="20" />
<hkern g1="exclam" 	g2="quoteright,quotedblright" 	k="52" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="54" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t" 	k="18" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="40" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="v" 	k="8" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="26" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="26" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="y,yacute,ydieresis" 	k="8" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="x" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="18" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="30" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="73" />
<hkern g1="guillemotright,guilsinglright" 	g2="t" 	k="24" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="56" />
<hkern g1="guillemotright,guilsinglright" 	g2="v" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="30" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="28" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="18" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="12" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="35" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="93" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="5" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="24" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-4" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-4" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="18" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="16" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="67" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="34" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="59" />
<hkern g1="quoteleft,quotedblleft" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="18" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="24" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="124" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclamdown" 	k="44" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="124" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="109" />
<hkern g1="quoteright,quotedblright" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="67" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="47" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="81" />
<hkern g1="quoteright,quotedblright" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="12" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="27" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="35" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="152" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde,dotlessi" 	k="4" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="22" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="113" />
<hkern g1="quotedbl,quotesingle" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="15" />
<hkern g1="quotedbl,quotesingle" 	g2="S" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="X" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="73" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="133" />
</font>
</defs></svg> 