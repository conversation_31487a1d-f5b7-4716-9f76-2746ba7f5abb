<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="source_sans_proregular" horiz-adv-x="497" >
<font-face units-per-em="1000" ascent="750" descent="-250" />
<missing-glyph horiz-adv-x="200" />
<glyph unicode="&#xfb00;" horiz-adv-x="577" d="M381 0v419h-203v-419h-82v419h-66v62l66 5v64q0 76 37.5 120t113.5 44q24 0 47.5 -5t41.5 -13l-17 -62q-30 13 -65 13q-36 0 -56 -25.5t-20 -73.5v-62h203v77q0 75 34.5 118t107.5 43q23 0 43.5 -4.5t37.5 -11.5l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103 v-419h-82z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="333" />
<glyph unicode=" "  horiz-adv-x="200" />
<glyph unicode="&#x09;" horiz-adv-x="200" />
<glyph unicode="&#xa0;" horiz-adv-x="200" />
<glyph unicode="!" horiz-adv-x="289" d="M116 198l-11 378l-2 94h83l-2 -94l-11 -378h-57zM145 -12q-25 0 -42.5 17.5t-17.5 44.5q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5z" />
<glyph unicode="&#x22;" horiz-adv-x="425" d="M99 431l-16 167l-3 92h88l-3 -92l-16 -167h-50zM275 431l-16 167l-3 92h88l-3 -92l-16 -167h-50z" />
<glyph unicode="#" d="M90 0l25 204h-80v57h87l18 148h-85v58h92l23 183h53l-23 -183h133l24 183h53l-24 -183h81v-58h-87l-18 -148h85v-57h-92l-25 -204h-53l24 204h-132l-25 -204h-54zM176 261h132l18 148h-132z" />
<glyph unicode="$" d="M222 -110v99q-48 5 -93 26.5t-77 50.5l38 57q32 -28 70.5 -47.5t84.5 -19.5q56 0 83.5 28t27.5 75q0 40 -20.5 66.5t-51.5 46t-67.5 36.5t-67.5 39t-51.5 53.5t-20.5 80.5q0 67 39.5 111.5t105.5 54.5v101h60v-99q48 -5 81.5 -26t60.5 -50l-44 -49q-28 27 -54.5 42 t-67.5 15q-46 0 -73 -26t-27 -70q0 -36 20.5 -59t51.5 -40.5t67.5 -34t67.5 -40t51.5 -58t20.5 -88.5q0 -72 -41.5 -118t-113.5 -56v-101h-60z" />
<glyph unicode="%" horiz-adv-x="824" d="M184 254q-68 0 -108.5 54t-40.5 154q0 99 40.5 152.5t108.5 53.5q67 0 108 -53.5t41 -152.5q0 -100 -41 -154t-108 -54zM184 305q38 0 62 39.5t24 117.5t-24 116.5t-62 38.5q-39 0 -63 -38.5t-24 -116.5t24 -117.5t63 -39.5zM202 -12l362 680h56l-362 -680h-56zM641 -12 q-68 0 -108.5 54t-40.5 154q0 99 40.5 152.5t108.5 53.5q67 0 108 -53.5t41 -152.5q0 -100 -41 -154t-108 -54zM641 39q38 0 62 39.5t24 117.5t-24 116.5t-62 38.5q-39 0 -63 -38.5t-24 -116.5t24 -117.5t63 -39.5z" />
<glyph unicode="&#x26;" horiz-adv-x="609" d="M232 -12q-45 0 -81.5 13.5t-63 37.5t-41 57.5t-14.5 73.5q0 33 10.5 60t28.5 50t41.5 42.5t48.5 36.5q-20 41 -31.5 79.5t-11.5 74.5q0 33 11 61t31 49t48 33t62 12q61 0 95 -36t34 -94q0 -32 -13 -59t-34 -50.5t-47.5 -44t-53.5 -40.5q32 -51 75.5 -98t90.5 -86 q30 41 53.5 89t38.5 105h77q-20 -65 -48 -124t-67 -111q34 -23 65 -39t58 -24l-22 -68q-35 10 -73 28.5t-78 46.5q-38 -34 -84.5 -54.5t-104.5 -20.5zM189 514q0 -27 8.5 -56t22.5 -59q22 15 42.5 30.5t36 32t24.5 35t9 40.5q0 29 -14.5 50.5t-47.5 21.5q-37 0 -59 -27 t-22 -68zM241 54q34 0 65 14t60 39q-48 42 -92.5 91.5t-79.5 103.5q-35 -28 -58.5 -58.5t-23.5 -68.5q0 -27 10 -49.5t27.5 -38.5t41 -24.5t50.5 -8.5z" />
<glyph unicode="'" horiz-adv-x="249" d="M99 431l-16 167l-3 92h88l-3 -92l-16 -167h-50z" />
<glyph unicode="(" horiz-adv-x="303" d="M214 -176q-62 100 -97 211t-35 243t35 242.5t97 211.5l51 -24q-58 -96 -86.5 -205.5t-28.5 -224.5t28.5 -224.5t86.5 -205.5z" />
<glyph unicode=")" horiz-adv-x="303" d="M89 -176l-51 24q58 96 86.5 205.5t28.5 224.5t-28.5 224.5t-86.5 205.5l51 24q62 -101 97 -211.5t35 -242.5t-35 -243t-97 -211z" />
<glyph unicode="*" horiz-adv-x="418" d="M138 420l-40 29l57 94l-97 40l15 46l102 -25l9 108h49l9 -107l103 24l15 -46l-97 -40l56 -94l-39 -29l-71 86z" />
<glyph unicode="+" d="M216 104v195h-182v62h182v195h65v-195h182v-62h-182v-195h-65z" />
<glyph unicode="," horiz-adv-x="249" d="M67 -170l-20 48q43 19 67 50.5t23 71.5q-3 -1 -10 -1q-24 0 -41.5 14.5t-17.5 42.5q0 27 18 42.5t43 15.5q32 0 50.5 -26t18.5 -71q0 -65 -35 -113t-96 -74z" />
<glyph unicode="-" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="." horiz-adv-x="249" d="M125 -12q-25 0 -42.5 17.5t-17.5 44.5q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5z" />
<glyph unicode="/" horiz-adv-x="350" d="M10 -160l267 870h60l-267 -870h-60z" />
<glyph unicode="0" d="M249 -12q-97 0 -151 86t-54 247t54 245t151 84q96 0 150 -84t54 -245t-54 -247t-150 -86zM249 54q28 0 50.5 15.5t39 48t25.5 83t9 120.5t-9 120t-25.5 81.5t-39 46.5t-50.5 15t-51 -15t-39.5 -46.5t-25.5 -81.5t-9 -120q0 -140 34.5 -203.5t90.5 -63.5z" />
<glyph unicode="1" d="M79 0v68h146v470h-116v53q44 8 76.5 19.5t58.5 27.5h63v-570h132v-68h-360z" />
<glyph unicode="2" d="M40 0v49q72 72 128 130t94 107.5t58 91t20 80.5q0 55 -30 90t-91 35q-40 0 -74 -22.5t-62 -54.5l-47 47q40 44 85 70.5t108 26.5q89 0 140 -51.5t51 -136.5q0 -45 -19.5 -90.5t-54 -94t-82 -101t-104.5 -111.5q26 2 54 4t53 2h185v-71h-412z" />
<glyph unicode="3" d="M236 -12q-38 0 -69.5 7.5t-57.5 20t-46.5 28.5t-36.5 34l42 54q29 -30 67.5 -53t95.5 -23q58 0 95 31.5t37 85.5q0 28 -10.5 51.5t-34.5 40.5t-63 26t-96 9v63q51 0 85.5 9t56 25t30.5 38t9 47q0 47 -29.5 74t-80.5 27q-40 0 -73.5 -18t-62.5 -47l-44 52q37 35 81.5 57.5 t101.5 22.5q42 0 77 -11t60.5 -31.5t39.5 -50.5t14 -69q0 -58 -32 -95t-84 -57v-4q29 -7 54 -20.5t44 -34t29.5 -47.5t10.5 -60q0 -42 -16.5 -76t-45 -57.5t-66.5 -36t-82 -12.5z" />
<glyph unicode="4" d="M304 0v176h-287v54l273 408h92v-396h87v-66h-87v-176h-78zM104 242h200v185q0 26 1.5 61.5t3.5 61.5h-4q-12 -23 -25 -45t-27 -45z" />
<glyph unicode="5" d="M234 -12q-38 0 -69.5 7.5t-57 19.5t-46 27.5t-36.5 32.5l40 54q28 -29 66.5 -51t94.5 -22q29 0 54.5 10.5t44.5 29.5t30 46t11 60q0 66 -37 103t-99 37q-33 0 -56.5 -10t-52.5 -29l-44 28l21 307h319v-71h-247l-17 -189q23 12 46 19t52 7q41 0 77 -12t63 -36.5t42.5 -62 t15.5 -89.5t-18 -92t-48 -67.5t-68.5 -42t-80.5 -14.5z" />
<glyph unicode="6" d="M268 -12q-47 0 -87 19t-69.5 57.5t-46.5 96t-17 134.5q0 96 20 163.5t54 110t77.5 62t91.5 19.5q52 0 89.5 -19.5t64.5 -48.5l-46 -51q-20 23 -47.5 36.5t-57.5 13.5q-33 0 -63 -14t-53 -46t-37 -83.5t-15 -127.5q30 37 70 58.5t79 21.5q83 0 132.5 -49t49.5 -148 q0 -46 -15 -83.5t-41 -64.5t-60 -42t-73 -15zM268 53q24 0 44 10t35 28.5t23.5 44t8.5 57.5q0 63 -29 99t-89 36q-31 0 -67 -19.5t-66 -64.5q8 -94 43.5 -142.5t96.5 -48.5z" />
<glyph unicode="7" d="M177 0q4 90 16 165t33.5 142.5t55 130.5t80.5 129h-318v71h411v-51q-57 -72 -92.5 -138t-56.5 -134.5t-30.5 -145t-13.5 -169.5h-85z" />
<glyph unicode="8" d="M250 -12q-45 0 -83.5 13t-66 36.5t-43.5 55.5t-16 70q0 31 10.5 57.5t27 48t38 38t44.5 28.5v4q-35 25 -62 60.5t-27 86.5q0 37 14 67t38 51.5t57 33.5t72 12q42 0 75 -12.5t56.5 -35t36 -54t12.5 -68.5q0 -25 -8.5 -48t-21 -43t-28 -36t-30.5 -27v-4q21 -12 41 -27.5 t35.5 -35t25 -44.5t9.5 -57q0 -36 -15 -67t-42 -54t-65 -36t-84 -13zM295 348q32 29 49.5 61t17.5 67q0 48 -29 80.5t-82 32.5q-45 0 -74 -28t-29 -75q0 -29 12 -49.5t32.5 -36.5t47 -28.5t55.5 -23.5zM252 49q56 0 90.5 31t34.5 81q0 31 -14 53t-38 38.5t-55.5 30 t-65.5 27.5q-38 -26 -63.5 -60.5t-25.5 -78.5q0 -27 10.5 -49t29 -38.5t43.5 -25.5t54 -9z" />
<glyph unicode="9" d="M235 310q32 0 67.5 20t66.5 65q-8 94 -44 142t-97 48q-23 0 -43.5 -10t-35.5 -28.5t-23.5 -44.5t-8.5 -57q0 -63 29 -99t89 -36zM205 -12q-51 0 -89.5 19t-64.5 48l46 52q20 -23 47.5 -37t57.5 -14q34 0 64 14t53 46.5t37 84.5t15 129q-30 -38 -70 -60t-80 -22 q-83 0 -132 49t-49 148q0 46 15 83.5t40.5 64.5t60 42t72.5 15q47 0 87.5 -19t70 -57.5t46 -96t16.5 -134.5q0 -96 -20 -163.5t-54 -110t-77.5 -62t-91.5 -19.5z" />
<glyph unicode=":" horiz-adv-x="249" d="M125 349q-25 0 -42.5 17.5t-17.5 44.5q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5zM125 -12q-25 0 -42.5 17.5t-17.5 44.5q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5z" />
<glyph unicode=";" horiz-adv-x="249" d="M125 349q-25 0 -42.5 17.5t-17.5 44.5q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5zM67 -170l-20 48q43 19 67 50.5t23 71.5q-3 -1 -10 -1q-24 0 -41.5 14.5t-17.5 42.5q0 27 18 42.5t43 15.5q32 0 50.5 -26t18.5 -71 q0 -65 -35 -113t-96 -74z" />
<glyph unicode="&#x3c;" d="M463 131l-429 168v66l429 168v-71l-211 -78l-134 -50v-4l134 -50l211 -78v-71z" />
<glyph unicode="=" d="M34 406v62h429v-62h-429zM34 193v62h429v-62h-429z" />
<glyph unicode="&#x3e;" d="M34 131v71l211 78l134 50v4l-134 50l-211 78v71l429 -168v-66z" />
<glyph unicode="?" horiz-adv-x="425" d="M160 198q-6 39 2.5 70.5t23.5 58t34.5 49.5t36.5 45.5t28.5 45.5t11.5 50q0 40 -24.5 68.5t-72.5 28.5q-33 0 -62.5 -15.5t-52.5 -42.5l-47 43q32 36 74.5 59.5t97.5 23.5q76 0 121.5 -42.5t45.5 -117.5q0 -33 -12 -60t-29.5 -51t-37 -47.5t-36 -48.5t-25.5 -53.5 t-5 -63.5h-72zM198 -12q-25 0 -42 17.5t-17 44.5q0 29 17 46.5t42 17.5t42.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-42.5 -17.5z" />
<glyph unicode="@" horiz-adv-x="847" d="M403 -155q-74 0 -138 23t-111.5 68.5t-75 112.5t-27.5 154q0 100 33.5 182t91 140t133 89.5t159.5 31.5q76 0 136.5 -24t103 -67.5t65.5 -104.5t23 -135q0 -66 -18.5 -115.5t-47.5 -83t-64.5 -50.5t-69.5 -17q-41 0 -69.5 19t-33.5 57h-2q-25 -29 -57 -48.5t-65 -19.5 q-51 0 -85.5 36t-34.5 105q0 40 13.5 81.5t38.5 75t60.5 54.5t79.5 21q26 0 46 -11.5t34 -36.5h2l11 40h55l-39 -200q-30 -117 54 -117q24 0 47.5 14t43 40.5t31.5 65t12 87.5q0 60 -17.5 111.5t-52.5 88.5t-87 58t-120 21q-67 0 -130 -27.5t-112 -77.5t-78.5 -121 t-29.5 -159q0 -73 22.5 -130t62.5 -96t94.5 -59.5t118.5 -20.5q46 0 87 12.5t75 32.5l22 -49q-85 -51 -190 -51zM385 113q21 0 43.5 14t48.5 45l29 159q-14 23 -29 32.5t-35 9.5q-30 0 -53.5 -16t-40 -41t-25 -55t-8.5 -58q0 -48 20 -69t50 -21z" />
<glyph unicode="A" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85z" />
<glyph unicode="B" horiz-adv-x="588" d="M90 0v656h195q50 0 91.5 -9t71 -28t46 -49.5t16.5 -73.5q0 -49 -24.5 -88.5t-71.5 -54.5v-4q60 -11 97 -50t37 -107q0 -48 -18 -84t-51 -60t-79 -36t-101 -12h-209zM173 377h97q84 0 121 29t37 79q0 57 -38.5 81t-115.5 24h-101v-213zM173 66h114q85 0 132 31.5t47 97.5 q0 61 -46 89.5t-133 28.5h-114v-247z" />
<glyph unicode="C" horiz-adv-x="571" d="M338 -12q-62 0 -114 23t-90.5 66.5t-60 107t-21.5 143.5q0 79 22 142t61 107t92 67.5t116 23.5q60 0 105.5 -24.5t74.5 -56.5l-45 -54q-26 28 -59 45t-75 17q-47 0 -85 -18.5t-65 -53t-41.5 -83.5t-14.5 -110q0 -62 14.5 -111.5t40.5 -84.5t63.5 -54t84.5 -19 q48 0 84 19.5t68 54.5l46 -52q-39 -45 -88 -70t-113 -25z" />
<glyph unicode="D" horiz-adv-x="615" d="M90 0v656h164q152 0 231 -84t79 -241q0 -78 -20 -139.5t-59 -104t-96 -65t-131 -22.5h-168zM173 68h75q115 0 172.5 69t57.5 194t-57.5 191t-172.5 66h-75v-520z" />
<glyph unicode="E" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388z" />
<glyph unicode="F" horiz-adv-x="494" d="M90 0v656h378v-70h-295v-222h250v-70h-250v-294h-83z" />
<glyph unicode="G" horiz-adv-x="617" d="M348 -12q-65 0 -119.5 23t-93.5 66.5t-61 107t-22 143.5q0 79 22.5 142t62.5 107t95.5 67.5t120.5 23.5q34 0 62.5 -7t52 -18.5t42 -26t32.5 -29.5l-46 -54q-25 26 -58 44t-83 18t-90 -18.5t-68 -53t-43.5 -83.5t-15.5 -110q0 -62 14.5 -111.5t42 -84.5t68 -54t93.5 -19 q35 0 66.5 10.5t51.5 28.5v171h-139v69h215v-276q-32 -33 -84 -54.5t-118 -21.5z" />
<glyph unicode="H" horiz-adv-x="652" d="M90 0v656h83v-275h305v275h84v-656h-84v309h-305v-309h-83z" />
<glyph unicode="I" horiz-adv-x="263" d="M90 0v656h83v-656h-83z" />
<glyph unicode="J" horiz-adv-x="480" d="M212 -12q-123 0 -181 105l60 42q22 -39 50 -56.5t63 -17.5q53 0 79 32.5t26 107.5v455h84v-463q0 -42 -10.5 -79.5t-32 -65.5t-56 -44t-82.5 -16z" />
<glyph unicode="K" horiz-adv-x="579" d="M90 0v656h83v-329h3l273 329h94l-205 -250l237 -406h-93l-196 341l-113 -133v-208h-83z" />
<glyph unicode="L" horiz-adv-x="486" d="M90 0v656h83v-585h287v-71h-370z" />
<glyph unicode="M" horiz-adv-x="727" d="M90 0v656h100l126 -350q12 -34 23.5 -68.5t23.5 -68.5h4q12 34 22.5 68.5t22.5 68.5l124 350h101v-656h-78v361q0 44 4 97t7 97h-4l-52 -149l-124 -340h-55l-124 340l-52 149h-4q3 -44 6.5 -97t3.5 -97v-361h-75z" />
<glyph unicode="N" horiz-adv-x="647" d="M90 0v656h86l237 -412l71 -136h4q-3 50 -6.5 103.5t-3.5 105.5v339h79v-656h-86l-238 413l-71 135h-4q4 -50 7.5 -101.5t3.5 -103.5v-343h-79z" />
<glyph unicode="O" horiz-adv-x="664" d="M332 -12q-62 0 -113.5 24t-88.5 68.5t-57.5 108t-20.5 142.5t20.5 141.5t57.5 106t88.5 66.5t113.5 23t113.5 -23.5t89 -67t58 -106t20.5 -140.5q0 -79 -20.5 -142.5t-58 -108t-89 -68.5t-113.5 -24zM332 61q44 0 79.5 19t61 54.5t39.5 85t14 111.5q0 61 -14 110 t-39.5 83t-61 52.5t-79.5 18.5t-79.5 -18.5t-61 -52.5t-39.5 -83t-14 -110q0 -62 14 -111.5t39.5 -85t61 -54.5t79.5 -19z" />
<glyph unicode="P" horiz-adv-x="566" d="M90 0v656h187q55 0 100.5 -10t77.5 -32.5t50 -59.5t18 -91q0 -52 -17.5 -90t-49.5 -63t-76.5 -37.5t-98.5 -12.5h-108v-260h-83zM173 328h98q86 0 127.5 32.5t41.5 102.5q0 71 -43.5 98.5t-129.5 27.5h-94v-261z" />
<glyph unicode="Q" horiz-adv-x="664" d="M332 57q44 0 79.5 19t61 55t39.5 86.5t14 113.5q0 61 -14 110t-39.5 83t-61 52.5t-79.5 18.5t-79.5 -18.5t-61 -52.5t-39.5 -83t-14 -110q0 -63 14 -113.5t39.5 -86.5t61 -55t79.5 -19zM533 -165q-45 0 -83.5 11.5t-69 32.5t-53 49.5t-36.5 62.5q-54 7 -98 34t-75.5 71 t-48.5 103.5t-17 131.5q0 79 20.5 141.5t57.5 106t88.5 66.5t113.5 23t113.5 -23.5t89 -67t58 -106t20.5 -140.5q0 -71 -16.5 -129.5t-47 -102t-73.5 -71t-95 -36.5q23 -44 65 -65.5t96 -21.5q22 0 38.5 2.5t30.5 6.5l16 -64q-15 -6 -40 -10.5t-54 -4.5z" />
<glyph unicode="R" horiz-adv-x="569" d="M90 0v656h205q50 0 92.5 -9.5t73 -31.5t47.5 -57t17 -86q0 -77 -40 -123t-108 -63l167 -286h-94l-158 277h-119v-277h-83zM173 345h110q77 0 118 31.5t41 95.5q0 65 -41 91t-118 26h-110v-244z" />
<glyph unicode="S" horiz-adv-x="534" d="M272 -12q-69 0 -128 26t-102 71l50 58q35 -37 82.5 -59.5t98.5 -22.5q65 0 101 29.5t36 77.5q0 25 -8.5 42.5t-23 30t-34.5 22.5t-43 21l-94 41q-23 10 -47 24t-43 34t-31 47.5t-12 63.5q0 37 15.5 69t43 55.5t65 36.5t82.5 13q59 0 109 -22.5t85 -58.5l-45 -54 q-30 29 -66.5 45.5t-82.5 16.5q-55 0 -88.5 -25.5t-33.5 -70.5q0 -24 9.5 -40.5t25.5 -29t35 -22t39 -17.5l93 -40q28 -12 53 -27.5t43 -36t28.5 -48t10.5 -63.5q0 -39 -15.5 -73t-44.5 -59.5t-70 -40t-93 -14.5z" />
<glyph unicode="T" horiz-adv-x="536" d="M226 0v586h-198v70h480v-70h-198v-586h-84z" />
<glyph unicode="U" horiz-adv-x="645" d="M323 -12q-50 0 -93.5 14.5t-75 48t-49.5 87.5t-18 133v385h83v-387q0 -59 12 -99t32.5 -64t48.5 -34.5t60 -10.5q33 0 61 10.5t49 34.5t33 64t12 99v387h80v-385q0 -79 -18 -133t-49.5 -87.5t-74.5 -48t-93 -14.5z" />
<glyph unicode="V" horiz-adv-x="515" d="M210 0l-210 656h89l105 -354q18 -59 31.5 -109t32.5 -108h4q18 58 32 108t31 109l105 354h85l-208 -656h-97z" />
<glyph unicode="W" horiz-adv-x="786" d="M162 0l-139 656h86l69 -357q9 -54 19 -106t19 -106h4q11 54 23 106.5t23 105.5l91 357h76l91 -357q12 -52 24 -105t24 -107h4q9 54 18 106.5t19 105.5l69 357h80l-136 -656h-100l-99 395q-9 38 -16.5 74.5t-15.5 74.5h-4q-8 -38 -16.5 -74.5t-16.5 -74.5l-97 -395h-99z " />
<glyph unicode="X" horiz-adv-x="513" d="M15 0l191 339l-178 317h92l89 -168q13 -23 24.5 -44.5t26.5 -50.5h4q14 29 24.5 50.5t22.5 44.5l87 168h88l-179 -321l191 -335h-92l-96 177q-13 24 -26.5 49.5t-29.5 55.5h-4q-14 -30 -27 -55.5t-25 -49.5l-95 -177h-88z" />
<glyph unicode="Y" horiz-adv-x="476" d="M196 0v254l-197 402h89l85 -185q16 -36 31 -71t32 -72h4q17 37 34 72t32 71l84 185h87l-197 -402v-254h-84z" />
<glyph unicode="Z" horiz-adv-x="539" d="M45 0v50l345 536h-314v70h418v-49l-346 -536h349v-71h-452z" />
<glyph unicode="[" horiz-adv-x="303" d="M94 -152v860h179v-47h-117v-766h117v-47h-179z" />
<glyph unicode="\" horiz-adv-x="350" d="M281 -160l-267 870h59l267 -870h-59z" />
<glyph unicode="]" horiz-adv-x="303" d="M31 -152v47h116v766h-116v47h178v-860h-178z" />
<glyph unicode="^" d="M60 284l152 386h73l152 -386h-72l-65 176l-49 133h-4l-50 -133l-65 -176h-72z" />
<glyph unicode="_" horiz-adv-x="500" d="M12 -126v55h476v-55h-476z" />
<glyph unicode="`" horiz-adv-x="542" d="M285 573l-157 153l58 55l141 -167z" />
<glyph unicode="a" horiz-adv-x="504" d="M194 -12q-61 0 -101.5 36t-40.5 102q0 80 71 122.5t227 59.5q0 23 -4.5 45t-15.5 39t-30.5 27.5t-49.5 10.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83 42.5t108 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-35 -29 -75 -49.5t-86 -20.5zM218 54q35 0 66 16.5 t66 48.5v135q-61 -8 -102.5 -19t-67 -26t-37 -34.5t-11.5 -42.5q0 -42 25 -60t61 -18z" />
<glyph unicode="b" horiz-adv-x="553" d="M297 -12q-34 0 -70.5 16.5t-68.5 45.5h-3l-7 -50h-66v712h82v-194l-2 -88q33 29 72 48.5t80 19.5q47 0 83 -17.5t60.5 -50t37 -78t12.5 -101.5q0 -62 -17 -111t-46 -83t-67 -51.5t-80 -17.5zM283 57q30 0 55.5 13.5t44 38t29 60.5t10.5 81q0 40 -7 73t-22.5 56.5 t-40 36.5t-58.5 13q-59 0 -130 -66v-255q32 -28 63.5 -39.5t55.5 -11.5z" />
<glyph unicode="c" horiz-adv-x="456" d="M274 -12q-48 0 -90 17t-72.5 49.5t-48 80t-17.5 107.5q0 61 19 108.5t51 80.5t74.5 50t89.5 17q48 0 82 -17t60 -40l-42 -54q-21 19 -44.5 31t-52.5 12q-33 0 -61 -13.5t-48 -38.5t-31.5 -59.5t-11.5 -76.5t11 -76t30.5 -58.5t47.5 -38t61 -13.5q34 0 62.5 14.5 t51.5 34.5l36 -55q-33 -29 -73 -45.5t-84 -16.5z" />
<glyph unicode="d" horiz-adv-x="555" d="M248 -12q-92 0 -146.5 66t-54.5 188q0 59 17.5 106.5t46.5 80.5t67 51t80 18t73 -15t63 -41l-4 83v187h83v-712h-68l-7 57h-3q-29 -28 -66.5 -48.5t-80.5 -20.5zM266 57q34 0 64 16.5t60 50.5v254q-31 28 -59.5 39.5t-58.5 11.5q-29 0 -54.5 -13.5t-44.5 -38t-30 -58.5 t-11 -76q0 -88 35 -137t99 -49z" />
<glyph unicode="e" horiz-adv-x="496" d="M279 -12q-49 0 -91.5 17.5t-74 50t-49.5 79.5t-18 107t18.5 107.5t49 80.5t69.5 50.5t81 17.5q46 0 82.5 -16t61 -46t37.5 -72t13 -94q0 -13 -0.5 -25.5t-2.5 -21.5h-328q5 -78 48.5 -123.5t113.5 -45.5q35 0 64.5 10.5t56.5 27.5l29 -54q-32 -20 -71 -35t-89 -15z M126 282h260q0 74 -31.5 112.5t-88.5 38.5q-26 0 -49.5 -10t-42.5 -29.5t-31.5 -47.5t-16.5 -64z" />
<glyph unicode="f" horiz-adv-x="292" d="M96 0v419h-66v62l66 5v77q0 75 34.5 118t107.5 43q23 0 43.5 -4.5t37.5 -11.5l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82z" />
<glyph unicode="g" horiz-adv-x="504" d="M246 -224q-45 0 -82 8.5t-63.5 25.5t-41 41t-14.5 56q0 31 19 59t52 51v4q-18 11 -30.5 30.5t-12.5 48.5q0 31 17 54t36 36v4q-24 20 -43.5 53.5t-19.5 77.5q0 40 14.5 72t39.5 54.5t58.5 34.5t70.5 12q20 0 37.5 -3.5t31.5 -8.5h169v-63h-100q17 -17 28.5 -43t11.5 -57 q0 -39 -14 -70.5t-38 -53t-56.5 -33.5t-69.5 -12q-18 0 -37 4.5t-36 12.5q-13 -11 -22 -24.5t-9 -33.5q0 -23 18 -38t68 -15h94q85 0 127.5 -27.5t42.5 -88.5q0 -34 -17 -64.5t-49 -53.5t-77.5 -36.5t-102.5 -13.5zM246 209q21 0 39.5 8t33 23t22.5 36.5t8 48.5 q0 54 -30 83.5t-73 29.5t-73 -29.5t-30 -83.5q0 -27 8 -48.5t22.5 -36.5t33 -23t39.5 -8zM258 -167q35 0 63.5 8.5t48.5 22t31 31.5t11 37q0 34 -25 47t-73 13h-84q-14 0 -30.5 1.5t-32.5 6.5q-26 -19 -38 -40t-12 -42q0 -39 37.5 -62t103.5 -23z" />
<glyph unicode="h" horiz-adv-x="544" d="M82 0v712h82v-194l-3 -100q35 33 73 56.5t89 23.5q77 0 112.5 -48t35.5 -142v-308h-82v297q0 69 -22 99.5t-70 30.5q-38 0 -67 -19t-66 -56v-352h-82z" />
<glyph unicode="i" horiz-adv-x="246" d="M82 0v486h82v-486h-82zM124 586q-24 0 -40.5 15t-16.5 38q0 24 16.5 38.5t40.5 14.5t40.5 -14.5t16.5 -38.5q0 -23 -16.5 -38t-40.5 -15z" />
<glyph unicode="j" horiz-adv-x="247" d="M32 -217q-23 0 -41 4t-31 9l17 62q9 -3 21 -5.5t25 -2.5q36 0 48 25t12 70v541h82v-541q0 -74 -30 -118t-103 -44zM125 586q-24 0 -40.5 15t-16.5 38q0 24 16.5 38.5t40.5 14.5q23 0 39.5 -14.5t16.5 -38.5q0 -23 -16.5 -38t-39.5 -15z" />
<glyph unicode="k" horiz-adv-x="495" d="M82 0v712h81v-482h3l207 256h91l-163 -195l185 -291h-90l-142 234l-91 -106v-128h-81z" />
<glyph unicode="l" horiz-adv-x="255" d="M169 -12q-47 0 -67 28t-20 82v614h82v-620q0 -20 7 -28t16 -8h7.5t10.5 2l11 -62q-8 -4 -19 -6t-28 -2z" />
<glyph unicode="m" horiz-adv-x="829" d="M82 0v486h68l7 -70h3q32 35 69.5 58.5t81.5 23.5q56 0 87.5 -24.5t46.5 -68.5q38 42 76.5 67.5t83.5 25.5q75 0 111.5 -48t36.5 -142v-308h-82v297q0 69 -22 99.5t-68 30.5q-55 0 -122 -75v-352h-82v297q0 69 -22 99.5t-69 30.5q-55 0 -122 -75v-352h-82z" />
<glyph unicode="n" horiz-adv-x="547" d="M82 0v486h68l7 -70h3q35 35 73.5 58.5t89.5 23.5q77 0 112.5 -48t35.5 -142v-308h-82v297q0 69 -22 99.5t-70 30.5q-38 0 -67 -19t-66 -56v-352h-82z" />
<glyph unicode="o" horiz-adv-x="542" d="M271 -12q-45 0 -85.5 17t-71.5 49.5t-49.5 80t-18.5 107.5q0 61 18.5 108.5t49.5 80.5t71.5 50t85.5 17t85.5 -17t71.5 -50t49.5 -80.5t18.5 -108.5q0 -60 -18.5 -107.5t-49.5 -80t-71.5 -49.5t-85.5 -17zM271 56q31 0 57 13.5t44.5 38t28.5 58.5t10 76t-10 76.5 t-28.5 59.5t-44.5 38.5t-57 13.5t-57 -13.5t-44.5 -38.5t-28.5 -59.5t-10 -76.5t10 -76t28.5 -58.5t44.5 -38t57 -13.5z" />
<glyph unicode="p" horiz-adv-x="555" d="M82 -205v691h68l7 -56h3q33 28 72.5 48t82.5 20q47 0 83 -17.5t60 -50t36.5 -78t12.5 -102.5q0 -62 -17 -110.5t-46 -82.5t-67 -51.5t-80 -17.5q-34 0 -67.5 15t-67.5 41l2 -85v-164h-82zM283 57q30 0 55.5 13.5t44 38t29 60.5t10.5 81q0 40 -7 73t-22.5 56.5t-40 36.5 t-58.5 13q-31 0 -62.5 -17t-67.5 -49v-255q33 -28 64 -39.5t55 -11.5z" />
<glyph unicode="q" horiz-adv-x="555" d="M390 -205v173l4 88q-29 -29 -66.5 -48.5t-79.5 -19.5q-92 0 -146.5 66t-54.5 188q0 59 17.5 106.5t46.5 80.5t67 51t80 18t74 -14.5t65 -43.5h2l8 46h66v-691h-83zM266 57q34 0 64 16.5t60 50.5v254q-31 28 -59.5 39.5t-58.5 11.5q-29 0 -54.5 -13.5t-44.5 -38t-30 -58.5 t-11 -76q0 -88 35 -137t99 -49z" />
<glyph unicode="r" horiz-adv-x="347" d="M82 0v486h68l7 -88h3q25 46 60.5 73t77.5 27q29 0 52 -10l-16 -72q-12 4 -22 6t-25 2q-31 0 -64.5 -25t-58.5 -87v-312h-82z" />
<glyph unicode="s" horiz-adv-x="419" d="M209 -12q-52 0 -99 19t-82 48l41 55q32 -26 65.5 -42t77.5 -16q48 0 72 22t24 54q0 19 -10 33t-25.5 24.5t-35.5 18.5l-40 16q-26 9 -52 20.5t-46.5 28t-33.5 38.5t-13 53q0 29 11.5 54.5t33 44t52.5 29t70 10.5q46 0 84.5 -16t66.5 -39l-39 -52q-25 19 -52 31t-59 12 q-46 0 -67.5 -21t-21.5 -49q0 -17 9 -29.5t24 -22t34.5 -17t40.5 -15.5q26 -10 52.5 -21t47.5 -27.5t34.5 -40.5t13.5 -58q0 -30 -11.5 -56t-34 -46t-56 -31.5t-76.5 -11.5z" />
<glyph unicode="t" horiz-adv-x="338" d="M235 -12q-39 0 -65.5 12t-43 33t-23.5 51t-7 66v269h-72v62l76 5l10 136h69v-136h131v-67h-131v-270q0 -45 16.5 -69.5t58.5 -24.5q13 0 28 4t27 9l16 -62q-20 -7 -43.5 -12.5t-46.5 -5.5z" />
<glyph unicode="u" horiz-adv-x="544" d="M224 -12q-78 0 -113.5 48t-35.5 142v308h83v-297q0 -69 21.5 -99.5t69.5 -30.5q38 0 67 19.5t64 62.5v345h82v-486h-68l-7 76h-3q-34 -40 -71.5 -64t-88.5 -24z" />
<glyph unicode="v" horiz-adv-x="467" d="M187 0l-175 486h85l92 -276q11 -36 22.5 -72t22.5 -71h4q11 35 22 71l22 72l92 276h81l-172 -486h-96z" />
<glyph unicode="w" horiz-adv-x="718" d="M159 0l-135 486h84l72 -281q8 -35 15 -68t14 -67h4q8 34 16 67.5t17 67.5l75 281h80l76 -281q9 -35 17.5 -68t16.5 -67h4q8 34 15 67t15 68l71 281h78l-130 -486h-100l-70 261q-9 35 -16.5 69t-16.5 71h-4q-8 -37 -16 -71.5t-18 -69.5l-68 -260h-96z" />
<glyph unicode="x" horiz-adv-x="446" d="M14 0l159 254l-147 232h89l65 -107q11 -20 23 -40.5t25 -40.5h4q11 20 22 40.5t22 40.5l59 107h86l-147 -241l158 -245h-89l-71 113l-26 44t-27 43h-4q-13 -21 -25 -42.5t-24 -44.5l-66 -113h-86z" />
<glyph unicode="y" horiz-adv-x="467" d="M90 -209q-17 0 -31 2.5t-26 7.5l16 65l18 -4.5t19 -2.5q42 0 68.5 29.5t41.5 74.5l11 36l-195 487h85l99 -269q11 -32 23.5 -67.5t23.5 -69.5h4q11 33 21 69t20 68l87 269h80l-183 -526q-13 -36 -29 -67t-38 -53.5t-50 -35.5t-65 -13z" />
<glyph unicode="z" horiz-adv-x="425" d="M31 0v44l256 375h-228v67h332v-44l-256 -375h264v-67h-368z" />
<glyph unicode="{" horiz-adv-x="303" d="M228 -152q-59 0 -88 28t-29 105q0 27 1.5 51.5t3 47.5l3 46t1.5 48q0 15 -4 28.5t-13.5 24.5t-26.5 18t-42 7v52q25 0 42 7t26.5 17.5t13.5 24.5t4 28q0 51 -4.5 95t-4.5 99q0 77 29 105t88 28h45v-47h-27q-41 0 -54.5 -21t-13.5 -70q0 -46 3 -88t3 -93q0 -49 -14 -74.5 t-46 -34.5v-4q32 -9 46 -35t14 -74q0 -51 -3 -93t-3 -88q0 -49 13.5 -70t54.5 -21h27v-47h-45z" />
<glyph unicode="|" horiz-adv-x="241" d="M92 -250v1000h58v-1000h-58z" />
<glyph unicode="}" horiz-adv-x="303" d="M31 -152v47h26q41 0 54.5 21t13.5 70q0 46 -2.5 88t-2.5 93q0 48 13.5 74t45.5 35v4q-32 9 -45.5 34.5t-13.5 74.5q0 51 2.5 93t2.5 88q0 49 -13.5 70t-54.5 21h-26v47h44q30 0 52 -6.5t36.5 -21.5t21.5 -40.5t7 -64.5q0 -55 -4.5 -99t-4.5 -95q0 -29 18 -52.5t68 -24.5 v-52q-25 0 -41.5 -7t-26.5 -18t-14 -24.5t-4 -28.5q0 -25 1.5 -48l3 -46t3 -47.5t1.5 -51.5q0 -39 -7 -64.5t-21.5 -40.5t-36.5 -21.5t-52 -6.5h-44z" />
<glyph unicode="~" d="M336 257q-30 0 -53.5 13t-44 29t-39.5 29t-40 13q-22 0 -41 -14.5t-36 -46.5l-46 33q27 48 60 69t65 21q30 0 53.5 -13t44 -29t39.5 -29t40 -13q22 0 41 14.5t36 46.5l46 -34q-27 -48 -60 -68.5t-65 -20.5z" />
<glyph unicode="&#xa1;" horiz-adv-x="289" d="M103 -184l2 94l11 378h57l11 -378l2 -94h-83zM145 372q-25 0 -42.5 17.5t-17.5 46.5q0 27 17.5 44.5t42.5 17.5q24 0 41.5 -17.5t17.5 -44.5q0 -29 -17.5 -46.5t-41.5 -17.5z" />
<glyph unicode="&#xa2;" d="M143 310q0 -67 31 -111t88 -57v335q-55 -13 -87 -57t-32 -110zM262 -33v104q-45 5 -82 23.5t-63.5 48.5t-41 72t-14.5 95q0 52 15.5 93t42.5 71t64 48.5t79 24.5v106h52v-103q44 -2 76 -19t55 -39l-40 -52q-20 18 -42.5 29t-48.5 13v-344q32 2 58 15.5t46 31.5l36 -52 q-29 -26 -65 -43t-75 -20v-103h-52z" />
<glyph unicode="&#xa3;" d="M54 0v50q51 28 78.5 75.5t27.5 104.5q0 14 -2 27.5t-5 27.5h-100v52l67 4h18q-10 32 -18.5 62.5t-8.5 62.5q0 42 13.5 76t38 58t58.5 37t76 13q54 0 91.5 -21.5t63.5 -53.5l-48 -47q-19 23 -43 38t-58 15q-54 0 -82.5 -32.5t-28.5 -84.5q0 -32 7.5 -61.5t16.5 -61.5h159 v-56h-146q2 -14 3.5 -27.5t1.5 -28.5q0 -53 -16 -88t-46 -66v-4h281v-71h-399z" />
<glyph unicode="&#xa4;" d="M70 103l-44 45l64 65q-17 23 -26.5 52t-9.5 64t9.5 64.5t26.5 52.5l-64 66l44 45l68 -70q48 37 111 37q29 0 58 -9.5t52 -27.5l68 70l44 -45l-65 -66q17 -23 27 -52.5t10 -64.5t-10 -64t-27 -52l65 -65l-44 -45l-68 69q-23 -19 -52 -28.5t-58 -9.5q-64 0 -111 38z M249 195q24 0 45.5 9.5t37.5 27.5t25.5 42.5t9.5 54.5t-9.5 54.5t-25.5 42.5t-37.5 27.5t-45.5 9.5t-46 -9.5t-38 -27.5t-25.5 -42.5t-9.5 -54.5t9.5 -54.5t25.5 -42.5t38 -27.5t46 -9.5z" />
<glyph unicode="&#xa5;" d="M207 0v158h-162v48h162v65h-162v47h141l-163 320h86l78 -171q15 -33 29.5 -66.5t30.5 -68.5h4q17 35 31.5 68.5t29.5 66.5l78 171h84l-164 -320h142v-47h-163v-65h163v-48h-163v-158h-82z" />
<glyph unicode="&#xa6;" horiz-adv-x="241" d="M92 291v459h58v-459h-58zM92 -250v464h58v-464h-58z" />
<glyph unicode="&#xa7;" d="M117 348q0 -33 20 -54.5t50.5 -37.5t66 -30.5t66.5 -32.5q29 14 44.5 33t15.5 52q0 34 -20 56t-50 38.5t-65.5 30.5t-66.5 32q-29 -16 -45 -36t-16 -51zM236 -64q-54 0 -98 19t-75 52l50 45q25 -24 54 -38t69 -14t62.5 20t22.5 49q0 28 -20.5 47t-51 34t-66.5 29.5 t-66.5 35.5t-51 51.5t-20.5 76.5q0 44 24 76t62 53q-15 16 -23.5 36t-8.5 46t9.5 49.5t29 41.5t47.5 28.5t66 10.5q48 0 86.5 -17t67.5 -40l-40 -53q-24 20 -51 33t-60 13q-42 0 -60.5 -18.5t-18.5 -44.5q0 -27 20.5 -45t51.5 -33t66.5 -30t66.5 -36.5t51.5 -52t20.5 -76.5 q0 -48 -23.5 -77.5t-61.5 -51.5q14 -17 22 -36.5t8 -45.5q0 -30 -12 -55t-33.5 -43t-51 -28.5t-64.5 -10.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="542" d="M175 587q-22 0 -36 14.5t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM367 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="744" d="M372 -11q-65 0 -124 23.5t-103 67t-70 105t-26 138.5q0 76 26 137t70 104t103 66t124 23t123.5 -23t103 -66t70.5 -104t26 -137q0 -77 -26 -138.5t-70.5 -105t-103 -67t-123.5 -23.5zM372 31q57 0 107 21t87.5 59.5t59.5 92t22 119.5q0 65 -22 118.5t-59.5 91.5 t-87.5 58.5t-107 20.5t-107 -20.5t-87.5 -58.5t-59.5 -91.5t-22 -118.5q0 -66 22 -119.5t59.5 -92t87.5 -59.5t107 -21zM380 125q-38 0 -71 13.5t-58 39t-39 62t-14 83.5q0 43 15.5 78t41 60t59 38t69.5 13q42 0 71.5 -16.5t53.5 -40.5l-35 -39q-20 20 -40.5 30.5 t-46.5 10.5q-55 0 -88.5 -37.5t-33.5 -96.5q0 -66 32.5 -104.5t86.5 -38.5q32 0 56 12.5t46 31.5l30 -42q-28 -24 -59 -40.5t-76 -16.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="345" d="M136 387q-45 0 -72 25.5t-27 68.5q0 53 47.5 80.5t151.5 38.5q-1 32 -14 52.5t-48 20.5q-26 0 -53.5 -10.5t-47.5 -23.5l-23 43q25 15 59.5 28.5t73.5 13.5q60 0 87.5 -35t27.5 -98v-196h-50l-7 37h-4q-20 -18 -45 -31.5t-56 -13.5zM154 436q39 0 82 40v85 q-77 -8 -107.5 -27.5t-30.5 -47.5q0 -26 15 -38t41 -12z" />
<glyph unicode="&#xab;" horiz-adv-x="429" d="M181 66l-136 155v62l136 155l36 -30l-118 -156l118 -158zM339 66l-136 155v62l136 155l36 -30l-118 -156l118 -158z" />
<glyph unicode="&#xac;" d="M397 104v195h-363v62h429v-257h-66z" />
<glyph unicode="&#xad;" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="&#xae;" horiz-adv-x="423" d="M211 319q-39 0 -73 14.5t-59.5 40.5t-40.5 62t-15 80t15 80t40.5 62t59.5 40.5t73 14.5t73.5 -14.5t60 -40.5t40.5 -62t15 -80t-15 -80t-40.5 -62t-60 -40.5t-73.5 -14.5zM211 356q31 0 58.5 11.5t47.5 32.5t31.5 50.5t11.5 65.5q0 35 -11.5 64.5t-31.5 51t-47.5 33.5 t-58.5 12q-32 0 -59 -12t-46.5 -33.5t-31 -51t-11.5 -64.5q0 -36 11.5 -65.5t31 -50.5t46.5 -32.5t59 -11.5zM139 417v203h76q32 0 55 -14.5t23 -50.5q0 -18 -10 -33.5t-27 -21.5l46 -83h-46l-35 70h-41v-70h-41zM180 519h26q43 0 43 34q0 15 -9 24t-32 9h-28v-67z" />
<glyph unicode="&#xaf;" horiz-adv-x="542" d="M138 601v57h266v-57h-266z" />
<glyph unicode="&#xb0;" horiz-adv-x="331" d="M166 429q-25 0 -47.5 9t-40 25.5t-27.5 40t-10 52.5q0 30 10 53.5t27.5 40.5t40 26t47.5 9t47.5 -9t40 -26t27.5 -40.5t10 -53.5q0 -29 -10 -52.5t-27.5 -40t-40 -25.5t-47.5 -9zM166 475q33 0 54 23t21 58q0 37 -21 60t-54 23t-54 -23t-21 -60q0 -35 21 -58t54 -23z" />
<glyph unicode="&#xb1;" d="M216 127v177h-182v62h182v190h65v-190h182v-62h-182v-177h-65zM34 0v62h429v-62h-429z" />
<glyph unicode="&#xb2;" horiz-adv-x="367" d="M52 395v37q45 41 80 73.5t58 59.5t35 50.5t12 45.5q0 38 -20 60t-55 22q-25 0 -46 -16.5t-38 -40.5l-38 35q23 34 56.5 55t73.5 21q59 0 94.5 -32t35.5 -94q0 -28 -11 -54t-30.5 -52.5t-46 -54.5t-57.5 -60h165v-55h-268z" />
<glyph unicode="&#xb3;" horiz-adv-x="367" d="M180 383q-48 0 -85 21.5t-60 54.5l43 33q18 -27 43 -42.5t56 -15.5q32 0 55 18t23 52t-32 51.5t-89 17.5v41q51 0 78.5 20t27.5 50q0 28 -19.5 45t-51.5 17q-22 0 -42 -13.5t-37 -33.5l-39 34q26 29 56.5 46.5t71.5 17.5q25 0 47.5 -7t39.5 -20.5t27 -33t10 -44.5 q0 -35 -19 -58.5t-49 -37.5q33 -8 58.5 -32.5t25.5 -63.5q0 -27 -11 -48.5t-30 -37t-44.5 -23.5t-53.5 -8z" />
<glyph unicode="&#xb4;" horiz-adv-x="542" d="M257 573l-42 41l141 167l58 -55z" />
<glyph unicode="&#xb5;" horiz-adv-x="562" d="M82 -179v665h82v-297q0 -65 20.5 -97.5t68.5 -32.5q16 0 32 3.5t32 14t32 29t33 48.5v332h83q-2 -99 -4 -201t-2 -190q0 -20 10 -29.5t26 -9.5q12 0 29 6l11 -62q-11 -5 -24.5 -8.5t-33.5 -3.5q-42 0 -62.5 22.5t-25.5 71.5h-2q-26 -47 -60.5 -69.5t-74.5 -22.5 q-29 0 -53 9t-41 38q0 -36 0.5 -63.5t1.5 -51.5l2 -48t3 -53h-83z" />
<glyph unicode="&#xb6;" horiz-adv-x="560" d="M380 -80v736h84v-736h-84zM293 226q-54 0 -100.5 12.5t-80 39t-52.5 67.5t-19 98q0 59 18 99.5t50.5 66t76.5 36.5t96 11h44v-430h-33z" />
<glyph unicode="&#xb7;" horiz-adv-x="249" d="M125 259q-25 0 -42.5 17.5t-17.5 44.5q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="542" d="M190 -226l-8 40q63 5 86 16.5t23 33.5q0 20 -17 31t-60 17l44 91h53l-29 -67q35 -8 54 -24t19 -47q0 -44 -42.5 -65.5t-122.5 -25.5z" />
<glyph unicode="&#xb9;" horiz-adv-x="367" d="M172 395v308h-85v42q33 6 55 15.5t42 24.5h52v-390h-64z" />
<glyph unicode="&#xba;" horiz-adv-x="365" d="M182 387q-31 0 -58.5 11.5t-48.5 33t-33 52.5t-12 71t12 71.5t33 53.5t48.5 33t58.5 11t58.5 -11t48.5 -33t33 -53.5t12 -71.5t-12 -71t-33 -52.5t-48.5 -33t-58.5 -11.5zM182 438q42 0 65 32.5t23 84.5q0 54 -23 86t-65 32t-65.5 -32t-23.5 -86q0 -52 23.5 -84.5 t65.5 -32.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="429" d="M89 66l-35 28l118 158l-118 156l35 30l137 -155v-62zM247 66l-35 28l118 158l-118 156l35 30l137 -155v-62z" />
<glyph unicode="&#xbc;" horiz-adv-x="781" d="M149 266v308h-85v42q33 6 55 15.5t42 24.5h52v-390h-64zM180 -12l362 680h56l-362 -680h-56zM633 0v104h-178v33l164 253h72v-240h58v-46h-58v-104h-58zM522 150h111v70l4 109h-4l-50 -81z" />
<glyph unicode="&#xbd;" horiz-adv-x="808" d="M149 266v308h-85v42q33 6 55 15.5t42 24.5h52v-390h-64zM159 -12l362 680h56l-362 -680h-56zM493 0v37q45 41 80 73.5t58 59.5t35 50.5t12 45.5q0 38 -20 60t-55 22q-25 0 -46 -16.5t-38 -40.5l-38 35q23 34 56.5 55t73.5 21q59 0 94.5 -32t35.5 -94q0 -28 -11 -54 t-30.5 -52.5t-46 -54.5t-57.5 -60h165v-55h-268z" />
<glyph unicode="&#xbe;" horiz-adv-x="796" d="M180 254q-48 0 -85 21.5t-60 54.5l43 33q18 -27 43 -42.5t56 -15.5q32 0 55 18t23 52t-32 51.5t-89 17.5v41q51 0 78.5 20t27.5 50q0 28 -19.5 45t-51.5 17q-22 0 -42 -13.5t-37 -33.5l-39 34q26 29 56.5 46.5t71.5 17.5q25 0 47.5 -7t39.5 -20.5t27 -33t10 -44.5 q0 -35 -19 -58.5t-49 -37.5q33 -8 58.5 -32.5t25.5 -63.5q0 -27 -11 -48.5t-30 -37t-44.5 -23.5t-53.5 -8zM217 -12l362 680h56l-362 -680h-56zM648 0v104h-178v33l164 253h72v-240h58v-46h-58v-104h-58zM537 150h111v70l4 109h-4l-50 -81z" />
<glyph unicode="&#xbf;" horiz-adv-x="425" d="M215 -196q-76 0 -121.5 42.5t-45.5 117.5q0 33 12 60t29.5 51t37 47.5t35.5 48.5t25 53.5t5 63.5h73q5 -39 -3 -70.5t-23 -58t-34.5 -49.5t-36.5 -45.5t-28.5 -46t-11.5 -49.5q0 -40 24 -68t73 -28q33 0 62 15t52 42l48 -43q-32 -35 -74.5 -59t-97.5 -24zM227 372 q-25 0 -42.5 17.5t-17.5 46.5q0 27 17.5 44.5t42.5 17.5q24 0 41.5 -17.5t17.5 -44.5q0 -29 -17.5 -46.5t-41.5 -17.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM288 697l-152 115l47 55l142 -128z" />
<glyph unicode="&#xc1;" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM254 697l-37 42l142 128l47 -55z" />
<glyph unicode="&#xc2;" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM123 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93z" />
<glyph unicode="&#xc3;" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM346 709q-28 0 -48 11t-36.5 24.5t-31 24.5t-32.5 11q-19 0 -32 -17t-16 -49l-56 4q3 57 30 90t72 33 q28 0 48 -11t36.5 -24.5t31 -24.5t32.5 -11q40 0 48 66l56 -4q-3 -58 -30 -90.5t-72 -32.5z" />
<glyph unicode="&#xc4;" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM168 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14z M374 715q-22 0 -35.5 14t-13.5 35q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14z" />
<glyph unicode="&#xc5;" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-18 55 -34 109.5t-32 111.5h-4q-15 -57 -31 -111.5t-34 -109.5zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM271 699q-42 0 -69.5 24.5t-27.5 65.5q0 42 27.5 66.5t69.5 24.5t69.5 -24.5t27.5 -66.5q0 -41 -27.5 -65.5 t-69.5 -24.5zM271 735q20 0 35.5 14t15.5 40t-15.5 40t-35.5 14q-22 0 -37 -14t-15 -40t15 -40t37 -14z" />
<glyph unicode="&#xc6;" horiz-adv-x="822" d="M290 376l-61 -118h172v332h-4q-53 -107 -107 -214zM8 0l344 656h411v-70h-278v-206h232v-71h-232v-238h288v-71h-372v191h-206l-99 -191h-88z" />
<glyph unicode="&#xc7;" horiz-adv-x="571" d="M338 -12q-62 0 -114 23t-90.5 66.5t-60 107t-21.5 143.5q0 79 22 142t61 107t92 67.5t116 23.5q60 0 105.5 -24.5t74.5 -56.5l-45 -54q-26 28 -59 45t-75 17q-47 0 -85 -18.5t-65 -53t-41.5 -83.5t-14.5 -110q0 -62 14.5 -111.5t40.5 -84.5t63.5 -54t84.5 -19 q48 0 84 19.5t68 54.5l46 -52q-39 -45 -88 -70t-113 -25zM256 -226l-8 40q63 5 86 16.5t23 33.5q0 20 -17 31t-60 17l44 91h53l-29 -67q35 -8 54 -24t19 -47q0 -44 -42.5 -65.5t-122.5 -25.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388zM301 697l-152 115l47 55l142 -128z" />
<glyph unicode="&#xc9;" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388zM267 697l-37 42l142 128l47 -55z" />
<glyph unicode="&#xca;" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388zM136 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93z" />
<glyph unicode="&#xcb;" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388zM181 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14zM387 715q-22 0 -35.5 14t-13.5 35q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5 q0 -21 -14 -35t-35 -14z" />
<glyph unicode="&#xcc;" horiz-adv-x="263" d="M90 0v656h83v-656h-83zM148 697l-152 115l47 55l142 -128z" />
<glyph unicode="&#xcd;" horiz-adv-x="263" d="M90 0v656h83v-656h-83zM114 697l-37 42l142 128l47 -55z" />
<glyph unicode="&#xce;" horiz-adv-x="263" d="M90 0v656h83v-656h-83zM-17 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93z" />
<glyph unicode="&#xcf;" horiz-adv-x="263" d="M90 0v656h83v-656h-83zM28 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14zM234 715q-22 0 -35.5 14t-13.5 35q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14z" />
<glyph unicode="&#xd0;" horiz-adv-x="638" d="M33 321v43l79 4v288h164q152 0 231 -84t79 -241q0 -78 -20 -139.5t-59 -104t-96 -65t-131 -22.5h-168v321h-79zM195 68h75q115 0 172.5 69t57.5 194t-57.5 191t-172.5 66h-75v-220h149v-47h-149v-253z" />
<glyph unicode="&#xd1;" horiz-adv-x="647" d="M90 0v656h86l237 -412l71 -136h4q-3 50 -6.5 103.5t-3.5 105.5v339h79v-656h-86l-238 413l-71 135h-4q4 -50 7.5 -101.5t3.5 -103.5v-343h-79zM401 709q-28 0 -48 11t-36.5 24.5t-31 24.5t-32.5 11q-19 0 -32 -17t-16 -49l-56 4q3 57 30 90t72 33q28 0 48 -11t36.5 -24.5 t31 -24.5t32.5 -11q40 0 48 66l56 -4q-3 -58 -30 -90.5t-72 -32.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="664" d="M332 -12q-62 0 -113.5 24t-88.5 68.5t-57.5 108t-20.5 142.5t20.5 141.5t57.5 106t88.5 66.5t113.5 23t113.5 -23.5t89 -67t58 -106t20.5 -140.5q0 -79 -20.5 -142.5t-58 -108t-89 -68.5t-113.5 -24zM332 61q44 0 79.5 19t61 54.5t39.5 85t14 111.5q0 61 -14 110 t-39.5 83t-61 52.5t-79.5 18.5t-79.5 -18.5t-61 -52.5t-39.5 -83t-14 -110q0 -62 14 -111.5t39.5 -85t61 -54.5t79.5 -19zM349 697l-152 115l47 55l142 -128z" />
<glyph unicode="&#xd3;" horiz-adv-x="664" d="M332 -12q-62 0 -113.5 24t-88.5 68.5t-57.5 108t-20.5 142.5t20.5 141.5t57.5 106t88.5 66.5t113.5 23t113.5 -23.5t89 -67t58 -106t20.5 -140.5q0 -79 -20.5 -142.5t-58 -108t-89 -68.5t-113.5 -24zM332 61q44 0 79.5 19t61 54.5t39.5 85t14 111.5q0 61 -14 110 t-39.5 83t-61 52.5t-79.5 18.5t-79.5 -18.5t-61 -52.5t-39.5 -83t-14 -110q0 -62 14 -111.5t39.5 -85t61 -54.5t79.5 -19zM315 697l-37 42l142 128l47 -55z" />
<glyph unicode="&#xd4;" horiz-adv-x="664" d="M332 -12q-62 0 -113.5 24t-88.5 68.5t-57.5 108t-20.5 142.5t20.5 141.5t57.5 106t88.5 66.5t113.5 23t113.5 -23.5t89 -67t58 -106t20.5 -140.5q0 -79 -20.5 -142.5t-58 -108t-89 -68.5t-113.5 -24zM332 61q44 0 79.5 19t61 54.5t39.5 85t14 111.5q0 61 -14 110 t-39.5 83t-61 52.5t-79.5 18.5t-79.5 -18.5t-61 -52.5t-39.5 -83t-14 -110q0 -62 14 -111.5t39.5 -85t61 -54.5t79.5 -19zM184 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93z" />
<glyph unicode="&#xd5;" horiz-adv-x="664" d="M332 -12q-62 0 -113.5 24t-88.5 68.5t-57.5 108t-20.5 142.5t20.5 141.5t57.5 106t88.5 66.5t113.5 23t113.5 -23.5t89 -67t58 -106t20.5 -140.5q0 -79 -20.5 -142.5t-58 -108t-89 -68.5t-113.5 -24zM332 61q44 0 79.5 19t61 54.5t39.5 85t14 111.5q0 61 -14 110 t-39.5 83t-61 52.5t-79.5 18.5t-79.5 -18.5t-61 -52.5t-39.5 -83t-14 -110q0 -62 14 -111.5t39.5 -85t61 -54.5t79.5 -19zM407 709q-28 0 -48 11t-36.5 24.5t-31 24.5t-32.5 11q-19 0 -32 -17t-16 -49l-56 4q3 57 30 90t72 33q28 0 48 -11t36.5 -24.5t31 -24.5t32.5 -11 q40 0 48 66l56 -4q-3 -58 -30 -90.5t-72 -32.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="664" d="M332 -12q-62 0 -113.5 24t-88.5 68.5t-57.5 108t-20.5 142.5t20.5 141.5t57.5 106t88.5 66.5t113.5 23t113.5 -23.5t89 -67t58 -106t20.5 -140.5q0 -79 -20.5 -142.5t-58 -108t-89 -68.5t-113.5 -24zM332 61q44 0 79.5 19t61 54.5t39.5 85t14 111.5q0 61 -14 110 t-39.5 83t-61 52.5t-79.5 18.5t-79.5 -18.5t-61 -52.5t-39.5 -83t-14 -110q0 -62 14 -111.5t39.5 -85t61 -54.5t79.5 -19zM229 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14zM435 715q-22 0 -35.5 14t-13.5 35 q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14z" />
<glyph unicode="&#xd7;" d="M94 126l-44 45l155 159l-155 158l44 45l155 -159l154 159l44 -45l-155 -158l155 -159l-44 -45l-154 160z" />
<glyph unicode="&#xd8;" horiz-adv-x="664" d="M335 -12q-104 0 -175 65l-64 -83l-46 36l70 91q-31 45 -48 103.5t-17 130.5q0 79 20.5 141.5t57.5 106t88.5 66.5t113.5 23q104 0 176 -63l62 81l46 -35l-69 -89q32 -44 49 -101.5t17 -129.5q0 -79 -20.5 -142.5t-58 -108t-89 -68.5t-113.5 -24zM141 331q0 -48 8.5 -89.5 t24.5 -73.5l288 373q-51 54 -127 54q-44 0 -79.5 -18.5t-61 -52.5t-39.5 -83t-14 -110zM335 61q44 0 79.5 19t61 54.5t39.5 85t14 111.5q0 48 -8.5 88t-24.5 72l-287 -374q50 -56 126 -56z" />
<glyph unicode="&#xd9;" horiz-adv-x="645" d="M323 -12q-50 0 -93.5 14.5t-75 48t-49.5 87.5t-18 133v385h83v-387q0 -59 12 -99t32.5 -64t48.5 -34.5t60 -10.5q33 0 61 10.5t49 34.5t33 64t12 99v387h80v-385q0 -79 -18 -133t-49.5 -87.5t-74.5 -48t-93 -14.5zM339 697l-152 115l47 55l142 -128z" />
<glyph unicode="&#xda;" horiz-adv-x="645" d="M323 -12q-50 0 -93.5 14.5t-75 48t-49.5 87.5t-18 133v385h83v-387q0 -59 12 -99t32.5 -64t48.5 -34.5t60 -10.5q33 0 61 10.5t49 34.5t33 64t12 99v387h80v-385q0 -79 -18 -133t-49.5 -87.5t-74.5 -48t-93 -14.5zM305 697l-37 42l142 128l47 -55z" />
<glyph unicode="&#xdb;" horiz-adv-x="645" d="M323 -12q-50 0 -93.5 14.5t-75 48t-49.5 87.5t-18 133v385h83v-387q0 -59 12 -99t32.5 -64t48.5 -34.5t60 -10.5q33 0 61 10.5t49 34.5t33 64t12 99v387h80v-385q0 -79 -18 -133t-49.5 -87.5t-74.5 -48t-93 -14.5zM174 725l105 113h86l105 -113l-36 -26l-110 93h-4 l-110 -93z" />
<glyph unicode="&#xdc;" horiz-adv-x="645" d="M323 -12q-50 0 -93.5 14.5t-75 48t-49.5 87.5t-18 133v385h83v-387q0 -59 12 -99t32.5 -64t48.5 -34.5t60 -10.5q33 0 61 10.5t49 34.5t33 64t12 99v387h80v-385q0 -79 -18 -133t-49.5 -87.5t-74.5 -48t-93 -14.5zM219 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5 q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14zM425 715q-22 0 -35.5 14t-13.5 35q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14z" />
<glyph unicode="&#xdd;" horiz-adv-x="476" d="M196 0v254l-197 402h89l85 -185q16 -36 31 -71t32 -72h4q17 37 34 72t32 71l84 185h87l-197 -402v-254h-84zM221 697l-37 42l142 128l47 -55z" />
<glyph unicode="&#xde;" horiz-adv-x="583" d="M90 0v656h83v-110h118q54 0 99 -10t76.5 -33t49 -59.5t17.5 -90.5q0 -52 -17.5 -90.5t-49.5 -63.5t-76.5 -37t-98.5 -12h-118v-150h-83zM173 218h108q86 0 127.5 32t41.5 103t-42 98t-127 27h-108v-260z" />
<glyph unicode="&#xdf;" horiz-adv-x="576" d="M387 -12q-42 0 -76 13t-65 36l33 58q26 -22 51.5 -32.5t54.5 -10.5q42 0 63 24t21 56q0 29 -14.5 48t-36.5 33t-47.5 26.5t-47.5 28.5t-36.5 38.5t-14.5 57.5q0 34 13.5 58t30 46t30.5 45t14 55q0 38 -20.5 62.5t-62.5 24.5q-54 0 -83.5 -38.5t-29.5 -116.5v-500h-82v515 q0 94 51 150.5t145 56.5q39 0 69.5 -11.5t51.5 -31.5t31.5 -46.5t10.5 -56.5q0 -38 -14 -64.5t-31.5 -48.5t-31.5 -42.5t-14 -46.5q0 -24 14.5 -40t36.5 -28.5t47.5 -25.5t47.5 -31t36.5 -45t14.5 -67q0 -32 -11 -59t-32 -47t-50.5 -31.5t-66.5 -11.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="504" d="M194 -12q-61 0 -101.5 36t-40.5 102q0 80 71 122.5t227 59.5q0 23 -4.5 45t-15.5 39t-30.5 27.5t-49.5 10.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83 42.5t108 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-35 -29 -75 -49.5t-86 -20.5zM218 54q35 0 66 16.5 t66 48.5v135q-61 -8 -102.5 -19t-67 -26t-37 -34.5t-11.5 -42.5q0 -42 25 -60t61 -18zM276 573l-157 153l58 55l141 -167z" />
<glyph unicode="&#xe1;" horiz-adv-x="504" d="M194 -12q-61 0 -101.5 36t-40.5 102q0 80 71 122.5t227 59.5q0 23 -4.5 45t-15.5 39t-30.5 27.5t-49.5 10.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83 42.5t108 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-35 -29 -75 -49.5t-86 -20.5zM218 54q35 0 66 16.5 t66 48.5v135q-61 -8 -102.5 -19t-67 -26t-37 -34.5t-11.5 -42.5q0 -42 25 -60t61 -18zM248 573l-42 41l141 167l58 -55z" />
<glyph unicode="&#xe2;" horiz-adv-x="504" d="M194 -12q-61 0 -101.5 36t-40.5 102q0 80 71 122.5t227 59.5q0 23 -4.5 45t-15.5 39t-30.5 27.5t-49.5 10.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83 42.5t108 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-35 -29 -75 -49.5t-86 -20.5zM218 54q35 0 66 16.5 t66 48.5v135q-61 -8 -102.5 -19t-67 -26t-37 -34.5t-11.5 -42.5q0 -42 25 -60t61 -18zM107 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#xe3;" horiz-adv-x="504" d="M194 -12q-61 0 -101.5 36t-40.5 102q0 80 71 122.5t227 59.5q0 23 -4.5 45t-15.5 39t-30.5 27.5t-49.5 10.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83 42.5t108 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-35 -29 -75 -49.5t-86 -20.5zM218 54q35 0 66 16.5 t66 48.5v135q-61 -8 -102.5 -19t-67 -26t-37 -34.5t-11.5 -42.5q0 -42 25 -60t61 -18zM334 577q-27 0 -46 13t-35 29t-30.5 29t-31.5 13q-23 0 -34 -22t-13 -57l-55 3q1 29 7.5 54t18.5 43t30.5 28.5t44.5 10.5q27 0 46 -13t35 -29t30.5 -29t32.5 -13q22 0 33 22t13 57 l55 -4q-1 -29 -7.5 -53.5t-18.5 -42.5t-30.5 -28.5t-44.5 -10.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="504" d="M194 -12q-61 0 -101.5 36t-40.5 102q0 80 71 122.5t227 59.5q0 23 -4.5 45t-15.5 39t-30.5 27.5t-49.5 10.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83 42.5t108 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-35 -29 -75 -49.5t-86 -20.5zM218 54q35 0 66 16.5 t66 48.5v135q-61 -8 -102.5 -19t-67 -26t-37 -34.5t-11.5 -42.5q0 -42 25 -60t61 -18zM166 587q-22 0 -36 14.5t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM358 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5 t-36 -14.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="504" d="M194 -12q-61 0 -101.5 36t-40.5 102q0 80 71 122.5t227 59.5q0 23 -4.5 45t-15.5 39t-30.5 27.5t-49.5 10.5q-42 0 -79 -16t-66 -36l-32 57q34 22 83 42.5t108 20.5q89 0 129 -54.5t40 -145.5v-298h-68l-7 58h-3q-35 -29 -75 -49.5t-86 -20.5zM218 54q35 0 66 16.5 t66 48.5v135q-61 -8 -102.5 -19t-67 -26t-37 -34.5t-11.5 -42.5q0 -42 25 -60t61 -18zM262 554q-51 0 -80.5 28t-29.5 71q0 42 29.5 70t80.5 28t80.5 -28t29.5 -70q0 -43 -29.5 -71t-80.5 -28zM262 591q24 0 40.5 16.5t16.5 45.5q0 28 -16.5 44.5t-40.5 16.5t-40.5 -16.5 t-16.5 -44.5q0 -29 16.5 -45.5t40.5 -16.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="785" d="M201 -12q-62 0 -102.5 36t-40.5 102q0 80 71 122.5t223 59.5q0 23 -4 45t-15.5 39t-30.5 27.5t-49 10.5q-40 0 -76 -16t-65 -36l-33 57q34 22 82 42.5t102 20.5t89 -27.5t50 -75.5q29 48 69.5 75.5t90.5 27.5q45 0 79.5 -16t58 -46t35.5 -72.5t12 -94.5q0 -28 -3 -46 h-315q1 -38 13.5 -69t32.5 -53t47 -34t58 -12q35 0 63 11.5t55 28.5l30 -57q-32 -20 -70 -35t-88 -15q-61 0 -102 27.5t-69 63.5q-50 -45 -100.5 -68t-97.5 -23zM224 54q34 0 74 19.5t73 55.5q-8 19 -13 45.5t-6 54.5v25q-58 -8 -98.5 -19t-66 -26t-37 -34.5t-11.5 -42.5 q0 -42 24.5 -60t60.5 -18zM429 278h246q0 75 -29.5 115t-85.5 40q-51 0 -87.5 -41.5t-43.5 -113.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="456" d="M274 -12q-48 0 -90 17t-72.5 49.5t-48 80t-17.5 107.5q0 61 19 108.5t51 80.5t74.5 50t89.5 17q48 0 82 -17t60 -40l-42 -54q-21 19 -44.5 31t-52.5 12q-33 0 -61 -13.5t-48 -38.5t-31.5 -59.5t-11.5 -76.5t11 -76t30.5 -58.5t47.5 -38t61 -13.5q34 0 62.5 14.5 t51.5 34.5l36 -55q-33 -29 -73 -45.5t-84 -16.5zM185 -226l-8 40q63 5 86 16.5t23 33.5q0 20 -17 31t-60 17l44 91h53l-29 -67q35 -8 54 -24t19 -47q0 -44 -42.5 -65.5t-122.5 -25.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="496" d="M279 -12q-49 0 -91.5 17.5t-74 50t-49.5 79.5t-18 107t18.5 107.5t49 80.5t69.5 50.5t81 17.5q46 0 82.5 -16t61 -46t37.5 -72t13 -94q0 -13 -0.5 -25.5t-2.5 -21.5h-328q5 -78 48.5 -123.5t113.5 -45.5q35 0 64.5 10.5t56.5 27.5l29 -54q-32 -20 -71 -35t-89 -15z M126 282h260q0 74 -31.5 112.5t-88.5 38.5q-26 0 -49.5 -10t-42.5 -29.5t-31.5 -47.5t-16.5 -64zM279 573l-157 153l58 55l141 -167z" />
<glyph unicode="&#xe9;" horiz-adv-x="496" d="M279 -12q-49 0 -91.5 17.5t-74 50t-49.5 79.5t-18 107t18.5 107.5t49 80.5t69.5 50.5t81 17.5q46 0 82.5 -16t61 -46t37.5 -72t13 -94q0 -13 -0.5 -25.5t-2.5 -21.5h-328q5 -78 48.5 -123.5t113.5 -45.5q35 0 64.5 10.5t56.5 27.5l29 -54q-32 -20 -71 -35t-89 -15z M126 282h260q0 74 -31.5 112.5t-88.5 38.5q-26 0 -49.5 -10t-42.5 -29.5t-31.5 -47.5t-16.5 -64zM251 573l-42 41l141 167l58 -55z" />
<glyph unicode="&#xea;" horiz-adv-x="496" d="M279 -12q-49 0 -91.5 17.5t-74 50t-49.5 79.5t-18 107t18.5 107.5t49 80.5t69.5 50.5t81 17.5q46 0 82.5 -16t61 -46t37.5 -72t13 -94q0 -13 -0.5 -25.5t-2.5 -21.5h-328q5 -78 48.5 -123.5t113.5 -45.5q35 0 64.5 10.5t56.5 27.5l29 -54q-32 -20 -71 -35t-89 -15z M126 282h260q0 74 -31.5 112.5t-88.5 38.5q-26 0 -49.5 -10t-42.5 -29.5t-31.5 -47.5t-16.5 -64zM110 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#xeb;" horiz-adv-x="496" d="M279 -12q-49 0 -91.5 17.5t-74 50t-49.5 79.5t-18 107t18.5 107.5t49 80.5t69.5 50.5t81 17.5q46 0 82.5 -16t61 -46t37.5 -72t13 -94q0 -13 -0.5 -25.5t-2.5 -21.5h-328q5 -78 48.5 -123.5t113.5 -45.5q35 0 64.5 10.5t56.5 27.5l29 -54q-32 -20 -71 -35t-89 -15z M126 282h260q0 74 -31.5 112.5t-88.5 38.5q-26 0 -49.5 -10t-42.5 -29.5t-31.5 -47.5t-16.5 -64zM169 587q-22 0 -36 14.5t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM361 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5 t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#xec;" horiz-adv-x="246" d="M82 0v486h82v-486h-82zM137 573l-157 153l58 55l141 -167z" />
<glyph unicode="&#xed;" horiz-adv-x="246" d="M82 0v486h82v-486h-82zM109 573l-42 41l141 167l58 -55z" />
<glyph unicode="&#xee;" horiz-adv-x="246" d="M82 0v486h82v-486h-82zM-32 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#xef;" horiz-adv-x="246" d="M82 0v486h82v-486h-82zM27 587q-22 0 -36 14.5t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM219 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="545" d="M269 -12q-42 0 -81 16t-69 46.5t-48 74t-18 97.5q0 51 16 92.5t43.5 71t65 45.5t81.5 16q38 0 73 -16t61 -50q-14 62 -41.5 108t-65.5 84l-141 -73l-24 41l127 65q-52 40 -112 72l38 52q36 -20 71 -42t67 -49l142 73l24 -41l-129 -66q60 -61 98 -145t38 -203 q0 -60 -15.5 -109.5t-44 -85t-68 -55t-88.5 -19.5zM271 56q34 0 59 14.5t42 40.5t25.5 62.5t8.5 80.5q0 14 -0.5 28t-1.5 27q-33 44 -66 59t-67 15q-33 0 -59 -12t-44 -34t-27.5 -51.5t-9.5 -63.5q0 -38 11 -68.5t30.5 -52t44.5 -33.5t54 -12z" />
<glyph unicode="&#xf1;" horiz-adv-x="547" d="M82 0v486h68l7 -70h3q35 35 73.5 58.5t89.5 23.5q77 0 112.5 -48t35.5 -142v-308h-82v297q0 69 -22 99.5t-70 30.5q-38 0 -67 -19t-66 -56v-352h-82zM364 577q-27 0 -46 13t-35 29t-30.5 29t-31.5 13q-23 0 -34 -22t-13 -57l-55 3q1 29 7.5 54t18.5 43t30.5 28.5 t44.5 10.5q27 0 46 -13t35 -29t30.5 -29t32.5 -13q22 0 33 22t13 57l55 -4q-1 -29 -7.5 -53.5t-18.5 -42.5t-30.5 -28.5t-44.5 -10.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="542" d="M271 -12q-45 0 -85.5 17t-71.5 49.5t-49.5 80t-18.5 107.5q0 61 18.5 108.5t49.5 80.5t71.5 50t85.5 17t85.5 -17t71.5 -50t49.5 -80.5t18.5 -108.5q0 -60 -18.5 -107.5t-49.5 -80t-71.5 -49.5t-85.5 -17zM271 56q31 0 57 13.5t44.5 38t28.5 58.5t10 76t-10 76.5 t-28.5 59.5t-44.5 38.5t-57 13.5t-57 -13.5t-44.5 -38.5t-28.5 -59.5t-10 -76.5t10 -76t28.5 -58.5t44.5 -38t57 -13.5zM285 573l-157 153l58 55l141 -167z" />
<glyph unicode="&#xf3;" horiz-adv-x="542" d="M271 -12q-45 0 -85.5 17t-71.5 49.5t-49.5 80t-18.5 107.5q0 61 18.5 108.5t49.5 80.5t71.5 50t85.5 17t85.5 -17t71.5 -50t49.5 -80.5t18.5 -108.5q0 -60 -18.5 -107.5t-49.5 -80t-71.5 -49.5t-85.5 -17zM271 56q31 0 57 13.5t44.5 38t28.5 58.5t10 76t-10 76.5 t-28.5 59.5t-44.5 38.5t-57 13.5t-57 -13.5t-44.5 -38.5t-28.5 -59.5t-10 -76.5t10 -76t28.5 -58.5t44.5 -38t57 -13.5zM257 573l-42 41l141 167l58 -55z" />
<glyph unicode="&#xf4;" horiz-adv-x="542" d="M271 -12q-45 0 -85.5 17t-71.5 49.5t-49.5 80t-18.5 107.5q0 61 18.5 108.5t49.5 80.5t71.5 50t85.5 17t85.5 -17t71.5 -50t49.5 -80.5t18.5 -108.5q0 -60 -18.5 -107.5t-49.5 -80t-71.5 -49.5t-85.5 -17zM271 56q31 0 57 13.5t44.5 38t28.5 58.5t10 76t-10 76.5 t-28.5 59.5t-44.5 38.5t-57 13.5t-57 -13.5t-44.5 -38.5t-28.5 -59.5t-10 -76.5t10 -76t28.5 -58.5t44.5 -38t57 -13.5zM116 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#xf5;" horiz-adv-x="542" d="M271 -12q-45 0 -85.5 17t-71.5 49.5t-49.5 80t-18.5 107.5q0 61 18.5 108.5t49.5 80.5t71.5 50t85.5 17t85.5 -17t71.5 -50t49.5 -80.5t18.5 -108.5q0 -60 -18.5 -107.5t-49.5 -80t-71.5 -49.5t-85.5 -17zM271 56q31 0 57 13.5t44.5 38t28.5 58.5t10 76t-10 76.5 t-28.5 59.5t-44.5 38.5t-57 13.5t-57 -13.5t-44.5 -38.5t-28.5 -59.5t-10 -76.5t10 -76t28.5 -58.5t44.5 -38t57 -13.5zM343 577q-27 0 -46 13t-35 29t-30.5 29t-31.5 13q-23 0 -34 -22t-13 -57l-55 3q1 29 7.5 54t18.5 43t30.5 28.5t44.5 10.5q27 0 46 -13t35 -29t30.5 -29 t32.5 -13q22 0 33 22t13 57l55 -4q-1 -29 -7.5 -53.5t-18.5 -42.5t-30.5 -28.5t-44.5 -10.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="542" d="M271 -12q-45 0 -85.5 17t-71.5 49.5t-49.5 80t-18.5 107.5q0 61 18.5 108.5t49.5 80.5t71.5 50t85.5 17t85.5 -17t71.5 -50t49.5 -80.5t18.5 -108.5q0 -60 -18.5 -107.5t-49.5 -80t-71.5 -49.5t-85.5 -17zM271 56q31 0 57 13.5t44.5 38t28.5 58.5t10 76t-10 76.5 t-28.5 59.5t-44.5 38.5t-57 13.5t-57 -13.5t-44.5 -38.5t-28.5 -59.5t-10 -76.5t10 -76t28.5 -58.5t44.5 -38t57 -13.5zM175 587q-22 0 -36 14.5t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM367 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5 q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#xf7;" d="M34 299v62h429v-62h-429zM249 96q-23 0 -39 15t-16 39q0 23 16 38t39 15t38.5 -15t15.5 -38q0 -24 -15.5 -39t-38.5 -15zM249 456q-23 0 -39 15t-16 39q0 23 16 38t39 15t38.5 -15t15.5 -38q0 -24 -15.5 -39t-38.5 -15z" />
<glyph unicode="&#xf8;" horiz-adv-x="542" d="M271 -12q-79 0 -139 49l-49 -60l-37 29l54 65q-25 32 -39.5 74.5t-14.5 96.5q0 61 18.5 108.5t49.5 80.5t71.5 50t85.5 17q38 0 74 -12.5t65 -37.5l50 61l36 -29l-54 -65q25 -32 39.5 -75.5t14.5 -97.5q0 -60 -18.5 -107.5t-49.5 -80t-71.5 -49.5t-85.5 -17zM127 246 q0 -67 23 -113l214 259q-37 40 -93 40q-31 0 -57.5 -13.5t-45.5 -38t-30 -59t-11 -75.5zM271 54q31 0 57.5 13.5t46 38t30 58.5t10.5 75q0 67 -24 114l-213 -260q39 -39 93 -39z" />
<glyph unicode="&#xf9;" horiz-adv-x="544" d="M224 -12q-78 0 -113.5 48t-35.5 142v308h83v-297q0 -69 21.5 -99.5t69.5 -30.5q38 0 67 19.5t64 62.5v345h82v-486h-68l-7 76h-3q-34 -40 -71.5 -64t-88.5 -24zM286 573l-157 153l58 55l141 -167z" />
<glyph unicode="&#xfa;" horiz-adv-x="544" d="M224 -12q-78 0 -113.5 48t-35.5 142v308h83v-297q0 -69 21.5 -99.5t69.5 -30.5q38 0 67 19.5t64 62.5v345h82v-486h-68l-7 76h-3q-34 -40 -71.5 -64t-88.5 -24zM258 573l-42 41l141 167l58 -55z" />
<glyph unicode="&#xfb;" horiz-adv-x="544" d="M224 -12q-78 0 -113.5 48t-35.5 142v308h83v-297q0 -69 21.5 -99.5t69.5 -30.5q38 0 67 19.5t64 62.5v345h82v-486h-68l-7 76h-3q-34 -40 -71.5 -64t-88.5 -24zM117 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#xfc;" horiz-adv-x="544" d="M224 -12q-78 0 -113.5 48t-35.5 142v308h83v-297q0 -69 21.5 -99.5t69.5 -30.5q38 0 67 19.5t64 62.5v345h82v-486h-68l-7 76h-3q-34 -40 -71.5 -64t-88.5 -24zM176 587q-22 0 -36 14.5t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM368 587 q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="467" d="M90 -209q-17 0 -31 2.5t-26 7.5l16 65l18 -4.5t19 -2.5q42 0 68.5 29.5t41.5 74.5l11 36l-195 487h85l99 -269q11 -32 23.5 -67.5t23.5 -69.5h4q11 33 21 69t20 68l87 269h80l-183 -526q-13 -36 -29 -67t-38 -53.5t-50 -35.5t-65 -13zM228 573l-42 41l141 167l58 -55z " />
<glyph unicode="&#xfe;" horiz-adv-x="555" d="M82 -205v917h82v-194l-1 -83q32 26 70 44.5t78 18.5q48 0 84.5 -17.5t61.5 -50t37.5 -78t12.5 -102.5q0 -62 -17 -110.5t-46 -82.5t-67 -51.5t-80 -17.5q-35 0 -68 14t-66 40l1 -83v-164h-82zM283 57q30 0 55.5 13.5t44 38t29 60.5t10.5 81q0 40 -7 73t-22.5 56.5 t-40 36.5t-58.5 13q-31 0 -62.5 -17t-67.5 -49v-255q33 -28 64 -39.5t55 -11.5z" />
<glyph unicode="&#xff;" horiz-adv-x="467" d="M90 -209q-17 0 -31 2.5t-26 7.5l16 65l18 -4.5t19 -2.5q42 0 68.5 29.5t41.5 74.5l11 36l-195 487h85l99 -269q11 -32 23.5 -67.5t23.5 -69.5h4q11 33 21 69t20 68l87 269h80l-183 -526q-13 -36 -29 -67t-38 -53.5t-50 -35.5t-65 -13zM146 587q-22 0 -36 14.5t-14 35.5 t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM338 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#x131;" horiz-adv-x="246" d="M82 0v486h82v-486h-82z" />
<glyph unicode="&#x152;" horiz-adv-x="847" d="M369 0q-75 0 -133.5 22.5t-99.5 65t-62.5 104t-21.5 139.5q0 157 84 241t237 84h415v-70h-278v-206h232v-71h-232v-238h288v-71h-429zM379 68h48v520h-48q-119 0 -180 -66t-61 -191q0 -126 61 -194.5t180 -68.5z" />
<glyph unicode="&#x153;" horiz-adv-x="839" d="M264 -12q-44 0 -83.5 17t-69.5 49.5t-47.5 80t-17.5 107.5q0 61 18 108.5t48 80.5t70 50t84 17q56 0 103 -31t73 -91q28 57 72.5 89.5t98.5 32.5q45 0 80 -16t59 -46t36.5 -72.5t12.5 -94.5q0 -28 -3 -46h-319q1 -38 13.5 -69t33 -53t48 -34t58.5 -12q35 0 64 11.5 t56 28.5l30 -57q-32 -20 -71 -35t-89 -15q-57 0 -104 32t-75 89q-57 -121 -179 -121zM266 56q30 0 55 13.5t43 38t28 58.5t10 76t-10 76.5t-28 59.5t-43 38.5t-55 13.5t-55 -13.5t-43 -38.5t-28 -59.5t-10 -76.5t10 -76t28 -58.5t43 -38t55 -13.5zM479 278h250q0 75 -31 115 t-87 40q-51 0 -88 -41.5t-44 -113.5z" />
<glyph unicode="&#x178;" horiz-adv-x="476" d="M196 0v254l-197 402h89l85 -185q16 -36 31 -71t32 -72h4q17 37 34 72t32 71l84 185h87l-197 -402v-254h-84zM135 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14zM341 715q-22 0 -35.5 14t-13.5 35q0 22 13.5 35.5 t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14z" />
<glyph unicode="&#x2c6;" horiz-adv-x="542" d="M116 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#x2dc;" horiz-adv-x="542" d="M343 577q-27 0 -46 13t-35 29t-30.5 29t-31.5 13q-23 0 -34 -22t-13 -57l-55 3q1 29 7.5 54t18.5 43t30.5 28.5t44.5 10.5q27 0 46 -13t35 -29t30.5 -29t32.5 -13q22 0 33 22t13 57l55 -4q-1 -29 -7.5 -53.5t-18.5 -42.5t-30.5 -28.5t-44.5 -10.5z" />
<glyph unicode="&#x300;" horiz-adv-x="0" d="M14 573l-157 153l58 55l141 -167z" />
<glyph unicode="&#x301;" horiz-adv-x="0" d="M-14 573l-42 41l141 167l58 -55z" />
<glyph unicode="&#x302;" horiz-adv-x="0" d="M-155 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#x303;" horiz-adv-x="0" d="M72 577q-27 0 -46 13t-35 29t-30.5 29t-31.5 13q-23 0 -34 -22t-13 -57l-55 3q1 29 7.5 54t18.5 43t30.5 28.5t44.5 10.5q27 0 46 -13t35 -29t30.5 -29t32.5 -13q22 0 33 22t13 57l55 -4q-1 -29 -7.5 -53.5t-18.5 -42.5t-30.5 -28.5t-44.5 -10.5z" />
<glyph unicode="&#x304;" horiz-adv-x="0" d="M-133 601v57h266v-57h-266z" />
<glyph unicode="&#x308;" horiz-adv-x="0" d="M-96 587q-22 0 -36 14.5t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM96 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#x30a;" horiz-adv-x="0" d="M0 554q-51 0 -80.5 28t-29.5 71q0 42 29.5 70t80.5 28t80.5 -28t29.5 -70q0 -43 -29.5 -71t-80.5 -28zM0 591q24 0 40.5 16.5t16.5 45.5q0 28 -16.5 44.5t-40.5 16.5t-40.5 -16.5t-16.5 -44.5q0 -29 16.5 -45.5t40.5 -16.5z" />
<glyph unicode="&#x327;" horiz-adv-x="0" d="M-87 -226l-8 40q63 5 86 16.5t23 33.5q0 20 -17 31t-60 17l44 91h53l-29 -67q35 -8 54 -24t19 -47q0 -44 -42.5 -65.5t-122.5 -25.5z" />
<glyph unicode="&#x3bc;" horiz-adv-x="562" d="M82 -179v665h82v-297q0 -65 20.5 -97.5t68.5 -32.5q16 0 32 3.5t32 14t32 29t33 48.5v332h83q-2 -99 -4 -201t-2 -190q0 -20 10 -29.5t26 -9.5q12 0 29 6l11 -62q-11 -5 -24.5 -8.5t-33.5 -3.5q-42 0 -62.5 22.5t-25.5 71.5h-2q-26 -47 -60.5 -69.5t-74.5 -22.5 q-29 0 -53 9t-41 38q0 -36 0.5 -63.5t1.5 -51.5l2 -48t3 -53h-83z" />
<glyph unicode="&#x1d43;" horiz-adv-x="345" d="M136 387q-45 0 -72 25.5t-27 68.5q0 53 47.5 80.5t151.5 38.5q-1 32 -14 52.5t-48 20.5q-26 0 -53.5 -10.5t-47.5 -23.5l-23 43q25 15 59.5 28.5t73.5 13.5q60 0 87.5 -35t27.5 -98v-196h-50l-7 37h-4q-20 -18 -45 -31.5t-56 -13.5zM154 436q39 0 82 40v85 q-77 -8 -107.5 -27.5t-30.5 -47.5q0 -26 15 -38t41 -12z" />
<glyph unicode="&#x1d52;" horiz-adv-x="365" d="M182 387q-31 0 -58.5 11.5t-48.5 33t-33 52.5t-12 71t12 71.5t33 53.5t48.5 33t58.5 11t58.5 -11t48.5 -33t33 -53.5t12 -71.5t-12 -71t-33 -52.5t-48.5 -33t-58.5 -11.5zM182 438q42 0 65 32.5t23 84.5q0 54 -23 86t-65 32t-65.5 -32t-23.5 -86q0 -52 23.5 -84.5 t65.5 -32.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="440" />
<glyph unicode="&#x2001;" horiz-adv-x="880" />
<glyph unicode="&#x2002;" horiz-adv-x="440" />
<glyph unicode="&#x2003;" horiz-adv-x="880" />
<glyph unicode="&#x2004;" horiz-adv-x="293" />
<glyph unicode="&#x2005;" horiz-adv-x="220" />
<glyph unicode="&#x2006;" horiz-adv-x="146" />
<glyph unicode="&#x2007;" horiz-adv-x="146" />
<glyph unicode="&#x2008;" horiz-adv-x="110" />
<glyph unicode="&#x2009;" horiz-adv-x="176" />
<glyph unicode="&#x200a;" horiz-adv-x="48" />
<glyph unicode="&#x2010;" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="&#x2011;" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="&#x2012;" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="&#x2013;" horiz-adv-x="480" d="M41 223v57h398v-57h-398z" />
<glyph unicode="&#x2014;" horiz-adv-x="800" d="M41 223v57h718v-57h-718z" />
<glyph unicode="&#x2018;" horiz-adv-x="249" d="M120 428q-30 0 -46.5 23t-16.5 67q0 60 27 104t79 74l24 -39q-38 -25 -57 -53.5t-19 -70.5q3 1 9 1q20 0 36.5 -13t16.5 -38q0 -26 -15 -40.5t-38 -14.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="249" d="M86 431l-23 38q38 25 56.5 53.5t18.5 71.5q-3 -1 -8 -1q-21 0 -37 13t-16 37q0 26 15 41t38 15q29 0 46 -23.5t17 -67.5q0 -60 -27.5 -103.5t-79.5 -73.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="249" d="M86 -144l-23 38q38 25 56.5 53.5t18.5 71.5q-3 -1 -8 -1q-21 0 -37 13t-16 37q0 26 15 41t38 15q29 0 46 -23.5t17 -67.5q0 -60 -27.5 -103.5t-79.5 -73.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="425" d="M120 428q-30 0 -46.5 23t-16.5 67q0 60 27 104t79 74l24 -39q-38 -25 -57 -53.5t-19 -70.5q3 1 9 1q20 0 36.5 -13t16.5 -38q0 -26 -15 -40.5t-38 -14.5zM296 428q-30 0 -46.5 23t-16.5 67q0 60 27 104t79 74l24 -39q-38 -25 -57 -53.5t-19 -70.5q3 1 9 1q20 0 36.5 -13 t16.5 -38q0 -26 -15 -40.5t-38 -14.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="425" d="M86 431l-23 38q38 25 56.5 53.5t18.5 71.5q-3 -1 -8 -1q-21 0 -37 13t-16 37q0 26 15 41t38 15q29 0 46 -23.5t17 -67.5q0 -60 -27.5 -103.5t-79.5 -73.5zM262 431l-23 38q38 25 56.5 53.5t18.5 71.5q-3 -1 -8 -1q-21 0 -37 13t-16 37q0 26 15 41t38 15q29 0 46 -23.5 t17 -67.5q0 -60 -27.5 -103.5t-79.5 -73.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="425" d="M86 -144l-23 38q38 25 56.5 53.5t18.5 71.5q-3 -1 -8 -1q-21 0 -37 13t-16 37q0 26 15 41t38 15q29 0 46 -23.5t17 -67.5q0 -60 -27.5 -103.5t-79.5 -73.5zM262 -144l-23 38q38 25 56.5 53.5t18.5 71.5q-3 -1 -8 -1q-21 0 -37 13t-16 37q0 26 15 41t38 15q29 0 46 -23.5 t17 -67.5q0 -60 -27.5 -103.5t-79.5 -73.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="304" d="M152 143q-23 0 -43 8.5t-35.5 24.5t-24.5 38t-9 49t9 49.5t24.5 38t35.5 24.5t43 9q22 0 42.5 -9t36 -24.5t24.5 -38t9 -49.5t-9 -49t-24.5 -38t-36 -24.5t-42.5 -8.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="948" d="M154 -12q-25 0 -42.5 17.5t-17.5 44.5q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5zM489 -12q-25 0 -42.5 17.5t-17.5 44.5q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5zM824 -12 q-25 0 -42.5 17.5t-17.5 44.5q0 29 17.5 46.5t42.5 17.5q24 0 41.5 -17.5t17.5 -46.5q0 -27 -17.5 -44.5t-41.5 -17.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="176" />
<glyph unicode="&#x2039;" horiz-adv-x="271" d="M181 66l-136 155v62l136 155l36 -30l-118 -156l118 -158z" />
<glyph unicode="&#x203a;" horiz-adv-x="271" d="M89 66l-35 28l118 158l-118 156l35 30l137 -155v-62z" />
<glyph unicode="&#x2044;" horiz-adv-x="86" d="M-167 -12l362 680h56l-362 -680h-56z" />
<glyph unicode="&#x205f;" horiz-adv-x="220" />
<glyph unicode="&#x20ac;" d="M319 -12q-45 0 -83.5 16.5t-68.5 48.5t-50.5 77.5t-29.5 104.5h-64v43l59 4q-1 9 -1 18v18v16t1 16h-59v44l64 5q9 59 30 105.5t52.5 79t73 49.5t90.5 17q45 0 84 -22.5t65 -55.5l-49 -47q-21 26 -46 42.5t-57 16.5q-66 0 -106 -49t-53 -136h254v-49h-258q-1 -7 -1 -14 v-15v-19.5t1 -18.5h218v-48h-213q13 -85 51.5 -133t100.5 -48q37 0 64.5 18t53.5 53l49 -44q-33 -44 -75 -68.5t-97 -24.5z" />
<glyph unicode="&#x2122;" horiz-adv-x="637" d="M102 366v256h-99v54h259v-54h-100v-256h-60zM310 366v310h73l47 -116l28 -78h4l28 78l46 116h72v-310h-55v137l7 105h-4l-73 -194h-47l-73 194h-4l7 -105v-137h-56z" />
<glyph unicode="&#x2212;" d="M34 299v62h429v-62h-429z" />
<glyph unicode="&#x25fc;" horiz-adv-x="485" d="M0 485h485v-485h-485v485z" />
<glyph unicode="&#xfb01;" horiz-adv-x="556" d="M96 0v419h-66v62l66 5v77q0 75 34.5 118t107.5 43q23 0 43.5 -4.5t37.5 -11.5l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82zM392 0v486h82v-486h-82zM434 586q-24 0 -40.5 15t-16.5 38q0 24 16.5 38.5t40.5 14.5t40.5 -14.5t16.5 -38.5 q0 -23 -16.5 -38t-40.5 -15z" />
<glyph unicode="&#xfb02;" horiz-adv-x="547" d="M96 0v419h-66v62l66 5v77q0 75 34.5 118t107.5 43q23 0 43.5 -4.5t37.5 -11.5l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82zM461 -12q-47 0 -67 28t-20 82v614h82v-620q0 -20 7 -28t16 -8h7.5t10.5 2l11 -62q-8 -4 -19 -6t-28 -2z" />
<glyph unicode="&#xfb03;" horiz-adv-x="823" d="M381 0v419h-203v-419h-82v419h-66v62l66 5v64q0 76 37.5 120t113.5 44q24 0 47.5 -5t41.5 -13l-17 -62q-30 13 -65 13q-36 0 -56 -25.5t-20 -73.5v-62h203v77q0 75 34.5 118t107.5 43q23 0 43.5 -4.5t37.5 -11.5l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103 v-419h-82zM659 0v486h82v-486h-82zM701 586q-24 0 -40.5 15t-16.5 38q0 24 16.5 38.5t40.5 14.5t40.5 -14.5t16.5 -38.5q0 -23 -16.5 -38t-40.5 -15z" />
<glyph unicode="&#xfb04;" horiz-adv-x="832" d="M381 0v419h-203v-419h-82v419h-66v62l66 5v64q0 76 37.5 120t113.5 44q24 0 47.5 -5t41.5 -13l-17 -62q-30 13 -65 13q-36 0 -56 -25.5t-20 -73.5v-62h203v77q0 75 34.5 118t107.5 43q23 0 43.5 -4.5t37.5 -11.5l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103 v-419h-82zM746 -12q-47 0 -67 28t-20 82v614h82v-620q0 -20 7 -28t16 -8h7.5t10.5 2l11 -62q-8 -4 -19 -6t-28 -2z" />
<glyph horiz-adv-x="367" d="M184 -12q-68 0 -108.5 54t-40.5 154q0 99 40.5 152.5t108.5 53.5q67 0 108 -53.5t41 -152.5q0 -100 -41 -154t-108 -54zM184 39q38 0 62 39.5t24 117.5t-24 116.5t-62 38.5q-39 0 -63 -38.5t-24 -116.5t24 -117.5t63 -39.5z" />
<glyph horiz-adv-x="367" d="M172 0v308h-85v42q33 6 55 15.5t42 24.5h52v-390h-64z" />
<glyph horiz-adv-x="367" d="M52 0v37q45 41 80 73.5t58 59.5t35 50.5t12 45.5q0 38 -20 60t-55 22q-25 0 -46 -16.5t-38 -40.5l-38 35q23 34 56.5 55t73.5 21q59 0 94.5 -32t35.5 -94q0 -28 -11 -54t-30.5 -52.5t-46 -54.5t-57.5 -60h165v-55h-268z" />
<glyph horiz-adv-x="367" d="M180 -12q-48 0 -85 21.5t-60 54.5l43 33q18 -27 43 -42.5t56 -15.5q32 0 55 18t23 52t-32 51.5t-89 17.5v41q51 0 78.5 20t27.5 50q0 28 -19.5 45t-51.5 17q-22 0 -42 -13.5t-37 -33.5l-39 34q26 29 56.5 46.5t71.5 17.5q25 0 47.5 -7t39.5 -20.5t27 -33t10 -44.5 q0 -35 -19 -58.5t-49 -37.5q33 -8 58.5 -32.5t25.5 -63.5q0 -27 -11 -48.5t-30 -37t-44.5 -23.5t-53.5 -8z" />
<glyph horiz-adv-x="367" d="M220 0v104h-178v33l164 253h72v-240h58v-46h-58v-104h-58zM109 150h111v70l4 109h-4l-50 -81z" />
<glyph horiz-adv-x="0" d="M17 697l-152 115l47 55l142 -128z" />
<glyph horiz-adv-x="0" d="M-17 697l-37 42l142 128l47 -55z" />
<glyph horiz-adv-x="0" d="M-148 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93z" />
<glyph horiz-adv-x="0" d="M75 709q-28 0 -48 11t-36.5 24.5t-31 24.5t-32.5 11q-19 0 -32 -17t-16 -49l-56 4q3 57 30 90t72 33q28 0 48 -11t36.5 -24.5t31 -24.5t32.5 -11q40 0 48 66l56 -4q-3 -58 -30 -90.5t-72 -32.5z" />
<glyph horiz-adv-x="0" d="M-103 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14zM103 715q-22 0 -35.5 14t-13.5 35q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14z" />
<glyph horiz-adv-x="0" d="M0 699q-42 0 -69.5 24.5t-27.5 65.5q0 42 27.5 66.5t69.5 24.5t69.5 -24.5t27.5 -66.5q0 -41 -27.5 -65.5t-69.5 -24.5zM0 735q20 0 35.5 14t15.5 40t-15.5 40t-35.5 14q-22 0 -37 -14t-15 -40t15 -40t37 -14z" />
<glyph horiz-adv-x="0" d="M-87 -226l-8 40q63 5 86 16.5t23 33.5q0 20 -17 31t-60 17l44 91h53l-29 -67q35 -8 54 -24t19 -47q0 -44 -42.5 -65.5t-122.5 -25.5z" />
<hkern u1="&#x2f;" u2="&#xef;" k="-65" />
<hkern u1="&#x2f;" u2="&#xee;" k="-65" />
<hkern u1="&#x2f;" u2="&#xec;" k="-15" />
<hkern u1="F" u2="&#xef;" k="-36" />
<hkern u1="F" u2="&#xee;" k="-29" />
<hkern u1="V" u2="&#xef;" k="-64" />
<hkern u1="V" u2="&#xee;" k="-53" />
<hkern u1="V" u2="&#xed;" k="-13" />
<hkern u1="V" u2="&#xec;" k="-20" />
<hkern u1="x" u2="&#x3b;" k="-7" />
<hkern u1="x" u2="&#x2c;" k="-7" />
<hkern g1="backslash" 	g2="Eth" 	k="29" />
<hkern g1="backslash" 	g2="g" 	k="-33" />
<hkern g1="backslash" 	g2="j" 	k="-73" />
<hkern g1="backslash" 	g2="T" 	k="85" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="29" />
<hkern g1="backslash" 	g2="v" 	k="20" />
<hkern g1="backslash" 	g2="V" 	k="53" />
<hkern g1="backslash" 	g2="w" 	k="10" />
<hkern g1="backslash" 	g2="W" 	k="29" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="-13" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="73" />
<hkern g1="exclamdown" 	g2="j" 	k="-33" />
<hkern g1="exclamdown" 	g2="V" 	k="32" />
<hkern g1="exclamdown" 	g2="W" 	k="16" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="periodcentered" 	g2="T" 	k="64" />
<hkern g1="periodcentered" 	g2="V" 	k="26" />
<hkern g1="periodcentered" 	g2="Y,Yacute,Ydieresis" 	k="58" />
<hkern g1="periodcentered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="24" />
<hkern g1="periodcentered" 	g2="S" 	k="24" />
<hkern g1="periodcentered" 	g2="x" 	k="20" />
<hkern g1="periodcentered" 	g2="X" 	k="26" />
<hkern g1="periodcentered" 	g2="Z" 	k="38" />
<hkern g1="questiondown" 	g2="j" 	k="-59" />
<hkern g1="questiondown" 	g2="T" 	k="87" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="40" />
<hkern g1="questiondown" 	g2="V" 	k="58" />
<hkern g1="questiondown" 	g2="W" 	k="40" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="89" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="62" />
<hkern g1="questiondown" 	g2="S" 	k="35" />
<hkern g1="questiondown" 	g2="X" 	k="42" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="questiondown" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="62" />
<hkern g1="questiondown" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="21" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="44" />
<hkern g1="slash" 	g2="g" 	k="10" />
<hkern g1="slash" 	g2="j" 	k="-25" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="24" />
<hkern g1="slash" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="slash" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-25" />
<hkern g1="slash" 	g2="J" 	k="80" />
<hkern g1="slash" 	g2="t" 	k="-9" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="t" 	k="14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="T" 	k="24" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="V" 	k="16" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="W" 	k="4" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="Y,Yacute,Ydieresis" 	k="24" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="12" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="54" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="26" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="56" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="94" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="periodcentered" 	k="24" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="-23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="15" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="7" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="x" 	k="4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="-4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="7" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Z" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="67" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="40" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="registered" 	k="80" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="52" />
<hkern g1="B" 	g2="t" 	k="10" />
<hkern g1="B" 	g2="T" 	k="24" />
<hkern g1="B" 	g2="V" 	k="6" />
<hkern g1="B" 	g2="W" 	k="4" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="B" 	g2="asterisk" 	k="30" />
<hkern g1="B" 	g2="periodcentered" 	k="20" />
<hkern g1="B" 	g2="v" 	k="14" />
<hkern g1="B" 	g2="w" 	k="10" />
<hkern g1="B" 	g2="x" 	k="10" />
<hkern g1="B" 	g2="X" 	k="4" />
<hkern g1="B" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="B" 	g2="Z" 	k="4" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="B" 	g2="trademark" 	k="20" />
<hkern g1="B" 	g2="J" 	k="16" />
<hkern g1="B" 	g2="S" 	k="14" />
<hkern g1="c,ccedilla" 	g2="t" 	k="10" />
<hkern g1="c,ccedilla" 	g2="T" 	k="20" />
<hkern g1="c,ccedilla" 	g2="V" 	k="14" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="24" />
<hkern g1="c,ccedilla" 	g2="periodcentered" 	k="14" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="c,ccedilla" 	g2="v" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="x" 	k="-8" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="registered" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="g" 	k="10" />
<hkern g1="c,ccedilla" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="21" />
<hkern g1="c,ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="20" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="7" />
<hkern g1="C,Ccedilla" 	g2="periodcentered" 	k="52" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="registered" 	k="-10" />
<hkern g1="C,Ccedilla" 	g2="trademark" 	k="-24" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="S" 	k="24" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="26" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="24" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="26" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="7" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk" 	k="34" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="periodcentered" 	k="4" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="-5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="-5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="7" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="-5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="17" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="J" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="S" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="g" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="hyphen,uni00AD,endash,emdash" 	k="-10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="24" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="x" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="f,uniFB00" 	g2="T" 	k="-46" />
<hkern g1="f,uniFB00" 	g2="V" 	k="-66" />
<hkern g1="f,uniFB00" 	g2="W" 	k="-46" />
<hkern g1="f,uniFB00" 	g2="Y,Yacute,Ydieresis" 	k="-59" />
<hkern g1="f,uniFB00" 	g2="question" 	k="-26" />
<hkern g1="f,uniFB00" 	g2="quoteright,quotedblright" 	k="-34" />
<hkern g1="f,uniFB00" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="50" />
<hkern g1="f,uniFB00" 	g2="periodcentered" 	k="20" />
<hkern g1="f,uniFB00" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="f,uniFB00" 	g2="s" 	k="4" />
<hkern g1="f,uniFB00" 	g2="v" 	k="-13" />
<hkern g1="f,uniFB00" 	g2="x" 	k="4" />
<hkern g1="f,uniFB00" 	g2="X" 	k="-33" />
<hkern g1="f,uniFB00" 	g2="quoteleft,quotedblleft" 	k="-34" />
<hkern g1="f,uniFB00" 	g2="quotedbl,quotesingle" 	k="-40" />
<hkern g1="f,uniFB00" 	g2="backslash" 	k="-62" />
<hkern g1="f,uniFB00" 	g2="registered" 	k="-75" />
<hkern g1="f,uniFB00" 	g2="trademark" 	k="-76" />
<hkern g1="f,uniFB00" 	g2="g" 	k="14" />
<hkern g1="f,uniFB00" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="f,uniFB00" 	g2="hyphen,uni00AD,endash,emdash" 	k="14" />
<hkern g1="f,uniFB00" 	g2="j" 	k="10" />
<hkern g1="f,uniFB00" 	g2="z" 	k="14" />
<hkern g1="f,uniFB00" 	g2="parenright,bracketright,braceright" 	k="-49" />
<hkern g1="f,uniFB00" 	g2="exclam" 	k="-14" />
<hkern g1="f,uniFB00" 	g2="slash" 	k="14" />
<hkern g1="F" 	g2="t" 	k="10" />
<hkern g1="F" 	g2="V" 	k="-4" />
<hkern g1="F" 	g2="W" 	k="-4" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="72" />
<hkern g1="F" 	g2="periodcentered" 	k="14" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="34" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="F" 	g2="s" 	k="20" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="F" 	g2="v" 	k="20" />
<hkern g1="F" 	g2="w" 	k="16" />
<hkern g1="F" 	g2="x" 	k="26" />
<hkern g1="F" 	g2="X" 	k="24" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="F" 	g2="Z" 	k="30" />
<hkern g1="F" 	g2="registered" 	k="-20" />
<hkern g1="F" 	g2="trademark" 	k="-38" />
<hkern g1="F" 	g2="J" 	k="138" />
<hkern g1="F" 	g2="S" 	k="20" />
<hkern g1="F" 	g2="g" 	k="24" />
<hkern g1="F" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="F" 	g2="z" 	k="30" />
<hkern g1="F" 	g2="slash" 	k="69" />
<hkern g1="F" 	g2="m,n,p,r,ntilde,dotlessi" 	k="20" />
<hkern g1="germandbls" 	g2="t" 	k="16" />
<hkern g1="germandbls" 	g2="question" 	k="16" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="56" />
<hkern g1="germandbls" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="4" />
<hkern g1="germandbls" 	g2="v" 	k="16" />
<hkern g1="germandbls" 	g2="w" 	k="14" />
<hkern g1="germandbls" 	g2="x" 	k="-6" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="48" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="67" />
<hkern g1="germandbls" 	g2="backslash" 	k="26" />
<hkern g1="germandbls" 	g2="registered" 	k="35" />
<hkern g1="g" 	g2="T" 	k="26" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="g" 	g2="question" 	k="36" />
<hkern g1="g" 	g2="asterisk" 	k="33" />
<hkern g1="g" 	g2="periodcentered" 	k="8" />
<hkern g1="g" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="g" 	g2="v" 	k="4" />
<hkern g1="g" 	g2="w" 	k="4" />
<hkern g1="g" 	g2="y,yacute,ydieresis" 	k="-15" />
<hkern g1="g" 	g2="registered" 	k="-14" />
<hkern g1="g" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="g" 	g2="j" 	k="-37" />
<hkern g1="g" 	g2="z" 	k="14" />
<hkern g1="g" 	g2="parenright,bracketright,braceright" 	k="-14" />
<hkern g1="g" 	g2="slash" 	k="-48" />
<hkern g1="G" 	g2="T" 	k="20" />
<hkern g1="G" 	g2="V" 	k="14" />
<hkern g1="G" 	g2="W" 	k="4" />
<hkern g1="G" 	g2="asterisk" 	k="28" />
<hkern g1="G" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="G" 	g2="registered" 	k="-8" />
<hkern g1="G" 	g2="trademark" 	k="-12" />
<hkern g1="J" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="24" />
<hkern g1="J" 	g2="J" 	k="40" />
<hkern g1="k" 	g2="t" 	k="21" />
<hkern g1="k" 	g2="T" 	k="35" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="k" 	g2="question" 	k="12" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="26" />
<hkern g1="k" 	g2="asterisk" 	k="21" />
<hkern g1="k" 	g2="colon,semicolon" 	k="-13" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-13" />
<hkern g1="k" 	g2="periodcentered" 	k="26" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="k" 	g2="x" 	k="7" />
<hkern g1="k" 	g2="trademark" 	k="20" />
<hkern g1="k" 	g2="g" 	k="10" />
<hkern g1="k" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="k" 	g2="hyphen,uni00AD,endash,emdash" 	k="44" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="26" />
<hkern g1="k" 	g2="guillemotright,guilsinglright" 	k="11" />
<hkern g1="k" 	g2="j" 	k="10" />
<hkern g1="k" 	g2="z" 	k="7" />
<hkern g1="K" 	g2="t" 	k="33" />
<hkern g1="K" 	g2="T" 	k="17" />
<hkern g1="K" 	g2="V" 	k="12" />
<hkern g1="K" 	g2="W" 	k="10" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="K" 	g2="question" 	k="7" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="K" 	g2="asterisk" 	k="40" />
<hkern g1="K" 	g2="periodcentered" 	k="46" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="K" 	g2="v" 	k="20" />
<hkern g1="K" 	g2="w" 	k="16" />
<hkern g1="K" 	g2="x" 	k="16" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="K" 	g2="quotedbl,quotesingle" 	k="21" />
<hkern g1="K" 	g2="trademark" 	k="-9" />
<hkern g1="K" 	g2="S" 	k="9" />
<hkern g1="K" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="7" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="30" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="K" 	g2="j" 	k="10" />
<hkern g1="K" 	g2="z" 	k="14" />
<hkern g1="l,uniFB02" 	g2="j" 	k="-8" />
<hkern g1="L" 	g2="t" 	k="20" />
<hkern g1="L" 	g2="T" 	k="120" />
<hkern g1="L" 	g2="V" 	k="76" />
<hkern g1="L" 	g2="W" 	k="56" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="76" />
<hkern g1="L" 	g2="question" 	k="32" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="78" />
<hkern g1="L" 	g2="asterisk" 	k="152" />
<hkern g1="L" 	g2="periodcentered" 	k="92" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-3" />
<hkern g1="L" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="14" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="26" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="29" />
<hkern g1="L" 	g2="v" 	k="36" />
<hkern g1="L" 	g2="w" 	k="34" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="36" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="78" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="89" />
<hkern g1="L" 	g2="backslash" 	k="80" />
<hkern g1="L" 	g2="registered" 	k="92" />
<hkern g1="L" 	g2="trademark" 	k="98" />
<hkern g1="L" 	g2="S" 	k="22" />
<hkern g1="L" 	g2="g" 	k="4" />
<hkern g1="L" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="L" 	g2="hyphen,uni00AD,endash,emdash" 	k="54" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="34" />
<hkern g1="h,m,n,ntilde" 	g2="T" 	k="24" />
<hkern g1="h,m,n,ntilde" 	g2="V" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="7" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk" 	k="34" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="20" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="t" 	k="17" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="57" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V" 	k="19" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="46" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="question" 	k="12" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="26" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk" 	k="25" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="w" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="17" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="X" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="y,yacute,ydieresis" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="29" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="26" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="registered" 	k="7" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="hyphen,uni00AD,endash,emdash" 	k="-6" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="6" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="24" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="6" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="asterisk" 	k="34" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="14" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="17" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="registered" 	k="-6" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="26" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="40" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="7" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="18" />
<hkern g1="P" 	g2="T" 	k="24" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="112" />
<hkern g1="P" 	g2="periodcentered" 	k="8" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="43" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="50" />
<hkern g1="P" 	g2="s" 	k="10" />
<hkern g1="P" 	g2="x" 	k="14" />
<hkern g1="P" 	g2="X" 	k="24" />
<hkern g1="P" 	g2="Z" 	k="78" />
<hkern g1="P" 	g2="registered" 	k="-30" />
<hkern g1="P" 	g2="J" 	k="146" />
<hkern g1="P" 	g2="S" 	k="10" />
<hkern g1="P" 	g2="g" 	k="30" />
<hkern g1="P" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="24" />
<hkern g1="P" 	g2="hyphen,uni00AD,endash,emdash" 	k="33" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="P" 	g2="z" 	k="20" />
<hkern g1="P" 	g2="slash" 	k="75" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-18" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-25" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="57" />
<hkern g1="r" 	g2="periodcentered" 	k="16" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="24" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="r" 	g2="s" 	k="4" />
<hkern g1="r" 	g2="v" 	k="-25" />
<hkern g1="r" 	g2="w" 	k="-19" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-25" />
<hkern g1="r" 	g2="Z" 	k="10" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-33" />
<hkern g1="r" 	g2="backslash" 	k="-13" />
<hkern g1="r" 	g2="registered" 	k="-62" />
<hkern g1="r" 	g2="J" 	k="55" />
<hkern g1="r" 	g2="g" 	k="10" />
<hkern g1="r" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="r" 	g2="hyphen,uni00AD,endash,emdash" 	k="24" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="r" 	g2="z" 	k="4" />
<hkern g1="r" 	g2="slash" 	k="34" />
<hkern g1="R" 	g2="T" 	k="14" />
<hkern g1="R" 	g2="V" 	k="-5" />
<hkern g1="R" 	g2="W" 	k="-6" />
<hkern g1="R" 	g2="asterisk" 	k="10" />
<hkern g1="R" 	g2="periodcentered" 	k="10" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="4" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="R" 	g2="x" 	k="7" />
<hkern g1="R" 	g2="X" 	k="4" />
<hkern g1="R" 	g2="Z" 	k="10" />
<hkern g1="R" 	g2="registered" 	k="-25" />
<hkern g1="R" 	g2="J" 	k="18" />
<hkern g1="R" 	g2="S" 	k="14" />
<hkern g1="R" 	g2="g" 	k="4" />
<hkern g1="R" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="R" 	g2="hyphen,uni00AD,endash,emdash" 	k="31" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="30" />
<hkern g1="R" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="R" 	g2="z" 	k="7" />
<hkern g1="s" 	g2="t" 	k="20" />
<hkern g1="s" 	g2="T" 	k="24" />
<hkern g1="s" 	g2="V" 	k="10" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="s" 	g2="question" 	k="11" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="11" />
<hkern g1="s" 	g2="asterisk" 	k="49" />
<hkern g1="s" 	g2="hyphen,uni00AD,endash,emdash" 	k="-10" />
<hkern g1="S" 	g2="t" 	k="24" />
<hkern g1="S" 	g2="T" 	k="20" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="11" />
<hkern g1="S" 	g2="asterisk" 	k="16" />
<hkern g1="S" 	g2="periodcentered" 	k="11" />
<hkern g1="S" 	g2="registered" 	k="-3" />
<hkern g1="S" 	g2="J" 	k="14" />
<hkern g1="S" 	g2="S" 	k="14" />
<hkern g1="S" 	g2="hyphen,uni00AD,endash,emdash" 	k="-14" />
<hkern g1="Thorn" 	g2="asterisk" 	k="62" />
<hkern g1="Thorn" 	g2="backslash" 	k="40" />
<hkern g1="Thorn" 	g2="trademark" 	k="24" />
<hkern g1="Thorn" 	g2="slash" 	k="58" />
<hkern g1="t" 	g2="t" 	k="20" />
<hkern g1="t" 	g2="T" 	k="14" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="4" />
<hkern g1="t" 	g2="question" 	k="26" />
<hkern g1="t" 	g2="colon,semicolon" 	k="-14" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-7" />
<hkern g1="t" 	g2="periodcentered" 	k="20" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="21" />
<hkern g1="t" 	g2="s" 	k="10" />
<hkern g1="t" 	g2="x" 	k="16" />
<hkern g1="t" 	g2="registered" 	k="-34" />
<hkern g1="t" 	g2="g" 	k="10" />
<hkern g1="t" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="t" 	g2="hyphen,uni00AD,endash,emdash" 	k="26" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="t" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="t" 	g2="slash" 	k="-10" />
<hkern g1="T" 	g2="t" 	k="18" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="106" />
<hkern g1="T" 	g2="periodcentered" 	k="64" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="73" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="T" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="18" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="24" />
<hkern g1="T" 	g2="s" 	k="59" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="46" />
<hkern g1="T" 	g2="v" 	k="33" />
<hkern g1="T" 	g2="w" 	k="34" />
<hkern g1="T" 	g2="x" 	k="39" />
<hkern g1="T" 	g2="X" 	k="20" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="T" 	g2="Z" 	k="54" />
<hkern g1="T" 	g2="registered" 	k="-20" />
<hkern g1="T" 	g2="trademark" 	k="-34" />
<hkern g1="T" 	g2="J" 	k="126" />
<hkern g1="T" 	g2="S" 	k="39" />
<hkern g1="T" 	g2="g" 	k="73" />
<hkern g1="T" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="66" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="73" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="47" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="40" />
<hkern g1="T" 	g2="z" 	k="75" />
<hkern g1="T" 	g2="slash" 	k="89" />
<hkern g1="T" 	g2="m,n,p,r,ntilde,dotlessi" 	k="46" />
<hkern g1="T" 	g2="AE" 	k="85" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,dotlessi" 	g2="T" 	k="20" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,dotlessi" 	g2="V" 	k="14" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,dotlessi" 	g2="Y,Yacute,Ydieresis" 	k="30" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,dotlessi" 	g2="asterisk" 	k="24" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="4" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="4" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="4" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="7" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="47" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="S" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="31" />
<hkern g1="v" 	g2="T" 	k="20" />
<hkern g1="v" 	g2="V" 	k="9" />
<hkern g1="v" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="v" 	g2="asterisk" 	k="11" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="v" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="11" />
<hkern g1="v" 	g2="Z" 	k="10" />
<hkern g1="v" 	g2="registered" 	k="-45" />
<hkern g1="v" 	g2="trademark" 	k="-9" />
<hkern g1="v" 	g2="J" 	k="40" />
<hkern g1="v" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="v" 	g2="hyphen,uni00AD,endash,emdash" 	k="7" />
<hkern g1="v" 	g2="j" 	k="10" />
<hkern g1="v" 	g2="z" 	k="24" />
<hkern g1="v" 	g2="slash" 	k="20" />
<hkern g1="V" 	g2="V" 	k="-10" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-13" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="65" />
<hkern g1="V" 	g2="periodcentered" 	k="11" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="22" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="V" 	g2="s" 	k="12" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="30" />
<hkern g1="V" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="V" 	g2="v" 	k="9" />
<hkern g1="V" 	g2="w" 	k="9" />
<hkern g1="V" 	g2="x" 	k="15" />
<hkern g1="V" 	g2="y,yacute,ydieresis" 	k="9" />
<hkern g1="V" 	g2="Z" 	k="19" />
<hkern g1="V" 	g2="registered" 	k="-53" />
<hkern g1="V" 	g2="trademark" 	k="-54" />
<hkern g1="V" 	g2="J" 	k="73" />
<hkern g1="V" 	g2="S" 	k="10" />
<hkern g1="V" 	g2="g" 	k="21" />
<hkern g1="V" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="15" />
<hkern g1="V" 	g2="hyphen,uni00AD,endash,emdash" 	k="19" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="21" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="19" />
<hkern g1="V" 	g2="z" 	k="25" />
<hkern g1="V" 	g2="slash" 	k="47" />
<hkern g1="V" 	g2="m,n,p,r,ntilde,dotlessi" 	k="20" />
<hkern g1="w" 	g2="T" 	k="24" />
<hkern g1="w" 	g2="V" 	k="9" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="24" />
<hkern g1="w" 	g2="asterisk" 	k="11" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="w" 	g2="X" 	k="14" />
<hkern g1="w" 	g2="registered" 	k="-38" />
<hkern g1="w" 	g2="trademark" 	k="-10" />
<hkern g1="w" 	g2="J" 	k="30" />
<hkern g1="w" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="w" 	g2="j" 	k="10" />
<hkern g1="w" 	g2="z" 	k="17" />
<hkern g1="w" 	g2="slash" 	k="8" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-13" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="34" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="W" 	g2="Z" 	k="6" />
<hkern g1="W" 	g2="registered" 	k="-39" />
<hkern g1="W" 	g2="trademark" 	k="-34" />
<hkern g1="W" 	g2="J" 	k="65" />
<hkern g1="W" 	g2="S" 	k="6" />
<hkern g1="W" 	g2="g" 	k="13" />
<hkern g1="W" 	g2="hyphen,uni00AD,endash,emdash" 	k="10" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="W" 	g2="z" 	k="10" />
<hkern g1="W" 	g2="slash" 	k="29" />
<hkern g1="x" 	g2="t" 	k="24" />
<hkern g1="x" 	g2="T" 	k="33" />
<hkern g1="x" 	g2="V" 	k="15" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="34" />
<hkern g1="x" 	g2="asterisk" 	k="23" />
<hkern g1="x" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="7" />
<hkern g1="x" 	g2="periodcentered" 	k="20" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="x" 	g2="X" 	k="10" />
<hkern g1="x" 	g2="registered" 	k="-38" />
<hkern g1="x" 	g2="trademark" 	k="-9" />
<hkern g1="x" 	g2="S" 	k="4" />
<hkern g1="x" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="17" />
<hkern g1="x" 	g2="hyphen,uni00AD,endash,emdash" 	k="14" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="24" />
<hkern g1="x" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="x" 	g2="exclam" 	k="14" />
<hkern g1="X" 	g2="t" 	k="16" />
<hkern g1="X" 	g2="T" 	k="20" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="7" />
<hkern g1="X" 	g2="asterisk" 	k="10" />
<hkern g1="X" 	g2="periodcentered" 	k="40" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-3" />
<hkern g1="X" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="16" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="17" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="X" 	g2="v" 	k="16" />
<hkern g1="X" 	g2="w" 	k="14" />
<hkern g1="X" 	g2="x" 	k="10" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="7" />
<hkern g1="X" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="X" 	g2="registered" 	k="-8" />
<hkern g1="X" 	g2="trademark" 	k="-21" />
<hkern g1="X" 	g2="S" 	k="14" />
<hkern g1="X" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="7" />
<hkern g1="X" 	g2="hyphen,uni00AD,endash,emdash" 	k="27" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="X" 	g2="z" 	k="14" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="V" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="7" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="x" 	k="14" />
<hkern g1="y,yacute,ydieresis" 	g2="X" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="registered" 	k="-45" />
<hkern g1="y,yacute,ydieresis" 	g2="trademark" 	k="-13" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="40" />
<hkern g1="y,yacute,ydieresis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="j" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="z" 	k="24" />
<hkern g1="y,yacute,ydieresis" 	g2="slash" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="28" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="7" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-9" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="25" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="91" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="periodcentered" 	k="46" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="67" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="34" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="24" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="34" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="26" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="registered" 	k="-33" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="trademark" 	k="-46" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="60" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="55" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="67" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde,dotlessi" 	k="40" />
<hkern g1="z" 	g2="T" 	k="26" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="29" />
<hkern g1="z" 	g2="periodcentered" 	k="11" />
<hkern g1="z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="23" />
<hkern g1="z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="z" 	g2="v" 	k="4" />
<hkern g1="z" 	g2="y,yacute,ydieresis" 	k="4" />
<hkern g1="z" 	g2="registered" 	k="-25" />
<hkern g1="z" 	g2="trademark" 	k="-13" />
<hkern g1="z" 	g2="g" 	k="6" />
<hkern g1="z" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="z" 	g2="hyphen,uni00AD,endash,emdash" 	k="16" />
<hkern g1="Z" 	g2="t" 	k="14" />
<hkern g1="Z" 	g2="V" 	k="6" />
<hkern g1="Z" 	g2="W" 	k="6" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="Z" 	g2="periodcentered" 	k="60" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="Z" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="Z" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="20" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="23" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="Z" 	g2="v" 	k="16" />
<hkern g1="Z" 	g2="w" 	k="16" />
<hkern g1="Z" 	g2="x" 	k="24" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="Z" 	g2="Z" 	k="16" />
<hkern g1="Z" 	g2="registered" 	k="-20" />
<hkern g1="Z" 	g2="trademark" 	k="-20" />
<hkern g1="Z" 	g2="J" 	k="33" />
<hkern g1="Z" 	g2="S" 	k="30" />
<hkern g1="Z" 	g2="g" 	k="14" />
<hkern g1="Z" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="21" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="34" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="34" />
<hkern g1="uni03BC" 	g2="colon,semicolon" 	k="-7" />
<hkern g1="uni03BC" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-7" />
<hkern g1="uni03BC" 	g2="quoteright,quotedblright" 	k="27" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="j" 	k="-80" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="J" 	k="20" />
<hkern g1="colon,semicolon" 	g2="j" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="26" />
<hkern g1="colon,semicolon" 	g2="asterisk" 	k="49" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-26" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk" 	k="138" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="11" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="t" 	k="46" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="99" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="65" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="22" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="34" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="84" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="104" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="96" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="J" 	k="20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="66" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="t" 	k="26" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="47" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="v" 	k="7" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="V" 	k="19" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="W" 	k="10" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="S" 	k="17" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="x" 	k="14" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="X" 	k="27" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Z" 	k="20" />
<hkern g1="exclam" 	g2="quoteright,quotedblright" 	k="32" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t" 	k="14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="40" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="v" 	k="4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="17" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="17" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="y,yacute,ydieresis" 	k="4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="x" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="30" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="55" />
<hkern g1="guillemotright,guilsinglright" 	g2="t" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="46" />
<hkern g1="guillemotright,guilsinglright" 	g2="v" 	k="7" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="21" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="7" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="30" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="24" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="18" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="21" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="75" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-9" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-13" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-13" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="7" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="85" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="43" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="56" />
<hkern g1="quoteleft,quotedblleft" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="83" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclamdown" 	k="53" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="133" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="58" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="7" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="85" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="65" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="58" />
<hkern g1="quoteright,quotedblright" 	g2="f,uniFB00,uniFB01,uniFB02" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="45" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="132" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde,dotlessi" 	k="13" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="26" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="95" />
<hkern g1="quotedbl,quotesingle" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="quotedbl,quotesingle" 	g2="S" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="X" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="55" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="115" />
</font>
</defs></svg> 