/**
 * Tests for testUtils utility functions
 */

import { getTestId, getTestIdSafe, getTestAttributes } from '../testUtils';

describe('testUtils', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('getTestId', () => {
    it('should return data-testid in non-production environment', () => {
      process.env.TARGET_ENV = 'development';
      const result = getTestId('test-component');
      expect(result).toEqual({ 'data-testid': 'test-component' });
    });

    it('should return empty object in production environment', () => {
      process.env.TARGET_ENV = 'prod';
      const result = getTestId('test-component');
      expect(result).toEqual({});
    });

    it('should return data-testid when TARGET_ENV is not set', () => {
      delete process.env.TARGET_ENV;
      const result = getTestId('test-component');
      expect(result).toEqual({ 'data-testid': 'test-component' });
    });

    it('should return empty object when TARGET_ENV contains prod', () => {
      process.env.TARGET_ENV = 'staging-prod';
      const result = getTestId('test-component');
      expect(result).toEqual({});
    });
  });

  describe('getTestIdSafe', () => {
    it('should return data-testid in development environment', () => {
      process.env.TARGET_ENV = 'development';
      process.env.NODE_ENV = 'development';
      const result = getTestIdSafe('test-component');
      expect(result).toEqual({ 'data-testid': 'test-component' });
    });

    it('should return empty object when NODE_ENV is production', () => {
      process.env.TARGET_ENV = 'development';
      process.env.NODE_ENV = 'production';
      const result = getTestIdSafe('test-component');
      expect(result).toEqual({});
    });

    it('should return empty object when TARGET_ENV contains prod', () => {
      process.env.TARGET_ENV = 'prod';
      process.env.NODE_ENV = 'development';
      const result = getTestIdSafe('test-component');
      expect(result).toEqual({});
    });
  });

  describe('getTestAttributes', () => {
    it('should return attributes in non-production environment', () => {
      process.env.TARGET_ENV = 'development';
      process.env.NODE_ENV = 'development';
      const attributes = { 'data-testid': 'test', 'data-qa': 'component' };
      const result = getTestAttributes(attributes);
      expect(result).toEqual(attributes);
    });

    it('should return empty object in production environment', () => {
      process.env.TARGET_ENV = 'prod';
      process.env.NODE_ENV = 'production';
      const attributes = { 'data-testid': 'test', 'data-qa': 'component' };
      const result = getTestAttributes(attributes);
      expect(result).toEqual({});
    });
  });
});
