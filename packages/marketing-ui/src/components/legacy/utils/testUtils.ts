/**
 * Utility functions for handling test-related attributes and functionality
 */

/**
 * Conditionally adds data-testid attribute based on environment.
 * In production builds, this returns an empty object to ensure test IDs are stripped out.
 * In non-production environments (development, staging, test), it returns the data-testid attribute.
 * 
 * @param testId - The test ID to use for the data-testid attribute
 * @returns Object containing data-testid attribute or empty object
 * 
 * @example
 * // In development/test environments:
 * <div {...getTestId('my-component')} /> // renders: <div data-testid="my-component" />
 * 
 * // In production:
 * <div {...getTestId('my-component')} /> // renders: <div />
 */
export const getTestId = (testId: string): { 'data-testid'?: string } => {
  // Check if we're in a production environment
  // TARGET_ENV is set by the build process and contains 'prod' for production builds
  const isProduction = (process.env.TARGET_ENV || '').includes('prod');
  
  // Only include data-testid in non-production environments
  return isProduction ? {} : { 'data-testid': testId };
};

/**
 * Alternative utility that checks NODE_ENV as a fallback
 * This provides additional safety in case TARGET_ENV is not set
 */
export const getTestIdSafe = (testId: string): { 'data-testid'?: string } => {
  const isProduction = (process.env.TARGET_ENV || '').includes('prod') || 
                      process.env.NODE_ENV === 'production';
  
  return isProduction ? {} : { 'data-testid': testId };
};

/**
 * Utility for conditional test attributes beyond just data-testid
 * Useful for adding multiple test-related attributes conditionally
 */
export const getTestAttributes = (attributes: Record<string, string>): Record<string, string> => {
  const isProduction = (process.env.TARGET_ENV || '').includes('prod') || 
                      process.env.NODE_ENV === 'production';
  
  return isProduction ? {} : attributes;
};
