'use client';
import { <PERSON>a, StoryObj } from '@storybook/react';
import React from 'react';
import { NullJSX, UnsupportedByBrand } from '../../stories/story-helpers';
import { FeaturedCategoriesData } from './utils';
import { twoFramesData, fourFramesData, eightFramesData, videoTileTwoFrames, videoTileFourFrames, videoTileEightFrames } from './fixtures/test-data';
import FeaturedCategories from './index';

type ComponentTypeForStory = (props: { data: FeaturedCategoriesData }) => React.JSX.Element | null;

type Story = StoryObj<ComponentTypeForStory>;

const meta: Meta<ComponentTypeForStory> = {
  argTypes: {
    data: {
      name: 'Amplience Data',
      description: 'The JSON payload from Amplience',
      control: {
        type: 'object',
      },
    },
  },
  title: 'Common/JSON Components (Marketing)/Rapid Authoring/FeaturedCategories',
  component: ({ data }) => <FeaturedCategories {...data} />,
  parameters: {
    // docs: { page: README }, TODO: add README.md
    layout: 'fullscreen',
    knobs: {
      disabled: true,
    },
  },
  render: ({ data }) => {
    const content = <FeaturedCategories {...data} />;
    return (
      <NullJSX content={content}>
        <UnsupportedByBrand />
      </NullJSX>
    );
  },
  tags: ['exclude'],
};

export default meta;

export const twoFrames: Story = {
  args: { data: twoFramesData },
};

export const fourFrames: Story = {
  args: { data: fourFramesData },
};

export const eightFrames: Story = {
  args: { data: eightFramesData },
};

export const twoVideoFrames: Story = {
  args: { data: videoTileTwoFrames },
};

export const fourVideoFrames: Story = {
  args: { data: videoTileFourFrames },
};

export const eightVideoFrames: Story = {
  args: { data: videoTileEightFrames },
};
