import { ImageTileContent } from '../utils';

export const baseData = {
  _meta: {
    name: 'image car - 15%off',
    schema: 'https://cms.gap.com/schema/content/v1/mobileapp-image-tile.json',
    deliveryId: 'd8c99923-4244-45e0-b963-5b9bc1ca6369',
  },
  styling: {
    padding: {
      desktop: 20,
      mobile: 10,
    },
  },
  type: 'imageTile',
  title: '15% off',
  platforms: {
    android: true,
    ios: true,
  },
  tags: ['3b7fd336-4fa9-46c4-b98c-0dd0ad38d348'],
};

export const desktopImageData: ImageTileContent['imageTabs']['image'] = {
  url: 'https://dam-test.gap.orangelogic.com/IIIF3/Image/30k512w1g28164fxd24tk327gt0v6jg6/full/max/0.0/default.jpg',
  dimensions: [
    {
      usage: 'image',
      type: 'https://dam-test.gap.orangelogic.com/IIIF3/Image/30k512w1g28164fxd24tk327gt0v6jg6/full/max/0.0/default.jpg',
      width: 2510,
      height: 1130,
    },
  ],
  hotspots: [
    {
      type: 'url',
      coordinates: {
        x1: 0,
        x2: 100,
        y1: 0,
        y2: 100,
      },
      name: 'Go Short',
      url: 'https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1',
    },
    {
      type: 'text',
      coordinates: {
        x1: 85,
        x2: 96,
        y1: 80,
        y2: 92,
      },
      text: 'Lorem ipsum dolor sit amet, \n\n\nconsectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
      name: 'Modal Title',
      url: 'https://athleta.gap.com/browse/division.do?cid=1054832',
    },
  ],
};

export const mobileImageData: ImageTileContent['imageTabs']['image'] = {
  url: 'https://dam-test.gap.orangelogic.com/IIIF3/Image/0gvgd466035075b1gqc7372pjl6ua70e/full/max/0.0/default.jpg',
  dimensions: [
    {
      usage: 'image',
      type: 'https://dam-test.gap.orangelogic.com/IIIF3/Image/0gvgd466035075b1gqc7372pjl6ua70e/full/max/0.0/default.jpg',
      width: 1094,
      height: 1460,
    },
  ],
  hotspots: [
    {
      type: 'url',
      coordinates: {
        x1: 0,
        x2: 100,
        y1: 2,
        y2: 100,
      },
      url: 'https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1',
      name: 'Go Short',
    },
  ],
};

export const bothImagesData = {
  ...baseData,
  imageTabs: {
    desktopImage: desktopImageData,
    image: mobileImageData,
    accessibilityAltText: 'a woman in a tan top and shorts is posing for a photo',
    desktopAccessibilityAltText: 'a woman in a tan skirt and top is posing for a magazine',
  },
};

export const carouselData = {
  ...baseData,
  carouselSettings: {
    autoplay: true,
    looping: 'infinite',
    rotationTime: 2000,
    transitionTime: 0,
    transitionType: 'fade',
  },
  imageTabs: {
    desktopImage: desktopImageData,
    image: mobileImageData,
    accessibilityAltText: 'a woman in a tan top and shorts is posing for a photo',
    desktopAccessibilityAltText: 'a woman in a tan skirt and top is posing for a magazine',

    desktopCarousel: [
      {
        desktopImage: {
          url: 'https://cdn.media.amplience.net/i/athleta/HOL1_XL_TEST_F3?fmt=auto',
          dimensions: [
            {
              usage: 'image',
              type: 'https://cdn.media.amplience.net/i/athleta/HOL1_XL_TEST_F3?fmt=auto',
              width: 2880,
              height: 1620,
            },
          ],
        },
        desktopCarouselAltText: 'a woman in white outfit walking on a pier with a blue hat',
      },
    ],
    mobileCarousel: [
      {
        mobileImage: {
          url: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
          dimensions: [
            {
              usage: 'image',
              type: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
              width: 336,
              height: 408,
            },
          ],
        },
        mobileCarouselAltText: 'a woman in a blue shirt is doing a yoga pose',
      },
      {
        mobileImage: {
          url: 'https://cdn.media.amplience.net/i/athleta/HOL1_S_TEST_F2?fmt=auto',
          dimensions: [
            {
              usage: 'image',
              type: 'https://cdn.media.amplience.net/i/athleta/HOL1_S_TEST_F2?fmt=auto',
              width: 750,
              height: 1334,
            },
          ],
        },
        mobileCarouselAltText: 'two women walking down a street together, one with a bag',
      },
      {
        mobileImage: {
          url: 'https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto',
          dimensions: [
            {
              usage: 'image',
              type: 'https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto',
              width: 750,
              height: 1334,
            },
          ],
        },
        mobileCarouselAltText: 'a woman in a vest and pants standing in front of a wall',
      },
    ],
  },
};

export const desktopOnlyData: ImageTileContent = {
  ...baseData,
  imageTabs: {
    desktopImage: desktopImageData,
    desktopAccessibilityAltText: 'a woman in a tan skirt and top is posing for a magazine',
  },
};

export const mobileOnlyData: ImageTileContent = {
  ...baseData,
  imageTabs: {
    image: mobileImageData,
    accessibilityAltText: 'a woman in a tan top and shorts is posing for a photo',
  },
};
