// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Rapid authoring - ImageTile desktop should render the expected snapshot 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="w-full"
      style="padding-bottom: 20px;"
    >
      <div
        class="relative w-full"
      >
        <img
          alt="a woman in a tan skirt and top is posing for a magazine"
          class="w-full"
          src="https://dam-test.gap.orangelogic.com/IIIF3/Image/30k512w1g28164fxd24tk327gt0v6jg6/full/max/0.0/default.jpg"
          usemap="#hotspotMap-:r0:"
        />
        <map
          name="hotspotMap-:r0:"
          role="group"
        >
          <area
            alt="Modal Title"
            coords="0,0,0,0"
            data-hotspot-type="text"
            data-testid="hotspot-Modal Title"
            href="#"
            shape="rect"
            tabindex="0"
            title="Modal Title"
          />
          <area
            alt="Go Short"
            coords="0,0,0,0"
            data-hotspot-type="url"
            data-testid="hotspot-Go Short"
            href="https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1"
            rel="noopener noreferrer"
            shape="rect"
            tabindex="0"
            target="_blank"
            title="Go Short"
          />
        </map>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rapid authoring - ImageTile mobile should render the expected snapshot 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="w-full"
      style="padding-bottom: 10px;"
    >
      <div
        class="relative w-full"
      >
        <img
          alt="a woman in a tan top and shorts is posing for a photo"
          class="w-full"
          src="https://dam-test.gap.orangelogic.com/IIIF3/Image/0gvgd466035075b1gqc7372pjl6ua70e/full/max/0.0/default.jpg"
          usemap="#hotspotMap-:r8:"
        />
        <map
          name="hotspotMap-:r8:"
          role="group"
        >
          <area
            alt="Go Short"
            coords="0,0,0,0"
            data-hotspot-type="url"
            data-testid="hotspot-Go Short"
            href="https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1"
            rel="noopener noreferrer"
            shape="rect"
            tabindex="0"
            target="_blank"
            title="Go Short"
          />
        </map>
      </div>
    </div>
  </div>
</div>
`;
