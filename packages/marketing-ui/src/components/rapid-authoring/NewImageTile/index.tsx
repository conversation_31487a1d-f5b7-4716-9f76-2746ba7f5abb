// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { PlayActive, PauseActive } from '../../legacy/components/VideoComponent/components/icons/index';

import { ImageTileContent } from './utils';
import NewTile from './ImageTile';

const ImageTileCarousel = (serverResponse: ImageTileContent) => {
  const [currentSlideIdx, setCurrentSlideIdx] = useState(0);
  const { smallerThan } = useContext(BreakpointContext);
  const isMobile = smallerThan(XLARGE);
  const [isPlaying, setIsPlaying] = useState(true);
  const carouselSlides = isMobile ? serverResponse.imageTabs.mobileCarousel : serverResponse.imageTabs.desktopCarousel;
  const { carouselSettings = {} } = serverResponse;

  function getImageData(carouselSlides) {
    const finalSlides = [];
    if (!carouselSlides) return [];
    carouselSlides?.forEach(slide => {
      const serverResponseClone = structuredClone(serverResponse);
      if (isMobile) {
        serverResponseClone.imageTabs.image.url = slide?.mobileImage?.url;
        serverResponseClone.imageTabs.image.dimensions = slide?.mobileImage?.dimensions;
        serverResponseClone.imageTabs.accessibilityAltText = slide.mobileCarouselAltText as string;
      } else {
        serverResponseClone.imageTabs.desktopImage.url = slide?.desktopImage?.url;
        serverResponseClone.imageTabs.desktopImage.dimensions = slide?.desktopImage?.dimensions;
        serverResponseClone.imageTabs.desktopAccessibilityAltText = slide.desktopCarouselAltText as string;
      }

      finalSlides.push(serverResponseClone);
    });
    return finalSlides;
  }

  const slides = [serverResponse, ...getImageData(carouselSlides)];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlideIdx(currentSlideIdx === slides.length - 1 ? 0 : currentSlideIdx + 1);
    }, carouselSettings.rotationTime);

    if (!isPlaying) {
      clearInterval(interval);
      return;
    }
    return () => {
      clearInterval(interval);
    };
  }, [slides.length, currentSlideIdx, setCurrentSlideIdx, isPlaying]);

  const fadeStyles = (slideIdx: number, currentSlideIdx) => ({
    backgroundColor: '#fff',
    opacity: slideIdx === currentSlideIdx ? 1 : 0,
    pointerEvents: slideIdx === currentSlideIdx ? 'auto' : 'none',
    zIndex: slideIdx === currentSlideIdx ? 2 : 1,
    transitionDuration: `${carouselSettings?.transitionTime}ms`,
    position: slideIdx === currentSlideIdx ? 'static' : 'absolute',
  });

  return slides.length === 1 ? (
    <NewTile {...serverResponse} />
  ) : (
    <div className='relative mx-auto bg-white'>
      {slides.map((slide, slideIdx) => (
        <div
          key={slideIdx}
          className={`absolute inset-0 ${carouselSettings?.transitionType === 'fade' ? 'transition-opacity' : ''}`}
          // TODO: refactor these to use tailwind classes
          style={carouselSettings?.transitionType === 'fade' ? fadeStyles(slideIdx, currentSlideIdx) : {}}
        >
          <NewTile {...slide} />
          <button
            aria-label={isPlaying ? 'Pause' : 'Play'}
            onClick={() => setIsPlaying(!isPlaying)}
            style={{
              position: 'absolute',
              bottom: isMobile ? 20 : 30,
              left: isMobile ? 'auto' : 15,
              right: isMobile ? 15 : 'auto',
              background: 'transparent',
              border: 'none',
              cursor: 'pointer',
              zIndex: 3,
              display: 'block',
              opacity: 0.64,
            }}
          >
            {carouselSettings?.autoplay && carouselSettings?.looping === 'infinite' && (isPlaying ? <PauseActive /> : <PlayActive />)}
          </button>
        </div>
      ))}
    </div>
  );
};

export default ImageTileCarousel;
