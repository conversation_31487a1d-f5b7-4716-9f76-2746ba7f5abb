'use client';

export type Dimension = {
  usage: string;
  type: string;
  width: number;
  height: number;
};

export type BaseHotspot = {
  coordinates: {
    x1: number;
    x2: number;
    y1: number;
    y2: number;
  };
  name: string;
  isSmallest?: boolean;
};

export type UrlHotspot = BaseHotspot & {
  type: 'url';
  url: string;
};

export type TextHotspot = BaseHotspot & {
  type: 'text';
  text: string;
};

export type PemoleCodeHotspot = BaseHotspot & {
  type: 'pemoleCode';
  pemoleCode: string;
};

export type LegalHotspot = BaseHotspot & {
  type: 'legal';
  pemoleCode: string;
};

export type Hotspot = UrlHotspot | TextHotspot | PemoleCodeHotspot | LegalHotspot;

export type ImageData = {
  url: string;
  dimensions: Dimension[];
  hotspots: Hotspot[];
};

export type ImageTabs = {
  desktopImage?: ImageData;
  image?: ImageData;
  mobileCarousel?: Array<{
    mobileImage: ImageData;
    mobileCarouselAltText: string;
  }>;
  desktopCarousel?: Array<{
    desktopImage: ImageData;
    desktopCarouselAltText: string;
  }>;

  accessibilityAltText?: string;
  desktopAccessibilityAltText?: string;
};

export type Styling = {
  padding: {
    desktop: number;
    mobile: number;
  };
};

export type ImageTileContent = {
  _meta: {
    name: string;
    schema: string;
    deliveryId: string;
  };
  imageTabs: ImageTabs;
  carouselSettings?: {
    autoplay: boolean;
    looping: string; // TODO: refactor looping to stricter type
    rotationTime: number;
    transitionTime: number;
    transitionType: string; // TODO: refactor transitionType to stricter type
  };
  styling: Styling;
  linkTypeValue?: string;
  linkType?: string;
  type: string;
  title: string;
  platforms: {
    android: boolean;
    ios: boolean;
  };
  tags: string[];
};

export enum Brands {
  Athleta = 'at',
  BananaRepublic = 'br',
  BananaRepublicFactoryStore = 'brfs',
  Gap = 'gap',
  GapFactoryStore = 'gapfs',
  OldNavy = 'on',
}
