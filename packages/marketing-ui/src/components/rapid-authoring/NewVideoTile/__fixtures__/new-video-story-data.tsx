export const newVideoStoryData = {
  marketingType: 'home',
  styling: {
    padding: {
      desktop: 20,
      mobile: 10,
    },
  },
  link: {
    legal: {
      detailsText: '*Details',
      textColor: 'light',
      pemoleCode: '1101657',
    },
    linkType: 'url',
    linkTypeValue: 'https://athleta.gap.com/browse/new/shop-by-color?cid=1144219&mlink=1,1,Site_HP_Spotlight__Wildberry_053025',
  },
  video: {
    desktop: {
      fallbackImage: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/7mj287dj28dal8160s8knwnwp5ow0qho.webp',
        dimensions: [
          {
            usage: 'image',
            type: 'https://dam-test.gap.orangelogic.com/AssetLink/7mj287dj28dal8160s8knwnwp5ow0qho.webp',
            width: 2880,
            height: 1300,
          },
        ],
      },
      video: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/v5445hb8hyowy08550ysm67634cn14kw.mp4',
      },
      altText: 'a large brick house with a black roof and white trim',
      svgOverlay: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/85438f6jkyb3uwvlf7rnfwor3fsm1516.svg',
        dimensions: [
          {
            usage: 'image',
            type: 'https://dam-test.gap.orangelogic.com/AssetLink/85438f6jkyb3uwvlf7rnfwor3fsm1516.svg',
            width: 1440,
            height: 635,
          },
        ],
      },
    },
    mobile: {
      fallbackImage: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/k3c5cd7ykfqjaf8l4cj100ckf330m7l2.webp',
        dimensions: [
          {
            usage: 'image',
            type: 'https://dam-test.gap.orangelogic.com/AssetLink/k3c5cd7ykfqjaf8l4cj100ckf330m7l2.webp',
            width: 750,
            height: 1000,
          },
        ],
      },
      svgOverlay: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/5c4x3jfi483nk4218708033bre3owp46.svg',
        dimensions: [
          {
            usage: 'image',
            type: 'https://dam-test.gap.orangelogic.com/AssetLink/5c4x3jfi483nk4218708033bre3owp46.svg',
            width: 375,
            height: 485,
          },
        ],
      },
      video: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/3lv5ybh0c2jp61iexj22ag32ly1o0o33.mp4',
      },
      overlayAltText: 'Transparent SVG Text',
      altText: 'a woman in a purple crop top posing for a picture',
    },
  },
  type: 'videoTile',
  title: 'CMS Mobile App Video w/ Overlay and Video Selection',
  platforms: {
    android: true,
    ios: true,
  },
  _meta: {
    name: 'CMS Mobile App Video w/ Overlay and Video Selection',
    schema: 'https://cms.gap.com/schema/content/v1/mobileapp-video-tile.json',
    deliveryId: 'aa750706-e93d-4498-bc8a-fb1e3122fd21',
  },
};
