import { VideoTileContent } from '../utils';

export const desktopAndMobileVideoData: VideoTileContent = {
  _meta: {
    name: 'Mobile App Video Tile',
    schema: 'https://cms.gap.com/schema/content/v1/mobileapp-video-tile.json',
    deliveryId: 'b9f6bd8e-3e77-4ed5-8902-25d240d3d7ef',
  },
  styling: {
    padding: {
      desktop: 20,
      mobile: 10,
    },
  },
  link: {
    linkType: 'url',
    linkTypeValue: 'https://athleta.gap.com/browse/new/resort?cid=1022034&amp;mlink=1,1,HP_Spotlight_CTA1',
  },
  video: {
    mobile: {
      fallbackImage: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/1l1811m3h7muus8re87tggl7562dkbgj.webp',
        dimensions: [
          {
            usage: 'image',
            type: 'https://dam-test.gap.orangelogic.com/AssetLink/1l1811m3h7muus8re87tggl7562dkbgj.webp',
            width: 750,
            height: 1000,
          },
        ],
      },
      video: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/2bdt32r05c7ww6g7u7o7xg8gdt2puy74.mp4',
      },
      altText: 'a woman in a purple crop top posing for a picture',
    },
    desktop: {
      fallbackImage: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/sjvw6312f1v8m25hj2c33re24w3bh111.webp',
        dimensions: [
          {
            usage: 'image',
            type: 'https://dam-test.gap.orangelogic.com/AssetLink/sjvw6312f1v8m25hj2c33re24w3bh111.webp',
            width: 2880,
            height: 1300,
          },
        ],
      },
      video: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/yd1b1de47qx2q4e40k7fbq7ap48t806u.mp4',
      },
      altText: 'a woman in a purple top posing for a picture',
    },
  },
  type: 'videoTile',
  title: 'Test Video',
  platforms: {
    android: true,
    ios: true,
  },
};

export const svgOverlayVideoData: VideoTileContent = {
  _meta: {
    name: 'Mobile App Video Tile',
    schema: 'https://cms.gap.com/schema/content/v1/mobileapp-video-tile.json',
    deliveryId: 'b9f6bd8e-3e77-4ed5-8902-25d240d3d7ef',
  },
  styling: {
    padding: {
      desktop: 20,
      mobile: 10,
    },
  },
  link: {
    linkType: 'url',
    linkTypeValue: 'https://athleta.gap.com/browse/new/resort?cid=1022034&amp;mlink=1,1,HP_Spotlight_CTA1',
    legal: {
      detailsText: '*Details',
      textColor: 'dark',
    },
  },
  video: {
    mobile: {
      fallbackImage: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/1l1811m3h7muus8re87tggl7562dkbgj.webp',
        dimensions: [
          {
            usage: 'image',
            type: 'https://dam-test.gap.orangelogic.com/AssetLink/1l1811m3h7muus8re87tggl7562dkbgj.webp',
            width: 750,
            height: 1000,
          },
        ],
      },
      svgOverlay: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/l365w58hrvx7mush687orsrk8cj2o556.svg',
        dimensions: [
          {
            usage: 'image',
            type: 'https://dam-test.gap.orangelogic.com/AssetLink/l365w58hrvx7mush687orsrk8cj2o556.svg',
            width: 375,
            height: 485,
          },
        ],
      },
      video: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/2bdt32r05c7ww6g7u7o7xg8gdt2puy74.mp4',
      },
      altText: 'a woman in a purple crop top posing for a picture',
      overlayAltText: 'desktop svg alt text',
    },
    desktop: {
      fallbackImage: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/sjvw6312f1v8m25hj2c33re24w3bh111.webp',
        dimensions: [
          {
            usage: 'image',
            type: 'https://dam-test.gap.orangelogic.com/AssetLink/sjvw6312f1v8m25hj2c33re24w3bh111.webp',
            width: 2880,
            height: 1300,
          },
        ],
      },
      svgOverlay: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/co42gb30474s0hm056om3sl01m3onf8y.svg',
        dimensions: [
          {
            usage: 'image',
            type: 'https://dam-test.gap.orangelogic.com/AssetLink/co42gb30474s0hm056om3sl01m3onf8y.svg',
            width: 1440,
            height: 635,
          },
        ],
      },
      video: {
        url: 'https://dam-test.gap.orangelogic.com/AssetLink/yd1b1de47qx2q4e40k7fbq7ap48t806u.mp4',
      },
      altText: 'a woman in a purple top posing for a picture',
      overlayAltText: 'desktop svg alt text',
    },
  },
  type: 'videoTile',
  title: 'Test Video',
  platforms: {
    android: true,
    ios: true,
  },
};
