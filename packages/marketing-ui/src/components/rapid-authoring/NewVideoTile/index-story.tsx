'use client';
import React from 'react';
import { Meta, StoryObj } from '@storybook/react';
import NewVideoTile from '.';
import { desktopAndMobileVideoData, svgOverlayVideoData } from './__fixtures__/video-tile-story-data';
import { VideoTileContent } from './utils';

type ComponentTypeForStory = (props: { data: VideoTileContent }) => React.JSX.Element | null;

type Story = StoryObj<ComponentTypeForStory>;

const meta: Meta<ComponentTypeForStory> = {
  argTypes: {
    data: {
      name: 'Amplience Data',
      description: 'The JSON payload from Amplience',
      control: {
        type: 'object',
      },
    },
  },
  title: 'Common/JSON Components (Marketing)/Rapid Authoring/NewVideoTile',
  component: ({ data }) => <NewVideoTile {...data} />,
  parameters: {
    // docs: { page: README }, TODO: add README.md
    layout: 'fullscreen',
    knobs: {
      disabled: true,
    },
  },
  render: ({ data }) => {
    const content = <NewVideoTile {...data} />;
    return <React.Fragment>{content}</React.Fragment>;
  },
  tags: ['exclude'],
};
export default meta;

export const desktopAndMobileVideo: Story = {
  args: {
    data: desktopAndMobileVideoData,
  },
};

export const svgOverlay: Story = {
  args: {
    data: svgOverlayVideoData,
  },
};
