import React from 'react';
import { render, screen } from 'test-utils';
import { Brands } from '@ecom-next/core/react-stitch';
import NewVideoTile from './index';
import { newVideoStoryData } from './__fixtures__/new-video-story-data.tsx';
import { VideoTileContent } from './utils';

const renderNewVideoTile = ({ serverResponse = newVideoStoryData, brand = Brands.Athleta }: { serverResponse?: VideoTileContent; brand?: Brands }) => {
  return render(<NewVideoTile {...serverResponse} />, { brand });
};

describe('NewVideoTile', () => {
  it('should render the component', () => {
    const { container } = renderNewVideoTile(newVideoStoryData);

    expect(container).toMatchSnapshot();
  });

  it('should remove special characters from the start of legal text', () => {
    const specialChars = ['*', '#', '@', '!', '$', '%', '^', '&', '(', ')', '-', '+', '='];
    specialChars.forEach(char => {
      renderNewVideoTile({
        serverResponse: {
          ...newVideoStoryData,
          link: {
            ...newVideoStoryData.link,
            legal: {
              ...newVideoStoryData.link.legal,
              detailsText: `${char}Details`,
            },
          },
        },
      });

      // Ensure the text with the special character is NOT present
      expect(screen.queryByText(`${char}Details`)).not.toBeInTheDocument();
    });
  });

  it('should not remove any character from legal text if it does not start with a special character', () => {
    renderNewVideoTile({
      serverResponse: {
        ...newVideoStoryData,
        link: {
          ...newVideoStoryData.link,
          legal: {
            ...newVideoStoryData.link.legal,
            detailsText: 'OtherDetails',
          },
        },
      },
    });

    const legalText = screen.getByText('OtherDetails');
    expect(legalText).toBeInTheDocument();
  });
});
