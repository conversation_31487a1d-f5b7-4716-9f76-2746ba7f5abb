'use client';
import React, { useState, useContext, useRef, useEffect } from 'react';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { IframeModal } from '../../legacy/components/ComposableButton/IframeModal';
import { UnmuteIcon, MuteIcon, PlayActive, PauseActive } from '../../legacy/components/VideoComponent/components/icons/index';
import { VideoTileContent, Brands } from './utils';

export function getDetailsContent(pemoleCode?: string, htmlModalUrl?: string): string {
  if (htmlModalUrl) return htmlModalUrl;
  if (!pemoleCode) return '';
  return `https://athleta.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${pemoleCode}`;
}

/**
 * A responsive video tile component for marketing use, supporting autoplay, mute/unmute, play/pause,
 * and legal modal overlays. Displays a fallback image, SVG overlay, and handles both desktop and mobile layouts.
 *
 * @param serverResponse - The video tile content and configuration, including video sources, styling, links, and feature flags.
 * @returns A React component rendering a video tile with interactive controls and optional legal modal.
 *
 * @remarks
 * - Uses context from `BreakpointContext` to determine mobile/desktop rendering.
 * - Handles video playback, mute state, and overlays for legal information.
 * - Integrates with legacy modal and icon components.
 * - Supports feature flag for debug grid overlay.
 */
const NewVideoTile = (serverResponse: VideoTileContent) => {
  const {
    video,
    styling,
    link,
    // @ts-ignore
    featureFlags,
  } = serverResponse;
  const autoplay = true;

  const [videoLoaded, setVideoLoaded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(autoplay);
  const [isMuted, setIsMuted] = useState(true);
  const [displaySoundIcon, setDisplaySoundIcon] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [modal, setModal] = useState({
    isOpen: false,
    pemoleCode: '',
  });
  const [showDebug] = useState(featureFlags?.['omni-show-hotzone-grid'] || false);

  const videoRef = useRef<HTMLVideoElement | null>(null);

  const { smallerThan } = useContext(BreakpointContext);
  const isMobile = smallerThan(XLARGE);

  const videoData = isMobile ? video?.mobile : video?.desktop;
  // Check if videoData is available
  if (!videoData) return null;
  const videoUrl = videoData?.video?.url;
  // If no video URL is provided, return null
  if (!videoUrl) return null;

  const fallbackImage = videoData?.fallbackImage;
  const svgOverlay = videoData?.svgOverlay;
  const altText = videoData?.altText;
  const svgAltText = videoData?.overlayAltText;
  const fallbackUrl = `${fallbackImage?.url}`;
  const bottomPadding = styling.padding[isMobile ? 'mobile' : 'desktop'];
  const imgWidth = fallbackImage?.dimensions?.[0]?.width;
  const imgHeight = fallbackImage?.dimensions?.[0]?.height;
  const legal = link?.legal || {};

  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);

    return () => {
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
    };
  }, [videoRef, setIsPlaying]);

  useEffect(() => {
    const observer = new window.IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && videoRef.current) {
          videoRef.current.play();
        }
        if (!entry.isIntersecting && videoRef.current) {
          videoRef.current.pause();
        }
      },
      { threshold: 0.25 }
    );
    if (videoRef.current) {
      observer.observe(videoRef.current);
    }
    return () => {
      if (videoRef.current) {
        observer.unobserve(videoRef.current);
      }
    };
  }, [videoRef]);

  useEffect(() => {
    setVideoLoaded(true);
  }, []);

  // Handle play/pause
  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;
    if (video.paused) {
      video.play();
      setIsPlaying(true);
    } else {
      video.pause();
      setIsPlaying(false);
    }
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;
    video.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  const handleVideoClick = () => {
    if (!link?.linkTypeValue) return;
    const isAbsolute = /^https?:\/\//i.test(link.linkTypeValue);
    const url = isAbsolute ? link.linkTypeValue : `${window.location.origin}${link.linkTypeValue}`;
    if (isAbsolute) {
      window.open(url, '_blank', 'noopener,noreferrer');
    } else {
      window.location.href = url;
    }
  };

  // This function renders a string with its leading non-alphanumeric character unstyled
  // and the rest underlined, or returns the text as-is if no such character exists.
  function renderLeadingCharUnderline(text?: string) {
    if (!text) return null;
    const specialCharMatch = text.match(/^[^a-zA-Z0-9]/);
    if (specialCharMatch) {
      const specialChar = specialCharMatch[0];
      return (
        <>
          {specialChar}
          <span className='underline'>{text.slice(1).trimStart()}</span>
        </>
      );
    }
    return text;
  }

  return (
    <div
      style={{
        paddingBottom: `${bottomPadding}px`,
      }}
    >
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '1fr',
          gridTemplateRows: '1fr',
          width: '100%',
          aspectRatio: `${imgWidth} / ${imgHeight}`,
          cursor: 'pointer',
          position: 'relative',
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <img
          src={fallbackUrl}
          alt={altText}
          fetchPriority='high'
          style={{
            gridColumnStart: 1,
            gridRowStart: 1,
            width: '100%',
          }}
        />
        <video
          key={videoUrl}
          ref={videoRef}
          width='100%'
          height='auto'
          controls={false}
          onClick={handleVideoClick}
          preload='auto'
          poster={fallbackUrl}
          autoPlay={autoplay}
          loop
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          muted={isMuted}
          style={{
            gridColumnStart: 1,
            gridRowStart: 1,
            width: '100%',
          }}
          tabIndex={0}
          aria-label={altText || 'Video player'}
        >
          <source src={videoUrl} type='video/mp4' />
          Your browser does not support the video tag.
        </video>
        <img
          src={svgOverlay?.url}
          alt={svgAltText}
          style={{
            gridColumnStart: 1,
            gridRowStart: 1,
            width: '100%',
          }}
        />
        {videoUrl && videoLoaded && (
          <button
            aria-label={isPlaying ? 'Pause' : 'Play'}
            onClick={togglePlay}
            style={{
              position: 'absolute',
              bottom: 10,
              left: isMobile ? 'auto' : 15,
              right: isMobile ? 15 : 'auto',
              background: 'transparent',
              border: 'none',
              cursor: 'pointer',
              zIndex: 3,
              display: 'block',
              opacity: 0.64,
            }}
          >
            {isPlaying ? <PauseActive /> : <PlayActive />}
          </button>
        )}
        {videoUrl && videoLoaded && displaySoundIcon && (
          <button
            aria-label={isMuted ? 'Unmute' : 'Mute'}
            onClick={toggleMute}
            style={{
              position: 'absolute',
              bottom: 30,
              left: isMobile ? 'auto' : 60,
              right: isMobile ? 50 : 'auto',
              background: 'transparent',
              border: 'none',
              cursor: 'pointer',
              zIndex: 3,
            }}
          >
            {isMuted ? <MuteIcon /> : <UnmuteIcon />}
          </button>
        )}
        {legal?.pemoleCode && (
          <>
            <div className={`pointer-events-none absolute bottom-[10px] flex w-full justify-center`}>
              <button
                className={`block ${showDebug ? 'border-1 border border-black' : ''} pointer-events-auto ${legal?.textColor === 'dark' ? 'text-bk' : 'text-wh'} border-none shadow-none`}
                style={{
                  width: 137,
                  height: 16,
                }}
                onClick={e => {
                  e.preventDefault();

                  setModal({
                    isOpen: true,
                    pemoleCode: legal?.pemoleCode || '',
                  });
                }}
                aria-label={legal?.detailsText}
                type='button'
                title={legal?.detailsText}
              >
                {renderLeadingCharUnderline(legal?.detailsText)}
              </button>
            </div>
            <IframeModal
              closeButtonAriaLabel={'close'}
              data-testid='default-modal'
              isOpen={modal.isOpen}
              onClose={() => {
                setModal({
                  isOpen: false,
                  pemoleCode: '',
                });
              }}
              src={getDetailsContent(legal?.pemoleCode)}
            ></IframeModal>
          </>
        )}
      </div>
    </div>
  );
};

export default NewVideoTile;
