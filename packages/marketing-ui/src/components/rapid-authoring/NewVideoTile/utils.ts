export interface ImageDimensions {
  height: number;
  type: string;
  usage: string;
  width: number;
}

export interface VideoTileFallbackImage {
  dimensions: ImageDimensions[];
  url: string;
}

export interface VideoTileSVGOverlay {
  url: string;
  dimensions: ImageDimensions[];
}

export interface VideoTileVideoSource {
  fallbackImage?: VideoTileFallbackImage;
  video?: {
    url: string;
  };
  altText?: string;
  svgOverlay?: VideoTileSVGOverlay;
  overlayAltText?: string;
}

export interface VideoTileVideo {
  desktop?: VideoTileVideoSource;
  mobile?: VideoTileVideoSource;
}

export interface VideoTileStyling {
  padding: {
    desktop: number;
    mobile: number;
  };
}

export interface VideoTilePlatforms {
  android: boolean;
  ios: boolean;
}

export interface VideoTileMeta {
  deliveryId: string;
  name: string;
  schema: string;
}

export interface LinkRedirectUrl {
  legal?: LegalBannerType;
  linkType: string;
  linkTypeValue?: string;
}

export interface LegalBannerType {
  detailsText?: string;
  pemoleCode?: string;
  textColor?: string;
}
export interface VideoTileContent {
  _meta: VideoTileMeta;
  link: LinkRedirectUrl;
  platforms: VideoTilePlatforms;
  styling: VideoTileStyling;
  title: string;
  type: string;
  video?: VideoTileVideo;
  videoControlSettings?: {
    controlsIconsColor: string;
  };
}

export enum Brands {
  Athleta = 'at',
  BananaRepublic = 'br',
  BananaRepublicFactoryStore = 'brfs',
  Gap = 'gap',
  GapFactoryStore = 'gapfs',
  OldNavy = 'on',
}
