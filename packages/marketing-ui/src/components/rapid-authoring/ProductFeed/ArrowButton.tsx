'use client';
import React from 'react';
import { arrows, arrowOpacity } from './styles';
import { ArrowButtonProps } from './utils';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';

/**
 * ArrowButton component renders a button with a left or right arrow image,
 * used for carousel navigation.
 *
 * @param {'left' | 'right'} direction - The direction of the arrow, determines which arrow image to display and the aria-label.
 * @param {boolean} disabled - Whether the button is disabled.
 * @param {() => void} onClick - The click handler for the button.
 *
 * @returns {JSX.Element} A button element with an arrow image.
 */
const ArrowButton: React.FC<ArrowButtonProps> = ({ direction, disabled, onClick }) => {
  const { localize } = useLocalize();
  const localizedNext = localize('cms.carousel.next');
  const localizedPrevious = localize('cms.carousel.previous');
  return (
    <button
      className={`${arrows} ${direction === 'left' ? 'left-0' : 'right-0'} ${disabled ? arrowOpacity : ''}`}
      onClick={onClick}
      disabled={disabled}
      aria-label={direction === 'left' ? localizedPrevious : localizedNext}
      type='button'
    >
      <img
        alt={direction === 'left' ? localizedPrevious : localizedNext}
        src={
          direction === 'left'
            ? 'https://athletaprod.a.bigcontent.io/v1/static/Carousel_Arrow_Black_Left_1'
            : 'https://athletaprod.a.bigcontent.io/v1/static/Carousel_Arrow_Black_Right_1'
        }
      />
    </button>
  );
};

export default ArrowButton;
