import { ProductFeedData } from '../utils';

// Two Frame Layout
export const twoFramesData: ProductFeedData = {
  _meta: {
    name: 'Mobile App Category Carousel - 2 frames Only Images - Bhaskar',
    schema: 'https://cms.gap.com/schema/content/v1/mobileapp-category-carousel.json',
    deliveryId: 'e34927e2-a942-45d4-8b30-b4a77ce4d0c3',
  },
  layout: {
    desktop: {
      linear: [2, 4, 8],
    },
    mobile: {
      linear: [2, 8],
      stacked: [4],
    },
  },
  styling: {
    padding: {
      desktop: 20,
      mobile: 10,
    },
  },
  type: 'featured',
  title: 'category carousel 2 frames - Images only',
  mobileCarousel: [
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/2d0tp28i8kcqo35v7mrafq58ut2x555d.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/2d0tp28i8kcqo35v7mrafq58ut2x555d.jpg',
              width: 1116,
              height: 1517,
            },
          ],
          hotspots: [
            {
              type: 'text',
              coordinates: {
                x1: 0,
                x2: 100,
                y1: 64,
                y2: 79,
              },
              name: 'Textzone2',
              text: 'New Arrivals',
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/i64736t2f6k7dluwn0g882c6v7y70cry.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/i64736t2f6k7dluwn0g882c6v7y70cry.jpg',
              width: 1116,
              height: 1517,
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
  ],
  headline: '2 frames Image',
  desktopCarousel: [
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/2d0tp28i8kcqo35v7mrafq58ut2x555d.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/2d0tp28i8kcqo35v7mrafq58ut2x555d.jpg',
              width: 1116,
              height: 1517,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/i64736t2f6k7dluwn0g882c6v7y70cry.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/i64736t2f6k7dluwn0g882c6v7y70cry.jpg',
              width: 1116,
              height: 1517,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
  ],
  platforms: {
    android: true,
    ios: true,
  },
};

export const fourFramesData: FeaturedCategoriesData = {
  _meta: {
    name: 'Mobile App Category Carousel - 4 frames Only Images - Bhaskar',
    schema: 'https://cms.gap.com/schema/content/v1/mobileapp-category-carousel.json',
    deliveryId: '42696fcb-89b3-4cb2-8ffc-c118f7678b46',
  },
  layout: {
    desktop: {
      linear: [2, 4, 8],
    },
    mobile: {
      linear: [2, 8],
      stacked: [4],
    },
  },
  styling: {
    padding: {
      desktop: 20,
      mobile: 10,
    },
  },
  type: 'featured',
  title: 'category carousel 4 frames - Images only',
  mobileCarousel: [
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/838d5u37u24125va752ipx8qv38riq84.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/838d5u37u24125va752ipx8qv38riq84.jpg',
              width: 804,
              height: 1517,
            },
          ],
          hotspots: [
            {
              type: 'text',
              coordinates: {
                x1: 0,
                x2: 100,
                y1: 0,
                y2: 100,
              },
              name: 'zone1',
              text: 'Sets',
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/2jb2g7132c88206wm2yacn22m0857e71.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/2jb2g7132c88206wm2yacn22m0857e71.jpg',
              width: 804,
              height: 1517,
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/e12bd4jal5o83wc313g3j4is01076as0.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/e12bd4jal5o83wc313g3j4is01076as0.jpg',
              width: 804,
              height: 1517,
            },
          ],
        },
        accessibilityAltText: 'a woman walking down a sidewalk with a tennis racket',
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg',
              width: 804,
              height: 1517,
            },
          ],
        },
        accessibilityAltText: 'a woman in white pants sitting on a bench with a caption that reads pants',
      },
    },
  ],
  headline: '4 frames Image',
  desktopCarousel: [
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/838d5u37u24125va752ipx8qv38riq84.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/838d5u37u24125va752ipx8qv38riq84.jpg',
              width: 804,
              height: 1517,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/2jb2g7132c88206wm2yacn22m0857e71.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/2jb2g7132c88206wm2yacn22m0857e71.jpg',
              width: 804,
              height: 1517,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/e12bd4jal5o83wc313g3j4is01076as0.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/e12bd4jal5o83wc313g3j4is01076as0.jpg',
              width: 804,
              height: 1517,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman walking down a sidewalk with a tennis racket',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg',
              width: 804,
              height: 1517,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in white pants sitting on a bench with a caption that reads pants',
      },
    },
  ],
  platforms: {
    android: true,
    ios: true,
  },
};

export const eightFramesData: FeaturedCategoriesData = {
  _meta: {
    name: 'Mobile App Category Carousel - 8 frames Only Images - Bhaskar',
    schema: 'https://cms.gap.com/schema/content/v1/mobileapp-category-carousel.json',
    deliveryId: '3bf17144-b1ee-48ef-b97e-d904702a6389',
  },
  layout: {
    desktop: {
      linear: [2, 4, 8],
    },
    mobile: {
      linear: [2, 8],
      stacked: [4],
    },
  },
  styling: {
    padding: {
      desktop: 20,
      mobile: 10,
    },
  },
  type: 'featured',
  title: 'category carousel 8 frames - Images only',
  mobileCarousel: [
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/566s4w7532x2mqu1w3k5j82pnvl1u8b4.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/566s4w7532x2mqu1w3k5j82pnvl1u8b4.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/h37v472th2876g31sbs3kui1452ifmnw.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/h37v472th2876g31sbs3kui1452ifmnw.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/ltoa5100tq5t0a230o2625760il0v31m.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/ltoa5100tq5t0a230o2625760il0v31m.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        accessibilityAltText: 'a woman walking down a sidewalk with a tennis racket',
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        accessibilityAltText: 'a woman in white pants sitting on a bench with a caption that reads pants',
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/a1k0fepyxy5405ff23n47dy6620q1m77.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/a1k0fepyxy5405ff23n47dy6620q1m77.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        accessibilityAltText: 'a woman in a blue outfit posing for a photo',
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/p07325phsju0m5lsp3136kip28s75f7a.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/p07325phsju0m5lsp3136kip28s75f7a.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        accessibilityAltText: 'a woman in a pink sweatshirt and shorts posing for a photo',
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/dfpl653wfxcb8021wcq47124ok1040ue.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/dfpl653wfxcb8021wcq47124ok1040ue.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        accessibilityAltText: 'a woman in a sports bra holding a large stick',
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/x6s3ou1kc856ct3cerp2uy2srcvs77de.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/x6s3ou1kc856ct3cerp2uy2srcvs77de.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        accessibilityAltText: 'a woman in red pants and a red top posing for a magazine',
      },
    },
  ],
  headline: '8 frames Image',
  desktopCarousel: [
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/566s4w7532x2mqu1w3k5j82pnvl1u8b4.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/566s4w7532x2mqu1w3k5j82pnvl1u8b4.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/h37v472th2876g31sbs3kui1452ifmnw.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/h37v472th2876g31sbs3kui1452ifmnw.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/ltoa5100tq5t0a230o2625760il0v31m.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/ltoa5100tq5t0a230o2625760il0v31m.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman walking down a sidewalk with a tennis racket',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in white pants sitting on a bench with a caption that reads pants',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/a1k0fepyxy5405ff23n47dy6620q1m77.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/a1k0fepyxy5405ff23n47dy6620q1m77.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a blue outfit posing for a photo',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/p07325phsju0m5lsp3136kip28s75f7a.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/p07325phsju0m5lsp3136kip28s75f7a.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a pink sweatshirt and shorts posing for a photo',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/dfpl653wfxcb8021wcq47124ok1040ue.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/dfpl653wfxcb8021wcq47124ok1040ue.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a sports bra holding a large stick',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/x6s3ou1kc856ct3cerp2uy2srcvs77de.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/x6s3ou1kc856ct3cerp2uy2srcvs77de.jpg',
              width: 654,
              height: 872,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in red pants and a red top posing for a magazine',
      },
    },
  ],
  platforms: {
    android: true,
    ios: true,
  },
};

export const videoTileTwoFrames: FeaturedCategoriesData = {
  _meta: {
    name: 'Mobile App Category Carousel - 2 frames - Bhaskar',
    schema: 'https://cms.gap.com/schema/content/v1/mobileapp-category-carousel.json',
    deliveryId: 'd4d6d01e-6631-47f3-b23e-021c7d2a0b1b',
  },
  layout: {
    desktop: {
      linear: [2, 4, 8],
    },
    mobile: {
      linear: [2, 8],
      stacked: [4],
    },
  },
  styling: {
    padding: {
      desktop: 20,
      mobile: 10,
    },
  },
  type: 'featured',
  title: 'category carousel 2 frames - Video',
  mobileCarousel: [
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
              width: 750,
              height: 1000,
            },
          ],
          hotspots: [
            {
              type: 'url',
              coordinates: {
                x1: 0,
                x2: 100,
                y1: 0,
                y2: 100,
              },
              name: 'Textzone2',
              url: 'https://athleta.gap.com',
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
    {
      type: 'video',
      video: {
        mobile: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
                width: 750,
                height: 1000,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/c20870l2ref50c45mahot56680c77o23.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/c20870l2ref50c45mahot56680c77o23.svg',
                width: 1440,
                height: 635,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/j5cxo8k1v6k45qyl54yp5sk1xn43xcb1.mp4',
          },
          altText: 'a woman in a purple crop top posing for a picture',
        },
      },
    },
  ],
  headline: '2 frames video and Image',
  desktopCarousel: [
    {
      type: 'video',
      video: {
        desktop: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/hg201m8234k1nd1p60y31pb5n1nu7iyn.webp',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/hg201m8234k1nd1p60y31pb5n1nu7iyn.webp',
                width: 2880,
                height: 1300,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/c20870l2ref50c45mahot56680c77o23.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/c20870l2ref50c45mahot56680c77o23.svg',
                width: 1440,
                height: 635,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/lf00l247g0k551m4aop0g732w4w5o3s8.mp4',
          },
          altText: 'a woman in a purple top posing for a picture',
        },
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/hg201m8234k1nd1p60y31pb5n1nu7iyn.webp',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/hg201m8234k1nd1p60y31pb5n1nu7iyn.webp',
              width: 2880,
              height: 1300,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
  ],
  platforms: {
    android: true,
    ios: true,
  },
};

export const videoTileFourFrames: FeaturedCategoriesData = {
  _meta: {
    name: 'Mobile App Category Carousel - 4 frames - Bhaskar',
    schema: 'https://cms.gap.com/schema/content/v1/mobileapp-category-carousel.json',
    deliveryId: '216ba5dd-3bb4-4f8b-8f11-4beb4e48dab8',
  },
  layout: {
    desktop: {
      linear: [2, 4, 8],
    },
    mobile: {
      linear: [2, 8],
      stacked: [4],
    },
  },
  styling: {
    padding: {
      desktop: 20,
      mobile: 10,
    },
  },
  type: 'featured',
  title: 'category carousel 4 frames - Video',
  mobileCarousel: [
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
              width: 750,
              height: 1000,
            },
          ],
          hotspots: [
            {
              type: 'url',
              coordinates: {
                x1: 0,
                x2: 100,
                y1: 0,
                y2: 100,
              },
              name: 'Textzone2',
              url: 'https://athleta.gap.com',
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
    {
      type: 'video',
      video: {
        mobile: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
                width: 750,
                height: 1000,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
                width: 375,
                height: 485,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/j5cxo8k1v6k45qyl54yp5sk1xn43xcb1.mp4',
          },
          altText: 'a woman in a purple crop top posing for a picture',
        },
      },
    },
    {
      type: 'video',
      video: {
        mobile: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
                width: 750,
                height: 1000,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
                width: 375,
                height: 485,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/j5cxo8k1v6k45qyl54yp5sk1xn43xcb1.mp4',
          },
          altText: 'a woman in a purple crop top posing for a picture',
        },
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
              width: 750,
              height: 1000,
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
  ],
  headline: '4 frames video and Image',
  desktopCarousel: [
    {
      type: 'video',
      video: {
        desktop: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
                width: 654,
                height: 872,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
                width: 375,
                height: 485,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/3dfi2df416i6554oi4s3hbo8pvxum5p6.mp4',
          },
          altText: 'a woman in a purple top posing for a picture',
        },
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/2nao4audygawc10c8466f7pms2ed07x4.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/2nao4audygawc10c8466f7pms2ed07x4.jpg',
              width: 752,
              height: 1517,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/8to2232qxyt74x142if8fc0nov3e0470.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/8to2232qxyt74x142if8fc0nov3e0470.jpg',
              width: 752,
              height: 1517,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
    {
      type: 'video',
      video: {
        desktop: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
                width: 654,
                height: 872,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
                width: 375,
                height: 485,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/375s2yr5a32c0whc8hr6co021177526n.mp4',
          },
          altText: 'a woman in a purple top posing for a picture',
        },
      },
    },
  ],
  platforms: {
    android: true,
    ios: true,
  },
};

export const videoTileEightFrames: FeaturedCategoriesData = {
  _meta: {
    name: 'Mobile App Category Carousel - 4 frames - Bhaskar',
    schema: 'https://cms.gap.com/schema/content/v1/mobileapp-category-carousel.json',
    deliveryId: '216ba5dd-3bb4-4f8b-8f11-4beb4e48dab8',
  },
  layout: {
    desktop: {
      linear: [2, 4, 8],
    },
    mobile: {
      linear: [2, 8],
      stacked: [4],
    },
  },
  styling: {
    padding: {
      desktop: 20,
      mobile: 10,
    },
  },
  type: 'featured',
  title: 'category carousel 4 frames - Video',
  mobileCarousel: [
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
              width: 750,
              height: 1000,
            },
          ],
          hotspots: [
            {
              type: 'url',
              coordinates: {
                x1: 0,
                x2: 100,
                y1: 0,
                y2: 100,
              },
              name: 'Textzone2',
              url: 'https://athleta.gap.com',
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
    {
      type: 'video',
      video: {
        mobile: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
                width: 750,
                height: 1000,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
                width: 375,
                height: 485,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/j5cxo8k1v6k45qyl54yp5sk1xn43xcb1.mp4',
          },
          altText: 'a woman in a purple crop top posing for a picture',
        },
      },
    },
    {
      type: 'video',
      video: {
        mobile: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
                width: 750,
                height: 1000,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
                width: 375,
                height: 485,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/j5cxo8k1v6k45qyl54yp5sk1xn43xcb1.mp4',
          },
          altText: 'a woman in a purple crop top posing for a picture',
        },
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
              width: 750,
              height: 1000,
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
              width: 750,
              height: 1000,
            },
          ],
          hotspots: [
            {
              type: 'url',
              coordinates: {
                x1: 0,
                x2: 100,
                y1: 0,
                y2: 100,
              },
              name: 'Textzone2',
              url: 'https://athleta.gap.com',
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
    {
      type: 'video',
      video: {
        mobile: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
                width: 750,
                height: 1000,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
                width: 375,
                height: 485,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/j5cxo8k1v6k45qyl54yp5sk1xn43xcb1.mp4',
          },
          altText: 'a woman in a purple crop top posing for a picture',
        },
      },
    },
    {
      type: 'video',
      video: {
        mobile: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
                width: 750,
                height: 1000,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
                width: 375,
                height: 485,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/j5cxo8k1v6k45qyl54yp5sk1xn43xcb1.mp4',
          },
          altText: 'a woman in a purple crop top posing for a picture',
        },
      },
    },
    {
      type: 'image',
      image: {
        image: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/juwx1rr78n7vgv4113cn55y53ia5w1gu.webp',
              width: 750,
              height: 1000,
            },
          ],
        },
        accessibilityAltText: 'a woman in a purple crop top posing for a picture',
      },
    },
  ],
  headline: '8 frames video and Image',
  desktopCarousel: [
    {
      type: 'video',
      video: {
        desktop: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
                width: 654,
                height: 872,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
                width: 375,
                height: 485,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/3dfi2df416i6554oi4s3hbo8pvxum5p6.mp4',
          },
          altText: 'a woman in a purple top posing for a picture',
        },
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/2nao4audygawc10c8466f7pms2ed07x4.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/2nao4audygawc10c8466f7pms2ed07x4.jpg',
              width: 752,
              height: 1517,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/8to2232qxyt74x142if8fc0nov3e0470.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/8to2232qxyt74x142if8fc0nov3e0470.jpg',
              width: 752,
              height: 1517,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
    {
      type: 'video',
      video: {
        desktop: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
                width: 654,
                height: 872,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
                width: 375,
                height: 485,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/375s2yr5a32c0whc8hr6co021177526n.mp4',
          },
          altText: 'a woman in a purple top posing for a picture',
        },
      },
    },
    {
      type: 'video',
      video: {
        desktop: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
                width: 654,
                height: 872,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
                width: 375,
                height: 485,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/3dfi2df416i6554oi4s3hbo8pvxum5p6.mp4',
          },
          altText: 'a woman in a purple top posing for a picture',
        },
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/2nao4audygawc10c8466f7pms2ed07x4.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/2nao4audygawc10c8466f7pms2ed07x4.jpg',
              width: 752,
              height: 1517,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
    {
      type: 'image',
      image: {
        desktopImage: {
          url: 'https://dam-test.gap.orangelogic.com/AssetLink/8to2232qxyt74x142if8fc0nov3e0470.jpg',
          dimensions: [
            {
              usage: 'image',
              type: 'https://dam-test.gap.orangelogic.com/AssetLink/8to2232qxyt74x142if8fc0nov3e0470.jpg',
              width: 752,
              height: 1517,
            },
          ],
        },
        desktopAccessibilityAltText: 'a woman in a purple top posing for a picture',
      },
    },
    {
      type: 'video',
      video: {
        desktop: {
          fallbackImage: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg',
                width: 654,
                height: 872,
              },
            ],
          },
          svgOverlay: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/AssetLink/6g257oly3341k46b2gqjd6c8348im71m.svg',
                width: 375,
                height: 485,
              },
            ],
          },
          video: {
            url: 'https://dam-test.gap.orangelogic.com/AssetLink/375s2yr5a32c0whc8hr6co021177526n.mp4',
          },
          altText: 'a woman in a purple top posing for a picture',
        },
      },
    },
  ],
  platforms: {
    android: true,
    ios: true,
  },
};
