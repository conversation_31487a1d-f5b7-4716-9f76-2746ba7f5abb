# RichTextBanner

## What is `RichTextBanner`?

`RichTextBanner` is used to add custom styles to copy (_string_). Options includes support for multiple font-face(s), sizes, and weights.

- Supports options for `background`, `textPadding` and `bannerLink` (CTA's)
- `background` accepts a solid color, or, gradient values
- `textPadding` is a `boolean`. When set to `true` the `textPadding` value defaults to `60px 104px` for desktop and `40px 16px` for mobile.
- It is a JSON-configurable component that accepts JSON keys generated by our Content Management System, Amplience. Note that this format differs from earlier JSON formats.

## Default Behavior

- `RichTextBanner` creates a pre-styled banner using the `RichText` component, adding `background` color, `textPadding` and `bannerLink` (CTA) props.
- As of August 2024, Old Navy wants their `RichTextBanner` to have a max width of "1440px" when it exists within the homepage slot and "1280px" when it exists within the electronic billboard (ebb) slot.
- Optionally, if a value for `richTextMobile` is provided and the viewport size is mobile, then that `richTextMobile` content will be displayed. On desktop viewports, the `richText` content will always be displayed regardless of whether `richTextMobile` is provided.

## Limitations

- `RichTextBanner` accepts JSON keys generated by our Content Management System, Amplience. JSON can be manually written as long as it remains compatible with Amplience JSON schema.
- **Note:** Amplience JSON schema differs from earlier JSON schemas. It is intended to be much simpler, with more styles built into the component, and therefore it is less configurable via JSON.

## Technical Notes

- The styling in the `RichTextBanner` package uses [`react-stitch`](https://github.gapinc.com/ecomfrontend/ecom-next/tree/main/packages/core/src/components/react-stitch) and Tailwind CSS.

## Testing the Component in Storybook

- Changes in the Storybook controls should be reflected in the visual example.
