import { RichTextProps } from '../types';

export const dataWithOptionals: RichTextProps = {
  _meta: {
    schema: 'https://cms.gap.com/schema/content/v1/rich-text.json',
    deliveryId: '90b17bbc-a32f-4624-bddf-6eed4dbdde43',
  },
  richText: '<span class="amp-cms--headline-1 amp-cms--avenir-next-regular">Headline 1 Rich Text</span>',
  background: {
    type: 'gradient',
    gradient: {
      from: '#43345',
      to: '#546343',
    },
  },
  bannerLink: {
    label: 'my banner link label',
    value: 'http://www.gap.com',
  },
  textPadding: true,
};

export const dataWithoutOptionals: RichTextProps = {
  _meta: {
    schema: 'https://cms.gap.com/schema/content/v1/rich-text.json',
    deliveryId: '90b17bbc-a32f-4624-bddf-6eed4dbdde43',
  },
  richText: '<span class="amp-cms--headline-1 amp-cms--avenir-next-regular">Headline 1 Rich Text</span>',
  richTextMobile: '<span class="amp-cms--headline-2 amp-cms--text-center amp-cms--avenir-next-regular">Mobile Headline 2 Rich Text</span>',
  background: { type: 'solid' },
  textPadding: false,
};

export const dataWithinHomepageSlot: RichTextProps = {
  _meta: {
    schema: 'https://cms.gap.com/schema/content/v1/rich-text.json',
    deliveryId: '90b17bbc-a32f-4624-bddf-6eed4dbdde43',
  },
  richText: '<span class="amp-cms--headline-1 amp-cms--avenir-next-regular">Headline 1 Rich Text</span>',
  richTextMobile: '<span class="amp-cms--headline-2 amp-cms--text-center amp-cms--avenir-next-regular">Mobile Headline 2 Rich Text</span>',
  background: {
    type: 'solid',
    color: '#c8cdf1',
  },
  textPadding: true,
  marketingType: 'home',
};

export const dataWithinEBBSlot: RichTextProps = {
  _meta: {
    schema: 'https://cms.gap.com/schema/content/v1/rich-text.json',
    deliveryId: '90b17bbc-a32f-4624-bddf-6eed4dbdde43',
  },
  richText: '<span class="amp-cms--headline-1 amp-cms--avenir-next-regular">Headline 1 Rich Text</span>',
  richTextMobile: '<span class="amp-cms--headline-2 amp-cms--text-center amp-cms--avenir-next-regular">Mobile Headline 2 Rich Text</span>',
  background: {
    type: 'solid',
    color: '#c6e3c6',
  },
  textPadding: true,
  marketingType: 'ebb',
};
