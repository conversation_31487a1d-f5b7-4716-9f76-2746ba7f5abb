import React from 'react';
import { render, screen } from 'test-utils';
import { LARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import { StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { defaultComponentMaxWidths } from '../legacy/CMS/subcomponents/ComponentMaxWidth';
import { dataWithinEBBSlot, dataWithinHomepageSlot, dataWithOptionals, dataWithoutOptionals } from './__fixtures__/test-data';
import RichTextBanner, { bannerId } from './index';

describe('Rich Text Banner Component', () => {
  describe('with optional props', () => {
    it('should match snapshots on desktop', () => {
      const { container } = render(<RichTextBanner {...dataWithOptionals} />, {
        breakpoint: LARGE,
      });
      expect(container).toMatchSnapshot();
    });
    it('should match snapshots on mobile', () => {
      const { container } = render(<RichTextBanner {...dataWithOptionals} />, {
        breakpoint: SMALL,
      });
      expect(container).toMatchSnapshot();
    });
    it(`should have a banner with background color ${dataWithOptionals.background.color}`, () => {
      const { getByTestId } = render(<RichTextBanner {...dataWithOptionals} />);
      const banner = getByTestId(bannerId);
      expect(banner).toHaveStyle({
        backgroundColor: dataWithOptionals.background.color,
      });
    });
    it('a tag should have title "my banner link label"', () => {
      const { getByTestId } = render(<RichTextBanner {...dataWithOptionals} />);
      const link = getByTestId(bannerId).querySelector('a');
      const bannerLinkLabel = 'my banner link label';
      expect(link).toHaveAttribute('title', bannerLinkLabel);
    });
    it('should have an hyperlink as "http://www.gap.com"', () => {
      const { getByTestId } = render(<RichTextBanner {...dataWithOptionals} />);
      const link = getByTestId(bannerId).querySelector('a');
      const bannerLinkValue = 'http://www.gap.com';
      expect(link).toHaveAttribute('href', bannerLinkValue);
    });
  });
  describe('without optional props', () => {
    it("should not have background-color'", () => {
      const { getByTestId } = render(<RichTextBanner {...dataWithoutOptionals} />);
      const banner = getByTestId(bannerId);
      expect(banner).not.toHaveStyle({
        backgroundColor: 'transparent',
      });
    });
    it('should not render banner link', () => {
      const { queryByRole } = render(<RichTextBanner {...dataWithoutOptionals} />);
      expect(queryByRole('link')).toBeNull();
    });
  });
  describe('max width', () => {
    it('should be 1280px within home slot for Old Navy', () => {
      const { getByTestId } = render(
        <StitchStyleProvider brand={'on'}>
          <RichTextBanner {...dataWithinEBBSlot} />
        </StitchStyleProvider>,
        {
          breakpoint: LARGE,
        }
      );
      const banner = getByTestId(bannerId);
      const styles = window.getComputedStyle(banner);

      expect(styles.getPropertyValue('--rtb--max-width')).toBe(`${defaultComponentMaxWidths.categoryBanner}px`);
      // Successful when checking if the Utility Class exists, however,
      // using a custom class the value could not be extracted, therefore only checking the class is present
      expect(banner).toHaveClass(`max-w-[${defaultComponentMaxWidths.categoryBanner}px]`);
    });
    it('should be 1440px within ebb slot for Old Navy', () => {
      const { getByTestId } = render(
        <StitchStyleProvider brand={'on'}>
          <RichTextBanner {...dataWithinHomepageSlot} />
        </StitchStyleProvider>,
        {
          breakpoint: LARGE,
        }
      );
      const banner = getByTestId(bannerId);
      const styles = window.getComputedStyle(banner);

      expect(styles.getPropertyValue('--rtb--max-width')).toBe(`${defaultComponentMaxWidths.homepage.on}px`);
      expect(banner).toHaveClass(`max-w-[${defaultComponentMaxWidths.homepage.on}px]`);
    });
  });
});
