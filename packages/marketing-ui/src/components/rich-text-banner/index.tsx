'use client';

import React from 'react';
import classNames from 'classnames';
import { useTheme, Brands } from '@ecom-next/core/react-stitch';
import { Link } from '@ecom-next/core/migration/link';
import { setColorSpace } from '../../utils/css-helpers';
import { RichText } from '../legacy/CMS/subcomponents/RichText';
import { useViewportIsLarge } from '../legacy/hooks/useViewportIsLarge';
import { defaultComponentMaxWidths } from '../legacy/CMS/subcomponents/ComponentMaxWidth';
import { ShowHideWrapper } from '../legacy/CMS/subcomponents/ShowHideWrapper';
import { useIsBrand } from '../legacy/hooks';
import { RichTextProps } from './types';
import './rich-text-banner-styles.css';

export const bannerId = 'richTextBanner-bannerDiv';

const RichTextBanner: React.FC<RichTextProps> = ({ background, bannerLink, richText, richTextMobile, textPadding, marketingType, webAppearance }) => {
  // @ts-ignore
  const { brand } = useTheme();
  const isMobile = !useViewportIsLarge();
  const { color, gradient } = background;
  const richTextValue = isMobile ? richTextMobile || richText : richText;
  const brandMaxWidth =
    marketingType === 'home'
      ? defaultComponentMaxWidths.homepage[brand as keyof typeof defaultComponentMaxWidths.homepage]
      : defaultComponentMaxWidths.categoryBanner || 0;

  const contentTypeMaxWidth = useIsBrand(Brands.OldNavy) ? brandMaxWidth : undefined;
  const rtbMaxWidth = contentTypeMaxWidth !== undefined ? `${contentTypeMaxWidth}px` : '';
  const classes = classNames('mui_rich-text-banner', {
    'bg-gradient-to-b': !color && gradient,
    'max-w-[1280px]': marketingType === 'ebb' && contentTypeMaxWidth,
    'max-w-[1440px]': marketingType === 'home' && contentTypeMaxWidth,
  });

  const richTextBanner = bannerLink ? (
    <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={webAppearance?.showHideBasedOnScreenSize}>
      <div data-component-type='rich-text-banner' className={classes} data-testid={bannerId}>
        <Link className='inline-block' title={bannerLink?.label} to={bannerLink?.value}>
          <RichText isDesktop={!isMobile} scalableText={{ parentMaxWidthPx: brandMaxWidth, enable: true }} text={richTextValue} />
        </Link>
      </div>
    </ShowHideWrapper>
  ) : (
    <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={webAppearance?.showHideBasedOnScreenSize}>
      <div data-component-type='rich-text-banner' className={classes} data-testid={bannerId}>
        <div className='inline-block'>
          <RichText isDesktop={!isMobile} scalableText={{ parentMaxWidthPx: brandMaxWidth, enable: true }} text={richTextValue} />
        </div>
      </div>
    </ShowHideWrapper>
  );

  return (
    <>
      <style>
        {`.mui_rich-text-banner {
          --rtb--container-desktop-padding: ${textPadding ? '60px 104px' : ''} ;
          --rtb--container-mobile-padding: ${textPadding ? '40px 16px' : ''};
          --rtb--bg-gradient-from: ${setColorSpace(gradient?.from)};
          --rtb--bg-gradient-to: ${setColorSpace(gradient?.to)};
          --rtb--bg-color: ${setColorSpace(color)};
          --rtb--max-width: ${rtbMaxWidth};
          --rtb--gradient-stops: var(--rtb--bg-gradient-from), var(--rtb--bg-gradient-to);
          }
        `}
      </style>
      {richTextBanner}
    </>
  );
};

export default RichTextBanner;
