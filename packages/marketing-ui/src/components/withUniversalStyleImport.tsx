import { Brand } from '@ecom-next/utils/server';

export type StyleImport = string | ((brand: Brand) => string);

interface Props {
  brandName: Brand;
  criticalCss?: string[];
}

/** deprecated: TODO - figure out how to handle this efficiently */
export default function withUniversalStyleImport(styles: (brand: Brand) => string) {
  return function UniversalStyleImport(props: Props) {
    const outStyles = styles(props.brandName);
    return <style dangerouslySetInnerHTML={{ __html: outStyles }} />;
  };
}
