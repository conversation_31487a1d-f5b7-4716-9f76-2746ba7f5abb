import type { MarketingContent, MarketingPageType, PageProperties, CID } from './fetchMarketing';

export type NonSitewideType = Exclude<MarketingPageType, 'sitewide'>;
export type { MarketingContent, CID } from './fetchMarketing';

export interface ExtractedPageProperties {
  canonicalUrl?: string;
  non_indexable?: boolean;
  pageDescription: PageProperties['pageDescription'];
  pageTitle: PageProperties['pageTitle'];
}

export function extractPagePropertiesFromCmsData(
  cid: CID,
  contentData: MarketingContent<NonSitewideType> | undefined
): ExtractedPageProperties | Record<string, never> {
  // If there is no CMS content or no cid, we can't do anything useful
  if (!contentData) {
    return {};
  }

  const pagePropertiesKey = `${cid}/page-properties` as const;
  if (!(pagePropertiesKey in contentData)) {
    return {};
  }

  const contentItem = contentData[pagePropertiesKey]?.contentItems?.[0];

  if (contentItem) {
    const maybePageProperties = contentItem as PageProperties;
    const { pageTitle, pageDescription, url } = maybePageProperties;
    const canonicalUrl = url?.value;
    const retVal: ExtractedPageProperties = {
      pageTitle,
      pageDescription,
    };

    if (canonicalUrl) {
      retVal.canonicalUrl = canonicalUrl;
    }

    return retVal;
  }

  return {};
}
