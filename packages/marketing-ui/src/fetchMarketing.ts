import { CSSProperties } from 'react';
import { serverFetch } from '@ecom-next/utils/serverFetch';
import { Brand, getPageContext, Locale, Market, Page } from '@ecom-next/utils/server';
import logger from '@ecom-next/app/logger';
import moment from 'moment-timezone';
import TTLCache from '@isaacs/ttlcache';

const marketingCache = new TTLCache({ ttl: parseInt(process.env.PMCS_CACHE_TTL || '30000'), checkAgeOnGet: true });

const validCid = /^\d+$/;

export function isValidCid(cid: string | undefined): cid is CID {
  return cid !== undefined && validCid.test(cid);
}

const getPMCSUrl = (
  cid: string,
  pageType: Page,
  brand: Brand,
  market: Market,
  locale: Locale,
  contentApi?: string,
  previewDate?: string,
  targetEnv?: string,
  pid?: string,
  badgesCookie?: string,
  overrideLocale?: boolean,
  passBadgeIdToPMCS?: boolean
) => {
  const page = pageType === 'home' ? 'homepage' : cid;
  const updatedPageType = cid === 'sitewide' && pageType !== 'home' ? 'sitewide' : pageType;

  const isPreview = targetEnv === 'preview';
  const shouldAddContentApiQuery = isPreview && contentApi;
  const shouldAddPreviewDateQuery = isPreview && previewDate && !shouldAddContentApiQuery;

  const baseUrl = process.env.PMCS_SERVICE_URL || 'https://pmcs.aks.prod.azeus.gaptech.com';  
  const localeFormatted = locale.replace('-', '_');
  let queryParams =
    `brand=${brand}&locale=${localeFormatted}&market=${market}` +
    `&pageType=${updatedPageType}&statePageType=${pageType}&cidForPageType=${cid}${pid ? `&pid=${pid}` : ''}${passBadgeIdToPMCS && badgesCookie ? `&filter=${badgesCookie}` : ''}${overrideLocale ? '&overrideLocale=true' : ''}`;

  if (shouldAddContentApiQuery) {
    queryParams += `&contentApi=${encodeURIComponent(contentApi)}`;
  }

  if (shouldAddPreviewDateQuery) {
    const dateString = moment(new Date(previewDate)).toISOString();
    queryParams += `&previewDate=${encodeURIComponent(dateString)}`;
  }

  return `${baseUrl}/${page}?${queryParams}`;
};

export interface DesktopOverrideConfig {
  contrast: string;
  fullBleedOptions: FullBleedConfig;
  headerBackground?: string;
  isStickyEnabled?: boolean;
  isUtilityLinksEnabled?: boolean;
  stickyBackground?: 'transparent' | 'contrast';
  stickyScrollDirection?: 'up' | 'both';
  styles?: CSSProperties;
}

export interface FullBleedConfig {
  flyoutBackground?: string;
  fullBleedContrast: 'light' | 'dark';
  hasTransparencyLayer: boolean;
  isFullBleedEnabled: boolean;
}

export interface StyleConfig {
  configurationForCids?: string[];
  configurationForPageTypes?: string[];
  desktopOverride: DesktopOverrideConfig;
  details: string;
  fullBleedOptions: FullBleedConfig;
  headerBackground?: string;
  isStickyEnabled?: boolean;
  isUtilityLinksEnabled?: boolean;
  stickyBackground?: 'transparent' | 'contrast';
  stickyScrollDirection?: 'up' | 'both';
  styles?: CSSProperties;
}

export type CID = `${number}`;

const pageSlotsMap = {
  home: ['home'],
  sitewide: [
    'edfslarge',
    'edfssmall',
    'footer',
    'topnav',
    'hamnav',
    'header',
    'below-topnav',
    'logo',
    'countdown',
    'desktopemergencybanner',
    'mobileemergencybanner',
    'popup',
    'prefooter',
    'search',
    'headline',
    'secondary-headline',
    'preheader',
    'promodrawer',
    'promorover',
    'animatedheadline',
  ],
  profileui: [],
  checkout: [],
  category: [
    'ebb',
    'ism',
    'categorybottom1',
    'categorybottom2',
    'topleftnavcurtain',
    'bottomleftnavcurtain',
    'subcategorybanner',
    'rover',
    'seocategorydescription',
  ],
  division: ['certonafeed', 'categorygrid', 'seocategorydescription', 'page-properties'],
  product: ['pdp/banner1', 'in-situ'],
  shoppingbag: ['marketingbanner', 'productrecommendations', 'cardpromo'],
  utility: ['customhtml', 'content'],
  store: [],
  search: [],
} as const;

interface LegacyMarketingComponentData {
  components?: LegacyJsonContent[];
}
export interface LegacyJsonContent {
  CIID?: string;
  byCid?: StyleConfig[];
  byPageType?: StyleConfig[];
  data: LegacyMarketingComponentData;
  default?: StyleConfig;
  experimentRunning?: boolean;
  instanceName?: string;
  name: string;
  type: string;
}

export interface SeoContent {
  metaDescription?: string;
  pageDescription: string;
  pageTitle?: string;
  titleOverride?: string;
}
export interface MarketingResponse {
  _meta: {
    schema: `https://cms.gap.com/schema/content/v1/${string}`;
  };
  content?: unknown;
  output?: LegacyJsonContent;
}

export interface MarketingSlotResponse {
  content: {
    _meta: {
      schema: `https://cms.gap.com/schema/content/v1/${string}`;
    };
    content: MarketingResponse[];
  };
}

type PageSlotsMap = typeof pageSlotsMap;
type ContentSlotKey<K extends keyof PageSlotsMap> = K extends 'home'
  ? 'homepage'
  : K extends 'category' | 'division' | 'utility' | 'product'
    ? CID | 'error'
    : K;

export interface PageProperties {
  pageDescription: string;
  pageTitle: string;
  url?: {
    value: string;
  };
}

export type ContentSlotItems = {
  contentItems?: (MarketingResponse | MarketingSlotResponse | SeoContent | PageProperties)[];
};

export type MarketingPageType = Exclude<Page, 'other' | 'Error_Page'> | 'utility';
export type PMCSResponse<P extends MarketingPageType> = {
  [K in `${ContentSlotKey<P>}/${PageSlotsMap[P][number]}` | P]?: ContentSlotItems;
} & {
  content?: {
    '0'?: {
      contentItems: ContentSlotItems;
    };
    rawHtml?: string;
  };
};

export type SitewideJsonContent = {
  home?: LegacyJsonContent;
  sitewide?: {
    [K in PageSlotsMap['sitewide'][number]]: MarketingResponse['output'];
  };
};

type MarketingPageProperties = {
  [K in `${number}/page-properties`]?: ContentSlotItems;
};

interface LegacySeoOverrides {
  'meta.description'?: string;
  'meta.title.override'?: string;
}

export type MarketingContent<P extends MarketingPageType = 'sitewide'> = SitewideJsonContent & PMCSResponse<P> & MarketingPageProperties & LegacySeoOverrides;

const restrictedPageTypes = ['store', 'checkout', 'profileui', 'search'];
const shouldSkipPMCSCall = (pageType: string) => restrictedPageTypes.includes(pageType);

export async function fetchMarketing<P extends MarketingPageType>(
  cid: CID | 'home' | 'sitewide',
  page: Page,
  brand?: Brand,
  contentApi?: string,
  previewDate?: string,
  pid?: string,
  overrideLocale: boolean = false,
  passBadgeIdToPMCS: boolean = false
): Promise<MarketingContent<P>> {
  const updatedPageType = cid === 'sitewide' && page !== 'home' ? 'sitewide' : page;
  if (shouldSkipPMCSCall(updatedPageType)) {
    return Promise.resolve({ sitewide: {} } as PMCSResponse<P>);
  }

  const pageContext = getPageContext();
  const { brand: contextBrand, locale, market, badgesCookie } = pageContext;

  const targetEnv = process.env.TARGET_ENV || 'prod';

  const pmcsUrl = getPMCSUrl(cid, page, brand || contextBrand, market, locale, contentApi, previewDate, targetEnv, pid, badgesCookie, overrideLocale, passBadgeIdToPMCS);
  const log = logger.child({ cid, page, url: pmcsUrl });
  log.info('fetching content...');

  // If badgesCookie is present, skip all caching and always fetch fresh data
  if (badgesCookie) {
    try {
      const data = await serverFetch<PMCSResponse<P>>(pmcsUrl, {
        mode: 'no-cors',
        timeout: 4000,
        retries: 1,
      });
      return data;
    } catch (err) {
      logger.error('no content for cid ', page, err);
      return Promise.resolve({});
    }
  }

  const cacheKey = `${cid}-${page}-${brand || contextBrand}-${locale}-${market}-${contentApi || ''}-${previewDate || ''}-${targetEnv || ''}-${pid || ''}-${overrideLocale || ''}`;
  const fetchCacheKey = `${cacheKey}-fetch`;

  const eagerFetch = (requestUrl: string) => {
    const fetchRequest = serverFetch<PMCSResponse<P>>(requestUrl, {
      mode: 'no-cors',
      timeout: 4000,
      retries: 1,
    }).then(data => {
      marketingCache.delete(cacheKey);
      marketingCache.set(cacheKey, JSON.stringify(data));
      return data;
    });
    marketingCache.delete(fetchCacheKey);
    marketingCache.set(fetchCacheKey, fetchRequest);
  };

  try {
    const cachedData = marketingCache.get(cacheKey) as string | undefined;
    const dataTtlLeft = marketingCache.getRemainingTTL(cacheKey);

    if (cachedData) {
      if (dataTtlLeft < 5000) {
        eagerFetch(pmcsUrl);
      }

      log.info(`using cached data for fetchMarketing service : ${cacheKey}`);
      return Promise.resolve(JSON.parse(cachedData));
    }
    log.info(`missed cached data for fetchMarketing service: ${cacheKey}`);

    const fetchCachedRequest = marketingCache.get(fetchCacheKey);

    if (!fetchCachedRequest) {
      eagerFetch(pmcsUrl);
    }

    return await (marketingCache.get(fetchCacheKey) as Promise<PMCSResponse<P>>);
  } catch (err) {
    logger.error('no content for cid ', page, err);
    marketingCache.set(cacheKey, '{}');
    return Promise.resolve({});
  }
}

export const getPMCSMarketingData = fetchMarketing;
