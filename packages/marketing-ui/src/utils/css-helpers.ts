function hexToRgb(hex: string, useAlpha = true) {
  if (!hex) throw new Error('Invalid HEX format');
  let newHex = hex;

  if (hex.length === 7) newHex = hex.concat('ff');

  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?([a-f\d]{2})$/i.exec(newHex);

  if (!result) throw new Error('Invalid HEX format');

  return {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16),
    ...(useAlpha ? { a: parseInt(result[4], 16) / 255 } : {}),
  };
}

function rgbaToHex(rgba: string, useAlpha = true): string {
  const result = /^rgba?\((\d+)[,\s]*(\d+)[,\s]*(\d+)(?:[,\s]*[/,]?\s*([\d.]+))?\)$/i.exec(rgba);
  if (!result) throw new Error('Invalid RGBA format');

  const toHex = (value: number) => value.toString(16).padStart(2, '0');
  const r = parseInt(result[1], 10);
  const g = parseInt(result[2], 10);
  const b = parseInt(result[3], 10);
  const a = result[4] ? Math.round(parseFloat(result[4]) * 255) : 255;

  return `#${toHex(r)}${toHex(g)}${toHex(b)}${useAlpha ? toHex(a) : ''}`;
}

/**
 *
 * @param color accepts hex color with optional alpha values eg. #FF5733FF
 * @param colorSpace optionally accepts hex, hex with alpha, rgb(a) color spaces
 * @returns default or selected color space value eg. rgba(255, 87, 51, 1)
 */
export const setColorSpace = (color: string, colorSpace: 'hex' | 'hexAlpha' | 'rgb' | 'rgba' = 'hex'): string | undefined => {
  if (!color) return '';
  switch (colorSpace) {
    case 'hex':
      if (color.includes('rgb(')) return rgbaToHex(color, false);
      if (color.includes('rgba(')) return rgbaToHex(color);
      if (color.includes('hsl(')) {
        return color;
      }
      if (color.includes('#')) return color;
      break;
    case 'rgb':
      {
        if (color.includes('#')) {
          const rgb = hexToRgb(color, false);
          return rgb?.a ? `rgba(${rgb.r} ${rgb.g} ${rgb.b})` : `rgb(${rgb.r} ${rgb.g} ${rgb.b})`;
        }
      }
      break;
    case 'rgba':
      if (color.includes('#')) {
        const rgb = hexToRgb(color);
        return `rgba(${rgb.r} ${rgb.g} ${rgb.b} / ${rgb.a})`;
      }
      break;
    default:
      return color;
  }
};
