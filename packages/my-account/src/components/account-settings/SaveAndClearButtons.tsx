import React from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Button } from '@ecom-next/core/migration/button';

export type SaveAndClearButtonsProps = {
  onCancel: (e: React.MouseEvent<HTMLButtonElement>) => void;
  onSave: (e: React.MouseEvent<HTMLButtonElement>) => void;
};

const SaveAndClearButtons = ({ onCancel, onSave }: SaveAndClearButtonsProps) => {
  const { localize } = useLocalize();

  return (
    <div className='mt-4 flex'>
      <Button
        id='account-settings-item-save'
        data-testid='account-settings-item-save'
        fullWidth={false}
        className='[&_*]:!font-sourcesans !font-sourcesans m-0 box-border block h-11 w-[112px] rounded !bg-[#333] !px-8 py-2 align-middle text-base font-bold uppercase leading-normal tracking-[1px] text-white md:w-auto'
        onClick={onSave}
        kind='primary'
      >
        {localize('accountSettings.OTP.save')}
      </Button>
      <Button
        className='[&_*]:!font-sourcesans !font-sourcesans m-0 box-border block h-11 w-[112px] cursor-pointer items-center justify-center rounded border-none bg-white !px-5 py-2 text-center align-middle text-base font-normal uppercase not-italic leading-5 tracking-[1px] text-[#333] hover:bg-transparent focus:bg-transparent md:w-auto'
        fullWidth={false}
        id='account-settings-item-clear'
        data-testid='account-settings-item-clear'
        kind={'secondary'}
        onClick={onCancel}
      >
        {localize('accountSettings.OTP.cancel')}
      </Button>
    </div>
  );
};

export default SaveAndClearButtons;
