import { render, fireEvent, screen } from 'test-utils';
import datalayer from '@mfe/data-layer';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import type { Market } from '@ecom-next/utils/server';
import { State } from '../../../context/communication-preferences/types';
import { useCommunicationPreferences } from '../../../hooks/useCommunicationPreferences';
import * as requests from '../../../requests/communication-preferences/communicationPreferencesRequests';
import SubscriptionEmailForm, { Props } from '../SubscriptionEmailForm';
import { CommunicationPreferencesProvider } from '../../../context/communication-preferences/CommunicationPreferencesContext';

jest.mock('../../../hooks/useCommunicationPreferences');
jest.mock('../../../requests/communication-preferences/communicationPreferencesRequests', () => ({
  triggerSubscribePostReq: jest.fn(),
}));

jest.mock('@ecom-next/sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn(),
}));

const initialState = {
  communicationPreferencesDispatch: jest.fn(),
  communicationPreferencesState: {
    subscribePostReq: {
      isSuccess: false,
      isFail: false,
    },
  } as State,
};

const props = {
  market: 'us',
  selectedBrand: 'gp',
  commPrefSubscribeEmailSuccess: false,
  commPrefSubscribeEmailFail: false,
};

const renderComponent = (modifiedProps: Props) => {
  const { market } = modifiedProps;
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  const translationLocale = market === 'ca' ? 'en-CA' : 'en-US';
  return (
    <LocalizationProvider locale={translationLocale} translations={translations} market={market as Market}>
      <CommunicationPreferencesProvider>
        <SubscriptionEmailForm {...modifiedProps} />
      </CommunicationPreferencesProvider>
    </LocalizationProvider>
  );
};

describe('SubscribeEmail', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
    (requests.triggerSubscribePostReq as jest.Mock).mockReturnValue({});
    (usePageContext as jest.Mock).mockReturnValue({
      brandCode: 1,
      brandAbbr: 'gp',
      market: 'us',
      locale: 'en_US',
      ecomApiBaseUrl: 'https://api.gap.com',
    });
    (useCommunicationPreferences as jest.Mock).mockReturnValue({
      ...initialState,
      communicationPreferencesState: {
        ...initialState.communicationPreferencesState,
        subscribePostReq: { isSuccess: true, isFail: false },
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  describe('SubscriptionEmail Form', () => {
    it('should render form heading correctly', () => {
      render(renderComponent(props));
      expect(screen.getByText('Email Subscription')).toBeInTheDocument();
    });

    it('should render email input empty error message correctly', () => {
      render(renderComponent(props));
      const emailInput = screen.getByLabelText('Email Address');
      fireEvent.change(emailInput, { target: { value: '' } });
      fireEvent.blur(emailInput, { target: { value: '' } });
      expect(screen.getByText('Email address cannot be blank.')).toBeInTheDocument();
    });

    it('should render email input invalid error message correctly', () => {
      render(renderComponent(props));
      const emailInput = screen.getByLabelText('Email Address');
      fireEvent.change(emailInput, { target: { value: 'abc' } });
      fireEvent.blur(emailInput, { target: { value: 'abc' } });
      expect(screen.getByText('Enter a valid email address.')).toBeInTheDocument();
    });

    it('should display rewards section for Canada market correctly', () => {
      const modifiedProps = { ...props, market: 'ca' };
      render(renderComponent(modifiedProps));
      expect(screen.getByText(/Bonus! Rewards Members, use the email associated with your Membership for/)).toBeInTheDocument();
    });

    it('should not display rewards section for US market', () => {
      const modifiedProps = { ...props, market: 'us' };
      render(renderComponent(modifiedProps));
      expect(screen.queryByText(/Bonus! Rewards Members, use the email associated with your Membership for/)).toBeNull();
    });

    it('should display the category section for selected brands in Canada market', () => {
      const modifiedProps = { ...props, market: 'ca' };
      render(renderComponent(modifiedProps));
      expect(screen.getByText('Which categories are you interested in? (optional)')).toBeInTheDocument();
    });

    it('should render button text for Subscribe correctly in CA market', () => {
      const modifiedProps = { ...props, market: 'ca' };
      render(renderComponent(modifiedProps));
      expect(screen.getByText(/Subscribe/)).toBeInTheDocument();
    });

    it('should render button text for Select All correctly in CA market', () => {
      const modifiedProps = { ...props, market: 'ca' };
      render(renderComponent(modifiedProps));
      expect(screen.getByText(/Select All/)).toBeInTheDocument();
    });

    it('should render reward and sign-up options correctly in canada', () => {
      const modifiedProps = { ...props, market: 'ca', selectedBrand: 'at' };
      render(renderComponent(modifiedProps));
      expect(screen.getByText('Not a Rewards Member?')).toBeInTheDocument();
      expect(screen.getByText('Sign up for free today.')).toBeInTheDocument();

      expect(screen.getByRole('link', { name: 'Sign up for free today.' })).toHaveAttribute(
        'href',
        'https://athleta.gapcanada.ca/browse/info.do?cid=1184799&mlink=82632,1,EmailLP_1018_RewardsLP'
      );
    });

    it('should render checkboxes correctly for Gap', () => {
      render(renderComponent(props));
      expect(screen.getByText('Men')).toBeInTheDocument();
      expect(screen.getByText('Women')).toBeInTheDocument();
      const checkbox = screen.getByText('Men') as HTMLInputElement;
      fireEvent.click(checkbox);
    });

    it('should render checkboxes correctly for Athleta', () => {
      const modifiedProps = { ...props, selectedBrand: 'at' };
      render(renderComponent(modifiedProps));
      expect(screen.getByText('Athleta')).toBeInTheDocument();
      expect(screen.getByText('Athleta Girl')).toBeInTheDocument();
      expect(screen.queryByText('Men')).not.toBeInTheDocument();
      expect(screen.queryByText('Women')).not.toBeInTheDocument();
      const checkbox = screen.getByText('Athleta') as HTMLInputElement;
      fireEvent.click(checkbox);
    });

    it('should render PostalCodeField in Canda when selectedBrand is not Athleta', () => {
      const modifiedProps = { ...props, market: 'ca', selectedBrand: 'on' };
      render(renderComponent(modifiedProps));
      expect(screen.getByLabelText(/Postal Code/)).toBeInTheDocument();
      expect(screen.getByText('Please provide your postal code to receive information on events in your area.')).toBeInTheDocument();
    });

    it('should not render PostalCodeField in Canda when selectedBrand is Athleta', () => {
      const modifiedProps = { ...props, market: 'ca', selectedBrand: 'at' };
      render(renderComponent(modifiedProps));
      expect(screen.queryByText(/Postal Code/)).not.toBeInTheDocument();
      expect(screen.queryByText('Please provide your postal code to receive information on events in your area.')).not.toBeInTheDocument();
    });

    it('should render success notification correctly', () => {
      const state = { ...initialState };
      state.communicationPreferencesState.subscribePostReq.isFail = false;
      state.communicationPreferencesState.subscribePostReq.isSuccess = true;
      (useCommunicationPreferences as jest.Mock).mockReturnValue(state);
      render(renderComponent(props));
      expect(screen.getByText('Success! Your communication preferences have been updated.')).toBeInTheDocument();
    });

    it('should render error notification correctly when subscribe request fails', () => {
      const state = { ...initialState };
      state.communicationPreferencesState.subscribePostReq.isFail = true;
      state.communicationPreferencesState.subscribePostReq.isSuccess = false;
      (useCommunicationPreferences as jest.Mock).mockReturnValue(state);
      render(renderComponent(props));
      expect(screen.getByText("We weren't able to update your communication preferences. Please try again.")).toBeInTheDocument();
    });

    it('should handle language preference radio buttons correctly in Canada', () => {
      const modifiedProps = { ...props, market: 'ca' };
      render(renderComponent(modifiedProps));
      expect(screen.getByText('English')).toBeInTheDocument();
      expect(screen.getByText('French')).toBeInTheDocument();
      const firstRadio = screen.getByLabelText('English');
      const secondRadio = screen.getByLabelText('French');
      fireEvent.click(secondRadio);
      expect(secondRadio).toBeChecked();
      expect(firstRadio).not.toBeChecked();
      fireEvent.click(firstRadio);
      expect(firstRadio).toBeChecked();
      expect(secondRadio).not.toBeChecked();
    });

    it('should handle language preference radio buttons correctly in US', () => {
      const modifiedProps = { ...props, market: 'us' };
      render(renderComponent(modifiedProps));
      expect(screen.queryByText('English')).not.toBeInTheDocument();
      expect(screen.queryByText('French')).not.toBeInTheDocument();
    });

    it('should toggle maternity view when maternity is checked in selected category', () => {
      render(renderComponent(props));
      expect(screen.queryByText('Maternity')).toBeInTheDocument();
      const checkbox = screen.getByText('Maternity');
      fireEvent.click(checkbox);
      expect(screen.getByText('Select Month')).toBeInTheDocument();
      expect(screen.getByText('Select Year')).toBeInTheDocument();
    });

    it('should toggle birthday gift correctly ', () => {
      const modifiedProps = { ...props, selectedBrand: 'brfs' };
      render(renderComponent(modifiedProps));
      expect(screen.queryByText('Yes, send me my birthday gift')).toBeInTheDocument();
      const checkbox = screen.getByTestId('checkbox-input');
      fireEvent.click(checkbox);
      expect(checkbox).toBeChecked();
      expect(screen.getByText('Select Month')).toBeInTheDocument();
      expect(screen.getByText('Select Day')).toBeInTheDocument();
    });

    it('should fire a tealium link event on subscribe', () => {
      const tealiumLinkEventSpy = jest.spyOn(datalayer, 'link');
      render(renderComponent(props));
      const emailInput = screen.getByLabelText('Email Address');
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.blur(emailInput, { target: { value: '<EMAIL>' } });
      const button = screen.getByText(/Subscribe/);
      expect(button).toBeInTheDocument();
      fireEvent.click(button);
      expect(tealiumLinkEventSpy).toHaveBeenCalledTimes(1);
    });

    it('should render opt-in disclosure when in CA market', () => {
      const mockProps = {
        market: 'ca',
        selectedBrand: 'gp',
        commPrefSubscribeEmailSuccess: false,
        commPrefSubscribeEmailFail: false,
      };
      render(renderComponent(mockProps));
      expect(screen.getByText('Privacy Policy')).toBeInTheDocument();
      expect(screen.getByText('Contact Us')).toBeInTheDocument();
    });

    it('should not render opt-in disclosure when in US market', () => {
      const mockProps = {
        market: 'us',
        selectedBrand: 'gp',
        commPrefSubscribeEmailSuccess: false,
        commPrefSubscribeEmailFail: false,
      };
      render(renderComponent(mockProps));
      expect(screen.queryByText('Privacy Policy')).not.toBeInTheDocument();
      expect(screen.queryByText('Contact Us')).not.toBeInTheDocument();
    });

    it('should render birthday signup form when in Gap Factory CA market', () => {
      const mockProps = {
        market: 'ca',
        selectedBrand: 'gpfs',
        commPrefSubscribeEmailSuccess: false,
        commPrefSubscribeEmailFail: false,
      };
      render(renderComponent(mockProps));
      expect(screen.queryByText('Sign up for your birthday gift')).toBeInTheDocument();
    });
  });

  describe('SubscriptionEmail Headless', () => {
    const specialtyBrands = ['GP', 'ON', 'AT', 'BR', 'GPFS', 'BRFS'];
    const factoryBrandsUs = ['GPFS', 'BRFS'];
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const factoryBrandsCa = ['BRFS'];

    describe('SubscriptionEmail Headless US Specialty', () => {
      it.each(specialtyBrands)('should fire with the correct payload when email only is entered for headless email and preferences for %s', brand => {
        const lowerCaseBrand = brand.toLowerCase();
        const ecomApiBaseUrl = brand === 'GPFS' || brand === 'BRFS' ? 'https://api.gapfactory.com' : 'https://api.gap.com';
        (usePageContext as jest.Mock).mockReturnValue({
          brandCode: 1,
          brandAbbr: lowerCaseBrand,
          market: 'us',
          locale: 'en_US',
          ecomApiBaseUrl,
        });

        const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
        render(renderComponent({ ...props, selectedBrand: brand }));
        const button = screen.getByText(/Subscribe/);
        const emailInput = screen.getByLabelText('Email Address');
        fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
        fireEvent.click(button);
        expect(requestSpy).toHaveBeenCalledWith({
          emailPreferencesBody: {
            brand: brand,
            market: 'US',
            customerInfo: {
              emailAddress: '<EMAIL>',
              country: 'US',
            },
            languagePreference: 'en_US',
            sourceGenericCode: 'profileui',
            sourceSpecificCodeMap: {
              [brand]: 'communication-preferences',
            },
            selectedCategoriesMap: {
              [brand]: [],
            },
          },
          emailSubscribeBody: {
            emailAddress: '<EMAIL>',
            brand: brand,
            market: 'US',
            locale: 'en_US',
            mainSource: 'profileui',
            subSource: 'communication-preferences',
          },
          ecomApiBaseUrl,
          communicationPreferencesDispatch: expect.any(Function),
        });
      });

      describe('SubscriptionEmail Headless Gap US with Categories', () => {
        it('should fire with the correct payload when a category is selected for headless email and preferences for Gap US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          render(renderComponent(props));
          const mensCategory = screen.getByText(/Men/);
          const button = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(mensCategory);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(button);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'GP',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                GP: 'communication-preferences',
              },
              selectedCategoriesMap: {
                GP: ['gap_men'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'GP',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gap.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when all categories are selected for headless email and preferences for Gap US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          render(renderComponent(props));
          const allCategories = screen.getByText(/Select All/);
          const button = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(allCategories);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(button);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'GP',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                GP: 'communication-preferences',
              },
              selectedCategoriesMap: {
                GP: ['gap_women', 'gap_men', 'gap_maternity', 'gap_kids', 'gap_baby'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'GP',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gap.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when the maternity category is selected without the due date for headless email and preferences for Gap US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          render(renderComponent(props));
          const maternityOpt = screen.getByText(/Maternity/);
          const button = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(maternityOpt);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(button);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'GP',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                GP: 'communication-preferences',
              },
              selectedCategoriesMap: {
                GP: ['gap_maternity'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'GP',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gap.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when the maternity category is selected with the due date for headless email and preferences for Gap US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          render(renderComponent(props));
          const subscribeBtn = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          const maternityBtn = screen.getAllByText(/Maternity/)[0];
          expect(maternityBtn).toBeInTheDocument();
          fireEvent.click(maternityBtn);
          const selectMonthBtn = screen.getByRole('button', { name: /Select Month/i });
          const selelctYearBtn = screen.getByRole('button', { name: /Select Year/i });
          expect(selectMonthBtn).toBeInTheDocument();
          expect(selelctYearBtn).toBeInTheDocument();
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(selectMonthBtn);
          const januaryOpt = screen.getByText(/January/i);
          fireEvent.click(januaryOpt);
          fireEvent.click(selelctYearBtn);
          const yearOf2026 = screen.getByText(/2026/i);
          fireEvent.click(yearOf2026);
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'GP',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
                maternityMonth: '1',
                maternityYear: '2026',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                GP: 'communication-preferences',
              },
              selectedCategoriesMap: {
                GP: ['gap_maternity'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'GP',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gap.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });
      });

      describe('SubscriptionEmail Headless ON US should have no Categories', () => {
        it('should fire with the correct payload for headless email and preferences for ON US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'on',
            market: 'us',
            locale: 'en_US',
            ecomApiBaseUrl: 'https://api.gap.com',
          });
          render(renderComponent({ ...props, selectedBrand: 'on' }));
          const mensCategory = screen.queryByText(/Men/);
          expect(mensCategory).not.toBeInTheDocument();
          const subscribeBtn = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'ON',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                ON: 'communication-preferences',
              },
              selectedCategoriesMap: {
                ON: [],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'ON',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gap.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });
      });

      describe('SubscriptionEmail Headless BR US with Categories', () => {
        it('should fire with the correct payload when a category is selected for headless email and preferences for BR US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          render(renderComponent({ ...props, selectedBrand: 'br' }));
          const womenCategory = screen.getByText(/Women/);
          const button = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(womenCategory);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(button);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'BR',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                BR: 'communication-preferences',
              },
              selectedCategoriesMap: {
                BR: ['br_women'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'BR',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gap.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when all categories are selected for headless email and preferences for BR US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          render(renderComponent({ ...props, selectedBrand: 'br' }));
          const allCategories = screen.getByText(/Select All/);
          const button = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(allCategories);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(button);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'BR',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                BR: 'communication-preferences',
              },
              selectedCategoriesMap: {
                BR: ['br_women', 'br_men', 'br_home'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'BR',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gap.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });
      });

      describe('SubscriptionEmail Headless AT US with Categories', () => {
        it('should fire with the correct payload when a category is selected for headless email and preferences for AT US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          render(renderComponent({ ...props, selectedBrand: 'at' }));
          const athletaCategory = screen.getAllByText(/Athleta/)[0];
          const subscribeBtn = screen.getByText(/Subscribe/);
          const selectAllBtn = screen.getByText(/Select All/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(athletaCategory);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(subscribeBtn);
          expect(selectAllBtn).toBeInTheDocument();
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'AT',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                AT: 'communication-preferences',
              },
              selectedCategoriesMap: {
                AT: ['ath_women'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'AT',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gap.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when all categories are selected for headless email and preferences for AT US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          render(renderComponent({ ...props, selectedBrand: 'at' }));
          const subscribeBtn = screen.getByText(/Subscribe/);
          const selectAllBtn = screen.getByText(/Select All/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(selectAllBtn);
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'AT',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                AT: 'communication-preferences',
              },
              selectedCategoriesMap: {
                AT: ['ath_both'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'AT',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gap.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });
      });
    });

    describe('SubscriptionEmail Headless CA Specialty', () => {
      beforeEach(() => {
        (usePageContext as jest.Mock).mockReturnValue({
          brandCode: 1,
          brandAbbr: 'gp',
          market: 'ca',
          locale: 'en_CA',
        });
      });

      it.each(specialtyBrands)('should fire with the correct payload when email only is entered for headless email and preferences for %s', brand => {
        const lowerCaseBrand = brand.toLowerCase();
        const ecomApiBaseUrl = brand === 'GPFS' || brand === 'BRFS' ? 'https://api.gapfactory.ca' : 'https://api.gapcanada.ca';
        (usePageContext as jest.Mock).mockReturnValue({
          brandCode: 1,
          brandAbbr: lowerCaseBrand,
          market: 'ca',
          locale: 'en_CA',
          ecomApiBaseUrl,
        });

        const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
        render(renderComponent({ ...props, selectedBrand: brand, market: 'ca' }));
        const button = screen.getByText(/Subscribe/);
        const emailInput = screen.getByLabelText('Email Address');
        fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
        fireEvent.click(button);
        expect(requestSpy).toHaveBeenCalledWith({
          emailPreferencesBody: {
            brand: brand,
            market: 'CA',
            customerInfo: {
              emailAddress: '<EMAIL>',
              country: 'CA',
            },
            languagePreference: 'en_CA',
            sourceGenericCode: 'profileui',
            sourceSpecificCodeMap: {
              [brand]: 'communication-preferences',
            },
            selectedCategoriesMap: {
              [brand]: [],
            },
          },
          emailSubscribeBody: {
            emailAddress: '<EMAIL>',
            brand: brand,
            market: 'CA',
            locale: 'en_CA',
            mainSource: 'profileui',
            subSource: 'communication-preferences',
          },
          ecomApiBaseUrl,
          communicationPreferencesDispatch: expect.any(Function),
        });
      });

      describe('SubscriptionEmail Headless Gap CA with Categories', () => {
        beforeEach(() => {
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'gp',
            market: 'ca',
            locale: 'en_CA',
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
          });
        });

        it('should fire with the correct payload when a category is selected for headless email and preferences for Gap CA', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          render(renderComponent({ ...props, market: 'ca', selectedBrand: 'gp' }));
          const mensCategory = screen.getByText(/Men/);
          const button = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(mensCategory);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(button);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'GP',
              market: 'CA',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'CA',
              },
              languagePreference: 'en_CA',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                GP: 'communication-preferences',
              },
              selectedCategoriesMap: {
                GP: ['gap_ca_men'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'GP',
              market: 'CA',
              locale: 'en_CA',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when all categories are selected for headless email and preferences for Gap CA', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          render(renderComponent({ ...props, market: 'ca', selectedBrand: 'gp' }));
          const allCategories = screen.getByText(/Select All/);
          const button = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(allCategories);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(button);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'GP',
              market: 'CA',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'CA',
              },
              languagePreference: 'en_CA',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                GP: 'communication-preferences',
              },
              selectedCategoriesMap: {
                GP: ['gap_ca_women', 'gap_ca_men', 'gap_ca_maternity', 'gap_ca_kids', 'gap_ca_baby'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'GP',
              market: 'CA',
              locale: 'en_CA',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when the maternity category is selected without the due date for headless email and preferences for Gap CA', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          render(renderComponent({ ...props, market: 'ca', selectedBrand: 'gp' }));
          const maternityOpt = screen.getByText(/Maternity/);
          const button = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(maternityOpt);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(button);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'GP',
              market: 'CA',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'CA',
              },
              languagePreference: 'en_CA',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                GP: 'communication-preferences',
              },
              selectedCategoriesMap: {
                GP: ['gap_ca_maternity'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'GP',
              market: 'CA',
              locale: 'en_CA',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when the maternity category is selected with the due date for headless email and preferences for Gap CA', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          render(renderComponent({ ...props, market: 'ca', selectedBrand: 'gp' }));
          const subscribeBtn = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          const maternityBtn = screen.getAllByText(/Maternity/)[0];
          expect(maternityBtn).toBeInTheDocument();
          fireEvent.click(maternityBtn);
          const selectMonthBtn = screen.getByRole('button', { name: /Select Month/i });
          const selelctYearBtn = screen.getByRole('button', { name: /Select Year/i });
          expect(selectMonthBtn).toBeInTheDocument();
          expect(selelctYearBtn).toBeInTheDocument();
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(selectMonthBtn);
          const januaryOpt = screen.getByText(/January/i);
          fireEvent.click(januaryOpt);
          fireEvent.click(selelctYearBtn);
          const yearOf2026 = screen.getByText(/2026/i);
          fireEvent.click(yearOf2026);
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'GP',
              market: 'CA',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'CA',
                maternityMonth: '1',
                maternityYear: '2026',
              },
              languagePreference: 'en_CA',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                GP: 'communication-preferences',
              },
              selectedCategoriesMap: {
                GP: ['gap_ca_maternity'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'GP',
              market: 'CA',
              locale: 'en_CA',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });
      });

      describe('SubscriptionEmail Headless ON CA Categories', () => {
        it('should fire with the correct payload for headless email and preferences for ON CA', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'on',
            market: 'ca',
            locale: 'en_CA',
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
          });
          render(renderComponent({ ...props, market: 'ca', selectedBrand: 'on' }));
          const subscribeBtn = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          const mensCategory = screen.getAllByText(/Women/i)[0];
          const maternityBtn = screen.getAllByText(/Maternity/)[0];
          expect(maternityBtn).toBeInTheDocument();
          expect(mensCategory).toBeInTheDocument();
          fireEvent.click(mensCategory);
          fireEvent.click(maternityBtn);
          const selectMonthBtn = screen.getByRole('button', { name: /Select Month/i });
          const selelctYearBtn = screen.getByRole('button', { name: /Select Year/i });
          expect(selectMonthBtn).toBeInTheDocument();
          expect(selelctYearBtn).toBeInTheDocument();
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(selectMonthBtn);
          const januaryOpt = screen.getByText(/January/i);
          fireEvent.click(januaryOpt);
          fireEvent.click(selelctYearBtn);
          const yearOf2026 = screen.getByText(/2026/i);
          fireEvent.click(yearOf2026);
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'ON',
              market: 'CA',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'CA',
                maternityMonth: '1',
                maternityYear: '2026',
              },
              languagePreference: 'en_CA',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                ON: 'communication-preferences',
              },
              selectedCategoriesMap: {
                ON: ['on_ca_women', 'on_ca_maternity'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'ON',
              market: 'CA',
              locale: 'en_CA',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });
      });

      describe('SubscriptionEmail Headless BR CA with Categories', () => {
        it('should fire with the correct payload when a category is selected for headless email and preferences for BR CA', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'br',
            market: 'ca',
            locale: 'en_CA',
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
          });
          render(renderComponent({ ...props, market: 'ca', selectedBrand: 'br' }));
          const postalCode = 'A1A 1A1';
          const postalCodeInput = screen.getByTestId('postal-code-input');
          const womenCategory = screen.getByText(/Women/);
          const button = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(womenCategory);
          fireEvent.change(postalCodeInput, { target: { value: postalCode } });
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(button);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'BR',
              market: 'CA',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'CA',
                postalCode: 'A1A 1A1',
              },
              languagePreference: 'en_CA',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                BR: 'communication-preferences',
              },
              selectedCategoriesMap: {
                BR: ['br_ca_women'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'BR',
              market: 'CA',
              locale: 'en_CA',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when all categories are selected for headless email and preferences for BR CA', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'br',
            market: 'ca',
            locale: 'en_CA',
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
          });
          render(renderComponent({ ...props, market: 'ca', selectedBrand: 'br' }));
          const allCategories = screen.getByText(/Select All/);
          const button = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(allCategories);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(button);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'BR',
              market: 'CA',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'CA',
              },
              languagePreference: 'en_CA',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                BR: 'communication-preferences',
              },
              selectedCategoriesMap: {
                BR: ['br_ca_women', 'br_ca_men'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'BR',
              market: 'CA',
              locale: 'en_CA',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });
      });

      describe('SubscriptionEmail Headless AT CA with Categories', () => {
        it('should fire with the correct payload when a category is selected for headless email and preferences for AT CA', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'at',
            market: 'ca',
            locale: 'en_CA',
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
          });
          render(renderComponent({ ...props, market: 'ca', selectedBrand: 'at' }));
          const athletaGirl = screen.getAllByText(/Athleta Girl/i)[0];
          const subscribeBtn = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(athletaGirl);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'AT',
              market: 'CA',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'CA',
              },
              languagePreference: 'en_CA',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                AT: 'communication-preferences',
              },
              selectedCategoriesMap: {
                AT: ['ath_girls'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'AT',
              market: 'CA',
              locale: 'en_CA',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when all categories are selected for headless email and preferences for AT CA', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'at',
            market: 'ca',
            locale: 'en_CA',
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
          });
          render(renderComponent({ ...props, market: 'ca', selectedBrand: 'at' }));
          const subscribeBtn = screen.getByText(/Subscribe/);
          const selectAllBtn = screen.getByText(/Select All/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(selectAllBtn);
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'AT',
              market: 'CA',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'CA',
              },
              languagePreference: 'en_CA',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                AT: 'communication-preferences',
              },
              selectedCategoriesMap: {
                AT: ['ath_both'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'AT',
              market: 'CA',
              locale: 'en_CA',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapcanada.ca',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });
      });
    });

    describe('SubscriptionEmail Headless US Factory', () => {
      const ecomApiBaseUrl = 'https://api.gapfactory.com';
      it.each(factoryBrandsUs)('should fire with the correct payload when email only is entered for headless email and preferences for %s', brand => {
        (usePageContext as jest.Mock).mockReturnValue({
          brandCode: 1,
          brandAbbr: brand?.toLowerCase(),
          market: 'us',
          locale: 'en_US',
          ecomApiBaseUrl,
        });
        const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
        render(renderComponent({ ...props, selectedBrand: brand, market: 'us' }));
        const button = screen.getByText(/Subscribe/);
        const emailInput = screen.getByLabelText('Email Address');
        fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
        fireEvent.click(button);
        expect(requestSpy).toHaveBeenCalledWith({
          emailPreferencesBody: {
            brand: brand,
            market: 'US',
            customerInfo: {
              emailAddress: '<EMAIL>',
              country: 'US',
            },
            languagePreference: 'en_US',
            sourceGenericCode: 'profileui',
            sourceSpecificCodeMap: {
              [brand]: 'communication-preferences',
            },
            selectedCategoriesMap: {
              [brand]: [],
            },
          },
          emailSubscribeBody: {
            emailAddress: '<EMAIL>',
            brand: brand,
            market: 'US',
            locale: 'en_US',
            mainSource: 'profileui',
            subSource: 'communication-preferences',
          },
          ecomApiBaseUrl,
          communicationPreferencesDispatch: expect.any(Function),
        });
      });

      describe('SubscriptionEmail Headless Gap Factory US with Categories', () => {
        it('should fire with the correct payload when a category is selected for headless email and preferences for Gap Factory US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'gpfs',
            market: 'us',
            locale: 'en_US',
            ecomApiBaseUrl,
          });
          render(renderComponent({ ...props, selectedBrand: 'gpfs', market: 'us' }));
          const kidsCategory = screen.getByText(/Kids/);
          const babyCategory = screen.getByText(/Baby/);
          const subscribeBtn = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(kidsCategory);
          fireEvent.click(babyCategory);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'GPFS',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                GPFS: 'communication-preferences',
              },
              selectedCategoriesMap: {
                GPFS: ['gap_kids', 'gap_baby'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'GPFS',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapfactory.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when all categories are selected for headless email and preferences for Gap Factory US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'gpfs',
            market: 'us',
            locale: 'en_US',
            ecomApiBaseUrl,
          });
          render(renderComponent({ ...props, selectedBrand: 'gpfs', market: 'us' }));
          const allCategories = screen.getByText(/Select All/);
          const button = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(allCategories);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(button);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'GPFS',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                GPFS: 'communication-preferences',
              },
              selectedCategoriesMap: {
                GPFS: ['gap_women', 'gap_men', 'gap_kids', 'gap_baby'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'GPFS',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapfactory.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });
      });

      describe('SubscriptionEmail Headless BR Factory US with Categories', () => {
        it('should fire with the correct payload when a category is selected for headless email and preferences for BR Factory US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'brfs',
            market: 'us',
            locale: 'en_US',
            ecomApiBaseUrl,
          });
          render(renderComponent({ ...props, selectedBrand: 'brfs', market: 'us' }));
          const parentElement = screen.getByText('Sign up for your birthday gift').closest('div') as HTMLElement;
          const birthdayCheckbox = parentElement.querySelector('div[role="checkbox"][data-testid="checkbox-input"]');
          expect(birthdayCheckbox).toBeInTheDocument();
          const mensCategory = screen.getByText(/Women/);
          const subscribeBtn = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(mensCategory);
          fireEvent.click(birthdayCheckbox as HTMLElement);
          const selectMonthBtn = screen.getByRole('button', { name: /Select Month/i });
          const selelctDayBtn = screen.getByRole('button', { name: /Select Day/i });
          fireEvent.click(selectMonthBtn);
          const januaryOpt = screen.getByText(/January/i);
          fireEvent.click(januaryOpt);
          fireEvent.click(selelctDayBtn);
          const dayTwelve = screen.getByText(/12/i);
          fireEvent.click(dayTwelve);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'BRFS',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
                birthDate: '12',
                birthMonth: '1',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                BRFS: 'communication-preferences',
              },
              selectedCategoriesMap: {
                BRFS: ['brfs_womens'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'BRFS',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapfactory.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when all categories are selected for headless email and preferences for BR Factory US', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'brfs',
            market: 'us',
            locale: 'en_US',
            ecomApiBaseUrl,
          });
          render(renderComponent({ ...props, selectedBrand: 'brfs', market: 'us' }));
          const parentElement = screen.getByText('Sign up for your birthday gift').closest('div') as HTMLElement;
          const birthdayCheckbox = parentElement.querySelector('div[role="checkbox"][data-testid="checkbox-input"]');
          expect(birthdayCheckbox).toBeInTheDocument();
          const selectAllBtn = screen.getByText(/Select All/);
          const subscribeBtn = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(selectAllBtn);
          fireEvent.click(birthdayCheckbox as HTMLElement);
          const selectMonthBtn = screen.getByRole('button', { name: /Select Month/i });
          const selelctDayBtn = screen.getByRole('button', { name: /Select Day/i });
          fireEvent.click(selectMonthBtn);
          const januaryOpt = screen.getByText(/January/i);
          fireEvent.click(januaryOpt);
          fireEvent.click(selelctDayBtn);
          const dayTwelve = screen.getByText(/12/i);
          fireEvent.click(dayTwelve);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'BRFS',
              market: 'US',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'US',
                birthDate: '12',
                birthMonth: '1',
              },
              languagePreference: 'en_US',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                BRFS: 'communication-preferences',
              },
              selectedCategoriesMap: {
                BRFS: ['brfs_womens', 'brfs_mens'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'BRFS',
              market: 'US',
              locale: 'en_US',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapfactory.com',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });
      });

      describe('SubscriptionEmail Headless Factory CA with Categories', () => {
        it('should fire with the correct payload when a category is selected for headless email and preferences for BR Factory CA', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'brfs',
            market: 'ca',
            locale: 'en_CA',
            ecomApiBaseUrl: 'https://api.gapfactory.ca',
          });
          render(renderComponent({ ...props, selectedBrand: 'brfs', market: 'ca' }));
          const parentElement = screen.getByText('Sign up for your birthday gift').closest('div') as HTMLElement;
          const birthdayCheckbox = parentElement.querySelector('div[role="checkbox"][data-testid="checkbox-input"]');
          expect(birthdayCheckbox).toBeInTheDocument();
          const mensCategory = screen.getByText(/Women/);
          const subscribeBtn = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(mensCategory);
          fireEvent.click(birthdayCheckbox as HTMLElement);
          const selectMonthBtn = screen.getByRole('button', { name: /Select Month/i });
          const selelctDayBtn = screen.getByRole('button', { name: /Select Day/i });
          fireEvent.click(selectMonthBtn);
          const januaryOpt = screen.getByText(/January/i);
          fireEvent.click(januaryOpt);
          fireEvent.click(selelctDayBtn);
          const dayTwelve = screen.getByText(/12/i);
          fireEvent.click(dayTwelve);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'BRFS',
              market: 'CA',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'CA',
                birthDate: '12',
                birthMonth: '1',
              },
              languagePreference: 'en_CA',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                BRFS: 'communication-preferences',
              },
              selectedCategoriesMap: {
                BRFS: ['brfs_womens'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'BRFS',
              market: 'CA',
              locale: 'en_CA',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapfactory.ca',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when all categories are selected for headless email and preferences for BR Factory CA', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'brfs',
            market: 'ca',
            locale: 'en_CA',
            ecomApiBaseUrl: 'https://api.gapfactory.ca',
          });
          render(renderComponent({ ...props, selectedBrand: 'brfs', market: 'ca' }));
          const postalCode = 'A1A 1A1';
          const postalCodeInput = screen.getByTestId('postal-code-input');
          const parentElement = screen.getByText('Sign up for your birthday gift').closest('div') as HTMLElement;
          const birthdayCheckbox = parentElement.querySelector('div[role="checkbox"][data-testid="checkbox-input"]');
          expect(birthdayCheckbox).toBeInTheDocument();
          const selectAllBtn = screen.getByText(/Select All/);
          const subscribeBtn = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(selectAllBtn);
          fireEvent.click(birthdayCheckbox as HTMLElement);
          const selectMonthBtn = screen.getByRole('button', { name: /Select Month/i });
          const selelctDayBtn = screen.getByRole('button', { name: /Select Day/i });
          fireEvent.click(selectMonthBtn);
          const januaryOpt = screen.getByText(/January/i);
          fireEvent.click(januaryOpt);
          fireEvent.click(selelctDayBtn);
          const dayTwelve = screen.getByText(/12/i);
          fireEvent.click(dayTwelve);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.change(postalCodeInput, { target: { value: postalCode } });
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'BRFS',
              market: 'CA',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'CA',
                birthDate: '12',
                birthMonth: '1',
                postalCode: 'A1A 1A1',
              },
              languagePreference: 'en_CA',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                BRFS: 'communication-preferences',
              },
              selectedCategoriesMap: {
                BRFS: ['brfs_womens', 'brfs_mens'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'BRFS',
              market: 'CA',
              locale: 'en_CA',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapfactory.ca',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });

        it('should fire with the correct payload when a category is selected for headless email and preferences for Gap Factory CA', () => {
          const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
          (usePageContext as jest.Mock).mockReturnValue({
            brandCode: 1,
            brandAbbr: 'gpfs',
            market: 'ca',
            locale: 'en_CA',
            ecomApiBaseUrl: 'https://api.gapfactory.ca',
          });
          render(renderComponent({ ...props, selectedBrand: 'gpfs', market: 'ca' }));
          const mensCategory = screen.getByText(/Women/);
          const subscribeBtn = screen.getByText(/Subscribe/);
          const emailInput = screen.getByLabelText('Email Address');
          fireEvent.click(mensCategory);
          fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
          fireEvent.click(subscribeBtn);
          expect(requestSpy).toHaveBeenCalledWith({
            emailPreferencesBody: {
              brand: 'GPFS',
              market: 'CA',
              customerInfo: {
                emailAddress: '<EMAIL>',
                country: 'CA',
              },
              languagePreference: 'en_CA',
              sourceGenericCode: 'profileui',
              sourceSpecificCodeMap: {
                GPFS: 'communication-preferences',
              },
              selectedCategoriesMap: {
                GPFS: ['gap_ca_women'],
              },
            },
            emailSubscribeBody: {
              emailAddress: '<EMAIL>',
              brand: 'GPFS',
              market: 'CA',
              locale: 'en_CA',
              mainSource: 'profileui',
              subSource: 'communication-preferences',
            },
            ecomApiBaseUrl: 'https://api.gapfactory.ca',
            communicationPreferencesDispatch: expect.any(Function),
          });
        });
      });

      it('should fire with the correct payload when all categories are selected for headless email and preferences for Gap Factory CA', () => {
        const requestSpy = jest.spyOn(requests, 'triggerSubscribePostReq');
        (usePageContext as jest.Mock).mockReturnValue({
          brandCode: 1,
          brandAbbr: 'gpfs',
          market: 'ca',
          locale: 'en_CA',
          ecomApiBaseUrl: 'https://api.gapfactory.ca',
        });
        render(renderComponent({ ...props, selectedBrand: 'gpfs', market: 'ca' }));
        const postalCode = 'A1A 1A1';
        const postalCodeInput = screen.getByTestId('postal-code-input');
        const selectAllBtn = screen.getByText(/Select All/);
        const subscribeBtn = screen.getByText(/Subscribe/);
        const emailInput = screen.getByLabelText('Email Address');
        fireEvent.click(selectAllBtn);
        fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
        fireEvent.change(postalCodeInput, { target: { value: postalCode } });
        fireEvent.click(subscribeBtn);
        expect(requestSpy).toHaveBeenCalledWith({
          emailPreferencesBody: {
            brand: 'GPFS',
            market: 'CA',
            customerInfo: {
              emailAddress: '<EMAIL>',
              country: 'CA',
              postalCode: 'A1A 1A1',
            },
            languagePreference: 'en_CA',
            sourceGenericCode: 'profileui',
            sourceSpecificCodeMap: {
              GPFS: 'communication-preferences',
            },
            selectedCategoriesMap: {
              GPFS: ['gap_ca_women', 'gap_ca_men', 'gap_ca_kids', 'gap_ca_baby'],
            },
          },
          emailSubscribeBody: {
            emailAddress: '<EMAIL>',
            brand: 'GPFS',
            market: 'CA',
            locale: 'en_CA',
            mainSource: 'profileui',
            subSource: 'communication-preferences',
          },
          ecomApiBaseUrl: 'https://api.gapfactory.ca',
          communicationPreferencesDispatch: expect.any(Function),
        });
      });
    });

    describe('language selection', () => {
      it('should render the language selection for CA markets', () => {
        (usePageContext as jest.Mock).mockReturnValue({
          brandCode: 1,
          brandAbbr: 'gp',
          market: 'ca',
          locale: 'en_US',
          ecomApiBaseUrl: 'https://api.gapcanada.ca',
        });
        render(renderComponent({ ...props, market: 'ca', selectedBrand: 'gp' }));
        expect(screen.getByText('French')).toBeInTheDocument();
        expect(screen.getByText('English')).toBeInTheDocument();
      });

      it('should not render the language selection for AT CA', () => {
        (usePageContext as jest.Mock).mockReturnValue({
          brandCode: 1,
          brandAbbr: 'at',
          market: 'ca',
          locale: 'en_US',
          ecomApiBaseUrl: 'https://api.gapcanada.ca',
        });
        render(renderComponent({ ...props, market: 'ca', selectedBrand: 'at' }));
        expect(screen.queryByText('French')).not.toBeInTheDocument();
        expect(screen.queryByText('English')).not.toBeInTheDocument();
      });
    });
  });
});
