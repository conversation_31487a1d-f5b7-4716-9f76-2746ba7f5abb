import { useLocalize } from '@sitewide/providers/localization';
import { formatDollarAmout, formatDate } from '../../utils/gap-cards/cardUtils';

export type PeekInfoProps = {
  availableCredit: number;
  currentBalance: number;
  lastPaymentReceivedDate: string;
  paymentDueDate: string;
  statementBalance: number;
};

export const PeekInfo = (props: PeekInfoProps) => {
  const { currentBalance, availableCredit, lastPaymentReceivedDate, statementBalance, paymentDueDate } = props;
  const { localize } = useLocalize();

  return (
    <div className='flex flex-col '>
      <div className='flex justify-between font-medium'>
        <div>{localize('barclaysServicing.cardDetails.availableCredit')}</div>
        <div>${((availableCredit || availableCredit === 0) && formatDollarAmout(availableCredit)) || 'N/A'}</div>
      </div>
      <div className='flex justify-between text-[#4d4d4d]'>
        <div>{localize('barclaysServicing.cardDetails.currentBal')}</div>
        <div>${((currentBalance || currentBalance === 0) && formatDollarAmout(currentBalance)) || 'N/A'} </div>
      </div>
      <div className='flex justify-between text-[#4d4d4d]'>
        <div>{localize('barclaysServicing.cardDetails.lastPayment')}</div>
        <div>{(lastPaymentReceivedDate && formatDate(lastPaymentReceivedDate)) || 'N/A'}</div>
      </div>
      <div className='flex justify-between text-[#4d4d4d]'>
        <div>{localize('barclaysServicing.cardDetails.statementBal')}</div>
        <div>${((statementBalance || statementBalance === 0) && formatDollarAmout(statementBalance)) || 'N/A'}</div>
      </div>
      <div className='flex justify-between text-[#4d4d4d]'>
        <div>{localize('barclaysServicing.cardDetails.dueDate')}</div>
        <div>{(paymentDueDate && formatDate(paymentDueDate)) || 'N/A'}</div>
      </div>
    </div>
  );
};
