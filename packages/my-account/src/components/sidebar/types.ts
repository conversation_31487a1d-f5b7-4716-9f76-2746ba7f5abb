import type { Market } from '@ecom-next/utils/server';

type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type Brand = {
  athleta: string;
  bananarepublic: string;
  bananarepublicfactory?: string;
  gap: string;
  gapfactory?: string;
  oldnavy: string;
};

export type configUrl = {
  'account-security': string;
  'change-password': string;
  'communication-preferences': string;
  'gap-cards': string;
  'gift-cards-balance': string;
  home: string;
  'name-and-email': string;
  'order-details': string;
  'order-history': string;
  'order-lookup': string;
  'saved-cards': string;
  'shipping-addresses': string;
  'value-center': string;
};

export type BrandNames = 'gap' | 'bananarepublic' | 'athleta' | 'oldnavy' | 'gapfactory' | 'bananarepublicfactory';

export type Environment = 'prod' | 'preview' | 'stage' | 'stage-preview' | 'test' | 'development';

type EnvBase = {
  readonly [k in Environment]: string;
};

type EnvOptional = Optional<EnvBase, 'test' | 'stage-preview' | 'preview'>;

type MarketBase = {
  readonly [k in Market]: EnvOptional;
};

type MarketOptional = Optional<MarketBase, 'ca'>;

type BrandBase = {
  readonly [k in BrandNames]: MarketOptional;
};

export type HomeUrlRefs = {
  readonly FAQ: BrandBase;
  readonly checkGiftCardBalances: BrandBase;
  readonly communicationPreferences: BrandBase;
  readonly customerService: BrandBase;
  readonly myGapIncRewardsCreditCards: BrandBase;
  readonly myPointsRewards: BrandBase;
  readonly storeFinder: BrandBase;
};

export type RefsBase = {
  href?: string;
  icon: string;
  links: Links[] | [];
  title: string;
};

export type Links = {
  href?: string;
  title: string;
};

export enum ReturnTypeKeys {
  orderHistory,
  myPointsRewards,
  myGapIncRewardsCreditCards,
  manageAccount,
  giftCards,
  customerService,
}

export type ReturnType = {
  [ReturnTypeKeys.orderHistory]: RefsBase;
  [ReturnTypeKeys.myPointsRewards]: RefsBase;
  [ReturnTypeKeys.myGapIncRewardsCreditCards]: RefsBase;
  [ReturnTypeKeys.manageAccount]: RefsBase;
  [ReturnTypeKeys.giftCards]: RefsBase;
  [ReturnTypeKeys.customerService]: RefsBase;
};

export type GiftCardLinks = Links[] | [];
