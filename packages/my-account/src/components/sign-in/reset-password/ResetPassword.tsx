import { useEffect, useState, useCallback, useRef } from 'react';
import { But<PERSON> } from '@ecom-next/core/migration/button';
import { Email, IphoneApple, NotificationCriticalIcon } from '@ecom-next/core/migration/icons';
import { useLocalize } from '@sitewide/providers/localization';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import {
  LOADING_TEMPLATE,
  RESET_PASSWORD_BY_TEXT,
  RESET_PASSWORD_BY_EMAIL,
  RESET_PASSWORD_FORM_VIEW,
  RESET_PASSWORD_OPTIONS_VIEW,
  RESET_PASSWORD_EMAIL_SENT_VIEW,
  RESET_PASSWORD_VERIFY_CODE_VIEW,
} from '../../../constants/sign-in/signInConstants';
import { useSignIn } from '../../../hooks/useSignIn';
import { EmailInput } from '../../common/EmailInput';
import type { FormState } from '../types';
import { validateEmail } from '../../../utils/validation';
import { validateForm } from '../../../utils/validation/form/formValidation';
import { triggerResetPasswordReq, triggerResetPasswordSendCodeReq, triggerResetPasswordOptionsReq } from '../../../requests/sign-in/signInRequests';
import { Loader } from '../../common/loader/Loader';
import {
  RESET_RESET_PASSWORD_REQ,
  RESET_RESET_PASSWORD_SEND_CODE_REQ,
  RESET_RESET_PASSWORD_ALL,
  RESET_SIGN_IN_REQ,
} from '../../../constants/sign-in/signInActionTypes';
import { ErrorNotification } from '../../common/Notifications';
import { fireTealiumViewTag, fireResetPasswordOTPTealiumLinkTag } from '../../../utils/tealium/tealiumUtil';
import { ResetPasswordEmailSent } from './ResetPasswordEmailSent';
import { ResetPasswordVerifyCode } from './ResetPasswordVerifyCode';
import { ResetPasswordForm } from './ResetPasswordForm';

type Props = {
  returnToSignIn: () => void;
  setSignInView: () => void;
};

type ResetPasswordOptions = typeof RESET_PASSWORD_BY_TEXT | typeof RESET_PASSWORD_BY_EMAIL;

type Views =
  | typeof RESET_PASSWORD_FORM_VIEW
  | typeof RESET_PASSWORD_OPTIONS_VIEW
  | typeof RESET_PASSWORD_EMAIL_SENT_VIEW
  | typeof RESET_PASSWORD_VERIFY_CODE_VIEW;

export const ResetPassword = ({ returnToSignIn, setSignInView }: Props): JSX.Element => {
  const { localize } = useLocalize();
  const { signInState, signInDispatch } = useSignIn();
  const { market, locale, brandAbbr, brandCode } = usePageContext();
  const brandTealium = brandAbbr === 'gpfs' ? 'gapfs' : brandAbbr;
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [isRetrySuccess, setIsRetrySuccess] = useState<boolean>(false);
  const [isAccountLocked, setIsAccountLocked] = useState<boolean>(false);
  const [isRetry, setIsRetry] = useState<boolean | undefined>(undefined);
  const [view, setView] = useState<Views>(RESET_PASSWORD_OPTIONS_VIEW);
  const [isSendCodeReqInFlight, setIsSendCodeReqInFlight] = useState<boolean>(false);
  const [isSendEmailReqInFlight, setIsSendEmailReqInFlight] = useState<boolean>(false);

  const {
    user: { email, maskedPhoneNumber },
    signInReq,
    resetPasswordReq,
    resetPasswordOptionsReq,
    resetPasswordSendCodeReq,
    resetPasswordVerifyCodeReq,
  } = signInState;

  const errorMap = {
    email: localize('accountSettings.errors.required.email'),
  } as const;

  const [formData, setFormData] = useState<FormState>({
    email: {
      value: email,
      errorMessage: '',
      hasError: false,
    },
  });

  const maskedPhoneNumberRef = useRef(maskedPhoneNumber);

  const onReturnToSignIn = useCallback((e: React.MouseEvent<HTMLButtonElement> | React.KeyboardEvent<HTMLButtonElement>, type: ResetPasswordOptions): void => {
    e?.preventDefault();
    if (e.type === 'click' || (e as React.KeyboardEvent<HTMLButtonElement>).key === 'Enter') {
      if (type === RESET_PASSWORD_BY_TEXT) {
        signInDispatch({ type: RESET_SIGN_IN_REQ });
        signInDispatch({ type: RESET_RESET_PASSWORD_ALL });
        returnToSignIn();
      } else if (type === RESET_PASSWORD_BY_EMAIL) {
        window.location.reload();
      }
    }
  }, []);

  const onSendCode = useCallback(
    (event?: React.MouseEvent<HTMLButtonElement> | React.KeyboardEvent<HTMLButtonElement>, retry?: boolean): void => {
      event?.preventDefault();
      const isReqInFlight = isSendEmailReqInFlight || isSendCodeReqInFlight;
      if (!isReqInFlight) {
        fireResetPasswordOTPTealiumLinkTag({
          brandCode,
          brandTealium,
          page: maskedPhoneNumberRef ? 'Forgot Password - phone# verified' : 'Forgot Password - phone# not verified',
          eventName: retry ? 'resend-otp' : 'text-otp-flow-start',
        });
        setIsRetry(retry);
        setIsRetrySuccess(false);
        setIsSendCodeReqInFlight(true);
        signInDispatch({ type: RESET_RESET_PASSWORD_REQ });
        signInDispatch({ type: RESET_RESET_PASSWORD_SEND_CODE_REQ });
        const payload = {
          locale,
          market,
          mfaType: 'SMS',
          emailAddress: formData.email.value,
          signInDispatch,
        };
        triggerResetPasswordSendCodeReq(payload);
      }
    },
    [isSendCodeReqInFlight, isSendEmailReqInFlight]
  );

  const onSendEmail = useCallback(
    (event?: React.FormEvent<HTMLFormElement>): void => {
      event?.preventDefault();
      const isReqInFlight = isSendEmailReqInFlight || isSendCodeReqInFlight;
      if (!isReqInFlight) {
        const { newFormData, isFormValid } = validateForm(formData, errorMap);
        setFormData((prevState: FormState) => ({
          ...prevState,
          ...newFormData,
        }));
        if (isFormValid) {
          fireResetPasswordOTPTealiumLinkTag({
            brandCode,
            brandTealium,
            page: maskedPhoneNumberRef ? 'Forgot Password - phone# verified' : 'Forgot Password - phone# not verified',
            eventName: 'send_email_password',
          });
          setIsSendEmailReqInFlight(true);
          signInDispatch({ type: RESET_RESET_PASSWORD_REQ });
          signInDispatch({ type: RESET_RESET_PASSWORD_SEND_CODE_REQ });
          const payload = {
            locale,
            signInDispatch,
            emailAddress: newFormData.email.value,
            brandCode: brandAbbr.toUpperCase(),
          };
          triggerResetPasswordReq(payload);
        } else if (!isFormValid) {
          const errorMessage = localize(validateEmail(formData.email.value)) || '';
          setFormData((prevState: FormState) => ({
            ...prevState,
            email: {
              value: formData.email.value,
              errorMessage: errorMessage,
              hasError: !!errorMessage,
            },
          }));
        }
      }
    },
    [isSendEmailReqInFlight, isSendCodeReqInFlight]
  );

  const onEmailChange = (modifiedEmail: string, errorMessage: string): void => {
    setFormData((prevState: FormState) => ({
      ...prevState,
      email: {
        value: modifiedEmail,
        errorMessage: errorMessage,
        hasError: !!errorMessage,
      },
    }));
  };

  const onEmailBlur = (modifiedEmail: string, errorMessage: string): void => {
    setFormData((prevState: FormState) => ({
      ...prevState,
      email: {
        value: modifiedEmail,
        errorMessage: errorMessage,
        hasError: !!errorMessage,
      },
    }));
  };

  const renderAccountLocked = () => {
    return isAccountLocked ? (
      <div className='bg-cb-alert-error-muted mt-6 flex p-4'>
        <div className='flex'>
          <NotificationCriticalIcon />
        </div>
        <p className='cb-base-note ml-4'>{localize('accountLogin.login.accountLocked')}</p>
      </div>
    ) : (
      <></>
    );
  };

  const renderTextOrEmail = () => {
    return maskedPhoneNumber ? (
      <div className='mt-6'>
        <Button loadingAnimationStatus={isSendCodeReqInFlight} onClick={onSendCode} kind='secondary' data-testid='rp-send-otp-code-btn' fullWidth>
          <div className='flex justify-center'>
            <IphoneApple />
            <p className='cb-base-default-emphasis ml-2'>{localize('accountLogin.login.sendVerificationCodeByText')}</p>
          </div>
        </Button>
        <div className='mt-2'>
          <Button loadingAnimationStatus={isSendEmailReqInFlight} onClick={onSendEmail} kind='secondary' data-testid='rp-send-eamil-link-btn' fullWidth>
            <div className='flex justify-center'>
              <Email />
              <p className='cb-base-default-emphasis ml-2'>{localize('accountLogin.login.sendVerificationLinkToEmail')}</p>
            </div>
          </Button>
        </div>
      </div>
    ) : (
      <></>
    );
  };

  const renderEmail = () => {
    return !maskedPhoneNumber ? (
      <form onSubmit={onSendEmail} className='mt-6' noValidate>
        <div>
          <EmailInput
            name='email'
            label={localize('accountLogin.form.emailLabel')}
            onBlur={onEmailBlur}
            onChange={onEmailChange}
            data-testid='rp-email-input'
            errorMessage={formData.email.errorMessage}
            hasError={formData.email.hasError}
            value={formData.email.value}
            autoComplete='email'
          />
          <div className='relative mx-auto w-[343px] py-6'>
            <Button loadingAnimationStatus={isSendEmailReqInFlight} onClick={onSendEmail} kind='primary' data-testid='rp-send-eamil-link-btn' fullWidth>
              <div className='flex justify-center space-x-2'>
                <Email isWhite={true} />
                <p className='cb-base-default-emphasis font-bold text-white'>{localize('resetYourPasswordModal.forcedResetPassword.btnText')}</p>
              </div>
            </Button>
          </div>
        </div>
      </form>
    ) : (
      <></>
    );
  };

  useEffect(() => {
    if (resetPasswordOptionsReq.isSuccess) {
      fireTealiumViewTag({
        brandCode,
        brandTealium,
        page: maskedPhoneNumber ? 'Forgot Password - phone# verified' : 'Forgot Password - phone# not verified',
        recognitionStatus: 'unrecognized',
      });
      setIsLoaded(true);
    } else if (resetPasswordOptionsReq.isFail) {
      setSignInView();
      setIsLoaded(false);
    }
  }, [resetPasswordOptionsReq.isSuccess, resetPasswordOptionsReq.isFail]);

  useEffect(() => {
    if (resetPasswordReq.isSuccess) {
      fireTealiumViewTag({
        brandCode,
        brandTealium,
        page: 'profile:Check Inbox',
        recognitionStatus: 'unrecognized',
      });
      setIsSendEmailReqInFlight(false);
      setView(RESET_PASSWORD_EMAIL_SENT_VIEW);
    } else if (resetPasswordReq.isFail) {
      setIsSendEmailReqInFlight(false);
    }
  }, [resetPasswordReq.isSuccess, resetPasswordReq.isFail]);

  useEffect(() => {
    if (resetPasswordSendCodeReq.isSuccess) {
      fireTealiumViewTag({
        brandCode,
        brandTealium,
        page: 'Forgot Password- verification code',
        recognitionStatus: 'unrecognized',
      });
      if (isRetry) {
        setIsRetry(false);
        setIsRetrySuccess(true);
      }
      setIsSendCodeReqInFlight(false);
      setView(RESET_PASSWORD_VERIFY_CODE_VIEW);
    } else if (resetPasswordSendCodeReq.isFail) {
      setIsAccountLocked(false);
      setIsSendCodeReqInFlight(false);
    }
  }, [resetPasswordSendCodeReq.isSuccess, resetPasswordSendCodeReq.isFail]);

  useEffect(() => {
    if (resetPasswordVerifyCodeReq.isSuccess) {
      setView(RESET_PASSWORD_FORM_VIEW);
    }
  }, [resetPasswordVerifyCodeReq.isSuccess]);

  useEffect(() => {
    if (signInReq.isFail && signInReq.isAccountLocked) {
      setIsAccountLocked(true);
      signInDispatch({ type: RESET_SIGN_IN_REQ });
    }
  }, [signInReq.isFail, signInReq.isAccountLocked]);

  useEffect(() => {
    const payload = {
      emailAddress: email,
      market: market,
      signInDispatch,
    };
    triggerResetPasswordOptionsReq(payload);
    return () => {
      setIsRetry(false);
      setIsLoaded(false);
      setIsRetrySuccess(false);
      setIsAccountLocked(false);
      setIsSendCodeReqInFlight(false);
      setIsSendEmailReqInFlight(false);
    };
  }, []);

  return (
    <div data-testid='send-text-or-email'>
      {!isLoaded && (
        <div data-testid='send-text-or-email-loader'>
          <Loader template={LOADING_TEMPLATE} />
        </div>
      )}
      {isLoaded && (
        <div className='justify-center'>
          <div className='px-4'>
            {(resetPasswordReq.isFail || resetPasswordSendCodeReq.isFail) && (
              <div className='pt-6'>
                <ErrorNotification message={localize('forgotPasswordOTP.resetPasswordError')} />
              </div>
            )}
            {view === RESET_PASSWORD_OPTIONS_VIEW && (
              <>
                <div>
                  {renderAccountLocked()}
                  <div className='cb-display-lg-emphasis mt-4'>{localize('accountLogin.login.sendEmailTitle')}</div>
                  <div className='cb-base-default mt-4'>{localize('accountLogin.login.emailTextHeader')}</div>
                </div>
                {renderEmail()}
                {renderTextOrEmail()}
              </>
            )}
          </div>
          {view === RESET_PASSWORD_EMAIL_SENT_VIEW && (
            <ResetPasswordEmailSent email={formData.email.value} onSendEmail={onSendEmail} onReturnToSignIn={onReturnToSignIn} />
          )}
          {view === RESET_PASSWORD_VERIFY_CODE_VIEW && (
            <ResetPasswordVerifyCode
              onSendCode={onSendCode}
              isRetrySuccess={isRetrySuccess}
              onReturnToSignIn={onReturnToSignIn}
              maskedPhoneNumber={maskedPhoneNumber}
            />
          )}
          {view === RESET_PASSWORD_FORM_VIEW && <ResetPasswordForm setSignInView={setSignInView} />}
        </div>
      )}
    </div>
  );
};
