import { useLocalize } from '@sitewide/providers/localization';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { Tabs } from '../../../tabs/ValueCenterTabs';
import { CheckMarkItem } from './CheckMarkItem';

type ExclusivesProps = {
  onSetActiveTab: (tab: Tabs, tabSection?: string) => void;
};

export const Exclusives = (props: ExclusivesProps) => {
  const { onSetActiveTab } = props;
  const { localize } = useLocalize();
  const { market } = usePageContext();
  const isCanada = market === 'ca';

  const handleTabSelection = (tabSection: string) => {
    onSetActiveTab(Tabs.EARN_AND_REDEEM, tabSection);
  };

  if (isCanada) {
    return <></>;
  }
  return (
    <div className='max655:w-auto max655:divide-none m-4 mb-8 box-border w-full max-w-[623px] flex-col divide-y divide-[#d4d4d4] rounded shadow-[0_1px_4px_rgba(0,0,0,0.25)]'>
      <>
        <h1 className='max655:text-center m-4 text-left font-bold'>{localize('valueCenter.membership.exclusives.header')}</h1>
        <div className='max655:flex-col max655:mb-4 flex w-full items-stretch'>
          <div
            className='max655:flex-col flex basis-1/2 cursor-pointer flex-row items-center p-4 hover:underline'
            role='button'
            tabIndex={0}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleTabSelection('convertPoints');
              }
            }}
            onClick={() => handleTabSelection('convertPoints')}
          >
            <p className='max655:w-auto max655:text-center min-w-[72px] text-left font-bold'>{localize('valueCenter.membership.exclusives.gp')}</p>
            <p className='max655:text-center p-4'> {localize('valueCenter.membership.exclusives.gpCopytext')}</p>
          </div>
          <div className='flex basis-1/2 items-center '>
            <p className='max655:min-h-[36px] flex min-h-[80px] basis-1/3 items-center justify-center self-stretch text-center'>
              <CheckMarkItem type='dot' count={1} />
            </p>
            <p className='max655:min-h-[36px] flex min-h-[80px] basis-1/3 items-center justify-center self-stretch border-x border-[#d4d4d4] text-center'>
              <CheckMarkItem type='dot' count={1} />
            </p>
            <p className='max655:min-h-[36px] flex min-h-[80px] basis-1/3 items-center justify-center self-stretch text-center'>
              <CheckMarkItem type='dot' count={1} />
            </p>
          </div>
        </div>
        <div className='max655:flex-col max655:mb-4 flex w-full items-stretch'>
          <div className='max655:flex-col relative flex basis-1/2 flex-row items-center p-4'>
            <p className='max655:w-auto min-w-[72px] font-bold'>{localize('valueCenter.membership.exclusives.br')}</p>
            <p className='max655:text-center p-4'> {localize('valueCenter.membership.exclusives.brCopytext1')}</p>
            <div className='max655:hidden absolute bottom-0 mb-[-10px] ml-[-16px] h-[20px] w-[120px] border-0 bg-white'></div>
          </div>
          <div className='flex basis-1/2'>
            <p className=' max655:min-h-[36px] flex min-h-[80px] min-w-[60px] basis-1/3 items-center justify-center self-stretch text-center'></p>
            <p className=' max655:min-h-[36px] flex min-h-[80px] min-w-[60px] basis-1/3 items-center justify-center self-stretch border-x border-[#d4d4d4] text-center'></p>
            <p className=' max655:min-h-[36px] flex min-h-[80px] min-w-[60px] basis-1/3 items-center justify-center self-stretch text-center'>
              <CheckMarkItem type='dot' count={1} />
            </p>
          </div>
        </div>
        <div className='max655:flex-col max655:mb-4 flex w-full items-stretch'>
          <div
            className='max655:flex-col flex basis-1/2 cursor-pointer flex-row items-center p-4 hover:underline'
            role='button'
            tabIndex={0}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleTabSelection('convertPoints');
              }
            }}
            onClick={() => handleTabSelection('convertPoints')}
          >
            <div className='min-w-[72px]'></div>
            <p className='max655:text-center cursor-pointer p-4 hover:underline'> {localize('valueCenter.membership.exclusives.brCopytext2')}</p>
          </div>
          <div className='flex basis-1/2'>
            <p className='max655:min-h-[36px] flex min-h-[80px] basis-1/3 items-center justify-center self-stretch text-center'>
              <CheckMarkItem type='dot' count={1} />
            </p>
            <p className='max655:min-h-[36px] flex min-h-[80px] basis-1/3 items-center justify-center self-stretch border-x border-[#d4d4d4] text-center'>
              <CheckMarkItem type='dot' count={1} />
            </p>
            <p className='max655:min-h-[36px] flex min-h-[80px] basis-1/3 items-center justify-center self-stretch text-center'>
              <CheckMarkItem type='dot' count={1} />
            </p>
          </div>
        </div>
        <div className='max655:flex-col max655:mb-4 flex w-full items-stretch'>
          <div
            className='max655:flex-col flex basis-1/2 cursor-pointer flex-row items-center p-4 hover:underline'
            role='button'
            tabIndex={0}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleTabSelection('convertPoints');
              }
            }}
            onClick={() => handleTabSelection('convertPoints')}
          >
            <p className='max655:w-auto min-w-[72px] font-bold'>{localize('valueCenter.membership.exclusives.on')}</p>
            <p className='max655:text-center p-4'> {localize('valueCenter.membership.exclusives.onCopytext')}</p>
          </div>
          <div className='flex basis-1/2'>
            <p className='max655:min-h-[36px] flex min-h-[80px] basis-1/3 items-center justify-center self-stretch text-center'>
              <CheckMarkItem type='dot' count={1} />
            </p>
            <p className='max655:min-h-[36px] flex min-h-[80px] basis-1/3 items-center justify-center self-stretch border-x border-[#d4d4d4] text-center'>
              <CheckMarkItem type='dot' count={1} />
            </p>
            <p className='max655:min-h-[36px] flex min-h-[80px] basis-1/3 items-center justify-center self-stretch text-center'>
              <CheckMarkItem type='dot' count={1} />
            </p>
          </div>
        </div>
      </>
    </div>
  );
};
