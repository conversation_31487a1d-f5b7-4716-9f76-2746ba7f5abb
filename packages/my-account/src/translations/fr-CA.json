{"quickAdd.colorLabel": "Color", "quickAdd.variantGroupLabel": "Variants", "quickAdd.selectASize": "Select a Size", "quickAdd.openModal": "Open Quick Add", "quickAdd.closeModal": "Close Quick Add", "quickAdd.addToBag": "Add to Bag", "quickAdd.addToBagSuccess": "Added to Bag", "quickAdd.addToBagFailure": "Item Not Added", "quickAdd.error.general": "We're having a technical problem on this page. Refresh the page and try your selection again or find another product.", "quickAdd.error.sizeSelection": "Please select a {{unselectedDimensions}} before adding to bag.", "quickAdd.error.itemOutOfStock": "We're sorry, this item is now out of stock. Try another selection.", "quickAdd.onBackOrderMessage": "On back order - est. shipping date {{date}}", "quickAdd.onBackOrderMessage.ariaLabel": "On back order. Estimated shipping date is {{date}}", "homeRedesign.breadcrumbs.home": "Page d’accueil", "homeRedesign.breadcrumbs.account": "<PERSON><PERSON><PERSON>", "homeRedesign.breadcrumbs.settings": "Renseignements personnels", "homeRedesign.breadcrumbs.changePassword": "Changer le mot de passe", "homeRedesign.breadcrumbs.orderDetails": "<PERSON><PERSON><PERSON> de la commande", "homeRedesign.breadcrumbs.shippingAddresses": "<PERSON><PERSON><PERSON>", "homeRedesign.breadcrumbs.orderHistory": "Historique des commandes", "homeRedesign.breadcrumbs.savedCards": "Cartes sa<PERSON>", "homeRedesign.breadcrumbs.orderLookup": "Recherche d’une commande d’un invité", "homeRedesign.breadcrumbs.communicationPreferences": "Préférences de communication", "homeRedesign.breadcrumbs.giftCardsBalance": "Gift Cards Balance", "homeRedesign.breadcrumbs.valueCenter": "Mes points et récompenses", "homeRedesign.breadcrumbs.myPointsRewardsNew": "Points et récompenses", "homeRedesign.breadcrumbs.purchaseHistory": "Historique des achats", "homeRedesign.breadcrumbs.details": "Détails", "homeRedesign.breadcrumbs.accountSecurity": "Sûreté du compte", "homeRedesign.sidebarNav.userCardText": "Bonjou<PERSON>,", "homeRedesign.sidebarNav.personalInfo": "Renseignements personnels", "homeRedesign.giftCardNav.checkGiftCardBalance": "Vérifier le solde des cartes-cadeaux", "homeRedesign.giftCardNav.reloadGiftCard": "Recharger la carte-cadeau", "homeRedesign.certona.title": "Recently Viewed", "homeRedesign.certona.previous": "Prev", "homeRedesign.certona.next": "Next", "homeRedesign.errors.myPointsRewards": "L’historique de vos points et récompenses n’est pas accessible pour le moment. Veuillez réessayer plus tard.", "homeRedesign.errors.rewardsNotAvailable": "Nous avons de la difficulté à accéder à vos données sur vos récompenses. Consultez la section Mes ", "homeRedesign.errors.pointsRewardsLinkText": "points et récompenses", "homeRedesign.errors.forMoreInfo": " pour obtenir plus d’information.", "homeRedesign.errors.myRewardsCreditCards": "Your Rewards Credit Cards activity is not available at this time. Please try again later.", "homeRedesign.myRewardsCreditCards": "My Rewards Credit Cards", "homeRedesign.myRewardsCreditCardsNew": "Rewards Credit Cards", "homeRedesign.rewardsTileRedeemText": "Les récompenses sont échangeables à la caisse.", "homeRedesign.rewardsTileAvailableText": "de récompenses disponibles.", "homeRedesign.rewardsCardMemberText": "Membre du programme Récompenses", "homeRedesign.tierText.core": "CLASSIQUE", "homeRedesign.tierText.enthusiast": "ENTHOUSIASTE", "homeRedesign.tierText.icon": "ÉLITE", "communicationPreferences.pageHeading": "Préférences en matière de communications", "communicationPreferences.formHeading": "Abonnement aux courriels", "communicationPreferences.emailLabel": "<PERSON><PERSON><PERSON>", "communicationPreferences.buttonText": "<PERSON><PERSON>abonner", "communicationPreferences.emailOptIn": "Oui! J’accepte de recevoir des courriels de marketing (nouvelles sur la mode et offres exclusives) de Gap Inc. et de ses sociétés affiliées : Old Navy (Canada) Inc. et Gap (Canada) Inc. et de leurs marques : Gap, Old Navy, Athleta et Banana Republic.", "communicationPreferences.textNotification": "NOTIFICATION PAR TEXTO", "communicationPreferences.checkBoxText": "Oui! Envoyez-moi des messages texte sur les aubaines et les promotions.", "communicationPreferences.textNotificationForBR": "<PERSON><PERSON>ez-vous à la conversation", "communicationPreferences.checkBoxTextForBR": "Inscrivez-vous pour recevoir les messages texte de Banana Republic et profitez d'une offre de bienvenue de 20 % de réduction, d'un accès exclusif et bien plus.", "communicationPreferences.checkBoxTextForBRFactory": "Inscrivez-vous pour recevoir les messages texte de Banana Republic et profitez d'une offre de bienvenue de 20 % de réduction, nouvel arrivage et bien plus.", "communicationPreferences.checkBoxTextForGapFactory": "Inscrivez-vous pour recevoir les messages texte de Gap Factory et profitez d'une offre de bienvenue de 20 % de réduction, nouvel arrivage et bien plus.", "communicationPreferences.phoneNumber": "Numéro de téléphone", "communicationPreferences.textDisclaimer": "* Des frais de messagerie et de données peuvent s’appliquer. Le fait d’envoyer votre numéro de téléphone, de cliquer sur « Soumettre » et de compléter le processus d’inscription signifie que vous consentez à recevoir des textos récurrents chaque semaine au numéro de téléphone fourni. De plus, vous consentez aux modalités du programme de textos ainsi qu’à la politique de confidentialité de l’entreprise. Le consentement n’est pas une condition d’achat de biens ou de services. Vous pouvez vous désinscrire en tout temps en répondant STOP. Vous pouvez aussi répondre HELP pour obtenir de l’aide.", "communicationPreferences.signUp": "INSCRIPTION", "communicationPreferences.unsubscribeButtonText": "<PERSON> d<PERSON>ab<PERSON>ner", "communicationPreferences.unsubscribeText": "Entrez votre adresse courriel pour vous désabonner de tous les courriels de marketing. Vous continuerez de recevoir les courriels liés à vos transactions et à votre compte.", "communicationPreferences.unsubscribeOneText": "Se désabonner de tous les courriels de marketing de {{brand}}.", "communicationPreferences.unsubscribeAllText": "Se désabonner de tous les courriels de marketing de Gap, Old Navy, Banana Republic, Athleta, Gap Entrepôt et Banana Republic Entrepôt.", "communicationPreferences.textTerms.consentToRecieve": "En cliquant ici, vous consentez à recevoir des messages texte personnalisés automatisés (par exemple : rappel de votre panier d’achats) et de marketing de la part de ", "communicationPreferences.textTerms.atCellNum": " au numéro de téléphone cellulaire fourni.", "communicationPreferences.textTerms.termsAndConditions": "Les conditions générales", "communicationPreferences.textTerms.learnMore": " s’appliquent. Apprenez-en plus sur nos pratiques de protection de la vie privée dans notre ", "communicationPreferences.textTerms.privacyPolicy": "politique de confidentialité", "communicationPreferences.textTerms.disclaimer": "Le consentement n’est pas une condition d’achat. Répondez AIDE pour obtenir de l’aide et ARRÊT pour vous désinscrire. La fréquence des messages peut varier. Frais de messagerie et de données applicables.", "communicationPreferences.category.categoryHeader": "Quelles sont les catégories qui vous intéressent? (facultatif)", "communicationPreferences.category.categoryWomens": "<PERSON>mme", "communicationPreferences.category.categoryMens": "<PERSON><PERSON>", "communicationPreferences.category.categoryMaternity": "<PERSON><PERSON><PERSON>", "communicationPreferences.category.categoryKids": "<PERSON><PERSON>", "communicationPreferences.category.categoryBaby": "<PERSON><PERSON><PERSON><PERSON>", "communicationPreferences.category.at": "Athleta", "communicationPreferences.category.atGirl": "Athleta Girl", "communicationPreferences.category.categoryGirls": "<PERSON><PERSON>", "communicationPreferences.category.categoryBoys": "Garçon", "communicationPreferences.category.categoryToddlerGirls": "Toute-petite fille 12 M à 5 A", "communicationPreferences.category.categoryToddlerBoys": "Tout-petit garçon 12 M à 5 A", "communicationPreferences.category.categoryBabyWithAges": "Bébé 0 à 24 M", "communicationPreferences.category.selectAll": "<PERSON><PERSON>", "communicationPreferences.category.categoryHome": "Home", "communicationPreferences.birthday.header": "Inscrivez-vous pour obtenir votre cadeau d’anniversaire", "communicationPreferences.birthday.text": "<PERSON><PERSON>, envoyez-moi mon cadeau d’anniversaire", "communicationPreferences.birthday.selectMonth": "Sé<PERSON><PERSON>ner le mois", "communicationPreferences.birthday.selectDay": "Sélectionner le jour", "communicationPreferences.maternity.header": "Si vous le souhaitez, indiquez-nous la date d’accouchement!", "communicationPreferences.maternity.selectMonth": "<PERSON><PERSON>", "communicationPreferences.maternity.selectYear": "<PERSON><PERSON>", "communicationPreferences.rewards.rewardsPrefix": "En prime! Membres du programme de récompenses, utilisez le courriel lié à votre compte pour obtenir", "communicationPreferences.rewards.rewardsEmphasised": " 250 POINTS EN PRIME ", "communicationPreferences.rewards.rewardsSuffix": "lorsque vous vous abonnez.*", "communicationPreferences.rewards.emailOptInPopover": "*Les membres du programme de récompenses qui s’inscrivent aux courriels marketing obtiendront 250 points en prime. Les points s’afficheront au compte dans un délai de 14 jours. Offre réservée aux nouveaux abonnés aux courriels de Gap Inc. au nom de leurs sociétés affiliées Old Navy (Canada) Inc. et Gap (Canada) Inc., et de leurs marques : Gap, Old Navy, Athleta et Banana Republic.", "communicationPreferences.rewards.notMember": "Vous n’êtes pas membre du programme Récompenses?", "communicationPreferences.rewards.joinNowUnderlined": "Inscrivez-vous gratuitement aujourd’hui.", "communicationPreferences.rewards.joinNowNotUnderlined": "", "communicationPreferences.rewards.freeSignup": "Inscrivez-vous gratuitement aujourd'hui.", "communicationPreferences.postalCode.heading": "Code postal", "communicationPreferences.postalCode.label": "Code postal", "communicationPreferences.postalCode.details": "Veuillez indiquer votre code postal pour obtenir des renseignements sur les événements organisés dans votre région.", "communicationPreferences.athletaModal.mmac": "Oui! Envoyez-moi un catalogue Athleta", "communicationPreferences.athletaModal.firstName": "Prénom", "communicationPreferences.athletaModal.lastName": "Nom de famille", "communicationPreferences.athletaModal.addressLine1": "Ligne d’adresse 1", "communicationPreferences.athletaModal.addressLine2": "Ligne d’adresse 2", "communicationPreferences.athletaModal.city": "Ville", "communicationPreferences.athletaModal.US.state": "État", "communicationPreferences.athletaModal.US.zipcode": "Code postal (É.-U).", "communicationPreferences.athletaModal.CA.state": "Province", "communicationPreferences.athletaModal.CA.zipcode": "Code postal", "communicationPreferences.ATCalalog.successNotification": "Vous avez réussi! Vos préférences de communication ont été mises à jour.", "communicationPreferences.ATCalalog.failNotification": "Nous n’avons pas pu mettre à jour vos préférences de communication. Veuillez réessayer.", "communicationPreferences.subscribe.successNotification": "Vous avez réussi! Vos préférences de communication ont été mises à jour.", "communicationPreferences.subscribe.failNotification": "Nous n’avons pas pu mettre à jour vos préférences de communication. Veuillez réessayer.", "communicationPreferences.subscribe.optInDisclosure1": "Oui! J’aimerais recevoir des nouvelles sur la mode et des promotions de Gap Inc., Gap (Canada) Inc., Old Navy (Canada) Inc. et Banana Republic. Lisez notre ", "communicationPreferences.subscribe.optInDisclosure2": " et ", "communicationPreferences.subscribe.optInDisclosure3": " si vous avez des questions.", "communicationPreferences.subscribe.privacyPolicy": "politique de confidentialité", "communicationPreferences.subscribe.contactUs": "communiquez avec nous", "communicationPreferences.textMessage.successNotification": "Vous avez réussi! Vos préférences de communication ont été mises à jour.", "communicationPreferences.textMessage.failNotification": "Nous n’avons pas pu mettre à jour vos préférences de communication. Veuillez réessayer.", "communicationPreferences.unsubscribe.successNotification": "Vous avez réussi! Vos préférences de communication ont été mises à jour.", "communicationPreferences.unsubscribe.failNotification": "Nous n’avons pas pu mettre à jour vos préférences de communication. Veuillez réessayer.", "communicationPreferences.language.heading": "<PERSON><PERSON>", "communicationPreferences.language.options.english": "<PERSON><PERSON><PERSON>", "communicationPreferences.language.options.french": "Français", "resetYourPasswordModal.resetYourPasswordTitle": "Réinitialiser votre mot de passe", "resetYourPasswordModal.createPassword": "CRÉER UN NOUVEAU MOT DE PASSE", "resetYourPasswordModal.confirmPassword": "Confirmer le mot de passe", "resetYourPasswordModal.savePassword": "SAUVEGARDER LE MOT DE PASSE", "resetYourPasswordModal.resetPasswordFor": "Réinitialiser le mot de passe pour", "resetYourPasswordModal.passwordSentTitle": "Votre mot de passe a été mis à jour", "resetYourPasswordModal.errorLabels.passwordCannotBeBlank": "Le mot de passe ne peut être vide.", "resetYourPasswordModal.errorLabels.passwordsDoNotMatch": "Les mots de passe ne correspondent pas.", "resetYourPasswordModal.errorLabels.passwordCriteriaNotMet": "Les critères pour le mot de passe ne sont pas respectés.", "resetYourPasswordModal.validationRules.eightToTwentyFourChars": "De 8 à 24 caractères", "resetYourPasswordModal.validationRules.aLowercaseLetter": "Une lettre minuscule", "resetYourPasswordModal.validationRules.anUppercaseLetter": "Une lettre majuscule", "resetYourPasswordModal.validationRules.aNumber": "Un chiffre", "resetYourPasswordModal.validationRules.aSpecialChar": "Un caractère spécial", "resetYourPasswordModal.forcedResetPassword.exampleSpecialChar": "- ! @ # $ % ^ & * ( ) _ +", "resetYourPasswordModal.forcedResetPassword.forcedResetLabel1": "<PERSON><PERSON><PERSON> de ma<PERSON>iner chez nous, ", "resetYourPasswordModal.forcedResetPassword.forcedResetLabel2": ". V<PERSON><PERSON><PERSON> prendre un instant pour mettre à jour et renforcer votre mot de passe.", "resetYourPasswordModal.forcedResetPassword.forcedResetTitle": "Protéger votre compte", "resetYourPasswordModal.forcedResetPassword.title": "Protéger votre compte", "resetYourPasswordModal.forcedResetPassword.btnText": "Envoyer un Courriel", "resetYourPasswordModal.forcedResetPassword.backToSignIn": "Retourner à la Connexion", "resetYourPasswordModal.forcedResetPassword.description-1": "Pour veiller à protéger votre compte, veuillez mettre à jour et renforcer votre mot de passe. Nous enverrons un lien de réinitialisation du mot de passe à {{email}}", "resetYourPasswordModal.forcedResetPassword.description-2": "Pour aider à protéger votre compte, veuil<PERSON>z mettre à jour et renforcer votre mot de passe. Nous vous enverrons par e-mail un lien de réinitialisation du mot de passe.", "resetYourPasswordModal.forcedResetPassword.errorMessage": "Nous sommes désolés, mais cette page est temporairement indisponible. Veuillez réessayer plus tard ou appeler le service à la clientèle au {{brandNumber}} si le problème persiste.", "resetYourPasswordModal.forcedResetPassword.error": "Une erreur technique s’est malheureusement produite. Renvoyer le courriel pour réessayer.", "resetYourPasswordModal.forcedResetPassword.resendEmailBtnText": "<PERSON><PERSON><PERSON> le courriel", "resetYourPasswordModal.forcedResetPassword.successTitle": "Consultez V<PERSON>re <PERSON>", "resetYourPasswordModal.forcedResetPassword.successDescription": "Nous avons renvoyé les instructions de réinitialisation du mot de passe à {{email}}", "resetYourPasswordModal.forcedResetPassword.successDescription2": "Nous avons envoyé par e-mail des instructions sur la façon de réinitialiser votre mot de passe.", "resetYourPasswordModal.ariaLabeling.forcedResetHeaderAriaLabel1": "<PERSON><PERSON><PERSON> de ma<PERSON>iner chez nous, ", "resetYourPasswordModal.ariaLabeling.forcedResetHeaderAriaLabel2": ". Pour votre protection, vous êtes dans le menu de réinitialisation du mot de passe. Votre mot de passe n’est plus valide et il doit être mis à jour afin de pouvoir continuer à passer à la caisse en tant que client existant. ", "resetYourPasswordModal.ariaLabeling.passwordCriteriaAriaLabel": "Mettez à jour votre mot de passe pour protéger votre compte. Les critères du mot de passe sont les suivants: votre mot de passe doit contenir entre huit et vingt-quatre caractères, dont une lettre minuscule, une lettre majuscule, un chiffre et un caractère spécial, incluant au moins un des suivants: un tiret, un arobas, une touche Carré, un signe de dollar, un signe de pourcentage, une flèche pointant vers le haut, une perluète, un astérisque, un crochet ouvrant, un crochet fermant, un trait de soulignement, un signe plus. Une fois que vous aurez saisi votre mot de passe, vous devrez le confirmer en le saisissant à nouveau dans le champ de texte suivant, et les deux mots de passe doivent correspondre pour que le nouveau mot de passe soit sauvegardé avec succès. Veuillez saisir votre nouveau mot de passe maintenant. ", "resetYourPasswordModal.ariaLabeling.confirmPasswordAriaLabel": "Veuillez confirmer votre nouveau mot de passe. Veuillez saisir le même mot de passe à nouveau pour qu’il puisse être sauvegardé avec succès dans votre compte.", "resetYourPasswordModal.ariaLabeling.ariaPasswordCriteriaErrorLabel1": "Le mot de passe que vous avez saisi ne respecte pas les critères. Votre mot de passe doit ", "resetYourPasswordModal.ariaLabeling.ariaPasswordMismatchErrorLabel": "Le mot de passe que vous avez saisi ne respecte pas les critères. Vos mots de passe ne correspondent pas", "resetYourPasswordModal.ariaLabeling.ariaPasswordErrorUpdateInputLabel": ". <PERSON><PERSON><PERSON><PERSON> appuyer sur la touche Majuscule, puis sur la touche de tabulation pour mettre à jour ce que vous avez saisi.", "resetYourPasswordModal.ariaLabeling.ariaValidationRules.hasEightToTwentyFourChars": "contenir de huit à vingt-quatre caractères, ", "resetYourPasswordModal.ariaLabeling.ariaValidationRules.hasLowercaseChar": "inclure une lettre minuscule, ", "resetYourPasswordModal.ariaLabeling.ariaValidationRules.hasUppercaseChar": "inclure une lettre majuscule, ", "resetYourPasswordModal.ariaLabeling.ariaValidationRules.hasANumber": "inclure un chiffre, ", "resetYourPasswordModal.ariaLabeling.ariaValidationRules.hasSpecialChar": "inclure un caractère spécial, incluant au moins un des suivants: un tiret, un point d’exclamation, un arobas, une touche Carré, un signe de dollar, un signe de pourcentage, une flèche pointant vers le haut, une perluète, un astérisque, un crochet ouvrant, un crochet fermant, un trait de soulignement, un signe plus ", "passwordSentModal.passwordUpdatedTitle": "Votre mot de passe a été mis à jour.", "passwordSentModal.continueShopping": "CONTINUER À MAGASINER", "passwordSentModal.passwordResetAriaLabel": "Votre mot de passe a été mis à jour. Appuyez sur Entrée pour continuer de magasiner.", "passwordSentModal.forcedResetPassword.passwordUpdatedLabel": "Pour votre sécurité, tous les renseignements de facturation — incluant les détails de la carte de crédit — ont été supprimés de votre compte.", "passwordSentModal.forcedResetPassword.btnLabel": "Revenir à la caisse", "passwordSentModal.forcedResetPassword.ariaLabel": "Votre mot de passe a été mis à jour. Pour votre sécurité, tous les renseignements de facturation — incluant les détails de la carte de crédit — ont été supprimés de votre compte. Appuyez sur Retour pour revenir à la caisse.", "finalForm.example": "p. ex. ", "finalForm.optional": "Optionnel", "usedLinkModal.header": "<PERSON><PERSON>", "usedLinkModal.usedLinkTitle": "Vous avez déjà utilisé ce lien", "usedLinkModal.usedLinkLabel": "Ouvrez une session avec votre nouveau mot de passe ou ", "usedLinkModal.signinText": "Ouvrir une session", "usedLinkModal.forgotYourPassword": "demandez un autre lien", "usedLinkModal.errorEmailFailed": "Erreur: échec d’envoi du courriel. Veuillez réessayer plus tard.", "usedLinkModal.resentEmailSuccess": "Nous avons renvoyé les instructions de réinitialisation du mot de passe.  Une fois que vous aurez modifié votre mot de passe, vous pourrez ouvrir une session.", "forgotPasswordModal.sendEmailTitle": "Vous avez oublié votre mot de passe?", "forgotPasswordModal.enterEmailAddress": "Saisissez votre adresse électronique ci-dessous. Nous vous enverrons un lien de réinitialisation du mot de passe.", "forgotPasswordModal.emailAddressLabel": "ADRESSE COURRIEL", "forgotPasswordModal.sendEmail": "ENVOYER UN COURRIEL", "forgotPasswordModal.backToSignIn": "PRÉCÉDANT", "forgotPasswordModal.invalidEmail": "<PERSON><PERSON><PERSON>z saisir une adresse courriel valide.", "forgotPasswordModal.blankEmail": "Le champ adresse courriel ne peut pas être vide.", "forgotPasswordModal.checkInbox": "Consultez votre bo<PERSON><PERSON> de ré<PERSON>.", "forgotPasswordModal.emailSentText": "Si {{email}} correspond à un compte, nous vous enverrons par courriel les directives pour réinitialiser votre mot de passe.", "forgotPasswordModal.OkLabel": "OK", "forgotPasswordModal.emailSendFailed": "Erreur: Échec de l'envoi de l'adresse e-mail. Veuillez réessayer plus tard.", "expiredLinkPage.expiredLinkTitle": "Ce lien est expiré.", "expiredLinkPage.returnToForgotPasswordPart1": "Veuillez revenir à ", "expiredLinkPage.returnToForgotPasswordPart2": " pour obtenir un nouveau lien de réinitialisation du mot de passe.", "expiredLinkPage.forgotYourPassword": "Mot de passe oublié", "emailSentModal.emailSentTitle": "<PERSON><PERSON><PERSON> envoy<PERSON>", "emailSentModal.emailSentLabel": "Nous avons envoyé les instructions de réinitialisation du mot de passe à ", "emailSentModal.changePasswordLabel": ". Une fois que vous aurez modifié votre mot de passe, vous pourrez ouvrir une session.", "emailSentModal.backToSignIn": "RETOUR À L’OUVERTURE DE SESSION", "emailSentModal.ok": "OK", "accountLogin.barclays.creditCardApplication": "credit card application", "accountLogin.barclays.getStarted": "Let’s Get Started", "accountLogin.barclays.signInOrCreateAccount": "Sign in or create your free account to begin your credit card application.", "accountLogin.barclays.emailRewardsProfile": "Use the email tied to your Rewards profile to save time and make using your credit card easier.", "accountLogin.barclays.applyWithoutLinking": "Go directly to the credit card application without linking your rewards profile.", "accountLogin.barclays.continueWithoutAccount": "Continue Without An Account", "accountLogin.barclays.signInToAccount": "Sign In to Your Account", "accountLogin.errors.accountAlreadyExist": "Cette adresse courriel est déjà inscrite. Si vous avez déjà un compte pour une de nos marques sœurs, vous pouvez ouvrir une session en utilisant la même adresse courriel.", "accountLogin.errors.sessionTimeout": "Votre session a expiré. Pour votre sécurité, veuillez ouvrir une nouvelle session.", "accountLogin.errors.passwordsDoNotMatch": "Les mots de passe ne correspondent pas.", "accountLogin.errors.noEmailSocialSignin": "Une adresse courriel est nécessaire pour continuer avec Facebook.", "accountLogin.errors.pingUniqueIdErrorNotification": "Une erreur s’est produite. Veuillez réessayer plus tard.", "accountLogin.errors.verifyEmailError": "Nous n’avons pas pu vérifier votre courriel. Veuillez réessayer.", "accountLogin.errors.pingFlowIdExpired": "Votre session a expiré. Essayez de nouveau.", "accountLogin.errors.accountNotExists": "Nous n’avons pas reconnu le nom d’utilisateur. Essayez de nouveau ou créez un compte.", "accountLogin.errors.accountExists": "Cette adresse courriel est déjà liée à un compte. Si vous avez un compte pour n’importe quelle marque de Gap Inc., vous pouvez ouvrir une session avec cette adresse courriel à partir de n’importe quel site d’une de nos marques.", "accountLogin.errors.resetPasswordFail": "<PERSON><PERSON> failed to send. Please try again later.", "accountLogin.form.emailAddressLabel": "ADRESSE COURRIEL", "accountLogin.form.emailLabel": "<PERSON><PERSON><PERSON>", "accountLogin.form.passwordLabel": "CRÉER UN MOT DE PASSE", "accountLogin.form.password": "Mot de passe", "accountLogin.form.passwordCriteria": "Un mot de passe doit contenir de 8 à 24 caractères et comprendre une majuscule, une minuscule, un chiffre et un caractère spécial.", "accountLogin.loyalty.backButton": "Back button", "accountLogin.loyalty.buttonContinue": "<PERSON><PERSON><PERSON>", "accountLogin.loyalty.tryAgain": "Essayez de nouveau", "accountLogin.loyalty.headerText": "Entrez votre adresse courriel", "accountLogin.loyalty.enterEmail": "Entrez Votre Adresse <PERSON>urriel", "accountLogin.loyalty.headerTextSignInSegmentation": "Ouvrir une session | S’inscrire", "accountLogin.loyalty.paragraphText": "Entrez votre courriel pour commencer.", "accountLogin.loyalty.emailAddressLabel": "<PERSON><PERSON><PERSON>", "accountLogin.loyalty.signIn": "Ouvrir une session", "accountLogin.loyalty.signInBarclaysButton": "Ouvrir une session et demander une carte", "accountLogin.loyalty.messaging": "<PERSON><PERSON><PERSON>z remplir votre demande de carte dans un délai de 20 minutes après avoir ouvert une session pour associer l’approbation de votre demande de carte à votre compte de récompenses.", "accountLogin.loyalty.barclays.messaging": "<PERSON><PERSON><PERSON>z remplir votre demande de carte dans un délai de 20 minutes après avoir ouvert une session pour associer l’approbation de votre demande de carte à votre compte de récompenses.", "accountLogin.navigationTabs.signupText": "CRÉER UN COMPTE", "accountLogin.navigationTabs.signinText": "SE CONNECTER", "accountLogin.navigationTabs.backToShoppingText": "RETOUR AU MAGASINAGE", "accountLogin.navigationTabs.hiddenBodyText": "Vous êtes dans le menu d’ouverture de session afin de créer un compte utilisateur pour les marques Gap, Banana Republic, Old Navy ou Athleta. Veuillez appuyer sur la touche de tabulation pour procéder à la création d’un compte. Pour revenir à l’ouverture de session, veuillez appuyer sur les touches Majuscule et tabulation. Si vous êtes titulaire d’une carte de crédit d’une de nos marques, veuillez appuyer sur « ...... » pour passer à l’ouverture de session pour les titulaires de carte", "accountLogin.createAccount.nextButtonLabel": "Suivant", "accountLogin.createAccount.createButtonLabel": "<PERSON><PERSON><PERSON> un compte", "accountLogin.createAccount.barclaysHeader": "Create Account", "accountLogin.createAccount.barclaysSubHeader": "Setting up an account will help you get the most out of your credit card.", "accountLogin.createAccount.createButtonLabelBarclays": "<PERSON><PERSON>er un compte et demander une carte", "accountLogin.createAccount.createButtonLabelLoyality": "CRÉER UN COMPTE ET PASSER LA COMMANDE", "accountLogin.createAccount.trackYourOrders": "<PERSON><PERSON>z vos commandes", "accountLogin.createAccount.enjoyPersonalizedExp": "Profitez d’une expérience personnalisée", "accountLogin.createAccount.earnRewards": "Gagnez des cadeaux pour magasiner", "accountLogin.createAccount.requiredField": "Ce champ est obligatoire.", "accountLogin.createAccount.invalidEmail": "Entrez une adresse courriel valide.", "accountLogin.createAccount.blankEmail": "<PERSON>’adresse courriel ne peut pas être vide.", "accountLogin.createAccount.invalidPassword": "Votre mot de passe doit respecter les critères", "accountLogin.createAccount.blankPassword": "Le mot de passe ne peut être vide", "accountLogin.createAccount.blankFirstName": "Saisir votre prénom", "accountLogin.createAccount.blankLastName": "Saisir votre nom de famille", "accountLogin.createAccount.emailAddressLabel": "<PERSON><PERSON><PERSON>", "accountLogin.createAccount.blankFullName": "Saisir un prénom et un nom", "accountLogin.createAccount.blankMobile": "Saisir votre numéro de cellulaire.", "accountLogin.createAccount.passwordLabel": "<PERSON><PERSON><PERSON> un mot de passe", "accountLogin.createAccount.passwordConfirmLabel": "Confirmer le mot de passe", "accountLogin.createAccount.firstName": "Prénom", "accountLogin.createAccount.lastName": "Nom de famille", "accountLogin.createAccount.firstAndLastName": "Prénom et nom", "accountLogin.createAccount.mobileNumber": "Numéro de cellulaire", "accountLogin.createAccount.mobileNumberOptionalText": "Optionnel", "accountLogin.createAccount.mobileNumberOptional": "Numéro de cellulaire (Optionnel)", "accountLogin.createAccount.dateOfBirthOptional": "Birthday MM/DD (Optionnel)", "accountLogin.createAccount.dateOfBirth.invalid": "Enter a valid date of birth (MM/DD).", "accountLogin.createAccount.dateOfBirth.invalidMonth": "Enter a valid birth month (MM).", "accountLogin.createAccount.dateOfBirth.invalidDay": "Enter a valid birth day (DD).", "accountLogin.createAccount.phoneNote": "Ajoutez un numéro de téléphone pour faciliter la recherche des récompenses en magasin", "accountLogin.createAccount.welcomeLoyalty": "Bienvenue!", "accountLogin.createAccount.WelcomeNoteExpandedWithLoyalty": "Votre programme de récompenses a été amélioré. Créez un mot de passe pour profiter de la livraison gratuite sur toutes les commandes d’au moins 50 $, obtenir des points sur chaque achat et bien d’autres avantages!", "accountLogin.createAccount.welcomeWithoutLoyalty": "Devenez un membre du programme de récompenses", "accountLogin.createAccount.welcomeTextBarclays": "<PERSON><PERSON><PERSON> un compte", "accountLogin.createAccount.welcomeNoteExpanded": "Vous êtes à un pas de la livraison gratuite sur toutes les commandes de 50 $ et plus, de points sur chaque achat et bien d’autres avantages!", "accountLogin.createAccount.welcomeNoteExpandedBarclays": "Créez un compte pour lier une nouvelle carte de crédit de récompenses de Gap Inc. aux autres avantages du programme de récompenses, comme la livraison gratuite sur toutes les commandes de 50 $ et plus, des points sur chaque achat et bien d’autres avantages!", "accountLogin.createAccount.accountCreated": "Votre compte a été créé.", "accountLogin.createAccount.errorNotification": "Nous sommes désolés, mais cette page est temporairement inaccessible. Veuillez réessayer plus tard ou appeler le service à la clientèle au {{brandNumber}} si le problème persiste.", "accountLogin.createAccount.emailAlreadyRegistered": "Utiliser une autre adresse courriel", "accountLogin.createAccount.show": "MONTRER", "accountLogin.createAccount.hide": "CACHER", "accountLogin.createAccount.globalMessageError": "Veuillez réessayer. Nous subissons une interruption de service temporaire.", "accountLogin.createAccount.globalMessagePingError": "Une erreur s’est produite. Veuillez réessayer plus tard.", "accountLogin.createAccount.globalMessagePingErrorV2": "Une erreur est survenue. Veuillez réessayer.", "accountLogin.createAccount.legalDisclosure": "En créant un compte, vous rejoignez le programme de récompenses Gap Inc. et acceptez les conditions générales et la politique de confidentialité du programme de récompenses et consentez à recevoir des courriels marketing.", "accountLogin.createAccount.rewardAccount": "Votre compte de récompenses a été créé. Ouvrir une session pour continuer.", "accountLogin.createAccount.isAccountExists": "Un compte avec cette adresse courriel existe déjà.", "accountLogin.createAccount.isCreateAccountError": "Nous n’avons pas pu créer votre compte. Veuillez réessayer.", "accountLogin.createAccount.isPingCreateAccountError": "Nous avons pu créer votre compte. <PERSON><PERSON><PERSON>, une erreur s’est produite et nous n’avons pas pu ouvrir une session automatiquement. Veuillez utiliser les mêmes adresses de courriel et mot de passe pour ouvrir une session.", "accountLogin.createAccount.signinCheckout": "Ouvrir une session et passer la commande", "accountLogin.createAccount.transactionalEmailDisclosure": "Courriels transactionnels. En adhérant au programme, vous acceptez de recevoir des courriels transactionnels liés à votre adhésion, y compris des courriels de bienvenue, des mises à jour et relevés récurrents sur l’état ou la mise à jour de votre adhésion et des rappels sur l’expiration des points accumulés. Les courriels transactionnels ne sont pas des courriels de marketing. Le seul moyen d’interrompre l’envoi des courriels transactionnels est d’annuler votre adhésion au programme. Les courriels de marketing sont facultatifs. Vous ne les recevrez que si vous y consentez au préalable.", "accountLogin.createAccount.passwordPreExistError": "Password not associated with this email address. You have a limited number of attempts before your account is temporarily locked.", "accountLogin.canadaLogin.welcome": "Bienvenue!", "accountLogin.canadaLogin.welcomeBack": "Nous sommes heureux de vous retrouver!", "accountLogin.canadaLogin.welcomeBecomeRewardsMember": "Devenir membre du programme de récompenses", "accountLogin.canadaLogin.welcomeNote": "Ouvrir une session pour passer une commande plus rapidement, voir l’historique des commandes, les détails du compte et bien d’autres choses!", "accountLogin.canadaLogin.welcomeNoteCreateAccount": "Vous êtes à un pas de la création de votre compte, d’accéder facilement à votre historique des commandes et bien d’autres choses.", "accountLogin.canadaLogin.welcomeNoteExpanded": "Ce qui vous attend? La possibilité d’obtenir la livraison gratuite, de gagner des points sur vos achats et bien d’autres avantages!", "accountLogin.canadaLogin.welcomeNotePhaseTwo": "Inscrivez-vous pour avoir la possibilité d’obtenir la livraison gratuite, de gagner des points sur vos achats et bien d’autres avantages!", "accountLogin.canadaLogin.welcomeNotePhaseTwoNoLoyalty": "Votre compte bénéficie maintenant de récompenses! Inscrivez-vous pour avoir la possibilité d’obtenir la livraison gratuite, de gagner des points sur vos achats et bien d’autres avantages!", "accountLogin.canadaLogin.passwordCriteria": "Utilisez votre mot de passe de compte Gap, Old Navy, Banana Republic ou Athleta. Il doit contenir de 8 à 24 caractères et comprendre une majuscule, une minuscule, un chiffre et un caractère spécial.", "accountLogin.canadaLogin.marketingEmailOptIn": "Oui! J’accepte de recevoir des courriels de marketing (nouvelles sur la mode et offres exclusives) de Gap Inc. et de ses sociétés affiliées : Old Navy (Canada) Inc. et Gap (Canada) Inc. et de leurs marques : Gap, Old Navy, Athleta et Banana Republic. Je peux résilier cet abonnement en tout temps.", "accountLogin.canadaLogin.marketingEmailOptInHeader": "Cochez la case ci-dessous pour vous abonner aux courriels de marketing et obtenir", "accountLogin.canadaLogin.marketingEmailOptInSubHeader": "250 POINTS EN PRIME", "accountLogin.canadaLogin.marketingEmailOptInPopover": "Les membres du programme de récompenses qui s’inscrivent aux courriels marketing obtiendront 250 points en prime. Les points s’afficheront au compte dans un délai de 14 jours. Offre réservée aux nouveaux abonnés aux courriels de Gap Inc. au nom de leurs sociétés affiliées Old Navy (Canada) Inc. et Gap (Canada) Inc., et de leurs marques : Gap, Old Navy, Athleta et Banana Republic.", "accountLogin.canadaLogin.loyaltyContactUs": "Communiquez avec nous.", "accountLogin.canadaLogin.marketingLoyaltyAgreement": "Oui! J’accepte de recevoir des courriels de marketing de Gap Inc. et de ses sociétés affiliées : Old Navy (Canada) Inc. et Gap (Canada) Inc. et de leurs marques : Gap, Old Navy, Athleta et Banana Republic. ", "accountLogin.canadaLogin.guestCheckoutTitle": "Ou", "accountLogin.canadaLogin.signInAndCheckout": "Ou<PERSON><PERSON>r une session et commander", "accountLogin.canadaLogin.createAccountAndCheckout": "<PERSON><PERSON><PERSON> un compte et commander", "accountLogin.canadaLogin.completeAccountAndCheckout": "<PERSON><PERSON><PERSON> un compte et commander", "accountLogin.canadaLogin.loyaltyQuestions": " Des questions?", "accountLogin.canadaLogin.loyaltyUnsubscribe": "Vous pouvez vous désabonner à tout moment.", "accountLogin.canadaLogin.welcomeNoteExpandedWithLoyalty": "Votre programme de récompenses a été amélioré. Créez un mot de passe pour avoir la possibilité d’obtenir la livraison gratuite, de gagner des points sur vos achats et bien d’autres avantages!", "accountLogin.canadaLogin.completeAccount": "<PERSON><PERSON><PERSON> la création du compte", "accountLogin.canadaPrivacyPolicy.privacyPolicy": "https://www.gapinc.com/fr-ca/consumer-privacy-policy", "accountLogin.canadaContactInfo.gap.url": "https://www.gapcanada.ca/customerService/info.do?cid=2136", "accountLogin.canadaContactInfo.gap.label": "gapcanada.ca Nous joindre", "accountLogin.canadaContactInfo.gp.url": "https://www.gapcanada.ca/customerService/info.do?cid=2136", "accountLogin.canadaContactInfo.gp.label": "gapcanada.ca Nous joindre", "accountLogin.canadaContactInfo.at.url": "https://athleta.gapcanada.ca/customerService/info.do?cid=44959", "accountLogin.canadaContactInfo.on.url": "https://oldnavy.gapcanada.ca/customerService/info.do?cid=3171", "accountLogin.canadaContactInfo.on.label": "oldnavy.ca Nous joindre", "accountLogin.canadaContactInfo.br.url": "https://bananarepublic.gapcanada.ca/customerService/info.do?cid=6740", "accountLogin.canadaContactInfo.brfs.url": "https://bananarepublicfactory.gapfactory.ca/customerService/info.do?cid=1037881", "accountLogin.canadaContactInfo.br.label": "bananarepublic.ca Nous joindre", "accountLogin.canadaContactInfo.gapfs.url": "https://www.gapfactory.ca/customerService/info.do?cid=1037175", "accountLogin.canadaContactInfo.gapfs.label": "gapfactory.ca Nous joindre", "accountLogin.canadaContactInfo.gpfs.url": "https://www.gapfactory.ca/customerService/info.do?cid=1037175", "accountLogin.canadaContactInfo.gpfs.label": "gapfactory.ca Nous joindre", "accountLogin.login.welcome": "Bienvenue!", "accountLogin.login.welcomeNote": "Ouvrez une session pour bénéficier de la livraison gratuite sur les commandes de plus de 50 $, de points sur chaque achat dans notre famille de marques et bien d’autres avantages!", "accountLogin.login.welcomeNoteBarclays": "Ouvrez une session pour lier votre compte existant à votre nouvelle carte de crédit de récompenses Gap Inc, une fois qu’elle est approuvée.", "accountLogin.login.signinText": "Ouvrir une session", "accountLogin.login.goToSignIn": "Aller à la Connexion", "accountLogin.login.tryAgain": "Something went wrong. Please try again.", "accountLogin.login.hiddenBodyText": "Vous êtes dans le menu d’ouverture de session afin de créer un compte utilisateur pour les marques Gap, Banana Republic, Old Navy ou Athleta. Veuillez appuyer sur la touche de tabulation pour procéder à la création d’un compte. Pour revenir à l’ouverture de session, veuillez appuyer sur les touches Majuscule et tabulation. Si vous êtes titulaire d’une carte de crédit d’une de nos marques, veuillez appuyer sur « ...... » pour passer à l’ouverture de session pour les titulaires de carte", "accountLogin.login.sendEmailTitle": "Vous avez oublié votre mot de passe?", "accountLogin.login.invalidPingSignInCredens": "Nous n’avons pas reconnu le nom d’utilisateur ou le mot de passe saisi. Veuillez réessayer.", "accountLogin.login.notAllowedPingSignIn": "Votre compte a été bloqué. Veuillez appeler le service à la clientèle.", "accountLogin.login.accountLocked": "Your account has been temporarily locked. Try again later or reset your password.", "accountLogin.login.emailTextHeader": "Nous vous enverrons un lien ou un code unique pour réinitialiser votre mot de passe.", "accountLogin.login.sendVerificationLinkToEmail": " Envoyer le lien par courriel", "accountLogin.login.sendVerificationCodeByText": " Envoyer le code par texto", "accountLogin.login.keepMeSignedIn": "Maintenir la connexion sur cet appareil.", "accountLogin.login.moreInformation": "More information", "accountLogin.login.popoverText": "Cochez cette case et vous n’aurez pas besoin de vous connecter souvent. Si vous changez d’avis, déconnectez-vous et décochez cette case avant de vous reconnecter. Nous ne recommandons pas l’utilisation de cette fonctionnalité sur un appareil public.", "accountLogin.login.signingIn": "Ouvrir une session", "accountLogin.login.forgotPassword": "Forgot your password?", "accountLogin.login.emailLink": "We’ll email you a link so you can reset it.", "accountLogin.login.sendEmailBtn": "SEND EMAIL", "accountLogin.login.emailWillBeSent": "The email will be sent to:", "accountLogin.login.emailSentSuccess": "<PERSON><PERSON>", "accountLogin.login.signInDataStoreError": "Nous rencontrons des problèmes techniques. Veuillez réessayer plus tard.", "accountLogin.guestLogin.guestCheckoutTitle": "Ou passer la commande en tant qu’invité. Les commandes passées en tant qu’invité ne sont pas admissibles à l’expédition gratuite*.", "accountLogin.guestLogin.guestCheckoutDisclaimer": "*Les commandes passées en tant qu’invité sont admissibles à l’expédition gratuite durant certaines promotions seulement.", "accountLogin.guestLogin.continueAsGuestButton": "Continuer en tant que client invité", "accountLogin.guestLogin.dividerText": "OU", "accountLogin.guestLogin.continueToCheckoutButton": "PASSER À LA CAISSE EN TANT QU'INVITÉ", "accountLogin.guestLogin.errorNotification": "Nous sommes désolés, cette page est temporairement indisponible. Veuillez réessayer plus tard ou communiquer avec notre service à la clientèle au {{brandNumber}} si le problème persiste.", "accountLogin.guestLogin.isGuestCheckoutError": "Nous ne pouvons pas passer votre commande en tant qu’invité. Veuillez réessayer.", "accountLogin.brandCard.label": "Vous souhaitez gérer votre carte Banana Republic?", "accountLogin.recaptchaDisclaimer.siteProtected": "Ce site est protégé par reCAPTCHA ainsi que la", "accountLogin.recaptchaDisclaimer.privacyPolicy": "Politique de confidentialité", "accountLogin.recaptchaDisclaimer.and": "et", "accountLogin.recaptchaDisclaimer.termsOfService": "les conditions d’utilisation de Google", "accountLogin.recaptchaDisclaimer.apply": "s'appliquent", "accountLogin.legalDisclosure.signIn": "En vous inscrivant, vous joignez le programme de récompenses Gap Inc. et consentez à respecter les", "accountLogin.legalDisclosure.signUp": "En créant un compte, vous joignez le programme de récompenses Gap Inc. et consentez aux", "accountLogin.legalDisclosure.termsAndCondition": "conditions générales", "accountLogin.legalDisclosure.and": "et", "accountLogin.legalDisclosure.privacyPolicy": "Politique de confidentialité", "accountLogin.legalDisclosure.reward": "du programme de récompenses et consentez à recevoir des courriels de marketing. Check our", "accountLogin.legalDisclosure.rewardsEmailText": "du programme de récompenses. De plus, vous acceptez de recevoir des courriels transactionnels liés à votre compte de membre, y compris des courriels de bienvenue, des relevés récurrents et des rappels sur l’expiration des points accumulés. Le seul moyen d’interrompre l’envoi des courriels transactionnels est d’annuler votre adhésion au programme. Les courriels de marketing sont facultatifs; vous ne les recevrez que si vous y consentez au préalable. Veuillez vous reporter à notre ", "accountLogin.legalDisclosure.about": "En savoir plus sur le programme de récompenses", "accountLogin.legalDisclosure.personalInfo": "pour connaître nos pratiques en matière de collecte et d’utilisation de vos données à caractère personnel.", "accountLogin.validationRules.eightToTwentyFourChars": "De 8 à 24 caractères", "accountLogin.validationRules.aLowercaseLetter": "Une lettre minuscule", "accountLogin.validationRules.anUppercaseLetter": "Une lettre majuscule", "accountLogin.validationRules.aNumber": "Un chiffre", "accountLogin.validationRules.aSpecialChar": "Un caractère spécial", "accountLogin.ariaLabels.showPasswordAriaLabel": "Afficher le mot de passe en texte brut. Remarque : votre mot de passe s’affichera à l’écran.", "accountLogin.loyaltyFooter.gp.linkText": "<PERSON><PERSON><PERSON>", "accountLogin.loyaltyFooter.gp.link": "https://gap.syf.com/login/", "accountLogin.loyaltyFooter.gp.text": "votre carte de crédit de récompenses Gap", "accountLogin.loyaltyFooter.on.linkText": "<PERSON><PERSON><PERSON>", "accountLogin.loyaltyFooter.on.link": "https://oldnavy.syf.com/login/", "accountLogin.loyaltyFooter.on.text": "votre carte de crédit de récompenses Old Navy ", "accountLogin.loyaltyFooter.br.linkText": "<PERSON><PERSON><PERSON>", "accountLogin.loyaltyFooter.br.link": "https://bananarepublic.syf.com/login/", "accountLogin.loyaltyFooter.br.text": "votre carte de crédit de récompenses Banana Republic", "accountLogin.loyaltyFooter.at.linkText": "<PERSON><PERSON><PERSON>", "accountLogin.loyaltyFooter.at.link": "https://athleta.syf.com/login/", "accountLogin.loyaltyFooter.at.text": "votre carte de crédit de récompenses Athleta", "accountLogin.loyaltyFooter.gpfs.linkText": "<PERSON><PERSON><PERSON>", "accountLogin.loyaltyFooter.gpfs.link": "https://gap.syf.com/login/", "accountLogin.loyaltyFooter.gpfs.text": "votre carte de crédit de récompenses Gap", "accountLogin.loyaltyFooter.brfs.linkText": "<PERSON><PERSON><PERSON>", "accountLogin.loyaltyFooter.brfs.link": "https://bananarepublic.syf.com/login/", "accountLogin.loyaltyFooter.brfs.text": "votre carte de crédit de récompenses Banana Republic", "accountLogin.BarclaysFooter.gp.linkText": "<PERSON><PERSON><PERSON>", "accountLogin.BarclaysFooter.gp.link": "https://gap.syf.com/login/", "accountLogin.BarclaysFooter.gp.text": "votre carte de crédit de récompenses Gap", "accountLogin.BarclaysFooter.on.linkText": "<PERSON><PERSON><PERSON>", "accountLogin.BarclaysFooter.on.link": "https://oldnavy.syf.com/login/", "accountLogin.BarclaysFooter.on.text": "votre carte de crédit de récompenses Old Navy ", "accountLogin.BarclaysFooter.br.linkText": "<PERSON><PERSON><PERSON>", "accountLogin.BarclaysFooter.br.link": "https://bananarepublic.syf.com/login/", "accountLogin.BarclaysFooter.br.text": "votre carte de crédit de récompenses Banana Republic", "accountLogin.BarclaysFooter.at.linkText": "<PERSON><PERSON><PERSON>", "accountLogin.BarclaysFooter.at.link": "https://athleta.syf.com/login/", "accountLogin.BarclaysFooter.at.text": "votre carte de crédit de récompenses Athleta", "accountLogin.BarclaysFooter.gpfs.linkText": "<PERSON><PERSON><PERSON>", "accountLogin.BarclaysFooter.gpfs.link": "https://gap.syf.com/login/", "accountLogin.BarclaysFooter.gpfs.text": "votre carte de crédit de récompenses Gap", "accountLogin.BarclaysFooter.brfs.linkText": "<PERSON><PERSON><PERSON>", "accountLogin.BarclaysFooter.brfs.link": "https://bananarepublic.syf.com/login/", "accountLogin.BarclaysFooter.brfs.text": "votre carte de crédit de récompenses Banana Republic", "passwordValidation.blankPassword": "Le mot de passe ne peut être vide.", "passwordValidation.passwordsDoNotMatch": "Les mots de passe ne correspondent pas.", "passwordValidation.passwordCriteriaNotMet": "Les critères pour le mot de passe ne sont pas respectés.", "singleOrderLookup.singleOrderLookupTitle": "Consultation d’une seule commande", "singleOrderLookup.singleOrderLookupText": "Veuillez saisir vos renseignements de commande pour obtenir plus de détails sur votre commande.", "singleOrderLookup.orderNumber": "<PERSON><PERSON><PERSON><PERSON> de commande", "singleOrderLookup.emailAddress": "Adresse électronique", "singleOrderLookup.findOrderNumber": "Où puis-je trouver mon numéro de commande?", "singleOrderLookup.gapPhoneNumber": "**************", "singleOrderLookup.findOrderNumberPopupText1": "Votre numéro de commande est inclus dans le courriel que vous avez reçu lorsque vous avez passé la commande. Si vous n’avez pas conservé ce courriel, veuil<PERSON><PERSON> appeler au ", "singleOrderLookup.findOrderNumberPopupText2": " pour obtenir de l’aide.", "singleOrderLookup.signInViewOrderHistory1": "<PERSON><PERSON>-vous un compte? ", "singleOrderLookup.signInViewOrderHistory2": " pour voir votre historique de commande", "singleOrderLookup.lookUpOrder": "CONSULTER UNE COMMANDE", "singleOrderLookup.lookUpOrderAuthenticatedFlow": "RECHERCHER LA COMMANDE", "singleOrderLookup.signIn": "Ouvrir une session", "singleOrderLookup.blankEmail": "Saisir votre adresse électronique", "singleOrderLookup.invalidEmail": "Saisir une adresse électronique valide", "singleOrderLookup.invalidOrderNumber": "Ce numéro de commande n’est pas valide. Les numéros de commande doivent contenir au moins sept caractères et inclure des lettres et des chiffres seulement", "singleOrderLookup.blankOrderNumber": "Saisir votre numéro de commande", "singleOrderLookup.accessibility.enterYourOrderNumber": "Vous êtes dans le menu de consultation d’une seule commande. Veuillez saisir vos détails de commande pour obtenir plus d’information sur votre commande. Saisir votre numéro de commande", "singleOrderLookup.accessibility.findOrderNumber": "Où puis-je trouver mon numéro de commande? Cliquer sur ce lien pour en savoir plus", "singleOrderLookup.accessibility.enterYourEmail": "Saisir votre adresse électronique", "singleOrderLookup.accessibility.signInToViewOrder": "Vous avez un compte? Ouvrez une session pour voir votre historique de commande.", "orDivider.orLabel": "ou", "mobileHeader.backButtonLabel": "PRÉCÉDANT", "accessibility.ModalCloseButton": "fermer la fenêtre contextuelle", "brands.on": "Old Navy", "brands.br": "Banana Republic", "brands.gp": "Gap", "brands.at": "Athleta", "brands.hc": "Hill City", "ordersAndReturns.singleOrderLookupLabel": "Consultez une seule commande en saisissant votre numéro de commande et l’adresse électronique utilisée pour passer la commande.", "ordersAndReturns.lookupSingleOrderButton": "Consulter une commande", "ordersAndReturns.ordersAndReturnsHeaderLabel": "Commandes et retours", "ordersAndReturns.trackOrdersStartReturnLabel": "Ouvrez une session pour faire le suivi de vos commandes, entamer un retour et consulter votre historique de commande.", "ordersAndReturns.ordersAndReturnsAriaLabel": "Vous êtes dans le menu Commandes et retours, qui propose deux options d’accès. Vous avez choisi l’accès avec ouverture de session. Vous devez ouvrir une session pour faire le suivi de vos commandes, entamer un retour ou consulter votre historique de commande pour les six derniers mois. OU si vous avez fait un achat en tant qu’invité ou que vous souhaitez consulter une commande en particulier, veuillez utiliser l’accès pour commande unique. Pour utiliser l’accès pour commande unique, continuez en utilisant la touche de tabulation. Pour ouvrir une session, veuillez appuyer sur la touche Retour.", "ordersAndReturns.errors.orderNotFound": "<PERSON><PERSON><PERSON><PERSON> entrer une adresse courriel valide et un numéro de commande. Les détails de la commande sont disponibles pendant 13 mois.", "ordersAndReturns.errors.globalMessage": "Veuillez réessayer. Nous subissons une interruption de service temporaire.", "shared.giveUsFeedbackLabel": "Rétroaction", "shared.signInLabel": "Ouvrir une session", "orderSummary.orderedOn": "Date de commande", "orderSummary.orderNumber": "<PERSON><PERSON><PERSON><PERSON> de commande", "orderSummary.totalCost": "Coût total", "orderSummary.orderedFrom": "Commande passée chez", "orderSummary.packageCount": "Nombre de colis", "orderSummary.returnItem": "Articles du retour ", "orderSummary.covidMessage": "Due to concerns about the coronavirus, purchases made between January 1, 2020 and April 30, 2020 have an extended return window through ", "orderSummary.covidMessageDate": "July 1, 2020", "orderSummary.finalSaleNotReturnable": "Les articles en vente ferme ne sont pas admissibles aux retours", "orderSummary.returnWindowClosed": "Retour possible jusqu’au", "orderSummary.orderSummaryHeader": "SOMMAIRE DE COMMANDE", "orderSummary.purchaseSummaryHeader": "RÉCAPITULATIF DES ACHATS", "orderSummary.purchased": "<PERSON><PERSON><PERSON>", "orderSummary.purchaseNumber": "Achat Nº", "orderSummary.returnWindowClosedOn": "Retour possible jusqu’au ", "orderSummary.fromThisOrderThrough": "de cette commande jusqu’au ", "orderSummary.totalCostDisplayTextSingular": "{{price}} ({{itemCount}} article)", "orderSummary.totalCostDisplayTextPlural": "{{price}} ({{itemCount}} articles)", "orderSummary.itemNotShipped": "", "orderSummary.returnAvailable": "Au moins un article de votre commande est admissible à un ", "orderSummary.returnUnAvailableWithBackorder": "Des articles de votre commande que nous avons reçus ne sont pas admissibles à un retour. ", "orderSummary.returnUnAvailable": "Des articles de votre commande ne sont plus admissibles à un retour. ", "orderSummary.returnItems": "retour. ", "orderSummary.see": "<PERSON>re la ", "orderSummary.returnPolicy": "politique de retour.", "orderSummary.optoroExchange.returnAvailable": "Au moins un article de votre commande est admissible à un ", "orderSummary.optoroExchange.returnUnAvailableWithBackorder": "Des articles de votre commande que nous avons reçus ne sont pas admissibles à un retour ni échangés. ", "orderSummary.optoroExchange.returnUnAvailable": "Des articles de votre commande ne sont plus admissibles à un retour ni échangés. ", "orderSummary.optoroExchange.returnItems": "retour ni échangés. ", "orderSummary.optoroExchange.returnPolicy": "politique de retours et d'échanges.", "orderSummary.optoroExchange.cannotReturn": "Les retours ne peuvent plus être initiés pour les articles de cette commande. Pour réimprimer l'étiquette, accédez au ", "orderSummary.optoroExchange.returnPortal": "portail des retours. ", "orderTimeout.orderTimeoutErrorMsgStart": "Le statut de la commande a changé et nous ne pouvons plus la modifier. Voir notre ", "orderTimeout.orderTimeoutErrorMsgEnd": " page pour plus d’information ou retourner aux détails de la commande.", "orderTimeout.itemInOrder": "Articles de cette commande :", "orderTimeout.shippingAddress": "Vos articles seront livrés à l’adresse suivante :", "orderTimeout.itemCountSingular": "{{itemCount}} Article", "orderTimeout.itemCountPlural": "{{itemCount}} Articles", "orderTimeout.backToDetails": "RETOUR AUX DÉTAILS DE LA COMMANDE", "orderDetails.newOrderNumber": "Nouvelle commande", "orderDetails.originalOrderNumber": "Commande d'origine", "orderDetails.header": "<PERSON><PERSON><PERSON> de la commande", "orderDetails.details": "Détails", "orderDetails.accessibility.pickupItems": "Récupérer vos articles.", "orderDetails.deliveryStatus.itemCountSingular": "{{itemCount}} ARTICLE", "orderDetails.deliveryStatus.itemCountPlural": "{{itemCount}} ARTICLES", "orderDetails.deliveryStatus.cancelledMessage.cancelledItem": "Article indisponible", "orderDetails.deliveryStatus.cancelledMessage.cancelledCard": "Impossible de vérifier le paiement", "orderDetails.deliveryStatus.cancelledMessage.cancelledCustomer": "Annulation demandée par le client", "orderDetails.deliveryStatus.cancelledMessage.cancelledUnavailable": "Nous n'avons pas pu trouver ces articles lorsque la commande est arrivée dans notre magasin. Nous nous excusons pour tout inconvénient. Veuillez patienter quelques jours pour que la suspension de votre carte de crédit soit libérée.", "orderDetails.deliveryStatus.cancelledMessage.cancelled": "La demande d’annulation a été reçue. Veuillez allouer de 3 à 5 jours ouvrables pour que la retenue du montant sur le mode de paiement initial soit annulée.", "orderDetails.deliveryStatus.delayedMessage.late4days": "Nous traitons votre commande et à l’heure actuelle, elle dev<PERSON>t arriver à temps.", "orderDetails.deliveryStatus.delayedMessage.fpddDelayed": "Nous ne pouvons expédier votre commande d’ici la date de livraison prévue. Nous déployons tous les efforts pour l’expédier le plus tôt possible. Pour plus de détails, veuillez appeler au {{hotlinePhoneNumber}}.", "orderDetails.deliveryStatus.deliveryStatus.backOrdered": "EN ATTENTE", "orderDetails.deliveryStatus.deliveryStatus.delayed": "EN RETARD", "orderDetails.deliveryStatus.deliveryStatus.delivered": "LIVRÉE", "orderDetails.deliveryStatus.deliveryStatus.orderPlaced": "COMMANDE PASSÉE", "orderDetails.deliveryStatus.deliveryStatus.orderReceived": "COMMANDE REÇUE", "orderDetails.deliveryStatus.deliveryStatus.outForDelivery": "SORTIE POUR LIVRAISON", "orderDetails.deliveryStatus.deliveryStatus.preparingForShipment": "EN PRÉPARATION POUR L’EXPÉDITION", "orderDetails.deliveryStatus.deliveryStatus.inTransit": "EN TRANSIT", "orderDetails.deliveryStatus.deliveryStatus.justShipped": "EXPÉDIÉE", "orderDetails.deliveryStatus.deliveryStatus.undeliverable": "NON LIVRABLE", "orderDetails.deliveryStatus.deliveryStatus.exception": "EXCEPTION", "orderDetails.deliveryStatus.deliveryStatus.shipped": "EXPÉDIÉE", "orderDetails.deliveryStatus.deliveryStatus.returnRequested": "RETOUR DEMANDÉ", "orderDetails.deliveryStatus.deliveryStatus.returned": "RETOURNÉE", "orderDetails.deliveryStatus.deliveryStatus.cancelled": "ANNULÉE", "orderDetails.deliveryStatus.deliveryStatus.cancelled-customer": "ANNULÉE", "orderDetails.deliveryStatus.deliveryStatus.cancelled-item": "ANNULÉE", "orderDetails.deliveryStatus.deliveryStatus.cancelled-card": "ANNULÉE", "orderDetails.deliveryStatus.deliveryStatus.WORKING_ON_YOUR_ORDER": "PREPARING PICKUP", "orderDetails.deliveryStatus.deliveryStatus.CANCELED_ITEM_UNAVALIABLE": "CANCELED - UNAVAILABLE", "orderDetails.deliveryStatus.deliveryStatus.BOPIS_RETURNED": "RETOURNÉE", "orderDetails.deliveryStatus.deliveryStatus.ORDER_READY": "READY FOR PICKUP", "orderDetails.deliveryStatus.deliveryStatus.REFUNDED": "Remboursée", "orderDetails.deliveryStatus.deliveryStatus.statusUnavailable": "STATUT INDISPONIBLE", "orderDetails.deliveryStatus.deliveryStatus.PICKED_UP": "<PERSON><PERSON><PERSON>", "orderDetails.deliveryStatus.deliveryStatus.PICK_UP": "Rama<PERSON><PERSON>", "orderDetails.deliveryStatus.deliveryStatus.VERIFIED": "VERIFIED", "orderDetails.deliveryStatus.deliveryStatus.AWAITING_CANCELLATION": "EN ATTENTE D'ANNULATION", "orderDetails.deliveryStatus.deliveryStatus.workingOnYourOrder": "PRÉPARATION DU RAMASSAGE", "orderDetails.deliveryStatus.deliveryStatus.cancelledUnavailable": "ANNULÉ - NON DISPONIBLE", "orderDetails.deliveryStatus.deliveryStatus.orderReady": "PRÊT POUR LE RAMASSAGE", "orderDetails.deliveryStatus.deliveryStatus.refunded": "REMBOURSÉ", "orderDetails.deliveryStatus.deliveryStatus.pickUp": "RAMASSÉ", "orderDetails.deliveryStatus.deliveryStatus.verified": "VÉRIFIÉ", "orderDetails.deliveryStatus.deliveryStatus.awaitingCancellation": "EN ATTENTE D'ANNULATION", "orderDetails.deliveryStatus.deliveryStatus.cancelledItem": "ANNULÉE", "orderDetails.deliveryStatus.deliveryStatus.cancelledCard": "ANNULÉE", "orderDetails.deliveryStatus.deliveryStatus.cancelledCustomer": "ANNULÉE", "orderDetails.deliveryStatus.deliveryStatus.bopisReturned": "RETOURNÉE", "orderDetails.deliveryStatus.deliveryStatus.inProgress": "EN TRAITEMENT", "orderDetails.deliveryStatus.deliveryStatus.returnInitiated": "RETOUR DÉBUTÉ", "orderDetails.deliveryStatus.deliveryStatus.packageUpdate": "MISE À JOUR DU FORFAIT", "orderDetails.deliveryStatus.deliveryStatus.purchasedInStore": "Marchandise achetée en magasin", "orderDetails.deliveryStatus.bopisStatusText.returned": "Retournée", "orderDetails.deliveryStatus.bopisStatusText.orderReady": "<PERSON><PERSON><PERSON><PERSON><PERSON> d'ici {{dateTime}}", "orderDetails.deliveryStatus.bopisStatusText.refunded": "Refunded", "orderDetails.deliveryStatus.bopisStatusText.pickedUp": "Rama<PERSON><PERSON>", "orderDetails.deliveryStatus.deliveryStatusBodyText.estimatedDeliveryBy": "Livraison prévue d’ici le", "orderDetails.deliveryStatus.deliveryStatusBodyText.estimatedDeliveryIs": "Livraison prévue le", "orderDetails.deliveryStatus.deliveryStatusBodyText.packageDeliveredAt": "Colis livré à", "orderDetails.deliveryStatus.deliveryStatusBodyText.estimatedShipDateBy": "Date d’expédition prévue d’ici le", "orderDetails.deliveryStatus.deliveryStatusBodyText.estimatedShipBackBy": "À renvoyer d’ici le", "orderDetails.deliveryStatus.deliveryStatusBodyText.estimatedShipBy": "Expédition prévue d’ici le", "orderDetails.deliveryStatus.deliveryStatusBodyText.cancelledCustomerCancellation": "Annulation demandée par le client", "orderDetails.deliveryStatus.deliveryStatusBodyText.cancelledItemUnavailable": "Article non offert", "orderDetails.deliveryStatus.deliveryStatusBodyText.cancelledPotentialFraud": "Échec de la vérification du paiement", "orderDetails.deliveryStatus.deliveryStatusBodyText.cancelledGenericReason": "Article annulé", "orderDetails.deliveryStatus.deliveryStatusBodyText.returnProcessedOn": "Retour traité le", "orderDetails.deliveryStatus.deliveryStatusBodyText.bopisReceivedText": "Nous vous enverrons un courriel dès que cette commande sera prête. En général, les commandes sont prêtes dans les deux heures suivant l'achat en ligne. Veuillez noter : les commandes passées après 16 h seront prêtes le lendemain.", "orderDetails.deliveryStatus.deliveryStatusBodyText.bopisItemUnavailable": "Nous n’avons pas été en mesure de trouver ces articles lorsque la commande nous est arrivée en magasin. Nous nous excusons pour tout désagrément. Ces articles ne seront pas portés à votre carte de crédit.", "orderDetails.deliveryStatus.deliveryStatusBodyText.bopisOrderReady": "Disponible pour un ramassage {{dateTime}}", "orderDetails.deliveryStatus.deliveryStatusBodyText.bopisOrderReadyComplement": "Récupérer d'ici ", "orderDetails.deliveryStatus.deliveryStatusBodyText.bopisRefunded": "Cette commande n’a pas été ramassée le {{dateTime}}. Le coût de ces articles a été remboursé selon votre mode de paiement original.", "orderDetails.deliveryStatus.deliveryStatusBodyText.pilotCarrierSchedulePromptHeader": "Action Requise:", "orderDetails.deliveryStatus.deliveryStatusBodyText.pilotCarrierSchedulePromptTextNormal": "Si vous ne l'avez pas déjà fait, vous", "orderDetails.deliveryStatus.deliveryStatusBodyText.pilotCarrierSchedulePromptTextEmphasized": "devez planifier votre livraison en gants blancs.", "orderDetails.deliveryStatus.deliveryStatusBodyText.pilotCarrierUrlPrefix": "Visitez le pilote à", "orderDetails.deliveryStatus.deliveryStatusBodyText.pilotCarrierUrlSuffix": "et entrez le numéro de suivi ci-dessus dans le champ du numéro d'expédition pour planifier ou voir les mises à jour.", "orderDetails.deliveryStatus.deliveryStatusBodyText.packageUpdateText": "Cliquez sur le numéro de suivi ci-dessous pour voir les informations détaillées de suivi et de livraison.", "orderDetails.deliveryStatus.bopisPopUpMessage": "Si vos articles n'ont pas été récupérés à cette date, nous effectuerons un remboursement du montant par le mode de paiement d'origine.", "orderDetails.deliveryStatus.packageOrdering": "Colis no {{packageOrdering}} sur {{numberOfPackages}}", "orderDetails.deliveryStatus.localTime": "Heure locale", "orderDetails.shippingSummaryCard.businessDays": "<PERSON><PERSON>)", "orderDetails.shippingSummaryCard.header": "LIVRAISON", "orderDetails.shippingSummaryCard.storePurchaseHeader": "ACHATS EN MAGASIN", "orderDetails.shippingSummaryCard.itemCountSingular": "({{itemCount}} article)", "orderDetails.shippingSummaryCard.itemCountPlural": "({{itemCount}} articles)", "orderDetails.shippingSummaryCard.bopisLabel": "Ramassage en Magasin ", "orderDetails.shippingSummaryCard.shippingLabel": "Expédition ", "orderDetails.shippingSummaryCard.packageCount": "Nombre de Colis: {{itemCount}}", "orderDetails.shippingSummaryCard.alt.pickup": "icône cueillette BOPIS", "orderDetails.shippingSummaryCard.alt.shipping": "icône expédition", "orderDetails.shippingSummaryCard.alt.storePurchase": "icône d'achat en magasin", "orderDetails.shippingSummaryCard.dropship.packageCount": "Nombre total de colis: {{itemCount}}", "orderDetails.shippingSummaryCard.dropship.description": "Voir ci-dessous pour les dates de livraison estimées", "orderDetails.paymentSummaryCard.shippingPaymentHeader": "LIVRAISON", "orderDetails.paymentSummaryCard.shippingType": "Type d’expédition", "orderDetails.paymentSummaryCard.shippingTo": "Commande expédiée à", "orderDetails.paymentSummaryCard.paymentLabel": "Paiement", "orderDetails.paymentSummaryCard.paymentType.GIFT_CARD": "Carte-cadeau", "orderDetails.paymentSummaryCard.paymentType.REFUND_CHECK": "Chèque de remboursement", "orderDetails.paymentSummaryCard.paymentType.RETURN_CREDIT": "<PERSON><PERSON><PERSON> de retour", "orderDetails.paymentSummaryCard.paymentType.CREDIT_CARD": "Carte de <PERSON>", "orderDetails.paymentSummaryCard.paymentType.CHECK": "Chèque", "orderDetails.paymentSummaryCard.paymentType.PRE_PAID": "Prépayée", "orderDetails.paymentSummaryCard.paymentType.BABY_GIFT": "Cadeau pour bébé", "orderDetails.paymentSummaryCard.paymentType.VISA_CARD": "Visa", "orderDetails.paymentSummaryCard.paymentType.GAPPLCC_CARD": "Carte Gap", "orderDetails.paymentSummaryCard.paymentType.MC_CARD": "MasterCard", "orderDetails.paymentSummaryCard.paymentType.AE_CARD": "American Express", "orderDetails.paymentSummaryCard.paymentType.DISCOVER_CARD": "Discover", "orderDetails.paymentSummaryCard.paymentType.BRPLCC_CARD": "Carte Banana Republic", "orderDetails.paymentSummaryCard.paymentType.JCB_CARD": "Carte JCP", "orderDetails.paymentSummaryCard.paymentType.ONPLCC_CARD": "Carte Old Navy", "orderDetails.paymentSummaryCard.paymentType.DINERS_CARD": "Carte Diners Club", "orderDetails.paymentSummaryCard.paymentType.MS_CARD": "Carte MS", "orderDetails.paymentSummaryCard.paymentType.ATPLCC_CARD": "<PERSON>te <PERSON>", "orderDetails.paymentSummaryCard.paymentType.PayPal": "PayPal", "orderDetails.paymentSummaryCard.paymentType.AFTERPAY": "Afterpay", "orderDetails.paymentSummaryCard.paymentType.INSTANT_CREDIT": "Merch Return Card", "orderDetails.paymentSummaryCard.paymentType.Cash": "Argent comptant", "orderDetails.paymentSummaryCard.paymentType.Klarna": "<PERSON><PERSON><PERSON>", "orderDetails.paymentSummaryCard.paymentEndingIn": " se terminant par ", "orderDetails.paymentSummaryCard.shippingOptions.free": "Expédition gratuite (5 à 7 jours ouvrables)", "orderDetails.paymentSummaryCard.shippingOptions.fiveToSeven": "5 à 7 jours ouvrables", "orderDetails.paymentSummaryCard.shippingOptions.threeToFive": "3 à 5 jours ouvrables", "orderDetails.paymentSummaryCard.shippingOptions.twoToThree": "2 à 3 jours ouvrables", "orderDetails.paymentSummaryCard.shippingOptions.oneDay": "1 jour ouvrable", "orderDetails.paymentSummaryCard.linkedExchange": "<PERSON><PERSON><PERSON> d’é<PERSON>e de la commande ", "orderDetails.shipmentTrackingCard.trackingNumber": "Numéro de suivi", "orderDetails.shipmentTrackingCard.packageCarrier": "Transporteur", "orderDetails.shipmentTrackingCard.showMore": "Plus", "orderDetails.shipmentTrackingCard.showLess": "<PERSON>ins", "orderDetails.shipmentTrackingCard.shipmentHistory": "Historique d’expédition", "orderDetails.shipmentTrackingCard.distributionCenter": " (Centre de distribution)", "orderDetails.shipmentTrackingCard.deliveryType.GROUND_FREIGHT_PRICING": "Transport terrestre au prix du fret", "orderDetails.chargesSummary.summaryChargesHeader": "SOMMAIRE DES FRAIS", "orderDetails.chargesSummary.shippingLabel": "Expédition ({{shippingOptionDescription}})", "orderDetails.chargesSummary.shippingOnlyLabel": "Expédition", "orderDetails.chargesSummary.promotionsAndRewardsLabel": "Mes économies", "orderDetails.chargesSummary.giftServicesLabel": "Services cadeaux", "orderDetails.chargesSummary.taxLabel": "<PERSON><PERSON><PERSON><PERSON>", "orderDetails.chargesSummary.stateTaxLabel": "Frais de réglementation de l'État", "orderDetails.chargesSummary.totalLabel": "Total", "orderDetails.chargesSummary.subtotalLabelPlural": "Sous-total ({{itemCount}} articles)", "orderDetails.chargesSummary.subtotalLabelSingular": "Sous-total ({{itemCount}} article)", "orderDetails.chargesSummary.subtotalLabel": "Sous-total", "orderDetails.chargesSummary.free": "GRATUIT", "orderDetails.chargesSummary.returnProductLabel": "Article retourné {{itemCount}}", "orderDetails.chargesSummary.returnsTaxDisclaimer": "Total du retour incluant les taxes", "orderDetails.chargesSummary.exchangeCreditLabel": "Crédit d’échange", "orderDetails.chargesSummary.promotionsLabel": "Promos", "orderDetails.chargesSummary.inStorePickup": "Ramassage en magasin", "orderDetails.chargesSummary.returnProductLabelPlural": "Produits retournés ({{itemCount}} articles)", "orderDetails.chargesSummary.returnProductLabelSingular": "Produit retourné ({{itemCount}} article)", "orderDetails.chargesSummary.exchangeCreditLabelSingular": "Crédit d’échange ({{itemCount}} article)", "orderDetails.chargesSummary.exchangeCreditLabelPlural": "Crédit d’échange ({{itemCount}} articles)", "orderDetails.chargesSummary.exchangeTaxDisclaimer": "Taxes incluses", "orderDetails.chargesSummary.homePickupFeeLabelSingular": "Frais de cueillette à domicile ({{itemCount}} d’article)", "orderDetails.chargesSummary.homePickupFeeLabelPlural": "Frais de cueillette à domicile ({{itemCount}} d’articles)", "orderDetails.chargesSummary.homePickupFeeLabelText": "Frais de cueillette à domicile", "orderDetails.chargesSummary.merchInstantCreditLabelSingular": "Carte de retour de marchandise ({{itemCount}} d’article)", "orderDetails.chargesSummary.merchInstantCreditLabelPlural": "Carte de retour de marchandise ({{itemCount}} d’articles)", "orderDetails.chargesSummary.merchInstantCreditLabelText": "Carte de retour de marchandise", "orderDetails.chargesSummary.savings": "Des économies", "orderDetails.chargesSummary.rewards": "Récompenses", "orderDetails.orderSelfServiceSummary.additionalChangesPrompt": "Vous devez faire d’autres modifications?", "orderDetails.orderSelfServiceSummary.cancelItems": "Annuler les articles", "orderDetails.orderSelfServiceSummary.cancelOrder": "annuler la commande", "orderDetails.orderSelfServiceSummary.changeShippingAddress": "Modifier l’adresse de livraison", "orderDetails.orderSelfServiceSummary.contactCustomerService": "Contactez le service à la clientèle", "orderDetails.orderSelfServiceSummary.header": "Options en libre-service", "orderDetails.orderSelfServiceSummary.addressUpdateConfirmationText": "Vous avez demandé à changer l'adresse sur cette commande le {{updatedDate}} at {{updatedTime}}. Une fois le changement d'adresse effectué, vous recevrez un e-mail.", "orderDetails.orderSelfServiceSummary.cancelledMessage.cancelledItem": "Vous avez demandé l'annulation d'un ou de plusieurs articles de cette commande le 1er octobre 2021.  Dès que l’annulation aura été traitée, vous recevrez un courriel. Veuillez patienter quelques jours pour que la retenue du montant sur la carte de crédit soit annulée.", "orderDetails.orderSelfServiceSummary.cancelledMessage.cancelledCard": "Unable to verify payment", "orderDetails.orderSelfServiceSummary.cancelledMessage.cancelledCustomer": "Customer requested cancellation", "orderDetails.orderSelfServiceSummary.cancelledMessage.cancelledUnavailable": "We weren't able to find these items when the order arrived at our store. We apologize for any inconvenience. Please wait a few days for the hold on your credit card to be released.", "orderDetails.orderSelfServiceSummary.cancelledMessage.cancelled": "Vous avez demandé l’annulation de cette commande le {{updatedDate}}. Dès que l’annulation aura été traitée, vous recevrez un courriel. Veuillez patienter quelques jours pour que la retenue du montant sur la carte de crédit soit annulée.", "orderDetails.orderSelfServiceSummary.emptySelfServiceText": "Malheureusement, votre commande ne peut être modifiée en raison de son état ou de son type.", "orderDetails.orderSelfServiceSummary.learnMore": "En Savoir Plus", "orderDetails.orderSelfServiceSummary.returnOrExchangeItemText": "<PERSON><PERSON><PERSON> ou échanger un article", "orderDetails.orderSelfServiceSummary.accessReturnPortalText": "Access Return Portal", "orderDetails.orderSelfServiceSummary.returnAnItem": "Return an Item", "orderDetails.signInCard.signInLabel": "Connectez-vous pour voir vos détails de livraison et de paiement pour cette commande.", "orderDetails.signInCard.buttonText": "Ouvrir une session", "orderDetails.signInCard.signUpLabel1": "Vous n'avez pas de compte en ligne avec notre famille de marques?", "orderDetails.signInCard.signUpLinkText": "<PERSON><PERSON>ez un compte", "orderDetails.shared.cost": "{{price}}", "orderDetails.shared.deductionAmount": "{{price}}", "orderDetails.productPanel.productName": "{{<PERSON><PERSON><PERSON><PERSON>}}", "orderDetails.productPanel.sku": "#{{skuNumber}}", "orderDetails.productPanel.colorLabel": "<PERSON><PERSON><PERSON>", "orderDetails.productPanel.sizeLabel": "<PERSON><PERSON>", "orderDetails.productPanel.priceLabel": "Prix", "orderDetails.productPanel.promoLabel": "Promo", "orderDetails.productPanel.quantityLabel": "Qté", "orderDetails.productPanel.finalSaleLabel": "VENTE FERME", "orderDetails.productPanel.finalSaleUnreturnableLabel": "Les articles en vente ferme ne peuvent être retournés.", "orderDetails.productPanel.returnByMailLabel": "Retour par la poste seulement.", "orderDetails.productPanel.imageNotAvailable": "Image non disponible", "orderDetails.productPanel.item": "article", "orderDetails.productPanel.imageUnavailable": "Image non disponible", "orderDetails.productPanel.finalSaleText": "<PERSON><PERSON> Fe<PERSON>e", "orderDetails.productPanel.noReturnsExchanges": "Aucun retour ni échange", "orderDetails.productPanel.savings": "Des économies", "orderDetails.productPanel.each": "chaque", "orderDetails.defaultCopyRightText": "Vous avez demandé l'échange de cet article. V<PERSON><PERSON>z le renvoyer dès que possible.", "orderDetails.copyRightTextWithOriginalOrder": "Cette commande a été générée à partir de votre échange demandé. Si vous ne l'avez pas déjà fait, veuil<PERSON>z renvoyer l'article d'origine dès que possible.", "orderDetails.submitted": "soumis le", "orderDetails.returnProcessed": "Retour traité le", "orderDetails.globalMessageError": "Une erreur technique s'est malheureusement produite. Veuillez actualiser pour réessayer.", "utils.dateTimeFormatter.byDayOfWeekMonthDateTime": "{{dayOfWeekMonthDate}} avant {{time}}", "utils.dateTimeFormatter.atDayOfWeekMonthDateTime": "{{dayOfWeekMonthDate}} à {{time}}", "utils.dateTimeFormatter.monthDaySuffixOrSooner": "{{monthDaySuffix}} ou plus tôt", "utils.dateTimeFormatter.today": "<PERSON>jou<PERSON><PERSON>hui", "support.supportNumber.athleta": "1-877-328-4538", "support.supportNumber.at": "1-877-328-4538", "support.supportNumber.oldnavy": "1-800-OLD-NAVY (1-800-653-6289)", "support.supportNumber.on": "1-800-653-6289", "support.supportNumber.bananarepublic": "1-888-<PERSON> (1-888-277-8953)", "support.supportNumber.br": "1-888-277-8953", "support.supportNumber.bananarepublicfactory": "1-844-273-7746", "support.supportNumber.brfs": "1-844-273-7746", "support.supportNumber.gap": "1-800-GAPSTY<PERSON> (**************)", "support.supportNumber.gp": "**************", "support.supportNumber.gapfactory": "1-844-437-6654", "support.supportNumber.gpfs": "1-844-437-6654", "support.supportNumber.hillcity": "1-833-HILL-CIT<PERSON> (1-833-445-5248)", "support.supportNumber.hc": "1-833-445-5248", "support.homeLink.gp.url": "https://www.gap.com", "support.homeLink.gp.label": "page d'accueil gapcanada.ca", "support.homeLink.at.url": "https://athleta.gap.com", "support.homeLink.at.label": "page d'accueil athleta.gap.com", "support.homeLink.on.url": "https://oldnavy.gap.com", "support.homeLink.on.label": "page d'accueil oldnavy.gapcanada.ca", "support.homeLink.br.url": "https://bananarepublic.gap.com", "support.homeLink.br.label": "page d'accueil bananarepublic.gapcanada.ca", "support.homeLink.hc.url": "https://hillcity.gap.com", "support.homeLink.hc.label": "page d'accueil hillcity.gap.com", "support.relayService": "Composer le 711 pour le service de relais", "support.customerServicePage.gap": "/customerService/info.do?cid=2136", "support.customerServicePage.gapfactory": "/customerService/info.do?cid=1037175", "support.customerServicePage.bananarepublic": "/customerService/info.do?cid=6740", "support.customerServicePage.bananarepublicfactory": "/customerService/info.do?cid=1037835", "support.customerServicePage.athleta": "/customerService/info.do?cid=44959", "support.customerServicePage.oldnavy": "/customerService/info.do?cid=3171", "support.customerServicePage.hillcity": "/customerService/info.do?cid=1113884", "support.chat.chatHeaderLabel": "Clavardage en direct", "support.chat.timing": "Ouvert du lundi au vendredi de 8 h à 22 h (HE)", "support.chat.chatNow": "Clavarder maintenant", "support.chat.chatIsClosed": "Le service de clavardage est fermé", "support.giveUsFeedbackLabel": "Rétroaction", "support.customerServiceLabel": "Service à la clientèle ", "support.customerServiceHours": "De 7 h à 1 h (HE)", "bopis.accordion.tabOne.label": "DÉTAILS SUR LE MAGASIN", "bopis.accordion.tabOne.storeHours": "<PERSON><PERSON><PERSON> du magasin", "bopis.accordion.tabTwo.label": "INSTRUCTIONS DE RAMASSAGE", "bopis.accordion.tabTwo.pickUpNameLabel": "Personne responsable du ramassage:", "bopis.accordion.tabTwo.pickUpName": "<PERSON><PERSON>", "bopis.accordion.tabTwo.instructionLabel": "Que dois-je apporter?", "bopis.accordion.tabTwo.stepOne": "1. <PERSON><PERSON><PERSON> co<PERSON><PERSON>", "bopis.accordion.tabTwo.stepTwo": "2. Une pièce d’identité délivrée par le gouvernement", "bopis.accordion.tabTwo.goTo": "<PERSON><PERSON><PERSON>vous au ", "bopis.accordion.tabTwo.storeName": "Old Navy", "bopis.accordion.tabTwo.storeAddress": "55 Colma Blvd, Colma, CA 94102", "bopis.accordion.tabTwo.openUntil": "Ouvert aujourd’hui jusqu’à ", "bopis.accordion.tabTwo.closingHour": "21 h", "bopis.accordion.tabTwo.idCard": "Carte d'identité", "bopis.accordion.tabTwo.shoppingBag": "Sac de courses", "bopis.accordion.tabTwo.check": "Vérifier les", "bopis.accordion.tabTwo.currentHours": "heures actuelles", "bopis.accordion.tabThree.label": "<PERSON><PERSON>z votre commande!", "bopis.accordion.tabThree.message": "Si vous n’êtes pas en mesure de ramasser votre commande dans les cinq jours, nous l’annulerons et nous annulerons immédiatement la retenue de fonds à votre compte. ", "returns.header": "Retours", "returns.learnMore": "En savoir plus sur les retours et les échanges", "returns.inStore.label": "EN MAGASIN", "returns.inStore.labelInfo": "Retournez en magasin pour notre remboursement le plus rapide possible.", "returns.inStore.labelTwo": "Que dois-je apporter en magasin?", "returns.inStore.labelTwoInfo": "Apporter les articles que vous souhaitez retourner avec un des éléments suivants:", "returns.inStore.labelTwoBold": "un des éléments suivants:", "returns.inStore.bulletOne": "la carte de crédit utilisée pour l’achat ou", "returns.inStore.bulletTwo": "le courriel de confirmation d’expédition", "returns.inStore.labelThree": "Trouver un magasin près de chez vous", "returns.inStore.labelThreeInfo": "Les retours en magasin sont acceptés par toutes nos marques exclusivement dans leurs magasins", "returns.inStore.atStoreLocatorLabel": "Localisateur de magasins Athleta", "returns.inStore.onStoreLocatorLabel": "Localisateur de magasins Old Navy", "returns.inStore.brStoreLocatorLabel": "Localisateur de magasins Banana Republic", "returns.inStore.gpStoreLocatorLabel": "Localisateur de magasins Gap", "returns.inStore.hcStoreLocatorLabel": "Localisateur de magasins Hill City", "returns.inStore.gpfsStoreLocatorLabel": "Localisateur de magasins Gap Factory", "returns.inStore.brfsStoreLocatorLabel": "Localisateur de magasins Banana Republic Factory", "returns.inStore.atStoreLocatorLink": "https://athleta.gap.com/customerService/storeLocator.do", "returns.inStore.onStoreLocatorLink": "https://oldnavy.gap.com/customerService/storeLocator.do", "returns.inStore.brStoreLocatorLink": "https://bananarepublic.gap.com/customerService/storeLocator.do", "returns.inStore.gpStoreLocatorLink": "https://www.gap.com/customerService/storeLocator.do", "returns.inStore.header": "Vous voulez votre remboursement plus tôt?", "returns.byMail.byMail": "Par la poste", "returns.byMail.createLabel": "C<PERSON>ez une étiquette d’expédition", "returns.byMail.createLabelDetails": "pour effectuer un retour par la poste. Tous les retours sont gratuits et peuvent être suivis aisément en ligne. Les retours doivent être traités dans la fenêtre de retour.", "returns.byMail.header": "Vous p<PERSON><PERSON><PERSON><PERSON> expédier votre retour?", "orderHistory.header": "Historique des commandes", "orderHistory.card.estimatedDeliveryLabel": "<PERSON><PERSON><PERSON> livraison par ", "orderHistory.card.trackPackageLink": "Suivre un colis", "orderHistory.card.orderDetailsLink": "<PERSON><PERSON><PERSON> de la commande", "orderHistory.card.sellerLabel": "Produit vendu et expédié par", "orderHistory.card.details": "Détails", "orderHistory.purchaseHistory": "Historique des achats", "orderHistory.purchasedOn": "<PERSON><PERSON><PERSON> le", "orderHistory.next": "Next", "orderHistory.prev": "Prev", "orderSelfService.addNewAddressHeader": "Saisir La Nouvelle Adresse De Livrai<PERSON>", "orderSelfService.addNewShippingAddressHeader": "SAISIR LA NOUVELLE ADRESSE DE LIVRAISON", "orderSelfService.newAddressNote": "Le traitement de votre commande est à l’étape de l’expédition. Veuillez saisir la nouvelle adresse ci-dessous..", "orderSelfService.editShippingAddressHeader": "Modifier l’adresse de livraison", "orderSelfService.saveNewAddrFooterBtnLabel": "Enregistrer les modifications", "orderSelfService.selectAddressButtonLabel": "Sélectionner l’adresse de livraison", "orderSelfService.confirmShipingAddressButtonLabel": "Confirmer l’adresse de livraison", "orderSelfService.changeAddressPrefix": "Vous", "orderSelfService.changeAddressEmphasised": " modifiez l’adresse de livraison", "orderSelfService.changeAddressSuffix": " de cette commande. L’adresse de livraison peut être modifiée une seule fois.", "orderSelfService.changeAddressNote1": "Dès que l’adresse aura été changée, vous recevrez un courriel et les détails de votre commande seront mis à jour.", "orderSelfService.changeAddressNote2": "En fonction de la nouvelle adresse, il est possible que le montant des taxes de vente applicables soit modifié.", "orderSelfService.newShippingAddressHeader": "Nouvelle adresse de livraison", "orderSelfService.selectAddressHeader": "Sélectionner une nouvelle adresse de livraison:", "orderSelfService.changeShippingAddressHeader": "Modifier l’adresse de livraison", "orderSelfService.changeAddressWarning": "Vous pouvez modifier l’adresse de cette commande jusqu’à {{time}} aujourd’hui, {{date}}.", "orderSelfService.currentShippingAddressLabel": "<PERSON><PERSON><PERSON> de livraison actuelle :", "orderSelfService.updateAddressButtonLabel": "METTRE À JOUR L’ADRESSE DE LIVRAISON", "orderSelfService.header": "Annuler des articles", "orderSelfService.content": "Nous préparons votre commande pour l’expédition. Veuillez sélectionner les articles que vous souhaitez annuler.", "orderSelfService.cancelMyEntireOrder": "Annuler toute ma commande", "orderSelfService.useSuggestedAddrTitle": "Or Use Suggested Address", "orderSelfService.verifyAddressTitle": "Verify the Address You Entered", "orderSelfService.useAddressButtonTitle": "USE THIS ADDRESS", "orderSelfService.buttonContinue": "ANNULER LA COMMANDE", "orderSelfService.confirmButton": "CONFIRMER les articles à annuler", "orderSelfService.selectReasonForCancel": "<PERSON>euillez sélectionner une raison", "orderSelfService.cancelItem": "Annuler cet article", "orderSelfService.itemsInThisOrder": "Articles de cette commande", "orderSelfService.reasonForCancel": "Raison de l’annulation (obligatoire)", "orderSelfService.cancelWarning": "Vous pouvez annuler des articles de cette commande jusqu’à {{time}} aujourd’hui, {{date}}.", "orderSelfService.options.0": "J’ai oublié de saisir un code promotionnel ou de rabais", "orderSelfService.options.1": "<PERSON><PERSON>, couleur ou article incorrect", "orderSelfService.options.2": "<PERSON><PERSON><PERSON> <PERSON> liv<PERSON> erronée", "orderSelfService.options.3": "Mode d’expédition erroné", "orderSelfService.options.4": "Mode de paiement erroné", "orderSelfService.options.5": "Je n’avais pas terminé ma commande", "orderSelfService.options.6": "<PERSON>’ai changé d<PERSON>avis", "orderSelfService.options.7": "Commande passée en double", "orderSelfService.options.8": "<PERSON><PERSON>", "orderSelfService.termsAndConditions": "Vous confirmez la", "orderSelfService.cancellation": "annulation d'article(s)", "orderSelfService.order": "de cette commande.", "orderSelfService.action": "Cette action est irréversible.", "orderSelfService.process": "Dès que l’annulation aura été traitée, vous recevrez un courriel et les détails de votre commande seront mis à jour.", "orderSelfService.holdYourCreditCard": "Veuillez patienter quelques jours pour que la retenue du montant sur la carte de crédit soit annulée. Si vous avez utilisé une carte-cadeau, vous recevrez une carte-cadeau électronique par courriel.", "orderSelfService.goBack": "RETOURNER", "orderSelfService.releaseSummary": "Récapitulatif – Articles annulés", "orderSelfService.shippingDetails": "Expédition (3 à 5 jours ouvrables)", "orderSelfService.reasonForCancellation": "Raison de l’annulation", "orderSelfService.incorrectPayment": "Mode de paiement erroné", "orderSelfService.releaseSubtotal": "Ré<PERSON><PERSON><PERSON><PERSON><PERSON>-total", "orderSelfService.releaseTotal": "Total – Articles annulés", "orderSelfService.tax": "<PERSON><PERSON><PERSON><PERSON>", "orderSelfService.item": "article", "orderSelfService.cancel": "ANNULER", "orderSelfService.cancelSelectitem": "article", "orderSelfService.cancelNumItems": "ANNULER {{num}} ARTICLE(S)", "orderSelfService.items": "articles", "orderSelfService.SelectItems": "Sélectionnez le ou les éléments à annuler", "orderSelfService.validOption": "Sélectionnez une option valide.", "orderSelfService.editButtonTitle": "ÉDITER", "orderSelfService.itemIndexTotal": "Article {{index}} sur {{total}}", "orderSelfService.cancelQtyLabel": "Quantité à annuler", "accountSettings.header": "Renseignements personnels", "accountSettings.labels.firstName": "Prénom", "accountSettings.labels.lastName": "Nom de famille", "accountSettings.labels.fullName": "Prénom et nom de famille", "accountSettings.labels.mobile": "Numéro de cellulaire", "accountSettings.labels.dob": "Date de naissance (MM/JJ))", "accountSettings.labels.tmmr": "Textez-moi mes récompenses", "accountSettings.labels.email": "Adresse électronique", "accountSettings.labels.language": "<PERSON><PERSON>", "accountSettings.labels.phoneNumber": "NUMÉRO DE TÉLÉPHONE CELLULAIRE", "accountSettings.errors.required.firstName": "Saisir votre prénom", "accountSettings.errors.required.lastName": "Saisir votre nom de famille", "accountSettings.errors.required.email": "Saisir votre adresse électronique", "accountSettings.errors.required.phoneNumber": "Veuillez saisir un numéro", "accountSettings.errors.required.globalMessageError": "Veuillez réessayer. Nous subissons une interruption de service temporaire.", "accountSettings.errors.valid.firstName": "Saisir un prénom valide.", "accountSettings.errors.valid.lastName": "Saisir un nom de famille valide.", "accountSettings.errors.valid.nameNoSpecialChars": "Veuillez saisir votre nom à nouveau sans caractères spéciaux afin de poursuivre.", "accountSettings.errors.valid.email": "Saisir une adresse électronique valide.", "accountSettings.errors.valid.phoneNumber": "Veuillez saisir un numéro valide.", "accountSettings.errors.valid.dob": "Enter a valid date of birth(MM/DD).", "accountSettings.errors.valid.dobMonth": "Enter a valid birth month(MM).", "accountSettings.errors.valid.dobDate": "Enter a valid birth day(DD).", "accountSettings.errors.notificationSocialSignIn": "You created your Gap Inc. account using Facebook and cannot create a password.", "accountSettings.formTitle": "<PERSON><PERSON>", "accountSettings.formHelpContent.mobileNumberText": "Ajouter le numéro de téléphone pour faciliter la recherche en magasin", "accountSettings.formHelpContent.dobText": "Pour échanger des points en magasin", "accountSettings.formHelpContent.disclaimer.signUpText": "Mobile number must be provided above to sign up for text messages. ", "accountSettings.formHelpContent.disclaimer.disclaimerText": "Consent is not a condition of purchasing goods and services. You can opt-out anytime by responding STOP. You can respond HELP for help.", "accountSettings.formHelpContent.disclaimer.continuationText": "Msg & Data Rates May Apply. By entering your phone number, clicking OK, and completing the sign-up, you consent to receive recurring marketing text messages regarding any Gap Inc. Rewards Program in which you enroll (e.g. Loyalty, CreditCard) which will include receiving as many rewards as you have earned at the mobile number provided that may be sent via an automated system each week, and you also consent to the ", "accountSettings.formHelpContent.disclaimer.textTerms": "text terms", "accountSettings.formHelpContent.disclaimer.privacyPolicy": "privacy policy. ", "accountSettings.notificationSocialSignIn": "You created your Gap Inc. account using Facebook and cannot edit your email address.", "accountSettings.saveButton.save": "Sauvegarder les paramètres", "accountSettings.saveButton.saved": "<PERSON>uvegarde r<PERSON>", "accountSettings.successNotification": "Vous avez réussi! Votre compte a été mis à jour.", "accountSettings.errorNotification": "Veuillez actualiser la page et réessayer. Nous subissons une interruption de service temporaire.", "accountSettings.emailExistsNotification": "Cette adresse courriel est liée à un autre compte. <PERSON><PERSON>illez essayer de la saisir à nouveau.", "accountSettings.phNoExistsNotification": "Ce numéro de téléphone est lié à un autre compte. Veuillez essayer avec un autre numéro.", "accountSettings.mergedAccountNotification": "Vos comptes ont été fusionnés. Veuillez ouvrir une session avec votre compte actif afin d'accéder à vos avantages du programme de Récompenses.", "accountSettings.changePassword": "Modifier le mot de passe", "accountSettings.language.english": "<PERSON><PERSON><PERSON>", "accountSettings.language.french": "Français", "accountSettings.OTP.identityVerification.Main": "Vérification de l’identité", "accountSettings.OTP.identityVerification.header": "Veuillez choisir une méthode pour recevoir un code de vérification de l’identité.", "accountSettings.OTP.identityVerification.secondaryHeader": "Veuillez saisir le code de vérification ici (code valide durant 15 minutes):", "accountSettings.OTP.identityVerification.submitButtonEmail": "ENVOI PAR COURRIEL", "accountSettings.OTP.identityVerification.submitButtonPhone": "ENVOI PAR TEXTO", "accountSettings.OTP.identityVerification.disclaimer": "Le fait de choisir l’envoi par texto signifie que vous consentez à recevoir des messages textes. Des frais pour envoi de messages et de données peuvent s’appliquer.", "accountSettings.OTP.identityVerification.otpPhoneHeader": "Nous avons envoyé un code de vérification au numéro de cellulaire suivant:", "accountSettings.OTP.identityVerification.otpEmailHeader": "Nous avons envoyé un code de vérification à l’adresse de courriel suivante:", "accountSettings.OTP.identityVerification.otpSecondaryHeader": "Veuillez saisir le code de vérification ici (code valide durant 15 minutes):", "accountSettings.OTP.identityVerification.verifyMyIdentity": "VÉRIFIER MON IDENTITÉ", "accountSettings.OTP.identityVerification.sessionTimeout": "Malheureusement, vos renseignements personnels n’ont pas été enregistrés. Pour continuer, veuillez refaire la vérification de votre identité.", "accountSettings.OTP.identityVerification.resendVerificationCode": "Renvoyer le code de vérifications", "accountSettings.OTP.identityVerification.resendVerificationCodeByEmail": "Renvoyer le code de vérification par courriel", "accountSettings.OTP.identityVerification.resendVerificationCodeByText": "Renvoyer le code de vérification par texto", "accountSettings.OTP.identityVerification.sendVerificationCodeByText": "Envoyer le code de vérification par texto", "accountSettings.OTP.identityVerification.sendVerificationCodeByEmail": "Envoyer le code de vérification par courriel", "accountSettings.OTP.identityVerification.resendSuccessMessageEmail": "Le code de vérification a été renvoyé. Veuillez patienter quelques minutes avant de demander un autre code.", "accountSettings.OTP.identityVerification.otpCodeExpired": "Le code de vérification n’est plus valide. Veuillez sélectionner « Renvoyer le code de vérification » et essayez de nouveau.", "accountSettings.OTP.identityVerification.otpServiceError": "Une erreur s’est produite. Veuillez réessayer.", "accountSettings.OTP.identityVerification.otpBotError": "Une erreur s’est produite lors de la vérification de votre compte. Veuillez réessayer.", "accountSettings.OTP.footerText": "Une vérification de l’identité peut être exigée pour certaines modifications.", "accountSettings.OTP.save": "<PERSON><PERSON><PERSON><PERSON>", "accountSettings.OTP.cancel": "Annuler", "accountSettings.OTP.edit": "Modifier", "accountSettings.OTP.add": "Ajouter", "accountSettings.OTP.addPhoneNumberText": "A<PERSON>ter le numéro de cellulaire", "accountSettings.OTP.addDateOfBirthText": "Ajouter la date d’anniversaire", "accountSettings.OTP.addPhoneNumberDescription": "Ajouter un numéro de téléphone pour faciliter la recherche de récompenses en magasin", "accountSettings.OTP.addDateOfBirthDescription": "Ajouter une date d’anniversaire pour échanger des points en magasin", "accountSettings.OTP.validateOTPSuccessMessage": "Votre identité a été vérifiée", "accountSettings.OTP.blankOtp": "Pour continuer, veuillez saisir un code de vérification à 6 chiffres valide.", "accountSettings.OTP.invalidOtp": "Le code de vérification est erroné. Veuillez vérifier le code saisi et essayez de nouveau.", "accountSettings.OTP.otpMaxAttemptsReached": "Le code de vérification a été verrouillé en raison de trop nombreuses tentatives. Veuillez sélectionner « Renvoyer le code de vérification ».", "accountSettings.OTP.isInvalidSessionError": "Malheureusement, vos renseignements personnels n’ont pas été enregistrés. Veuillez sélectionner Modifier pour vérifier votre identité et réessayer.", "accountSettings.OTP.verificationPlaceholderText": "Saisir le code de vérification (p.ex. : 123456)", "changePassword.header": "Modifier mot de passe", "changePassword.subHeader": "Après avoir modifié votre mot de passe, vous pourriez devoir saisir à nouveau vos détails de facturation la prochaine fois que vous passerez une commande.", "changePassword.showPasswordAriaLabel": "Afficher le mot de passe en texte brut. Remarque: votre mot de passe s’affichera à l’écran.", "changePassword.hidePasswordAriaLabel": "Cacher le mot de passe. Remarque:  votre mot de passe ne s’affichera plus à l’écran.", "changePassword.show": "MONTRER", "changePassword.hide": "CACHER", "changePassword.buttonText": "Sauvegarder le mot de passe", "changePassword.buttonTextSaved": "<PERSON>uvegarde r<PERSON>", "changePassword.include": "Votre mot de passe doit inclure:", "changePassword.currentPassword": "Mot de passe actuel", "changePassword.newPassword": "Nouveau mot de passe", "changePassword.confirmPassword": "Confirmer le mot de passe", "changePassword.invalidPassword": "Le mot de passe actuel saisi est erroné.", "changePassword.blankPassword": "Le mot de passe ne peut être vide", "changePassword.globalMessageError": "Veuillez réessayer. Nous subissons une interruption de service temporaire.", "changePassword.errorNotification": "Veuillez actualiser la page et réessayer. Nous subissons une interruption de service temporaire.", "changePassword.errorPasswordAlreadyUsed": "Ce mot de passe a déjà été utilisé. Veuillez réessayer avec un nouveau mot de passe.", "resetPassword.header": "Réinitialiser votre mot de passe", "resetPassword.subHeader": "Réinitialiser le mot de passe pour le compte suivant:", "resetPassword.successHeader": "Votre mot de passe a été mis à jour.", "resetPassword.signInText": "créer un compte", "resetPassword.continueShopping": "Continuer à magasiner", "resetPassword.expiredLinkHeader": "Ce lien est expiré.", "resetPassword.usedLinkHeader": "Ce lien a déjà été utilisé.", "resetPassword.invalidLinkHeader": "Ce lien n’est pas valide.", "resetPassword.saveButton": "Sauvegarder le mot de passe", "resetPassword.requestLinkButton": "Demander un nouveau lien", "resetPassword.errorOnLoadMessage": "Comme nous subissons une interruption temporaire de service, veuillez demander un nouveau lien par courriel.", "resetPassword.errorOnSaveMessage": "Nous n’avons pas été en mesure de mettre à jour le mot de passe du compte {{emailAddress}} pour le moment. Vous pouvez réessayer ou appeler au {{supportNumber}} pour obtenir plus d’assistance.", "forgotPasswordOTP.enterVerificationCode": "Saisissez le code de vérification", "forgotPasswordOTP.OTPPhoneHeader": "Nous avons envoyé un code de vérification au numéro de cellulaire suivant :", "forgotPasswordOTP.OTPVerificationCodeDescription": "Pour la sécurité de votre compte, veuillez saisir le code de vérification ici (code valide durant 15 minutes)", "forgotPasswordOTP.verificationCodeButton": "Code de vérification", "forgotPasswordOTP.submitCodeButton": "Soumettre le code", "forgotPasswordOTP.resendVerificationCode": "Le code de vérification a été renvoyé. Veuillez patienter quelques minutes avant de demander un autre code.", "forgotPasswordOTP.resendVerificationLink": "Le courriel a été envoyé.", "forgotPasswordOTP.identityVerified": "Votre identité a été vérifiée..", "forgotPasswordOTP.resendCode": "Renvoyer le code", "forgotPasswordOTP.returnToSignIn": "Retourner à la connexion", "forgotPasswordOTP.resendLink": "Renvoyer le lien", "forgotPasswordOTP.getEmailLink": "Vous n’avez pas reçu de courriel?", "forgotPasswordOTP.checkYourInbox": "Consultez votre bo<PERSON><PERSON> de r<PERSON>", "forgotPasswordOTP.if": "Si", "forgotPasswordOTP.emailSentDescription": "correspond à un compte, nous vous enverrons par courriel les directives pour réinitialiser votre mot de passe.", "forgotPasswordOTP.blankOtpErrorMessage": "Pour continuer, veuillez saisir un code de vérification à 6 chiffres valide.", "forgotPasswordOTP.resetPasswordSuccess": "Votre mot de passe a été mis à jour.", "forgotPasswordOTP.resetPasswordError": "Malheureusement, votre mot de passe n’a pas pu être réinitialisé. Pour continuer, veuillez refaire la vérification de votre identité.", "forgotPasswordOTP.incorrectOtpErrorMessage": "Le code de vérification est erroné. Veuillez vérifier le code saisi et essayez de nouveau.", "forgotPasswordOTP.expiredOtpErrorMessage": "Le code de vérification n’est plus valide. Veuillez sélectionner « Renvoyer le code » et essayez de nouveau.", "forgotPasswordOTP.technicalErrorMessage": "Une erreur est survenue. Veuillez réessayer.", "noOrderHistory.header": "Vous n’avez actuellement aucune commande active.", "noOrderHistory.summary2": "Pour toute question, veuil<PERSON>z contacter le service à la clientèle au ", "noOrderHistory.or": " ou au custserv@", "noOrderHistory.endEmail": "canada.ca", "noOrderHistory.summary3": "Pour toute question, veuil<PERSON>z contacter le service à la clientèle au 1-800-GAPSTYLE <NAME_EMAIL>", "noOrderHistory.noOrderHistoryImg": "There are no orders from the past 13 months", "noOrderHistory.noRecentOrders": "Nombre d’achats récents", "orderHistoryFooter.summary": "L’historique de vos commandes affiche toutes vos commandes des 6 derniers mois. Les commandes passées lorsque vous n’étiez pas connecté ne sont pas incluses.", "orderHistoryFooter.lookUpCTA": "Recherche d’une commande d’un invité", "orderHistoryFooter.lookUpCTAPrefix": "Consulter", "orderHistoryFooter.lookUpContent": "Besoin de rechercher une commande d’un invité?", "orderHistoryFooter.summary2": "Votre historique des achats affiche toutes les commandes faites en ligne au cours des 13 derniers mois. Les commandes passées lorsque vous n’étiez pas connecté(e) à votre compte ne sont pas affichées dans cet historique.", "orderHistoryFooter.lookupLinkText": "Recherche d’une commande d’un invité", "guestOrderLookup.header": "Recherche d’une commande d’un invité", "guestOrderLookup.summaryPrompt": "Une commande ne figure pas à votre historique?", "guestOrderLookup.summaryMessage": "Recherchez une seule commande en saisissant le numéro de commande et l’adresse électronique utilisée pour passer la commande.", "systemError.header": "Nous sommes désolés.", "systemError.summary1": "Une erreur empêchant l’affichage de cette page est survenue. Pour continuer à magasiner, veuillez revenir à la page ", "systemError.summary2": "Si le problème persiste, veuillez nous appeler au ", "technicalDifficulties.header": "Oh non! Un problème est survenu.", "technicalDifficulties.summary1": "Cette page est temporairement indisponible. Merci de votre patience; nous travaillons à trouver une solution.", "technicalDifficulties.summary2": "Si le problème persiste, veuillez nous appeler au ", "technicalDifficulties.button": "VEUILLEZ ESSAYER DE NOUVEAU", "addressNotSaved.header": "Adresse d’expédition", "addressNotSaved.summary1": "Vous n’avez actuellement aucune adresse sauvegardée.", "addressNotSaved.summary2_link": "<PERSON><PERSON><PERSON>z une adresse", "addressNotSaved.summary2": "pour passer à la caisse plus rapidement.", "addressNotSaved.altText": "Maison avec des arbres", "address.header": "Adresse d’expédition", "address.addAddress": "Ajouter une adresse", "address.edit": "Modifier", "address.delete": "<PERSON><PERSON><PERSON><PERSON>", "address.default": "<PERSON><PERSON> <PERSON><PERSON>", "address.setAsDefault": "Définir comme adresse par défaut", "address.deleteModal.title": "Supprimer l’adresse", "address.deleteModal.confirmationMessage": "Voulez-vous vraiment supprimer cette adresse d’expédition?", "address.deleteModal.yesButton": "O<PERSON>, la supprimer", "address.deleteModal.noButton": "Non, conserver l’adresse", "address.verifyModal.or": "", "address.verifyModal.addressNotRecognized": "", "address.verifyModal.reviewAddress": "", "address.verifyModal.reviewAddresses": "", "address.verifyModal.yesButton": "", "address.verifyModal.editButton": "", "address.verifyModal.accept": "", "address.verifyModal.verifyAddressModalError": "", "address.verifyModal.verifyAddressModalTryAgain": "", "address.addModal.setAsDefault": "Définir comme adresse d’expédition par défaut", "address.addModal.addNewAddress": "Ajouter une nouvelle adresse", "address.addModal.saveAddress": "Sauvegarder l’adresse", "address.addModal.fullName": "Nom complet", "address.addModal.addressLine1": "Adresse municipale", "address.addModal.addressLine2": "No d’app.", "address.addModal.townCity": "Ville/municipalité", "address.addModal.phoneNumber": "Numéro de téléphone", "address.addModal.US.state": "Province", "address.addModal.US.zipcode": "Code postal", "address.addModal.CA.state": "Province", "address.addModal.CA.zipcode": "Code postal", "address.editModal.editAddress": "Modifier l’adresse", "address.editModal.updateAddress": "Mettre à jour l’adresse", "address.errors.required.fullName": "Saisir un prénom et un nom de famille", "address.errors.required.lastName": "Veuillez entrer un nom de famille", "address.errors.required.addressLine1": "Saisir une adresse municipale", "address.errors.required.townCity": "Saisir une ville/municipalité", "address.errors.required.phoneNumber": "Saisir un numéro de téléphone", "address.errors.required.US.state": "Saisir une province", "address.errors.required.US.zipcode": "Saisir un code postal", "address.errors.required.CA.state": "Saisir une province", "address.errors.required.ca.state": "Saisir une province", "address.errors.required.CA.zipcode": "Saisir un code postal", "address.errors.required.ca.zipcode": "Saisir un code postal", "address.errors.valid.fullName": "Saisir un prénom et un nom de famille valide", "address.errors.valid.addressLine1": "Saisir une adresse municipale valide", "address.errors.valid.townCity": "Saisir une ville/municipalité valide", "address.errors.valid.phoneNumber": "Saisir un numéro de téléphone valide", "address.errors.valid.US.state": "Choisir une province valide", "address.errors.valid.US.zipcode": "Saisir un code postal valide", "address.errors.valid.CA.state": "Choisir une province valide", "address.errors.valid.CA.zipcode": "Saisir un code postal valide", "address.errors.valid.ca.zipcode": "Saisir un code postal valide", "address.errors.globalMessageError": "Veuillez réessayer. Nous subissons une interruption de service temporaire.", "wallet.edit": "Modifier", "wallet.delete": "<PERSON><PERSON><PERSON><PERSON>", "wallet.endingIn": "se terminant par", "wallet.addNewCard": "AJOUTER CARTE", "wallet.savedCards": "Cartes sa<PERSON>", "wallet.default": "<PERSON><PERSON> <PERSON><PERSON>", "wallet.expires": "Expiration:", "wallet.noCardsAltText": "icône de carte de crédit", "wallet.noCardsCopy": "Vous n’avez actuellement aucun mode de paiement sauvegardé", "wallet.addCardCopy": "Ajou<PERSON>z une carte de crédit ou de débit", "wallet.fasterCheckoutCopy": "pour passer à la caisse plus rapidement.", "wallet.deleteModal.title": "Supprimer le mode de paiement", "wallet.deleteModal.confirmationMessage": "Voulez-vous vraiment supprimer cette carte?", "wallet.deleteModal.yesButton": "O<PERSON>, la supprimer", "wallet.deleteModal.noButton": "Non, conserver la carte", "wallet.addModal.addPaymentMethod": "Ajouter un mode de paiement", "wallet.addModal.creditCardDetails": "Dé<PERSON> de la carte de crédit", "wallet.addModal.billingAddress": "Adresse de facturation", "wallet.addModal.saveCard": "Sauvegarder la carte", "wallet.addModal.cardNumber": "Numéro de carte", "wallet.addModal.expirationDate": "Date d’expiration (MM/AA)", "wallet.addModal.setAsDefault": "Définir comme mode de paiement par défaut", "wallet.editModal.editPaymentMethod": "Modifier le mode de paiement", "wallet.editModal.updateCard": "Mettre à jour la carte", "wallet.errors.required.cardNumber": "Saisir un numéro de carte", "wallet.errors.required.expirationDate": "Saisir une date d’expiration", "wallet.errors.valid.field": "Le champ a une valeur invalide", "wallet.errors.valid.cardNumber": "Saisir un numéro de carte valide", "wallet.errors.valid.expirationDate": "Saisir une date d’expiration valide", "wallet.errors.valid.cvv": "Entrez un numéro CVV valide", "wallet.errors.globalMessageError": "Veuillez réessayer. Nous subissons une interruption de service temporaire.", "wallet.errors.barclays.signIn": "There was an error. Try again.", "wallet.errors.barclays.guestSignIn": "There was an error. Refresh the page and try again.", "wallet.errors.technicalError": "Nous rencontrons des difficultés techniques. Veuillez réessayer plus tard.", "wallet.barclays.globalInfoMessagePhase1a": "Your Gap Inc. Rewards Credit Card(s) ending -- ", "wallet.barclays.globalInfoMessagePhase1b": " -- will be deactivated June 20, 2022 and replaced with new cards. No action is needed.", "wallet.barclays.globalInfoMessagePhase1Halfa": "Your new Gap Inc. Rewards Credit Card(s) ending -- ", "wallet.barclays.globalInfoMessagePhase1Halfb": " -- has been added to your account.", "wallet.barclays.globalInfoMessagePhase2a": "Start using your new Gap Inc. Rewards Credit Card(s) ending -- ", "wallet.barclays.globalInfoMessagePhase2b": ". Those issued before May 2022 have been deactivated.", "wallet.barclays.deactivationMessage": "This card deactivates on June 20, 2022", "wallet.barclays.deactivatedMessage": "This card was deactivated on June 20, 2022", "wallet.barclays.newCardEndingIn": "New card ending in ****", "wallet.barclays.newCardMessage": "Your new card will be available to use on June, 20 2022", "wallet.barclays.deactivated": "Deactivated", "wallet.gapCreditCardLinkFooter.linkText": "Manage my Gap Inc. Rewards Credit Cards", "wallet.gapCreditCardLinkFooter.link": "/my-account/gap-cards", "home.myAccount": "Mon compte", "home.privacyPolicyMessage": "Vos renseignements seront recueillis et traités conformément à notre", "home.privacyPolicyLink": "Politique de confidentialité", "home.myAccountNav.accountDetails": "<PERSON>é<PERSON> du compte", "home.myAccountNav.settings": "Paramètres", "home.myAccountNav.changePassword": "Modifier le mot de passe", "home.myAccountNav.shippingAddresses": "Adresses d’expédition", "home.myAccountNav.myPointsRewardsNew": "Points et récompenses", "home.myAccountNav.purchaseHistory": "Historique des achats", "home.myAccountNav.communicationPreferences": "Préférences de communication", "home.myAccountNav.ordersReturns": "Commandes et retours", "home.myAccountNav.orderHistory": "Historique de commande", "home.myAccountNav.returnsFaq": "FAQ sur les retours", "home.myAccountNav.storeFinder": "Localisateur de magasins", "home.myAccountNav.wallet": "Portefeuille", "home.myAccountNav.savedCards": "Cartes sa<PERSON>", "home.myAccountNav.savedPaymentMethods": "Saved Payment Methods", "home.myAccountNav.myPointsRewards": "Mes points et récompenses", "home.myAccountNav.checkGiftCardBalances": "Vérifier le solde des cartes-cadeaux", "home.myAccountNav.payMyCard": "Payer ma <PERSON>", "home.myAccountNav.payMyCardgap": "Carte Pay Ma Gap Inc.", "home.myAccountNav.payMyCardoldnavy": "Carte Pay Ma Old Navy", "home.myAccountNav.payMyCardbananarepublic": "Carte Pay Ma Banana Republic", "home.myAccountNav.payMyCardathleta": "Carte Pay Ma Athleta", "home.myAccountNav.recentOrders": "Recent Orders", "home.myAccountNav.gapIncCards": "Gap Inc. Cards", "home.myAccountNav.manageAccount": "<PERSON><PERSON><PERSON> le compte", "home.myAccountNav.giftCards": "Cartes-cadeaux", "home.myAccountNav.giftCardCenter": "Gift Card Center", "home.myAccountNav.customerService": "Customer Service", "home.myAccountNav.payGapIncRewardCard": "My Gap Inc. Rewards Credit Cards", "home.myAccountNav.payGapIncCard": "Pay My Gap Inc. Card", "home.myAccountNav.accountSecurity": "Sûreté du compte", "home.bdss.description": "Looks like you have two Rewards accounts. Merge them to access all your benefits and points in one place.", "home.bdss.title": "You might be missing out", "home.bdss.continueToMerge": "CONTINUE TO MERGE", "home.bdss.thisAccount": "This account", "home.bdss.anotherAccount": "is associated with another account", "home.bdss.drawerHeader": "<PERSON><PERSON> Accounts", "home.bdss.loadingMessage": "Loading", "home.bdss.loadingSubMessage": "Please do not refresh.", "home.bdss.mergeForm.header": "Merge accounts to access your points balance and benefits through this email account:", "home.bdss.mergeForm.secondaryHeader": "For account security, enter this complete email:", "home.bdss.mergeForm.disclaimer": "Member level may be updated according to your total qualified activity. Once accounts are merged, it can’t be undone.", "home.bdss.mergeForm.inputLabel": "Email Address", "home.bdss.mergeForm.submitButton": "MERGE ACCOUNTS", "home.bdss.mergeForm.incorrectEmailThreeTimes": "You have entered the incorrect email address too many times. Please contact customer service at 1.800.GAP.STYLE", "home.bdss.mergeForm.incorrectEmailTwoTimes": "After two failed attempts, you have one more chance at entering the correct email online. If unsuccessful, you'll have to call customer service to continue.", "home.bdss.mergeForm.incorrectEmailOneTime": "After one failed attempt, you have two more chances at entering the correct email online. If unsuccessful, you'll have to call customer service to continue.", "home.bdss.mergeForm.mergeUnsuccessful": "We’ve unfortunately run into issues and your accounts haven’t been merged. Please try again later or contact customer service at ************.", "home.bdss.mergeReview.headerStateOne": "Your Accounts Are Merged", "home.bdss.mergeReview.secondaryHeaderStateOne": "You’ll receive an email once the merge is complete. Earned points and all benefits will then be associated with one account, ", "home.bdss.mergeReview.reviewMessageStateOne": "Review your phone number and birthday", "home.bdss.mergeReview.reviewMessageStateTwo": "Add your birthday", "home.bdss.mergeReview.reviewMessageStateThree": "Add your phone number", "home.bdss.mergeReview.reviewMessageStateFour": "Add your phone number and birthday", "home.bdss.mergeReview.mmdd": " (MM/DD)", "home.bdss.mergeReview.inYour": " in your ", "home.bdss.mergeReview.toYour": " to your ", "home.bdss.mergeReview.profile": "profile", "home.bdss.mergeReview.reviewMessagePartTwo": " to earn points in stores and to make in-store purchases with your rewards.", "home.bdss.mergeReview.emailLabel": "Email", "home.bdss.mergeReview.phoneNumberLabel": "Phone Number", "home.bdss.mergeReview.reviewButton": "REVIEW PROFILE", "home.bdss.mergeReview.goToButton": "GO TO PROFILE", "home.bdss.mergeReview.notNow": "Not Now", "home.bdss.mergeReview.disclaimer": "Member level may be updated according to total qualified activity.", "unsubscribe.success.header": "Désinscription réussie!", "unsubscribe.success.email": "L’adresse électronique suivante:", "unsubscribe.success.hasBeen": "a été désinscrite de", "unsubscribe.success.marketingEmails": "Old Navy, Gap et Banana Republic.", "unsubscribe.success.preferences": "Modifiez vos préférences de communication", "unsubscribe.success.toStart": "pour recommencer à recevoir des courriels de notre part.", "unsubscribe.success.startShopping": "commencer à magasiner", "banner.rewards": "Récompenses", "banner.rewardsBalances": "Re<PERSON>s Balance", "banner.rewardsCardmember": "ENTHOUSIASTE Membre du programme Récompenses", "banner.rewardsAvailable": "in rewards available", "banner.cashAvailable": "super cash available", "banner.availableToUse": "Available for use until", "banner.hello": "Bonjour", "banner.helloLoud": "Bonjour", "banner.recentOrder": "commande récente", "banner.recentOrders": "commandes récentes", "banner.shipmentReady": "A shipment is almost ready to go!", "banner.daysLeft": "jours restants", "banner.expiresToday": "Prend fin aujourd'hui", "banner.expiresTodayToRedeem": "À échanger jusqu'à aujourd'hui", "banner.daysLeftToRedeem": "Jours pour échanger", "banner.daysLeftToRedeemLower": "jours pour échanger", "banner.daysLeftToRedeemYour": "jours pour échanger vos", "banner.youHave": "<PERSON><PERSON> avez", "banner.inRewardsWith": "de récompenses avec", "banner.dollarsOfYour": "dollars de vos", "banner.dollarsInRewards": "dollars en récompenses", "banner.in": "en", "banner.with": "avec", "loader.loading": "chargement...", "barclaysServicing.peekInfoLoadingText": "Loading...please don't refresh while we connect.", "barclaysServicing.labels.headerBarclaysOrSynchrony": "Gap Inc. Rewards Visa Cards", "barclaysServicing.labels.dontSeeCreaditCards": "Don’t see your credit cards below?", "barclaysServicing.labels.Select": "Select manage and pay to log in and access your card information.", "barclaysServicing.labels.cardIssuedBefore": "All Gap Inc. Rewards Credit Cards applied", "barclaysServicing.labels.Before": " before", "barclaysServicing.labels.cardIssuedAfter": "All Gap Inc. Rewards Credit Cards applied", "barclaysServicing.labels.After": " on or after", "barclaysServicing.labels.date": " May, 2022", "barclaysServicing.labels.header": "Manage My {{brands}} Credit Cards", "barclaysServicing.labels.faq": "FAQ", "barclaysServicing.labels.enableAccountPreview": "Enable account preview", "barclaysServicing.labels.manageAndPay": "Manage and Pay", "barclaysServicing.labels.enablePreview": "Enable Quickview", "barclaysServicing.labels.peekWarning": "Quickview unavailable at this time. Refresh or try again later.", "barclaysServicing.labels.myAccount": "My Account", "barclaysServicing.labels.savedCards": "Saved Cards", "barclaysServicing.labels.apply": "Apply", "barclaysServicing.labels.rewardsCards": "Rewards Cards", "barclaysServicing.labels.servicingHeader": "My Gap Inc. Rewards Credit Cards", "barclaysServicing.cardDetails.availableCredit": "Available Credit", "barclaysServicing.cardDetails.currentBal": "Current Balance", "barclaysServicing.cardDetails.lastPayment": "Last Payment Received", "barclaysServicing.cardDetails.statementBal": "Statement Balance", "barclaysServicing.cardDetails.dueDate": "Payment Due", "barclaysServicing.cardDetails.manageCard": "Manage and Pay", "barclaysServicing.cardInfo.isSynchrony": "Icon Visa", "barclaysServicing.cardInfo.isBarclays": "Rewards Mastercard", "barclaysServicing.cardInfo.availableDate": "This card will be available to use on ", "barclaysServicing.payCard.at": "https://athleta.syf.com/login/", "barclaysServicing.payCard.br": "https://bananarepublic.syf.com/login/", "barclaysServicing.payCard.gp": "https://gap.syf.com/login/", "barclaysServicing.payCard.gap": "https://gap.syf.com/login/", "barclaysServicing.payCard.on": "https://oldnavy.syf.com/login/", "barclaysServicing.payCard.gapfs": "https://gap.syf.com/login/", "barclaysServicing.payCard.brfs": "https://bananarepublic.syf.com/login/", "barclaysServicing.manageAndPayBarclays.prod.gap": "https://gap.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.prod.gp": "https://gap.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.prod.on": "https://oldnavy.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.prod.br": "https://bananarepublic.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.prod.at": "https://athleta.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.prod.gapfs": "https://gap.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.prod.brfs": "https://bananarepublic.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.stage.gap": "https://qa01-gap.barclaysus.com/servicing/home", "barclaysServicing.manageAndPayBarclays.stage.gp": "https://qa01-gap.barclaysus.com/servicing/home", "barclaysServicing.manageAndPayBarclays.stage.on": "https://qa01-oldnavy.barclaysus.com/servicing/home", "barclaysServicing.manageAndPayBarclays.stage.br": "https://qa01-bananarepublic.barclaysus.com/servicing/home", "barclaysServicing.manageAndPayBarclays.stage.at": "https://qa01-athleta.barclaysus.com/servicing/home", "barclaysServicing.manageAndPayBarclays.stage.gapfs": "https://qa01-gap.barclaysus.com/servicing/home", "barclaysServicing.manageAndPayBarclays.stage.brfs": "https://qa01-bananarepublic.barclaysus.com/servicing/home", "barclaysServicing.unKnownCard.synchrony": "Gap Inc. Rewards Visa Cards", "barclaysServicing.unKnownCard.barclays": "Gap Inc. Rewards Mastercards", "barclaysServicing.barclaysPLCC.gp": "Gap Good Rewards Credit Card", "barclaysServicing.barclaysPLCC.on": "Navyist Rewards Credit Card", "barclaysServicing.barclaysPLCC.br": "Banana Republic Rewards Credit Card", "barclaysServicing.barclaysPLCC.at": "Athleta Rewards Credit Card", "barclaysServicing.barclaysCBCC.gp": "Gap Good Rewards Mastercard®", "barclaysServicing.barclaysCBCC.on": "Navyist Rewards Mastercard®", "barclaysServicing.barclaysCBCC.br": "Banana Republic Rewards Mastercard®", "barclaysServicing.barclaysCBCC.at": "Athleta Rewards Mastercard®", "barclaysServicing.barclaysCBCC.gpw": "Gap Good Rewards World Mastercard®", "barclaysServicing.barclaysCBCC.onw": "Navyist Re<PERSON>s World Mastercard®", "barclaysServicing.barclaysCBCC.brw": "Banana Republic Rewards World Mastercard®", "barclaysServicing.barclaysCBCC.atw": "Athleta Rewards World Mastercard®", "barclaysServicing.synchronyCBCC.gp": "Gap Good Rewards Visa Credit Card", "barclaysServicing.synchronyCBCC.on": "Navyist Rewards Visa Credit Card", "barclaysServicing.synchronyCBCC.br": "Banana Republic Rewards Visa Credit Card", "barclaysServicing.synchronyCBCC.at": "Athleta Rewards Visa Credit Card", "barclaysServicing.synchronyCBCC.gf": "Gap Inc. Visa Signature Credit Card", "barclaysServicing.synchronyPLCC.gp": "Gap Good Rewards Credit Card", "barclaysServicing.synchronyPLCC.on": "Navyist Rewards Credit Card", "barclaysServicing.synchronyPLCC.br": "Banana Republic Rewards Credit Card", "barclaysServicing.synchronyPLCC.at": "Athleta Rewards Credit Card", "barclaysServicing.synchronyPLCC.gf": "Gap Inc. Signature Credit Card", "barclaysServicing.applyAndEarnText.applyText": "Apply for a", "barclaysServicing.applyAndEarnText.earnText": "and earn 5 points per $1 spent.", "barclaysServicing.pendingApproval": "*Pending approval", "privacypolicy.url": "", "privacypolicy.labels.headerPrivacypolicy": "Have questions about your Gap Inc. Rewards Credit Cards?", "privacypolicy.labels.askedQuestions": "View Frequently Asked Questions", "privacypolicy.labels.information": "Your information will be collected and handled in accordance with our ", "privacypolicy.labels.policy": "Privacy Policy", "privacypolicy.labels.dontSeeCreaditCards": "Don’t have a Gap Inc. Rewards Credit Card?", "privacypolicy.labels.applyForCard": "Apply for a Card", "privacypolicy.barclays.startButton": "Start Card Application", "privacypolicy.barclays.skip": "Or, skip this step. If skipped, your card won't be saved automatically*", "privacypolicy.barclays.disclosure": "Customers not signed into a Rewards Account prior to applying for a card will not have their card credentials saved to their account.", "privacypolicy.askedQuestionLinks.gap.url": "#", "privacypolicy.askedQuestionLinks.gap.label": "gap.com faq", "privacypolicy.askedQuestionLinks.gp.url": "#", "privacypolicy.askedQuestionLinks.gp.label": "gap.com faq", "privacypolicy.askedQuestionLinks.at.url": "#", "privacypolicy.askedQuestionLinks.at.label": "athleta.gap.com faq", "privacypolicy.askedQuestionLinks.on.url": "#", "privacypolicy.askedQuestionLinks.on.label": "oldnavy.gap.com faq", "privacypolicy.askedQuestionLinks.br.url": "#", "privacypolicy.askedQuestionLinks.br.label": "bananarepublic.gap.com faq", "privacypolicy.askedQuestionLinks.hc.url": "#", "privacypolicy.askedQuestionLinks.hc.label": "hillcity.gap.com faq", "privacypolicy.askedQuestionLinks.gapfs.url": "#", "privacypolicy.askedQuestionLinks.gapfs.label": "gapfactory.com faq", "privacypolicy.askedQuestionLinks.brfs.url": "#", "privacypolicy.askedQuestionLinks.brfs.label": "bananarepublicfactory.gapfactory.com", "giftCardsBalance.title": "CARTES-CADEAUX", "giftCardsBalance.header": "Vérifier le solde de la carte-cadeau", "giftCardsBalance.checkBalance": "Vérifier le solde", "giftCardsBalance.cardPlaceHolder": "Numéro de carte à 16 chiffres", "giftCardsBalance.pinPlaceHolder": "NIP à 4 chiffres", "giftCardsBalance.checkBalanceAmountHeader": "Solde de la carte-cadeau (#{{cardsLastFourDigits}})", "giftCardsBalance.checkBalanceAmountReloadButton": "Recharger la carte-cadeau", "giftCardsBalance.errors.cardNumber": "Saisissez un numéro de carte-cadeau valide", "giftCardsBalance.errors.cardNumberError": "Le numéro de la carte doit comporter 16 caractères", "giftCardsBalance.errors.invalidCardNumber": "Saisir un numéro de carte valide.", "giftCardsBalance.errors.cardPin": "Entrez un numéro d’identification valide", "giftCardsBalance.errors.cardPinError": "Le code PIN doit comporter 4 caractères", "giftCardsBalance.errors.vaultError": "Une erreur technique s’est malheureusement produite. Veuillez rafraîchir la page pour réessayer.", "giftCardsBalance.errors.giftCardError": "Nous ne reconnaissons pas les informations de cette  carte-cadeau. Veuillez réessayer.", "valueCenter.tabs.membership": "Connaître les avantages", "valueCenter.tabs.trackPoints": "Suivre vos points", "valueCenter.tabs.earnRedeem": "Accumuler et échanger", "valueCenter.membership.featuredBenefits.header": "Vos avantages", "valueCenter.membership.featuredBenefits.pointsTitle1": "1 POINT POUR CHAQUE DOLLAR D’ACHAT", "valueCenter.membership.featuredBenefits.pointsTitle2": "5 POINTS POUR CHAQUE DOLLAR D’ACHAT", "valueCenter.membership.featuredBenefits.earnOneAltText": "Earn Points Icon", "valueCenter.membership.featuredBenefits.pointsCopytext1": "Vous accumulez des points sur vos achats au sein de notre famille de marques.", "valueCenter.membership.featuredBenefits.pointsCopytext2": "100 points = 1 $ en récompense", "valueCenter.membership.featuredBenefits.shippingTitle": "LIVRAISON GRATUITE", "valueCenter.membership.featuredBenefits.shippingCopytext1": "Obtenez une livraison gratuite de 3 à 5 jours sur toutes les commandes de 50 $ et plus", "valueCenter.membership.featuredBenefits.shippingCopytext2": "Obtenez une expédition gratuite de 2 à 3 jours sur toutes les commandes de 50 $ et plus.", "valueCenter.membership.featuredBenefits.shippingCopytext3": "Obtenez une livraison gratuite de 7 à 9 jours sur toutes les commandes de 50 $ et plus", "valueCenter.membership.featuredBenefits.shippingCopytext4": "Obtenez une livraison gratuite de 7 à 9 jours sur toutes les commandes de 35 $ et plus.", "valueCenter.membership.featuredBenefits.shippingAltText": "Flying Package Icon", "valueCenter.membership.featuredBenefits.quarterlyTitle": "Prime de magasinage trimestrielle", "valueCenter.membership.featuredBenefits.quarterlyCopytext1": "Obtenez des 125 points en prime chaque trimestre où vous faites un achat au sein de notre famille de marques.", "valueCenter.membership.featuredBenefits.quarterlyCopytext2": "Obtenez des 250 points en prime chaque trimestre où vous faites un achat au sein de notre famille de marques.", "valueCenter.membership.featuredBenefits.quarterlyCopytext3": "Obtenez des 500 points en prime chaque trimestre où vous faites un achat au sein de notre famille de marques.", "valueCenter.membership.featuredBenefits.bonusAltText": "Quarterly Bonus Icon", "valueCenter.membership.featuredBenefits.brandsTitle": "Prime multimarque", "valueCenter.membership.featuredBenefits.brandsCopytext": "Obtenez jusqu’à 2000 points en prime par année lorsque vous achetez chez deux marques de Gap Inc. ou plus.", "valueCenter.membership.featuredBenefits.familyBrandsAltText": "Family of Brands Shopping Bag Icon", "valueCenter.membership.featuredBenefits.birthdayTitle": "EXCLUSIVE BIRTHDAY OFFERS", "valueCenter.membership.featuredBenefits.birthdayCopytext": "Loyalty members receive exclusive deals from our family of brands around their birthday.", "valueCenter.membership.compareBenefits.header": "Avantages comparés", "valueCenter.membership.compareBenefits.allBrandsAltText": "UNE SEULE ADHÉSION. QUATRE MARQUES.", "valueCenter.membership.compareBenefits.or": "OU", "valueCenter.membership.compareBenefits.coreLabel": "CLASSIQUE", "valueCenter.membership.compareBenefits.coreCopytext": "Moins de 500 $ d’achats annuels", "valueCenter.membership.compareBenefits.coreAltText": "Core Level Badge", "valueCenter.membership.compareBenefits.enthusiastLabel": "ENTHOUSIASTE", "valueCenter.membership.compareBenefits.enthusiastCopytext1": "Les titulaires de carte commencent ici", "valueCenter.membership.compareBenefits.enthusiastCopytext2": "500 $ à 999 $ d’achats annuels", "valueCenter.membership.compareBenefits.enthusiastAltText": "Enthusiast Level Badge", "valueCenter.membership.compareBenefits.iconLabel": "ÉLITE", "valueCenter.membership.compareBenefits.iconCopytext1": "5000 $ points en tant que titulaire de carte", "valueCenter.membership.compareBenefits.iconCopytext2": "1000 $ et plus d’achats annuels", "valueCenter.membership.compareBenefits.iconAltText": "Icon Level Badge", "valueCenter.membership.compareBenefits.membersAndCardmembers": "Membres et Titulaire*", "valueCenter.membership.compareBenefits.membersAndCardmembersAltText": "Membres et Titulaire", "valueCenter.membership.compareBenefits.cardmemberExclusive": "Exclusif au titulaire*", "valueCenter.membership.compareBenefits.cardmemberExclusiveAltText": "Exclusif au titulaire", "valueCenter.membership.newCardMember.header": "Offre pour les nouveaux titulaires de carte", "valueCenter.membership.newCardMember.copytext1": "Obtenez 20 % de réduction la première fois que vous utilisez notre carte de crédit dans chacune de nos marques au cours des 14 premiers jours", "valueCenter.membership.newCardMember.copytext2": "Inscrivez-vous pour recevoir des e-mails et restez informé", "valueCenter.membership.newCardMember.copytext3": "250 POINTS EN PRIME", "valueCenter.membership.earn.header": "Gains et échanges", "valueCenter.membership.earn.or": "OU", "valueCenter.membership.earn.copytext1": "Gagnez 5 points pour chaque dollar dépensé en tant que titulaire de carte dans notre famille de marques", "valueCenter.membership.earn.copytext2": "Gagnez 1 point pour chaque dollar dépensé dans notre famille de marques en tant que membre Rewards", "valueCenter.membership.earn.copytext3": "Les titulaires de la carte Gap Inc. Mastercard® gagnent 1 point pour chaque dollar dépensé en dehors de notre famille de marques", "valueCenter.membership.earn.copytext4": "Tous les 100 points = 1 $ de récompense échangeable dans notre famille de marques, aucune restriction de marchandise", "valueCenter.membership.specialPerks.header": "Avantages spéciaux", "valueCenter.membership.specialPerks.shipping1": "Livraison rapide gratuite (sur les commandes de 50 $ et plus)", "valueCenter.membership.specialPerks.shipping2": "<PERSON><PERSON><PERSON> gratuite", "valueCenter.membership.specialPerks.shipDays1": "3 À 5 JOURS", "valueCenter.membership.specialPerks.shipDays2": "2 À 3 JOURS", "valueCenter.membership.specialPerks.shipCash1": "Commandes de 50 $ et plus", "valueCenter.membership.specialPerks.shipCash2": "Commandes de 35 $ et plus", "valueCenter.membership.specialPerks.copytext1": "Pas de frais annuels et 0 $ de protection contre la fraude**", "valueCenter.membership.specialPerks.copytext2": "Faites le bien : donnez au suivant lorsque vous faites don de vos récompenses à une bonne cause", "valueCenter.membership.specialPerks.copytext3": "Offres exclusives réservées aux membres", "valueCenter.membership.specialPerks.copytext4": "Accès anticipé", "valueCenter.membership.bonuses.header": "Primes", "valueCenter.membership.bonuses.brandBonusCopytext": "Prime multimarque : <PERSON><PERSON><PERSON><PERSON> jusqu’à 2000 points en prime par année lorsque vous achetez chez deux marques de Gap Inc. ou plus.", "valueCenter.membership.bonuses.brandPoints1": "2 marques = 500 points", "valueCenter.membership.bonuses.brandPoints2": "3 marques = 1000 points", "valueCenter.membership.bonuses.brandPoints3": "4 marques = 2000 points", "valueCenter.membership.bonuses.quarterlyBonusCopytext": "Bonus trimestriel : obtenez des points bonus chaque trimestre où vous effectuez un achat dans notre famille de marques.", "valueCenter.membership.bonuses.bonusPoints1": "125 points en prime", "valueCenter.membership.bonuses.bonusPoints2": "250 points en prime", "valueCenter.membership.bonuses.bonusPoints3": "500 points en prime", "valueCenter.membership.bonuses.birthday": "Prime d’anniversaire : O<PERSON><PERSON><PERSON> une prime pour votre anniversaire – C’est notre cadeau!", "valueCenter.membership.bonuses.copytext1": "Double Reward Day : <PERSON><PERSON> la valeur de vos récompenses", "valueCenter.membership.bonuses.copytext2": "<PERSON><PERSON>ez votre propre journée de vente et recevez 15 % de réduction supplémentaire sur tout achat dans notre famille de marques", "valueCenter.membership.exclusives.header": "EXCLUSIVITÉS DE LA MARQUE", "valueCenter.membership.exclusives.gp": "GAP", "valueCenter.membership.exclusives.gpCopytext": "Convertissez les GapCash inutilisés en points", "valueCenter.membership.exclusives.br": "BANANA REPUBLIC", "valueCenter.membership.exclusives.brCopytext1": "Modifications de base gratuites sur les produits Banana Republic", "valueCenter.membership.exclusives.brCopytext2": "Convertissez les StyleCash Banana Republic Factory inutilisés en points", "valueCenter.membership.exclusives.on": "OLD NAVY", "valueCenter.membership.exclusives.onCopytext": "Convertir les Super Cash Old Navy inutilisés en points", "valueCenter.membership.exclusives.at": "ATHLETA", "valueCenter.membership.exclusives.atCopytext1": "Accès à la communauté Athleta, y compris des événements numériques et en direct, des ressources d'experts, des cours de fitness et plus encore", "valueCenter.membership.addCard.header": "Vous avez une carte?", "valueCenter.membership.addCard.copytext": "Ajoutez votre carte de crédit Récompenses Gap Inc. à votre compte pour faire le suivi de vos points et de vos récompenses.", "valueCenter.membership.addCard.buttonText": "Ajouter une carte", "valueCenter.membership.earnPoints.header": "Vous souhaitez obtenir plus de points?", "valueCenter.membership.earnPoints.copytext": "Demandez une carte de crédit Récompenses {{brand}} et obtenez 5 points pour chaque dollar dépensé au sein de notre famille de marques avec votre nouvelle carte, dès votre approbation.**", "valueCenter.membership.earnPoints.buttonText": "De<PERSON>er la carte", "valueCenter.membership.earnPoints.altText": "icône de carte de crédit", "valueCenter.membership.startShopping.core": "CLASSIQUE", "valueCenter.membership.startShopping.coreAltText": "Core Level Badge", "valueCenter.membership.startShopping.enthusiast": "ENTHOUSIASTE", "valueCenter.membership.startShopping.enthusiastAltText": "Enthusiast Level Badge", "valueCenter.membership.startShopping.icon": "ÉLITE", "valueCenter.membership.startShopping.iconAltText": "Icon Level Badge", "valueCenter.membership.startShopping.header1": "Commencez à magasiner!", "valueCenter.membership.startShopping.congratulationsHeader": "Félicitations!", "valueCenter.membership.startShopping.congratulationsHeader_icon": "Félicitations! Vous êtes membre Élite!", "valueCenter.membership.startShopping.iconCelebrateAltText": "Icon Level Celebration Trophy", "valueCenter.membership.startShopping.header_core": "Vous pourriez devenir membre Enthousiaste!", "valueCenter.membership.startShopping.header_enthusiast": "Vous pourriez devenir membre Élite!", "valueCenter.membership.startShopping.header_icon": "Vous êtes sur la bonne voie!", "valueCenter.membership.startShopping.congratulationsText_icon": "Comme membre Élite, vous avez maintenant accès à des avantages exclusifs. Profitez-en!", "valueCenter.membership.startShopping.congratulationsText_visasignature": "Comme titulaire d’une carte Visa Signature® Gap Inc., vous avez accès à tous les avantages du statut de membre Élite. Profitez-en!", "valueCenter.membership.startShopping.copyright_core_noProgress_noCardHolder": "Dépensez {{amount}} $ d’ici la fin de l’année pour profiter de tous les ", "valueCenter.membership.startShopping.copyright_core_progress_noCardHolder": "Dépensez {{amount}} d’ici la fin de l’année pour profiter de tous les", "valueCenter.membership.startShopping.copyright_core_noProgress_cardHolder": "Dépensez {{amount}} d’ici la fin de l’année pour profiter de tous les ", "valueCenter.membership.startShopping.copyright_core_progress_cardHolder": "Dépensez {{amount}} by d’ici la fin de l’année pour profiter de tous les ", "valueCenter.membership.startShopping.copyright_enthusiast_cardHolder": "Dépensez {{amount}} au sein de notre famille de marques {{points}} points d’ici la fin de l’année pour profiter de tous les ", "valueCenter.membership.startShopping.copyright_enthusiast_noCardHolder": "Dépensez {{amount}} d’ici la fin de l’année pour profiter de tous les ", "valueCenter.membership.startShopping.copyright_icon_cardHolder": "Dépensez {{amount}} au sein de notre famille de marques {{points}} d’ici la fin de l’année pour rester membre Élite", "valueCenter.membership.startShopping.copyright_icon_noCardHolder": "Dépensez {{amount}} d’ici la fin de l’année pour rester membre Élite", "valueCenter.membership.startShopping.copyright_next_core": "Avantages membre Enthousiaste", "valueCenter.membership.startShopping.copyright_next_enthusiast": "Avantages membre Élite", "valueCenter.membership.startShopping.copyright_transition": "Félicitations! Vous êtes admissible au statut de membre supérieur. Vos nouveaux avantages entreront en vigueur d’ici 60 jours.", "valueCenter.membership.startShopping.spent": "<PERSON><PERSON> avez d<PERSON>", "valueCenter.membership.startShopping.earned": "<PERSON> as gagné", "valueCenter.membership.startShopping.or": "OU", "valueCenter.membership.startShopping.applyCard_gp": "Demandez une carte de crédit Récompenses Gap Good", "valueCenter.membership.startShopping.applyCard_gap": "Demandez une carte de crédit Récompenses Gap Good", "valueCenter.membership.startShopping.applyCard_on": "Demandez une carte de crédit Récompenses Navyist", "valueCenter.membership.startShopping.applyCard_br": "Demandez une carte de crédit Récompenses Banana Republic", "valueCenter.membership.startShopping.applyCard_at": "Demandez une carte de crédit Récompenses Athleta", "valueCenter.membership.startShopping.applyCard_gpfs": "Demandez une carte de crédit Récompenses Gap Good", "valueCenter.membership.startShopping.applyCard_gapfs": "Demandez une carte de crédit Récompenses Gap Good", "valueCenter.membership.startShopping.applyCard_brfs": "Demandez une carte de crédit Récompenses Banana Republic", "valueCenter.membership.startShopping.applyCard_CORE": " et profitez de tous les avantages du statut de membre Enthousiaste suivant votre approbation. De plus, obtenez {{points}} points pour chaque dollar dépensé au sein de notre famille de marques avec votre nouvelle carte!**", "valueCenter.membership.startShopping.applyCard_ENTHUSIAST": " et obtenez {{points}} points pour chaque dollar dépensé au sein de notre famille de marques avec votre nouvelle carte!**", "valueCenter.membership.startShopping.applyCard_ICON": " et obtenez {{points}} points pour chaque dollar dépensé au sein de notre famille de marques avec votre nouvelle carte!**", "valueCenter.membership.startShopping.footer_core": "Profitez de vos avantages jusqu’à ce que vous changiez de statut de membre. Votre admissibilité au statut de membre supérieur dépend des activités de votre compte.", "valueCenter.membership.startShopping.footer_enthusiast_noCardHolder": "Profitez de vos avantages actuels jusqu'à ce que vous soyez surclassé. L'admissibilité au surclassement de niveau peut changer en fonction de l'activité du compte.**", "valueCenter.membership.startShopping.footer_enthusiast_cardHolder": "Profitez de vos avantages actuels jusqu’à ce que vous changiez de statut de membre. Votre admissibilité au statut de membre supérieur dépend des activités de votre compte.", "valueCenter.membership.startShopping.footer_icon_noCardHolder_nextYearExp": "Profitez de vos avantages actuels jusqu’au  {{date}}.**", "valueCenter.membership.startShopping.footer_icon_noCardHolder_currentYearExp": "Profitez de vos avantages actuels jusqu’au  {{date}}.  Le maintien de votre admissibilité à votre statut de membre dépend des activités de votre compte.", "valueCenter.membership.startShopping.footer_icon_cardHolder_currentYearExp": "Profitez de vos avantages actuels jusqu’au  {{date}}.  Le maintien de votre admissibilité à votre statut de membre dépend des activités de votre compte.", "valueCenter.membership.startShopping.manageCard": "<PERSON><PERSON><PERSON> et payez la carte de crédit My Gap Inc. Rewards", "valueCenter.trackPoints.pointsSummary.youHave": "<PERSON><PERSON> avez", "valueCenter.trackPoints.pointsSummary.points": "Points", "valueCenter.trackPoints.pointsSummary.inRewards": "({{rewards}} en récompenses)", "valueCenter.trackPoints.pointsSummary.viewCopyright": "<PERSON>ff<PERSON><PERSON> et échanger toutes les récompenses à la caisse et dans le sac", "valueCenter.trackPoints.pointsSummary.legalCopyright": "Certaines conditions légales s’appliquent. Veuillez cliquer sur les liens Détails et conditions générales au bas de la page pour en savoir plus", "valueCenter.trackPoints.pointsSummary.earnPointsText": "Obtenez 1 point pour chaque dollar d’achat.", "valueCenter.trackPoints.pointsSummary.rewardsText": "100 points = 1 $ en récompense", "valueCenter.trackPoints.table.activity": "Activité", "valueCenter.trackPoints.table.points": "POINTS", "valueCenter.trackPoints.table.pagination": "Page de {{index}} de {{total}}", "valueCenter.trackPoints.table.empty": "Lorsque vous aurez accumulé des points, vous pourrez les voir ici.", "valueCenter.trackPoints.table.history": "Nous faisons le suivi de vos activités des trois derniers mois.", "valueCenter.trackPoints.table.order": "Ordre #", "valueCenter.trackPoints.pagination.page": "Page", "valueCenter.trackPoints.pagination.of": "de", "valueCenter.trackPoints.pagination.left": "Aller à la page précédente", "valueCenter.trackPoints.pagination.right": "Aller à la page suivante", "valueCenter.trackPoints.pagination.pagination": "Pagination", "valueCenter.earnRedeem.rewardsAndEarnings.title": "Récompenses et primes**", "valueCenter.earnRedeem.rewardsAndEarnings.description1": "Échangez des récompenses et des primes en argent en magasin ou en ligne. Vous pouvez même les combiner!", "valueCenter.earnRedeem.rewardsAndEarnings.description2": "Lorsque vous aurez accumulé des points, vous pourrez les voir ici et échanger des récompenses lors du paiement.", "valueCenter.earnRedeem.rewardPoints.header": "Récompenses", "valueCenter.earnRedeem.rewardPoints.redeem": "À échanger à la caisse", "valueCenter.earnRedeem.rewardPoints.balance": "Solde de points : {{points}} points", "valueCenter.earnRedeem.rewardPoints.description": "Une récompense équivaut à 100 points.", "valueCenter.earnRedeem.rewardPoints.doubleRewardTitle": "C'est le jour de la double récompense!", "valueCenter.earnRedeem.rewardPoints.doubleRewardText": "Aujourd'hui seulement, échangez le double de la valeur de vos récompenses. Le montant des récompenses ci-dessous est le double de la valeur habituelle.", "valueCenter.earnRedeem.rewardPoints.altText": "Reward Stars Icon", "valueCenter.earnRedeem.rewardTile.header": "Dollarsgap", "valueCenter.earnRedeem.rewardTile.redeemDate": "Période d’échange : {{date1}} au {{date2}}", "valueCenter.earnRedeem.rewardTile.expiresDate": "Date d’expiration {{date}}", "valueCenter.earnRedeem.rewardTile.viewButton": "Voir le code", "valueCenter.earnRedeem.rewardTile.terms": "Voir les conditions", "valueCenter.earnRedeem.rewardTile.gapCash": "Dollarsgap", "valueCenter.earnRedeem.rewardTile.styleCash": "Dollars Mode de BR Entrepôt", "valueCenter.earnRedeem.rewardTile.superCash": "COUPONS SUPER CASH OLD NAVY", "valueCenter.earnRedeem.rewardTile.card": "Récompenses de la carte de crédit Gap Inc.", "valueCenter.earnRedeem.rewardTile.reward": "en récompenses", "valueCenter.earnRedeem.rewardTile.cashApplied": "Les primes en argent seront appliquées lors du paiement.", "valueCenter.earnRedeem.rewardTile.rewardApplied": "Les récompenses seront appliquées lors du paiement.", "valueCenter.earnRedeem.rewardTile.cardApplied": "Utilisez votre carte de crédit Gap Inc. pour échanger vos récompenses lors du paiement.", "valueCenter.earnRedeem.rewardTile.errorMessage": "Nous ne sommes pas en mesure d'appliquer ces économies. Veuillez actualiser la page ou réessayer lors du paiement.", "valueCenter.earnRedeem.rewardTile.BonusCash_altText": "Bonus Cash Icon", "valueCenter.earnRedeem.rewardTile.rewardsStars_altText": "Rewards Icon", "valueCenter.earnRedeem.barcodeModal.reward": "{{cash}} Récompenses", "valueCenter.earnRedeem.barcodeModal.description1": "Pour les échanger en magasin, présentez ce code à la caisse.", "valueCenter.earnRedeem.barcodeModal.description2": "Pour les échanger en ligne, entrez le code promotionnel ci-dessous lors du paiement.", "valueCenter.earnRedeem.barcodeModal.expires": "Date d’expiration {{date}}", "valueCenter.earnRedeem.barcodeModal.specialOffer": "Offre spéciale", "valueCenter.earnRedeem.barcodeModal.altText": "Family of Brands Lo<PERSON>", "valueCenter.earnRedeem.termsModal.title1": "Récompenses, conditions d’utilisation", "valueCenter.earnRedeem.termsModal.title2": "Voir les conditions", "valueCenter.earnRedeem.payItForward.title": "Donnez au suivant**", "valueCenter.earnRedeem.donateRewards.header": "Faites le bien", "valueCenter.earnRedeem.donateRewards.altText": "Helping Hands Icon", "valueCenter.earnRedeem.donateRewards.title": "Offrez vos récompenses", "valueCenter.earnRedeem.donateRewards.description": "Donnez au suivant en offrant vos récompenses à une bonne cause. Nous ferons des dons équivalents à tous les dons jusqu’à concurrence de 1 million de dollars US pour les programmes de récompenses du Canada et des États-Unis combinés.", "valueCenter.earnRedeem.donateRewards.descriptionAT": "For the past 23 years, Athleta has been dedicated to a single aim: igniting the potential of all women and girls. The Power of She Fund puts this mission into action with grant programs that fuel confidence through movement and connection by supporting amazing non-profits like the Women’s Sports Foundation and YWCA.", "valueCenter.earnRedeem.donateRewards.learnMore": "En savoir plus sur les organismes", "valueCenter.earnRedeem.donateRewards.balance": "Solde : {{points}} points ({{cash}} $ en récompenses)", "valueCenter.earnRedeem.donateRewards.balanceDoubleRewards": "Solde : {{points}} points", "valueCenter.earnRedeem.donateRewards.donate": "Faire un don", "valueCenter.earnRedeem.donateRewards.infoDescription": "Une récompense équivaut à 100 points.", "valueCenter.earnRedeem.donateRewards.organizationPlaceholder": "Sélectionnez l'organisation", "valueCenter.earnRedeem.donateRewards.amountPlaceholder": "Sélectionnez le montant", "valueCenter.earnRedeem.donateRewards.amountSelectItem": "{{cash}} ({{points}} points)", "valueCenter.earnRedeem.donateRewards.partnership": "en partenariat avec ", "valueCenter.earnRedeem.donateRewards.supportedBy": "soutenu par notre famille de marques", "valueCenter.earnRedeem.donateRewards.validationAmountError": "Sélectionnez le montant du don.", "valueCenter.earnRedeem.donateRewards.validationOrganizationError": "Sélectionnez un organisme.", "valueCenter.earnRedeem.donateRewards.successMessage1": "Vous avez fait don de {{amount}} points en récompenses à {{charityName}}.", "valueCenter.earnRedeem.donateRewards.successMessage2": "Merci pour votre générosité! Nous suivrons votre exemple en versant un don équivalent.", "valueCenter.earnRedeem.donateRewards.errorMessage": "Impossible de conclure le don. Veuillez actualiser la page et réessayer.", "valueCenter.earnRedeem.donateRewards.lowBalance": "Chaque 1 $ amassé en récompense peut être versé en don.", "valueCenter.earnRedeem.donateRewards.doubleRewardsMessage": "Le solde ci-dessus est disponible pour faire un don. La Journée des récompenses doubles ne s'applique pas au don de vos récompenses.", "valueCenter.earnRedeem.organizationModal.header": "Renseignements sur l’organisme", "valueCenter.earnRedeem.organizationModal.title": "Faites le bien", "valueCenter.earnRedeem.organizationModal.description": "Donnez au suivant en offrant vos récompenses à une bonne cause. Sélectionnez l’un de nos organismes partenaires dans la liste ci-dessous.", "valueCenter.earnRedeem.organizationModal.handsAltText": "Helping Hands Icon", "valueCenter.earnRedeem.earnMorePoints.title": "Cumulez plus de points**", "valueCenter.earnRedeem.birthdayBonus.header": "POINTS EN PRIME", "valueCenter.earnRedeem.birthdayBonus.altText": "Birthday Cupcake", "valueCenter.earnRedeem.birthdayBonus.addButton": "Ajouter la date d’anniversaire", "valueCenter.earnRedeem.birthdayBonus.outside.title": "Prime d’anniversaire", "valueCenter.earnRedeem.birthdayBonus.outside.description": "Obtenez une prime lors de votre anniversaire en {{month}}– C’est notre cadeau!", "valueCenter.earnRedeem.birthdayBonus.during.title": "Joyeux anniversaire!", "valueCenter.earnRedeem.birthdayBonus.during.description": "Obtenez une prime lors de votre anniversaire – C’est notre cadeau! Consultez votre solde de points pour voir votre cadeau.", "valueCenter.earnRedeem.birthdayBonus.missing.title": "Votre anniversaire", "valueCenter.earnRedeem.birthdayBonus.missing.description": "Obtenez une prime lors de votre anniversaire – C’est notre cadeau!", "valueCenter.earnRedeem.birthdayBonusPromo.header": "PRIME D’ANNIVERSAIRE", "valueCenter.earnRedeem.birthdayBonusPromo.promoLabel": "Enjoy {{percentage}}% off from {{brand}} for your birthday!", "valueCenter.earnRedeem.birthdayBonusPromo.description": "Des exclusions s’appliquent. Voir les conditions ci-dessous pour plus de détails.", "valueCenter.earnRedeem.birthdayBonusPromo.viewCode": "Voir le code", "valueCenter.earnRedeem.birthdayBonusPromo.viewTerms": "Voir les conditions", "valueCenter.earnRedeem.birthdayBonusPromo.modal.header": "BIRTHDAY BONUS CODE", "valueCenter.earnRedeem.birthdayBonusPromo.modal.description": "To redeem in store, present this code to the cashier. To redeem online, enter the promo code below in checkout.", "valueCenter.earnRedeem.expiredRewardTile.header": "Points en prime", "valueCenter.earnRedeem.expiredRewardTile.title": "Conversion de primes expirées {{type}} en points", "valueCenter.earnRedeem.expiredRewardTile.expiredDate": "Date d’expiration {{date}}", "valueCenter.earnRedeem.expiredRewardTile.description": "Appuyez pour convertir en  {{points}} points.", "valueCenter.earnRedeem.expiredRewardTile.convertButton": "Convertir en {{points}} points", "valueCenter.earnRedeem.expiredRewardTile.successMessage": "{{points}} points convertis", "valueCenter.earnRedeem.expiredRewardTile.errorAlreadyConverted": "Les primes en argent ont déjà été converties. Vérifiez votre solde de points pour en savoir plus.", "valueCenter.earnRedeem.specialOffer.header": "Exclusivité pour les membres", "valueCenter.earnRedeem.specialOffer.title": "Offre spéciale", "valueCenter.earnRedeem.specialOffer.availableDate": "Du {{date1}} au {{date2}}", "valueCenter.earnRedeem.specialOffer.description1": "Offre de test de code-barres", "valueCenter.earnRedeem.specialOffer.description2": "Des exclusions s’appliquent. Voir les conditions ci-dessous pour plus de détails.", "valueCenter.earnRedeem.specialOffer.viewButton": "Voir le code", "valueCenter.earnRedeem.specialOffer.terms": "Voir les conditions", "valueCenter.earnRedeem.specialOffer.offerApplied": "L'offre spéciale sera appliquée à la caisse.", "valueCenter.earnRedeem.specialOffer.altText": "Special Offer <PERSON><PERSON>", "valueCenter.earnRedeem.familyBrands.header": "POINTS EN PRIME", "valueCenter.earnRedeem.familyBrands.header1": "LEVEL UP TO UNLOCK", "valueCenter.earnRedeem.familyBrands.bagAltText": "Family of Brands Shopping Bag Icon", "valueCenter.earnRedeem.familyBrands.logosAltText": "Family of Brands Lo<PERSON>", "valueCenter.earnRedeem.familyBrands.title": "Prime multimarque", "valueCenter.earnRedeem.familyBrands.description1": "Obtenez jusqu’à 2000 points en prime par année lorsque vous achetez chez deux marques de Gap Inc. ou plus.", "valueCenter.earnRedeem.familyBrands.description2": "Félicitations! Vous avez obtenu 500 points en prime puisque vous avez fait des achats chez deux de nos marques! Continuez sur votre lancée!", "valueCenter.earnRedeem.familyBrands.description3": "Félicitations! Vous avez obtenu 1000 points en prime puisque vous avez fait des achats chez trois de nos marques! Continuez sur votre lancée!", "valueCenter.earnRedeem.familyBrands.description4": "Félicitations! Vous avez obtenu 2000 points en prime puisque vous avez fait des achats chez quatre de nos marques!", "valueCenter.earnRedeem.familyBrands.description5_part_1": "Core Member, ", "valueCenter.earnRedeem.familyBrands.description5_part_2": "level up to Enthusiast ", "valueCenter.earnRedeem.familyBrands.description5_part_3": "to unlock up to ", "valueCenter.earnRedeem.familyBrands.description5_part_4": "2,000 bonus points ", "valueCenter.earnRedeem.familyBrands.description5_part_5": "annually when you shop at 2 or more of our Gap Inc. brands.", "valueCenter.earnRedeem.familyBrands.brand2": "2 marques = 500 points", "valueCenter.earnRedeem.familyBrands.brand3": "3 marques = 1,000 points", "valueCenter.earnRedeem.familyBrands.brand4": "4 marques = 2,000 points", "valueCenter.earnRedeem.familyBrands.brandChecks.hasGP_true": "You have shopped at Gap", "valueCenter.earnRedeem.familyBrands.brandChecks.hasGP_false": "You have not shopped at Gap", "valueCenter.earnRedeem.familyBrands.brandChecks.hasBR_true": "You have shopped at Banana Republic", "valueCenter.earnRedeem.familyBrands.brandChecks.hasBR_false": "You have not shopped at Banana Republic", "valueCenter.earnRedeem.familyBrands.brandChecks.hasON_true": "You have shopped at Old Navy", "valueCenter.earnRedeem.familyBrands.brandChecks.hasON_false": "You have not shopped at Old Navy", "valueCenter.earnRedeem.familyBrands.brandChecks.hasAT_true": "You have shopped at Athleta", "valueCenter.earnRedeem.familyBrands.brandChecks.hasAT_false": "You have not shopped at Athleta", "valueCenter.earnRedeem.convertCashPoints.header": "POINTS EN PRIME", "valueCenter.earnRedeem.convertCashPoints.altText": "Bonus Cash Icon", "valueCenter.earnRedeem.convertCashPoints.title": "Convertir des primes en argent expirées en points", "valueCenter.earnRedeem.convertCashPoints.description": "Convertissez des coupons Super Cash, des Dollarsgap ou des Dollars Mode inutilisés en points. Ajoutez le code associé aux primes en argent inutilisées dans un délai de 30 jours à compter de la date d’expiration pour faire la conversion.", "valueCenter.earnRedeem.convertCashPoints.placeholder": "Code d’échange", "valueCenter.earnRedeem.convertCashPoints.convertButton": "Convertir", "valueCenter.earnRedeem.convertCashPoints.validationError": "Entrez un code à 12 chiffres", "valueCenter.earnRedeem.convertCashPoints.successMessage": "{{points}} points obtenus après la conversion des primes en argent.", "valueCenter.earnRedeem.convertCashPoints.errorMessage": "Il est impossible de convertir cette prime en argent. Veuillez actualiser la page et réessayer.", "valueCenter.earnRedeem.wantEarnMore.header": "POINTS EN PRIME", "valueCenter.earnRedeem.wantEarnMore.title": "Vous souhaitez obtenir plus de points?", "valueCenter.earnRedeem.wantEarnMore.description": "Demandez une carte de crédit Récompenses {{brand}} et profitez de tous les avantages du statut de membre Enthousiaste suivant votre approbation. De plus, obtenez {{points}} points pour chaque dollar dépensé au sein de notre famille de marques avec votre nouvelle carte!**", "valueCenter.earnRedeem.wantEarnMore.applyButton": "Demander la carte maintenant", "valueCenter.earnRedeem.wantEarnMore.applyCardLinkText_gp": " Demandez une carte de crédit Récompenses Gap Good", "valueCenter.earnRedeem.wantEarnMore.applyCardLinkText_on": " Demandez une carte de crédit Récompenses Navyist", "valueCenter.earnRedeem.wantEarnMore.applyCardLinkText_br": " Demandez une carte de crédit Récompenses Banana Republic ", "valueCenter.earnRedeem.wantEarnMore.applyCardLinkText_at": " Demandez une carte de crédit Récompenses Athleta", "valueCenter.earnRedeem.wantEarnMore.applyCardLinkText_gpfs": " Demandez une carte de crédit Récompenses Gap Good", "valueCenter.earnRedeem.wantEarnMore.applyCardLinkText_brfs": " Demandez une carte de crédit Récompenses Banana Republic", "valueCenter.earnRedeem.wantEarnMore.applyCardEncourageText_CORE": " et profitez de tous les avantages du statut de membre Enthousiaste suivant votre approbation. De plus, obtenez {{points}} points pour chaque dollar dépensé au sein de notre famille de marques avec votre nouvelle carte!**", "valueCenter.earnRedeem.wantEarnMore.applyCardEncourageText_ENTHUSIAST": " et obtenez {{points}} points pour chaque dollar dépensé au sein de notre famille de marques avec votre nouvelle carte!**", "valueCenter.earnRedeem.wantEarnMore.applyCardEncourageText_ICON": " et obtenez {{points}} points pour chaque dollar dépensé au sein de notre famille de marques avec votre nouvelle carte!**", "valueCenter.earnRedeem.shopQuarterly.header": "POINTS EN PRIME", "valueCenter.earnRedeem.shopQuarterly.header1": "LEVEL UP TO UNLOCK", "valueCenter.earnRedeem.shopQuarterly.altText": "Quarterly Bonus Icon", "valueCenter.earnRedeem.shopQuarterly.title": "Magasinez chaque trimestre", "valueCenter.earnRedeem.shopQuarterly.description1": "Félicitations! Vous avez droit à une prime puisque vous avez fait un achat au sein de notre famille de marques pendant le trimestre.", "valueCenter.earnRedeem.shopQuarterly.description2": "Obtenez des points en prime chaque trimestre où vous faites un achat au sein de notre famille de marques.", "valueCenter.earnRedeem.shopQuarterly.description3": "<PERSON><PERSON><PERSON><PERSON> ", "valueCenter.earnRedeem.shopQuarterly.description4": "lorsque vous faites un achat au sein de notre famille de marques pendant le trimestre.", "valueCenter.earnRedeem.shopQuarterly.corePoints": "125 points en prime ", "valueCenter.earnRedeem.shopQuarterly.enthusiastPoints": "250 points en prime ", "valueCenter.earnRedeem.shopQuarterly.iconPoints": "500 points en prime ", "valueCenter.earnRedeem.shopQuarterly.core": "Membre Classique = 125 points", "valueCenter.earnRedeem.shopQuarterly.enthusiast": "Membre Enthousiaste = 250 points", "valueCenter.earnRedeem.shopQuarterly.icon": "Membre Élite = 500 points", "valueCenter.earnRedeem.shopQuarterly.linkActivity": "Suivre vos points", "valueCenter.earnRedeem.shopQuarterly.description5_part_1": "Core Member, ", "valueCenter.earnRedeem.shopQuarterly.description5_part_2": "level up to Enthusiast ", "valueCenter.earnRedeem.shopQuarterly.description5_part_3": "to get ", "valueCenter.earnRedeem.shopQuarterly.description5_part_4": "250 bonus points ", "valueCenter.earnRedeem.shopQuarterly.description5_part_5": "when you make a purchase at our family of brands starting next quarter.", "valueCenter.unlockMoreBenefits.header": "Unlock More Benefits", "valueCenter.footer.text1": "Cartes de crédit Gap Inc. sous réserve d'approbation de crédit. ", "valueCenter.footer.text2": "Voir ", "valueCenter.footer.text3": "les détails", "valueCenter.footer.text4": " et programme de récompenses ", "valueCenter.footer.text5": "termes et conditions", "valueCenter.errors.infoUnavailable": "En raison d’un problème technique, certaines données ne sont pas accessibles. Veuillez réessayer plus tard.", "accountSecurity.header": "Sûreté du compte", "accountSecurity.mainDescription": "Ces appareils reconnus ont actuellement une session ouverte à votre compte Gap, Old Navy, Banana Republic ou Athleta.", "accountSecurity.footerDescription": "Votre visite actuelle n’est pas compromise; cep<PERSON>ant, à votre prochaine visite, nous vous demanderons d’ouvrir une session avec vos identifiants.", "accountSecurity.removeDevices": "Retrait d’appareils", "accountSecurity.signout": "Se déconnecter", "accountSecurity.lastAccessed": "<PERSON><PERSON> acc<PERSON>", "accountSecurity.webBrowser": "Navigateur web", "accountSecurity.mobileApp": "Application mobile", "accountSecurity.mac": "<PERSON>", "accountSecurity.linux": "Linux", "accountSecurity.windows": "Windows", "accountSecurity.iosDevice": "Appareil iOS", "accountSecurity.androidDevice": "Appareil Android", "accountSecurity.today": "<PERSON>jou<PERSON><PERSON>hui", "accountSecurity.error1": "Une erreur technique est survenue durant le chargement de vos appareils reconnus. Veuillez actualiser la page et réessayer.", "accountSecurity.error2": "Une erreur technique est survenue. Veuillez actualiser la page et réessayer de nouveau pour retirer vos appareils.", "accountSecurity.summaryDescription": "End all sessions of any saved signed-in visits to your Gap, Old Navy, Banana Republic, and Athleta account.", "accountSecurity.endAllSessions": "End All Sessions", "accountSecurity.emptyStateDescription": "Les appareils utilisés au cours des 72 dernières heures pour ouvrir une session, y compris celui-ci, conserveront leur session ouverte jusqu’à un maximum de 3 jours.", "accountSecurity.emptyStateInfo": "Les appareils utilisés au cours des 72 dernières heures pour ouvrir une session, y compris celui-ci, conserveront leur session ouverte jusqu’à un maximum de 3 jours.", "accountSecurity.unknown": "Inconnue", "wismo.noOrders": "Il n’y a pas de commandes récentes à suivre.", "wismo.wismoFetchError": "L’historique de vos commandes n’est pas accessible en ce moment. Veuillez réessayer plus tard.", "wismo.wismoCard.myOrdersText": "<PERSON><PERSON> commandes", "wismo.wismoTile.estimatedDeliveryDate": "Livraison estimée d’ici le ", "wismo.wismoTile.estimatedShipDate": "Date liv. est.", "wismo.wismoTile.trackPackage": "Suivre un colis", "wismo.wismoTile.orderDetails": "<PERSON><PERSON><PERSON> de la commande", "wismo.wismoTile.details": "Détails", "wismo.wismoTile.imageNotAvailable": "Image non disponible", "wismo.deviverStattus.backOrdered": "Au moins un de vos articles est en rupture de stock.", "wismo.deviverStattus.delayed": "Un colis a été retardé.", "wismo.deviverStattus.delivered": "Un colis a été livré!", "wismo.deviverStattus.orderPlaced": "Vous avez réussi! Nous avons reçu votre commande", "wismo.deviverStattus.orderReceived": "Vous avez réussi! Nous avons reçu votre commande.", "wismo.deviverStattus.outForDelivery": "Un colis est prêt à être expédié!", "wismo.deviverStattus.preparingForShipment": "Un colis est presque prêt à être expédié!", "wismo.deviverStattus.shipped": "Un colis a été expédié!", "wismo.deviverStattus.returnRequested": "Votre retour de marchandise est en cours.", "wismo.deviverStattus.returned": "Votre retour a été traité.", "wismo.deviverStattus.cancelled": "Votre livraison a été annulée.", "wismo.deviverStattus.cancelled-customer": "Votre livraison a été annulée.", "wismo.deviverStattus.cancelled-item": "Votre demande d’annulation a été traitée.", "wismo.deviverStattus.cancelled-card": "Votre livraison a été annulée.", "wismo.deviverStattus.WORKING_ON_YOUR_ORDER": "Un colis est presque prêt à être expédié!", "wismo.deviverStattus.CANCELED_ITEM_UNAVALIABLE": "Votre livraison a été annulée.", "wismo.deviverStattus.BOPIS_RETURNED": "Nous avons reçu la marchandise que vous avez retournée.", "wismo.deviverStattus.ORDER_READY": "Votre commande est prête à être récupérée!", "wismo.deviverStattus.REFUNDED": "Votre remboursement a été effectué.", "wismo.deviverStattus.statusUnavailable": "Revérifiez l’état de votre commande plus tard.", "wismo.deviverStattus.PICKED_UP": "<PERSON><PERSON><PERSON>", "wismo.deviverStattus.PICK_UP": "<PERSON><PERSON><PERSON>", "wismo.deviverStattus.VERIFIED": " Vous avez réussi! Nous avons reçu votre commande.", "wismo.deviverStattus.AWAITING_CANCELLATION": "Votre demande d’annulation a été traitée.", "wismo.deviverStattus.workingOnYourOrder": "Nous préparons votre commande pour une cueillette.", "wismo.deviverStattus.cancelledUnavailable": "Votre livraison a été annulée.", "wismo.deviverStattus.orderReady": "Votre commande est prête à être récupérée!", "wismo.deviverStattus.refunded": "Votre remboursement a été effectué.", "wismo.deviverStattus.pickUp": "<PERSON><PERSON><PERSON>", "wismo.deviverStattus.verified": " Vous avez réussi! Nous avons reçu votre commande.", "wismo.deviverStattus.awaitingCancellation": "Votre demande d’annulation a été traitée.", "wismo.deviverStattus.cancelledItem": "Votre demande d’annulation a été traitée.", "wismo.deviverStattus.cancelledCard": "Votre livraison a été annulée.", "wismo.deviverStattus.cancelledCustomer": "Un ou plusieurs articles de votre commande ont été annulés.", "wismo.deviverStattus.bopisReturned": "Nous avons reçu la marchandise que vous avez retournée.", "wismo.deviverStattus.inProgress": "Nous préparons votre commande pour une cueillette", "wismo.deviverStattus.inTransit": "Un colis est en route.", "wismo.deviverStattus.undeliverable": "Il y a eu un problème avec la livraison de votre colis.", "wismo.deviverStattus.exception": "Revenez plus tard pour une mise à jour de votre commande.", "wismo.deviverStattus.justShipped": "Un colis a été expédié !", "wismo.deviverStattus.returnInitiated": "Votre retour est en cours.", "wismo.deviverStattus.packageUpdate": "Vérifiez l'état de votre colis !", "wismo.deviverStattus.purchasedInStore": "Marchandise achetée en magasin", "wismo.omniDeliveryStatus.backOrdered": "Commande en souffrance", "wismo.omniDeliveryStatus.delayed": "Retardée", "wismo.omniDeliveryStatus.delivered": "Livrée", "wismo.omniDeliveryStatus.orderPlaced": "Commande passée", "wismo.omniDeliveryStatus.orderReceived": "Commande passée", "wismo.omniDeliveryStatus.outForDelivery": "<PERSON><PERSON><PERSON>", "wismo.omniDeliveryStatus.preparingForShipment": "Préparation de la livraison", "wismo.omniDeliveryStatus.shipped": "Expédiée", "wismo.omniDeliveryStatus.returnRequested": "Retour de marchandise en cours", "wismo.omniDeliveryStatus.returned": "Retournée", "wismo.omniDeliveryStatus.cancelled": "Annulation de la livraison", "wismo.omniDeliveryStatus.cancelled-customer": "Annulation de la livraison", "wismo.omniDeliveryStatus.cancelled-item": "En attente d’annulation", "wismo.omniDeliveryStatus.cancelled-card": "Annulation de la livraison", "wismo.omniDeliveryStatus.WORKING_ON_YOUR_ORDER": "Préparation de la livraison", "wismo.omniDeliveryStatus.CANCELED_ITEM_UNAVALIABLE": "Annulation de la livraison", "wismo.omniDeliveryStatus.BOPIS_RETURNED": "Réception d’un retour de marchandise", "wismo.omniDeliveryStatus.ORDER_READY": "Prête pour le ramassage", "wismo.omniDeliveryStatus.REFUNDED": "Remboursement terminé", "wismo.omniDeliveryStatus.statusUnavailable": "État non disponible", "wismo.omniDeliveryStatus.PICKED_UP": "<PERSON><PERSON><PERSON>", "wismo.omniDeliveryStatus.PICK_UP": "<PERSON><PERSON><PERSON>", "wismo.omniDeliveryStatus.VERIFIED": "Commande passée", "wismo.omniDeliveryStatus.AWAITING_CANCELLATION": "En attente d’annulation", "wismo.omniDeliveryStatus.workingOnYourOrder": "Being Prepared", "wismo.omniDeliveryStatus.cancelledUnavailable": "Annulation de la livraison", "wismo.omniDeliveryStatus.orderReady": "Prête pour le ramassage", "wismo.omniDeliveryStatus.refunded": "Remboursement terminé", "wismo.omniDeliveryStatus.pickUp": "<PERSON><PERSON><PERSON>", "wismo.omniDeliveryStatus.verified": "Commande passée", "wismo.omniDeliveryStatus.awaitingCancellation": "En attente d’annulation", "wismo.omniDeliveryStatus.cancelledItem": "En attente d’annulation", "wismo.omniDeliveryStatus.cancelledCard": "Annulation de la livraison", "wismo.omniDeliveryStatus.cancelledCustomer": "Article(s) annulé(s)", "wismo.omniDeliveryStatus.bopisReturned": "Réception d’un retour de marchandise", "wismo.omniDeliveryStatus.inProgress": "Being Prepared", "wismo.omniDeliveryStatus.inTransit": "Expédiée", "wismo.omniDeliveryStatus.undeliverable": "Delivery Issue", "wismo.omniDeliveryStatus.exception": "État non disponible", "wismo.omniDeliveryStatus.justShipped": "Expédiée", "wismo.omniDeliveryStatus.returnInitiated": "Retour de marchandise en cours", "wismo.omniDeliveryStatus.packageUpdate": "Check the status of your package!", "wismo.omniDeliveryStatus.purchasedInStore": "Marchandise achetée en magasin", "wismo.bopisStatusText.returned": "Retournée", "wismo.bopisStatusText.orderReady": "À récupérer avant le {{dateTime}}", "wismo.bopisStatusText.refunded": "Remboursée", "wismo.bopisStatusText.pickedUp": "Votre colis a été ramass<PERSON>."}