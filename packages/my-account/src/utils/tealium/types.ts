type ViewEventOpts = Omit<LinkEventOpts, 'eventName'>;
type ViewEventPayload = Omit<LinkEventPayload, 'event_name'>;

type LinkEventOpts = {
  brandCode: number;
  brandTealium: string;
  eventName: string;
  page?: string;
};

type LinkEvent = {
  data: Promise<{
    business_unit_id: number;
    channel: string;
    event_name?: string;
    page_name: string;
    page_type: string;
  }>;
  name: string;
};

type LinkEventPayload = {
  business_unit_id: number;
  channel: string;
  event_name: string;
  page_name: string;
  page_type: string;
};

type DataLayerView = {
  data: Promise<ViewEventPayload>;
  name: string;
};

type QuickAddLoadStartOpts = {
  action: string;
  brandCode: string;
  brandTealium: string;
  eventName: string;
  pageType: string;
  productBrand: string | string[];
  productCategory: string;
  productDropship: string | string[];
  productId: string | string[];
  productName: string | string[];
  productSellerId: string | number;
  productSellerName: string;
  product_cc_id: string | string[];
  product_inventory_status: string;
  product_page_type: string | string[];
  recognition_status: string;
};

type QuickAddLoadStartPayload = {
  event_name: string;
  page_name: string;
  page_type: string;
  product_brand: string | string[];
  product_category: string;
  product_cc_id: string | string[];
  product_dropship: string | string[];
  product_id: string | string[];
  product_inventory_status: string;
  product_name: string | string[];
  product_seller_id: string | number;
  product_seller_name: string;
};

type QuickAddToCartOpts = {
  action: string;
  brandCode: string;
  brandTealium: string;
  eventName: string;
  pageType: string;
  productBrand: string | string[];
  productCategory: string;
  productDropship: string | string[];
  productGrossMerchandise: string | string[];
  productId: string | string[];
  productMarkdownAmount: string | string[];
  productName: string | string[];
  productSellerId: string | number;
  productSellerName: string;
  product_cc_id: string | string[];
  product_inventory_status: string;
  product_page_type: string | string[];
  product_price: string | string[];
  product_quantity: string | string[];
  product_selected_size: string | string[];
  recognition_status: string;
};

type QuickAddToCartPayload = {
  event_name: string;
  page_name: string;
  page_type: string;
  product_brand: string | string[];
  product_category: string;
  product_cc_id: string | string[];
  product_dropship: string | string[];
  product_gross_merchandise: string | string[];
  product_id: string | string[];
  product_inventory_status: string;
  product_markdown_amount: string | string[];
  product_name: string | string[];
  product_price: string | string[];
  product_quantity: string | string[];
  product_selected_size: string | string[];
  product_seller_id: string | number;
  product_seller_name: string;
};

type TriageViewOpts = {
  brandCode: number;
  brandTealium: string;
  event_name?: string;
  page: string;
  password_entry: boolean;
  profile_submit: boolean;
};

type OrderSelfServiceOpts = {
  address_change_option_present?: boolean;
  brandCode: number;
  brandTealium: string;
  cancel_order_and_address_change_present?: boolean;
  cancel_order_option_present?: boolean;
  eventName?: string;
  page?: string | undefined;
  ss_address_change?: string;
  ss_cancel_order?: string;
  ss_cancel_order_confirmation_number?: string;
};

type ExtendedSelfServicesOpts = {
  address_change_option_present?: boolean;
  cancel_order_and_address_change_present?: boolean;
  cancel_order_option_present?: boolean;
  ss_address_change?: string;
  ss_cancel_order?: string;
  ss_cancel_order_confirmation_number?: string;
};

type ExtendValueCenterOpts = {
  specialOffersPresent?: boolean;
  special_offers_present?: boolean;
  value_center_interaction?: string;
  vc_error_location?: string;
};

export type {
  LinkEvent,
  DataLayerView,
  LinkEventOpts,
  ViewEventOpts,
  TriageViewOpts,
  LinkEventPayload,
  ViewEventPayload,
  QuickAddLoadStartOpts,
  QuickAddLoadStartPayload,
  QuickAddToCartOpts,
  QuickAddToCartPayload,
  OrderSelfServiceOpts,
  ExtendValueCenterOpts,
  ExtendedSelfServicesOpts,
};
