import * as utils from '../valueCenterUtils';
import type { Localize } from '../../../components/value-center/types';
import coreLightBlue from '../../../assets/value-center/membership_core_light_blue.svg';
import coreDarkBlue from '../../../assets/value-center/membership_core_dark_blue.svg';
import enthusiastGrey from '../../../assets/value-center/membership_enthusiast_grey.svg';
import enthusiastLightBlue from '../../../assets/value-center/membership_enthusiast_light_blue.svg';
import enthusiastDarkBlue from '../../../assets/value-center/membership_enthusiast_dark_blue.svg';
import iconGrey from '../../../assets/value-center/membership_icon_grey.svg';
import iconLightBlue from '../../../assets/value-center/membership_icon_light_blue.svg';

describe('valueCenterUtils functions', () => {
  let localize: Localize;
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
    localize = jest.fn(str => {
      return str;
    });
  });

  it('test function: getFooterLinks()', () => {
    const results = utils.getFooterLinks();
    expect(results).toEqual({
      us: {
        gp: {
          detail: 'https://www.gap.com/customerService/info.do?cid=1099008',
          termCondition: '/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html?v=2',
        },
        on: {
          detail: 'https://oldnavy.gap.com/customerService/info.do?cid=1095422',
          termCondition: '/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html?v=2',
        },
        br: {
          detail: 'https://bananarepublic.gap.com/customerService/info.do?cid=1098875',
          termCondition: '/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html?v=2',
        },
        at: {
          detail: 'https://athleta.gap.com/browse/info.do?cid=1098761',
          termCondition: '/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html?v=2',
        },
        gpfs: {
          detail: 'https://www.gapfactory.com/customerService/info.do?cid=1099133',
          termCondition: '/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html?v=2',
        },
        brfs: {
          detail: 'https://bananarepublicfactory.gapfactory.com/customerService/info.do?cid=1098825',
          termCondition: '/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html?v=2',
        },
      },
      ca: {
        gp: {
          detail: 'https://www.gapcanada.ca/customerService/info.do?cid=1099008&locale=',
          termCondition: 'https://www.gapcanada.ca/customerService/info.do?cid=1099008&terms-conditions=1&locale=',
        },
        on: {
          detail: 'https://oldnavy.gapcanada.ca/customerService/info.do?cid=1095422&locale=',
          termCondition: 'https://oldnavy.gapcanada.ca/customerService/info.do?cid=1095422&terms-conditions=1&locale=',
        },
        br: {
          detail: 'https://bananarepublic.gapcanada.ca/browse/info.do?cid=1182998&locale=',
          termCondition: 'https://bananarepublic.gapcanada.ca/browse/info.do?cid=1182998&terms-conditions=1&locale=',
        },
        at: {
          detail: 'https://athleta.gapcanada.ca/browse/info.do?cid=1184799&locale=',
          termCondition: 'https://athleta.gapcanada.ca/browse/info.do?cid=1184799&terms-conditions=1&locale=',
        },
        brfs: {
          detail: 'https://bananarepublicfactory.gapfactory.ca/customerService/info.do?cid=1098825&locale=',
          termCondition: 'https://bananarepublicfactory.gapfactory.ca/customerService/info.do?cid=1098825&terms-conditions=1&locale=',
        },
        gpfs: {
          detail: 'https://www.gapfactory.ca/customerService/info.do?cid=1099133&locale=',
          termCondition: 'https://www.gapfactory.ca/customerService/info.do?cid=1099136&locale=',
        },
      },
    });
  });

  it('test function: linkToBarclays() gap', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-www.stage.gaptechol.com',
        href: 'secure-www.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays();
    expect(window.location.href).toContain(
      '/my-account/sign-in?creditOffer=barclays&sitecode=GPSSVALUMD&retUrl=https://secure-www.stage.gaptechol.com/my-account/value-center'
    );
  });

  it('test function: linkToBarclays() GPFS', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-www.stage.factory-gaptechol.com',
        href: 'secure-www.stage.factory-gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('gpfs');
    expect(window.location.href).toContain(
      '/my-account/sign-in?creditOffer=barclays&sitecode=GPFSVALUMD&retUrl=https://secure-www.stage.factory-gaptechol.com/my-account/value-center'
    );
  });

  it('test function: linkToBarclays() BR', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-brol.stage.gaptechol.com',
        href: 'secure-brol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('br');
    expect(window.location.href).toContain(
      '/my-account/sign-in?creditOffer=barclays&sitecode=BRSSVALUMD&retUrl=https://secure-brol.stage.gaptechol.com/my-account/value-center'
    );
  });

  it('test function: linkToBarclays() BRFS', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-brfol.stage.factory-gaptechol.com',
        href: 'secure-brfol.stage.factory-gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('br');
    expect(window.location.href).toContain(
      '/my-account/sign-in?creditOffer=barclays&sitecode=BRSSVALUMD&retUrl=https://secure-brfol.stage.factory-gaptechol.com/my-account/value-center'
    );
  });

  it('test function: linkToBarclays() ON', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-onol.stage.gaptechol.com',
        href: 'secure-onol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('br');
    expect(window.location.href).toContain(
      '/my-account/sign-in?creditOffer=barclays&sitecode=BRSSVALUMD&retUrl=https://secure-onol.stage.gaptechol.com/my-account/value-center'
    );
  });

  it('test function: linkToBarclays() AT', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-atol.stage.gaptechol.com',
        href: 'secure-atol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('at');
    expect(window.location.href).toContain(
      '/my-account/sign-in?creditOffer=barclays&sitecode=ATSSVCMDE&retUrl=https://secure-atol.stage.gaptechol.com/my-account/value-center'
    );
  });

  it('return correct sideCode on gap desktop for earn and redeem tab', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-www.stage.gaptechol.com',
        href: 'secure-www.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('gp', 'earn-and-redeem');
    expect(window.location.href).toContain('sitecode=GPSSVALUED&');
  });

  it('return correct sideCode on GPFS desktop for earn and redeem tab', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-www.stage.factory-gaptechol.com',
        href: 'secure-www.stage.factory-gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('gpfs', 'earn-and-redeem');
    expect(window.location.href).toInclude('sitecode=GPFSVALUED&');
  });

  it('return correct sideCode on BR desktop for earn and redeem tab', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-brol.stage.gaptechol.com',
        href: 'secure-brol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('br', 'earn-and-redeem');
    expect(window.location.href).toInclude('sitecode=BRSSVALUED&');
  });

  it('return correct sideCode on BRFS desktop for earn and redeem tab', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-brfol.stage.factory-gaptechol.com',
        href: 'secure-brfol.stage.factory-gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('brfs', 'earn-and-redeem');
    expect(window.location.href).toInclude('sitecode=BRFSVALUED&');
  });

  it('return correct sideCode on ON desktop for earn and redeem tab', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-onol.stage.gaptechol.com',
        href: 'secure-onol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('on', 'earn-and-redeem');
    expect(window.location.href).toInclude('sitecode=ONVALUED&');
  });

  it('return correct sideCode on AT desktop for earn and redeem tab', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-atol.stage.gaptechol.com',
        href: 'secure-atol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('at', 'earn-and-redeem');
    expect(window.location.href).toInclude('sitecode=ATSSVCEDE&');
  });

  it('return correct sideCode on gap desktop for membership', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-www.stage.gaptechol.com',
        href: 'secure-www.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('gp', 'membership');
    expect(window.location.href).toContain('sitecode=GPSSVALUMD&');
  });

  it('return correct sideCode on GPFS desktop for membership', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-www.stage.factory-gaptechol.com',
        href: 'secure-www.stage.factory-gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('gpfs', 'membership');
    expect(window.location.href).toInclude('sitecode=GPFSVALUMD&');
  });

  it('return correct sideCode on BR desktop for membership', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-brol.stage.gaptechol.com',
        href: 'secure-brol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('br', 'membership');
    expect(window.location.href).toInclude('sitecode=BRSSVALUMD&');
  });

  it('return correct sideCode on BRFS desktop for membership', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-brfol.stage.factory-gaptechol.com',
        href: 'secure-brfol.stage.factory-gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('brfs', 'membership');
    expect(window.location.href).toInclude('sitecode=BRFSVALUMD&');
  });

  it('return correct sideCode on ON desktop for membership', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-onol.stage.gaptechol.com',
        href: 'secure-onol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('on', 'membership');
    expect(window.location.href).toInclude('sitecode=ONVALUMD&');
  });

  it('return correct sideCode on AT desktop for membership', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-atol.stage.gaptechol.com',
        href: 'secure-atol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    utils.linkToBarclays('at', 'membership');
    expect(window.location.href).toInclude('sitecode=ATSSVCMDE&');
  });

  it('return correct sideCode on gap mobile for earn and redeem tab', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-www.stage.gaptechol.com',
        href: 'secure-www.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    window.innerWidth = 400;
    utils.linkToBarclays('gp', 'earn-and-redeem');
    expect(window.location.href).toInclude('sitecode=GPSSVALUEM&');
  });

  it('return correct sideCode on GPFS mobile for earn and redeem tab', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-www.stage.factory-gaptechol.com',
        href: 'secure-www.stage.factory-gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    window.innerWidth = 400;

    utils.linkToBarclays('gpfs', 'earn-and-redeem');
    expect(window.location.href).toInclude('sitecode=GPFSVALUEM&');
  });

  it('return correct sideCode on BR mobile for earn and redeem tab', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-brol.stage.gaptechol.com',
        href: 'secure-brol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    window.innerWidth = 400;

    utils.linkToBarclays('br', 'earn-and-redeem');
    expect(window.location.href).toInclude('sitecode=BRSSVALUEM&');
  });

  it('return correct sideCode on BRFS mobile for earn and redeem tab', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-brfol.stage.factory-gaptechol.com',
        href: 'secure-brfol.stage.factory-gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    window.innerWidth = 400;

    utils.linkToBarclays('brfs', 'earn-and-redeem');
    expect(window.location.href).toInclude('sitecode=BRFSVALUEM&');
  });

  it('return correct sideCode on ON mobile for earn and redeem tab', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-onol.stage.gaptechol.com',
        href: 'secure-onol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    window.innerWidth = 400;

    utils.linkToBarclays('on', 'earn-and-redeem');
    expect(window.location.href).toInclude('sitecode=ONVALUEM&');
  });

  it('return correct sideCode on AT mobile for earn and redeem tab', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-atol.stage.gaptechol.com',
        href: 'secure-atol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    window.innerWidth = 400;

    utils.linkToBarclays('at', 'earn-and-redeem');
    expect(window.location.href).toInclude('sitecode=ATSSVCEME&');
  });

  it('return correct sideCode on gap mobile for membership', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-www.stage.gaptechol.com',
        href: 'secure-www.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    window.innerWidth = 400;
    utils.linkToBarclays('gp', 'membership');
    expect(window.location.href).toInclude('sitecode=GPSSVALUMM&');
  });

  it('return correct sideCode on GPFS mobile for membership', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-www.stage.factory-gaptechol.com',
        href: 'secure-www.stage.factory-gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    window.innerWidth = 400;

    utils.linkToBarclays('gpfs', 'membership');
    expect(window.location.href).toInclude('sitecode=GPFSVALUMM&');
  });

  it('return correct sideCode on BR mobile for membership', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-brol.stage.gaptechol.com',
        href: 'secure-brol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    window.innerWidth = 400;

    utils.linkToBarclays('br', 'membership');
    expect(window.location.href).toInclude('sitecode=BRSSVALUMM&');
  });

  it('return correct sideCode on BRFS mobile for membership', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-brfol.stage.factory-gaptechol.com',
        href: 'secure-brfol.stage.factory-gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    window.innerWidth = 400;

    utils.linkToBarclays('brfs', 'membership');
    expect(window.location.href).toInclude('sitecode=BRFSVALUMM&');
  });

  it('return correct sideCode on ON mobile for membership', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-onol.stage.gaptechol.com',
        href: 'secure-onol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    window.innerWidth = 400;

    utils.linkToBarclays('on', 'membership');
    expect(window.location.href).toInclude('sitecode=ONVALUMM&');
  });

  it('return correct sideCode on AT mobile for membership', () => {
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-atol.stage.gaptechol.com',
        href: 'secure-atol.stage.gaptechol.com/my-account/value-center',
        pathname: '/my-account/value-center',
      },
      writable: true,
    });
    window.innerWidth = 400;

    utils.linkToBarclays('at', 'membership');
    expect(window.location.href).toInclude('sitecode=ATSSVCMME&');
  });

  it('test function: getStartShoppingHeader()', () => {
    const results1 = utils.getStartShoppingHeader('CORE', 10, localize);
    const results2 = utils.getStartShoppingHeader('CORE', 0, localize);
    const results3 = utils.getStartShoppingHeader('ENTHUSIAST', 10, localize);
    const results4 = utils.getStartShoppingHeader('ICON', 10, localize);
    const results5 = utils.getStartShoppingHeader('', 10, localize);
    expect(results1).toEqual('valueCenter.membership.startShopping.header_core');
    expect(results2).toEqual('valueCenter.membership.startShopping.header1');
    expect(results3).toEqual('valueCenter.membership.startShopping.header_enthusiast');
    expect(results4).toEqual('valueCenter.membership.startShopping.congratulationsHeader_icon');
    expect(results5).toEqual('valueCenter.membership.startShopping.header1');
  });

  it('test function: getStartShoppingFooterText()', () => {
    const results1 = utils.getStartShoppingFooterText('CORE', false, '', localize);
    const results2 = utils.getStartShoppingFooterText('ENTHUSIAST', true, '', localize);
    const results3 = utils.getStartShoppingFooterText('ENTHUSIAST', false, '', localize);
    const results4 = utils.getStartShoppingFooterText('ICON', true, '', localize);
    const results5 = utils.getStartShoppingFooterText('', false, '', localize);
    expect(results1).toEqual('valueCenter.membership.startShopping.footer_core');
    expect(results2).toEqual('valueCenter.membership.startShopping.footer_enthusiast_cardHolder');
    expect(results3).toEqual('valueCenter.membership.startShopping.footer_enthusiast_noCardHolder');
    expect(results4).toEqual('valueCenter.membership.startShopping.footer_icon_noCardHolder_currentYearExp');
    expect(results5).toEqual('valueCenter.membership.startShopping.footer_core');
  });

  it('test function: setupTierProgressOptions()', () => {
    const results1 = utils.setupTierProgressOptions('CORE');
    const results2 = utils.setupTierProgressOptions('ENTHUSIAST');
    const results3 = utils.setupTierProgressOptions('ICON');
    expect(results1).toEqual({
      core: {
        imgSrc: coreLightBlue,
        textClasses: 'text-sm font-bold',
        textColor: '#0076a8',
      },
      enthusiast: {
        imgSrc: enthusiastGrey,
        textClasses: 'text-sm',
        textColor: '#ccc',
      },
      icon: {
        imgSrc: iconGrey,
        textClasses: 'text-sm',
        textColor: '#ccc',
      },
      lineClasses1: '#ccc',
      lineClasses2: '#ccc',
    });
    expect(results2).toEqual({
      core: {
        imgSrc: coreDarkBlue,
        textClasses: 'text-sm font-bold',
        textColor: '#003865',
      },
      enthusiast: {
        imgSrc: enthusiastLightBlue,
        textClasses: 'text-sm font-bold',
        textColor: '#0076a8',
      },
      icon: {
        imgSrc: iconGrey,
        textClasses: 'text-sm',
        textColor: '#ccc',
      },
      lineClasses1: '#003865',
      lineClasses2: '#ccc',
    });
    expect(results3).toEqual({
      core: {
        imgSrc: coreDarkBlue,
        textClasses: 'text-sm font-bold',
        textColor: '#003865',
      },
      enthusiast: {
        imgSrc: enthusiastDarkBlue,
        textClasses: 'text-sm font-bold',
        textColor: '#003865',
      },
      icon: {
        imgSrc: iconLightBlue,
        textClasses: 'text-sm font-bold',
        textColor: '#0076a8',
      },
      lineClasses1: '#003865',
      lineClasses2: '#003865',
    });
  });

  it('test function: chunkArray()', () => {
    const results = utils.chunkArray(
      [
        {
          points: 0,
          status: '',
          transactionDate: '',
          transactionDesc: '',
          transactionNumber: '',
        },
        {
          points: 1,
          status: '',
          transactionDate: '',
          transactionDesc: '',
          transactionNumber: '',
        },
        {
          points: 3,
          status: '',
          transactionDate: '',
          transactionDesc: '',
          transactionNumber: '',
        },
        {
          points: 4,
          status: '',
          transactionDate: '',
          transactionDesc: '',
          transactionNumber: '',
        },
      ],
      2
    );
    expect(results).toEqual([
      [
        {
          points: 0,
          status: '',
          transactionDate: '',
          transactionDesc: '',
          transactionNumber: '',
        },
        {
          points: 1,
          status: '',
          transactionDate: '',
          transactionDesc: '',
          transactionNumber: '',
        },
      ],
      [
        {
          points: 3,
          status: '',
          transactionDate: '',
          transactionDesc: '',
          transactionNumber: '',
        },
        {
          points: 4,
          status: '',
          transactionDate: '',
          transactionDesc: '',
          transactionNumber: '',
        },
      ],
    ]);
  });

  it('test function: formatLocaleDate()', () => {
    const results = utils.formatLocaleDate('04/01/2023');
    expect(results).toEqual('Apr 1, 2023');
  });

  it('test function: getRewardTileHeader()', () => {
    const results1 = utils.getRewardTileHeader('card', '', localize);
    const results2 = utils.getRewardTileHeader('cash', 'gapCash', localize);
    const results3 = utils.getRewardTileHeader('cash', 'styleCash', localize);
    const results4 = utils.getRewardTileHeader('cash', 'superCash', localize);
    const results5 = utils.getRewardTileHeader('', '', localize);
    expect(results1).toEqual('valueCenter.earnRedeem.rewardTile.card');
    expect(results2).toEqual('valueCenter.earnRedeem.rewardTile.gapCash');
    expect(results3).toEqual('valueCenter.earnRedeem.rewardTile.styleCash');
    expect(results4).toEqual('valueCenter.earnRedeem.rewardTile.superCash');
    expect(results5).toEqual('valueCenter.earnRedeem.rewardTile.reward');
  });

  it('test function: checkCurrentlyActive()', () => {
    const results1 = utils.checkCurrentlyActive('04/01/2023', '04/01/2027');
    const results2 = utils.checkCurrentlyActive('04/01/1998', '04/01/1999');
    expect(results1).toEqual(true);
    expect(results2).toEqual(false);
  });

  it('test function: checkDateAfterCurrentDate()', () => {
    const results1 = utils.checkDateAfterCurrentDate('04/01/2027');
    const results2 = utils.checkDateAfterCurrentDate('04/01/1998');
    expect(results1).toEqual(true);
    expect(results2).toEqual(false);
  });

  it('test function: getStartShoppingFooterText()', () => {
    const results1 = utils.getStartShoppingFooterText('CORE', false, '12/12/2023', localize);
    const results2 = utils.getStartShoppingFooterText('ENTHUSIAST', false, '12/12/2023', localize);
    const results3 = utils.getStartShoppingFooterText('ICON', false, '12/12/2023', localize);
    expect(results1).toEqual('valueCenter.membership.startShopping.footer_core');
    expect(results2).toEqual('valueCenter.membership.startShopping.footer_enthusiast_noCardHolder');
    expect(results3).toEqual('valueCenter.membership.startShopping.footer_icon_noCardHolder_currentYearExp');
  });

  it('test function: setupFeatureBenefitsTexts()', () => {
    const results1 = utils.setupFeatureBenefitsTexts('CORE', false, 'US', localize);
    const results2 = utils.setupFeatureBenefitsTexts('ENTHUSIAST', false, 'US', localize);
    const results3 = utils.setupFeatureBenefitsTexts('ICON', false, 'US', localize);
    expect(results1).toEqual({
      pointsTitle: 'valueCenter.membership.featuredBenefits.pointsTitle1',
      pointsCopytext1: 'valueCenter.membership.featuredBenefits.pointsCopytext1',
      pointsCopytext2: 'valueCenter.membership.featuredBenefits.pointsCopytext2',
      shippingTitle: 'valueCenter.membership.featuredBenefits.shippingTitle',
      shippingCopytext: 'valueCenter.membership.featuredBenefits.shippingCopytext1',
      quarterlyTitle: 'valueCenter.membership.featuredBenefits.quarterlyTitle',
      quarterlyCopytext: 'valueCenter.membership.featuredBenefits.quarterlyCopytext1',
    });
    expect(results2).toEqual({
      pointsTitle: 'valueCenter.membership.featuredBenefits.pointsTitle1',
      pointsCopytext1: 'valueCenter.membership.featuredBenefits.pointsCopytext1',
      pointsCopytext2: 'valueCenter.membership.featuredBenefits.pointsCopytext2',
      shippingTitle: 'valueCenter.membership.featuredBenefits.shippingTitle',
      shippingCopytext: 'valueCenter.membership.featuredBenefits.shippingCopytext1',
      quarterlyTitle: 'valueCenter.membership.featuredBenefits.quarterlyTitle',
      quarterlyCopytext: 'valueCenter.membership.featuredBenefits.quarterlyCopytext2',
    });
    expect(results3).toEqual({
      pointsTitle: 'valueCenter.membership.featuredBenefits.pointsTitle1',
      pointsCopytext1: 'valueCenter.membership.featuredBenefits.pointsCopytext1',
      pointsCopytext2: 'valueCenter.membership.featuredBenefits.pointsCopytext2',
      shippingTitle: 'valueCenter.membership.featuredBenefits.shippingTitle',
      shippingCopytext: 'valueCenter.membership.featuredBenefits.shippingCopytext2',
      quarterlyTitle: 'valueCenter.membership.featuredBenefits.quarterlyTitle',
      quarterlyCopytext: 'valueCenter.membership.featuredBenefits.quarterlyCopytext3',
    });
  });

  it('test function: setupBarcodeModalHeaderText()', () => {
    const results1 = utils.setupBarcodeModalHeaderText(localize, 'offer', {
      awardsPercentage: 0,
      promoBrand: 'GAP',
      promoDescription: 'description text',
      totalCash: '',
    });
    const results2 = utils.setupBarcodeModalHeaderText(localize, 'birthday', {
      awardsPercentage: 0,
      promoBrand: 'GAP',
      promoDescription: 'description text',
      totalCash: '',
    });
    expect(results1).toEqual('valueCenter.earnRedeem.barcodeModal.specialOffer');
    expect(results2).toEqual('description text');
  });

  it('test function: setupBrandName()', () => {
    const results1 = utils.setupBrandName('gp');
    const results2 = utils.setupBrandName('gpfs');
    expect(results1).toEqual('GAP');
    expect(results2).toEqual('GAPFS');
  });

  it('test function: removeItemAll()', () => {
    const results = utils.removeItemAll(['1', '2', '3', '4'], '2');
    expect(results).toEqual(['1', '3', '4']);
  });

  it('test function: getCharityLogo()', () => {
    const results = utils.getCharityLogo('gap-logo');
    expect(results).toEqual('/gap-logo');
  });

  it('test function: validateRedemptionCodeForm()', () => {
    const results1 = utils.validateRedemptionCodeForm('abc123def456');
    const results2 = utils.validateRedemptionCodeForm('@#(ddds');
    expect(results1).toEqual(true);
    expect(results2).toEqual(false);
  });

  it('test function: getBirthdayType()', () => {
    const results1 = utils.getBirthdayType('Jan', true);
    const results2 = utils.getBirthdayType('Jan', false);
    const results3 = utils.getBirthdayType('', false);
    expect(results1).toEqual('during');
    expect(results2).toEqual('outside');
    expect(results3).toEqual('missing');
  });

  it('test function: checkShowExpirationDate()', () => {
    const results1 = utils.checkShowExpirationDate('969777', {
      isBirthdayBonusPromo: false,
      isShowDateRangeOnBirthdayBonusPromoEnabled: false,
      isTargetedOffersEnabled: true,
    });
    const results2 = utils.checkShowExpirationDate('123', {
      isBirthdayBonusPromo: false,
      isShowDateRangeOnBirthdayBonusPromoEnabled: false,
      isTargetedOffersEnabled: true,
    });
    expect(results1).toEqual(false);
    expect(results2).toEqual(true);
  });

  it('test function: checkDateWithinBirthdayDateRange()', () => {
    const results1 = utils.checkDateWithinBirthdayDateRange(false, false, 'January');
    const results2 = utils.checkDateWithinBirthdayDateRange(true, true, 'January');
    expect(results1).toEqual(false);
    expect(results2).toEqual(true);
  });

  it('test function: checkTargetScrollTabSection() ', () => {
    const results = utils.checkTargetScrollTabSection(null);
    expect(results).toEqual({});
  });

  it('test function: checkTargetScrollTabSection() with membership', () => {
    const mockWindow = {
      location: {
        href: 'https://secure-www.stage.gaptechol.com/my-account/value-center?target=membership',
        search: '?target=membership',
      },
    };
    const results = utils.checkTargetScrollTabSection(mockWindow as Window);
    expect(results).toEqual({ scrollSection: '', tab: 'membership' });
  });

  it('test function: checkTargetScrollTabSection() with comparebenefits', () => {
    const mockWindow = {
      location: {
        href: 'https://secure-www.stage.gaptechol.com/my-account/value-center?target=comparebenefits',
        search: '?target=comparebenefits',
      },
    };
    const results = utils.checkTargetScrollTabSection(mockWindow as Window);
    expect(results).toEqual({ scrollSection: 'compareBenefits', tab: 'membership' });
  });

  it('test function: checkTargetScrollTabSection() with tierprogress', () => {
    const mockWindow = {
      location: {
        href: 'https://secure-www.stage.gaptechol.com/my-account/value-center?target=tierprogress',
        search: '?target=tierprogress',
      },
    };
    const results = utils.checkTargetScrollTabSection(mockWindow as Window);
    expect(results).toEqual({ scrollSection: 'startShopping', tab: 'membership' });
  });

  it('test function: checkTargetScrollTabSection() with trackpoints', () => {
    const mockWindow = {
      location: {
        href: 'https://secure-www.stage.gaptechol.com/my-account/value-center?target=trackpoints',
        search: '?target=trackpoints',
      },
    };
    const results = utils.checkTargetScrollTabSection(mockWindow as Window);
    expect(results).toEqual({ scrollSection: '', tab: 'trackPoints' });
  });

  it('test function: checkTargetScrollTabSection() with pointsactivity', () => {
    const mockWindow = {
      location: {
        href: 'https://secure-www.stage.gaptechol.com/my-account/value-center?target=pointsactivity',
        search: '?target=pointsactivity',
      },
    };
    const results = utils.checkTargetScrollTabSection(mockWindow as Window);
    expect(results).toEqual({ scrollSection: 'pointsActivity', tab: 'trackPoints' });
  });

  it('test function: checkTargetScrollTabSection() with earnandredeem', () => {
    const mockWindow = {
      location: {
        href: 'https://secure-www.stage.gaptechol.com/my-account/value-center?target=earnandredeem',
        search: '?target=earnandredeem',
      },
    };
    const results = utils.checkTargetScrollTabSection(mockWindow as Window);
    expect(results).toEqual({ scrollSection: '', tab: 'earnAndRedeem' });
  });

  it('test function: checkTargetScrollTabSection() with dogood', () => {
    const mockWindow = {
      location: {
        href: 'https://secure-www.stage.gaptechol.com/my-account/value-center?target=dogood',
        search: '?target=dogood',
      },
    };
    const results = utils.checkTargetScrollTabSection(mockWindow as Window);
    expect(results).toEqual({ scrollSection: 'donateRewards', tab: 'earnAndRedeem' });
  });

  it('test function: checkTargetScrollTabSection() with earnmorepoints', () => {
    const mockWindow = {
      location: {
        href: 'https://secure-www.stage.gaptechol.com/my-account/value-center?target=earnmorepoints',
        search: '?target=earnmorepoints',
      },
    };
    const results = utils.checkTargetScrollTabSection(mockWindow as Window);
    expect(results).toEqual({ scrollSection: 'earnMorePoints', tab: 'earnAndRedeem' });
  });

  it('test function: checkTargetScrollTabSection() with familybrandsbonus', () => {
    const mockWindow = {
      location: {
        href: 'https://secure-www.stage.gaptechol.com/my-account/value-center?target=familybrandsbonus',
        search: '?target=familybrandsbonus',
      },
    };
    const results = utils.checkTargetScrollTabSection(mockWindow as Window);
    expect(results).toEqual({ scrollSection: 'familyOfBrands', tab: 'earnAndRedeem' });
  });

  it('test function: checkTargetScrollTabSection() with quarterlybonus', () => {
    const mockWindow = {
      location: {
        href: 'https://secure-www.stage.gaptechol.com/my-account/value-center?target=quarterlybonus',
        search: '?target=quarterlybonus',
      },
    };
    const results = utils.checkTargetScrollTabSection(mockWindow as Window);
    expect(results).toEqual({ scrollSection: 'shopQuarterly', tab: 'earnAndRedeem' });
  });

  it('test function: checkTargetScrollTabSection() with birthdaybonus', () => {
    const mockWindow = {
      location: {
        href: 'https://secure-www.stage.gaptechol.com/my-account/value-center?target=birthdaybonus',
        search: '?target=birthdaybonus',
      },
    };
    const results = utils.checkTargetScrollTabSection(mockWindow as Window);
    expect(results).toEqual({ scrollSection: 'birthdayBonus', tab: 'earnAndRedeem' });
  });

  it('test function: checkTargetScrollTabSection() with convertpoints', () => {
    const mockWindow = {
      location: {
        href: 'https://secure-www.stage.gaptechol.com/my-account/value-center?target=convertpoints',
        search: '?target=convertpoints',
      },
    };
    const results = utils.checkTargetScrollTabSection(mockWindow as Window);
    expect(results).toEqual({ scrollSection: 'convertPoints', tab: 'earnAndRedeem' });
  });

  it('test function: setupPreviewDateFormat()', () => {
    const results1 = utils.setupPreviewDateFormat('2025-05-14 10:32:34 PDT');
    const results2 = utils.setupPreviewDateFormat('');
    expect(results1).toEqual('2025-05-14T10:32:34.000-07:00');
    expect(results2).toEqual('');
  });
});
