import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { FC } from 'react';
import { brandLookupTable } from '../../contexts/constants';
import { useOCP } from '../../contexts/GlobalProvider';
import { BopisItemsInfoList } from '../../contexts/types';
import { formatPhoneNumber } from '../../utils/OrderConfirmationUtil';

export const BopisCard: FC = () => {
  const { ocpResponse } = useOCP();
  const { panels: { bopisPanel: { pickupPerson: { firstName = '', lastName = '', mobileNumber = '' } = {}, bopisItemsInfoList = [] } = {} } = {} } =
    ocpResponse || {};
  const storeCount = bopisItemsInfoList.length;
  const { localize } = useLocalize();
  const bopisTitle = localize('ocp.bopisTitle');
  const storeText = localize('ocp.storeText');
  const storesText = localize('ocp.storesText');
  const emailPickupText = localize('ocp.emailPickupText');
  const pickupPerson = localize('ocp.pickupPerson');
  const preparingText = localize('ocp.preparingText');
  const bopisItemText = localize('ocp.bopisItemText');
  const bopisItemsText = localize('ocp.bopisItemsText');
  const pickupText = localize('ocp.pickupText');

  return (
    <div className='h-full w-full'>
      <div className='cb-display-sm-emphasis pb-4'>{`${bopisTitle} (${storeCount} ${storeCount > 1 ? storesText : storeText})`}</div>
      <div className='cb-base-compact pb-4'>{`${emailPickupText}`}</div>
      <div data-testid='ocp-bopis-pickup-person'>
        <div className='cb-base-compact-emphasis pb-1'>{`${pickupPerson}`}</div>
        <div className='cb-base-compact'>{`${firstName} ${lastName}`}</div>
        {mobileNumber && <div className='cb-base-compact pt-1'>{`${formatPhoneNumber(mobileNumber)}`}</div>}
      </div>
      {bopisItemsInfoList.map(({ itemCount, brandCode, storeId }: BopisItemsInfoList) => {
        return (
          <div className='pt-4' key={storeId}>
            <div className='cb-base-compact-emphasis'>{`${preparingText} ${itemCount} ${itemCount > 1 ? bopisItemsText : bopisItemText}`}</div>
            <div className='cb-base-compact pt-1'>{`${pickupText} ${brandLookupTable[brandCode]}`}</div>
          </div>
        );
      })}
    </div>
  );
};
