import '@testing-library/jest-dom';

import React from 'react';
import { screen } from '@testing-library/react';
import { OCPContext } from '../../../contexts/GlobalProvider';
import { render } from '../../../utils/test-utils';
import { Summary } from '../Summary';

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));
describe('OrderConfirmationSummary', () => {
  const ocpResponseMock = {
    panels: {
      bopisPanel: {
        bopisItemsInfoList: [
          {
            brandCode: '1',
            itemCount: 1,
            storeId: '148',
          },
        ],
        pickupPerson: {
          firstName: 'Teressa',
          lastName: 'Lisbon',
          mobileNumber: '**********',
        },
        bopisItemCount: 1,
      },
      orderSummaryPanel: {
        orderNumber: '123456',
        customerFirstName: 'John',
        customerLastName: '',
        customerEmail: '<EMAIL>',
        isGuest: false,
        customerPhoneNumber: '',
        errorType: '',
        isDropship: false,
        marketCode: 'A123',
        shippingItemCount: 123,
      },
      shippingAddressPanel: {
        shippingAddress: {
          city: 'city',
          country: 'us',
          firstName: 'Raghu',
          lastName: 'Ram',
          state: 'CA',
          streetName: 'sfsdf',
          unitNo: '234535',
          zip: '12345',
        },
        shippingMethod: '',
      },
    },
  };
  const renderComponent = (isMobile = false) => {
    return render(
      <OCPContext.Provider value={{ ocpResponse: ocpResponseMock, apiError: false }}>
        <Summary />
      </OCPContext.Provider>,
      '123456',
      isMobile
    );
  };

  it('renders order number and customer info', () => {
    renderComponent();
    expect(screen.getByText(/HI JOHN/i)).toBeInTheDocument();
    expect(screen.getByText(/Thank you for your order!/i)).toBeInTheDocument();
    expect(screen.getByText(/Your order number is/i)).toBeInTheDocument();
    expect(screen.getByText(/<EMAIL>/i)).toBeInTheDocument();
  });

  it('shows guest-specific message if user is a guest', () => {
    ocpResponseMock.panels.orderSummaryPanel.isGuest = true;
    renderComponent();
    expect(screen.queryByText(/Need to make changes?/i)).not.toBeInTheDocument();
  });

  it('shows member-specific message if user is not a guest', () => {
    ocpResponseMock.panels.orderSummaryPanel.isGuest = false;
    renderComponent();
    expect(screen.getByText(/Need to make changes?/i)).toBeInTheDocument();
  });

  it('renders with mobile styles when isMobile is true', () => {
    renderComponent(true);
    expect(screen.getByRole('button', { name: /Order Details/i })).toHaveClass('sm:w-auto');
  });

  it('renders with desktop styles when isMobile is false', () => {
    renderComponent(false);
    expect(screen.getByRole('button', { name: /Order Details/i })).toHaveClass('w-full px-[50px]');
  });

  it('show email Arrival check Spam', () => {
    renderComponent(false);
    expect(screen.getByText(/It might not arrive immediately. Make sure to check your spam folder./i)).toBeInTheDocument();
  });

  it('shows order update message if user is a guest and account creation is successful', () => {
    ocpResponseMock.panels.orderSummaryPanel.isGuest = true;
    render(
      <OCPContext.Provider value={{ ocpResponse: ocpResponseMock, apiError: false, isAccountCreationSuccess: true }}>
        <Summary />
      </OCPContext.Provider>
    );
    expect(screen.getByText(/Your order has been updated/i)).toBeInTheDocument();
  });

  it('shows thank you message if user is not a guest or account creation is not successful', () => {
    ocpResponseMock.panels.orderSummaryPanel.isGuest = false;
    render(
      <OCPContext.Provider value={{ ocpResponse: ocpResponseMock, apiError: false, isAccountCreationSuccess: false }}>
        <Summary />
      </OCPContext.Provider>
    );
    expect(screen.getByText(/Thank you for your order!/i)).toBeInTheDocument();
  });
});
