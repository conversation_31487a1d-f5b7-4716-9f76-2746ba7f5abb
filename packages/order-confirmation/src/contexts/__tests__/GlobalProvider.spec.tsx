import React from 'react';
import { act, render } from '@testing-library/react';
import { FeatureFlagsProvider } from '@ecom-next/core/legacy/feature-flags';
import { GlobalProvider, useOCP } from '../GlobalProvider';

jest.mock('@ecom-next/utils/useClientFetch', () => ({
  useClientFetch: jest.fn(),
}));

jest.mock('@ecom-next/sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn().mockReturnValue({
    locale: 'en-US',
  }),
}));

jest.mock('../GlobalProvider', () => {
  const actual = jest.requireActual('../GlobalProvider');
  return {
    ...actual,
    useOCP: jest.fn(),
    GlobalProvider: actual.GlobalProvider,
  };
});

describe('GlobalProvider component', () => {
  const mockContextValue = {
    ocpResponse: {
      panels: {
        orderSummaryPanel: {
          customerEmail: '<EMAIL>',
          customerFirstName: 'Jane',
          customerLastName: 'Dells',
          customerPhoneNumber: '**********',
          errorType: null,
          isDropship: false,
          isGuest: false,
          marketCode: 'US',
          orderNumber: '11DN5VM',
          shippingItemCount: 1,
        },
        shippingAddressPanel: {
          shippingAddress: {
            city: 'Huntington',
            country: 'US',
            firstName: 'Amy',
            lastName: 'Santiago',
            state: 'NY',
            streetName: '2 Queens St',
            unitNo: '32',
            zip: '11743-3725',
          },
          shippingMethod: '7-9',
        },
      },
    },
    apiError: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders children correctly', () => {
    const { getByTestId } = render(
      <FeatureFlagsProvider enabledFeatures={{}}>
        <GlobalProvider orderNumber='123'>
          <div data-testid='child'>Child Component</div>
        </GlobalProvider>
      </FeatureFlagsProvider>
    );
    expect(getByTestId('child')).toBeInTheDocument();
  });

  it('provides correct context value', () => {
    (useOCP as jest.Mock).mockReturnValue(mockContextValue);

    let receivedContext;
    const TestConsumer = () => {
      receivedContext = useOCP();
      return null;
    };

    render(
      <FeatureFlagsProvider enabledFeatures={{}}>
        <GlobalProvider orderNumber='123'>
          <TestConsumer />
        </GlobalProvider>
      </FeatureFlagsProvider>
    );

    expect(receivedContext).toEqual(mockContextValue);
  });

  it('fetches data from API', async () => {
    const mockFetchResponse = { data: mockContextValue, error: null };
    const useClientFetchMock = jest.fn(() => mockFetchResponse);
    require('@ecom-next/utils/useClientFetch').useClientFetch = useClientFetchMock;

    await act(async () => {
      render(
        <FeatureFlagsProvider enabledFeatures={{}}>
          <GlobalProvider orderNumber='123'>
            <div />
          </GlobalProvider>
        </FeatureFlagsProvider>
      );
    });

    expect(useClientFetchMock).toHaveBeenCalledWith(
      '/checkout/place-order/xapi/confirmation-details/123',
      expect.objectContaining({
        headers: expect.objectContaining({ locale: 'en-US' }),
        method: 'GET',
      })
    );
  });
});

describe('GlobalProvider component for Guest', () => {
  const mockContextValueGuest = {
    ocpResponse: {
      panels: {
        orderSummaryPanel: {
          customerEmail: '<EMAIL>',
          customerFirstName: 'John',
          customerLastName: 'Palmer',
          customerPhoneNumber: '**********',
          errorType: null,
          isDropship: false,
          isGuest: true,
          marketCode: 'US',
          orderNumber: '11WVY0Y',
          shippingItemCount: 1,
        },
        shippingAddressPanel: {
          shippingAddress: {
            city: 'Huntington',
            country: 'US',
            firstName: 'John',
            lastName: 'Palmer',
            state: 'NY',
            streetName: '2 Queens St',
            unitNo: '32',
            zip: '11743-3725',
          },
          shippingMethod: '5-7',
        },
      },
    },
    apiError: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders children correctly for guest', () => {
    const { getByTestId } = render(
      <FeatureFlagsProvider enabledFeatures={{}}>
        <GlobalProvider orderNumber='11WVY0Y'>
          <div data-testid='child'>Child Component</div>
        </GlobalProvider>
      </FeatureFlagsProvider>
    );
    expect(getByTestId('child')).toBeInTheDocument();
  });

  it('provides correct context value', () => {
    (useOCP as jest.Mock).mockReturnValue(mockContextValueGuest);

    let receivedContext;
    const TestConsumer = () => {
      receivedContext = useOCP();
      return null;
    };

    render(
      <FeatureFlagsProvider enabledFeatures={{}}>
        <GlobalProvider orderNumber='11WVY0Y'>
          <TestConsumer />
        </GlobalProvider>
      </FeatureFlagsProvider>
    );

    expect(receivedContext).toEqual(mockContextValueGuest);
  });

  it('fetches data from API for guest', async () => {
    const mockFetchResponse = { data: mockContextValueGuest, error: null };
    const useClientFetchMock = jest.fn(() => mockFetchResponse);
    require('@ecom-next/utils/useClientFetch').useClientFetch = useClientFetchMock;

    await act(async () => {
      render(
        <FeatureFlagsProvider enabledFeatures={{}}>
          <GlobalProvider orderNumber='11WVY0Y'>
            <div />
          </GlobalProvider>
        </FeatureFlagsProvider>
      );
    });

    expect(useClientFetchMock).toHaveBeenCalledWith(
      expect.stringContaining('/checkout/place-order/xapi/confirmation-details/11WVY0Y'),
      expect.objectContaining({
        headers: expect.objectContaining({ locale: 'en-US' }),
        method: 'GET',
      })
    );
  });
});
