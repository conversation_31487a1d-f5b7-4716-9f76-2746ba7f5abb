import type { Metada<PERSON> } from 'next';
import dynamic from 'next/dynamic';
import PageWrapper, { type PageParams } from '@ecom-next/sitewide/page-wrapper';
import { getPageContext } from '@ecom-next/utils/server';
import { CID, SitewideJsonContent } from '@ecom-next/marketing-ui/fetch';
import HeaderContainer from '@ecom-next/checkout/components/modules/Header/index';
import { brandCIDMap, getBrandCID } from '@ecom-next/marketing-ui/checkout';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import { StaticOCPContentDataCID, STATIC_OCP_CONTENTDATA } from '../utils/constants';
import OrderConfirmationApp from '../components/layout/OrderConfirmationApp';

const DynamicFooterContainer = dynamic(() => import('@ecom-next/checkout/components/modules/Footer/index').then(mod => mod.default), { ssr: false });

/**
 * Meta data that provides title for the Order Confirmation page.
 */
export const generateOCPMetaData = async (): Promise<Metadata> => {
  const { locale } = getPageContext();

  const pageName = locale === 'fr_CA' ? 'Confirmation de commande' : 'Order Confirmation';

  return { title: pageName };
};

export const OrderConfirmation = (props: PageParams): JSX.Element => {
  const { params } = props;
  const orderNumberParam = params?.orderNumber || 'N/A';
  const { brand, locale, market } = getPageContext();
  const cid = getBrandCID(brandCIDMap, brand) as CID;
  props.searchParams.cid = cid || 'error';
  const translations = getLocaleSpecificTranslations(locale, ['bag-page', 'checkout', 'profileui']);

  if (cid) {
    const key = `${cid}/cardpromo` as string;
    STATIC_OCP_CONTENTDATA.CID[key as keyof StaticOCPContentDataCID] = STATIC_OCP_CONTENTDATA.CID.dynamicCardpromo;
  }

  return (
    <PageWrapper {...props} pageType='checkout' promoContent={STATIC_OCP_CONTENTDATA.CID as SitewideJsonContent}>
      <div className='flex min-h-screen flex-col justify-between'>
        <LocalizationProvider locale={locale} supportNesting translations={translations} market={market}>
          <HeaderContainer />
          <div className='bg-crossbrand-g6 w-full grow overflow-scroll'>
            <OrderConfirmationApp orderNumber={orderNumberParam} />
          </div>
          <DynamicFooterContainer />
        </LocalizationProvider>
      </div>
    </PageWrapper>
  );
};
