export const formatPhoneNumber = (phoneNum: string) => {
  const phone = phoneNum.replace(/[^\d]/g, '');
  return phone.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
};

export const deleteQueryParams = (paramsList: string[], locationSearchParams: string) => {
  const searchParams = new URLSearchParams(locationSearchParams);
  const validParamList: string[] = [];
  searchParams?.forEach((key, value) => {
    const queryParam = `${value}=${key}`;
    !paramsList.includes(queryParam) && validParamList.push(queryParam);
  });
  return validParamList.join('&');
};
