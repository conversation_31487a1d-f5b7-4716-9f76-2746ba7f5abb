interface DataContent {
  [key: string]: string | number | boolean | object;
}
interface BaseContent {
  data: DataContent;
  experimentRunning: boolean;
  instanceName: string;
  lazy: boolean;
  name: string;
  redpointExperimentRunning: boolean;
  type: string;
  useGreyLoadingEffect: boolean;
}

interface ContentItem {
  _meta: {
    deliveryId: string;
    name: string;
    schema: string;
  };
  output: BaseContent & {
    description: string;
  };
}

export interface StaticOCPContentDataCID {
  [key: string]: BaseContent | { contentItems: ContentItem[] };
  dynamicCardpromo: {
    contentItems: ContentItem[];
  };
}

interface StaticOCPContentData {
  CID: StaticOCPContentDataCID;
}

export const STATIC_OCP_CONTENTDATA: StaticOCPContentData = {
  CID: {
    dynamicCardpromo: {
      contentItems: [
        {
          _meta: {
            name: '2023-05-01_us-bag-cardpromo',
            schema: 'https://cms.gap.com/schema/content/v1/json-marketing.json',
            deliveryId: '44360b5e-9077-4db5-afc5-e62fe3ef4b16',
          },
          output: {
            instanceName: 'OCPMarketing',
            type: 'sitewide',
            name: 'CreditCardOffer',
            experimentRunning: false,
            redpointExperimentRunning: true,
            description: 'GS MCM Version',
            lazy: false,
            useGreyLoadingEffect: false,
            data: {},
          },
        },
      ],
    },
  },
};
