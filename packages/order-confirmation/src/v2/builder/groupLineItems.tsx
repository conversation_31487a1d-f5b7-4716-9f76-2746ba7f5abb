import { DeliveryGroups, LineItems, ShipmentPreference, ShipmentStandard } from '../../contexts/types';

export const getGroupedLineItems = (lineItemList: LineItems[], shipmentPreferences: ShipmentPreference[], shipmentStandards: ShipmentStandard[]) => {
  const deliveryGroups: DeliveryGroups[] = [];
  const shippingPreferencesArray = new Map();
  shipmentPreferences.forEach(shipment => {
    shippingPreferencesArray.set(shipment.shippingGroupId, shipment);
  });
  const shipmentStandardsArray = new Map();
  shipmentStandards.forEach(shipment => {
    shipmentStandardsArray.set(shipment.shippingGroupId, shipment);
  });

  lineItemList.forEach(lineItem => {
    const { shippingGroupId } = lineItem;
    const shipmentPreference = shippingPreferencesArray.get(shippingGroupId);
    const shipmentStandard = shipmentStandardsArray.get(shippingGroupId);

    let deliveryGroup = deliveryGroups.find(group => group.shippingGroupId === shippingGroupId);
    if (!deliveryGroup) {
      deliveryGroup = {
        shippingGroupId,
        lineItemList: [],
        shipmentPreferences: [],
        shipmentStandards: [],
      } as DeliveryGroups;
      deliveryGroups.push(deliveryGroup);
    }

    deliveryGroup.lineItemList.push(lineItem);
    if (shipmentPreference && deliveryGroup.shipmentPreferences.length === 0) {
      deliveryGroup.shipmentPreferences.push(shipmentPreference);
    }
    if (shipmentStandard && deliveryGroup.shipmentStandards.length === 0) {
      deliveryGroup.shipmentStandards.push(shipmentStandard);
    }
  });
  return deliveryGroups;
};
