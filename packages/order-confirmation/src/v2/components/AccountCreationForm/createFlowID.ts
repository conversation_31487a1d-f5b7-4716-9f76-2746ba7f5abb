import { triggerAuthorizeReq, triggerUniqueIdReq } from '@ecom-next/my-account/requests/sign-in/signInRequests';
import type { BrandAbbr, Market, Locale } from '@ecom-next/utils/server';

type GetFlowIDPayload = {
  brandAbbr: BrandAbbr;
  ecomApiBaseUrl: string;
  email: string;
  locale: Locale;
  market: Market;
  uiv: string;
};

interface IDResponse {
  payload: {
    id: string;
  };
  type: string;
}

export const createFlowID = async (payload: GetFlowIDPayload) => {
  const { brandAbbr, ecomApiBaseUrl, locale, market, uiv, email } = payload;

  const authorizeRequestResponse: IDResponse = await new Promise(resolve => {
    // @ts-ignore
    triggerAuthorizeReq({ brandAbbr, ecomApiBaseUrl, locale, market, uiv, signInDispatch: resolve });
  });
  if (!authorizeRequestResponse.type.includes('SUCCESS') || !authorizeRequestResponse?.payload?.id) return { error: authorizeRequestResponse };

  const {
    payload: { id },
  } = authorizeRequestResponse;
  const uniqueIDResponse: IDResponse = await new Promise(resolve => {
    // @ts-ignore
    triggerUniqueIdReq({ flowId: id, email, ecomApiBaseUrl, signInDispatch: resolve });
  });
  if (!uniqueIDResponse.type.includes('SUCCESS') || !uniqueIDResponse?.payload?.id) return { error: uniqueIDResponse };

  return { uniqueIDResponse };
};
