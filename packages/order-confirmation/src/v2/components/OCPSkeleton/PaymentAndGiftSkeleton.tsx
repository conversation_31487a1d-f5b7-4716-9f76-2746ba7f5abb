import { LoadingPlaceholder } from '@ecom-next/core/migration/loading-placeholder';

export const PaymentAndGiftSkeleton = () => {
  return (
    <div data-testid='ocp-payment-section'>
      <div className='mb-2'>
        <LoadingPlaceholder fixedSize={{ width: 110, height: 18 }} />
      </div>
      <div>
        <LoadingPlaceholder fixedSize={{ width: 'auto', height: 18 }} />
      </div>
      <div className='mt-1'>
        <LoadingPlaceholder fixedSize={{ width: 'auto', height: 18 }} />
      </div>
    </div>
  );
};
