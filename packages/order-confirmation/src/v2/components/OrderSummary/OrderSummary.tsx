'use client';
import React from 'react';
import { OrderSummary as SummaryOfCharges } from '@ecom-next/shopping-bag/order-summary';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { Popover } from '@ecom-next/core/migration/pop-over';
import type { BasePlacement } from '@ecom-next/core/migration/pop-over/types';
import { InfoIcon } from '@ecom-next/checkout/components/panels/OrderSummary/assets/InfoIcon';
import { LineItem } from '@ecom-next/shopping-bag/order-summary/line-item';
import { formatPrice } from '@ecom-next/shopping-bag/utils';
import { useOCP } from '../../../contexts/GlobalProvider';

export const OrderSummary = () => {
  const { ocpResponse, apiError } = useOCP();
  const { localize } = useLocalize();
  const { locale } = usePageContext();

  const {
    donationsTotal,
    estimatedTax,
    giftCardsTotal,
    hasDonations,
    hasGiftCards,
    markdownPromoSavings,
    rewardsSavings,
    retailDeliveryFee = 0,
    shippingMessage = '',
    shippingPrice,
    subTotal,
    totalPrice,
    totalSavings,
  } = ocpResponse?.panels?.orderSummaryPanel ?? {};

  const { bopisItemCount = 0 } = ocpResponse?.panels?.bopisPanel ?? {};

  const popoverProps = {
    content: localize('orderSummary.stateRegFeeInfo'),
    closeButtonAriaLabel: 'close popover',
    disableCloseOnClickOutside: false,
    placement: 'top' as BasePlacement,
    crossBrand: true,
    offSetPlacement: [0, 5],
  };

  const labels = {
    shippingLabel: `${localize('orderSummary.shipping')} ${shippingMessage}`,
    totalLabel: localize('orderSummary.total'),
  };

  const shippingCharge = shippingPrice === 0 ? localize('orderSummary.free') : formatPrice(shippingPrice, '$', locale);

  const price = {
    bagTotal: String(totalPrice),
    baseTotal: subTotal ?? 0,
    currencySymbol: '$',
    estimatedTax: formatPrice(estimatedTax, '$', locale),
    markdownAndPromoSavings: markdownPromoSavings,
    rewardSavings: rewardsSavings,
    shippingCost: shippingCharge,
    totalSavings: totalSavings,
  };

  return (
    <>
      {!apiError && (
        <SummaryOfCharges>
          <SummaryOfCharges.Header />
          <SummaryOfCharges.PriceSummary
            labels={labels}
            price={price}
            showRewardsLineItem={price.rewardSavings !== 0 && price.rewardSavings !== undefined}
            showSavingsLineItem={price.markdownAndPromoSavings !== 0 && price.markdownAndPromoSavings !== undefined}
            showShippingLineItem={bopisItemCount === 0}
          >
            <>
              {retailDeliveryFee > 0 && (
                <LineItem
                  currencySymbol={price.currencySymbol}
                  label={
                    <div className='flex items-center'>
                      <span className='pr-0.5'>{localize('orderSummary.stateRegFee')}</span>
                      <Popover {...popoverProps}>
                        {({ onOpen, triggerProps }) => {
                          return (
                            <button data-testid='popover-trigger' onClick={onOpen} {...triggerProps} className='flex p-0.5 pb-[2.5px] leading-none'>
                              <InfoIcon size={14} className='static' />
                            </button>
                          );
                        }}
                      </Popover>
                    </div>
                  }
                  value={retailDeliveryFee}
                />
              )}
              {hasDonations && <LineItem currencySymbol={price.currencySymbol} label={localize('orderSummary.donations')} value={donationsTotal} />}
              {hasGiftCards && <LineItem currencySymbol={price.currencySymbol} label={localize('orderSummary.giftCards')} value={giftCardsTotal} />}
            </>
          </SummaryOfCharges.PriceSummary>
        </SummaryOfCharges>
      )}
    </>
  );
};
