import React from 'react';
import { formatPrice } from '@ecom-next/shopping-bag/utils';
import { render } from '../../../../utils/test-utils';
import { OCPContext } from '../../../../contexts/GlobalProvider';
import { PaymentDetailsPanel } from '../PaymentDetailsPanel';
import {
  ocpWithCreditCardPayment,
  ocpWithGIftCardPayment,
  ocpWithGiftCardAndCC,
  ocpWithDigitalWalletPayment,
  ocpWithApplePayPayment,
} from './getOCPResponse.mock';

describe('Test OCP Payment Component', () => {
  it('renders payment component with Visa credit card', () => {
    const { getByText, getByLabelText } = render(
      <OCPContext.Provider value={{ ocpResponse: ocpWithCreditCardPayment, apiError: false }}>
        <PaymentDetailsPanel />
      </OCPContext.Provider>
    );
    expect(getByText('Payment & Gift Cards')).toBeInTheDocument();
    expect(getByText('•••• 1111')).toBeInTheDocument();
    expect(getByLabelText('Credit card icon')).toHaveClass('card_image_visa');
  });

  it('renders payment component with Gift cards', () => {
    const { getByText, getAllByLabelText } = render(
      <OCPContext.Provider value={{ ocpResponse: ocpWithGIftCardPayment, apiError: false }}>
        <PaymentDetailsPanel />
      </OCPContext.Provider>
    );
    expect(getByText('Gift Card 0539')).toBeInTheDocument();
    expect(getByText('Gift Card 6987')).toBeInTheDocument();
    expect(getByText('$75.09')).toBeInTheDocument();
    expect(getByText('$90.09')).toBeInTheDocument();
    expect(getAllByLabelText('Gift card icon')[0]).toHaveClass('card_image_giftcard');
  });
  it('renders payment component with Gift cards and Credit card', () => {
    const { getByText, getByLabelText } = render(
      <OCPContext.Provider value={{ ocpResponse: ocpWithGiftCardAndCC, apiError: false }}>
        <PaymentDetailsPanel />
      </OCPContext.Provider>
    );
    expect(getByText('Payment & Gift Cards')).toBeInTheDocument();
    expect(getByText('•••• 5647')).toBeInTheDocument();
    expect(getByLabelText('Credit card icon')).toHaveClass('payment__card--ON_BCS_PLCC_ID');
    expect(getByText('Gift Card 0539')).toBeInTheDocument();
    expect(getByText('$75.09')).toBeInTheDocument();
    expect(getByLabelText('Gift card icon')).toHaveClass('card_image_giftcard');
  });
  it('renders payment component with Digital Wallet', () => {
    const { getByText, getByLabelText } = render(
      <OCPContext.Provider value={{ ocpResponse: ocpWithDigitalWalletPayment, apiError: false }}>
        <PaymentDetailsPanel />
      </OCPContext.Provider>
    );
    expect(getByText('Paying with')).toBeInTheDocument();
    expect(getByLabelText('Third party icon')).toHaveClass('payment__card--AFTERPAY');
  });
  it('renders payment component with Apple Pay', () => {
    const { getByText, queryByText, getByLabelText } = render(
      <OCPContext.Provider value={{ ocpResponse: ocpWithApplePayPayment, apiError: false }}>
        <PaymentDetailsPanel />
      </OCPContext.Provider>
    );
    expect(getByText('Paying with')).toBeInTheDocument();
    expect(queryByText('•••• 1111')).not.toBeInTheDocument();
    expect(getByLabelText('Apple Pay Icon')).toBeInTheDocument();
  });
  it('renders currency correctly with in US english', () => {
    const { getByText } = render(<span className='cb-base-compact'>{formatPrice('100.45', '$', 'en_US')}</span>);
    expect(getByText('$100.45')).toBeInTheDocument();
  });
  it('renders currency correctly with in CA english', () => {
    const { getByText } = render(<span className='cb-base-compact'>{formatPrice('100.45', '$', 'en_CA')}</span>);
    expect(getByText('CA$100.45')).toBeInTheDocument();
  });
  it('renders currency correctly with in CA french', () => {
    const { getByText } = render(<span className='cb-base-compact'>{formatPrice('100.45', '$', 'fr_CA')}</span>);
    expect(getByText('100,45 $ CA')).toBeInTheDocument();
  });
});
