import { render, screen, cleanup, act, waitFor } from '@testing-library/react';
import { useOutOfStock, useProductCardConfig } from '@ecom-next/plp-ufa';
import { ProductImage } from '../';
import { useProductImage, useProductInfo } from '../../../../api';

jest.mock('../../../../api');
jest.mock('../../../../experiments');
const mockUseProductImage = useProductImage as jest.Mock;
const mockUseProductInfo = useProductInfo as jest.Mock;
const mockUseOutOfStock = useOutOfStock as jest.Mock;
const mockProductCardConfig = useProductCardConfig as jest.Mock;

mockProductCardConfig.mockReturnValue({
  displayBadge: true,
});

const badgeText = 'Badge Text';

afterEach(() => {
  cleanup();
  jest.clearAllMocks();
});

describe('<ProductImage />', () => {
  describe('WHEN: the product is sold out and the feature flag to display the sold out overlay is on,', () => {
    test('THEN: a Sold Out overlay is displayed.', () => {
      mockUseOutOfStock.mockReturnValue(true);
      mockUseProductImage.mockReturnValue({
        mainImage: { type: 'Z', path: '/main.png' },
        zoomImages: [{ type: 'av1_Z', path: '/webcontent/0057/636/721/cn57636721.jpg' }],
        outOfStock: true,
      });
      mockUseProductInfo.mockReturnValue({
        productName: { name: 'Product Name' },
        productMarketingFlag: { badgeText },
      });
      const { container } = render(<ProductImage productId='12345678' />);

      const soldOutOverlay = container.querySelector('.plp_sold-out-overlay');

      expect(soldOutOverlay).toBeInTheDocument();
    });
    test('THEN: only the main image is displayed.', () => {
      mockUseProductImage.mockReturnValue({
        mainImage: { type: 'Z', path: '/main.png' },
        zoomImages: [{ type: 'av1_Z', path: '/webcontent/0057/636/721/cn57636721.jpg' }],
        outOfStock: true,
      });
      mockUseProductInfo.mockReturnValue({
        productName: { name: 'Product Name' },
        productMarketingFlag: { badgeText },
      });

      const { container } = render(<ProductImage productId='12345678' />);

      const zoomImage = container.querySelectorAll('.plp_zoom-image');

      expect(zoomImage).toHaveLength(0);
    });
    test('THEN: carousel buttons are not displayed.', () => {
      mockUseProductImage.mockReturnValue({
        mainImage: { type: 'Z', path: '/main.png' },
        zoomImages: [{ type: 'av1_Z', path: '/webcontent/0057/636/721/cn57636721.jpg' }],
        outOfStock: true,
      });
      mockUseProductInfo.mockReturnValue({
        productName: { name: 'Product Name' },
        productMarketingFlag: { badgeText },
      });

      const { container } = render(<ProductImage productId='12345678' />);

      const leftButton = container.querySelector('.plp_product-image__carousel-chevron-prev');
      const rightButton = container.querySelector('.plp_product-image__carousel-chevron-next');

      expect(leftButton).toBeNull();
      expect(rightButton).toBeNull();
    });
  });
  describe('GIVEN: the product is NOT sold out', () => {
    beforeEach(() => {
      mockUseProductImage.mockReturnValue({
        mainImage: { type: 'Z', path: '/main.png' },
        zoomImages: [{ type: 'av1_Z', path: '/webcontent/0057/636/721/cn57636721.jpg' }],
        outOfStock: false,
      });

      mockUseProductInfo.mockReturnValue({
        productName: { name: 'Product Name' },
        productMarketingFlag: { badgeText },
      });
    });

    it('Renders component and displays placeholder', () => {
      const { container } = render(<ProductImage productId='12345678' />);
      const component = container.querySelector('.plp_product-image__carousel-container-preloader');
      expect(component).toBeInTheDocument();
    });

    it('Renders component with a product image', async () => {
      const { container } = render(<ProductImage productId='12345678' />);
      await waitFor(() => {
        const component = container.querySelector('.plp_product-image');
        expect(component).toBeInTheDocument();
      });
    });

    it('Renders component with an product images and badge', async () => {
      render(<ProductImage productId='12345678' />);
      await waitFor(() => {
        const badgeElement = screen.queryByText(badgeText);
        expect(badgeElement).toBeInTheDocument();
      });
    });

    it('Renders component with an product images but without badge', async () => {
      mockProductCardConfig.mockReturnValue({
        displayBadge: false,
      });
      render(<ProductImage productId='12345678' />);
      await waitFor(() => {
        const badgeElement = screen.queryByText(badgeText);
        expect(badgeElement).not.toBeInTheDocument();
      });
    });

    it('Renders component with an product images validates image is on component', async () => {
      const { container } = render(<ProductImage productId='12345678' />);
      const images = container.querySelectorAll('img');
      expect(images).toHaveLength(2);
    });

    it('Renders component with an product images must have only the next chevron button', async () => {
      render(<ProductImage productId='12345678' />);
      waitFor(() => {
        const nextButton = screen.queryByTestId('button_icon_carousel_chevron_next');
        expect(nextButton).toBeInTheDocument();
        const prevButton = screen.queryByTestId('button_icon_carousel_chevron_prev');
        expect(prevButton).not.toBeInTheDocument();
      });
    });

    it('Renders component with an product images and clicking on next button, must enable prev chevron button', () => {
      render(<ProductImage productId='12345678' />);
      waitFor(() => {
        const nextButton = screen.queryByRole('button');
        expect(nextButton).toBeInTheDocument();
        act(() => {
          nextButton?.click();
        });
        const prevButton = screen.queryByTestId('button_icon_carousel_chevron_next');
        expect(prevButton).toBeInTheDocument();
      });
    });

    it('Renders component with an product images and clicking on next button until last image, must disable next chevron button', () => {
      render(<ProductImage productId='12345678' />);
      waitFor(() => {
        const nextButton = screen.queryByTestId('button_icon_carousel_chevron_next');
        act(() => {
          nextButton?.click();
        });
        const prevButton = screen.queryByTestId('button_icon_carousel_chevron_prev');
        expect(prevButton).toBeInTheDocument();
        const newNextButton = screen.queryByTestId('button_icon_carousel_chevron_next');
        expect(newNextButton).not.toBeInTheDocument();
      });
    });

    it('When window size is < 1024px, queried image size should have 480 on url', () => {
      global.innerWidth = 1023;
      global.dispatchEvent(new Event('resize'));

      const { container } = render(<ProductImage productId='12345678' />);
      const image = container.querySelector('img');
      expect(image!.src).toContain('w=480');
    });

    it('When window size is >= 1024px, queried image size should have 768 on url', () => {
      global.innerWidth = 1024;
      global.dispatchEvent(new Event('resize'));

      const { container } = render(<ProductImage productId='12345678' />);
      const image = container.querySelector('img');
      expect(image!.src).toContain('w=768');
    });

    describe('When we click on product image except alt image arrows', () => {
      it('Should have a link to product page when product is in stock', () => {
        const expectedLink = 'http://www.local.gaptechol.com:3000/browse/product.do?pid=718877032&cid=1029133&pcid=8792&vid=1';
        mockUseProductInfo.mockReturnValue({
          productName: {
            name: 'Product Name',
            clickable: true,
            link: expectedLink,
          },
          productMarketingFlag: { badgeText },
        });

        render(<ProductImage productId='12345678' />);
        expect(screen.getByRole('link', { hidden: true })).toHaveAttribute('href', expectedLink);
      });
      it('Should not have a link when product is out of stock', () => {
        const expectedLink = 'http://www.local.gaptechol.com:3000/browse/product.do?pid=718877032&cid=1029133&pcid=8792&vid=1';
        mockUseProductInfo.mockReturnValue({
          productName: {
            name: 'Product Name',
            clickable: false,
            link: expectedLink,
          },
          productMarketingFlag: { badgeText },
        });

        render(<ProductImage productId='12345678' />);

        expect(screen.queryByRole('link')).not.toBeInTheDocument();
      });
    });
  });
});
