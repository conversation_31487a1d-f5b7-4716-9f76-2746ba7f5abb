// @ts-nocheck
import {
  <PERSON>AR<PERSON>_PLACEHOLDER,
  SUBMIT_SEARCH_ARIA_LABEL,
  Department,
  Brands,
  clientSideLocalStorage,
  INCREASE_TIMEOUT,
} from "@ecom-next/plp-ui/legacy/utils";
import { DesktopSearchProps } from "../types";
import { fireEvent, RenderOptions, screen, within, act } from "test-utils";
import { mocked } from "jest-mock";
import {
  RECENT_SEARCHES_CLEAR_ALL_BUTTON,
  RECENT_SEARCHES_HEADER,
} from "../__mocks__/localizationTokens";
import { filledRecentSearches } from "../__mocks__/recentSearches";
import { VisualSearchProduct } from "@ecom-next/plp-ui/legacy/autosuggest";
import { ExperimentStatus } from "@ecom-next/plp-ui/legacy/experiments";
import { DataLayer } from "@ecom-next/core/legacy/app-state-provider/types";
import {
  renderDesktopSearchBar,
  mockSearchSuggestions,
  defaultProps,
} from "../test_helpers/renderDesktopSearchBar";

jest.mock("@find/utils/redirectSearch");
jest.mock("@ecom-next/plp-ui/legacy/ab-seg");
jest.mock("@find/utils/clientSideStorage", () => ({
  clientSideLocalStorage: {
    setItem: jest.fn(),
    getItem: jest.fn(),
  },
}));

const mockedGetItem = mocked(clientSideLocalStorage?.getItem);
const mockedSetItem = mocked(clientSideLocalStorage?.setItem);

const datalayer = {
  linkWithPageProps: jest.fn(),
} as unknown as DataLayer;

const getSearchBox = (): Promise<HTMLElement> =>
  screen.findByLabelText("search");

const focusSearchBox = async (): Promise<void> => {
  const searchBox = await getSearchBox();
  return searchBox.focus();
};

const recentSearchesKey = "gap66";

const translation = {
  [SEARCH_PLACEHOLDER]: "Search",
  [SUBMIT_SEARCH_ARIA_LABEL]: "Submit search request",
  [RECENT_SEARCHES_HEADER]: "Recent Searches",
  [RECENT_SEARCHES_CLEAR_ALL_BUTTON]: "Clear All",
};

const recentSearchesTestId = "recent-searches";

const waitForSuggestions = (): Promise<HTMLElement[]> =>
  screen.findAllByRole("option");

const getSuggestion = async (suggestion: string): Promise<HTMLElement> =>
  screen.findByRole("option", { name: suggestion });

const searchText = "shirt";
const suggestions = ["fancy shirt", "simple shirt", "red shirt"];

export const inputSearchText = async (searchText: string): Promise<void> => {
  const searchBox = await getSearchBox();
  fireEvent.change(searchBox, { target: { value: searchText } });
};

const renderAndEnterText = async (
  productsList: VisualSearchProduct[],
  departmentsList: Department[],
  options?: RenderOptions
): Promise<void> => {
  mockSearchSuggestions(suggestions, productsList, departmentsList);

  const props: DesktopSearchProps = {
    toggleOpen: jest.fn(),
    open: true,
  };

  renderDesktopSearchBar({}, props, options);
  await inputSearchText("shirt");
  await waitForSuggestions();
};

describe("DesktopSearchBar - RecentSearches", () => {
  beforeEach(() => {
    mockSearchSuggestions([], [], []);
    jest.resetAllMocks();
  });

  describe("when the experiment is Active", () => {
    describe("when user clicks an autosuggestion", () => {
      it("should set a new recent search", async () => {
        mockSearchSuggestions([], [], []);
        mockedGetItem?.mockReturnValue(JSON.stringify(filledRecentSearches));

        await renderAndEnterText([], []);
        await act(async () => { 

         fireEvent.click(await getSuggestion(suggestions[0])); 

         })

        expect(mockedSetItem).toHaveBeenCalled();
      });
    });

    describe("when there are recent searches and no input text", () => {
      beforeEach(() => {
        jest.clearAllMocks();
        mockedGetItem?.mockReturnValue(JSON.stringify(filledRecentSearches));
      });

      it(
        "should render RecentSearches component",
        async () => {
          const abSeg = { [recentSearchesKey]: ExperimentStatus.Active };
          await renderDesktopSearchBar(abSeg);
          await focusSearchBox();

          expect(
            await screen.findByTestId(recentSearchesTestId)
          ).toBeInTheDocument();
        },
        INCREASE_TIMEOUT
      );

      it("should desktop Recent Searches heading have style", async () => {
        const abSeg = { [recentSearchesKey]: ExperimentStatus.Active };
        await renderDesktopSearchBar(abSeg);
        await focusSearchBox();
        const element = within(
          await screen.findByTestId(recentSearchesTestId)
        ).getByRole("heading", {
          level: 2,
        });

        expect(element).toHaveStyle({ maxWidth: "calc(100% - 97px)" });
      });

      test.each`
        brand                                | featureEnabled | backgroundColor
        ${Brands.Gap}                        | ${true}        | ${"#FFFFFF"}
        ${Brands.OldNavy}                    | ${true}        | ${"#FFFFFF"}
        ${Brands.BananaRepublic}             | ${true}        | ${"#F6F4EB"}
        ${Brands.Athleta}                    | ${true}        | ${"#FFFFFF"}
        ${Brands.GapFactoryStore}            | ${true}        | ${"#FFFFFF"}
        ${Brands.BananaRepublicFactoryStore} | ${true}        | ${"#F6F4EB"}
        ${Brands.Gap}                        | ${false}       | ${"#FFFFFF"}
        ${Brands.OldNavy}                    | ${false}       | ${"#FFFFFF"}
        ${Brands.BananaRepublic}             | ${false}       | ${"#FFFFFF"}
        ${Brands.Athleta}                    | ${false}       | ${"#FFFFFF"}
        ${Brands.GapFactoryStore}            | ${false}       | ${"#FFFFFF"}
        ${Brands.BananaRepublicFactoryStore} | ${false}       | ${"#FFFFFF"}
      `(
        'should show $backgroundColor background color for $brand when "pdp-redesign-2022" feature flag is $featureEnabled',
        async ({ brand, featureEnabled, backgroundColor }) => {
          await renderDesktopSearchBar(
            { [`${brand}66`]: ExperimentStatus.Active },
            {},
            {
              appState: { brandName: brand, datalayer },
              enabledFeatures: { "pdp-redesign-2022": featureEnabled },
            }
          );
          await focusSearchBox();

          expect(screen.getByTestId("search-terms-container")).toHaveStyle({
            backgroundColor,
          });
        }
      );
    });

    describe("when there are recent searches and text input", () => {
      it("should not render RecentSearches", async () => {
        const abSeg = { [recentSearchesKey]: ExperimentStatus.Active };
        mockedGetItem?.mockReturnValue(JSON.stringify(filledRecentSearches));
        await renderDesktopSearchBar(abSeg);
        const searchBox = await getSearchBox();
        fireEvent.change(searchBox, { target: { value: searchText } });

        expect(
          screen.queryByTestId(recentSearchesTestId)
        ).not.toBeInTheDocument();
      });
    });

    describe("when there are no recent searches or input text", () => {
      it("should not render RecentSearches", async () => {
        const abSeg = { [recentSearchesKey]: ExperimentStatus.Active };
        mockedGetItem?.mockReturnValue("");
        await renderDesktopSearchBar(abSeg);
        await focusSearchBox();

        expect(
          screen.queryByTestId(recentSearchesTestId)
        ).not.toBeInTheDocument();
      });
    });

    describe("when there is input text but no recent searches", () => {
      it("should not render RecentSearches", async () => {
        const abSeg = { [recentSearchesKey]: ExperimentStatus.Active };
        mockedGetItem?.mockReturnValue("");
        await renderDesktopSearchBar(abSeg);
        const searchBox = await getSearchBox();
        fireEvent.change(searchBox, { target: { value: searchText } });

        expect(
          screen.queryByTestId(recentSearchesTestId)
        ).not.toBeInTheDocument();
      });
    });

    describe("when user performs a search", () => {
      it("should set a new recent search", async () => {
        const abSeg = { [recentSearchesKey]: ExperimentStatus.Active };
        await renderDesktopSearchBar(abSeg);
        const searchBox = await getSearchBox();
        fireEvent.change(searchBox, { target: { value: searchText } });
        await act(async () => { 

         fireEvent.click(
           screen.getByRole("button", {
             name: translation[SUBMIT_SEARCH_ARIA_LABEL],
           })
         ); 

         })

        expect(mockedSetItem).toHaveBeenCalled();
      });
    });
  });

  describe("when recent searches experiment is off", () => {
    it("should not render RecentSearches", () => {
      mockedGetItem?.mockReturnValue(JSON.stringify(filledRecentSearches));
      const abSeg = { [recentSearchesKey]: ExperimentStatus.Control };
      renderDesktopSearchBar(abSeg, defaultProps);
      focusSearchBox();
      expect(
        screen.queryByTestId(recentSearchesTestId)
      ).not.toBeInTheDocument();
    });
  });
});
