// @ts-nocheck
'use client'

import {
  CSSObject,
  getFontWeight,
  Theme,
  forBrands,
} from "@ecom-next/core/react-stitch";
import { getControlBeforeStyles } from '../getControlBeforeStyles';

const setBackgroundColorForBR = (theme: Theme, isBRWhiteBackground: boolean) =>{
  if (isBRWhiteBackground) {
    return theme.color.wh;
  }
  return "#F6F4EB";
}
      

 const setControlStylesForBananaRepublic = (theme: Theme, isBRWhiteBackground: boolean): CSSObject => ({
  textAlign: 'left',
  color: theme.color.bk,
  backgroundColor: setBackgroundColorForBR(theme, isBRWhiteBackground),
  width: '6.25rem',
  padding: '0.375rem',
  borderBottom: `0.063rem solid ${theme.color.bk}`,
  '&::before': {
    ...getControlBeforeStyles(theme),
    backgroundPositionX: '0.5rem',
    backgroundPositionY: '1rem',
  },
  '&:hover': {
    backgroundColor: theme.color.wh,
    animation: 'none !important',
    outline: 'none',
  },
  '&:focus': {
    animation: 'none !important',
    outline: 'none',
  },
  fontWeight: getFontWeight('regular').fontWeight,
  fontSize: '1rem',
});

const setControlStylesForAthletaRedesign = (): CSSObject => ({
  fontSize: "1.1875rem",
})

export const getControlStyles =
  (isBRWhiteBackground: boolean) =>
  (base: CSSObject, { theme }: { theme: Theme }): CSSObject => ({
    ...base,
    color: theme.color.b1,
    marginTop: 0,
    position: 'relative',
    width: '100%',
    textAlign: 'center',
    '&::before': {
      ...getControlBeforeStyles(theme),
      width: '0.5rem',
      right: '-0.5rem',
      backgroundSize: '100% auto',
      backgroundPositionX: 'right',
      marginLeft: '0.5rem',
      marginRight: '0.5rem',
    },
    ...(forBrands(theme, {
      br: setControlStylesForBananaRepublic(theme, isBRWhiteBackground),
      brfs: setControlStylesForBananaRepublic(theme, isBRWhiteBackground),
      at: setControlStylesForAthletaRedesign(),
    }) as CSSObject),
  });

export default getControlStyles;
