// @ts-nocheck
'use client';

/* eslint-disable complexity */
import React, { useContext, useEffect, useRef, useState, useMemo } from 'react';
import { styled, Theme } from '@ecom-next/core/react-stitch';
import { FixedButton, Kind } from '@ecom-next/core/legacy/fixed-button';
import { useTheme } from '@ecom-next/core/react-stitch';
import { useLocalize, Localize } from '@ecom-next/sitewide/localization-provider';
import { ItemCount } from '@ecom-next/core/legacy/item-count';
import { Brands } from '@ecom-next/core/react-stitch';
import { Breakpoint, BreakpointContext, SMALL, XLARGE, LARGE } from '@ecom-next/core/breakpoint-provider';
import { DrawerSlidePosition } from '@ecom-next/core/legacy/motion';
import { Drawer } from '@ecom-next/core/legacy/drawer';
import { FeatureFlagsContext } from '@ecom-next/core/legacy/feature-flags';
import BopisBar from '@ecom-next/plp-ui/legacy/bopis-bar';
import { FacetBar, useCategoryFacetName } from '@ecom-next/plp-ui/legacy/facet-bar';
import { FilterIcon } from '@ecom-next/plp-ui/legacy/rail';
import { InlineFacetTags } from '@ecom-next/plp-ui/legacy/inline-facet-tags';
import { SortBy } from '@ecom-next/plp-ui/legacy/sort-by';
import { SizeModelToggle } from '@ecom-next/plp-ui/legacy/size-model-toggle';
import { features } from '../plp-experiments/experiments-config/constants';
import { usePLPGapRedesign2024 } from '../plp-experiments/hooks/plp-gap-redesign-2024-hook';
import { FilterOption, FilterProps } from './types';
import {
  FiltersContainer,
  FilterButton,
  DrawerContent,
  DrawerCSS,
  DrawerFooter,
  DrawerFooterCSS,
  SortContainer,
  ItemCountContainer,
  InlineFacetTagsContainer,
  FilterAndSortContainer,
  AllFiltersTextStyles,
  DrawerHeader,
  FixedButtonContainer,
  DrawerContentContainer,
} from './styles';
import { getAppliedFacetCount, getAllAppliedFacetCount, getPropertiesFromOptions, adaptFacetData } from './collaborators';
import {
  ALL_FILTERS_TEXT_AT,
  ALL_FILTERS_TEXT_BR,
  ALL_FILTERS_TEXT_DEFAULT,
  ITEM_COUNT_ARIA_LABEL,
  ITEM_COUNT_TEXT,
  GRID_HEADER_FACET_DRAWER_SEE_RESULTS_TEXT,
  CLOSE_ARIA_LABEL,
  FACET_REFERENCE_FACET_LABEL_DEPARTMENT,
  FACET_REFERENCE_FACET_LABEL_STYLE,
  FACET_REFERENCE_FACET_LABEL_COLOR,
  FACET_REFERENCE_FACET_LABEL_PRICE,
  FACET_REFERENCE_FACET_LABEL_SIZE,
  FACET_REFERENCE_FACET_LABEL_RATING,
} from './translations';
import CloseButton from './components/CloseButton/CloseButton';
import { useFiltersFacets, usePLPGridToggle } from '@ecom-next/plp-ui/legacy/plp-experiments';
import { GridButtons } from '@ecom-next/plp-ui/legacy/grid-buttons';
import { useGridHeaderTopPosition } from './hooks';
import { useFeature } from '../features/use-feature';

const renderAppliedCount = (appliedCount: number) => (appliedCount > 0 ? ` (${appliedCount})` : null);

type GridHeaderProps = {
  isMobile?: boolean;
  theme?: Theme;
  stickyOffset: number;
};

const GridContainer = styled.div(({ stickyOffset, isMobile, theme }: GridHeaderProps) => ({
  whiteSpace: 'nowrap',
  position: 'sticky',
  zIndex: 100,
  paddingBottom: '1rem',
  '@media (max-width: 1024px)': { paddingLeft: '0rem' },
  background: theme?.color?.wh,
  paddingTop: isMobile ? '0rem' : '1rem',
  top: isMobile ? `64px` : `${stickyOffset}px`,
}));

export const GridHeader = (props: FilterProps): JSX.Element => {
  const {
    appliedFacets,
    facets,
    itemCount,
    appliedOptions,
    onClear,
    clearAll,
    sortValue,
    onSortChange,
    bopisArgs,
    sizeModelToggleArgs,
    isSortByRatingsEnabled,
    isSortByNewEnabled,
    isSortByBestSellersEnabled,
    isOnlyOneDepartment,
    allowedFacets,
    onClickGridHeader,
    locale,
    handleDataLayer,
  } = props;
  const theme = useTheme();
  const { localize } = useLocalize();
  const drawerBottomRef = useRef<HTMLDivElement>(null);
  const [open, setOpen] = useState(false);
  const [drawerBottomOffsetHeight, setDrawerBottomOffsetHeight] = useState(0);
  const { minWidth, smallerThan } = useContext(BreakpointContext);
  const isMobile = smallerThan(XLARGE);
  const isDesktop = minWidth(XLARGE);
  const isTablet = minWidth(LARGE) && !isDesktop;
  const isPLPGapRedesign2024 = usePLPGapRedesign2024();
  const [filter, setFilter] = useState(['']);
  const isBrandBr = theme.brand === 'br' || theme.brand === 'brfs';
  const filterText = theme.brand === Brands.Athleta || isMobile ? localize(ALL_FILTERS_TEXT_AT) : localize(ALL_FILTERS_TEXT_DEFAULT);
  const itemCountText = localize(ITEM_COUNT_TEXT, { count: itemCount });
  const itemCountLabel = localize(ITEM_COUNT_ARIA_LABEL, { count: itemCount });
  const DEPARTMENT_FACET = 'department';
  const CATEGORY_FACET = 'style';
  const isFiltersFacets = useFiltersFacets();
  const plpGridToggleExperiment = usePLPGridToggle();
  const isPlpGridToggleExperimentEnabled = plpGridToggleExperiment !== 'GRID_TOGGLE_OFF';
  const isPLPRedesignEnabled = isFiltersFacets === 'TEST_WITH_DYNAMIC_QUICK_FACETS' || isFiltersFacets === 'TEST_WITH_BRAND_PRIORITIZED_QUICK_FACETS';
  const { enabledFeatures } = useContext(FeatureFlagsContext);
  const gapNewFont = 'gap-redesign-2024';
  const isGapNewFontEnabled = enabledFeatures?.[gapNewFont];
  const shouldChangeFontWeightForGapRedesign2024 = isPLPGapRedesign2024 && isGapNewFontEnabled;
  const { gridHeaderTopPosition } = useGridHeaderTopPosition();
  const modifiedCategoryFacetName = useCategoryFacetName();

  const adaptedFacets = useMemo(() => adaptFacetData(facets, allowedFacets), [facets, allowedFacets]);

  const filterData = getPropertiesFromOptions(adaptedFacets, isOnlyOneDepartment);

  const isAdaptedFacetsAvailable = () => adaptedFacets && adaptedFacets.length > 0;

  const openDrawer = (selectedFilter: string, index: number) => {
    setOpen(true);
    setFilter([selectedFilter]);
    onClickGridHeader && onClickGridHeader(`${index}_${selectedFilter}`);
  };

  useEffect(() => {
    setTimeout(() => {
      setDrawerBottomOffsetHeight(appliedOptions && appliedOptions.length > 0 ? 118 : 78);
    }, 50);
  }, [appliedOptions]);

  const closeDrawer = () => {
    setOpen(false);
    setFilter([]);
  };

  const allFiltersButton = (
    <>
      {!isBrandBr && (
        <div className='all_filters_icon' data-testid='all-filters'>
          <FilterIcon />
        </div>
      )}
      <>{isBrandBr ? localize(ALL_FILTERS_TEXT_BR) : filterText}</>
    </>
  );

  const itemCountView = (
    <ItemCountContainer>
      <ItemCount aria-label={itemCountLabel} text={itemCountText} />
    </ItemCountContainer>
  );

  function localizedFilterButton(facet: FilterOption, index: number) {
    const facetLocalizationKeys: { [key: string]: string } = {
      department: FACET_REFERENCE_FACET_LABEL_DEPARTMENT,
      style: FACET_REFERENCE_FACET_LABEL_STYLE,
      color: FACET_REFERENCE_FACET_LABEL_COLOR,
      price: FACET_REFERENCE_FACET_LABEL_PRICE,
      size: FACET_REFERENCE_FACET_LABEL_SIZE,
      reviewScore: FACET_REFERENCE_FACET_LABEL_RATING,
    };
    const getLocalizedFacetDisplayName = ({
      localize,
      name,
      displayName,
      dynamicFacetName,
    }: {
      localize: Localize;
      name: string;
      displayName: string;
      dynamicFacetName?: boolean;
    }): string => {
      const facetLocalizationKey = facetLocalizationKeys[name];
      if (name === CATEGORY_FACET && modifiedCategoryFacetName) {
        return modifiedCategoryFacetName;
      }
      return facetLocalizationKey && !dynamicFacetName ? localize(facetLocalizationKey) : displayName;
    };
    facet = {
      ...facet,
      name: facet.name,
      options: facet.options,
      displayName: getLocalizedFacetDisplayName({
        localize,
        name: facet.name,
        displayName: facet.displayName,
        dynamicFacetName: facet.dynamicFacetName,
      }),
    };
    return (
      <FilterButton
        onClick={() => openDrawer(facet.name, index)}
        isAllFilters={false}
        shouldChangeFontWeightForGapRedesign2024={shouldChangeFontWeightForGapRedesign2024}
        key={facet.name}
        isShopByTeamFacet={facet.displayName === modifiedCategoryFacetName}
      >
        {facet.displayName}
        {renderAppliedCount(getAppliedFacetCount(appliedFacets, facet.name))}
      </FilterButton>
    );
  }

  let facetIndexCount = 0;

  const drawerOrigin = (): string => {
    const desktopOrigin = DrawerSlidePosition.left;
    return isDesktop ? desktopOrigin : DrawerSlidePosition.bottom;
  };

  return (
    <GridContainer data-testid='grid-header-container' stickyOffset={gridHeaderTopPosition} isMobile={isMobile}>
      {((isMobile && !isPlpGridToggleExperimentEnabled) || (isMobile && !isTablet && isPlpGridToggleExperimentEnabled)) && itemCountView}
      <FilterAndSortContainer isAppliedOptions={appliedOptions?.length}>
        <FiltersContainer>
          <FilterButton
            onClick={() => openDrawer('allFilters', 0)}
            shouldChangeFontWeightForGapRedesign2024={shouldChangeFontWeightForGapRedesign2024}
            isAllFilters={true}
          >
            {allFiltersButton}
            {renderAppliedCount(getAllAppliedFacetCount(appliedFacets))}
          </FilterButton>
          {filterData.map(facet => {
            if (facet.name === DEPARTMENT_FACET && isOnlyOneDepartment) {
              return null;
            }

            facetIndexCount++;
            return localizedFilterButton(facet, facetIndexCount);
          })}
          {isPlpGridToggleExperimentEnabled && (
            <>
              <SortContainer isPlpGridToggleEnabledDesktop={isPlpGridToggleExperimentEnabled}>
                {sizeModelToggleArgs?.enabled && (
                  <SizeModelToggle
                    brand={sizeModelToggleArgs.brand}
                    isMixedGridEnabled={sizeModelToggleArgs.isMixedGridEnabled}
                    publish={sizeModelToggleArgs.publish}
                    isMobile={false}
                    locale={locale}
                  />
                )}
                <SortBy
                  value={sortValue}
                  onChange={onSortChange}
                  isSortByRatingsEnabled={isSortByRatingsEnabled}
                  isSortByNewEnabled={isSortByNewEnabled}
                  isSortByBestSellersEnabled={isSortByBestSellersEnabled}
                  brand={theme.brand}
                />
              </SortContainer>
              {isTablet && itemCountView}
              {isDesktop && <GridButtons cid='' url='' getGridToggleDataLayer={handleDataLayer} />}
            </>
          )}
          {isDesktop && itemCountView}
        </FiltersContainer>
        {isPlpGridToggleExperimentEnabled && isMobile && !isTablet && <GridButtons cid='' url='' getGridToggleDataLayer={handleDataLayer} />}
        {isPlpGridToggleExperimentEnabled && isTablet && <GridButtons cid='' url='' getGridToggleDataLayer={handleDataLayer} />}
        {!isPlpGridToggleExperimentEnabled && (
          <SortContainer>
            {sizeModelToggleArgs?.enabled && (
              <SizeModelToggle
                brand={sizeModelToggleArgs.brand}
                isMixedGridEnabled={sizeModelToggleArgs.isMixedGridEnabled}
                publish={sizeModelToggleArgs.publish}
                isMobile={false}
                locale={locale}
              />
            )}
            <SortBy
              value={sortValue}
              onChange={onSortChange}
              isSortByRatingsEnabled={isSortByRatingsEnabled}
              isSortByNewEnabled={isSortByNewEnabled}
              isSortByBestSellersEnabled={isSortByBestSellersEnabled}
              brand={theme.brand}
            />
          </SortContainer>
        )}
      </FilterAndSortContainer>
      {appliedOptions?.length > 0 ? (
        <InlineFacetTagsContainer>
          <Breakpoint is='greaterOrEqualTo' size={SMALL}>
            <InlineFacetTags
              data-testid='grid-header-inline-facets'
              facetOptions={appliedOptions}
              onClear={onClear}
              clearAll={clearAll}
              id={''}
              name={''}
              facetName={''}
              applied={false}
              ariaLabel={''}
              displayName={''}
              capitalizedText={false}
            />
          </Breakpoint>
        </InlineFacetTagsContainer>
      ) : null}
      <Drawer data-testid='plp__filters-drawer' dialogCSS={DrawerCSS()} isOpen={open} onClose={() => closeDrawer()} position={drawerOrigin()}>
        <DrawerContentContainer>
          <DrawerHeader>
            <AllFiltersTextStyles>{localize(ALL_FILTERS_TEXT_DEFAULT)}</AllFiltersTextStyles>
            <CloseButton ariaLabel={localize(CLOSE_ARIA_LABEL)} onClick={closeDrawer} />
          </DrawerHeader>
          <DrawerContent drawerBottomOffsetHeight={drawerBottomOffsetHeight} isPLPFilterFacets={isPLPRedesignEnabled}>
            {bopisArgs?.enabled && (
              <BopisBar
                position={bopisArgs.position}
                abbrNameForTealium={bopisArgs.abbrNameForTealium}
                bopisData={bopisArgs.bopisData}
                businessUnitId={bopisArgs.businessUnitId}
                categoryName={bopisArgs.categoryName}
                datalayer={bopisArgs.datalayer}
                publish={bopisArgs.publish}
                isToggleButtonActive={bopisArgs.isToggleButtonActive}
                pageType={bopisArgs.pageType}
                selectedNodes={bopisArgs.selectedNodes}
                key='drawer-bopis-bar'
                isBopisHidden={false}
              />
            )}
            <FacetBar
              facets={isAdaptedFacetsAvailable() ? adaptedFacets : facets}
              appliedFacets={appliedFacets}
              defaultExpanded={filter}
              isOnlyOneDepartment={isOnlyOneDepartment}
            />
          </DrawerContent>
        </DrawerContentContainer>
        <DrawerFooter ref={drawerBottomRef}>
          {appliedOptions?.length > 0 ? (
            <InlineFacetTagsContainer>
              <InlineFacetTags
                data-testid='grid-header-inline-facets'
                facetOptions={appliedOptions}
                onClear={onClear}
                clearAll={clearAll}
                id={''}
                name={''}
                facetName={''}
                applied={false}
                ariaLabel={''}
                displayName={''}
                capitalizedText={false}
                position='FACET_DRAWER'
              />
            </InlineFacetTagsContainer>
          ) : null}
          <FixedButtonContainer>
            <FixedButton data-testid='grid-header-primary-button' css={DrawerFooterCSS()} kind={Kind.primary} onClick={() => closeDrawer()}>
              {localize(GRID_HEADER_FACET_DRAWER_SEE_RESULTS_TEXT, {
                count: itemCount,
              })}
            </FixedButton>
          </FixedButtonContainer>
        </DrawerFooter>
      </Drawer>
    </GridContainer>
  );
};
