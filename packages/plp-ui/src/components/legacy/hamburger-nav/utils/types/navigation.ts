// @ts-nocheck
'use client'

import { ReactNode } from "react";
import { CSSObject } from "@ecom-next/core/react-stitch";
import { Icon } from ".";

export type WebHierarchyElementType =
  | "category"
  | "categorysearch"
  | "division"
  | "header"
  | "headerless-group"
  | "sale"
  | "spacer"
  | "sub-division"
  | "subcategory"
  | "trimheader";

export type WebHierarchyElement = {
  children: WebHierarchyElement[];
  hasSubDivision: boolean;
  hidden: boolean;
  id: string;
  link?: string;
  customUrl?: string;
  name: string;
  parents: string[];
  type: WebHierarchyElementType;
  selected?: boolean; // should probably not mutate the webHierarchy data with this UI specific property
};

export type CurrentNavigationRoute = [
  WebHierarchyDivision,
  WebHierarchyCategory
];

export type WebHierarchyDivision = WebHierarchyElement & {
  type: "division";
  link: string;
  brandCode?: string;
};

export type WebHierarchyCategory = WebHierarchyElement & {
  type: "category";
  active?: boolean;
  link: string;
};

export type WebHierarchy = Array<WebHierarchyElement | WebHierarchyDivision>;

export type WebHierarchyTreeItem = {
  id: string;
  type: string;
  hasSubDivision: boolean;
  name: string;
  parents: string[];
  children: WebHierarchyTreeItem[];
};

export type HamburgerNavDivision = {
  ancestor: boolean;
  active: boolean;
  type: "division";
  dataType?: "division" | "sub-division";
  name: string;
  cid: string;
  icon?: Icon;
  children?: Array<HamburgerNavSection | HamburgerNavCategory>;
  link?: string;
  customStyles?: CSSObject;
  fallbackData?: boolean;
};

export type HamburgerNavSection = {
  active: boolean;
  type: "section";
  dataType?: "trimheader" | "header" | "home" | "headerless-group";
  name: string;
  cid: string;
  icon?: Icon;
  children: HamburgerNavCategory[];
  isAccordionOpened?: boolean;
  customStyles?: CSSObject;
};

export type HamburgerNavCategory = {
  active: boolean;
  type: "category" | "sale";
  dataType?: "category" | "sale";
  name: string;
  cid: string;
  icon?: Icon;
  children?: HamburgerNavSubcategory[];
  link: string;
  customStyles?: CSSObject;
};

export type HamburgerNavSubcategory = {
  active: boolean;
  type: "subcategory";
  dataType?: "subcategory";
  name: string;
  cid: string;
  icon?: Icon;
  link: string;
  customStyles?: CSSObject;
};

export type HamburgerNavItem = {
  active: boolean;
  ancestor: boolean;
  children?: HamburgerNavItem[];
  cid: string;
  name: string;
  type: string;
  dataType?: string;
};

export type HamburgerNavDataItem =
  | HamburgerNavDivision
  | HamburgerNavSection
  | HamburgerNavCategory
  | HamburgerNavSubcategory;

export type HamburgerNavDataCollectionItem =
  | HamburgerNavDivision
  | HamburgerNavSection
  | HamburgerNavCategory;

export type HamburgerNavData = HamburgerNavDataItem[];

export type HamburgerNavFooterItem = {
  title: string;
  url: string;
  children?: HamburgerNavFooterItem[];
};

export type HamburgerNavFooterRewardOverwritesItem = {
  title: string;
  children?: Record<string, unknown>;
};

export type HamburgerNavFooterRewardOverwrites = Record<
  string,
  HamburgerNavFooterRewardOverwritesItem
>;

export type HamburgerNavFooterSection = {
  title: string;
  children: HamburgerNavFooterItem[];
};

export type HamburgerNavFooterData = {
  content: Array<HamburgerNavFooterSection | HamburgerNavFooterItem>;
  title: string;
};

export type HamburgerNavStatus = "open" | "closed";

export type FetchStatus = "idle" | "pending" | "resolved" | "rejected";

export type HamNavActiveLevelTransitionDirection = "left" | "right";

export type HamNavActiveLevelTransitionProps = {
  activeLevel?: string;
  animationDirection: HamNavActiveLevelTransitionDirection;
  animationSpeed?: number;
  children: ReactNode;
  customCss?: CSSObject;
};

export type HamNavActiveMenuProps = {
  activatedLevels: HamburgerNavDataItem[];
  animationDirection: HamNavActiveLevelTransitionDirection;
  className?: string;
  isHamNavSubcatsEnabled?: boolean;
  title?: string;
  onBack: () => void;
};

export type HamNavItemListProps = {
  activateLevel: (level: HamburgerNavDataCollectionItem) => void;
  activatedLevels?: HamburgerNavDataItem[];
  items: HamburgerNavData;
  isHamNavSubcatsEnabled: boolean;
};

export type HamNavFooterItemProps = {
  title: string;
  url?: string;
  children?: HamburgerNavFooterItem[];
};

export type HamNavItemButtonProps = {
  item: HamburgerNavDataCollectionItem;
  clickHandler: (item: HamburgerNavDataCollectionItem) => void;
};
