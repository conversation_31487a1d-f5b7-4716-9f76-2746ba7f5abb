{"sitewide": {"footer": {"sitewide-footer-ciid": "18611290", "sitewide-footer-locale": "en_CA", "sitewide-footer-desc": "031620_gapCAfooterUpdate", "type": "sitewide", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components": [{"instanceName": "gap-footer", "name": "Footer", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "694px", "large": "416px"}, "socialLinks": [{"to": "https://www.facebook.com/gap/", "text": "Follow on Facebook"}], "emailRegistration": {"disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_CA", "data": {"classes": "legal", "style": {}, "html": "<p><span class=\"asterisk\">*</span>Valid for first-time registrants only &amp; applies to <span>reg. price items only.</span> <a onclick=\"return contentItemLink(this,'','CS_Footer_PrivacyPolicy');\" href=\"https://corporate.gapinc.com/en-ca/consumer-privacy-policy\">Privacy Policy</a></p><p>Yes! I would like to receive style news and exclusive offers from Gap Inc. and related companies and brands including Gap Inc. and Old Navy Inc., and Banana Republic Inc.</p><p>You can withdraw your consent at any time. For more details see our <a onclick=\"return contentItemLink(this,'','CS_Footer_PrivacyPolicy');\" href=\"https://corporate.gapinc.com/en-ca/consumer-privacy-policy\">Privacy Policy</a> or <a href=\"https://corporate.gapinc.com/en-ca/consumer-privacy-policy?locale=en_CA#contact\" onclick=\"return contentItemLink(this,'','CS_Footer_ContactUs');\">Contact Us</a>.</p>"}}, "title": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_CA", "data": {"classes": "footer-email-register__title-wrapper", "style": {}, "html": "<span className=\"footer-email-register__with-break-large-widths\">Sign up for email</span><span><span>&amp; get 30% off</span><span>*</span></span>"}}, "subtitle": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_CA", "data": {"classes": "", "style": {}, "html": ""}}, "submitButtonText": "Join", "emailPlaceholderText": "Email Address"}, "footerCustomerSupport": {"mobile": {"links": [{"type": "link", "text": "Store Locator", "to": "/stores", "className": "footer-line"}, {"type": "link", "text": "Customer Service", "to": "/customerService/info.do?cid=2136", "className": "footer-line"}, {"type": "link", "text": "Orders & Returns", "to": "/customerService/info.do?cid=2326", "className": "footer-line"}, {"type": "link", "text": "Shipping", "to": "/customerService/info.do?cid=2324", "className": "footer-line"}, {"type": "accordion", "text": "Gift Cards", "accordionLinks": [{"type": "link", "text": "Buy eGift Cards", "to": "https://www.buyatab.com/custom/gap/", "target": "_blank", "className": "footer-line"}, {"type": "link", "text": "Buy Gift Cards", "to": "/browse/product.do?pid=000166", "target": "_blank", "className": "footer-line"}]}, {"type": "link", "text": "Careers", "to": "https://corporate.gapinc.com/en-ca/careers/gap-careers", "target": "_blank", "rel": "noopener", "className": "footer-line"}, {"type": "accordion", "text": "Shop Our Other Brands", "accordionLinks": [{"type": "link", "text": "Old Navy", "to": "//oldnavy.gapcanada.ca", "target": "_blank", "className": "footer-line"}, {"type": "link", "text": "Banana Republic", "to": "//bananarepublic.gapcanada.ca", "target": "_blank", "className": "footer-line"}]}]}, "desktop": {"columns": [{"header": {}, "links": []}, {"header": {"text": "Customer Support", "link": "/customerService/info.do?cid=2136", "style": {"fontSize": "15px", "fontWeight": "500", "letterSpacing": "0.5px", "textTransform": "uppercase"}}, "links": [{"type": "link", "text": "Store Locator", "to": "/stores", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".5rem", "paddingBottom": ".5rem"}}, {"type": "text", "text": "1.800.GAPSTYLE (1.800.427.7895)", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".5rem", "paddingBottom": ".5rem"}}, {"type": "link", "text": "Customer Service", "to": "/customerService/info.do?cid=2136", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".5rem", "paddingBottom": ".5rem"}}, {"type": "link", "text": "Shipping", "to": "/customerService/info.do?cid=2324", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".5rem", "paddingBottom": ".5rem"}}, {"type": "link", "text": "Returns", "to": "/customerService/info.do?cid=2326", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".5rem", "paddingBottom": ".5rem"}}, {"type": "link", "text": "Track Your Order", "to": "https://secure-www.gapcanada.ca/profile/order_status_gateway.do", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".5rem", "paddingBottom": ".5rem"}}, {"type": "link", "text": "Giftcards", "to": "/customerService/info.do?cid=2116", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "marginBottom": "5rem", "paddingTop": ".5rem", "paddingBottom": ".5rem"}}]}, {"header": {"text": "About Us", "link": "", "target": "blank", "rel": "noopener", "style": {"fontSize": "15px", "fontWeight": "500", "letterSpacing": "0.5px", "textTransform": "uppercase"}}, "links": [{"type": "link", "text": "Careers", "to": "https://corporate.gapinc.com/en-ca/careers/gap-careers", "target": "blank", "rel": "noopener", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".5rem", "paddingBottom": ".5rem"}}, {"type": "link", "text": "Gap For Good", "to": "/browse/info.do?cid=1086537&mlink=footer,18611290,footer_gapforgood", "target": "blank", "rel": "noopener", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".5rem", "paddingBottom": ".5rem"}}]}, {"header": {}, "links": []}]}}, "copyRights": {"rows": [[{"text": "© 2020 The Gap, Inc.", "style": {"fontSize": "11px"}}, {"text": "Privacy Policy", "to": "https://corporate.gapinc.com/en-ca/consumer-privacy-policy", "style": {"fontSize": "11px"}}, {"text": "Terms of Use", "to": "/customerService/info.do?cid=6754", "style": {"fontSize": "11px"}}, {"text": "Sustainability", "to": "https://www.gapincsustainability.com/", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}], [{"text": "Accessibility for Ontarians with Disabilities Act", "to": "/customerService/info.do?cid=1005563", "style": {"fontSize": "11px"}}]]}}}]}, "sitewide-universalBar-ciid": "18700734", "sitewide-universalBar-desc": "032320_edfs_covid19", "edfssmall": {"instanceName": "edfs-header-small", "name": "MktEdfsSmall", "type": "sitewide", "experimentRunning": false, "data": {"lazy": false, "defaultData": {"textStrong": "Free Shipping ", "text": "on Orders of $25 or More.", "detailsLink": "Details"}, "modalUrl": "/browse/info.do?cid=47471", "modalTitle": "Everyday Free Shipping", "modalCloseButtonAriaLabel": "Close Pop-Up"}}, "edfslarge": {"instanceName": "edfs-header-large", "name": "MktEdfsLarge", "type": "sitewide", "experimentRunning": false, "data": {"lazy": false, "defaultData": {"text": "Free Shipping on Orders of $25 or More.", "detailsLink": "Details"}, "modalUrl": "/browse/info.do?cid=47471", "modalTitle": "Everyday Free Shipping", "modalCloseButtonAriaLabel": "Close Pop-Up"}}, "mobileemergencybanner": {"instanceName": "dpg_emergency_banner_mob_en", "name": "LayoutComponent", "type": "sitewide", "defaultHeight": "100px", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"height": "auto", "width": "100%", "display": "flex", "margin": "0 auto", "justifyContent": "center"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"width": "100%"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"display": "flex", "justifyContent": "center"}, "components": [{"type": "builtin", "name": "div", "data": {"props": {"id": "dpg_COVID_mob_en", "style": {"fontFamily": "Helvetica, sans-serif", "display": "flex", "justifyContent": "center"}}, "components": [{"type": "builtin", "name": "div", "data": {"props": {"style": {"fontSize": "14px", "lineHeight": "1.5", "verticalAlign": "middle", "padding": ".5rem 0", "textAlign": "center"}}, "components": ["Caring for our Customers and Communities: ", {"type": "builtin", "name": "div", "data": {"props": {}, "components": [{"type": "builtin", "name": "a", "data": {"props": {"href": "/browse/info.do?cid=1154703&locale=en_CA", "target": "_blank", "style": {"fontWeight": "bold", "fontSize": "12px", "textDecoration": "underline", "textAlign": "center"}}, "components": ["Temporary Store Closures and Coronavirus Update"]}}]}}]}}]}}]}}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"height": "auto", "maxWidth": "1400px", "width": "100%", "display": "flex", "margin": "0 auto", "justifyContent": "center", "marginTop": "10px"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"maxWidth": "1400px"}, "components": [{"type": "builtin", "name": "div", "data": {"props": {"id": "dpg_COVID_ipad_en", "style": {"fontFamily": "Helvetica, sans-serif", "display": "flex", "justifyContent": "center", "flexDirection": "column", "padding": "0.5em 0"}}, "components": [{"type": "builtin", "name": "div", "data": {"props": {"style": {"textAlign": "center"}}, "components": ["Caring for our Customers and Communities: "]}}, {"type": "builtin", "name": "a", "data": {"props": {"href": "/browse/info.do?cid=1154703&locale=en_CA", "target": "_blank", "style": {"textDecoration": "underline", "fontWeight": "bold", "color": "black", "display": "inherit", "justifyContent": "center"}}, "components": ["Temporary Store Closures and Coronavirus Update"]}}]}}]}}}}]}}}}, "desktopemergencybanner": {"instanceName": "dpg_emergency_banner_desk_en", "name": "LayoutComponent", "type": "sitewide", "defaultHeight": {"mobile": "100px", "desktop": "100px"}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"height": "auto", "maxWidth": "1400px", "width": "100%", "display": "flex", "margin": "0 auto", "justifyContent": "center"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"maxWidth": "1400px"}, "components": [{"type": "builtin", "name": "div", "data": {"props": {"id": "dpg_COVID_desk_en", "style": {"fontFamily": "Helvetica, sans-serif", "display": "flex", "justifyContent": "center"}}, "components": [{"type": "builtin", "name": "div", "data": {"props": {"style": {"padding": "1.2rem .5rem"}}, "components": ["Caring for our Customers and Communities: "]}}, {"type": "builtin", "name": "a", "data": {"props": {"href": "/browse/info.do?cid=1154703&locale=en_CA", "target": "_blank", "style": {"textDecoration": "underline", "fontWeight": "bold", "color": "black", "display": "inherit", "padding": "1.2rem 0.25rem 1.2rem 0rem"}}, "components": ["Temporary Store Closures and Coronavirus Update"]}}]}}]}}}}]}}}}, "sitewide-ciid": "********", "sitewide-desc": "update_032520", "sitewide-ciid_locale": "en_CA", "headline": {"instanceName": "headline-03-26", "type": "sitewide", "name": "AcquisitionBanner", "experimentRunning": false, "data": {"style": {"backgroundColor": "#ff8f73"}, "desktopStyle": {"maxHeight": "38px"}, "loyaltyLogo": {"component": {"name": "div", "type": "builtin", "data": {"props": {"style": {"fontSize": "11px", "width": "100%", "height": "100%"}}, "options": {"isDesktopVisible": true, "isMobileVisible": true}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"style": {"fontWeight": "bold", "color": "#FFFFFF", "backgroundColor": "#000000", "height": "100%", "display": "flex", "alignItems": "center", "justifyContent": "center"}}, "components": ["40% OFF", {"type": "builtin", "name": "a", "data": {"props": {"href": "https://www.gap.com", "target": "_blank", "style": {"backgroundColor": "#000000", "color": "#FFFFFF", "textDecoration": "underline", "paddingLeft": "5px", "fontWeight": "normal"}}}}]}}]}}, "style": {"border": "pink solid 2px"}, "desktopStyle": {"border": "pink solid 2px"}}, "dynamicCustomerName": {"showGreeting": true, "greeting": {"text": "Hi,", "style": {"fontSize": "7px"}, "desktopStyle": {"fontSize": "9px"}}, "firstName": {"style": {"fontWeight": "bold", "fontSize": "7px"}, "desktopStyle": {"fontWeight": "bold"}}, "style": {}, "desktopStyle": {}}, "applyButton": {"button": {"text": "Apply!", "style": {"display": "flex", "justifyContent": "center", "alignItems": "center", "textAlign": "center", "fontSize": "6px", "background": "none", "color": "inherit", "border": "none", "padding": "0 15px", "font": "inherit", "cursor": "pointer", "outline": "inherit"}, "desktopStyle": {"display": "flex", "justifyContent": "center", "alignItems": "center", "textAlign": "center", "fontSize": "11px"}}, "style": {"display": "flex", "justifyContent": "center", "alignItems": "center", "textAlign": "center", "background": "none"}, "desktopStyle": {"display": "flex", "justifyContent": "center", "alignItems": "center", "textAlign": "center"}}, "offer": {"component": {"type": "builtin", "name": "div", "data": {"props": {"style": {"width": "100%", "maxWidth": "1920px", "margin": "0 auto"}}, "components": [{"instanceName": "gpus_secondaryheadline-gsb", "instanceDesc": "0107 GSB No PZ", "name": "div", "type": "builtin", "gsb-ciid": "28443193", "experimentRunning": false, "meta": {"excludePageTypes": []}, "data": {"props": {"style": {"backgroundColor": "#ffffff", "position": "relative", "width": "100%", "lineHeight": "0", "maxWidth": "1920px", "margin": "0 auto"}}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"style": {"position": "relative", "maxWidth": "1920px", "margin": "0 auto", "lineHeight": "0"}}, "components": [{"useGreyLoadingEffect": false, "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "transparent"}}, "background": {"image": {"alt": "null", "srcUrl": "/Asset_Archive/GPWeb/content/0028/443/193/assets/010822_JANUARYCONTINGENCIES_USEC_img_GSB_MOB.jpg?v=1", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/443/193/assets/010822_JANUARYCONTINGENCIES_USEC_img_GSB_DESK.jpg?v=1", "style": {}}, "linkData": {"to": "/browse/category.do?cid=1127938#pageId=0&department=136&mlink=5058,28443193,globalbanner_FlashSale", "target": "_self"}, "style": {}}, "overlay": {"alt": "flash sale 40% off your purchase code TREAT + extra 20% off code MORE", "srcUrl": "/Asset_Archive/GPWeb/content/0028/443/193/assets/010822_JANUARYCONTINGENCIES_USEC_copy_GSB_MOB.svg?v=1", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/443/193/assets/010822_JANUARYCONTINGENCIES_USEC_copy_GSB_DESK.svg?v=1"}, "ctaList": {"mobilePositionAboveContent": false, "style": {"height": "auto", "width": "auto"}, "desktopStyle": {}, "className": "", "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/buy/promo_legal_details.do?promoId=847465", "height": "500px"}}, "composableButtonData": {"children": "details", "capitalization": "uppercase", "style": {"backgroundColor": "transparent", "color": "#ffffff", "fontSize": "9px", "fontWeight": "normal", "textDecoration": "underline", "padding": "0", "position": "absolute", "bottom": "0px", "right": ".1%"}, "desktopStyle": {"fontSize": "10px", "bottom": "5%", "right": ".15%"}}}]}}}]}}]}}]}}, "style": {"border": "purple solid 2px"}, "desktopStyle": {"border": "purple solid 2px"}}}}, "logo": {"type": "sitewide", "name": "Logo", "logoImgPath": "/Asset_Archive/GPWeb/content/0017/054/502/assets/gol_logo_CA.svg"}, "search": {"type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["Shorts", "Dresses", "Tanks", "Skorts", "Masks"]}}, "hamnav": {"type": "sitewide", "name": "HamburgerNav", "data": {"activeDivisions": [{"name": "NEW", "customStyles": {"color": "#000000", "font-weight": ""}, "cid": "127361", "divisionHeaders": [{"name": "SHOP NEW ARRIVALS", "customStyles": {"TBD": "TBD"}, "cid": "5361", "isAccordionOpened": true, "categories": [{"name": "Women", "customStyles": {"TBD": "TBD"}, "cid": "5362", "link": "/browse/category.do?cid=8792&nav=hamnav%3ANew%20%2B%20Now%3AShop%20New%20Arrivals%3AWomen"}]}]}, {"name": "Gap factory", "customStyles": {"TBD": "TBD"}, "link": "gapfactory.com"}], "topBanner": {"altText": "alt text", "imgSrc": "image url"}, "bottomBanner": {"altText": "alt text", "imgSrc": "image url"}, "exclusionIds": ["49708", "49709"]}}, "topnav": {"type": "sitewide", "name": "MegaNav", "data": {"isNavSticky": true, "activeDivisions": [{"name": "New Arrivals", "divisionId": ["1086624"], "megaNavOrder": [["1139272"]], "numberOfColumns": {"1139272": 2}, "exclusionIds": [], "customStyles": {}}, {"name": "<PERSON><PERSON>", "divisionId": ["1077403"], "megaNavOrder": [["1135249"], ["1137310"], ["1137320", "1137322"]], "exclusionIds": [], "customStyles": {}}, {"name": "Women", "divisionId": ["/browse/division.do?cid=5643&mlink=39813,18507430,Megnav_Women&clink=18507430", "5646"], "megaNavOrder": [["1131702"], ["1042481"], ["1131696", "5903"], ["1065848", "1131698"]], "numberOfColumns": {"1042481": 2}, "exclusionIds": [], "customStyles": {"65179": {"colorScheme": "sale"}}}, {"name": "Maternity", "divisionId": ["/browse/division.do?cid=5997&mlink=39813,18507430,Meganav_Maternity&clink=18507430", "5997"], "megaNavOrder": [["1149538"], ["1042513"], ["1014415"], ["1065847"]], "numberOfColumns": {"1042513": 2}, "exclusionIds": [], "customStyles": {"65302": {"colorScheme": "sale"}}}, {"name": "Men", "divisionId": ["/browse/division.do?cid=5063&mlink=39813,18507430,Megnav_Men&clink=18507430", "5065"], "megaNavOrder": [[], ["1149531"], ["1042515"], ["1065865", "1076121"]], "numberOfColumns": {"1042515": 2}, "exclusionIds": [], "customStyles": {"65289": {"colorScheme": "sale"}}}, {"name": "Girls", "divisionId": ["/browse/division.do?cid=1137865&mlink=39813,18507430,Megnav_Girls&clink=18507430", "6256"], "megaNavOrder": [[], ["1056088"], ["1042516"], ["1065849", "6258"]], "numberOfColumns": {"1042516": 2}, "exclusionIds": [], "customStyles": {"65194": {"colorScheme": "sale"}}}, {"name": "Boys", "divisionId": ["/browse/division.do?cid=1137867&mlink=39813,18507430,Megnav_Boys&clink=18507430", "6172"], "megaNavOrder": [[], ["1056087"], ["1042518"], ["1065843", "6174"]], "numberOfColumns": {"1042518": 2}, "exclusionIds": [], "customStyles": {"65217": {"colorScheme": "sale"}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=1137868&mlink=39813,18507430,Megnav_<PERSON>ler&clink=18507430", "6413"], "megaNavOrder": [["1149845"], ["1016135"], ["1016083"], ["1065866", "1016582"]], "exclusionIds": [], "customStyles": {"65236": {"colorScheme": "sale"}, "65263": {"inlineStyle": {"color": "#d0011b"}}}}, {"name": "Baby", "divisionId": ["/browse/division.do?cid=1137869&mlink=39813,18507430,Megnav_Baby&clink=18507430", "6487"], "megaNavOrder": [["1149847"], ["95461"], ["95574"], ["1149848"], ["1065867", "1014322"]], "exclusionIds": [], "customStyles": {"65208": {"inlineStyle": {"color": "#d0011b"}}, "65261": {"inlineStyle": {"color": "#d0011b"}}}}, {"name": "Gap For Good", "divisionId": ["/browse/info.do?cid=1086537&mlink=39813,18507430,Meganav_GFG&clink=18507430", "1147558"], "megaNavOrder": [["1150547"], ["1150546"]], "exclusionIds": [], "customStyles": {"65208": {"inlineStyle": {"color": "#d0011b"}}, "65261": {"inlineStyle": {"color": "#d0011b"}}}}]}, "abtest": {"isNavSticky": true, "activeDivisions": [{"name": "New Arrivals", "divisionId": ["1086624"], "megaNavOrder": [["1139272"]], "numberOfColumns": {"1139272": 2}, "exclusionIds": [], "customStyles": {}}, {"name": "<PERSON><PERSON>", "divisionId": ["1077403"], "megaNavOrder": [["1135249"], ["1137310"], ["1137320", "1137322"]], "exclusionIds": [], "customStyles": {}}, {"name": "Women", "divisionId": ["/browse/division.do?cid=5643&mlink=39813,18507430,Megnav_Women&clink=18507430", "5646"], "megaNavOrder": [["1131702"], ["1042481"], ["1131696", "5903"], ["1065848", "1131698"]], "numberOfColumns": {"1042481": 2}, "exclusionIds": [], "customStyles": {"65179": {"colorScheme": "sale"}}}]}}, "promodrawer": {"instanceName": "EC_promoDrawer-032520", "name": "PromoDrawerComponentV2", "type": "sitewide", "data": {"buildInfo": ["********", "GP"], "style": {"height": "293px"}, "options": {"desktopVisible": false, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "search", "info", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart"]}, "autoFire": "load", "disabledAutoFirePageTypes": ["category"], "promos": [{"bannerContent": {"name": "Image", "type": "sitewide", "isBannerClickable": false, "data": {"largeImg": "/Asset_Archive/GPWeb/content/0018/699/204/assets/033120_PD_1_EC.png?v=2", "smallImg": "/Asset_Archive/GPWeb/content/0018/699/204/assets/033120_PD_1_EC.png?v=2", "imageMap": {"identifier": "map_0", "areaAttributes": [{"shape": "rect", "coords": "7, 3, 635, 362", "plink": "false", "href": "/browse/category.do?cid=8792", "alt": "48 hour flash sale 40% off everything code 2days"}]}, "classes": "promoDrawer__content__item__banner", "altText": "Promo #1", "style": {}}}, "applicationDetails": {"toolType": "tap", "type": "tap", "overlay": "Code will be applied at bag", "message": "tap to apply", "defaultMessage": "tap to apply", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "40% Off Everything: Excludes gift cards. Offer valid on March 31, 2020 from 12:01am PT to April 1, 2020 11:59pm PT online at gapcanada.ca in Canada only. Discount applies to merchandise only, not to gift cards, applicable taxes, shipping & handling charges. Offer cannot be combined with any other offers or discounts except for the Extra 20% off Everything offer with code DEAL. Offer is non-transferable and not valid for cash or cash equivalent. Offer subject to change without notice. No adjustments on previous purchases. Enter promo code at checkout or tap to redeem when available.", "genericCodeId": "648973", "genericCode": "2DAYS"}}, {"bannerContent": {"name": "Image", "type": "sitewide", "isBannerClickable": false, "data": {"largeImg": "/Asset_Archive/GPWeb/content/0018/699/204/assets/033120_PD_2_EC.png?v=2", "smallImg": "/Asset_Archive/GPWeb/content/0018/699/204/assets/033120_PD_2_EC.png?v=2", "imageMap": {"identifier": "map_1", "areaAttributes": [{"shape": "rect", "coords": "8, 2, 636, 365", "plink": "false", "href": "/browse/category.do?cid=8792", "alt": "extra 20% off code deal"}]}, "classes": "promoDrawer__content__item__banner", "altText": "Promo #2", "style": {}}}, "applicationDetails": {"toolType": "tap", "type": "tap", "overlay": "Code will be applied at bag", "message": "tap to apply", "defaultMessage": "tap to apply", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "Extra 20% Off Everything: Excludes gift cards. Offer valid on March 31, 2020 from 12:01am PT to April 1, 2020 11:59pm PT online at gapcanada.ca in Canada only. Discount applies to merchandise only, not to gift cards, applicable taxes, shipping & handling charges. Offer cannot be combined with any other offers or discounts except for the 40% off Everything offer with code 2DAYS. Offer is non-transferable and not valid for cash or cash equivalent. Offer subject to change without notice. No adjustments on previous purchases. Enter promo code at checkout or tap to redeem when available.", "genericCodeId": "649033", "genericCode": "DEAL"}}, {"bannerContent": {"name": "Image", "type": "sitewide", "isBannerClickable": false, "data": {"largeImg": "/Asset_Archive/GPWeb/content/0018/699/204/assets/033112_PD_3_EC.png?v=2", "smallImg": "/Asset_Archive/GPWeb/content/0018/699/204/assets/033112_PD_3_EC.png?v=2", "imageMap": {"identifier": "map_2", "areaAttributes": [{"shape": "rect", "coords": "10, 6, 590, 321", "plink": "false", "alt": "free shipping on orders of $25+ (and free returns!)"}]}, "classes": "promoDrawer__content__item__banner", "altText": "Promo #3", "style": {}}}, "applicationDetails": {"toolType": "auto", "type": "auto", "message": "", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "Free Shipping is valid online only at bananarepublic.ca, oldnavy.ca and gapcanada.ca on purchases of $25 or more in the same order. Eligible customers must select the FREE ($25 min) option in Checkout to activate the free shipping. Qualifying amount applies to merchandise only, not Giftcards, packaging, applicable taxes or shipping & handling charges. Offer is good for the order’s first “ship-to” address anywhere in Canada. Please allow up to fourteen (14) days for shipments to remote locations. If you choose another shipping option, additional charges will apply. No adjustments on previous purchases. Offer is non-transferable and subject to change without notice. Not valid on international shipments. Actual delivery times are subject to change. The delivery times displayed in Checkout at the time of your order will reflect when you should expect to receive an order placed on that day and will appear in your order confirmation email.", "genericCodeId": "646333", "genericCode": ""}}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "style": {"desktop": {"fontSize": "14px", "fontWeight": "400"}}, "text": "{--! headerText !--}"}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "style": {"desktop": {"fontSize": "14px", "fontWeight": "400", "margin": "0 0 0 6px"}}, "text": "{--! subHeaderText !--}"}}, {"name": "Image", "type": "sitewide", "data": {"smallImg": "{--! iconUrl !--}", "largeImg": "{--! iconUrl !--}", "altText": "{--! iconAltText !--}", "style": {"desktop": {"position": "absolute", "right": "calc(49% - 370px)"}, "mobile": {}}}}], "style": {"display": "inline-flex"}}}}}], "style": {"width": "100%", "height": "100%", "backgroundColor": "#0057B8", "alignItems": "center"}}}, "mobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "style": {"mobile": {"fontSize": ".8em"}}, "text": "{--! headerText !--}"}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}"}}], "style": {"flexDirection": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transition-duration": ".2s", "transition-timing-function": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "iconUrl": "/Asset_Archive/GPWeb/content/promo_drawer/assets/minus-white.svg", "iconAltText": "open drawer toggle icon", "linkWithModalDisplayStyle": "none", "subHeaderText": "(3 available)"}, "closedState": {"headerText": "flash sale!", "subHeaderText": "40% off everything + extra 20% off", "iconUrl": "/Asset_Archive/GPWeb/content/promo_drawer/assets/plus-white.svg", "iconAltText": "closed drawer toggle icon"}}, "pd_id": "pdid_1534541049574"}}}, "home": {"instanceName": "HomePageCA-EC_032520", "ciid": "18605637", "type": "home", "name": "HomeMultiSimple", "components": [{"type": "builtin", "name": "div", "experimentRunning": false, "instanceName": "dpg-banner", "instanceDesc": "DPG-placeholder-1", "useGreyLoadingEffect": false, "desktop": {"height": "0"}, "mobile": {"height": 0}, "data": {"lazy": false, "defaultHeight": {"large": "0px", "small": "0px"}, "isVisible": {"large": true, "small": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"width": 0, "height": 0}}}}, {"instanceName": "optly-placeholder-1", "instanceDesc": "032520_UNREC1", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "500px", "large": "417px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 437, "margin": "0 auto 3.25rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "10px auto 4.5rem", "maxWidth": "1920px", "width": "100%"}}, "desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto 3.25rem", "maxWidth": "640px"}, "desktop": {"margin": "10px auto 4.5rem", "maxWidth": "1920px", "width": "100%"}}, "data": {"containerStyle": {"mobile": {}, "desktop": {"maxWidth": "1920px"}}, "background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_1/G28969_GOL_SPRING_KHAKIS_HP_MOB.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_1/G28969_GOL_SPRING_KHAKIS_HP_DESK.jpg", "altText": "men and women's relearn the khaki"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_1/G28969_GOL_SPRING_KHAKIS_HP_MOB.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_1/G28969_GOL_SPRING_KHAKIS_HP_DESK.svg", "altText": "men and women's relearn the khaki", "link": {"url": "/browse/category.do?cid=1153129#pageId=0&department=136&mlink=1153129,18605637,HP_HERO1_Image"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"right": "0px", "padding": "0", "top": "70%", "width": "100%", "textAlign": "center"}}, "links": {"style": {"mobile": {"color": "#122344", "fontSize": "1rem", "fontWeight": "700", "letterSpacing": "1px", "textAlign": "center", "backgroundColor": "#FFFFFF"}, "desktop": {"backgroundColor": "rgb(255, 255, 255)", "borderWidth": "0px", "color": "rgb(18, 35, 68)", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "1px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Women", "url": "/browse/category.do?cid=1153129#pageId=0&department=136&mlink=1153129,18605637,HP_HERO1_CTA"}, {"text": "Men", "url": "/browse/category.do?cid=1152975#pageId=0&department=75&mlink=1152975,18605637,HP_HERO1_CTA"}, {"text": "Watch the Video", "url": "/"}]}, "detailsLink": {"content": {"modalSize": "max", "linkText": "Watch the Video", "normalTextColor": "transparent", "linkTextColor": "transparent", "url": "https://www.youtube.com/embed/0-K7vEpX9MI?autoplay=1&rel=0", "modalCloseButtonAriaLabel": "Close Video Pop-up"}, "style": {"desktop": {"overflow": "hidden", "position": "absolute", "left": "51%", "width": "170px", "fontSize": "1000px", "height": "12%", "color": "black", "transform": "translateX(0%)", "top": "69%", "cursor": "pointer", "display": "block"}, "mobile": {"position": "absolute", "bottom": "0", "left": "0", "width": "100%", "height": "7.5%", "overflow": "hidden", "fontSize": "50px", "color": "transparent"}}, "modalIframeStyle": {"desktop": {"height": "37.5rem"}}}}}]}}}}, {"instanceName": "optly-placeholder-2", "instanceDesc": "032520_UNREC2", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "500px", "large": "417px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 437, "margin": "0 auto 3.25rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 4.5rem", "maxWidth": "1920px", "width": "100%"}}, "desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "SVGOverlay", "type": "home", "tileStyle": {"mobile": {"margin": "0 auto 3.25rem", "maxWidth": "640px"}, "desktop": {"margin": "0 auto 4.5rem", "maxWidth": "1920px", "width": "100%"}}, "data": {"containerStyle": {"mobile": {}, "desktop": {"maxWidth": "1920px"}}, "background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_2/G29084_KTB_NewArrivalsSpringFlow3_HPMain_MOB.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_2/G29084_KTB_NewArrivalsSpringFlow3_HPMain_DESK.jpg", "altText": "Nice Day for New Arrivals - children's new arrivals"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_2/G29084_KTB_NewArrivalsSpringFlow3_HPMain_MOB.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_2/G29084_KTB_NewArrivalsSpringFlow3_HPMain_DESK.svg", "altText": "Nice Day for New Arrivals - children's new arrivals", "link": {"url": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=63896,18605637,HP_HERO2_Image"}}, "linksContainerStyle": {"desktop": {"padding": "0", "left": "8%", "bottom": "0%", "zIndex": "3", "transform": "translateX(0%)", "width": "22%", "display": "grid", "height": "19%"}, "mobile": {"padding": "0"}}, "links": {"type": "dropdown", "style": {"desktop": {"border": "none"}, "mobile": {"lineHeight": "20px", "padding": ".75rem 0"}}, "content": [{"heading": {"text": "New Arrivals"}, "submenu": [{"text": "Girls", "href": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=63895,18605637,HP_HERO2_CTA"}, {"text": "Boys", "href": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=63896,18605637,HP_HERO2_CTA"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=63863,18605637,HP_HERO2_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1016138#pageId=0&department=165&mlink=1016138,18605637,HP_HERO2_CTA"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=14249#pageId=0&department=166&mlink=14249,18605637,HP_HERO2_CTA"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=95575,18605637,HP_HERO2_CTA"}]}]}}}]}}}}, {"instanceName": "certona_hp2", "instanceDesc": "111319_WePickedTheseForYou", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "275px", "large": "260px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "Recommendations", "type": "home", "tileStyle": {"mobile": {"boxSizing": "border-box", "margin": "0 auto 2rem", "maxWidth": "640px", "paddingLeft": "0rem", "paddingRight": "0.5rem", "width": "100%"}, "desktop": {"boxSizing": "border-box", "margin": "0 auto 4.5rem", "maxWidth": "1280px", "paddingLeft": "0.5rem", "paddingRight": "0.5rem", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome1_rr", "displayTitle": false, "certonaTitle": {"title": "", "style": {"mobile": {"color": "#122344", "display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "fontSize": "1.5rem", "fontWeight": "400", "marginBottom": "0.5rem", "paddingLeft": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}, "desktop": {"paddingLeft": "0.5rem", "color": "#122344", "display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "fontSize": "1.5rem", "fontWeight": "400", "marginBottom": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}}}, "layout": "carousel", "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "priceFlag": false, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0017/748/073/assets/certona_arrows/gp_certona_gfol-style-arrow3.png", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0017/748/073/assets/certona_arrows/gp_certona_gfol-style-arrow3.png", "nextArrowAlt": "Next", "productTextStyles": {"productTitle": {"style": {"color": "#666666", "textAlign": "left", "fontSize": ".75rem"}}, "productPrice": {"style": {"color": "#000000", "float": "left", "fontSize": ".75rem"}}, "productSalePrice": {"style": {"color": "red", "float": "left", "fontSize": ".75rem"}}, "size": {"width": "90%", "height": "150px"}}, "productCardStyles": {"style": {}}, "gridLayout": {}}}]}}}}, {"instanceName": "optly-placeholder-3", "instanceDesc": "032520_UNREC3", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "500px", "large": "285px"}, "placeholderSettings": {"useGreyLoadingEffect": false, "mobile": {"backgroundColor": "#ccc", "height": 500, "margin": "0 auto 3.25rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 630, "margin": "0 auto 4.5rem", "maxWidth": "1820px", "width": "100%"}}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 auto", "maxWidth": "1920px"}, "components": [{"instanceDesc": "032520_UNREC3_sub1", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto 3.25rem", "maxWidth": "640px", "width": "100%"}, "desktop": {"boxSizing": "border-box", "marginBottom": "4.5rem", "width": "50%", "flex": "1 1 auto", "padding": "0px 1% 0px 2%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "SVGOverlay", "type": "sitewide", "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_3/sub_1/G28782_CR0204_WOM_FeminineTops_HPSub_DESK.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_3/sub_1/G28782_CR0204_WOM_FeminineTops_HPSub_DESK.jpg", "altText": "All the eyelet"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_3/sub_1/G28782_CR0204_WOM_FeminineTops_HPSub_DESK.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_3/sub_1/G28782_CR0204_WOM_FeminineTops_HPSub_DESK.jpg", "altText": "All the eyelet", "link": {"url": "/browse/category.do?cid=8792&scrollToCid=1152759&department=136&mlink=5058,18513101,HP_HERO3_Image"}}}}, {"name": "SVGOverlay", "type": "sitewide", "data": {"containerStyle": {"desktop": {"width": "100%"}}, "background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_3/sub_1/G28782_CR0204_WOM_FeminineTops_HPSub_MOB.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_3/sub_1/G28782_CR0204_WOM_FeminineTops_HPSub_DESK.svg", "altText": "All the eyelet"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_3/sub_1/G28782_CR0204_WOM_FeminineTops_HPSub_MOB.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/605/637/assets/unrec_3/sub_1/G28782_CR0204_WOM_FeminineTops_HPSub_DESK.svg", "altText": "All the eyelet", "link": {"url": "/browse/category.do?cid=8792&scrollToCid=1152759&department=136&mlink=5058,18513101,HP_HERO3_Image"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"right": "0px", "padding": "0", "top": "0", "zIndex": "8", "height": "100%", "display": "flex", "flexDirection": "column", "justifyContent": "center"}}, "links": {"style": {"mobile": {"color": "#122344", "fontSize": "1rem", "fontWeight": "700", "letterSpacing": "1px", "textAlign": "center", "backgroundColor": "#FFFFFF"}, "desktop": {"color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "1px", "textAlign": "center", "paddingRight": "0px"}}, "content": [{"text": "Check Out The Pretty Details", "url": "/browse/category.do?cid=8792&scrollToCid=1152759&department=136&mlink=5058,18513101,HP_HERO3_CTA"}]}}}]}}}}, {"instanceDesc": "031220_UNREC2_sub2", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto 3.25rem", "maxWidth": "640px", "width": "100%"}, "desktop": {"boxSizing": "border-box", "marginBottom": "4.5rem", "width": "50%", "flex": "1 1 auto", "padding": "0px 2% 0px 1%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "SVGOverlay", "type": "sitewide", "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/620/313/assets/unrec_2/sub_2/G28575_CR0218_MEN_SleevesBreak_HPSub.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/620/313/assets/unrec_2/sub_2/G28575_CR0218_MEN_SleevesBreak_HPSub.jpg", "altText": "Short Sleeve Season"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/620/313/assets/unrec_2/sub_2/G28575_CR0218_MEN_SleevesBreak_HPSub.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/620/313/assets/unrec_2/sub_2/G28575_CR0218_MEN_SleevesBreak_HPSub.jpg", "altText": "Short Sleeve Season", "link": {"url": "/browse/category.do?cid=15043&pageId=0&department=75&scrollToCid=1030362&mlink=15043,18576837,HP_HERO4_Image#style=1030362"}}}}, {"name": "SVGOverlay", "type": "sitewide", "data": {"containerStyle": {"desktop": {"width": "100%"}}, "background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/620/313/assets/unrec_2/sub_2/G28575_CR0218_MEN_SleevesBreak_HPSub_MOB.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/620/313/assets/unrec_2/sub_2/G28575_CR0218_MEN_SleevesBreak_HPSub_DESK.svg", "altText": "Short Sleeve Season"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/620/313/assets/unrec_2/sub_2/G28575_CR0218_MEN_SleevesBreak_HPSub_MOB.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/620/313/assets/unrec_2/sub_2/G28575_CR0218_MEN_SleevesBreak_HPSub_DESK.svg", "altText": "Short Sleeve Season", "link": {"url": "/browse/category.do?cid=15043&pageId=0&department=75&scrollToCid=1030362&mlink=15043,18576837,HP_HERO4_Image#style=1030362"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"right": "0px", "padding": "0", "top": "0", "zIndex": "8", "height": "100%", "display": "flex", "flexDirection": "column", "justifyContent": "center"}}, "links": {"style": {"mobile": {"color": "#122344", "fontSize": "1rem", "fontWeight": "700", "letterSpacing": "1px", "textAlign": "center", "backgroundColor": "#FFFFFF"}, "desktop": {"color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "1px", "textAlign": "center", "paddingRight": "0px"}}, "content": [{"text": "shop men's shirts", "url": "/browse/category.do?cid=15043&pageId=0&department=75&scrollToCid=1030362&mlink=15043,18576837,HP_HERO4_CTA#style=1030362"}]}}}]}}}}]}}}}, {"instanceName": "optly-vcn", "instanceDesc": "CatNav_021020", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"defaultHeight": {"small": "400px", "large": "413px"}, "placeholderSettings": {"useGreyLoadingEffect": false, "mobile": {"backgroundColor": "#ccc", "height": 836, "margin": "0 auto 3rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 570, "margin": "0 auto 4rem", "maxWidth": "1400px", "width": "100%"}}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"boxSizing": "border-box", "display": "flex", "flexFlow": "row wrap", "justifyContent": "center", "margin": "0 auto 2rem", "maxWidth": "1185px", "width": "100%"}, "components": [{"instanceDesc": "8grid-item1", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_1.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_1.jpg", "altText": "Shop Women"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_1.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_1.jpg", "altText": "Shop Women", "link": {"url": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,18420662,HP_VDN_W_G28771_Image"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "8"}}, "links": {"type": "dropdown", "style": {"mobile": {"color": "#122344", "borderColor": "#ccc", "borderTopWidth": "0", "fontSize": "12px", "fontWeight": "700", "lineHeight": "20px", "padding": "6px 0 7px", "textAlign": "center"}, "desktop": {"border": "none", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "paddingTop": "7px", "textAlign": "center"}}, "content": [{"heading": {"text": "Women"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,18420662,HP_VDN_W_G28771_CTA"}, {"text": "Maternity", "href": "/browse/category.do?cid=11437#pageId=0&department=136&mlink=5058,18420662,HP_VDN_MAT_G28771_CTA"}, {"text": "Body", "href": "/browse/category.do?cid=1140272#pageId=0&department=136&mlink=5058,18420662,HP_VDN_BODY_G28771_CTA"}, {"text": "GapFit", "href": "/browse/category.do?cid=1117374#pageId=0&department=136&mlink=5058,18420662,HP_VDN_GAPFIT_G28771_CTA"}]}]}}}, {"instanceDesc": "8grid-item2", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_2.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_2.jpg", "altText": "Men"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_2.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_2.jpg", "altText": "Men", "link": {"url": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,18420662,HP_VDN_M_G28771_Image"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "7"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Men", "href": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,18420662,HP_VDN_M_G28771_CTA"}]}}}, {"instanceDesc": "8grid-item3", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_3.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_3.jpg", "altText": "Girls"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_3.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_3.jpg", "altText": "Girls", "link": {"url": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,18420662,HP_VDN_G_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "6"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Girls", "href": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,18420662,HP_VDN_G_G28771_CTA"}]}}}, {"instanceDesc": "8grid-item4", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_4.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_4.jpg", "altText": "Boys"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_4.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_4.jpg", "altText": "Boys", "link": {"url": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,18420662,HP_VDN_B_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "5"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Boys", "href": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,18420662,HP_VDN_B_G28771_CTA"}]}}}, {"instanceDesc": "8grid-item5", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_5.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_5.jpg", "altText": "<PERSON>ler Girl"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_5.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_5.jpg", "altText": "<PERSON>ler Girl", "link": {"url": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,18420662,HP_VDN_TG_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "4"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,18420662,HP_VDN_TG_G28771_CTA"}]}}}, {"instanceDesc": "8grid-item6", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_6.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_6.jpg", "altText": "<PERSON><PERSON>"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_6.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_6.jpg", "altText": "<PERSON><PERSON>", "link": {"url": "/browse/category.do?cid=1016138#pageId=0&department=165&mlink=5058,18420662,HP_VDN_TB_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "3"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1016138#pageId=0&department=165&mlink=5058,18420662,HP_VDN_TB_G28771_CTA"}]}}}, {"instanceDesc": "8grid-item7", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_7.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_7.jpg", "altText": "Baby Girl"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_7.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_7.jpg", "altText": "Baby Girl", "link": {"url": "/browse/category.do?cid=14249#pageId=0&department=166&mlink=5058,18420662,HP_VDN_BG_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "2"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Baby Girl", "href": "/browse/category.do?cid=14249#pageId=0&department=166&mlink=5058,18420662,HP_VDN_BG_G28771_CTA"}]}}}, {"instanceDesc": "8grid-item8", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_8.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_8.jpg", "altText": "Baby Boy"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_8.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/420/662/assets/VCN/VCN_8.jpg", "altText": "Baby Boy", "link": {"url": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,18420662,HP_VDN_BB_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "2"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Baby Boy", "href": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,18420662,HP_VDN_BB_G28771_CTA"}]}}}]}}}}, {"instanceName": "promo2", "instanceDesc": "Promo2_030320_GapCash", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "319px", "large": "88px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"maxWidth": "1920px", "margin": "0 auto"}, "components": [{"name": "SVGOverlay", "type": "sitewide", "data": {"containerStyle": {"mobile": {"maxWidth": "640px", "margin": "0 auto 3.25rem"}, "desktop": {"maxWidth": "1920px", "margin": "0 auto 4.5rem"}}, "background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/579/776/assets/promos/022520_ALL_GapCashEarnR28_HPbanner_MOB_US.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/579/776/assets/promos/121019_ALL_GapCashEarnR26_HPbanner_DESK_US.svg", "altText": "GapCash. Earn at Gap & Gap Factory."}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/579/776/assets/promos/022520_ALL_GapCashEarnR28_HPbanner_MOB_US.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/579/776/assets/promos/121019_ALL_GapCashEarnR26_HPbanner_DESK_US.svg?v=2", "altText": "1. Start Here: Earn $20 in GapCash for every $50+ you spend. New: Now you can earn every day! 2. Go Big: Earn up to $120 in GapCash per order! 3. Cash Out: Redeem your GapCash & combine with other offers 6/4-6/8.", "link": {"url": "/browse/info.do?cid=1000194&mlink=5058,18579776,HP_Banner_GapCashRedeem_LearnMore"}}, "linksContainerStyle": {"mobile": {"position": "absolute", "top": "84.5%", "left": "50%", "padding": "0", "transform": "translateX(-50%)", "zIndex": "2"}, "desktop": {"top": "64%", "left": "72.6%", "padding": "0", "transform": "translateY(-50%)", "zIndex": "2"}}, "links": {"style": {"mobile": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "1rem", "fontWeight": "700", "letterSpacing": "1px", "padding": "0.6rem 1rem", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "1px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Learn More", "href": "/browse/info.do?cid=1000194&mlink=5058,18579776,HP_Banner_GapCashRedeem_LearnMore"}]}}}]}}}}]}, "meta.title.override": "Shop Women, Men, Mat<PERSON><PERSON>, Baby & Kids Clothes Online", "type": "meta", "brand": "gap", "meta.description": "Shop womens, mens, maternity, kids & baby clothes at Gap online and find the perfect pair of jeans, t-shirts, dresses and more for the whole family."}