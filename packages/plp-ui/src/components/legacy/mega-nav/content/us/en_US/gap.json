{"sitewide": {"footer": {"footer-ciid": "28917530", "footer-desc": "020122_<PERSON><PERSON><PERSON>ooter", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "sitewide", "components": [{"name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "1418px", "large": "799px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "2.5rem auto 0", "maxWidth": "1920px", "position": "relative", "width": "100%"}, "components": [{"name": "Footer", "type": "sitewide", "data": {"socialLinks": [{"to": "https://www.facebook.com/gap/", "text": "Follow Gap on Facebook"}], "emailRegistration": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<h3>Sign Up for Email <span style=\"white-space:nowrap\">&amp; Get 25% Off<span>*</span></span></h3>"}}, "emailPlaceholderText": "Enter your email address", "submitButtonText": "Join", "submitButtonOptions": {"mobile": {"className": "signButton label-a sds_button_primary", "variant": "border", "size": "medium", "fullWidth": true, "crossBrand": false, "color": "white", "style": {"backgroundColor": "transparent", "borderColor": "#FFFFFF"}}, "desktop": {"className": "signButton label-a sds_button_primary", "variant": "border", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "white", "style": {"backgroundColor": "transparent", "borderColor": "#FFFFFF"}}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<p style=\"font-size: 10px;\"><span>*</span>Valid for first-time registrants only &amp; applies to reg. price items only. <a onclick=\"return contentItemLink(this,'','CS_Footer_PrivacyPolicy');\" href=\"https://corporate.gapinc.com/en-us/consumer-privacy-policy\" style=\"text-decoration: underline; white-space: nowrap;\">Privacy Policy</a>", "style": {"textTransform": "uppercase"}}}}, "marketingBannerLayout": {"name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto", "maxWidth": "1260px", "position": "relative"}, "components": [{"instanceName": "footer_overrides", "name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<style>.email-registration__wrapper{align-items:flex-start;background-color:#2b2b2b}.email-registration__wrapper>div{margin:0 auto;max-width:608px;width:100%}.email-registration__title{font-size:32px;max-width:100%;padding:0;text-align:left}.email-registration__disclaimer{padding-left:0}.email-registration__form-email{margin-left:0}.email-registration__form-email input[type=email]{margin-top:28px}@media only screen and (min-width:1024px){.gap-footer .footer-container-wrapper.footer-container-wrapper{max-width:100%}.footer-container-wrapper>div:first-child{background-color:#2b2b2b;display:block}.email-registration__wrapper{align-items:center;background-color:transparent;flex-direction:row;flex-wrap:nowrap;margin:0 auto;max-width:1120px}.email-registration__wrapper>div:first-child{max-width:100%}.email-registration__wrapper>div:last-child{flex-grow:1;max-width:400px}.email-registration__form-email{padding-bottom:12px}.email-registration__form-email input[type=email]{margin-top:0}}@media only screen and (max-width:767px){.footer-container-wrapper>div:nth-child(2)>div{box-sizing:border-box;margin:0 auto;max-width:640px;padding-left:16px;padding-right:16px;text-align:left;width:100%}.footer-container-wrapper>div:nth-child(2) div>a::before{content:'GapGood Rewards';display:block;font-size:1.375em;font-weight:500;line-height:1;margin:0 0 .5rem;text-align:left;text-transform:uppercase}.footer-container-wrapper>div:nth-child(2) div>a img{display:none}.footer-container-wrapper>div:nth-child(2) li{padding:2px 0;text-align:left;width:100%}.footer-container-wrapper>div:nth-child(2) li:last-child{padding-top:12px}}.medallia-feedback-wrapper{box-sizing:border-box;margin:0 auto;max-width:640px;padding:0 16px 16px}.medallia-feedback-wrapper button{box-sizing:border-box;margin:0;width:100%}.footer-legal__wrapper{margin:0 auto;max-width:640px}.footer-copyright-section{background-color:#2b2b2b;font-size:14px;text-transform:uppercase}.footer-copyright-section a:hover{text-decoration:underline}@media only screen and (min-width:768px){.footer-copyright-section{background-color:transparent;border-top-width:2px;color:#2b2b2b}.footer-copyright-section button{color:inherit}}@media only screen and (min-width:1024px){.footer-legal__wrapper{max-width:920px}}</style>"}}, {"instanceName": "BOPIS_footer", "instanceDesc": "032921 - Created", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "2px 0 0", "margin": "0 auto", "maxWidth": "640px", "width": "100%"}, "desktop": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 0 2px", "margin": "0 auto", "maxWidth": "1920px", "width": "100%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"position": "relative"}, "components": [{"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1161798&mlink=55278,********,LP_BOPIS_footer_CTA"}, "style": {"alignItems": "center", "display": "flex", "flexDirection": "row", "flexWrap": "nowrap", "fontSize": "min(max(18px, calc(1.125rem + ((1vw - 3.75px) * 0.7653))), 21px)", "lineHeight": "1", "min-height": "0vw", "padding": "16px", "textTransform": "uppercase"}, "desktopStyle": {"fontSize": "min(max(16px, calc(1rem + ((1vw - 7.68px) * 0.6076))), 23px)"}, "components": [{"name": "img", "type": "builtin", "data": {"props": {"alt": "null", "src": "/Asset_Archive/GPWeb/content/0028/917/530/assets/footer_gap_shopping_bag--darkgrey.svg"}, "style": {"marginRight": "8px", "width": "24px"}}}, {"name": "div", "type": "builtin", "data": {"style": {"color": "#2b2b2b"}, "desktopStyle": {"marginRight": "1rem"}, "components": ["Buy Online, Pick Up In Store, or Curbside."]}}, {"name": "div", "type": "builtin", "data": {"style": {"color": "#2b2b2b", "display": "none", "marginRight": "8px"}, "desktopStyle": {"display": "initial"}, "components": ["Learn More"]}}, {"name": "img", "type": "builtin", "data": {"props": {"alt": "null", "src": "/Asset_Archive/GPWeb/content/0028/917/530/assets/arrow--darkgrey.svg"}, "style": {"display": "none", "height": "16px"}, "desktopStyle": {"display": "initial"}}}]}}]}}}}, {"instanceName": "edfs_footer", "instanceDesc": "072021 - Updated Copy for ILP", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "2px 0", "margin": "0 auto", "maxWidth": "640px", "width": "100%"}, "desktop": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 0 2px", "margin": "0 auto", "maxWidth": "1920px", "width": "100%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Footer EDFS Wrapper", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"maxWidth": "100%"}, "desktop": {"maxWidth": "100%"}}, "data": {"style": {"alignItems": "center", "color": "#2b2b2b", "display": "flex", "flexWrap": "wrap", "fontSize": "min(max(18px, calc(1.125rem + ((1vw - 3.75px) * 0.7653))), 21px)", "lineHeight": "1", "min-height": "0vw", "padding": "16px"}, "desktopStyle": {"flexWrap": "nowrap", "fontSize": "min(max(16px, calc(1rem + ((1vw - 7.68px) * 0.6076))), 23px)", "justifyContent": "space-between"}, "components": [{"instanceDesc": "Left Side", "name": "div", "type": "builtin", "data": {"style": {"marginBottom": "4px", "textTransform": "uppercase"}, "desktopStyle": {"lineHeight": "1.125", "marginBottom": "0", "marginRight": "1rem", "min-width": "240px"}, "components": ["4 Brands 1 Easy Checkout."]}}, {"instanceDesc": "Right Side", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexWrap": "wrap", "lineHeight": "1.125", "marginTop": "10px"}, "desktopStyle": {"justifyContent": "flex-end", "marginTop": "0", "maxWidth": "70%"}, "components": [{"name": "span", "type": "builtin", "data": {"style": {"whiteSpace": "nowrap"}, "components": [{"name": "span", "type": "builtin", "data": {"style": {"textTransform": "uppercase"}, "components": ["Rewards Members "]}}, {"name": "span", "type": "builtin", "data": {"components": ["get "]}}, {"name": "span", "type": "builtin", "data": {"style": {"textTransform": "uppercase"}, "components": ["Free Shipping "]}}]}}, {"name": "span", "type": "builtin", "data": {"style": {"whiteSpace": "nowrap"}, "desktopStyle": {"marginLeft": "5px"}, "components": [{"name": "span", "type": "builtin", "data": {"components": ["on all orders $50+ "]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://secure-www.gap.com/my-account/sign-in"}, "style": {"textDecoration": "underline"}, "components": ["Sign In"]}}, {"name": "span", "type": "builtin", "data": {"components": [" or "]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://secure-www.gap.com/my-account/sign-in"}, "style": {"textDecoration": "underline"}, "components": ["Join"]}}]}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"marginLeft": "auto"}, "desktopStyle": {"marginLeft": "10px"}}, "ctaList": {"mobilePositionAboveContent": false, "ctas": [{"composableButtonData": {"children": "Details", "font": "primary", "style": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "10px", "fontWeight": "400", "lineHeight": "1", "padding": "4px", "textDecoration": "underline", "textTransform": "uppercase", "&:hover": {"color": "#333333"}}, "desktopStyle": {"paddingRight": "0"}}, "modalData": {"closeButtonAriaLabel": "Close Pop-up", "modalSize": "max", "iframeData": {"title": "SHIPPING & RETURNS", "src": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-GP-Narvar_07202021.html?v=0", "tid": "FooterEDFS_Details", "height": "500px"}}}]}}}]}}]}}]}}}}, {"instanceName": "footer_social-media-links", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto", "maxWidth": "640px", "padding": "12px 0", "width": "100%"}, "desktop": {"display": "none", "margin": "0 auto", "maxWidth": "1920px"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"alignItems": "baseline", "display": "flex", "flexDirection": "row", "flexWrap": "nowrap"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"margin": "0 auto", "maxWidth": "60px", "width": "40%"}}, "background": {"image": {"alt": "Instagram", "srcUrl": "/Asset_Archive/GPWeb/content/0028/917/530/assets/footer_social-icon_instagram--darkgrey.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/917/530/assets/footer_social-icon_instagram--darkgrey.svg"}, "linkData": {"to": "https://www.instagram.com/gap/", "target": "_blank"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"margin": "0 auto", "maxWidth": "60px", "width": "40%"}}, "background": {"image": {"alt": "Twitter", "srcUrl": "/Asset_Archive/GPWeb/content/0028/917/530/assets/footer_social-icon_twitter--darkgrey.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/917/530/assets/footer_social-icon_twitter--darkgrey.svg"}, "linkData": {"to": "https://twitter.com/gap?lang=en", "target": "_blank"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"margin": "0 auto", "maxWidth": "60px", "width": "40%"}}, "background": {"image": {"alt": "Facebook", "srcUrl": "/Asset_Archive/GPWeb/content/0028/917/530/assets/footer_social-icon_facebook--darkgrey.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/917/530/assets/footer_social-icon_facebook--darkgrey.svg"}, "linkData": {"to": "https://www.facebook.com/gap/", "target": "_blank"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"margin": "0 auto", "maxWidth": "60px", "width": "40%"}}, "background": {"image": {"alt": "YouTube", "srcUrl": "/Asset_Archive/GPWeb/content/0028/917/530/assets/footer_social-icon_youtube--darkgrey.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/917/530/assets/footer_social-icon_youtube--darkgrey.svg"}, "linkData": {"to": "https://www.youtube.com/user/Gap/featured", "target": "_blank"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"margin": "0 auto", "maxWidth": "60px", "width": "40%"}}, "background": {"image": {"alt": "TikTok", "srcUrl": "/Asset_Archive/GPWeb/content/0028/917/530/assets/footer_social-icon_tiktok--darkgrey.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/917/530/assets/footer_social-icon_tiktok--darkgrey.svg"}, "linkData": {"to": "https://vm.tiktok.com/ZMeDk5Wvw", "target": "_blank"}}}}]}}}}, {"instanceName": "footer_bottom-section", "instanceDesc": "Download Our App", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"backgroundColor": "#2b2b2b", "textAlign": "center"}, "desktop": {"display": "none"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"name": "a", "type": "builtin", "data": {"props": {"href": "https://gap.onelink.me/9QBP/12b7d83a"}, "style": {"color": "#FFFFFF", "display": "inline-block", "fontSize": "16px", "padding": "16px", "textTransform": "uppercase", "&:hover": {"textDecoration": "underline"}}, "components": ["Download Our App"]}}]}}}}]}}}}, "customerSupportLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"mobilePositionAboveContent": false, "style": {"margin": "16px auto", "maxWidth": "640px", "a, > div, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "justifyContent": "flex-start", "textAlign": "left"}, "a, button": {"fontWeight": "500", "padding": "6px 16px"}, "a": {"width": "100%"}, "ul": {"boxShadow": "none"}, "li": {"borderWidth": "0", "padding": "0", "textTransform": "none", "a": {"fontWeight": "400", "paddingLeft": "24px"}}}, "ctas": [{"composableButtonData": {"children": "Store Locator"}, "linkData": {"to": "/stores"}}, {"composableButtonData": {"children": "Customer Service"}, "linkData": {"to": "/customerService/info.do?cid=2136"}}, {"composableButtonData": {"children": "Orders & Returns"}, "linkData": {"to": "/my-account/order-history"}}, {"composableButtonData": {"children": "Shipping"}, "linkData": {"to": "/customerService/info.do?cid=81265&cs=shipping_and_handling&mlink=55278,********,CS_Footer_Shipping&clink=********"}}, {"buttonDropdownData": {"heading": {"text": "Gift Cards"}, "submenu": [{"text": "Buy eGift Cards", "href": "http://gap.cashstar.com/gift-card/buy/?ref=gap1", "target": "_blank"}, {"text": "Buy Gift Cards", "href": "/customerService/info.do?cid=2116&mlink=55278,********,Footer_1_GC&clink=********"}]}}, {"buttonDropdownData": {"heading": {"text": "Gap Good Rewards"}, "submenu": [{"text": "My Points and Rewards", "href": "/loyalty/customer-value"}, {"text": "Explore Benefits", "href": "/customerService/info.do?cid=1099008"}, {"text": "Pay Credit Card Bill", "href": "https://gap.syf.com/login/", "target": "_blank"}, {"text": "Activate Credit Card", "href": "https://gap.syf.com/activate/load?clientId=gap&langId=en", "target": "_blank"}, {"text": "Join Gap Good Rewards — it’s Free", "href": "/my-account/sign-in"}]}}, {"composableButtonData": {"children": "Careers"}, "linkData": {"to": "https://jobs.gapinc.com/gap-home", "target": "_blank"}}, {"composableButtonData": {"children": "Gap Sustainability"}, "linkData": {"to": "https://www.gapinc.com/en-us/values/sustainability", "target": "_blank"}}, {"buttonDropdownData": {"heading": {"text": "Shop Our Other Brands"}, "submenu": [{"text": "Old Navy", "href": "https://oldnavy.gap.com/browse/home.do?ssiteID=GAP"}, {"text": "Banana Republic", "href": "https://bananarepublic.gap.com/browse/home.do?ssiteID=GAP"}, {"text": "Athleta", "href": "https://athleta.gap.com/browse/home.do?ssiteID=GAP"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Desktop CS Footer Links", "name": "div", "type": "builtin", "data": {"style": {"color": "#2b2b2b", "display": "flex", "flexDirection": "row", "flexWrap": "nowrap", "justifyContent": "space-between", "margin": "0 auto", "maxWidth": "920px", "padding": "48px 16px", "> div > *": {"flexBasis": "auto", "flexGrow": "0"}, ".wcd_ftr_header, .wcd_ftr_link, .wcd_ftr_misc": {"marginBottom": "6px"}, ".wcd_ftr_header": {"fontSize": "1.25rem", "textTransform": "uppercase", "@media only screen and (min-width: 1024px)": {"whiteSpace": "nowrap"}}, ".wcd_ftr_link": {"display": "inline-block"}, ".wcd_ftr_link:hover": {"textDecoration": "underline"}}, "components": [{"instanceDesc": "Desktop CS Footer Links - Column 1", "name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "minWidth": "120px", "paddingRight": "16px"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_ftr_header"}, "components": ["About Us"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "https://www.gapinc.com/en-us/careers/gap-careers", "target": "_blank"}, "components": ["Careers"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "https://www.gapinc.com/en-us/values/sustainability", "target": "_blank"}, "components": ["Sustainability"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/browse/info.do?cid=1086537&mlink=55278,********,<PERSON>_Footer_GapforGood"}, "components": ["Gap for Good"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/browse/info.do?cid=1188970"}, "components": ["Gap News"]}}]}}}}, {"instanceDesc": "Desktop CS Footer Links - Column 2", "name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "minWidth": "100px", "paddingRight": "16px"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_ftr_header"}, "components": ["Customer Support"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/stores"}, "components": ["Store Locator"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/customerService/info.do?cid=2136&mlink=55278,********,CS_Footer_CustomerService", "target": "_blank"}, "components": ["Customer Service"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/customerService/info.do?cid=81265&cs=shipping_and_handling&mlink=55278,********,CS_Footer_Shipping", "target": "_blank"}, "components": ["Shipping"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/customerService/info.do?cid=81264&cs=return_policies&mlink=55278,********,CS_Footer_Returns", "target": "_blank"}, "components": ["Returns"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/profile/order_history.do?isNav=true&mlink=55278,********,CS_Footer_TrackYourOrder&clink=********", "target": "_blank"}, "components": ["Track Your Order"]}}, {"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_ftr_misc wcd_ftr_phone"}, "components": ["**************"]}}]}}}}, {"instanceDesc": "Desktop CS Footer Links - Column 3", "name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "minWidth": "275px", "paddingRight": "16px"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_ftr_header"}, "components": ["Gap Good Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/my-account/sign-in?targetURL=/loyalty/customer-value"}, "components": ["My Points And Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/customerService/info.do?cid=1099008"}, "components": ["Explore Benefits"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "https://gap.syf.com/login/", "target": "_blank"}, "components": ["Pay Credit Card Bill"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "https://gap.syf.com/activate/load?clientId=gap&langId=en", "target": "_blank"}, "components": ["Activate Credit Card"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/my-account/sign-in"}, "components": ["Join Gap Good Rewards — it’s Free"]}}]}}}}, {"instanceDesc": "Desktop CS Footer Links - Column 4", "name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "minWidth": "150px"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_ftr_header"}, "components": ["Ways to Shop"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "https://apps.apple.com/us/app/gap/id326347260", "target": "_blank"}, "components": ["Get the App"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/customerService/info.do?cid=1099008"}, "components": ["Gap Good Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/customerService/info.do?cid=81265&cs=payment_options&mlink=55278,********,<PERSON>_Footer_ApplePay"}, "components": ["Apple Pay"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/customerService/info.do?cid=81265&cs=payment_options&mlink=55278,********,CS_Footer_Afterpay"}, "components": ["Afterpay"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/customerService/info.do?cid=81265&cs=payment_options&mlink=55278,********,CS_Footer_Paypal"}, "components": ["PayPal"]}}, {"name": "a", "type": "builtin", "data": {"props": {"className": "wcd_ftr_link", "href": "/customerService/info.do?cid=2116&mlink=55278,********,CS_Footer_Giftcards"}, "components": ["Giftcards"]}}]}}}}]}}]}}}}, "copyRights": {"rows": [[{"text": "© 2022 The Gap, Inc."}, {"text": "Privacy Policy", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy?mlink=55278,********,CS_Footer_PrivacyPolicy&clink=********", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}, {"text": "Do Not Sell My Info", "doNotSell": true}, {"text": "Interest Based Ads", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy#Interest-Based-Ad", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}, {"text": "Your California Privacy Rights", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy?mlink=55278,********,<PERSON>_Footer_Returns_CA_Policy&clink=********#Your-California-Privacy-Rights", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}, {"text": "California Transparency in Supply Chains Act", "to": "https://www.gapinc.com/content/gapinc/html/sustainability/ca-transparency-insupplychainsact.html", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}, {"text": "Terms of Use", "to": "/customerService/info.do?cid=6754"}, {"text": "Careers", "to": "https://jobs.gapinc.com/gap-home", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}, {"text": "Sustainability", "to": "https://www.gapinc.com/en-us/values/sustainability", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}, {"text": "About Gap Inc.", "to": "http://www.gapinc.com/content/gapinc/html/aboutus.html", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}], [{"text": "Americans with Disabilities Act", "to": "/customerService/info.do?cid=1005563"}, {"text": "Gap Inc. Policies", "to": "https://www.gapinc.com/en-us/values/sustainability", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}]]}}}]}}}}]}, "appsflyer-smart-banner": {"instanceDesc": "043021 Appsflyer (this code is in the footer CIID)", "data": {"meta": {"excludePageTypes": ["ShoppingBag", "checkout", "search", "info", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "Information", "CustomerService", "Information"]}}}, "sitewide-hamNavBanner-ciid": "********", "hamburgerNavBanner": {"CIID": "********", "type": "builtin", "name": "div", "data": {"props": {"class": "hamNavMaskShop", "style": {"fontFamily": "Avenir Next,Gap Sans,Helvetica,Arial,Roboto,sans-serif", "background": "#122344", "fontSize": 0, "width": "100%", "display": "flex"}}, "components": [{"type": "builtin", "name": "a", "data": {"props": {"href": "/browse/info.do?cid=1103504&tlink=B2_LPTD_HamNav", "style": {"textDecoration": "none", "color": "#FFFFFF", "width": "100%"}}, "components": [{"type": "builtin", "name": "img", "data": {"props": {"style": {"width": "100%"}, "src": "/Asset_Archive/GPWeb/content/0020/551/001/assets/I8_HAMNAV_TXT.svg", "alt": "lets get personal.my gap- your place for style"}}}]}}]}}, "promodrawer": {"name": "PromoDrawerComponentV2", "type": "sitewide", "sitewide-promodrawer-ciid": "********", "sitewide-promodrawer-desc": "4/18 PD MCM noPZ", "instanceName": "promoDrawer-********", "experimentRunning": false, "data": {"shouldWaitForOptimizely": false, "buildInfo": ["********", "GP"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "info", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "Profile", "LoyaltyValueCenter"], "anchor": "bottom", "collapseOnScroll": true}, "autoFire": "scroll", "disabledAutoFirePageTypes": ["category"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=65179#pageId=0&department=136&mlink=5058,PD_Tile1\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GPWeb/content/0028/964/369/assets/041822_PD1_US.png\" alt=\"spring sale up to 75% off markdowns by taking extra 50% off online only code SALE\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "tap", "overlay": "Applied at checkout", "defaultMessage": "tap to apply", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "874567", "genericCode": "SALE"}, "promoId": "l1mrdeyb"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1127938#pageId=0&department=136&mlink=5058,PD_Tile2\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GPWeb/content/0028/964/369/assets/041822_PD02_US.png\" alt=\"plus extra 10% off reg price styles code GOSHOP exclusions apply\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "tap", "overlay": "Applied at checkout", "defaultMessage": "tap to apply", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "874505", "genericCode": "GOSHOP"}, "promoId": "l1mrfu94"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1127938#pageId=0&department=136&mlink=5058,PD_Tile3\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GPWeb/content/0028/964/369/assets/041822_PD3_US.png\" alt=\"up to 60% off reg price styles\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "874525", "genericCode": ""}, "promoId": "l1mrhav7"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1127938#pageId=0&department=136&mlink=5058,PD_Tile4\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GPWeb/content/0028/964/369/assets/041822_PD4_US.png\" alt=\"gap good rewards exclusive $5 bonus reward when you purchase 4+ items ends 4/22\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile4"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "874485", "genericCode": ""}, "promoId": "l1mria4l"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "style": {"mobile": {"fontSize": ".75em"}, "desktop": {"fontSize": ".75em"}}, "text": "{--! headerText !--}"}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}"}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "iconAltText": "Open icon", "linkWithModalDisplayStyle": "none", "subHeaderText": "(4 available)"}, "closedState": {"headerText": "sale! extra 50% off — for up to 75% off", "subHeaderText": "", "iconAltText": "Closed icon"}, "aria-label": "sale! extra 50% off — for up to 75% off"}, "pd_id": "pdid_l1mrajpm"}}, "sitewide-logo-ciid": "28669369", "sitewide-logo-desc": "02082022-new-logo-color-size", "logo": {"name": "Logo", "type": "sitewide", "altText": "Gap logo", "lightLogoImgPath": "/Asset_Archive/GPWeb/content/0020/452/166/assets/logo/logo_gap--light.svg", "darkLogoImgPath": "/Asset_Archive/GPWeb/content/0028/669/369/assets/logo/Gap_logo_MOB_newV2.svg", "logoImgPath": "/Asset_Archive/GPWeb/content/0028/669/369/assets/logo/Gap_logo_MOB_newV2.svg", "isSquare": true, "className": ""}, "compressedHeader": {"options": {"isEnabled": true, "isStickyEnabled": true, "styleOverrides": {"maxHeaderWidth": 1920, "headerLeftPadding": 0, "headerRightPadding": 0}}, "fullbleed": {"options": {"isMobileVisible": true, "isDesktopVisible": true}, "mobile": {"contrast": "dark"}, "desktop": {"contrast": "dark"}}}, "sitewide-emergbanner_ciid": "20392767", "sitewide-emergbanner_desc": "update_04062021", "mobileemergencybanner": {"CIID": "20392767", "type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "attrition_banner_mob", "type": "builtin", "name": "div", "experimentRunning": true, "isAsyncExperiment": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"instanceName": "dpg_emergency_banner_mob", "type": "builtin", "name": "div", "experimentRunning": true, "redpointExperimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}, "desktopemergencybanner": {"CIID": "20392767", "type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "attrition_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "isAsyncExperiment": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"instanceName": "dpg_emergency_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "redpointExperimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}, "sitewide-popup-ciid": "20492652", "popup": {"type": "builtin", "name": "div", "data": {"components": [{"name": "DynamicModal", "type": "sitewide", "instanceName": "general-email-popup-050421", "experimentRunning": true, "useGreyLoadingEffect": false, "data": {"excludePageTypes": ["Information", "ShoppingBag", "Profile", "profile-ui"], "placeholderSettings": {"useGreyLoadingEffect": false}, "lazy": false, "defaultHeight": "1px", "shouldWaitForOptimizely": true, "closeButtonAriaLabel": "close email sign up modal", "localStorageKey": "emailPopup", "modalSize": "max", "title": "", "style": {"padding": "1rem"}, "breakpoints": ["large"], "layoutData": {"desktop": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0 1rem 2rem 1rem"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "row", "justifyContent": "space-between"}, "components": [{"name": "EmailRegistrationForm", "type": "sitewide", "tileStyle": {"desktop": {"width": "50%"}}, "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {"padding": "3rem 0 0 0", "margin": "0 1rem 0 0"}, "mobile": {"padding": "20px", "margin": "1rem"}}, "targetURL": "/profile/info.do?cid=82638&mlink=5058,8095009,PersadoV8&gapSubmit=true", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:GPUS_HP_emailPopup;BR:NA;ON:NA;PL:NA;AT:NA;BRFS:NA;GPO:NA"]}, "customText": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "sds_sp sds_relative optly-header-container", "style": {}, "html": "<h1 class=\"optly-main-header sds_font--secondary sds_line-height--1-0 sds_font-size--24 optly-main-header\" style=\"color: rgb(0, 0, 0);\">Perk Alert: Get 25% Off</h1>"}}, "subtitle": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "", "style": {}, "html": "<h3 class=\"sds_line-height--1-4 heading-d optly-lighter\" style=\"font-weight:400;\">As a thank you for signing up for emails, take 25% off reg. price styles online. Plus, we're giving you access to exclusive offers, new arrivals, and more.</h3>"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "sds_label-a sds_color--g2", "style": {"marginTop": "8em"}, "html": "<p class=\"sds_line-height--1-4 optly-lighter\" style=\"font-size:0.8em;font-weight:400;color:#666;\">*Valid for first-time registrants & applies to regular price items only.<a href=\"/customerService/info.do?cid=1162487\" style=\"font-size:0.75em;text-decoration:underline;text-transform:uppercase;\"> Details</a></p><p><a href=\"/customerService/info.do?cid=2331\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: block; line-height: 1.3; text-decoration: underline; font-size:0.8em;\">*Privacy Policy</a></p>"}}}, "textInputOptions": {"label": "Your Email Address", "errorMessage": "Please enter a valid email address", "desktop": {"className": "sds_sp", "crossBrand": false, "inverse": false}, "mobile": {"className": "sds_sp", "crossBrand": false, "inverse": false}}, "submitButtonOptions": {"text": "Claim your unique code", "desktop": {"className": "sds_sp_vertical sds_uppercase", "variant": "solid", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "primary"}, "mobile": {"className": "sds_sp_vertical sds_uppercase", "variant": "solid", "size": "small", "fullWidth": true, "crossBrand": false, "color": "primary"}}, "errorNotificationAriaLabel": "Error"}}, {"type": "builtin", "name": "img", "tileStyle": {"desktop": {"maxWidth": "325px", "marginLeft": "0.35rem"}}, "data": {"props": {"href": "/customerService/info.do?cid=1099008&tlink=PopUp_D9_Control", "target": "_blank", "src": "/Asset_Archive/GPWeb/content/0019/235/630/assets/fall2020_email_capture2_520x693[1].jpg", "alt": "Email Sign Up Pop-up"}}}]}}}}]}}, "mobile": {"shouldDisplay": false, "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0 1rem 2rem 1rem"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "row", "justifyContent": "space-between"}, "components": [{"name": "EmailRegistrationForm", "type": "sitewide", "tileStyle": {"desktop": {"width": "50%"}}, "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {}, "mobile": {}}, "targetURL": "/profile/info.do?cid=82637&mlink=5151,8980857,persado_signup_CONTROL", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:NA;ON:ON_Persado8;PL:NA;AT:NA;BRFS:NA;GPO:NA"]}, "customText": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "sds_sp sds_relative optly-header-container", "style": {}, "html": "<h1 class=\"optly-main-header sds_font--secondary sds_line-height--1-0 sds_font-size--24 optly-main-header\" style=\"color: rgb(0, 0, 0); font-size: 2rem; text-transform: uppercase;\">Perk Alert: Get 25% Off</h1>"}}, "subtitle": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "", "style": {}, "html": "<h3 class=\"sds_line-height--1-4 heading-d optly-lighter\" style=\"font-weight:400;\">As a thank you for signing up for emails, take 25% off reg. price styles online. Plus, we're giving you access to exclusive offers, new arrivals, and more.</h3>"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "sds_label-a sds_color--g2", "style": {"marginTop": "8em"}, "html": "<a href=\"/customerService/info.do?cid=2331\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: block; line-height: 1.3; text-decoration: underline; font-size: 0.8em;\">*Terms and Conditions</a>"}}}, "textInputOptions": {"label": "Your Email Address", "errorMessage": "Please enter a valid email address", "desktop": {"className": "sds_sp", "crossBrand": false, "inverse": false}, "mobile": {"className": "sds_sp", "crossBrand": false, "inverse": false}}, "submitButtonOptions": {"text": "Claim your unique code", "desktop": {"className": "sds_sp_vertical sds_uppercase", "variant": "solid", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "primary"}, "mobile": {"className": "sds_sp_vertical sds_uppercase", "variant": "solid", "size": "small", "fullWidth": true, "crossBrand": false, "color": "primary"}}, "errorNotificationAriaLabel": "Error"}}]}}}}]}}}, "analytics": {"on_close": {"tracking_enabled": true, "content_id": "email_popup_close", "link_name": "email_popup_close"}, "on_submit": {"tracking_enabled": true, "content_id": "email_popup_submit", "link_name": "email_popup_submit"}}}}, {"type": "builtin", "name": "div", "instanceName": "pod_popup", "experimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}, "sitewide-secondaryheadline-ciid": "28964275", "sitewide-secondaryheadline-desc": "update_041822", "universalNav": {"sitewide-universalNav-ciid": "28964275", "style": {"activeColor": "#ffffff", "hoverColor": "#08417B", "backgroundColor": "#0466CA"}}, "secondary-headline": {"name": "div", "type": "builtin", "CIID": "28964275", "data": {"props": {}, "components": [{"instanceName": "gpus_secondaryheadline-pzpod", "instanceDesc": "Keep for PZPod GSB", "type": "builtin", "name": "div", "experimentRunning": false, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"meta": {"excludePageTypes": []}, "shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"instanceName": "gpus_secondaryheadline-gsb-0223", "instanceDesc": "4/18 GSB - keep instanceName 0223 for dpg", "name": "div", "type": "builtin", "gsb-ciid": "28964275", "experimentRunning": true, "meta": {"excludePageTypes": []}, "data": {"props": {"style": {"backgroundColor": "#e0411c", "position": "relative", "width": "100%", "maxWidth": "2560px", "height": "auto", "lineHeight": "0", "margin": "0 auto"}}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"style": {"backgroundColor": "transparent", "position": "relative", "width": "100%", "maxWidth": "1920px", "lineHeight": "0", "margin": "0 auto"}}, "components": [{"useGreyLoadingEffect": false, "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "transparent"}, "desktopStyle": {"width": "100%", "backgroundColor": "transparent"}}, "background": {"image": {"alt": "spring sale up to 75% off markdowns by taking extra 50% off markdowns code SALE", "srcUrl": "/Asset_Archive/GPWeb/content/0028/964/275/assets/041822_SPRINGSALE_USEC_GMB_75MD_MOB.svg?v=1", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/964/275/assets/041822_SPRINGSALE_USEC_GMB_75MD_DESK.svg?v=1", "style": {}}, "linkData": {"to": "/browse/category.do?cid=65179#pageId=0&department=136&mlink=5058,28964275,globalbanner_spring_sale", "target": "_self"}, "style": {}, "desktopStyle": {}}, "overlay": {}, "ctaList": {"mobilePositionAboveContent": false, "style": {"height": "auto", "width": "auto"}, "desktopStyle": {}, "className": "", "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/buy/promo_legal_details.do?promoId=874567", "height": "500px"}}, "composableButtonData": {"children": "details", "capitalization": "uppercase", "style": {"backgroundColor": "transparent", "color": "#ffffff", "fontSize": "10px", "fontWeight": "normal", "textDecoration": "underline", "padding": "0", "position": "absolute", "bottom": "1px", "right": ".1%"}, "desktopStyle": {"fontSize": "10px", "bottom": "10%", "right": ".15%", "@media only screen and (max-width: 1024px)": {"fontSize": "10px", "bottom": "5%", "right": "0%"}}}}]}}}]}}]}}]}}, "sitewide-edfs-ciid": "28129033", "sitewide-edfs-desc": "11232021-update", "edfssmall": {"instanceName": "edfs-header-small", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"shouldWaitForOptimizely": "true", "lazy": false, "defaultHeight": {"large": "80px", "small": "50px"}, "isVisible": {"large": false, "small": true}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto"}, "components": [{"type": "sitewide", "name": "MktEdfsSmall", "data": {"lazy": false, "experimentRunning": false, "styles": {"headline": {"padding": "0", "fontSize": "10px"}}, "defaultData": {"textStrong": "Free Shipping", "text": "ON $50+ FOR REWARDS MEMBERS", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-GP-Narvar.html", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "SIGN IN OR JOIN", "path": "/my-account/sign-in", "style": {"letterSpacing": "0", "display": "inline", "position": "relative", "top": "-2px", "fontSize": "1em", "fontWeight": "400", "paddingRight": "0", "fontFamily": "'Gap Sans', Helvetica, Arial, Roboto, sans-serif"}}}}]}}}}, "edfslarge": {"instanceName": "edfs-header-large", "type": "sitewide", "name": "MktEdfsLarge", "tileStyle": {"display": "flex", "height": "40px", "alignItems": "center", "margin-top": "1px"}, "experimentRunning": true, "data": {"shouldWaitForOptimizely": "true", "defaultData": {"text": "FREE SHIPPING ON $50+ FOR REWARDS MEMBERS", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-GP-Narvar.html", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "SIGN IN OR JOIN", "path": "/my-account/sign-in", "style": {"letterSpacing": "0", "fontSize": "1em", "display": "inline", "position": "relative", "fontWeight": "400", "top": "-1px", "fontFamily": "'Gap Sans', Helvetica, Arial, Roboto, sans-serif"}}}}, "sitewide-topnav-ciid": "********", "sitewide-topnav-desc": "031622_Men Active ABb", "hamnav": {"data": {"exclusionIds": ["1170807"]}}, "search": {"type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dresses", "Sweaters", "Shorts"]}}, "topnav": {"name": "MegaNav", "type": "sitewide", "data": {"isNavSticky": true, "classStyles": {"topnav li:not(.catnav--item)": "padding: 0;", "topnav a.divisionLink": "box-shadow: none !important; box-sizing: border-box; color: #2b2b2b; display: block; font-size: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); font-weight: 400; height: 90px; line-height: 1; min-height: 0vw; padding: 40px 0 0; position: relative; text-transform: uppercase;", "topnav a.divisionLink::before": "border-color: transparent; border-style: solid; border-width: 0 0 1px; content: ''; height: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); left: 50%; min-height: 12px; padding-bottom: 3px; position: absolute; top: 40px; transform: translateX(-50%); width: calc(100% - 2vw);", "topnav a.divisionLink._selected": "color: #2b2b2b;", "topnav li.catnav--item.sitewide-1l5zl4x": "color: #2b2b2b;", "topnav a.sitewide-l0i3ri": "color: #2b2b2b;", "topnav span.sitewide-l0i3ri": "color: #2b2b2b;", "topnav li:hover a.divisionLink": "background-color: #fff;", "topnav li:hover a.divisionLink::before": "border-color: #2b2b2b;", "topnav a.divisionLink._selected::before": "border-color: #2b2b2b;", "topnav a.divisionLink:hover": "box-shadow: none !important;", "topnav li.catnav--header > span": "border-bottom-color: #2b2b2b;", "topnav li.catnav--header > a": "border-bottom-color: #2b2b2b;", "topnav a.divisionLink.navlink-pink": "color: #e51937;", "topnav a.divisionLink.navlink-red": "color: #d0011b;", "topnav a.catnav--item--link.sitewide-4l9vad[data-categoryid='1187473']": "color: #e51937;", "topnav .catnav--item.catnav--item-selected": "color: #2b2b2b;", "topnav .catnav--item.catnav--item-selected a": "color: #2b2b2b;", "topnav .catnav--item--link": "max-width: 265px;", "topnav .catnav--item--link:hover": "color: #2b2b2b;", "topnav li.catnav--item.sitewide-1l5zl4xli": "color: #e28743;", "meganav": "border-top-width: 0"}, "activeDivisions": [{"name": "New", "divisionId": ["1086624"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='8792' href='/browse/category.do?cid=8792&mlink=39813,********,topnav_newarrivals_w,visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/032922/SP221909_img.jpg' alt='new arrivals'><img style='position:absolute;left:0' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/032922/SP221909_copy.svg' alt='new arrivals'></a></li></ul></li>"], ["1139272"], ["1164542"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1070833' href='/browse/category.do?cid=1070833&mlink=39813,********,topnav_newarrivals_mothersday,visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/032922/SP221910_img.jpg' alt='New Arrivals-MothersDay'><img style='position:absolute;left:0' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/032922/SP221910_copy2.svg' alt='New Arrivals-MothersDay'></a></li></ul></li>"]], "numberOfColumns": {}, "exclusionIds": [], "customStyles": {}}, {"name": "Women", "divisionId": ["/browse/division.do?cid=5643&mlink=39813,********,Megnav_Women", "5646"], "megaNavOrder": [["1164545", "1131702"], ["1042481"], ["1131696", "5903"], ["1122595", "1131698"]], "numberOfColumns": {"1042481": 2}, "exclusionIds": [], "customStyles": {"65179": {"colorScheme": "sale"}, "1015684": {"colorScheme": "sale"}}}, {"name": "Maternity", "divisionId": ["/browse/division.do?cid=5997&mlink=39813,********,Megnav_Maternity&clink=********", "5997"], "megaNavOrder": [["1164546", "1149538"], ["1042513"], ["1188906", "1014415"], ["1122765"]], "numberOfColumns": {"1042513": 2}, "exclusionIds": [], "customStyles": {"65302": {"colorScheme": "sale"}, "1146678": {"colorScheme": "sale"}}}, {"name": "Men", "divisionId": ["/browse/division.do?cid=5063&mlink=39813,********,Megnav_Men&clink=********", "5065"], "megaNavOrder": [[], ["1164547", "1149531"], ["1042515"], ["1122755", "1076121"]], "numberOfColumns": {"1042515": 2}, "exclusionIds": [], "customStyles": {"65289": {"colorScheme": "sale"}, "1008073": {"colorScheme": "sale"}}}, {"name": "Teen", "divisionId": ["/browse/division.do?cid=1182426&mlink=39813,********,Megnav_Teen&clink=********", "1159071"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1171903' href='/browse/category.do?cid=1171903&mlink=39813,********,topnav_tng_newarrivals,visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative;' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/SP221538_Teen_TeenGirlMegaNav_img.jpg' alt='Shop Teen Girl'><img style='position:absolute;left:0;' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/SP221538_Teen_TeenGirlMegaNav_copy.svg' alt='Shop Teen Girl'></a></li></ul></li>"], ["1161736"], ["1161737"], ["1177960", "1188312"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1171904' href='/browse/category.do?cid=1171904&mlink=39813,********,topnav_tnb_newarrivals,visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative;' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/SP221539_Teen_TeenBoyMegaNav_img.jpg' alt='Shop Teen Boy'><img style='position:absolute;left:0;' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/SP221539_Teen_TeenBoyMegaNav_copy.svg' alt='Shop Teen Guy'></a></li></ul></li>"]], "exclusionIds": [], "customStyles": {"1177961": {"colorScheme": "sale"}, "1177962": {"colorScheme": "sale"}}}, {"name": "Girls", "divisionId": ["/browse/division.do?cid=1137865&mlink=39813,********,Megnav_Girls&clink=********", "6256"], "megaNavOrder": [[], ["1164548", "1056088"], ["1042516"], ["1122748", "6258"]], "numberOfColumns": {"1042516": 2}, "exclusionIds": [], "customStyles": {"65194": {"colorScheme": "sale"}, "1137652": {"colorScheme": "sale"}}}, {"name": "Boys", "divisionId": ["/browse/division.do?cid=1137867&mlink=39813,********,Megnav_Girls&clink=********", "6172"], "megaNavOrder": [[], ["1164549", "1056087"], ["1042518", "6189"], ["1122747", "6174"]], "numberOfColumns": {"1042518": 2}, "exclusionIds": [], "customStyles": {"65217": {"colorScheme": "sale"}, "1137659": {"colorScheme": "sale"}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=1137868&mlink=39813,********,Megnav_<PERSON>ler&clink=********", "6413"], "megaNavOrder": [["1164550", "1149845"], ["1016135"], ["1016083"], ["1048209", "1067853"]], "exclusionIds": [], "customStyles": {"65236": {"colorScheme": "sale"}, "65263": {"colorScheme": "sale"}}}, {"name": "Baby", "divisionId": ["/browse/division.do?cid=1137869&mlink=39813,********,Megnav_Baby&clink=********", "6487"], "megaNavOrder": [[], ["1164551", "1164552", "1149847"], ["95461"], ["95574"], ["1048187", "1067854"]], "exclusionIds": [], "customStyles": {"65208": {"colorScheme": "sale"}, "65261": {"colorScheme": "sale"}, "1187419": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Matching", "divisionId": ["#", "1189451"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1070833' href='/browse/category.do?cid=1070833&mlink=39813,********,topnav_newarrivals_mothersday,visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/032922/SP221910_img.jpg' alt='New Arrivals-MothersDay'><img style='position:absolute;left:0' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/032922/SP221910_copy2.svg' alt='New Arrivals-MothersDay'></a></li></ul></li>"], ["1189452"]], "exclusionIds": [], "customStyles": {"1187419": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["1077403"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1188517' href='/browse/category.do?cid=1188517#department=136&mlink=39813,********,Topnav_W_denimshorts,visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative;' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/032922/SP222038_img.jpg' alt='womens denim shorts'><img style='position:absolute;left:0;' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/032922/SP222038_copy.svg' alt='womens denim shorts'></a></li></ul></li>"], ["1135249"], ["1137310"], ["1137320", "1137322"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='6013' href='/browse/category.do?cid=6013&mlink=39813,********,topnav_jeans_maternity,visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/030122/SP221398_img.jpg' alt='Maternity Denim'><img style='position:absolute;left:0' src='/Asset_Archive/GPWeb/content/0028/865/609/assets/030122/SP221398_copy1.svg' alt='Maternity Denim'></a></li></ul></li>"]], "exclusionIds": [], "customStyles": {}}, {"name": "Sale", "divisionId": ["1156863"], "megaNavOrder": [["1156864"]], "numberOfColumns": {"1156864": 2}, "exclusionIds": [], "customStyles": {}}, {"name": "Factory", "divisionId": ["https://www.gapfactory.com/?tid=gfsv000000"]}]}}}, "home": {"home-ciid": "28978885", "instanceName": "HomePage_041222", "type": "home", "name": "HomeMultiSimple", "components": [{"instanceDesc": "WCD HP CSS Modifications", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>.slick-slide>div>div{display:block!important}.fullBleedCertona img{width:100%}.fullBleedCertona div.productCard{max-width:unset}.fullBleedCertona button.slick-arrow.slick-next.sitewide-0,.fullBleedCertona button.slick-arrow.slick-next.slick-disabled.sitewide-0,.fullBleedCertona button.slick-arrow.slick-prev.sitewide-0{margin-top:0}.fullBleedCertona .mkt-certona-recs,.fullBleedCertona .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon,.fullBleedCertona .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon .mkt-certona-recs__products{max-width:none}.wcd_main1-tile img{display:block}</style>"}}, {"instanceName": "WCD_HP-Spacer", "instanceDesc": "Spacer for topnav without transparent overlap", "name": "div", "type": "builtin", "useGreyLoadingEffect": false, "data": {"defaultHeight": {"small": "65px", "large": "65px"}, "style": {"margin": "0 auto"}, "tabletStyle": {"margin": "0 auto /* delays change to 1024px viewport width */"}, "desktopStyle": {"marginTop": "35px"}, "components": []}}, {"instanceName": "dpg-banner1", "instanceDesc": "DPG-placeholder-1", "type": "builtin", "name": "div", "experimentRunning": true, "redpointExperimentRunning": false, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": "0"}, "data": {"lazy": false, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "0", "large": "0"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "mobile": {"width": 0, "height": 0}, "desktop": {"height": "0"}}}}, {"instanceName": "optly-placeholder-1", "instanceDesc": "040522_UNREC1", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "360px", "large": "306px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0rem", "maxWidth": "1920px", "width": "100%"}}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 auto 2rem", "paddingBottom": "2rem", "maxWidth": "1920px", "borderBottom": "1px solid #2b2b2b"}, "components": [{"instanceDesc": "tile1_girl", "name": "a", "type": "builtin", "tileStyle": {"mobile": {"width": "50%", "order": "1", "position": "relative"}, "desktop": {"order": "1", "width": "25%", "position": "relative"}}, "data": {"props": {"href": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,28876662,HP_HERO1_G_SP221819_CTA", "className": "wcd_main1-tile"}, "components": [{"instanceDesc": "cta", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "width": "auto", "minWidth": "0px", "position": "absolute", "top": "73%", "transform": "translateX(-50%)", "@media only screen and (min-width: 768px)": {}, "a, button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "color": "#FFFFFF", "borderStyle": "solid", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "padding": "10px 12px", "width": "100%", "&:hover": {}}, "button": {"padding": "10px 12px", "border": "1px solid #2b2b2b", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #ad007"}, "&:first-child": {"borderTop": "0px"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"width": "auto", "top": "88%", "ul": {}, "@media only screen and (min-width: 768px)": {}, "@media only screen and (min-width: 1190px)": {}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,28876662,HP_HERO1_G_SP221819_CTA"}, "composableButtonData": {"children": "Girls", "font": "primary"}}]}}}, {"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_girls_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_girls_DESK.jpg"}, "backgroundHover": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_girls_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_girls_DESK_hover.jpg"}}}]}}, {"instanceDesc": "tile2_tb", "name": "a", "type": "builtin", "tileStyle": {"mobile": {"order": "8", "width": "50%", "position": "relative"}, "desktop": {"order": "2", "width": "25%", "position": "relative"}}, "data": {"props": {"href": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,28876662,HP_HERO1_TB_SP221819_CTA", "className": "wcd_main1-tile"}, "components": [{"instanceDesc": "cta", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "width": "auto", "minWidth": "0px", "position": "absolute", "top": "73%", "transform": "translateX(-50%)", "@media only screen and (min-width: 768px)": {}, "a, button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "color": "#FFFFFF", "borderStyle": "solid", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "padding": "10px 12px", "width": "100%", "&:hover": {}}, "button": {"padding": "10px 12px", "border": "1px solid #2b2b2b", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #ad007"}, "&:first-child": {"borderTop": "0px"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"width": "auto", "top": "88%", "ul": {}, "@media only screen and (min-width: 768px)": {}, "@media only screen and (min-width: 1190px)": {}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,28876662,HP_HERO1_TB_SP221819_CTA"}, "composableButtonData": {"children": "<PERSON><PERSON>", "font": "primary"}}]}}}, {"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_tb_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_tb_DESK.jpg"}, "backgroundHover": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_tb_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_tb_DESK_hover.jpg"}}}]}}, {"instanceDesc": "tile3_copy", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "100%", "order": "3"}, "desktop": {"order": "5", "width": "100%"}}, "data": {"background": {"image": {"alt": "It's <PERSON>. Feel the pep in every step, skip, and jump with all things new and supercharged pops of coral.", "srcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_copy_MOB.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_copy_DESK.svg", "style": {"display": "block"}}}}}, {"instanceDesc": "tile4_teengirls", "name": "a", "type": "builtin", "tileStyle": {"mobile": {"width": "50%", "order": "2", "position": "relative"}, "desktop": {"order": "3", "width": "25%", "position": "relative"}}, "data": {"props": {"href": "/browse/category.do?cid=1171903#pageId=0&department=48&mlink=5058,28876662,HP_HERO1_TNG_SP221819_CTA", "className": "wcd_main1-tile"}, "components": [{"instanceDesc": "cta", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "width": "auto", "minWidth": "0px", "position": "absolute", "top": "73%", "transform": "translateX(-50%)", "@media only screen and (min-width: 768px)": {}, "a, button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "color": "#FFFFFF", "borderStyle": "solid", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "padding": "10px 12px", "width": "100%", "&:hover": {}}, "button": {"padding": "10px 12px", "border": "1px solid #2b2b2b", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #ad007"}, "&:first-child": {"borderTop": "0px"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"width": "auto", "top": "88%", "ul": {}, "@media only screen and (min-width: 768px)": {}, "@media only screen and (min-width: 1190px)": {}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1171903#pageId=0&department=48&mlink=5058,28876662,HP_HERO1_TNG_SP221819_CTA"}, "composableButtonData": {"children": "Teen Girls", "font": "primary"}}]}}}, {"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "TeenGirl", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_teengirls_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_teensgirl_DESK.jpg"}, "backgroundHover": {"altText": "TeenGirl", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_teengirls_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_teensgirl_DESK_hover.jpg"}}}]}}, {"instanceDesc": "tile5_toddlergirl", "name": "a", "type": "builtin", "tileStyle": {"mobile": {"order": "5", "width": "50%", "position": "relative"}, "desktop": {"order": "4", "width": "25%", "position": "relative"}}, "data": {"props": {"href": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,28876662,HP_HERO1_TG_SP221819_CTA", "className": "wcd_main1-tile"}, "components": [{"instanceDesc": "cta", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "width": "auto", "minWidth": "0px", "position": "absolute", "top": "73%", "transform": "translateX(-50%)", "@media only screen and (min-width: 768px)": {}, "a, button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "color": "#FFFFFF", "borderStyle": "solid", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "padding": "10px 12px", "width": "100%", "&:hover": {}}, "button": {"padding": "10px 12px", "border": "1px solid #2b2b2b", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #ad007"}, "&:first-child": {"borderTop": "0px"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"width": "auto", "top": "88%", "ul": {}, "@media only screen and (min-width: 768px)": {}, "@media only screen and (min-width: 1190px)": {}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,28876662,HP_HERO1_TG_SP221819_CTA"}, "composableButtonData": {"children": "<PERSON>ler Girl", "font": "primary"}}]}}}, {"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_tg_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_tg_DESK.jpg"}, "backgroundHover": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_tg_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_tg_DESK_hover.jpg"}}}]}}, {"instanceDesc": "tile6_babygirl", "name": "a", "type": "builtin", "tileStyle": {"mobile": {"order": "7", "width": "50%", "position": "relative"}, "desktop": {"order": "6", "width": "25%", "position": "relative"}}, "data": {"props": {"href": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,28876662,HP_HERO1_BG_SP221819_CTA", "className": "wcd_main1-tile"}, "components": [{"instanceDesc": "cta", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "width": "auto", "minWidth": "0px", "position": "absolute", "top": "73%", "transform": "translateX(-50%)", "@media only screen and (min-width: 768px)": {}, "a, button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "color": "#FFFFFF", "borderStyle": "solid", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "padding": "10px 12px", "width": "100%", "&:hover": {}}, "button": {"padding": "10px 12px", "border": "1px solid #2b2b2b", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #ad007"}, "&:first-child": {"borderTop": "0px"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"width": "auto", "top": "88%", "ul": {}, "@media only screen and (min-width: 768px)": {}, "@media only screen and (min-width: 1190px)": {}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,28876662,HP_HERO1_BG_SP221819_CTA"}, "composableButtonData": {"children": "Baby Girl", "font": "primary"}}]}}}, {"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_bg_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_bg_DESK.jpg"}, "backgroundHover": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_bg_MOB.jpg?v=1", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_bg_DESK_hover.jpg"}}}]}}, {"instanceDesc": "tile7_boys", "name": "a", "type": "builtin", "tileStyle": {"mobile": {"order": "4", "width": "50%", "position": "relative"}, "desktop": {"order": "7", "width": "25%", "position": "relative"}}, "data": {"props": {"href": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,28876662,HP_HERO1_B_SP221819_CTA", "className": "wcd_main1-tile"}, "components": [{"instanceDesc": "cta", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "width": "auto", "minWidth": "0px", "position": "absolute", "top": "73%", "transform": "translateX(-50%)", "@media only screen and (min-width: 768px)": {}, "a, button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "color": "#FFFFFF", "borderStyle": "solid", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "padding": "10px 12px", "width": "100%", "&:hover": {}}, "button": {"padding": "10px 12px", "border": "1px solid #2b2b2b", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #ad007"}, "&:first-child": {"borderTop": "0px"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"width": "auto", "top": "88%", "ul": {}, "@media only screen and (min-width: 768px)": {}, "@media only screen and (min-width: 1190px)": {}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,28876662,HP_HERO1_B_SP221819_CTA"}, "composableButtonData": {"children": "Boys", "font": "primary"}}]}}}, {"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_boys_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_boys_DESK.jpg"}, "backgroundHover": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_boys_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_boys_DESK_hover.jpg"}}}]}}, {"instanceDesc": "tile8_babyboy", "name": "a", "type": "builtin", "tileStyle": {"mobile": {"order": "6", "width": "50%", "position": "relative"}, "desktop": {"order": "8", "width": "25%", "position": "relative"}}, "data": {"props": {"href": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,28876662,HP_HERO1_BB_SP221819_CTA", "className": "wcd_main1-tile"}, "components": [{"instanceDesc": "cta", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "width": "auto", "minWidth": "0px", "position": "absolute", "top": "73%", "transform": "translateX(-50%)", "@media only screen and (min-width: 768px)": {}, "a, button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "color": "#FFFFFF", "borderStyle": "solid", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "padding": "10px 12px", "width": "100%", "&:hover": {}}, "button": {"padding": "10px 12px", "border": "1px solid #2b2b2b", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #ad007"}, "&:first-child": {"borderTop": "0px"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"width": "auto", "top": "88%", "ul": {}, "@media only screen and (min-width: 768px)": {}, "@media only screen and (min-width: 1190px)": {}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,28876662,HP_HERO1_BB_SP221819_CTA"}, "composableButtonData": {"children": "Baby Boy", "font": "primary"}}]}}}, {"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_bb_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_bb_DESK.jpg"}, "backgroundHover": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_bb_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_bb_DESK_hover.jpg"}}}]}}, {"instanceDesc": "tile9_teenguy", "name": "a", "type": "builtin", "tileStyle": {"mobile": {"order": "9", "width": "50%", "position": "relative"}, "desktop": {"order": "9", "width": "25%", "position": "relative"}}, "data": {"props": {"href": "/browse/category.do?cid=1171904#pageId=0&department=16&mlink=5058,28876662,HP_HERO1_TNG_SP221819_CTA", "className": "wcd_main1-tile"}, "components": [{"instanceDesc": "cta", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "width": "auto", "minWidth": "0px", "position": "absolute", "top": "73%", "transform": "translateX(-50%)", "@media only screen and (min-width: 768px)": {}, "a, button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "color": "#FFFFFF", "borderStyle": "solid", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "padding": "10px 12px", "width": "100%", "&:hover": {}}, "button": {"padding": "10px 12px", "border": "1px solid #2b2b2b", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #ad007"}, "&:first-child": {"borderTop": "0px"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"width": "auto", "top": "88%", "ul": {}, "@media only screen and (min-width: 768px)": {}, "@media only screen and (min-width: 1190px)": {}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1171904#pageId=0&department=16&mlink=5058,28876662,HP_HERO1_TNG_SP221819_CTA"}, "composableButtonData": {"children": "Teen Guys", "font": "primary"}}]}}}, {"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_teenguy_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_teenguys_DESK.jpg"}, "backgroundHover": {"altText": "null", "img": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_teenguy_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221819_teenguys_DESK_hover.jpg"}}}]}}]}}}}, {"instanceName": "032222_Certona_1", "instanceDesc": "032222_Certona_1", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "2rem", "large": "3rem"}, "desktop": {"shouldDisplay": true, "data": {"style": {"margin": "0 auto 3rem", "flexDirection": "column", "padding": "0", "maxWidth": "1400px"}, "components": [{"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"lazy": false, "props": {"style": {"width": "100%"}, "className": "fullBleedCertona"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome2_rr", "displayTitle": true, "fullWidth": true, "certonaTitle": {"title": "New arrivals are happening now.", "style": {"mobile": {"color": "#2b2b2b", "display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "lineHeight": "1.1rem", "fontSize": "1.1rem", "padding": ".5rem 0rem .5rem .5rem", "paddingTop": "0", "fontWeight": "400", "letterSpacing": "0px", "textAlign": "left", "textTransform": "uppercase"}, "desktop": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#2b2b2b", "lineHeight": "3.5rem", "fontSize": "3.2vw", "textAlign": "left", "padding": "0px 0 0 0.6%", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "none"}}}, "layout": "carousel", "centerMode": false, "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "displayPlayPauseButton": false, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "arrows": true, "autoplay": false, "pauseOnHover": true, "infinite": false, "priceFlag": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0017/748/073/assets/certona_arrows/gp_certona_gfol-style-arrow3.png", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0017/748/073/assets/certona_arrows/gp_certona_gfol-style-arrow3.png", "nextArrowAlt": "Next", "arrowPosition": "-10px", "productTextStyles": {"productTitle": {"style": {"color": "#2b2b2b", "textAlign": "left", "fontSize": "0.75rem"}}, "productPrice": {"style": {"display": "none"}}, "productSalePrice": {"style": {"display": "none"}}}, "size": {"width": "100%", "height": "150px"}, "productMarketingFlag": {"style": {"fontWeight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "maxWidth": "unset", "padding": "0px", "width": "auto"}}, "productCardImageStyles": {"width": "19vw", "margin": "0", "maxWidth": "unset", "padding": "0px"}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"margin": "0px auto 2rem", "flexDirection": "column", "padding": "0"}, "components": [{"name": "TextHeadline", "type": "sitewide", "data": {"text": "New arrivals \n are happening now.", "defaultHeight": "34px", "style": {"mobile": {"whiteSpace": "pre-line", "color": "#2b2b2b", "textAlign": "left", "fontSize": "2rem", "textTransform": "none", "letterSpacing": "0px", "padding": "1rem 0px 0.8rem 2.1rem"}}}}, {"name": "Recommendations", "type": "home", "tileStyle": {"mobile": {"boxSizing": "border-box", "margin": "0 auto 0rem", "maxWidth": "640px", "paddingLeft": "0rem", "paddingRight": "0.5rem", "width": "100%"}, "desktop": {}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome2_rr", "displayTitle": false, "certonaTitle": {"title": "", "style": {"mobile": {"color": "white", "display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "lineHeight": "1.1rem", "fontSize": "1.1rem", "padding": ".5rem 0rem .5rem .5rem", "paddingTop": "0", "fontWeight": "400", "letterSpacing": "normal", "textAlign": "center", "textTransform": "uppercase"}, "desktop": {}}}, "layout": "carousel", "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "priceFlag": false, "prevArrowSlick": "/Asset_Archive/GPWeb/content/DPG/icons/arrow2.png", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/DPG/icons/arrow2.png", "nextArrowAlt": "Next", "productTextStyles": {"productTitle": {"style": {"color": "#2b2b2b", "textAlign": "left", "fontSize": ".75rem"}}, "productPrice": {"style": {"color": "#2b2b2b", "float": "left", "fontSize": ".75rem"}}, "productSalePrice": {"style": {"color": "red", "float": "left", "fontSize": ".75rem"}}, "size": {"width": "90%", "height": "150px"}}, "productCardStyles": {"style": {}}, "gridLayout": {}}}]}}}}, {"instanceName": "HP_NFT_041222", "instanceDesc": "HP NFT Placeholder", "type": "builtin", "name": "div", "experimentRunning": true, "useGreyLoadingEffect": false, "desktop": {"height": "0"}, "mobile": {"height": "0"}, "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"large": "0px", "small": "0px"}, "isVisible": {"large": true, "small": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"width": 0, "height": 0}}}}, {"instanceName": "optly-placeholder-2", "instanceDesc": "040522_UNREC2", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "shouldWaitForOptimizely": true, "data": {"defaultHeight": {"small": "0px", "large": "0px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 350, "margin": "0 auto 0rem", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 0rem", "maxWidth": "640px", "position": "relative", "borderTop": "1px solid #2b2b2b", "borderBottom": "1px solid #2b2b2b"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {"padding": "0", "margin": "0 auto", "maxWidth": "100%"}, "desktopStyle": {"boxSizing": "border-box"}}, "overlay": {"alt": "BRB, Getting Dressed. Our favorite new dresses for going... everywhere.", "srcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_2/SP222348_img_MOB.jpg"}, "background": {"linkData": {"to": "/browse/category.do?cid=13658#pageId=0&department=136&mlink=5058,28876662,HP_HERO2_W", "title": ""}, "className": "", "style": {}, "desktopStyle": {}, "image": {"alt": "BRB, Getting Dressed. Our favorite new dresses for going... everywhere.", "srcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_2/SP222348_img_MOB.jpg", "style": {}, "desktopStyle": {}}}}}, {"instanceDesc": "CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {}, "data": {"container": {"style": {}}, "overlay": {"alt": "BRB, Getting Dressed. Our favorite new dresses for going... everywhere.", "srcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/mob/SP221785_copy_MOB_v2.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,28876662,HP_HERO2_W", "title": ""}, "className": "", "style": {}, "desktopStyle": {}, "image": {"alt": "BRB, Getting Dressed. Our favorite new dresses for going... everywhere.", "srcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/mob/SP221785_copy_MOB_v2.svg", "style": {}, "desktopStyle": {}}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "width": "78%", "minWidth": "0px", "position": "relative", "marginBottom": "2rem", "transform": "translateX(-50%)", "@media only screen and (min-width: 768px)": {}, "a, button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "color": "#FFFFFF", "borderStyle": "solid", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "padding": "10px 12px", "width": "100%", "marginTop": "0.5rem", "&:hover": {}}, "button": {"padding": "10px 12px", "border": "1px solid #2b2b2b", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #ad007"}, "&:first-child": {"borderTop": "0px"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"transform": "none", "width": "auto", "left": "78.8%", "top": "95%", "ul": {}, "@media only screen and (min-width: 768px)": {}, "@media only screen and (min-width: 1190px)": {}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=13658#pageId=0&department=136&mlink=5058,28876662,HP_HERO2_W"}, "composableButtonData": {"children": "Shop Dresses", "font": "primary"}}, {"linkData": {"to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,28876662,HP_HERO2_W"}, "composableButtonData": {"children": "Shop New Arrivals", "font": "primary"}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"margin": "0rem auto 0rem", "maxWidth": "1920px", "flexDirection": "column", "position": "relative", "padding": "0 0 0rem"}, "components": [{"instanceDesc": "3column_30", "name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 auto", "maxWidth": "1920px", "position": "relative"}, "components": [{"instanceDesc": "video", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"boxSizing": "border-box", "maxWidth": "none", "width": "100%", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderBottomWidth": "0px", "borderTopWidth": "1px", "borderLeftWidth": "0", "borderRightWidth": "0", "position": "relative"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_hp-video1 wcd_hp-video_minimal"}, "components": [{"name": "VideoComponent", "type": "sitewide", "data": {"url": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_2/SP221785_img_DESK_mask.mp4", "playing": true, "controls": true, "muted": true, "loop": true, "width": "100%", "height": "100%", "playerStyles": {"video": {"display": "block"}}, "analytics": {"onPlay": {"video_name_play": "HP_HERO3_G31754_VIDEO", "event_name": "video_play_click"}}, "fallbackImage": {"alt": "BRB, Getting Dressed. Our favorite new dresses for going... everywhere.", "src": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221785_img_DESK_fallback.jpg"}}}]}}, {"instanceDesc": "SVG-overlay", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {}, "data": {"container": {"style": {"maxWidth": "100%", "zIndex": "11", "width": "auto", "padding": "0", "position": "absolute", "top": "0"}}, "style": {"display": "block"}, "background": {"linkData": {"to": "/browse/category.do?cid=13658#pageId=0&department=136&mlink=5058,28876662,HP_HERO2_W", "title": "BRB, Getting Dressed. Our favorite new dresses for going... everywhere."}, "image": {"alt": "BRB, Getting Dressed. Our favorite new dresses for going... everywhere.", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_1/SP221785_copy_cropped_DESK.svg?v=1"}, "style": {"display": "block"}}, "ctaList": {"mobilePositionAboveContent": false, "desktopStyle": {"display": "flex", "flexDirection": "row", "flexWrap": "nowrap", "padding": "0", "position": "absolute", "top": "100%", "left": "52.3%", "transform": "translateX(-50%)", "a": {"backgroundColor": "#2b2b2b", "border": "1px solid #2b2b2b", "color": "#FFFFFF", "display": "inline-block", "fontSize": "12px", "letterSpacing": "0px", "fontWeight": "400", "marginRight": "8px", "padding": "10px 12px", "@media only screen and (max-width: 966px)": {}}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=13658#pageId=0&department=136&mlink=5058,28876662,HP_HERO2_W"}, "composableButtonData": {"children": "Shop Dresses"}}, {"linkData": {"to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,28876662,HP_HERO2_W"}, "composableButtonData": {"children": "Shop All New Arrivals"}}]}}}]}}}}]}}}}]}}}}, {"instanceName": "optly-placeholder-3", "instanceDesc": "040522_UNREC3", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "shouldWaitForOptimizely": true, "data": {"defaultHeight": {"small": "0px", "large": "0px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 350, "margin": "0 auto 0rem", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "justifyContent": "space-between", "margin": "0px auto 2rem", "maxWidth": "640px", "borderBottom": "1px solid #2b2b2b"}, "components": [{"instanceDesc": "sub1", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"width": "100%", "boxSizing": "border-box", "borderBottom": "1px solid #2b2b2b"}, "desktop": {"width": "50%", "boxSizing": "border-box"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {}, "instanceName": "carousel-arrows", "data": {"modalCloseButtonAriaLabel": "Close", "carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 0, "autoplaySpeed": 2000, "fade": true, "displayPlayPauseBtn": true, "arrows": false, "arrowPosition": "0px", "prevArrowUrl": "/Asset_Archive/GPWeb/content/0028/031/299/Assets/unrec_1/left_carousel_arrow_red.svg?v=1", "nextArrowUrl": "/Asset_Archive/GPWeb/content/0028/031/299/Assets/unrec_1/right_carousel_arrow_red.svg?v=1", "displayArrows": {"mobile": false, "desktop": false}}, "buttonSetting": {"buttonStyle": {"left": "14%", "position": "absolute", "top": "11%", "transform": "translate(0,0)", "height": "1.5em", "width": "1.5em", "zIndex": "14"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GPWeb/content/0028/511/087/assets/BlackPlayPause/PLAYsoftblack.svg?v=3", "pauseBtnSrc": "/Asset_Archive/GPWeb/content/0028/511/087/assets/BlackPlayPause/PausebuttonHP_washedblack.svg?v=2"}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {"padding": "0", "margin": "0 auto", "maxWidth": "100%"}, "desktopStyle": {"boxSizing": "border-box"}}, "overlay": {"alt": "(<PERSON>wi<PERSON>)suit up!", "srcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_copy_MOB.svg?v=1", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_copy_DESK.svg?v=1"}, "background": {"linkData": {"to": "/browse/category.do?cid=1072982#pageId=0&department=166&mlink=5058,28811938,HP_HERO2_TB", "title": ""}, "className": "", "style": {}, "desktopStyle": {}, "image": {"alt": "Recycled materials", "srcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_img1_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_img1_DESK.jpg?v=1", "style": {}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {"padding": "0", "margin": "0 auto", "maxWidth": "100%"}, "desktopStyle": {"boxSizing": "border-box"}}, "overlay": {"alt": "(<PERSON>wi<PERSON>)suit up!", "srcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_copy2_MOB.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_copy2_DESK.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1075777#pageId=0&department=48&mlink=5058,28811938,HP_HERO2_G", "title": ""}, "className": "", "style": {}, "desktopStyle": {}, "image": {"alt": "Recycled materials", "srcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_img2_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_img2_DESK.jpg?v=2", "style": {}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {"padding": "0", "margin": "0 auto", "maxWidth": "100%"}, "desktopStyle": {"boxSizing": "border-box"}}, "overlay": {"alt": "(<PERSON>wi<PERSON>)suit up!", "srcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_copy_MOB.svg?v=1", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_copy_DESK.svg?v=1"}, "background": {"linkData": {"to": "/browse/category.do?cid=1075777#pageId=0&department=48&mlink=5058,28811938,HP_HERO2_G", "title": ""}, "className": "", "style": {}, "desktopStyle": {}, "image": {"alt": "Recycled materials", "srcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_img3_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_img3_DESK.jpg?v=1", "style": {}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {"padding": "0", "margin": "0 auto", "maxWidth": "100%"}, "desktopStyle": {"boxSizing": "border-box"}}, "overlay": {"alt": "(<PERSON>wi<PERSON>)suit up!", "srcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_copy_MOB.svg?v=1", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_copy_DESK.svg?v=1"}, "background": {"linkData": {"to": "/browse/category.do?cid=1120932#pageId=0&department=166&mlink=5058,28811938,HP_HERO2_BB", "title": ""}, "className": "", "style": {}, "desktopStyle": {}, "image": {"alt": "Recycled materials", "srcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_img4_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/811/938/assets/PZ/KTB/KTB_2/SP221620_KTB_img4_DESK.jpg?v=1", "style": {}, "desktopStyle": {}}}}}]}}, {"instanceDesc": "cta", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "84%", "width": "78%", "transform": "translateX(-50%)", "marginBottom": "2rem", "button, ul li a": {"fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "whiteSpace": "nowrap"}, "button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "borderWidth": "1px", "color": "#FFFFFF", "padding": "5px 12px 10px", "&:focus": {"borderColor": "#fff !important", "outline": "0"}}, "ul": {"borderColor": "#FFFFFF", "borderStyle": "solid", "borderWidth": "0", "minWidth": "0", "padding": "0", "zIndex": "104", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px 0px 1px 0px", "padding": "0", "a": {"borderWidth": "0", "padding": "8px 4px", "color": "#2b2b2b", "transitionDuration": "0.2s", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"transform": "none", "width": "auto", "left": "11%", "top": "88%", "ul": {}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop Recycled Swimwear"}, "submenu": [{"text": "Teen Girls", "href": "/browse/category.do?cid=1177076#pageId=0&department=48&mlink=5058,28876662,HP_HERO3_TNG"}, {"text": "Girls", "href": "/browse/category.do?cid=1075777#pageId=0&department=48&mlink=5058,28811938,HP_HERO2_G"}, {"text": "Boys", "href": "/browse/category.do?cid=1075793#pageId=0&department=16&mlink=5058,28811938,HP_HERO2_B"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1072981#pageId=0&department=165&mlink=5058,28811938,HP_HERO2_TG"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1072982#pageId=0&department=166&mlink=5058,28811938,HP_HERO2_TB"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1120931#pageId=0&department=165&mlink=5058,28811938,HP_HERO2_BG"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1120932#pageId=0&department=166&mlink=5058,28811938,HP_HERO2_BB"}]}}]}}}]}}}}, {"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_hp-video1 wcd_hp-video_minimal"}, "components": [{"name": "VideoComponent", "type": "sitewide", "data": {"url": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/b/SP221842_img_MOB.mp4?v=2", "playing": true, "controls": true, "muted": true, "loop": true, "width": "100%", "height": "100%", "playerStyles": {"video": {"display": "block"}}, "analytics": {"onPlay": {"video_name_play": "Generation Good", "event_name": "video_play_click"}}, "fallbackImage": {"alt": "National Geographic x Gap. Shop our latest Generation Good tees in honor of World Water Day. Because saving water is an everyday thing for us.", "src": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/b/SP221842_img_MOB.jpg"}}}]}}, {"instanceDesc": "CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {}, "data": {"container": {"style": {"position": "relative"}}, "overlay": {"alt": "National Geographic x Gap. Shop our latest Generatin Good tees in honor of World Water Day.", "srcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/b/SP221842_subcopy_MOB_v1.svg?v=3"}, "background": {"linkData": {"to": "/browse/category.do?cid=1165853#pageId=0&mlink=5058,28876662,HP_HERO4_ALLDIV", "title": ""}, "className": "", "style": {}, "desktopStyle": {}, "image": {"alt": "National Geographic x Gap. Shop our latest Generatin Good tees in honor of World Water Day.", "srcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/b/SP221842_subcopy_MOB_v1.svg?v=3", "style": {}, "desktopStyle": {}}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "width": "78%", "minWidth": "0px", "position": "absolute", "top": "77%", "transform": "translateX(-50%)", "@media only screen and (min-width: 768px)": {}, "a, button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "color": "#FFFFFF", "borderStyle": "solid", "borderWidth": "1px", "fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "padding": "10px 12px", "width": "100%", "marginBottom": "2%", "&:hover": {}}, "button": {"padding": "10px 12px", "border": "1px solid #2b2b2b", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #ad007"}, "&:first-child": {"borderTop": "0px"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"transform": "none", "width": "auto", "left": "78.8%", "top": "95%", "ul": {}, "@media only screen and (min-width: 768px)": {}, "@media only screen and (min-width: 1190px)": {}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1165853#pageId=0&mlink=5058,28876662,HP_HERO4_ALLDIV"}, "composableButtonData": {"children": "Shop Gen Good Tees", "font": "primary"}}]}}}, {"instanceDesc": "cta", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "relative", "width": "78%", "transform": "translateX(-50%)", "marginBottom": "2rem", "button, ul li a": {"fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "whiteSpace": "nowrap"}, "button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "borderWidth": "1px", "color": "#FFFFFF", "padding": "5px 12px 10px", "&:focus": {"borderColor": "#fff !important", "outline": "0"}}, "ul": {"borderColor": "#FFFFFF", "borderStyle": "solid", "borderWidth": "0", "minWidth": "0", "padding": "0", "zIndex": "104", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px 0px 1px 0px", "padding": "0", "a": {"borderWidth": "0", "padding": "8px 4px", "color": "#2b2b2b", "transitionDuration": "0.2s", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"transform": "none", "width": "auto", "left": "11%", "top": "88%", "ul": {}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop All Graphic Tees"}, "submenu": [{"text": "Men", "href": "/browse/category.do?cid=1130075#pageId=0&department=75&mlink=5058,28876662,HP_HERO4_M"}, {"text": "Teen", "href": "/browse/category.do?cid=1191010#pageId=0&mlink=5058,28876662,HP_HERO4_TEEN"}, {"text": "Girls", "href": "/browse/category.do?cid=1122942#pageId=0&department=48&mlink=5058,28876662,HP_HERO4_G"}, {"text": "Boys", "href": "/browse/category.do?cid=1122119#pageId=0&department=16&mlink=5058,28876662,HP_HERO4_B"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=6444#pageId=0&department=165&mlink=5058,28876662,HP_HERO4_TG"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1016096#pageId=0&department=166&mlink=5058,28876662,HP_HERO4_TB"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=7189#pageId=0&department=165&mlink=5058,28876662,HP_HERO4_BG"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=95598#pageId=0&department=166&mlink=5058,28876662,HP_HERO4_BB"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "justifyContent": "space-between", "margin": "0px auto 2rem", "maxWidth": "1920px"}, "components": [{"instanceDesc": "sub1", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "100%"}, "desktop": {"width": "43.06%", "position": "relative"}}, "data": {"container": {"style": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "0px", "borderBottomWidth": "1px", "borderLeftWidth": "0px", "borderRightWidth": "0px", "boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "1px", "borderLeftWidth": "0px", "borderRightWidth": "0px", "position": "relative"}}, "background": {"image": {"alt": "Swim Suit Up!Our newest, cutest little suits are ready to dive right in.", "srcUrl": "/Asset_Archive/GPWeb/content/0028/654/146/assets/UNREC_2/sub2/SP221301_subcopy_MOB.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/Desktop_reskin/SP222327_copyA_DESK.svg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1120932#pageId=0&department=166&mlink=5058,28876662,HP_HERO3_BB"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "60%", "width": "78%", "transform": "translateX(-50%)", "marginBottom": "2rem", "button, ul li a": {"fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "whiteSpace": "nowrap"}, "button": {"backgroundColor": "#2b2b2b", "borderColor": "#2b2b2b", "borderWidth": "1px", "color": "#FFFFFF", "padding": "5px 12px 10px", "&:focus": {"borderColor": "#fff !important", "outline": "0"}}, "ul": {"borderColor": "#FFFFFF", "borderStyle": "solid", "borderWidth": "0", "minWidth": "0", "padding": "0", "zIndex": "104", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0px 0px 1px 0px", "padding": "0", "a": {"borderWidth": "0", "padding": "8px 4px", "color": "#2b2b2b", "transitionDuration": "0.2s", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"button, ul li a": {"fontSize": "12px"}, "width": "auto", "top": "79%", "left": "66%", "transform": "translateX(-50%)", "@media only screen and (min-width: 768px)": {"left": "60%"}, "@media only screen and (min-width: 820px)": {"left": "62%"}, "@media only screen and (min-width: 1010px)": {"left": "74%"}, "@media only screen and (min-width: 1400px)": {"left": "74%"}, "ul": {}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop Recycled Swimwear"}, "submenu": [{"text": "Teen Girls", "href": "/browse/category.do?cid=1177076#pageId=0&department=48&mlink=5058,28876662,HP_HERO3_TNG"}, {"text": "Girls", "href": "/browse/category.do?cid=1075777#pageId=0&department=48&mlink=5058,28876662,HP_HERO3_G"}, {"text": "Boys", "href": "/browse/category.do?cid=1075793#pageId=0&department=16&mlink=5058,28876662,HP_HERO3_B"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1072981#pageId=0&department=165&mlink=5058,28876662,HP_HERO3_TG"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1072982#pageId=0&department=166&mlink=5058,28876662,HP_HERO3_TB"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1120931#pageId=0&department=165&mlink=5058,28876662,HP_HERO3_BG"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1120932#pageId=0&department=166&mlink=5058,28876662,HP_HERO3_BB"}]}}]}}}, {"instanceDesc": "sub2", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"width": "100%", "boxSizing": "border-box"}, "desktop": {"width": "56.87%", "boxSizing": "border-box"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"width:": "50%"}, "instanceName": "carousel-arrows", "data": {"modalCloseButtonAriaLabel": "Close", "carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 0, "autoplaySpeed": 2000, "fade": true, "displayPlayPauseBtn": true, "arrows": false, "arrowPosition": "0px", "prevArrowUrl": "/Asset_Archive/GPWeb/content/0028/031/299/Assets/unrec_1/left_carousel_arrow_red.svg?v=1", "nextArrowUrl": "/Asset_Archive/GPWeb/content/0028/031/299/Assets/unrec_1/right_carousel_arrow_red.svg?v=1", "displayArrows": {"mobile": false, "desktop": false}}, "buttonSetting": {"buttonStyle": {"left": "1%", "position": "absolute", "top": "4%", "transform": "translate(0,0)", "height": "1.5em", "width": "1.5em", "zIndex": "14"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GPWeb/content/0028/511/087/assets/BlackPlayPause/PLAYsoftblack.svg?v=3", "pauseBtnSrc": "/Asset_Archive/GPWeb/content/0028/511/087/assets/BlackPlayPause/PausebuttonHP_washedblack.svg?v=2"}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {"padding": "0", "margin": "0 auto", "maxWidth": "100%", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "0", "borderRightWidth": "0"}, "desktopStyle": {"boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "1px", "borderLeftWidth": "0px", "borderRightWidth": "0px"}}, "background": {"linkData": {"to": " /browse/category.do?cid=1075777#pageId=0&department=48&mlink=5058,28876662,HP_HERO3_G", "title": "Swim Suit Up!Our newest, cutest little suits are ready to dive right in."}, "className": "", "style": {}, "desktopStyle": {}, "image": {"alt": "Swim Suit Up!Our newest, cutest little suits are ready to dive right in.", "srcUrl": "/Asset_Archive/GPWeb/content/0028/654/146/assets/PZ/WOMEN/WOMEN_2/SP221300_img1_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/Desktop_reskin/swimvideo_slice1.jpg", "style": {}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {"padding": "0", "margin": "0 auto", "maxWidth": "100%", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "0", "borderRightWidth": "0"}, "desktopStyle": {"boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "1px", "borderLeftWidth": "0px", "borderRightWidth": "0px"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1072982#pageId=0&department=166&mlink=5058,28876662,HP_HERO3_TB", "title": "Swim Suit Up!Our newest, cutest little suits are ready to dive right in."}, "className": "", "style": {}, "desktopStyle": {}, "image": {"alt": "Swim Suit Up!Our newest, cutest little suits are ready to dive right in.", "srcUrl": "/Asset_Archive/GPWeb/content/0028/654/146/assets/PZ/WOMEN/WOMEN_2/SP221300_img1_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/Desktop_reskin/swimvideo_slice2.jpg", "style": {}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {"padding": "0", "margin": "0 auto", "maxWidth": "100%", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "0", "borderRightWidth": "0"}, "desktopStyle": {"boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "1px", "borderLeftWidth": "0px", "borderRightWidth": "0px"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1120932#pageId=0&department=166&mlink=5058,28876662,HP_HERO3_BB", "title": "Swim Suit Up!Our newest, cutest little suits are ready to dive right in."}, "className": "", "style": {}, "desktopStyle": {}, "image": {"alt": "Swim Suit Up!Our newest, cutest little suits are ready to dive right in.", "srcUrl": "/Asset_Archive/GPWeb/content/0028/654/146/assets/PZ/WOMEN/WOMEN_2/SP221300_img1_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/Desktop_reskin/swimvideo_slice3.jpg", "style": {}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {"padding": "0", "margin": "0 auto", "maxWidth": "100%", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "0", "borderRightWidth": "0"}, "desktopStyle": {"boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "1px", "borderLeftWidth": "0px", "borderRightWidth": "0px"}}, "overlay": {"alt": "Gap for good", "srcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/Gapfogood.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1075777#pageId=0&department=48&mlink=5058,28876662,HP_HERO3_G", "title": "Swim Suit Up!Our newest, cutest little suits are ready to dive right in."}, "className": "", "style": {}, "desktopStyle": {}, "image": {"alt": "Swim Suit Up!Our newest, cutest little suits are ready to dive right in.", "srcUrl": "/Asset_Archive/GPWeb/content/0028/654/146/assets/PZ/WOMEN/WOMEN_2/SP221300_img1_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/Desktop_reskin/swimvideo_slice4.jpg", "style": {}, "desktopStyle": {}}}}}]}}]}}}}, {"instanceDesc": "sub3", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"margin": "-0% auto", "boxSizing": "border-box", "maxWidth": "none", "width": "10%", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderBottomWidth": "0px", "borderTopWidth": "0px", "borderLeftWidth": "0", "borderRightWidth": "0"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_hp-video1 wcd_hp-video_minimal"}, "components": [{"name": "VideoComponent", "type": "sitewide", "data": {"url": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/Desktop_reskin/video/SP221842_img_DESKv2_water.mp4", "playing": true, "controls": true, "muted": true, "loop": true, "width": "100%", "height": "100%", "playerStyles": {"video": {"display": "block", "borderBottom": "1px solid #2b2b2b"}}, "analytics": {"onPlay": {"video_name_play": "HP_HERO3_G31754_VIDEO", "event_name": "video_play_click"}}, "fallbackImage": {"alt": "Generation Good. National Geographic x Gap. Shop our latest Generation Good tees in honor of World Water Day. Because saving water is an everyday thing for us.", "src": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/b/water_animation_still.00_00_00_00.MOB.jpg"}}}]}}]}}}}, {"instanceDesc": "sub4", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"width": "100%", "boxSizing": "border-box"}, "desktop": {"width": "85%", "boxSizing": "border-box"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "0px", "borderBottomWidth": "0px", "borderLeftWidth": "0", "borderRightWidth": "0", "padding": "0", "margin": "0 auto", "maxWidth": "100%"}, "desktopStyle": {"margin": "0% auto", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "0px", "borderBottomWidth": "0px", "borderLeftWidth": "0px", "borderRightWidth": "0px"}}, "overlay": {"alt": "Generation Good. National Geographic x Gap. Shop our latest Generation Good tees in honor of World Water Day. Because saving water is an everyday thing for us.", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/Desktop_reskin/video/SP221842_copy_DESKv2.svg?v=9"}, "background": {"linkData": {"to": "/browse/category.do?cid=1165853#pageId=0&mlink=5058,28876662,HP_HERO4_ALLDIV", "title": ""}, "className": "", "style": {}, "desktopStyle": {}, "image": {"alt": "Generation Good. National Geographic x Gap. Shop our latest Generation Good tees in honor of World Water Day. Because saving water is an everyday thing for us.", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/978/885/assets/UNREC_3/Desktop_reskin/video/SP221842_img_DESKv2.jpg?v=1", "style": {}, "desktopStyle": {"borderBottom": "1px solid #2b2b2b"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "81.5%", "right": "4%", "transform": "translate(0, 0)", "zIndex": "1"}, "mobile": {}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {"padding": "0", "margin": "0 auto", "maxWidth": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "display": "flex", "position": "relative", "div[data-testid='button-dropdown-container']": {"position": "relative", "marginLeft": "2%"}, "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "12px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%"}, "button": {"padding": "10px 12px"}, "ul": {"borderWidth": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 0 1px", "a": {"transitionDuration": "0.2s", "boxSizing": "border-box", "border": "none", "padding": "8px 4px"}}, "li:last-child": {"borderBottom": "1px solid #2b2b2b"}}}, "desktopStyle": {"position": "relative", "padding": "0", "a, button": {"padding": "10px 12px", "fontWeight": "400", "backgroundColor": "#2b2b2b", "color": "#FFFFFF", "fontSize": "12px", "transitionDuration": "0.2s", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}, "button": {"padding": "5px 10px 12px"}, "ul": {"position": "absolute", "borderWidth": "0 1px", "padding": "0", "li a": {"color": "#2b2b2b", "backgroundColor": "#FFFFFF"}}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1165853#pageId=0&mlink=5058,28876662,HP_HERO4_ALLDIV"}, "composableButtonData": {"children": "Shop Gen Good Tees", "font": "primary"}}, {"buttonDropdownData": {"heading": {"text": "Shop All Graphic Tees"}, "submenu": [{"text": "Men", "href": "/browse/category.do?cid=1130075#pageId=0&department=75&mlink=5058,28876662,HP_HERO4_M"}, {"text": "Teen", "href": "/browse/category.do?cid=1191010#pageId=0&mlink=5058,28876662,HP_HERO4_TEEN"}, {"text": "Girls", "href": "/browse/category.do?cid=1122942#pageId=0&department=48&mlink=5058,28876662,HP_HERO4_G"}, {"text": "Boys", "href": "/browse/category.do?cid=1122119#pageId=0&department=16&mlink=5058,28876662,HP_HERO4_B"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=6444#pageId=0&department=165&mlink=5058,28876662,HP_HERO4_TG"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1016096#pageId=0&department=166&mlink=5058,28876662,HP_HERO4_TB"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=7189#pageId=0&department=165&mlink=5058,28876662,HP_HERO4_BG"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=95598#pageId=0&department=166&mlink=5058,28876662,HP_HERO4_BB"}]}}]}}}]}}}}]}}}}, {"instanceName": "dpg-banner2", "instanceDesc": "DPG-placeholder-2", "type": "builtin", "name": "div", "experimentRunning": true, "redpointExperimentRunning": false, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": "0"}, "data": {"lazy": false, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "0", "large": "0"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "mobile": {"width": 0, "height": 0}, "desktop": {"height": "0"}}}}, {"instanceName": "032222_Certona_2", "instanceDesc": "032222_Certona_2", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "2rem", "large": "3rem"}, "desktop": {"shouldDisplay": true, "data": {"style": {"margin": "0 auto 3rem", "flexDirection": "column", "padding": "0", "maxWidth": "1400px"}, "components": [{"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"lazy": false, "props": {"style": {"width": "100%"}, "className": "fullBleedCertona"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome3_rr", "displayTitle": true, "fullWidth": true, "certonaTitle": {"title": "Shopping good is officially trending.", "style": {"mobile": {"color": "#2b2b2b", "display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "lineHeight": "1.1rem", "fontSize": "1.1rem", "padding": ".5rem 0rem .5rem .5rem", "paddingTop": "0", "fontWeight": "400", "letterSpacing": "0px", "textAlign": "left", "textTransform": "uppercase"}, "desktop": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#2b2b2b", "lineHeight": "3.5rem", "fontSize": "3.2vw", "textAlign": "left", "padding": "0px 0 0 0.6%", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "none"}}}, "layout": "carousel", "centerMode": false, "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "displayPlayPauseButton": false, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "arrows": true, "autoplay": false, "pauseOnHover": true, "infinite": false, "priceFlag": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0017/748/073/assets/certona_arrows/gp_certona_gfol-style-arrow3.png", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0017/748/073/assets/certona_arrows/gp_certona_gfol-style-arrow3.png", "nextArrowAlt": "Next", "arrowPosition": "-10px", "productTextStyles": {"productTitle": {"style": {"color": "#2b2b2b", "textAlign": "left", "fontSize": "0.75rem"}}, "productPrice": {"style": {"display": "none"}}, "productSalePrice": {"style": {"display": "none"}}}, "size": {"width": "100%", "height": "150px"}, "productMarketingFlag": {"style": {"fontWeight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "maxWidth": "unset", "padding": "0px", "width": "auto"}}, "productCardImageStyles": {"width": "19vw", "margin": "0", "maxWidth": "unset", "padding": "0px"}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"margin": "0px auto 2rem", "flexDirection": "column", "padding": "0"}, "components": [{"name": "TextHeadline", "type": "sitewide", "data": {"text": "Shopping good is officially trending.", "defaultHeight": "34px", "style": {"mobile": {"whiteSpace": "pre-line", "color": "#2b2b2b", "textAlign": "left", "fontSize": "2rem", "textTransform": "none", "letterSpacing": "0px", "padding": "0rem 0px 0.8rem 2.1rem"}}}}, {"name": "Recommendations", "type": "home", "tileStyle": {"mobile": {"boxSizing": "border-box", "margin": "0 auto 0rem", "maxWidth": "640px", "paddingLeft": "0rem", "paddingRight": "0.5rem", "width": "100%"}, "desktop": {}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome3_rr", "displayTitle": false, "certonaTitle": {"title": "", "style": {"mobile": {"color": "white", "display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "lineHeight": "1.1rem", "fontSize": "1.1rem", "padding": ".5rem 0rem .5rem .5rem", "paddingTop": "0", "fontWeight": "400", "letterSpacing": "normal", "textAlign": "center", "textTransform": "uppercase"}, "desktop": {}}}, "layout": "carousel", "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "priceFlag": false, "prevArrowSlick": "/Asset_Archive/GPWeb/content/DPG/icons/arrow2.png", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/DPG/icons/arrow2.png", "nextArrowAlt": "Next", "productTextStyles": {"productTitle": {"style": {"color": "#2b2b2b", "textAlign": "left", "fontSize": ".75rem"}}, "productPrice": {"style": {"color": "#2b2b2b", "float": "left", "fontSize": ".75rem"}}, "productSalePrice": {"style": {"color": "red", "float": "left", "fontSize": ".75rem"}}, "size": {"width": "90%", "height": "150px"}}, "productCardStyles": {"style": {}}, "gridLayout": {}}}]}}}}, {"instanceName": "optly-vcn", "instanceDesc": "VDN_020222", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "520px", "large": "406px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "justifyContent": "space-between", "margin": "0px auto 2rem", "maxWidth": "1920px"}, "components": [{"instanceDesc": "vdn_01", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "0"}, "desktop": {"width": "25%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "0"}}, "data": {"container": {"style": {"position": "relative"}, "desktopStyle": {"backgroundColor": "#FFFFFF", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop New Arrivals", "srcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/NEW_ARRIVALS_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/NEW_ARRIVALS_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,28511087,HP_VCN_1_W_SP221400_IMAGE"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "83%", "transform": "translateX(-50%)", "zIndex": "31", "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "13px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%"}, "button": {"padding": "0 7px 7px 4px", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "8px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"top": "79%", "a, button": {"fontSize": "15px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "New Arrivals"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,28511087,HP_VCN_1_W_SP221400_CTA"}, {"text": "Maternity", "href": "/browse/category.do?cid=1127956#pageId=0&department=136&mlink=5058,28511087,HP_VCN_1_MAT_SP221400_CTA"}, {"text": "Men", "href": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,28511087,HP_VCN_1_M_SP221400_CTA"}, {"text": "Teen Girls", "href": "/browse/category.do?cid=1171903#pageId=0&department=48&mlink=5058,28511087,HP_VCN_1_TNG_SP221400_CTA"}, {"text": "Teen Guys", "href": "/browse/category.do?cid=1171904#pageId=0&department=16&mlink=5058,28511087,HP_VCN_1_TNB_SP221400_CTA"}, {"text": "Girls", "href": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,28511087,HP_VCN_1_G_SP221400_CTA"}, {"text": "Boys", "href": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,28511087,HP_VCN_1_B_SP221400_CTA"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,28511087,HP_VCN_1_TG_SP221400_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,28511087,HP_VCN_1_TB_SP221400_CTA"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,28511087,HP_VCN_1_BG_SP221400_CTA"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,28511087,HP_VCN_1_BB_SP221400_CTA"}]}}]}}}, {"instanceDesc": "vdn_02", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "1px"}, "desktop": {"width": "25%", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "0", "boxSizing": "border-box"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#FFFFFF", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Women", "srcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/WOMEN_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/WOMEN_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,28511087,HP_VCN_2_W_SP221400_IMAGE"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "83%", "transform": "translateX(-50%)", "zIndex": "31", "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "13px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%"}, "button": {"padding": "0 7px 7px 4px", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "8px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"top": "79%", "a, button": {"fontSize": "15px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Women"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,28511087,HP_VCN_2_W_SP221400_CTA"}, {"text": "Body", "href": "/browse/category.do?cid=1140272#pageId=0&department=136&mlink=5058,28511087,HP_VCN_2_Body_SP221400_CTA"}, {"text": "GapFit", "href": "/browse/category.do?cid=1117374#pageId=0&department=136&mlink=5058,28511087,HP_VCN_2_Fit_SP221400_CTA"}]}}]}}}, {"instanceDesc": "vdn_03", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "0"}, "desktop": {"width": "25%", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "0", "boxSizing": "border-box"}}, "data": {"container": {"style": {"position": "relative"}, "desktopStyle": {"backgroundColor": "#FFFFFF", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Maternity", "srcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/MATERNITY_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/MATERNITY_DESK.jpg?v=1", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=11437#pageId=0&department=136&mlink=5058,28511087,HP_VCN_3_MAT_SP221400_IMAGE"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "83%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "13px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%", "&:hover": {"backgroundColor": "transparent", "color": "#2b2b2b"}}, "button": {"padding": "0 7px 7px 4px", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "8px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"top": "79%", "zIndex": "1", "a, button": {"fontSize": "15px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=11437#pageId=0&department=136&mlink=5058,28511087,HP_VCN_3_MAT_SP221400_CTA"}, "composableButtonData": {"children": "Maternity", "font": "primary"}}]}}}, {"instanceDesc": "vdn_04", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "1px"}, "desktop": {"width": "25%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "1px"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#FFFFFF", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Men", "srcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/MEN_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/MEN_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,28511087,HP_VCN_4_M_SP221400_IMAGE"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "83%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "13px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%", "&:hover": {"backgroundColor": "transparent", "color": "#2b2b2b"}}, "button": {"padding": "0 7px 7px 4px", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "8px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"top": "79%", "a, button": {"fontSize": "15px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,28511087,HP_VCN_4_M_SP221400_CTA"}, "composableButtonData": {"children": "Men", "font": "primary"}}]}}}, {"instanceDesc": "vdn_05", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "0"}, "desktop": {"width": "25%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "0px"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#FFFFFF", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Teen Girl", "srcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/TEEN_GIRLS_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/TEEN_GIRLS.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1171903#pageId=0&department=48&mlink=5058,28511087,HP_VCN_5_TNG_SP221400_IMAGE"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "83%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "13px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%", "&:hover": {"backgroundColor": "transparent", "color": "#2b2b2b"}}, "button": {"padding": "0 7px 7px 4px", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "8px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"top": "79%", "a, button": {"fontSize": "15px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1171903#pageId=0&department=48&mlink=5058,28511087,HP_VCN_5_TNG_SP221400_CTA"}, "composableButtonData": {"children": "Teen Girls", "font": "primary"}}]}}}, {"instanceDesc": "vdn_06", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "1px"}, "desktop": {"width": "25%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "0px"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#FFFFFF", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Teen Guy", "srcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/TEEN_GUYS_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/TEEN_GUYS_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1171904#pageId=0&department=16&mlink=5058,28511087,HP_VCN_6_TNB_SP221400_IMAGE"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "83%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "13px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%", "&:hover": {"backgroundColor": "transparent", "color": "#2b2b2b"}}, "button": {"padding": "0 7px 7px 4px", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "8px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"top": "79%", "a, button": {"fontSize": "15px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1171904#pageId=0&department=16&mlink=5058,28511087,HP_VCN_6_TNB_SP221400_CTA"}, "composableButtonData": {"children": "Teen Guys", "font": "primary"}}]}}}, {"instanceDesc": "vdn_07", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "0"}, "desktop": {"width": "25%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "0px"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#FFFFFF", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Girls", "srcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/GIRLS_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/GIRLS_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,28511087,HP_VCN_7_G_SP221400_IMAGE"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "83%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "13px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%", "&:hover": {"backgroundColor": "transparent", "color": "#2b2b2b"}}, "button": {"padding": "0 7px 7px 4px", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "8px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"top": "79%", "a, button": {"fontSize": "15px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,28511087,HP_VCN_7_G_SP221400_CTA"}, "composableButtonData": {"children": "Girls", "font": "primary"}}]}}}, {"instanceDesc": "vdn_08", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "1px"}, "desktop": {"width": "25%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "1px"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#FFFFFF", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Boys", "srcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/BOYS_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/BOYS_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,28511087,HP_VCN_8_B_SP221400_IMAGE"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "83%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "13px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%", "&:hover": {"backgroundColor": "transparent", "color": "#2b2b2b"}}, "button": {"padding": "0 7px 7px 4px", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "8px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"top": "79%", "a, button": {"fontSize": "15px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,28511087,HP_VCN_8_B_SP221400_CTA"}, "composableButtonData": {"children": "Boys", "font": "primary"}}]}}}, {"instanceDesc": "vdn_09", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "0"}, "desktop": {"width": "25%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "1px", "borderLeftWidth": "1px", "borderRightWidth": "0px"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#FFFFFF", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Toddler Girl", "srcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/TODDLER_GIRL_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/TODDLER_GIRL_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,28511087,HP_VCN_9_TG_SP221400_IMAGE"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "83%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "13px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%", "&:hover": {"backgroundColor": "transparent", "color": "#2b2b2b"}}, "button": {"padding": "0 7px 7px 4px", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "8px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"top": "79%", "a, button": {"fontSize": "15px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,28511087,HP_VCN_9_TG_SP221400_CTA"}, "composableButtonData": {"children": "<PERSON>ler Girl", "font": "primary"}}]}}}, {"instanceDesc": "vdn_10", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "0px", "borderLeftWidth": "1px", "borderRightWidth": "1px"}, "desktop": {"width": "25%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "1px", "borderLeftWidth": "1px", "borderRightWidth": "0px"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#FFFFFF", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Toddler Boy", "srcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/TODDLER_BOY_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/TODDLER_BOY_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,28511087,HP_VCN_10_TB_SP221400_IMAGE"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "83%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "13px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%"}, "button": {"padding": "0 7px 7px 4px", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#FFFFFF", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#FFFFFF", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #fff"}, "a": {"boxSizing": "border-box", "color": "#FFFFFF", "padding": "8px 12px"}}}}, "desktopStyle": {"top": "79%", "a, button": {"fontSize": "15px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,28511087,HP_VCN_10_TB_SP221400_CTA"}, "composableButtonData": {"children": "<PERSON><PERSON>", "font": "primary"}}]}}}, {"instanceDesc": "vdn_11", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "1px", "borderLeftWidth": "1px", "borderRightWidth": "0"}, "desktop": {"width": "25%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "1px", "borderLeftWidth": "1px", "borderRightWidth": "0px"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#FFFFFF", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Baby Girl", "srcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/BABY_GIRL_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/BABY_GIRL_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,28511087,HP_VCN_11_BG_SP221400_IMAGE"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "83%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "13px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%", "&:hover": {"backgroundColor": "transparent", "color": "#2b2b2b"}}, "button": {"padding": "0 7px 7px 4px", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "8px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"top": "79%", "a, button": {"fontSize": "15px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,28511087,HP_VCN_11_BG_SP221400_CTA"}, "composableButtonData": {"children": "Baby Girl", "font": "primary"}}]}}}, {"instanceDesc": "vdn_12", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "1px", "borderLeftWidth": "1px", "borderRightWidth": "1px"}, "desktop": {"width": "25%", "boxSizing": "border-box", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderTopWidth": "1px", "borderBottomWidth": "1px", "borderLeftWidth": "1px", "borderRightWidth": "1px"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#FFFFFF", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Baby Boy", "srcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/BABY_BOY_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/511/087/assets/VDN/BABY_BOY_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,28511087,HP_VCN_12_BB_SP221400_IMAGE"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "83%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "13px", "fontWeight": "400", "letterSpacing": "0", "padding": "4px", "width": "100%", "&:hover": {"backgroundColor": "transparent", "color": "#2b2b2b"}}, "button": {"padding": "0 7px 7px 4px", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "8px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}}}, "desktopStyle": {"top": "79%", "a, button": {"fontSize": "15px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,28511087,HP_VCN_1_BB_SP221400_CTA"}, "composableButtonData": {"children": "Baby Boy", "font": "primary"}}]}}}]}}}}, {"instanceName": "Promo2", "instanceDesc": "Promo2_030722", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "50px", "large": "88px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2rem", "maxWidth": "1920px", "position": "relative"}, "components": [{"instanceDesc": "Main Banner: St<PERSON>", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"position": "relative"}, "desktop": {"position": "relative"}}, "data": {"background": {"image": {"alt": "GapCash Earn at Gap and Gap Factory Star Here Earn $20 in GapCash for  every $50+ you spend through 4/22 Go Big Earn up to $120 in GapCash per order Cash Out Redeem your GapCash and combine with other offers 4/23-5/1", "srcUrl": "/Asset_Archive/GPWeb/content/0028/794/718/assets/promo2/0307_USEC22_GAPCASH_EARNR37_USEC_SUBHPBANNER_MOB.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/794/718/assets/promo2/0307_USEC22_GAPCASH_EARNR37_USEC_SUBHPBANNER_DESK.svg", "style": {"display": "block"}}, "linkData": {"to": "/browse/info.do?cid=99996&HP_BottomBanner"}}}}, {"instanceDesc": "Under-banner Copy Block & CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"position": "relative"}, "desktop": {"display": "none"}}, "data": {"ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "a, button": {"backgroundColor": "#FFFFFF", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 0 1px", "boxSizing": "border-box", "color": "#2b2b2b", "fontSize": "18px", "fontWeight": "500", "letterSpacing": "0", "padding": "8px 4px", "transitionDuration": "0.2s", "whiteSpace": "nowrap", "width": "100%", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFFFFF"}}}, "desktopStyle": {"display": "none", "borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px", "flexDirection": "row", "maxWidth": "50%", "position": "absolute", "right": "1.5%", "top": "18%", "a, button": {"borderBottomWidth": "0", "fontSize": "12px", "fontWeight": "500", "letterSpacing": "0", "padding": "10px 12px", "&:not(:first-child)": {"borderLeftWidth": "1px"}}, "@media only screen and (min-width: 920px)": {"a, button": {"fontSize": "11px", "padding": "10px 8px"}}, "@media only screen and (min-width: 1200px)": {"a, button": {"fontSize": "14px", "padding": "10px 10px"}}, "@media only screen and (min-width: 1320px)": {"a, button": {"fontSize": "16px", "padding": "12px 12px"}}}, "ctas": [{"linkData": {"to": "/browse/info.do?cid=99996&HP_BottomBanner"}, "composableButtonData": {"children": "Learn More", "font": "primary"}}]}}}]}}}}, {"instanceName": "news", "instanceDesc": "122621", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "200px", "large": "300px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 auto 2rem", "maxWidth": "1920px"}, "components": [{"instanceDesc": "GapForGood", "name": "a", "type": "builtin", "tileStyle": {"mobile": {"width": "100%", "marginTop": "2rem"}, "desktop": {"order": "1", "width": "100%"}}, "data": {"props": {"href": "https://www.gap.com/browse/info.do?cid=1086537&mlink=5058,28031299,HP_HERO_Footer_G4G", "className": "wcd_main1-tile"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "See How We Do Good", "img": "/Asset_Archive/GPWeb/content/0028/511/087/assets/news/SP222039_ReskinGapforGood_MOB--v2.svg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/511/087/assets/news/G4G.svg?v=4"}, "backgroundHover": {"altText": "Gap For Good.We believe Gap should be a force for good. Creating sustainable change and equitable spaces for all people is our purpose. One stitch at a time.", "desktopImg": "/Asset_Archive/GPWeb/content/0028/511/087/assets/news/SP222039_ReskinGapforGood_DESK.svg?v=5", "img": "/Asset_Archive/GPWeb/content/0028/511/087/assets/news/SP222039_ReskinGapforGood_MOB--v2.svg"}}}]}}, {"instanceDesc": "SeeHowWeDoGood", "name": "a", "type": "builtin", "tileStyle": {"mobile": {"width": "100%"}, "desktop": {"order": "2", "width": "100%"}}, "data": {"components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "Look for the (globe). When you see this globe, know that this style is responsibly made.", "img": "/Asset_Archive/GPWeb/content/0028/511/087/assets/news/SP222039_ReskinGlobe_MOB.svg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/511/087/assets/news/GLOBE.svg?v=1"}, "backgroundHover": {"altText": "Learn More", "img": "/Asset_Archive/GPWeb/content/0028/511/087/assets/news/SP222039_ReskinGlobe_MOB.svg", "desktopImg": "/Asset_Archive/GPWeb/content/0028/511/087/assets/news/SP222039_ReskinGlobe_DESK.svg?v=3"}}}]}}, {"instanceDesc": "<PERSON><PERSON>", "name": "a", "type": "builtin", "tileStyle": {"mobile": {"width": "100%"}, "desktop": {"order": "3", "width": "100%"}}, "data": {"props": {"href": "https://www.gapinc.com/en-us/articles/2021/01/gap-inc-joins-the-15-percent-pledge-and-commits-to", "className": "wcd_main1-tile"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "15% Pledge. We support the 15% Pledge, a non-profit organization advocating for equitable opportunity and access for black owned businesses and black people in the workforce.", "img": "/Asset_Archive/GPWeb/content/0028/511/087/assets/news/SP222039_Reskin15Pledge_MOB--v2.svg?v=2", "desktopImg": "/Asset_Archive/GPWeb/content/0028/511/087/assets/news/15PLEDGE.svg?v=2"}, "backgroundHover": {"altText": "Our Commitment To Change", "img": "/Asset_Archive/GPWeb/content/0028/511/087/assets/news/SP222039_Reskin15Pledge_MOB--v2.svg?v=2", "desktopImg": "/Asset_Archive/GPWeb/content/0028/511/087/assets/news/SP222039_Reskin15Pledge_DESK.svg?v=2"}}}]}}]}}}}]}, "meta.title.override": "Shop Women, Men, Mat<PERSON><PERSON>, Baby & Kids Clothes Online", "type": "meta", "brand": "gap", "meta.description": "Shop womens, mens, maternity, kids & baby clothes at Gap online and find the perfect pair of jeans, t-shirts, dresses and more for the whole family."}