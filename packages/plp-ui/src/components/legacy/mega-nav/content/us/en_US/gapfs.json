{"sitewide": {"sitewide-footer-ciid": "********", "sitewide-desc": "GF_Footer--020221", "footer": {"type": "sitewide", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components": [{"instanceDesc": "footerSpacer_040820", "name": "TextHeadline", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "32px", "large": "48px"}, "text": "[spacer]", "style": {"mobile": {"color": "transparent"}, "desktop": {"color": "transparent"}}}}, {"instanceName": "gap-footer", "name": "Footer", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "1217px", "large": "679px"}, "socialLinks": [{"to": "https://www.facebook.com/gap/", "text": "Follow on Facebook"}], "emailRegistration": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gapfactory", "locale": "en_US", "data": {"classes": "footer-email-register__title-wrapper", "style": {}, "html": "<span className=\"footer-email-register__with-break-large-widths\" style=\"text-transform: none;\">Join our email list to receive 15% off</span><span><span style=\"text-transform: none;\"> &amp; free shipping on your next purchase.</span><span>*</span></span>"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"classes": "legal", "style": {}, "html": "<p><span class=\"asterisk\">*</span>Valid for first-time registrants only &amp; applies to <span>reg. price items only.</span> <a onclick=\"return contentItemLink(this,'','CS_Footer_PrivacyPolicy');\" href=\"https://corporate.gapinc.com/en-us/consumer-privacy-policy\">Privacy Policy</a></p>"}}}, "footerCustomerSupport": {"mobile": {"links": [{"type": "link", "text": "Store Locator", "to": "/customerService/storeLocator.do?mlink=1037267,********,CS_Footer_StoreLocator&clink=********", "className": "footer-line", "style": {"marginLeft": "-2px"}}, {"type": "link", "text": "Customer Service", "to": "/customerService/info.do?cid=1037175&mlink=1037267,********,CS_Footer_CustomerService&clink=********", "className": "footer-line", "style": {"marginLeft": "-2px"}}, {"type": "link", "text": "Orders & Returns", "to": "/profile/order_history.do?isNav=true&mlink=1037267,********,CS_Footer_Returns&clink=********", "className": "footer-line", "style": {"marginLeft": "-2px"}}, {"type": "link", "text": "Shipping", "to": "/customerService/info.do?cid=1037495&cs=shipping_and_handling&mlink=1037495,18938778,csLink_37495&clink=18938778", "className": "footer-line", "style": {"marginLeft": "-2px"}}, {"type": "accordion", "text": "GiftCards", "accordionLinks": [{"type": "link", "text": "Buy eGiftCards", "to": "https://gapfactory.cashstar.com/store/recipient?ref=gcLP_egc&locale=en-us", "target": "_blank", "className": "footer-line"}, {"type": "link", "text": "Buy GiftCards", "to": "/customerService/info.do?cid=1067353&mlink=1037267,********,CS_Footer_Giftcards&clink=********", "target": "_blank", "className": "footer-line"}]}, {"type": "accordion", "text": "Gap Credit Card", "accordionLinks": [{"type": "link", "text": "Manage My Account", "to": "https://www3.onlinecreditcenter6.com/consumergen2/login.do?subActionId=1000&clientId=gap&langId=en&accountType=plcc&cmpid=CR_MNG_ACT", "target": "_blank", "className": "footer-line"}, {"type": "link", "text": "GapCard Benefits", "to": "https://www.gap.com/profile/info.do?cid=1033021&sitecode=gffcfl7d2", "target": "_blank", "className": "footer-line"}, {"type": "link", "text": "eApply Now", "to": "https://apply.syf.com/eapply/eapply.action?clientCode=GAP&sitecode=gffcfl7d1", "target": "_blank", "className": "footer-line"}]}, {"type": "link", "text": "Careers", "to": "https://corporate.gapinc.com/en-us/careers/gap-careers", "target": "_blank", "rel": "noopener", "className": "footer-line", "style": {"marginLeft": "-2px"}}, {"type": "link", "text": "Gap Sustainability", "to": "https://www.gapincsustainability.com/", "target": "_blank", "rel": "noopener", "className": "footer-line", "style": {"marginLeft": "-2px"}}, {"type": "link", "text": "Shop Banana Republic Factory", "to": "http://www.bananarepublicfactory.com/", "target": "_blank", "rel": "noopener", "className": "footer-line", "style": {"marginLeft": "-2px"}}]}, "desktop": {"columns": [{"header": {}, "links": []}, {"header": {"text": "Customer Support", "className": "", "style": {"fontSize": "15px", "fontWeight": "500", "letterSpacing": "0.5px", "textTransform": "uppercase"}, "link": "/customerService/info.do?cid=2136&mlink=1037267,********,CS_Footer_CustomerService&clink=********"}, "links": [{"type": "link", "text": "Store Locator", "to": "/customerService/storeLocator.do?mlink=1037267,********,CS_Footer_StoreLocator&clink=********", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}, {"type": "link", "text": "1.844.GFS.ONLINE (1.844.437.6654)", "to": "/customerService/info.do?cid=1037175&mlink=1037267,********,CS_Footer_CustomerService&clink=********", "target": "_blank", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}, {"type": "link", "text": "Customer Service", "to": "/customerService/info.do?cid=1037175&mlink=1037267,********,CS_Footer_CustomerService&clink=********", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}, {"type": "link", "text": "Returns", "to": "/customerService/info.do?cid=1037494&cs=items_bought_online&mlink=1037267,********,CS_Footer_Returns&clink=********", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}, {"type": "link", "text": "Shipping", "to": "/customerService/info.do?cid=1037495&cs=shipping_and_handling&mlink=1037495,18938778,csLink_37495&clink=18938778", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}, {"type": "link", "text": "Track Your Order", "to": "https://secure-www.gapfactory.com/profile/order_status_gateway.do?cs=items_bought_online&mlink=1037267,********,CS_Footer_TrackYourOrder&clink=********", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}, {"type": "link", "text": "GiftCards", "to": "/customerService/info.do?cid=1067353&mlink=1037267,********,CS_Footer_Giftcards&clink=********", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "marginBottom": "7rem", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}]}, {"header": {"text": "About Us", "className": "", "style": {"fontSize": "15px", "fontWeight": "500", "letterSpacing": "0.5px", "textTransform": "uppercase"}, "link": "", "target": "_blank", "rel": "noopener"}, "links": [{"type": "link", "text": "Gap For Good", "to": "/browse/info.do?cid=1151006&mlink=1037267,********,CS_Footer_GapforGood&clink=********", "target": "_blank", "rel": "noopener", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}, {"type": "link", "text": "Careers", "to": "https://corporate.gapinc.com/en-us/careers/gap-careers", "target": "_blank", "rel": "noopener", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}, {"type": "link", "text": "Get Coupons", "to": "/browse/info.do?cid=1157266&mlink=1037267,********,CS_Footer_Storecoupons&clink=********", "target": "_blank", "rel": "noopener", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}]}, {"header": {"text": "GapCard", "className": "", "style": {"fontSize": "15px", "fontWeight": "500", "letterSpacing": "0.5px", "textTransform": "uppercase"}, "link": "/profile/info.do?cid=1033021"}, "links": [{"type": "link", "text": "eApply Now", "newTo": "https://apply.syf.com/eapply/eapply.action?clientCode=GAP&sitecode=gffcfl7d1", "to": "https://apply.syf.com/eapply/eapply.action?clientCode=GAP&sitecode=gffcfl7d1", "target": "_blank", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}, {"type": "link", "text": "Activate Card", "to": "https://gap.syf.com/activate?&sitecode=gfgcfl8c1", "target": "_blank", "rel": "noopener", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}, {"type": "link", "text": "Review Card Benefits", "to": "https://www.gap.com/profile/info.do?cid=1033021&sitecode=gffcfl7d2", "target": "_blank", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}, {"type": "link", "text": "Pay Bill", "to": "https://www.synchronycredit.com/eSecurity/Login/login.action?clientId=gap&accountType=generic&langId=en", "target": "_blank", "rel": "noopener", "style": {"fontSize": "12px", "letterSpacing": "0.25px", "paddingTop": ".3rem", "paddingBottom": ".3rem"}}]}, {"header": {}, "links": []}]}}, "copyRights": {"rows": [[{"text": "© 2021 The Gap, Inc.", "style": {"fontSize": "11px"}}, {"text": "Privacy Policy", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy?mlink=1037267,********,CS_Footer_PrivacyPolicy&clink=********", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "Do Not Sell My Info", "doNotSell": true, "style": {"fontSize": "11px"}}, {"text": "Interest Based Ads", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy?mlink=1037267,********,CS_Footer_InterestBasedAds&clink=********##Cookies", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "Your California Privacy Rights", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy?mlink=1037267,********,CS_Footer_CAPrivacyRights&clink=********#Your-California-Privacy-Rights", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "California Transparency in Supply Chains Act", "to": "https://www.gapinc.com/content/gapinc/html/sustainability/ca-transparency-insupplychainsact.html?mlink=1037267,********,CS_Footer_CATransparencyAct&clink=********", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "Terms of Use", "to": "/customerService/info.do?cid=1037507&mlink=1037267,********,CS_Footer_TermsOfUse&clink=********", "style": {"fontSize": "11px"}}, {"text": "Careers", "to": "https://jobs.gapinc.com/gap-home?mlink=1037267,********,CS_Footer_Careers&clink=********", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "Sustainability", "to": "https://www.gapincsustainability.com/", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "About Gap Inc.", "to": "http://www.gapinc.com/content/gapinc/html/aboutus.html?mlink=1037267,********,<PERSON>_Footer_AboutGapInc&clink=********", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}], [{"text": "Americans with Disabilities Act", "to": "/customerService/info.do?cid=1041729&mlink=1037267,********,CCS_Footer_AmericansDisabilityAct&clink=********", "style": {"fontSize": "11px"}}, {"text": "Gap Inc. Policies", "to": "http://www.gapincsustainability.com/policies", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}]]}}}]}, "promodrawer": {"name": "PromoDrawerComponentV2", "type": "sitewide", "sitewide-promodrawer-ciid": "********", "instanceName": "promoDrawer-********", "experimentRunning": true, "data": {"buildInfo": ["********", "GF"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "info", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "Profile", "loyalty_value_center"], "collapseOnScroll": true}, "autoFire": "scroll", "disabledAutoFirePageTypes": ["category"], "promos": [], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "style": {"mobile": {"fontSize": ".8em"}}, "text": "{--! headerText !--}"}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}"}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "iconUrl": "/Asset_Archive/GPWeb/content/promo_drawer/assets/minus-white.svg", "iconAltText": "Open icon", "linkWithModalDisplayStyle": "none", "subHeaderText": "(5 available)"}, "closedState": {"headerText": "50% off tees, 40% off dresses", "subHeaderText": "30% off jeans & pants", "iconUrl": "/Asset_Archive/GPWeb/content/promo_drawer/assets/plus-white.svg", "iconAltText": "Closed icon"}}, "pd_id": "pdid_1534541049574"}}, "sitewide-logo-ciid": "18873880", "sitewide-logo-desc": "061620_Logo", "logo": {"type": "sitewide", "name": "Logo", "logoImgPath": "/Asset_Archive/GFWeb/content/0018/291/370/assets/GAP_Factory_BOX_CLASSIC.svg", "lightLogoImgPath": "/Asset_Archive/GFWeb/content/0018/291/370/assets/GAP_Factory_BOX_CLASSIC.svg", "darkLogoImgPath": "/Asset_Archive/GFWeb/content/0018/291/370/assets/GAP_Factory_BOX_CLASSIC.svg", "isSquare": true, "criticalCss": [{"width": "30em"}]}, "fullbleed": {"type": "sitewide", "name": "FullBleedHeader", "data": {"options": {"isDesktopVisible": true, "isMobileVisible": true}, "desktop": {"contrast": "light", "style": {}}, "mobile": {"contrast": "light", "style": {}}}}, "sitewide-headline-ciid": "20236239", "sitewide-headline-desc": "03112021_Headline", "headline": {"name": "OptimizelyPlaceholder", "type": "sitewide", "instanceName": "gpus_headline_revretention", "instanceDesc": "01012021_revretentionbanner", "experimentRunning": true, "useGreyLoadingEffect": false, "data": {"placeholderSettings": {"useGreyLoadingEffect": false}, "shouldWaitForOptimizely": true, "defaultHeight": {"large": "0", "small": "0"}}}, "sitewide-emegencyBanners-ciid": "20235644", "sitewide-emegencyBanners-desc": "03112021_EmergencyBanners", "mobileemergencybanner": {"name": "OptimizelyPlaceholder", "type": "sitewide", "instanceName": "dpg_emergency_banner_mob", "isAsyncExperiment": true, "useGreyLoadingEffect": false, "data": {"defaultHeight": {"large": "0px", "small": "0px"}, "placeholderSettings": {"useGreyLoadingEffect": false}}}, "desktopemergencybanner": {"name": "OptimizelyPlaceholder", "type": "sitewide", "instanceName": "dpg_emergency_banner_desk", "isAsyncExperiment": true, "useGreyLoadingEffect": false, "data": {"defaultHeight": {"large": "0px", "small": "0px"}, "placeholderSettings": {"useGreyLoadingEffect": false}}}, "popup": {"name": "DynamicModal", "type": "sitewide", "instanceName": "092920-email-popup", "sitewide-popup-ciid": "19344795", "experimentRunning": true, "data": {"excludePageTypes": ["Information", "ShoppingBag", "Profile"], "placeholderSettings": {"useGreyLoadingEffect": false}, "lazy": false, "defaultHeight": "1px", "shouldWaitForOptimizely": true, "closeButtonAriaLabel": "close email sign up modal", "localStorageKey": "emailPopup", "modalSize": "max", "title": "", "style": {"padding": "1rem"}, "breakpoints": ["large"], "layoutData": {"desktop": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0 1rem 2rem 1rem"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "row", "justifyContent": "space-between"}, "components": [{"name": "EmailRegistrationForm", "type": "sitewide", "tileStyle": {"desktop": {"width": "50%"}}, "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {"padding": "3rem 0 0 0", "margin": "0 1rem 0 0"}, "mobile": {"padding": "20px", "margin": "1rem"}}, "targetURL": "/profile/info.do?cid=1044955&mlink=1038092,19344795,HP_POPUP&clink=19344795", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:NA;ON:ON_Persado8;PL:NA;AT:NA;BRFS:NA;GPO:NA"]}, "customText": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "optly-header-container", "style": {}, "html": "<h1 class=\"optly-main-header optly-main-header\" style=\"color: rgb(0, 0, 0); font-size: 1.5rem; line-height: 1\">Perk Alert: Get 15% Off and Free Shipping</h1>"}}, "subtitle": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "", "style": {}, "html": "<h3 class=\"heading-d optly-lighter\" style=\"font-weight:400; line-height: 1.43\">As a thank you for signing up for emails, take 15% off online with free shipping. Plus, we're giving you access to exclusive offers, new arrivals, and more.</h3>"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "", "style": {"marginTop": "8em"}, "html": "<p class=\"optly-lighter\" style=\"font-size:0.8em;font-weight:400;color:#666;\">*Valid for first-time registrants & applies to regular price items only.<a href=\"/customerService/info.do?cid=1165787\" style=\"font-size:1em;text-decoration:underline;text-transform:uppercase;margin-left: 4px;\">Details</a></p><p><a href=\"/customerService/info.do?cid=2331\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: block; line-height: 1.3; text-decoration: underline; font-size:0.8em;\">*Privacy Policy</a></p>"}}}, "textInputOptions": {"label": "Your Email Address", "errorMessage": "Please enter a valid email address", "desktop": {"className": "", "crossBrand": false, "inverse": false}, "mobile": {"className": "", "crossBrand": false, "inverse": false}}, "submitButtonOptions": {"text": "Claim your unique code", "desktop": {"className": "", "variant": "solid", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "primary", "style": {"textTransform": "uppercase"}}, "mobile": {"className": "", "variant": "solid", "size": "small", "fullWidth": true, "crossBrand": false, "color": "primary", "style": {"textTransform": "uppercase"}}}, "errorNotificationAriaLabel": "Error"}}, {"type": "builtin", "name": "img", "tileStyle": {"desktop": {"maxWidth": "325px", "marginLeft": "0.35rem"}}, "data": {"props": {"src": "/Asset_Archive/GFWeb/content/0019/344/795/assets/GFOL_EMAIL_POPUP_520x693.jpg", "alt": "Email Sign Up Pop-up"}}}]}}}}]}}, "mobile": {"shouldDisplay": false, "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0 1rem 2rem 1rem"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "row", "justifyContent": "space-between"}, "components": [{"name": "EmailRegistrationForm", "type": "sitewide", "tileStyle": {}, "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {}, "mobile": {}}, "targetURL": "/profile/info.do?cid=82637&mlink=5151,8980857,persado_signup_CONTROL", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:NA;ON:ON_Persado8;PL:NA;AT:NA;BRFS:NA;GPO:NA"]}, "customText": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "optly-header-container", "style": {}, "html": "<h1 class=\"optly-main-header optly-main-header\" style=\"color: rgb(0, 0, 0); font-size: 1.5rem; line-height: 1; text-transform: uppercase;\">Perk Alert: Get 15% Off and Free Shipping</h1>"}}, "subtitle": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "", "style": {}, "html": "<h3 class=\"heading-d optly-lighter\" style=\"font-weight:400;\">As a thank you for signing up for emails, take 15% off online with free shipping. Plus, we're giving you access to exclusive offers, new arrivals, and more.</h3>"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "", "style": {"marginTop": "8em"}, "html": "<p class=\"optly-lighter\" style=\"font-size:0.8em;font-weight:400;color:#666;\">*Valid for first-time registrants & applies to regular price items only.<a href=\"/customerService/info.do?cid=1165787\" style=\"font-size:1em;text-decoration:underline;text-transform:uppercase;margin-left: 4px;\">Details</a></p><p><a href=\"/customerService/info.do?cid=2331\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: block; line-height: 1.3; text-decoration: underline; font-size:0.8em;\">*Privacy Policy</a></p>"}}}, "textInputOptions": {"label": "Your Email Address", "errorMessage": "Please enter a valid email address", "desktop": {"className": "", "crossBrand": false, "inverse": false}, "mobile": {"className": "", "crossBrand": false, "inverse": false}}, "submitButtonOptions": {"text": "Claim your unique code", "desktop": {"className": "", "variant": "solid", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "primary", "style": {"textTransform": "uppercase"}}, "mobile": {"className": "", "variant": "solid", "size": "small", "fullWidth": true, "crossBrand": false, "color": "primary", "style": {"textTransform": "uppercase"}}}, "errorNotificationAriaLabel": "Error"}}]}}}}]}}}, "analytics": {"on_close": {"tracking_enabled": true, "content_id": "email_popup_close", "link_name": "email_popup_close"}, "on_submit": {"tracking_enabled": true, "content_id": "email_popup_submit", "link_name": "email_popup_submit"}}}}, "sitewide-edfs-ciid": "19313942", "sitewide-edfs-desc": "092920_EDFS", "edfssmall": {"instanceName": "edfs-header-small", "name": "MktEdfsSmall", "type": "sitewide", "experimentRunning": false, "data": {"lazy": false, "defaultData": {"textStrong": "Free Shipping ", "text": "on $50+, Free Returns on All Orders.", "detailsLink": "Details"}, "modalUrl": "/Asset_Archive/GFWeb/content/0018/141/968/edfsLegalpop--052620.html?v=0", "modalTitle": "Everyday Free Shipping", "modalCloseButtonAriaLabel": "Close Pop-Up"}}, "edfslarge": {"instanceName": "edfs-header-large", "name": "MktEdfsLarge", "type": "sitewide", "experimentRunning": false, "data": {"lazy": false, "defaultData": {"text": "Free Shipping on $50+, Free Returns on All Orders.", "detailsLink": "Details"}, "modalUrl": "/Asset_Archive/GFWeb/content/0018/141/968/edfsLegalpop--052620.html?v=0", "modalTitle": "Everyday Free Shipping", "modalCloseButtonAriaLabel": "Close Pop-Up"}}, "sitewide-topnav-ciid": "20188717", "sitewide-topnav-desc": "061620_TopNav", "search": {"type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["Shorts", "Dresses", "Tanks", "Skorts", "Masks"]}}, "hamnav": {"type": "sitewide", "name": "HamburgerNav", "data": {"activeDivisions": [{"name": "NEW", "customStyles": {"color": "#000000", "font-weight": ""}, "cid": "127361", "divisionHeaders": [{"name": "SHOP NEW ARRIVALS", "customStyles": {"TBD": "TBD"}, "cid": "5361", "isAccordionOpened": true, "categories": [{"name": "Women", "customStyles": {"TBD": "TBD"}, "cid": "5362", "link": "/browse/category.do?cid=8792&nav=hamnav%3ANew%20%2B%20Now%3AShop%20New%20Arrivals%3AWomen"}]}]}, {"name": "Gap factory", "customStyles": {"TBD": "TBD"}, "link": "gapfactory.com"}], "topBanner": {"altText": "alt text", "imgSrc": "image url"}, "bottomBanner": {"altText": "alt text", "imgSrc": "image url"}, "exclusionIds": ["49708", "49709"]}}, "topnav": {"type": "sitewide", "name": "MegaNav", "data": {"isNavSticky": true, "classStyles": {"topnav .divisionLink.giftshop": "color:#ff67a7;", "meganav:not(.custom) ul.wcd-giftshop .catnav--header>span": "color:#ff67a7;", "topnav .divisionLink.clearance": "color:#e51937;", "topnav .divisionLink.gapTextLink": "text-transform: none;", "topnav-container": "margin-bottom:10px;"}, "activeDivisions": [{"name": "New Arrivals", "divisionId": ["1137421"], "megaNavOrder": [["1137422"]], "numberOfColumns": {}, "exclusionIds": [], "customStyles": {}}, {"name": "Gap for Good", "divisionId": ["/browse/info.do?cid=1151006&mlink=1037059,20188717,Meganav_GapForGood&clink=20188717", "1150681"], "megaNavOrder": [["1151005"], ["1150682"]], "numberOfColumns": {}, "exclusionIds": [], "customStyles": {}}, {"name": "<PERSON><PERSON>", "divisionId": ["1137431"], "megaNavOrder": [["1165338", "1165339"], ["1165340", "1165341"], ["1165342", "1165344"], ["1165343", "1165345"], ["1165346", "1165347"]], "numberOfColumns": {}, "exclusionIds": [], "customStyles": {}}, {"name": "Logo", "divisionId": ["1164903"], "megaNavOrder": [["1165351"], ["1165352"], ["1165353", "1165354"], ["1165355", "1165600"], ["1165356", "1165601"]], "numberOfColumns": {}, "exclusionIds": [], "customStyles": {}}, {"name": "Women", "divisionId": ["/browse/division.do?cid=1040941&mlink=1037059,20188717,Megnav_Women&clink=20188717", "1040941"], "megaNavOrder": [["1075602", "1144615"], ["1083398"], ["1144616"], ["1099767", "1089399"]], "exclusionIds": [], "customStyles": {"1042792": {"colorScheme": "sale"}, "1075601": {"colorScheme": "sale"}}}, {"name": "Men", "divisionId": ["/browse/division.do?cid=1040942&mlink=1037059,20188717,Megnav_Men&clink=20188717", "1040942"], "megaNavOrder": [["1084557", "1144614"], ["1083400"], ["1144612", "1162911"]], "exclusionIds": [], "customStyles": {"1042764": {"colorScheme": "sale"}}}, {"name": "Girls", "divisionId": ["/browse/division.do?cid=1040943&mlink=1037059,20188717,Megnav_Girls&clink=20188717", "1040943"], "megaNavOrder": [["1084367", "1144611"], ["1084370"], ["1144609"], ["1093865"]], "exclusionIds": [], "customStyles": {"1042785": {"colorScheme": "sale"}}}, {"name": "Boys", "divisionId": ["/browse/division.do?cid=1079661&mlink=1037059,20188717,Megnav_Boys&clink=20188717", "1079661"], "megaNavOrder": [["1083130", "1144606"], ["1083133"], ["1144607"], ["1093866"]], "exclusionIds": [], "customStyles": {"1042766": {"colorScheme": "sale"}, "1083131": {"colorScheme": "sale"}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=1091867&mlink=1037059,20188717,Megnav_Toddler&clink=20188717", "1091867"], "megaNavOrder": [["1093607", "1144054"], ["1049875"], ["1049876"], ["1093549", "1144264"]], "exclusionIds": [], "customStyles": {"1042786": {"colorScheme": "sale"}, "1042787": {"colorScheme": "sale"}, "1093555": {"colorScheme": "sale"}, "1114128": {"colorScheme": "sale"}}}, {"name": "Baby", "divisionId": ["/browse/division.do?cid=1040944&mlink=1037059,20188717,Megnav_Boys&clink=20188717", "1040944"], "megaNavOrder": [["1093606", "1144585"], ["1064166"], ["1064167"], ["1064700"], ["1075607", "1144583"]], "exclusionIds": [], "customStyles": {"1091767": {"colorScheme": "sale"}, "1091779": {"colorScheme": "sale"}}}, {"name": "Clearance", "divisionId": ["/browse/division.do?cid=1150511", "1150511"], "megaNavOrder": [["1150513"], ["1171882"]], "numberOfColumns": {"1150513": 2}, "exclusionIds": [], "customStyles": {"1150511": {"className": "clearance"}}}, {"name": "Masks", "divisionId": ["/browse/category.do?cid=1093511&mlink=1037059,20188717,Megnav_Masks&clink=20188717", "1164893"], "megaNavOrder": [["1164894"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1164893' href='/browse/category.do?cid=1093513&scrollToCid=1157832&mlink=18898025,topnav_newarrivalsmask,visnav&clink=18898025' class='catnav--item--link' style='max-width: 265px;'><img src='/Asset_Archive/GFWeb/content/0020/188/717/assets/GOL_HP_MEGANAV_MASKS_ADULT.jpg' alt='shop masks'><img style='left:0;' src='/Asset_Archive/GFWeb/content/0020/188/717/assets/GOL_HP_MEGANAV_MASKS_ADULT.svg' alt='shop masks'></a></li></ul></li>"]], "numberOfColumns": {}, "exclusionIds": [], "customStyles": {}}, {"name": "Gap.com", "divisionId": ["http://www.gap.com/browse/home.do?cid=5058&mlink=1037059,18446873,GFOL_MainNav_GOLHP&clink=18446873", "5058"], "megaNavOrder": [], "exclusionIds": [], "customStyles": {"5058": {"className": "gapTextLink"}}}]}}}, "home": {"instanceName": "FactoryHomepage_031721", "ciid": "20183304", "type": "home", "name": "HomeMultiSimple", "components": [{"instanceName": "HP_HP_PromoTB1_031721", "instanceDesc": "HP_HP_PromoTB1_031721", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {}, "components": []}}}}, {"instanceName": "HP_A", "instanceDesc": "HP_A_031721", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "shouldWaitForOptimizely": false, "defaultHeight": {"small": "578px", "large": "323px"}, "desktop": {"shouldDisplay": true, "data": {"style": {"maxWidth": "1920px", "position": "relative", "margin": "0 auto 2.7rem"}, "components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {}}, "data": {"carouselOptions": {"infinite": true, "slidesToShow": 1, "autoplay": true, "speed": 0, "autoplaySpeed": 2000, "displayPlayPauseBtn": false, "fade": true, "className": "", "displayArrows": {"mobile": false, "desktop": false}}, "style": {}, "components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "overlay": {"alt": "Men's everyday looks", "srcUrl": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_DESK_A1.SVG?v=2", "style": {}, "desktopStyle": {"width": "81.25%"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1052118#pageId=0&department=75&mlink=1038092,20183304,HP_A"}, "image": {"alt": "", "srcUrl": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_DESK_A_ANIM1.JPEG", "style": {"display": "block"}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "overlay": {"alt": "Women's everyday looks", "srcUrl": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_DESK_A3.SVG?v=2", "style": {}, "desktopStyle": {"width": "81.25%", "left": "unset", "right": "0"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1042791&scrollToCid=1153391#pageId=0&department=136&mlink=1038092,20183304,HP_A"}, "image": {"alt": "", "srcUrl": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_DESK_A_ANIM3.JPEG", "style": {"display": "block"}, "desktopStyle": {}}}}}]}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "overlay": {"alt": "Women's everyday looks", "srcUrl": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_DESK_A1.SVG", "style": {}, "desktopStyle": {"width": "81.25%"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1042791&scrollToCid=1153391#pageId=0&department=136&mlink=1038092,20183304,HP_A"}, "image": {"alt": "", "srcUrl": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_DESK_A_ANIM2.JPEG", "style": {"display": "block"}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "overlay": {"alt": "Boys' everyday looks", "srcUrl": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_DESK_A3.SVG", "style": {}, "desktopStyle": {"width": "81.25%", "left": "unset", "right": "0"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1064875#pageId=0&department=16&mlink=1038092,20183304,HP_A"}, "image": {"alt": "", "srcUrl": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_DESK_A_ANIM4.JPEG", "style": {"display": "block"}, "desktopStyle": {}}}}}]}}}}], "modalCloseButtonAriaLabel": "Close", "buttonSetting": {"nextArrowAlt": "next", "pauseAltText": "pause", "playAltText": "play", "prevArrowAlt": "previous"}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "left": "50%", "top": "0", "width": "18.75%", "transform": "translate(-50%, 0)", "zIndex": "1"}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "background": {"image": {"alt": "", "srcUrl": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_DESK_A2.SVG?v=2", "style": {"display": "block"}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "left": "50%", "top": "65%", "transform": "translate(-50%, 0)", "zIndex": "2"}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {"padding": "0", "margin": "0 auto", "maxWidth": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0% 0 0", "a, button": {"background": "transparent", "color": "#122344", "borderColor": "#122344", "borderWidth": "1px 0px", "borderStyle": "solid", "fontWeight": "400", "letterSpacing": "0", "width": "100%", "fontSize": "18px", "outline": "none"}, "button": {"padding": "5px 4px 6px"}, "ul": {"borderWidth": "0", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 0 1px", "a": {"transitionDuration": "0.2s", "boxSizing": "border-box", "border": "none", "padding": "8px 4px"}}, "li:last-child": {"borderBottom": "1px solid #122344"}}}, "desktopStyle": {"padding": "0", "a, button": {"background": "transparent", "padding": "5px 0.8rem 0.5rem", "color": "#FFFFFF", "borderColor": "#FFFFFF", "borderWidth": "1px", "fontSize": "14px"}, "button": {}, "ul": {"position": "absolute", "borderWidth": "0 1px", "padding": "0", "li a": {"color": "#122344"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop from $9"}, "style": {"mobile": {}, "desktop": {}}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1042791&scrollToCid=1153391#pageId=0&department=136&mlink=1038092,20183304,HP_A"}, {"text": "Men", "href": "/browse/category.do?cid=1052118#pageId=0&department=75&mlink=1038092,20183304,HP_A"}, {"text": "Girls", "href": "/browse/category.do?cid=1064872#pageId=0&department=48&mlink=1038092,20183304,HP_A"}, {"text": "Boys", "href": "/browse/category.do?cid=1064875#pageId=0&department=16&mlink=1038092,20183304,HP_A"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1064881#pageId=0&department=165&mlink=1038092,20183304,HP_A"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1064880#pageId=0&department=165&mlink=1038092,20183304,HP_A"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1063532#pageId=0&department=165&mlink=1038092,20183304,HP_A"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1063552#pageId=0&department=166&mlink=1038092,20183304,HP_A"}]}}]}}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "maxWidth": "1920px", "position": "relative", "margin": "0 auto 2.7rem"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "maxWidth": "1920px", "position": "relative"}, "components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {}}, "data": {"carouselOptions": {"infinite": true, "slidesToShow": 1, "autoplay": true, "speed": 0, "autoplaySpeed": 2000, "displayPlayPauseBtn": false, "fade": true, "pauseOnHover": true, "className": "", "displayArrows": {"mobile": false, "desktop": false}}, "style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_MOB_A_ANIM1.SVG", "desktopSrcUrl": "", "style": {}, "desktopStyle": {}}, "background": {"linkData": {"to": "/browse/category.do?cid=1052118#pageId=0&department=75&mlink=1038092,20183304,HP_A"}, "image": {"alt": "Men's everyday looks", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_MOB_A_ANIM1.JPEG", "desktopSrcUrl": "", "style": {"display": "block"}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_MOB_A_ANIM2.SVG", "desktopSrcUrl": "", "style": {}, "desktopStyle": {}}, "background": {"linkData": {"to": "/browse/category.do?cid=1042791&scrollToCid=1153391#pageId=0&department=136&mlink=1038092,20183304,HP_A"}, "image": {"alt": "Women's everyday looks", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_MOB_A_ANIM2.JPEG", "desktopSrcUrl": "", "style": {"display": "block"}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_MOB_A_ANIM3.SVG", "desktopSrcUrl": "", "style": {}, "desktopStyle": {}}, "background": {"linkData": {"to": "/browse/category.do?cid=1064875#pageId=0&department=16&mlink=1038092,20183304,HP_A"}, "image": {"alt": "Boy's everyday looks", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_MOB_A_ANIM3.JPEG", "desktopSrcUrl": "", "style": {"display": "block"}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_MOB_A_ANIM4.SVG", "desktopSrcUrl": "", "style": {}, "desktopStyle": {}}, "background": {"linkData": {"to": "/browse/category.do?cid=1052118#pageId=0&department=75&mlink=1038092,20183304,HP_A"}, "image": {"alt": "Men's everyday looks", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_MOB_A_ANIM4.JPEG", "desktopSrcUrl": "", "style": {"display": "block"}, "desktopStyle": {}}}}}], "modalCloseButtonAriaLabel": "Close", "buttonSetting": {"nextArrowAlt": "next", "pauseAltText": "pause", "playAltText": "play", "prevArrowAlt": "previous"}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"position": "absolute", "top": "90%"}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "background": {"image": {"alt": "Our new everyday looks are all of the above", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_A/031721_HP_MOB_A.SVG?v=2", "desktopSrcUrl": "", "style": {"display": "block"}, "desktopStyle": {}}}}}]}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {"padding": "0", "margin": "0 auto", "maxWidth": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0 0 0", "a, button": {"background": "transparent", "color": "#122344", "borderColor": "#122344", "borderWidth": "1px 0px", "borderStyle": "solid", "fontWeight": "400", "letterSpacing": "0", "width": "100%", "fontSize": "18px", "outline": "none"}, "button": {"padding": "5px 4px 6px"}, "ul": {"borderWidth": "0", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 0 1px", "a": {"transitionDuration": "0.2s", "boxSizing": "border-box", "border": "none", "padding": "8px 4px"}}, "li:last-child": {"borderBottom": "1px solid #122344"}}}, "desktopStyle": {"padding": "0", "a, button": {"background": "transparent", "padding": "5px 0.8rem 0.5rem", "color": "#FFFFFF", "borderColor": "#FFFFFF", "borderWidth": "1px", "fontSize": "14px"}, "button": {}, "ul": {"position": "absolute", "borderWidth": "0 1px", "li a": {"color": "#122344"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop from $9"}, "style": {"mobile": {}, "desktop": {}}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1042791&scrollToCid=1153391#pageId=0&department=136&mlink=1038092,20183304,HP_A"}, {"text": "Men", "href": "/browse/category.do?cid=1052118#pageId=0&department=75&mlink=1038092,20183304,HP_A"}, {"text": "Girls", "href": "/browse/category.do?cid=1064872#pageId=0&department=48&mlink=1038092,20183304,HP_A"}, {"text": "Boys", "href": "/browse/category.do?cid=1064875#pageId=0&department=16&mlink=1038092,20183304,HP_A"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1064881#pageId=0&department=165&mlink=1038092,20183304,HP_A"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1064880#pageId=0&department=165&mlink=1038092,20183304,HP_A"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1063532#pageId=0&department=165&mlink=1038092,20183304,HP_A"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1063552#pageId=0&department=166&mlink=1038092,20183304,HP_A"}]}}]}}}]}}}}, {"instanceName": "certona_hp2", "instanceDesc": "011210_StealsAndDeals", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "371px", "large": "379px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "column"}, "components": [{"instanceName": "certona-headlineText", "name": "TextHeadline", "type": "sitewide", "tileStyle": {"mobile": {"maxWidth": "640px", "width": "100%", "margin": "0 auto 1rem", "paddingLeft": "0.5rem", "boxSizing": "border-box"}, "desktop": {"maxWidth": "1425px", "width": "100%", "margin": "0 auto", "padding": "0 1rem"}}, "data": {"text": "Steals and Deals", "defaultHeight": "34px", "style": {"mobile": {"whiteSpace": "pre-line", "fontWeight": "500"}, "desktop": {"whiteSpace": "pre-line", "fontWeight": "500"}}, "className": {"mobile": "uppercase", "desktop": "uppercase"}}}, {"name": "ButtonList", "type": "sitewide", "tileStyle": {"mobile": {"display": "none"}, "desktop": {"width": "100%", "maxWidth": "1444px", "margin": "0 auto .5rem", "padding": "8px 6px", "lineHeight": "12px"}}, "data": {"viewType": {"className": "mkt_button-list_cta-container--rows-1"}, "containerStyle": {"desktop": {}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"textAlign": "left"}}, "links": {"buttonClasses": "", "style": {"mobile": {"color": "#122344", "textAlign": "center", "fontWeight": "700", "fontSize": "1rem", "letterSpacing": "1px", "backgroundColor": "#FFFFFF", "padding": "0.75rem 0.5rem"}, "desktop": {"backgroundColor": "transparent", "borderColor": "#122344", "color": "#122344", "fontSize": "10px", "letterSpacing": "1px", "padding": "0 12px"}}, "heading": {"text": "Shop Now"}, "content": [{"text": "Women", "href": "/browse/category.do?cid=1055577&department=136&mlink=1038092,18901827,gaphome2_CTA_W"}, {"text": "Men", "href": "/browse/category.do?cid=1057939&department=75&mlink=1038092,18901827,gaphome2_CTA_M"}, {"text": "Girls", "href": "/browse/category.do?cid=1057941&department=48&mlink=1038092,18901827,gaphome2_CTA_G"}, {"text": "Boys", "href": "/browse/category.do?cid=1063978&department=16&mlink=1038092,18901827,gaphome2_CTA_B"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1091835&department=165&mlink=1038092,18901827,gaphome2_CTA_TG"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1091837&department=165&mlink=1038092,18901827,gaphome2_CTA_TB"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1057967&department=166&mlink=1038092,18901827,gaphome2_CTA_BG"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1063932&department=166&mlink=1038092,18901827,gaphome2_CTA_BB"}]}}}, {"name": "Recommendations", "type": "home", "tileStyle": {"mobile": {"boxSizing": "border-box", "margin": "0 auto 2.7rem", "maxWidth": "640px", "paddingLeft": "0rem", "paddingRight": "0.5rem", "width": "100%"}, "desktop": {"boxSizing": "border-box", "margin": "0 auto 44px", "maxWidth": "1920px", "paddingLeft": "0.5rem", "paddingRight": "0.5rem", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome2_rr", "displayTitle": false, "certonaTitle": {"title": "", "style": {"mobile": {"color": "#122344", "display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "fontSize": "1.5rem", "fontWeight": "400", "marginBottom": "0.5rem", "paddingLeft": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}, "desktop": {"paddingLeft": "0.5rem", "color": "#122344", "display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "fontSize": "1.5rem", "fontWeight": "400", "marginBottom": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}}}, "layout": "carousel", "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "showMarketingFlag": false, "showPercentage": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0017/748/073/assets/certona_arrows/gp_certona_gfol-style-arrow3.png", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0017/748/073/assets/certona_arrows/gp_certona_gfol-style-arrow3.png", "nextArrowAlt": "Next", "productTextStyles": {"productTitle": {"style": {"color": "#666666", "textAlign": "left", "fontSize": ".8rem", "marginBottom": "-8px"}}, "productPrice": {"style": {"color": "#666666", "fontSize": ".8rem"}}, "productSalePrice": {"style": {"color": "#d00000", "fontSize": ".8rem", "display": "block", "marginTop": "2px"}}, "productPercentage": {"style": {"color": "#d00000", "fontSize": ".8rem", "marginLeft": "2px"}}, "size": {"width": "90%", "height": "150px"}}, "productCardStyles": {"style": {}}, "gridLayout": {}}}]}}}}, {"instanceName": "HP_B", "instanceDesc": "031721_HP_B", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "817px", "large": "361px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "maxWidth": "1920px", "margin": "0 auto 2.7rem", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "50%", "position": "relative", "paddingBottom": "30px"}, "mobile": {"flex": "unset", "width": "100%", "marginBottom": "2.7rem"}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {"padding": "0", "margin": "0 auto", "maxWidth": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "a, button": {"background": "transparent", "color": "#122344", "borderColor": "#122344", "borderWidth": "1px 0px", "borderStyle": "solid", "fontWeight": "400", "letterSpacing": "0", "width": "100%", "fontSize": "18px", "outline": "none"}, "button": {"padding": "5px 4px 6px"}, "ul": {"borderWidth": "0", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 0 1px", "a": {"transitionDuration": "0.2s", "boxSizing": "border-box", "border": "none", "padding": "8px 4px"}, "li:last-child": {"borderBottom": "1px solid #122344"}}}}, "desktopStyle": {"padding": "0", "position": "absolute", "left": "2.5%", "a, button": {"background": "transparent", "padding": "5px 0 0.5rem", "color": "#122344", "borderColor": "transparent", "borderWidth": "1px", "fontSize": "14px"}, "button": {}, "ul": {"position": "absolute", "borderWidth": "0 1px", "padding": "0", "li a": {"color": "#122344"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop them all"}, "style": {"mobile": {}, "desktop": {}}, "submenu": [{"text": "women", "href": "/browse/category.do?cid=1041618#pageId=0&department=136&mlink=1038092,20183304,HP_B1"}, {"text": "girls", "href": "/browse/category.do?cid=1041723#pageId=0&department=48&mlink=1038092,20183304,HP_B1"}, {"text": "toddler girl", "href": "/browse/category.do?cid=1041922#pageId=0&department=165&mlink=1038092,20183304,HP_B1"}, {"text": "baby girl", "href": "/browse/category.do?cid=1064646#pageId=0&department=165&mlink=1038092,20183304,HP_B1"}]}}]}, "overlay": {"alt": "From $10", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_B1/031721_HP_MOB_B1.SVG", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_B1/031721_HP_DESK_B1.SVG", "style": {}, "desktopStyle": {}}, "background": {"linkData": {"to": "/browse/category.do?cid=1041618#pageId=0&department=136&mlink=1038092,20183304,HP_B1"}, "image": {"alt": "The Spring shop", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_B1/031721_HP_MOB_B1.JPEG", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_B1/031721_HP_DESK_B1.JPEG", "style": {"display": "block"}, "desktopStyle": {}}}}}, {"name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"width": "50%", "position": "relative", "paddingBottom": "30px"}, "mobile": {"flex": "unset", "width": "100%", "marginBottom": "2.7rem"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"display": "block"}, "components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {}}, "data": {"carouselOptions": {"infinite": true, "slidesToShow": 1, "autoplay": true, "speed": 0, "autoplaySpeed": 2000, "displayPlayPauseBtn": false, "fade": true, "className": "", "displayArrows": {"mobile": false, "desktop": false}}, "style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "overlay": {"alt": "Women's T-shirts", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_B2/031721_HP_MOB_B2_ANIM1.SVG?v=2", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_B2/031721_HP_DESK_B2_ANIM1.SVG?v=2", "style": {}, "desktopStyle": {}}, "background": {"linkData": {"to": "/browse/category.do?cid=1127453&scrollToCid=1167376#pageId=0&department=136&mlink=1038092,20183304,HP_B2"}, "image": {"alt": "Fresh new t's from $9", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_B2/031721_HP_MOB_B2.JPEG", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_B2/031721_HP_DESK_B2.JPEG", "style": {"display": "block"}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "overlay": {"alt": "Men's T-shirts", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_B2/031721_HP_MOB_B2_ANIM2.SVG?v=2", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_B2/031721_HP_DESK_B2_ANIM2.SVG", "style": {}, "desktopStyle": {}}, "background": {"linkData": {"to": "/browse/category.do?cid=1127453&scrollToCid=1167376#pageId=0&department=136&mlink=1038092,20183304,HP_B2"}, "image": {"alt": "Fresh new t's from $9", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_B2/031721_HP_MOB_B2.JPEG", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_B2/031721_HP_DESK_B2.JPEG", "style": {"display": "block"}, "desktopStyle": {}}}}}], "modalCloseButtonAriaLabel": "Close", "buttonSetting": {"nextArrowAlt": "next", "pauseAltText": "pause", "playAltText": "play", "prevArrowAlt": "previous"}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "left": "4.75%"}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {"padding": "0", "margin": "0 auto", "maxWidth": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "6% 0 0", "a, button": {"background": "transparent", "color": "#122344", "borderColor": "#122344", "borderWidth": "1px 0px", "borderStyle": "solid", "fontWeight": "400", "letterSpacing": "0", "width": "100%", "fontSize": "18px", "outline": "none"}, "button": {"padding": "5px 4px 6px"}, "ul": {"borderWidth": "0", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 0 1px", "a": {"transitionDuration": "0.2s", "boxSizing": "border-box", "border": "none", "padding": "8px 4px"}}, "li:last-child": {"borderBottom": "1px solid #122344"}}}, "desktopStyle": {"padding": "0", "a, button": {"background": "transparent", "padding": "5px 0 0.5rem", "color": "#122344", "borderColor": "transparent", "borderWidth": "1px", "fontSize": "14px"}, "button": {}, "ul": {"position": "absolute", "borderWidth": "0 1px", "padding": "0", "li a": {"color": "#122344"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "See what's new"}, "style": {"mobile": {}, "desktop": {}}, "submenu": [{"text": "women", "href": "/browse/category.do?cid=1127453&scrollToCid=1167376#pageId=0&department=136&mlink=1038092,20183304,HP_B2"}, {"text": "men", "href": "/browse/category.do?cid=1127459&scrollToCid=1116212#pageId=0&department=75&mlink=1038092,20183304,HP_B2"}, {"text": "girls", "href": "/browse/category.do?cid=1041712&scrollToCid=1173820#pageId=0&department=48&mlink=1038092,20183304,HP_B2"}, {"text": "boys", "href": "/browse/category.do?cid=1041815&scrollToCid=1173818#pageId=0&department=16&mlink=1038092,20183304,HP_B2"}, {"text": "toddler girl", "href": "/browse/category.do?cid=1131155&scrollToCid=1137837#pageId=0&department=165&mlink=1038092,20183304,HP_B2"}, {"text": "toddler boy", "href": "/browse/category.do?cid=1131155&scrollToCid=1139403#pageId=0&department=165&mlink=1038092,20183304,HP_B2"}, {"text": "baby girl", "href": "/browse/category.do?cid=1131155&scrollToCid=1137837#pageId=0&department=165&mlink=1038092,20183304,HP_B2"}, {"text": "baby boy", "href": "/browse/category.do?cid=1131155&scrollToCid=1139403#pageId=0&department=166&mlink=1038092,20183304,HP_B2"}]}}]}}}]}}}}]}}}}, {"instanceName": "HP_C1", "instanceDesc": "031721_HP_C1", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "428px", "large": "320px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.7rem", "position": "relative", "maxWidth": "1920px"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "overlay": {"alt": "You to a tee", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_C1/031721_HP_MOB_C.SVG", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_C1/031721_HP_DESK_C.SVG", "style": {}, "desktopStyle": {}}, "background": {"linkData": {"to": "/browse/category.do?cid=1041699#pageId=0&department=48&mlink=1038092,20183304,HP_C1"}, "image": {"alt": "Brand new graphics and colors that are made to stand out from $9", "srcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_C1/031721_HP_MOB_C.jpeg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/183/304/assets/HP_C1/031721_HP_DESK_C.JPEG", "style": {"display": "block"}, "desktopStyle": {}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "60%", "left": "50%", "transform": "translate(-50%, 0%)", "zIndex": "1"}, "mobile": {}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {"padding": "0", "margin": "0 auto", "maxWidth": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "6% 0 0", "a, button": {"background": "transparent", "color": "#122344", "borderColor": "#122344", "borderWidth": "1px 0px", "borderStyle": "solid", "fontWeight": "400", "letterSpacing": "0", "width": "100%", "fontSize": "18px", "outline": "none"}, "button": {"padding": "5px 4px 6px"}, "ul": {"borderWidth": "0", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 0 1px", "a": {"transitionDuration": "0.2s", "boxSizing": "border-box", "border": "none", "padding": "8px 4px"}}, "li:last-child": {"borderBottom": "1px solid #122344"}}}, "desktopStyle": {"padding": "0", "a, button": {"background": "transparent", "padding": "5px 0.8rem 0.5rem", "color": "#122344", "borderColor": "#122344", "borderWidth": "1px", "fontSize": "14px"}, "button": {}, "ul": {"position": "absolute", "borderWidth": "0 1px", "padding": "0", "li a": {"color": "#122344"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Find yours"}, "style": {"mobile": {}, "desktop": {}}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1127842#pageId=0&department=136&mlink=1038092,20183304,HP_C1"}, {"text": "Men", "href": "/browse/category.do?cid=1041765#pageId=0&department=75&mlink=1038092,20183304,HP_C1"}, {"text": "Girls", "href": "/browse/category.do?cid=1041699#pageId=0&department=48&mlink=1038092,20183304,HP_C1"}, {"text": "Boys", "href": "/browse/category.do?cid=1041808#pageId=0&department=16&mlink=1038092,20183304,HP_C1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041874#pageId=0&department=165&mlink=1038092,20183304,HP_C1"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1041941#pageId=0&department=165&mlink=1038092,20183304,HP_C1"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064172#pageId=0&department=165&mlink=1038092,20183304,HP_C1"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1064652#pageId=0&department=166&mlink=1038092,20183304,HP_C1"}]}}]}}}]}}}}, {"instanceName": "certona_hp2", "instanceDesc": "050420_HandpickedForYou", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "275px", "large": "260px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "column"}, "components": [{"instanceName": "certona-headlineText", "name": "TextHeadline", "type": "sitewide", "tileStyle": {"mobile": {"maxWidth": "640px", "width": "100%", "margin": "0 auto 1rem", "paddingLeft": "0.5rem", "boxSizing": "border-box"}, "desktop": {"maxWidth": "1425px", "width": "100%", "margin": "0 auto 1rem", "padding": "0 1rem"}}, "data": {"text": "Handpicked For You", "defaultHeight": "34px", "style": {"mobile": {"whiteSpace": "pre-line", "fontWeight": "500"}, "desktop": {"whiteSpace": "pre-line", "fontWeight": "500"}}, "className": {"mobile": "uppercase", "desktop": "uppercase"}}}, {"name": "Recommendations", "type": "home", "tileStyle": {"mobile": {"boxSizing": "border-box", "margin": "0 auto 2rem", "maxWidth": "640px", "paddingLeft": "0rem", "paddingRight": "0.5rem", "width": "100%"}, "desktop": {"boxSizing": "border-box", "margin": "0 auto 44px", "maxWidth": "1920px", "paddingLeft": "0.5rem", "paddingRight": "0.5rem", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome1_rr", "displayTitle": false, "certonaTitle": {"title": "", "style": {"mobile": {"color": "#122344", "display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "fontSize": "1.5rem", "fontWeight": "400", "marginBottom": "0.5rem", "paddingLeft": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}, "desktop": {"paddingLeft": "0.5rem", "color": "#122344", "display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "fontSize": "1.5rem", "fontWeight": "400", "marginBottom": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}}}, "layout": "carousel", "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "showMarketingFlag": false, "showPercentage": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0017/748/073/assets/certona_arrows/gp_certona_gfol-style-arrow3.png", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0017/748/073/assets/certona_arrows/gp_certona_gfol-style-arrow3.png", "nextArrowAlt": "Next", "productTextStyles": {"productTitle": {"style": {"color": "#666666", "textAlign": "left", "fontSize": ".8rem", "marginBottom": "-8px"}}, "productPrice": {"style": {"color": "#666666", "fontSize": ".8rem"}}, "productSalePrice": {"style": {"color": "#d00000", "fontSize": ".8rem", "display": "block", "marginTop": "2px"}}, "productPercentage": {"style": {"color": "#d00000", "fontSize": ".8rem", "marginLeft": "2px"}}, "size": {"width": "90%", "height": "150px"}}, "productCardStyles": {"style": {}}, "gridLayout": {}}}]}}}}, {"instanceName": "VDN", "instanceDesc": "VDN_020320", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "1258px", "large": "531px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "justifyContent": "space-between", "margin": "0px auto", "maxWidth": "1920px"}, "components": [{"instanceDesc": "category_1", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"marginBottom": "20px", "width": "49.25%", "flex": "unset"}, "desktop": {"marginBottom": "1rem", "maxWidth": "19.4%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"alignItems": "center", "flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {"width": "100%"}}, "background": {"image": {"alt": "Masks", "srcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_MASK.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_MASK.jpg"}, "linkData": {"to": "/browse/category.do?cid=1093513&departmentRedirect=true#department=136&mlink=1038092,20105399,HP_VCN_1_Masks"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "a, button": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontWeight": "400", "letterSpacing": "0", "padding": "20px 12px", "width": "100%", "fontSize": "1rem", "outline": "none"}, "button": {"paddingTop": "16px"}, "ul": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "a": {"boxSizing": "border-box", "padding": "8px 12px", "transitionDuration": "0.2s"}}}}, "desktopStyle": {}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1093513&departmentRedirect=true#department=136&mlink=1038092,20105399,HP_VCN_1_Masks"}, "composableButtonData": {"children": "Masks", "font": "primary", "style": {"backgroundColor": "#FFFFFF", "color": "#122344", "fontSize": "10px", "fontWeight": "700", "letterSpacing": "1px", "padding": "8px 8px 12px", "width": "100%"}, "desktopStyle": {}}}]}}}]}}}}, {"instanceDesc": "category_2", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"marginBottom": "20px", "width": "49.25%", "flex": "unset"}, "desktop": {"marginBottom": "1rem", "maxWidth": "19.4%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"alignItems": "center", "flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"background": {"image": {"alt": "Women", "srcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_WOM.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_WOM.jpg"}, "linkData": {"to": "/browse/category.do?cid=1042791&departmentRedirect=true#department=136&mlink=1038092,20105399,HP_VCN_2_W"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "a, button": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontWeight": "400", "letterSpacing": "0", "padding": "20px 12px", "width": "100%", "fontSize": "1rem", "outline": "none"}, "button": {"paddingTop": "16px"}, "ul": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "a": {"boxSizing": "border-box", "padding": "8px 12px", "transitionDuration": "0.2s"}}}}, "desktopStyle": {}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1042791&departmentRedirect=true#department=136&mlink=1038092,20105399,HP_VCN_2_W"}, "composableButtonData": {"children": "Women", "font": "primary", "style": {"backgroundColor": "#FFFFFF", "color": "#122344", "fontSize": "10px", "fontWeight": "700", "letterSpacing": "1px", "padding": "8px 8px 12px", "width": "100%"}, "desktopStyle": {}}}]}}}]}}}}, {"instanceDesc": "category_3", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"marginBottom": "20px", "width": "49.25%", "flex": "unset"}, "desktop": {"marginBottom": "1rem", "maxWidth": "19.4%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"alignItems": "center", "flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {"width": "100%"}}, "background": {"image": {"alt": "Men", "srcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_M.jpg?v=2", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_M.jpg?v=2"}, "linkData": {"to": "/browse/category.do?cid=1052118&departmentRedirect=true#department=75&mlink=1038092,20105399,HP_VCN_3_M"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "a, button": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontWeight": "400", "letterSpacing": "0", "padding": "20px 12px", "width": "100%", "fontSize": "1rem", "outline": "none"}, "button": {"paddingTop": "16px"}, "ul": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "a": {"boxSizing": "border-box", "padding": "8px 12px", "transitionDuration": "0.2s"}}}}, "desktopStyle": {}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1052118&departmentRedirect=true#department=75&mlink=1038092,20105399,HP_VCN_3_M"}, "composableButtonData": {"children": "Men", "font": "primary", "style": {"backgroundColor": "#FFFFFF", "color": "#122344", "fontSize": "10px", "fontWeight": "700", "letterSpacing": "1px", "padding": "8px 8px 12px", "width": "100%"}, "desktopStyle": {}}}]}}}]}}}}, {"instanceDesc": "category_4", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"marginBottom": "20px", "width": "49.25%", "flex": "unset"}, "desktop": {"marginBottom": "1rem", "maxWidth": "19.4%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"alignItems": "center", "flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"background": {"image": {"alt": "Girls", "srcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_KG.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_KG.jpg"}, "linkData": {"to": "/browse/category.do?cid=1064872&departmentRedirect=true#department=48&mlink=1038092,20105399,HP_VCN_4_G"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "a, button": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontWeight": "400", "letterSpacing": "0", "padding": "20px 12px", "width": "100%", "fontSize": "1rem", "outline": "none"}, "button": {"paddingTop": "16px"}, "ul": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "a": {"boxSizing": "border-box", "padding": "8px 12px", "transitionDuration": "0.2s"}}}}, "desktopStyle": {}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1064872&departmentRedirect=true#department=48&mlink=1038092,20105399,HP_VCN_4_G"}, "composableButtonData": {"children": "Girls", "font": "primary", "style": {"backgroundColor": "#FFFFFF", "color": "#122344", "fontSize": "10px", "fontWeight": "700", "letterSpacing": "1px", "padding": "8px 8px 12px", "width": "100%"}, "desktopStyle": {}}}]}}}]}}}}, {"instanceDesc": "category_5", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"marginBottom": "20px", "width": "49.25%", "flex": "unset"}, "desktop": {"marginBottom": "1rem", "maxWidth": "19.4%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"alignItems": "center", "flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {"width": "100%"}}, "background": {"image": {"alt": "Boys", "srcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_KB.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_KB.jpg"}, "linkData": {"to": "/browse/category.do?cid=1064875&departmentRedirect=true#department=16&mlink=1038092,20105399,HP_VCN_5_B"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "a, button": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontWeight": "400", "letterSpacing": "0", "padding": "20px 12px", "width": "100%", "fontSize": "1rem", "outline": "none"}, "button": {"paddingTop": "16px"}, "ul": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "a": {"boxSizing": "border-box", "padding": "8px 12px", "transitionDuration": "0.2s"}}}}, "desktopStyle": {}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1064875&departmentRedirect=true#department=16&mlink=1038092,20105399,HP_VCN_5_B"}, "composableButtonData": {"children": "Boys", "font": "primary", "style": {"backgroundColor": "#FFFFFF", "color": "#122344", "fontSize": "10px", "fontWeight": "700", "letterSpacing": "1px", "padding": "8px 8px 12px", "width": "100%"}, "desktopStyle": {}}}]}}}]}}}}, {"instanceDesc": "category_6", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"marginBottom": "20px", "width": "49.25%", "flex": "unset"}, "desktop": {"marginBottom": "1rem", "maxWidth": "19.4%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"alignItems": "center", "flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"background": {"image": {"alt": "<PERSON>ler Girl", "srcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_TG.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_TG.jpg"}, "linkData": {"to": "/browse/category.do?cid=1064881&departmentRedirect=true#department=165&mlink=1038092,20105399,HP_VCN_6_TG"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "a, button": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontWeight": "400", "letterSpacing": "0", "padding": "20px 12px", "width": "100%", "fontSize": "1rem", "outline": "none"}, "button": {"paddingTop": "16px"}, "ul": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "a": {"boxSizing": "border-box", "padding": "8px 12px", "transitionDuration": "0.2s"}}}}, "desktopStyle": {}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1064881&departmentRedirect=true#department=165&mlink=1038092,20105399,HP_VCN_6_TG"}, "composableButtonData": {"children": "<PERSON>ler Girl", "font": "primary", "style": {"backgroundColor": "#FFFFFF", "color": "#122344", "fontSize": "10px", "fontWeight": "700", "letterSpacing": "1px", "padding": "8px 8px 12px", "width": "100%"}, "desktopStyle": {}}}]}}}]}}}}, {"instanceDesc": "category_7", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"marginBottom": "20px", "width": "49.25%", "flex": "unset"}, "desktop": {"marginBottom": "1rem", "maxWidth": "19.4%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"alignItems": "center", "flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {"width": "100%"}}, "background": {"image": {"alt": "<PERSON><PERSON>", "srcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_TB.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_TB.jpg"}, "linkData": {"to": "/browse/category.do?cid=1064880&departmentRedirect=true#department=165&mlink=1038092,20105399,HP_VCN_7_TB"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "a, button": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontWeight": "400", "letterSpacing": "0", "padding": "20px 12px", "width": "100%", "fontSize": "1rem", "outline": "none"}, "button": {"paddingTop": "16px"}, "ul": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "a": {"boxSizing": "border-box", "padding": "8px 12px", "transitionDuration": "0.2s"}}}}, "desktopStyle": {}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1064880&departmentRedirect=true#department=165&mlink=1038092,20105399,HP_VCN_7_TB"}, "composableButtonData": {"children": "<PERSON><PERSON>", "font": "primary", "style": {"backgroundColor": "#FFFFFF", "color": "#122344", "fontSize": "10px", "fontWeight": "700", "letterSpacing": "1px", "padding": "8px 8px 12px", "width": "100%"}, "desktopStyle": {}}}]}}}]}}}}, {"instanceDesc": "category_8", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"marginBottom": "20px", "width": "49.25%", "flex": "unset"}, "desktop": {"marginBottom": "1rem", "maxWidth": "19.4%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"alignItems": "center", "flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"background": {"image": {"alt": "Baby Girl", "srcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_BG.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_BG.jpg"}, "linkData": {"to": "/browse/category.do?cid=1063532&departmentRedirect=true#department=165&mlink=1038092,20105399,HP_VCN_8_BG"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "a, button": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontWeight": "400", "letterSpacing": "0", "padding": "20px 12px", "width": "100%", "fontSize": "1rem", "outline": "none"}, "button": {"paddingTop": "16px"}, "ul": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "a": {"boxSizing": "border-box", "padding": "8px 12px", "transitionDuration": "0.2s"}}}}, "desktopStyle": {}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1063532&departmentRedirect=true#department=165&mlink=1038092,20105399,HP_VCN_8_BG"}, "composableButtonData": {"children": "Baby Girl", "font": "primary", "style": {"backgroundColor": "#FFFFFF", "color": "#122344", "fontSize": "10px", "fontWeight": "700", "letterSpacing": "1px", "padding": "8px 8px 12px", "width": "100%"}, "desktopStyle": {}}}]}}}]}}}}, {"instanceDesc": "category_9", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"marginBottom": "20px", "width": "49.25%", "flex": "unset"}, "desktop": {"marginBottom": "1rem", "maxWidth": "19.4%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"alignItems": "center", "flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"container": {"style": {"width": "100%"}}, "background": {"image": {"alt": "Baby Boy", "srcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_BB.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_BB.jpg"}, "linkData": {"to": "/browse/category.do?cid=1063552&departmentRedirect=true#department=166&mlink=1038092,20105399,HP_VCN_9_BB"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "a, button": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontWeight": "400", "letterSpacing": "0", "padding": "20px 12px", "width": "100%", "fontSize": "1rem", "outline": "none"}, "button": {"paddingTop": "16px"}, "ul": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "a": {"boxSizing": "border-box", "padding": "8px 12px", "transitionDuration": "0.2s"}}}}, "desktopStyle": {}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1063552&departmentRedirect=true#department=166&mlink=1038092,20105399,HP_VCN_9_BB"}, "composableButtonData": {"children": "Baby Boy", "font": "primary", "style": {"backgroundColor": "#FFFFFF", "color": "#122344", "fontSize": "10px", "fontWeight": "700", "letterSpacing": "1px", "padding": "8px 8px 12px", "width": "100%"}, "desktopStyle": {}}}]}}}]}}}}, {"instanceDesc": "category_10", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"marginBottom": "20px", "width": "49.25%", "flex": "unset"}, "desktop": {"marginBottom": "1rem", "maxWidth": "19.4%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"alignItems": "center", "flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {}, "desktop": {}}, "data": {"background": {"image": {"alt": "Logo", "srcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_LOGO.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0019/986/476/assets/VCN/020321_HP_VDN_LOGO.jpg"}, "linkData": {"to": "/browse/category.do?cid=1171570&scrollToCid=1171579&departmentRedirect=true#department=136&mlink=1038092,20105399,HP_VCN_10_Logo"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "a, button": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontWeight": "400", "letterSpacing": "0", "padding": "20px 12px", "width": "100%", "fontSize": "1rem", "outline": "none"}, "button": {"paddingTop": "16px"}, "ul": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#122344", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "a": {"boxSizing": "border-box", "padding": "8px 12px", "transitionDuration": "0.2s"}}, "li:last-child": {"borderTop": "1px solid #122344"}}}, "desktopStyle": {"ul": {"position": "absolute", "minWidth": "148px", "left": "50%", "transform": "translate(-50%, 0)"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Logo"}, "style": {"mobile": {}, "desktop": {}}, "submenu": [{"text": "women", "href": "/browse/category.do?cid=1171570&scrollToCid=1171579&departmentRedirect=true#department=136&mlink=1038092,20105399,HP_VCN_10_WLogo"}, {"text": "men", "href": "/browse/category.do?cid=1171570&scrollToCid=1171571&departmentRedirect=true#department=75&mlink=1038092,20105399,HP_VCN_10_MLogo"}, {"text": "girls", "href": "/browse/category.do?cid=1171570&scrollToCid=1171572&departmentRedirect=true#department=48&mlink=1038092,20105399,HP_VCN_10_GLogo"}, {"text": "boys", "href": "/browse/category.do?cid=1171570&scrollToCid=1171573&departmentRedirect=true#department=16&mlink=1038092,20105399,HP_VCN_10_BLogo"}, {"text": "toddler girl", "href": "/browse/category.do?cid=1171570&scrollToCid=1171576&departmentRedirect=true#department=165&mlink=1038092,20105399,HP_VCN_10_TGLogo"}, {"text": "toddler boy", "href": "/browse/category.do?cid=1171570&scrollToCid=1171578&departmentRedirect=true#department=165&mlink=1038092,20105399,HP_VCN_10_TBLogo"}, {"text": "baby girl", "href": "/browse/category.do?cid=1171570&scrollToCid=1171574&departmentRedirect=true#department=165&mlink=1038092,20105399,HP_VCN_10_BGLogo"}, {"text": "baby boy", "href": "/browse/category.do?cid=1171570&scrollToCid=1171575&departmentRedirect=true#department=166&mlink=1038092,20105399,HP_VCN_10_BBLogo"}]}}]}}}]}}}}]}}}}, {"instanceDesc": "HolServices", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "210px", "large": "121px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 auto 2rem", "maxWidth": "1000px"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto", "maxWidth": "640px", "width": "100%", "padding": "3.2%"}, "desktop": {"boxSizing": "border-box", "padding": "1.2%", "width": "50%"}}, "data": {"overlay": {"alt": "Gap Good Rewards.", "srcUrl": "/Asset_Archive/GPWeb/content/0019/835/559/assets/newsFeed/GGR_1.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0019/835/559/assets/newsFeed/GGR_1.svg", "style": {}, "desktopStyle": {}}, "background": {"image": {"alt": "A really good way to shop and earn rewards. Sign up.", "srcUrl": "/Asset_Archive/GPWeb/content/0019/835/559/assets/newsFeed/GGR.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0019/835/559/assets/newsFeed/GGR.jpg"}, "linkData": {"to": "/customerService/info.do?cid=1099133&mlink=1038092,19494113,HP_Sub_GoodRewards"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto", "maxWidth": "640px", "width": "100%", "padding": "3.2%"}, "desktop": {"boxSizing": "border-box", "padding": "1.2%", "width": "50%"}}, "data": {"background": {"image": {"alt": "afterpay. shop now, pay later. It's that easy. Learn more.", "srcUrl": "/Asset_Archive/GPWeb/content/0019/943/611/assets/G31797_Afterpay_NEWS_SECTION.svg?v=2", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0019/943/611/assets/G31797_Afterpay_NEWS_SECTION.svg?v=2"}, "linkData": {"to": "/customerService/info.do?cid=1037495&cs=payment_options&cs=payment_options&accordianId=topaywithafterpayBanner&mlink=1038092,19625188,HP_Sub_AfterPay"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto", "maxWidth": "640px", "width": "100%", "padding": "3.2%"}, "desktop": {"boxSizing": "border-box", "padding": "1.2%", "width": "50%"}}, "data": {"overlay": {"alt": "Gap for Good", "srcUrl": "/Asset_Archive/GPWeb/content/0019/819/951/assets/newsFeed/G31738_AllDiv_G4GEvergreen_HPNews.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0019/819/951/assets/newsFeed/G31738_AllDiv_G4GEvergreen_HPNews.svg", "style": {}, "desktopStyle": {}}, "background": {"image": {"alt": "Gap for good. Because we do more than just sell clothes. See more on sustainability.", "srcUrl": "/Asset_Archive/GFWeb/content/0019/625/188/assets/holiday_hacks/G31738_AllDiv_G4GEvergreen_HPNews.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0019/625/188/assets/holiday_hacks/G31738_AllDiv_G4GEvergreen_HPNews.jpg"}, "linkData": {"to": "/browse/info.do?cid=1151006&mlink=1038092,19625188,HP_Sub_GapforGood"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto", "maxWidth": "640px", "width": "100%", "padding": "3.2%"}, "desktop": {"boxSizing": "border-box", "padding": "1.2%", "width": "50%"}}, "data": {"background": {"image": {"alt": "Our commitment to change. We are proud to join The 15 percent pledge. More info this way.", "srcUrl": "/Asset_Archive/GFWeb/content/0020/051/568/assets/G33223_AllDiv_15percent_HPNews.svg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0020/051/568/assets/G33223_AllDiv_15percent_HPNews.svg"}, "linkData": {"to": "https://www.gap.com/browse/info.do?cid=1160596&mlink=1038092,20051568,HP_Sub_15PercentPledge"}}}}]}}}}, {"instanceName": "dpg-banner2", "instanceDesc": "DPG-placeholder-2", "name": "SVGOverlay", "type": "sitewide", "experimentRunning": true, "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"width": 0, "height": 0}, "mobile": {"width": 0, "height": 0}}, "containerStyle": {"mobile": {"display": "none"}, "desktop": {"display": "none"}}, "background": {"content": {"smallImg": "", "largeImg": "", "altText": ""}}, "svgoverlay": {"smallImg": "", "largeImg": "", "altText": "Placeholder", "link": {"url": ""}}, "linksContainerStyle": {}, "links": {"buttonClasses": "", "style": {}, "content": [{}]}}}]}, "meta.title.override": "Everyday Deals On Clothes For Women, Men, Baby And Kids", "type": "meta", "brand": "gapfs", "meta.description": "Get great prices on great style when you shop Gap Factory clothes for women, men, baby and kids. Gap Factor"}