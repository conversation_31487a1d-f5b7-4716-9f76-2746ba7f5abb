{"sitewide": {"headline": {"sitewide-headline-ciid": "28852955", "name": "LayeredContentModule", "type": "sitewide", "instanceName": "global-banner-040722-loyalty", "experimentRunning": true, "useGreyLoadingEffect": false, "data": {"placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"backgroundColor": "#FFFFFF", "width": 0, "height": 75, "margin": "0 auto"}, "mobile": {"backgroundColor": "#FFFFFF", "width": 0, "height": 50, "margin": "0 auto"}}, "defaultHeight": {"large": "75px", "small": "50px"}, "isVisible": {"large": true, "small": true}, "excludePageTypes": ["ShoppingBag", "CustomerService", "store-service", "profile"], "lazy": false, "container": {"className": "", "style": {"margin": "0 auto", "display": "block", "background": "#e6f1f4"}, "desktopStyle": {"margin": "0 auto", "display": "block", "background": "#e6f1f4"}}, "background": {"linkData": {"to": "https://oldnavy.gap.com/stores?mlink=55294,1,GlobalBanner_CurbsidePlus", "target": "_blank", "title": "Get it today with our fast & worry-free pickup options. Find your store."}, "image": {"alt": "Get it today with our fast & worry-free pickup options. Find your store.", "srcUrl": "/Asset_Archive/ONWeb/content/0028/852/955/assets/210614_NS-N1753_Convenience2021_GlobalBanner_US_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/852/955/assets/210614_NS-N1753_Convenience2021_GlobalBanner_US_XL.svg", "style": {"display": "block", "box-sizing": "border-box", "max-height": "60px"}}, "style": {"width": "100%", "display": "block", "padding": "0", "margin": "0 auto"}, "desktopStyle": {"display": "block", "maxWidth": "1440px"}}}}, "animatedheadline": {"sitewide-animatedheadline-ciid": "27316064", "name": "AnimatedHeadline", "type": "sitewide", "useGreyLoadingEffect": false, "instanceName": "animatedheadline-1223", "experimentRunning": true, "placementConfiguration": {"home": {"include": ["headline", "countdown"]}, "division": {"include": ["headline", "countdown"]}, "category": {"include": ["headline", "countdown"]}, "product": {"include": ["headline", "countdown"]}, "Information": {"include": ["headline", "countdown"]}, "profile": {"include": ["headline", "countdown"]}, "store-service": {"include": ["headline", "countdown"]}, "ShoppingBag": {"include": ["secondary-headline", "<PERSON><PERSON><PERSON><PERSON>"], "exclude": ["animatedheadline"]}}, "data": {"defaultHeight": {"large": "0px", "small": "30px"}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"backgroundColor": "transparent", "width": 0, "height": 0, "margin": "0 auto"}, "mobile": {"backgroundColor": "transparent", "width": 0, "height": "30px", "margin": "0 auto"}}, "options": {"isDesktopVisible": false, "isMobileVisible": true}, "duration": 4000, "transitionSpeed": 500, "useContainerFullHeight": true, "style": {"backgroundColor": "#E8E8E8"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "free-shipping-slide", "style": {"fontWeight": "bold", "color": "#003764", "backgroundColor": "#E8E8E8", "width": "100%", "height": "100%", "display": "flex", "alignItems": "center", "justifyContent": "center", "fontSize": "2.5vw", "paddingTop": "1px", "font-family": "'ON Sans Text','Open Sans','Gap Sans',Helvetica,Arial,Roboto,sans-serif;", "textAlign": "center", "margin": "auto", "paddingLeft": "4px", "whiteSpace": "nowrap"}}, "components": ["FREE SHIPPING", {"type": "builtin", "name": "span", "data": {"props": {"style": {"textTransform": "uppercase", "color": "#003764", "textDecoration": "none", "paddingLeft": "3px", "fontWeight": "normal", "fontSize": "2.5vw"}}, "components": ["on $50+ for rewards members"]}}, "", {"type": "builtin", "name": "a", "data": {"props": {"style": {"color": "#003764", "textDecoration": "underline", "paddingLeft": "5px", "fontWeight": "normal", "fontSize": "2.5vw"}, "href": "/my-account/sign-in"}, "components": ["SIGN IN or JOIN"]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"color": "#003764", "textDecoration": "underline", "paddingLeft": "5px", "fontWeight": "normal", "fontSize": "2.5vw"}}, "components": [{"name": "ComposableButton", "type": "sitewide", "data": {"modalProps": {"src": "/Asset_Archive/ONWeb/content/static-marketing/xbrand-edfs-content/shippingLegal-ON-US-072021.html?v=5", "height": "500px", "width": "100%", "closeButtonAriaLabel": "Close Modal", "title": "Shipping & Returns"}, "style": {"color": "#003764", "backgroundColor": "transparent", "border": "0px", "textTransform": "lowercase", "textDecoration": "underline", "fontWeight": "normal", "fontSize": "2.1vw", "marginLeft": "-5px", "letterSpacing": "0"}, "buttonText": "details"}}]}}]}}, {"name": "div", "type": "builtin", "data": {"props": {"style": {"fontWeight": "bold", "color": "#003764", "backgroundColor": "#E8E8E8", "width": "100%", "height": "100%", "display": "flex", "alignItems": "center", "justifyContent": "center", "fontSize": "2.5vw", "paddingTop": "1px", "font-family": "'ON Sans Text','Open Sans','Gap Sans',Helvetica,Arial,Roboto,sans-serif;"}}, "components": ["", {"type": "builtin", "name": "span", "data": {"props": {"style": {"color": "#003764", "textDecoration": "none", "fontWeight": "normal", "fontSize": "2.5vw"}}, "components": ["PLUS, FREE RETURNS & EXCHANGES!"]}}, "", {"type": "builtin", "name": "a", "data": {"props": {"href": "/browse/category.do?cid=10018&mlink=5151,GlobalBanner_CurbsidePlus", "target": "_blank", "style": {"color": "#003764", "textDecoration": "underline", "paddingLeft": "5px", "fontWeight": "normal", "fontSize": "2.1vw"}}, "components": ["shop now"]}}]}}]}}, "secondary-headline": {"sitewide-secondary-headline-ciid": "28995578", "type": "sitewide", "name": "FlexHeadline", "experimentRunning": true, "instanceName": "react-flexHeadline-041822", "data": {"shouldWaitForOptimizely": false, "includePageTypes": ["ShoppingBag"], "lazy": false, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"backgroundColor": "transparent", "width": "0px", "height": "0px", "margin": "0px auto"}, "mobile": {"width": "0px", "height": "0px"}}, "defaultHeight": {"large": "0px", "small": "0px"}, "styles": {"root": {"base": {"backgroundColor": "#eeeeee", "justifyContent": "center", "textAlign": "center"}, "desktop": {"padding": "15px 5%"}, "mobile": {"backgroundColor": "#FFFFFF", "padding": ".5em 1%", "flexDirection": "row", "flexWrap": "wrap", "width": "100%", "maxWidth": "414px", "margin": "auto", "line-height": "1em"}}, "flexItemElement": {"base": {"color": "#003764", "fontFamily": "<PERSON><PERSON>oth<PERSON>, Open Sans, Gap Sans, Helvetica, Arial, Roboto, sans-serif", "paddingRight": "5px"}, "mobile": {"fontSize": "0.75em"}, "desktop": {"fontSize": "14px"}}}, "flexItems": [{"elements": [{"text": "Online Exclusive"}, {"text": "40% OFF YOUR ORDER", "style": {"base": {"fontWeight": "bold"}}}]}, {"elements": [{"text": "discount applied at checkout"}, {"text": "ENDS 4/19"}, {"text": "details", "modalProps": {"src": "/buy/promo_legal_details.do?promoId=876447", "closeButtonAriaLabel": "close modal", "height": "400px"}, "style": {"base": {"textDecoration": "underline"}}}]}]}}, "promodrawer": {"name": "PromoDrawerComponentV2", "type": "sitewide", "sitewide-promodrawer-ciid": "********", "instanceName": "promoDrawer-********", "experimentRunning": false, "data": {"shouldWaitForOptimizely": false, "buildInfo": ["********", "ON"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "search", "info", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "profile", "Profile", "LoyaltyValueCenter"]}, "disabledAutoFirePageTypes": ["category", "product", "Profile"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two-cta {\n  background-color: #fff; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_two-cta img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two-cta .pd_two-cta--cta-container {\n  bottom: 14%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_two-cta .pd_two-cta_button {\n  background-color: #000;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_two-cta .pd_two-cta_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n\n<div class=\"pd_two-cta\">\n  <a href=\"/browse/category.do?cid=5508&mlink=5151,1,PD_1&clink=1\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/ONWeb/content/0028/968/514/assets/220408_00H-M7895_OLX50OffActivewear-OnlineExt_TOD_Site_WM_PD_US.png\" alt=\"Today only! Online exclusive, 4/18. Now extended one more day - online only! 50% off Old Navy Active Activewear for the fam. Select styles only. Excludes Maternity, Everyday Magic, Licensed Products, Face Masks, and Baby. While supplies last.\">\n  </a>\n  <div class=\"pd_two-cta--cta-container\">\n    <a href=\"/browse/category.do?cid=5508&mlink=5151,1,PD_1_a&clink=1\" class=\"pd_two-cta_button\">WOMEN</a>\n    <a href=\"/browse/category.do?cid=1031103&mlink=5151,1,PD_1_b&clink=1\" class=\"pd_two-cta_button\">MEN</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "50% Off Activewear for Adult & Kids: Offer valid 4/18/22 at Old Navy online only. Offer not valid at Old Navy stores in the United States (including Puerto Rico). Excludes Everyday Magic, Licensed products, Face Masks, <PERSON><PERSON>ity, Toddler, and Baby 0-24 months. Select styles only. While supplies last. No adjustments on previous purchases. Cannot be combined with other offers or discounts, including Gap Inc. employee discount.", "genericCodeId": "", "genericCode": ""}, "promoId": "l1mo833f"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two-cta {\n  background-color: #fff; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_two-cta img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two-cta .pd_two-cta--cta-container {\n  bottom: 14%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_two-cta .pd_two-cta_button {\n  background-color: #000;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_two-cta .pd_two-cta_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n\n<div class=\"pd_two-cta\">\n  <a href=\"/browse/category.do?cid=1013969&mlink=5151,1,PD_2&clink=1\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/ONWeb/content/0028/968/514/assets/220408_00H-M7896_OLX50OffActivewear-OnlineExt_TOD_Site_GB_PD_US.png\" alt=\"Today only! Online exclusive, 4/18. Now extended one more day - online only! 50% off Old Navy Active Activewear for the fam. Select styles only. Excludes Everyday Magic, Licensed Products, Face Masks, and Baby. While supplies last.\">\n  </a>\n  <div class=\"pd_two-cta--cta-container\">\n    <a href=\"/browse/category.do?cid=1013969&mlink=5151,1,PD_2_a&clink=1\" class=\"pd_two-cta_button\">GIRLS</a>\n    <a href=\"/browse/category.do?cid=1013976&mlink=5151,1,PD_2_b&clink=1\" class=\"pd_two-cta_button\">BOYS</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "50% Off Activewear for Adult & Kids: Offer valid 4/18/22 at Old Navy online only. Offer not valid at Old Navy stores in the United States (including Puerto Rico). Excludes Everyday Magic, Licensed products, Face Masks, <PERSON><PERSON>ity, Toddler, and Baby 0-24 months. Select styles only. While supplies last. No adjustments on previous purchases. Cannot be combined with other offers or discounts, including Gap Inc. employee discount.", "genericCodeId": "", "genericCode": ""}, "promoId": "l1mo833f"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #fff; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<!-- <a href=\"\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\"> -->\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/ONWeb/content/0028/968/514/assets/220418_89A_M7866_OLX40OffPurchase_PROMO_Site_PromoDrawer_USCA.png\" alt=\"Online exclusive, ends 4/19. 40% off your purchase.\">\n  </div>\n<!-- </a> -->\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "discount automatically applied at checkout", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "876447", "genericCode": ""}, "promoId": "l1mnv97v"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two-cta {\n  background-color: #fff; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_two-cta img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two-cta .pd_two-cta--cta-container {\n  bottom: 14%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_two-cta .pd_two-cta_button {\n  background-color: #000;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_two-cta .pd_two-cta_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n\n<div class=\"pd_two-cta\">\n  <a href=\"/browse/category.do?cid=1152083&mlink=5151,1,PD_4&clink=1\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/ONWeb/content/0028/968/514/assets/210903_98-M4602_EDMKIDSpromodrawer_CP_Site_PromoDrawer_1US.png\" alt=\"Everyday magic. Extraordinary prices every single day. Kids styles from $5.\">\n  </a>\n  <div class=\"pd_two-cta--cta-container\" style=\"bottom: 4%\">\n    <a href=\"/browse/category.do?cid=1152083&mlink=5151,1,PD_4_a&clink=1\" class=\"pd_two-cta_button\">Girls</a>\n    <a href=\"/browse/category.do?cid=1152085&mlink=5151,1,PD_4_b&clink=1\" class=\"pd_two-cta_button\">Boys</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile4"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "", "legalOverride": "", "genericCodeId": "", "genericCode": ""}, "promoId": "l1mo2wi2"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #fff; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"https://secure-oldnavy.gap.com/profile/info.do?cid=1159462&mlink=5151,1,PD_EmailAcq&clink=1\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" target=\"_blank\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/ONWeb/content/0028/968/514/assets/200321_20A_USCDAEmailAcq_M4587_US_PD_CTA.png\" alt=\"Join our email fam to get 20% off your next purchase in-store and online.  Join now.\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile5"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "570893", "genericCode": ""}, "promoId": "l1mnjojv"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "text": "{--! headerText !--}", "style": {"mobile": {"fontSize": "0.75em"}}}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}"}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "subHeaderText": "(5 available)", "iconAltText": "Open icon"}, "closedState": {"headerText": "today only! 50% off all activewear", "subHeaderText": "tap for more ways to save", "iconAltText": "Closed icon"}, "aria-label": "today only! 50% off all activewear"}, "pd_id": "pdid_l1qr11wp"}}, "footer": {"sitewide-footer-ciid": "28019451", "type": "sitewide", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instanceName": "evergreen-footer", "experimentRunning": false, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"lazy": false, "defaultHeight": "0px", "html": "<style> .checkout-container {display: flex; flex-direction: row; max-width: 1400px; align-items: center; margin: 2em auto} .width100atXL {width: 100%} .width50atXL {width: 50%} .four-brands {text-align: right} @media screen and (min-width: 768px) { .footerCol .body-a, .footerCol .body-a span, .footer-legal__wrapper .footer_copyright-row { font-size: 0.8rem !important; } .pl50 { padding-left: 50%}} @media screen and (min-width: 768px) and (max-width: 1250px) { .pl20 {padding-left: 20%} .brand-logos-wrapper img { padding-left: 20px; width: 85%}} @media screen and (max-width: 767px) { .brand-logos-wrapper {text-align: center; margin-top: 1em} .brand-logos {margin-top: 1em; width: 70%; margin: auto} .four-brands {text-align: center} .checkout-container {flex-direction: column} .width100atSM {width: 100%} .width50atSM {width: 50%}}</style><div class='checkout-container' style='position: relative; display: flex;'><div class='width50atXL width100atSM four-brands' style='position: relative;'><div class='pl50 pl20'><div 'class='width100atXL width100atSM' style='text-align: center; font-size: 1.25em; font-weight: bold; margin-bottom: 10px'>4 BRANDS 1 EASY CHECKOUT</div><div 'class='width100atXL width100atSM' style='text-align: center; font-weight: bold; font-size: .95em;'>REWARDS MEMBERS get FREE SHIPPING on</div><div 'class='width100atXL width100atSM' style='text-align: center; font-weight: bold; font-size: .95em;'>all orders $50+ <span><a style='text-decoration: underline; font-weight: bold; font-size: .95em; z-index: 2; position: relative' target='blank' href='https://secure-oldnavy.gap.com/my-account/sign-in?mlink=5151,1,FTR_BannerSignIn'>Sign in</a></span> or <span><a style='text-decoration: underline; font-weight: bold; font-size: .95em; z-index: 2; position: relative' target='blank' href='https://secure-oldnavy.gap.com/my-account/sign-in?mlink=5151,1,FTR_BannerJoin&targetURL=/browse/home.do'>Join</a></span> <span><a style='text-decoration: underline; color: gray; font-size: .75em; padding-left: 3px'>DETAILS</a></span></div></div></div><div class='width50atXL width100atSM brand-logos-wrapper' style='position: relative; max-width: 400px;'><img class='brand-logos' src='/Asset_Archive/ONWeb/content/static-marketing/footer-assets/EDFS_Footer.svg' /></div></div>"}}, {"name": "LayeredContentModule", "type": "sitewide", "instanceName": "brand-logos-popup", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"position": "relative"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"position": "absolute", "top": 0, "height": "100%", "width": "100%", "display": "flex", "justifyContent": "center", "alignItems": "center"}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "SHIPPING & RETURNS", "src": "/Asset_Archive/ONWeb/content/static-marketing/xbrand-edfs-content/shippingLegal-ON-US-072021.html?v=5", "height": "500px"}}, "composableButtonData": {"children": "details", "style": {"color": "transparent", "backgroundColor": "transparent", "position": "absolute", "margin-top": "-26%", "width": "100%"}, "desktopStyle": {"margin-top": "-10%", "bottom": "auto", "right": "0", "height": "50px", "width": "50%"}}}]}}}, {"name": "LayeredContentModule", "type": "sitewide", "instanceName": "4brands-details-popup", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"position": "relative", "zIndex": "1"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"position": "absolute", "top": 0, "height": "100%", "width": "50%", "display": "flex", "justifyContent": "center", "alignItems": "center", "zIndex": "1"}, "className": "details-link", "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "SHIPPING & RETURNS", "src": "/Asset_Archive/ONWeb/content/static-marketing/xbrand-edfs-content/shippingLegal-ON-US-072021.html?v=5", "height": "500px"}}, "composableButtonData": {"children": "DETAILS", "style": {"fontSize": ".75em", "color": "transparent", "backgroundColor": "transparent", "position": "absolute", "margin-top": "-45%", "right": "15%", "width": "50px"}, "desktopStyle": {"margin-top": "-83px", "left": "auto", "height": "50px", "right": "0", "width": "100%", "zIndex": "1"}}}]}}}, {"name": "Footer", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "287px", "showSMS": true, "smsLayout": {"headerSection": {"desktopAndMobile": {"shouldDisplay": true, "data": {"placeholderSettings": {"desktop": {"width": "100%"}}, "style": {"flexDirection": "column", "maxWidth": "600px", "width": "100%", "textAlign": "center", "position": "relative", "padding": "0px 0px 1em 0px", "margin": "0 auto"}, "components": [{"name": "HomeSVGOverlay", "type": "home", "data": {"disableAlternativeImage": true, "isVisible": {"large": true, "small": true}, "containerStyle": {"mobile": {"padding": "0 0", "marginTop": "5%"}}, "background": {"content": {"smallImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/Number/190520_037E_US_SMSAcq_Footer_Mobile_Number.svg", "largeImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/Number/190520_037F_US_SMSAcq_Footer_Desk_Number.svg", "altText": "Great deals let's text!"}}, "svgoverlay": {"smallImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/Number/190520_037E_US_SMSAcq_Footer_Mobile_Number.svg", "largeImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/Number/190520_037F_US_SMSAcq_Footer_Desk_Number.svg", "altText": "Great deals let's text!"}}}]}}}, "legalSection": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "maxWidth": "600px", "width": "88%", "textAlign": "center", "position": "relative", "padding": "0 0 0 0", "margin": "0 auto", "lineHeight": "1.2", "fontSize": "55%", "letterSpacing": "1px"}, "components": [{"name": "TextHeadline", "type": "sitewide", "data": {"text": "Msg & Data Rates May Apply. By entering your phone number, clicking submit, and completing the sign-up instructions, you consent to receive one or more recurring marketing text messages each week at the mobile number provided that may be sent via an automated system, and you also consent to the "}}, {"name": "LinkWithModal", "type": "sitewide", "data": {"type": "link", "children": "text terms & privacy policy.", "to": "https://cs.waterfall.com/gap/terms/", "style": {"textDecoration": "underline", "color": "#003764"}}}, {"name": "TextHeadline", "type": "sitewide", "data": {"text": "Consent is not a condition of purchasing goods or services. You can opt-out at any time by responding STOP. You can also respond HELP for help."}}]}}}}, "emailRegistration": {"disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "oldnavy", "locale": "en_US", "data": {"style": {}, "html": ""}}}, "carousel": {"prevArrow": "", "nextArrow": "", "slides": [{"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0 2rem", "margin": "0px auto 5% auto", "width": "75%"}, "desktop": {"padding": "0 0", "backgroundColor": "#FFFFFF", "marginTop": "5%"}}, "background": {"content": {"smallImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/211025_NS-N2517_ImagineMission_HPFooter_US_SM.svg", "largeImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/211025_NS-N2517_ImagineMission_HPFooter_US_XL.svg", "altText": "Creating a better future for future generations. Learn more."}, "style": {"desktop": {}, "mobile": {"height": "100%", "width": "90%", "margin": "0 auto"}}}, "svgoverlay": {"smallImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/211025_NS-N2517_ImagineMission_HPFooter_US_SM.svg", "largeImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/211025_NS-N2517_ImagineMission_HPFooter_US_XL.svg", "altText": "Creating a better future for future generations. Learn more.", "link": {"url": "/browse/info.do?cid=1160383&mlink=5151,1,footer_imaginemission", "tid": "footer_imaginemission"}}}}, {"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0 2rem", "margin": "0px auto 5% auto", "width": "75%"}, "desktop": {"padding": "0 0", "backgroundColor": "#FFFFFF", "marginTop": "5%"}}, "background": {"content": {"smallImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/WE_HP_FootCarousel_US_240x277.svg", "largeImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/WE_HP_FootCarousel_US_240x277.svg", "altText": "We are change"}, "style": {"desktop": {}, "mobile": {"height": "100%", "width": "90%", "margin": "0 auto"}}}, "svgoverlay": {"smallImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/WE_HP_FootCarousel_US_240x277.svg", "largeImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/WE_HP_FootCarousel_US_240x277.svg", "altText": "We are change", "link": {"url": "/customerService/info.do?cid=1160233&mlink=5151,1,footer_wearechange", "tid": "footer_wearechange"}}}}, {"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0", "backgroundColor": "#FFFFFF", "height": "0", "display": "none"}, "desktop": {"padding": "0 0", "backgroundColor": "#FFFFFF", "marginTop": "2%"}}, "background": {"content": {"smallImg": "/assets/common/clear.gif", "largeImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/081717_US_StoreLocator_hp_carousel_xl_image.jpg?v=2", "altText": "Store Locator"}}, "svgoverlay": {"smallImg": "/assets/common/clear.gif", "largeImg": "/Asset_Archive/ONWeb/content/0028/019/451/assets/081717_US_StoreLocator_hp_carousel_xl_image.jpg?v=2", "altText": "Store Locator", "link": {"url": "/customerService/storeLocator.do?mlink=5151,1,footer_storelocator", "tid": "footer_storelocator"}}}}]}, "socialLinks": [{"to": "https://www.facebook.com/Oldnavy", "text": "Follow on Facebook", "className": "social__facebook", "style": {"position": "absolute"}}, {"to": "https://www.twitter.com/oldnavy", "text": "Follow on Twitter", "className": "social__twitter", "style": {"position": "absolute"}}, {"to": "https://www.pinterest.com/oldnavy/", "text": "Follow on Pinterest", "className": "social__pinterest", "style": {"position": "absolute"}}, {"to": "https://www.instagram.com/oldnavy/", "text": "Follow on Instagram", "className": "social__instagram", "style": {"position": "absolute"}}, {"to": "https://oldnavy.tumblr.com/", "text": "Follow on Tumblr", "className": "social__tumblr", "style": {"position": "absolute"}}, {"to": "https://snapchat.com/add/oldnavy", "text": "Follow on Snap<PERSON><PERSON>", "className": "social__snapchat", "style": {"position": "absolute"}}], "copyRights": {"rows": [[{"text": "© 2022 Old Navy, LLC"}, {"to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy", "text": "Privacy Policy"}, {"text": "Do Not Sell My Info", "doNotSell": true}, {"to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy#Interest-Based-Ad", "text": "Interest Based Ads"}, {"to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy#Your-California-Privacy-Rights", "text": "Your California Privacy Rights"}, {"to": "https://www.gapinc.com/content/gapinc/html/sustainability/ca-transparency-insupplychainsact.html", "text": "California Transparency in Supply Chains Act"}, {"to": "/customerService/info.do?cid=3319&mlink=5151,17385942,Footer_Terms_of_Use&clink=17385942", "text": "Terms of Use"}, {"to": "https://jobs.gapinc.com/old-navy-home", "text": "Careers"}, {"to": "/browse/info.do?cid=1130006", "text": "Sustainability"}, {"to": "/customerService/info.do?cid=1095422", "text": "Navy<PERSON> <PERSON><PERSON><PERSON>"}, {"to": "https://www.gapinc.com/content/gapinc/html/aboutus.html", "text": " About Gap Inc.", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}], [{"to": "/customerService/info.do?cid=1005695&mlink=5151,17385942,Footer_Disabilities_Act&clink=17385942", "text": "Americans with Disabilities Act", "style": {"font-weight": "700"}}, {"to": "https://www.gapinc.com/en-us/values/sustainability", "text": "Gap Inc. Policies"}]]}}}]}, "promorover": {"sitewide-promorover-ciid": "22008266", "name": "MktSticker", "type": "sitewide", "experimentRunning": false, "instanceName": "promo_sticker_07-17", "data": {"shouldWaitForOptimizely": false, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": 0, "width": 0}, "mobile": {"height": 0, "width": 0}}, "href": "/browse/info.do?cid=1156830&mlink=5151,1,<PERSON>er_CurbsidePlus", "hrefTarget": "_blank", "largeImg": "/Asset_Archive/ONWeb/content/0022/008/266/assets/210614_NS-N1755_Convenience2021_Sticker_US_close.svg", "altText": "Contactless curbside pickup. Quick & easy in-store pickup.", "isVisible": {"small": false, "large": true}, "modalCloseButtonAriaLabel": true, "localStorageKey": "wcd_onRoverStorage_071721", "localStorageVal": "ONroverHasBeenClosed_071721", "options": {"excludePageTypes": ["home", "division", "product", "ShoppingBag", "Information", "info", "CustomerService", "store-service", "profile", "dynamicerror", "account_summary", "address_book", "checkout", "credit_card_summary", "CustomerService", "customer_value", "express_account_settings", "dynamicerror", "staticerror", "order_detail", "order_history", "product", "rewards", "search", "ShoppingBag", "sign_in", "size<PERSON>hart", "storeLocator", "store-service", "update_personal_info", "Profile", "loyalty_value_center", "LoyaltyValueCenter"]}}}, "logo": {"sitewide-logo-ciid": "********", "type": "sitewide", "name": "Logo", "logoImgPath": "data:image/svg+xml;charset%3DUS-ASCII,%3Csvg%20id%3D%22mobile%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%221.203in%22%20height%3D%2237.536%22%20viewBox%3D%220%200%2086.649%2028.141%22%3E%20%20%20%20%3Cdefs%3E%20%20%20%20%20%20%20%20%3Cstyle%3E%20%20%20%20%20%20%20%20%20%20%20%20.cls-1%20%7B%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%20%23fff%3B%20%20%20%20%20%20%20%20%20%20%20%20%7D%20%20%20%20%20%20%20%20%20%20%20%20.cls-2%20%7B%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%20%********%3B%20%20%20%20%20%20%20%20%20%20%20%20%7D%20%20%20%20%20%20%20%20%20%20%20%20.cls-3%20%7B%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%20%23003764%3B%20%20%20%20%20%20%20%20%20%20%20%20%7D%20%20%20%20%20%20%20%20%3C%2Fstyle%3E%20%20%20%20%3C%2Fdefs%3E%20%20%20%20%3Ctitle%3E122616_HolidayLights_Off_mobile_masthead%3C%2Ftitle%3E%3Cellipse%20class%3D%22cls-1%22%20cx%3D%2243.324%22%20cy%3D%2214.07%22%20rx%3D%2243.325%22%20ry%3D%2214.071%22%2F%3E%3Cpath%20%20%20%20%20%20%20%20class%3D%22cls-2%22%20%20%20%20%20%20%20%20d%3D%22M52.831%2015.355h2.288l-1.156-4.3-1.132%204.3zM31.256%2010.962h-.581v6.252h.574a1.753%201.753%200%200%200%201.711-.773%205.234%205.234%200%200%200%200-4.72%201.738%201.738%200%200%200-1.704-.759zM15.211%2010.764c-1.616%200-1.9%202.242-1.9%203.357s.493%203.222%201.9%203.223%201.905-2.12%201.906-3.222-.29-3.356-1.906-3.358z%22%2F%3E%3Cpath%20%20%20%20%20%20%20%20class%3D%22cls-3%22%20%20%20%20%20%20%20%20d%3D%22M43.323%200C19.397%200%200%206.3%200%2014.069s19.4%2014.072%2043.323%2014.072%2043.326-6.3%2043.326-14.072S67.247%200%2043.323%200zM15.205%2019.334c-2.677%200-4.35-2.212-4.356-5.214v-.023c0-3.164%201.664-5.29%204.364-5.289s4.359%202.132%204.357%205.3c-.007%203.006-1.681%205.226-4.366%205.226zm11.7-.134h-6.04V8.986h2.336v8.227h3.705zm9.13-5.115v.066a5.923%205.923%200%200%201-1.291%203.925c-.956%201.1-1.98%201.125-3.262%201.124h-3.267V8.986h3.274c1.282%200%202.306.005%203.261%201.1a5.938%205.938%200%200%201%201.286%203.947zM48.121%2019.2h-2.346l-3.248-5.844v5.842h-2.352V8.986h2.35l3.256%205.921V8.986h2.344zm8.029%200l-.534-1.987h-3.278l-.523%201.987H49.43l2.745-10.214h3.566L58.49%2019.2zm7.525%200h-3.111L57.941%208.986h2.592l1.589%207.122%201.6-7.122h2.589zm9.066-3.257V19.2h-2.607v-3.258l-3.051-6.956h2.573l1.783%204.465%201.79-4.465h2.572z%22%2F%3E%3C%2Fsvg%3E"}, "sitewide-emergencybanner-ciid": "28003515", "mobileemergencybanner": {"name": "OptimizelyPlaceholder", "type": "sitewide", "instanceName": "dpg_emergency_banner_mob", "experimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, "desktopemergencybanner": {"name": "OptimizelyPlaceholder", "type": "sitewide", "instanceName": "dpg_emergency_banner_desk", "experimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, "hamnavRedesignBanner": {"sitewide-hamnavRedesignBanner-ciid": "28852148", "type": "builtin", "name": "div", "data": {"props": {"class": "hamNavMaskShop", "style": {}}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "instanceName": "bottom-banner", "data": {"excludePageTypes": [], "lazy": false, "container": {"className": "", "style": {"display": "block"}, "desktopStyle": {}}, "overlay": {"alt": "Smile we're going greener 85% of the fibers used in our clothing will be more sustainable by 2025. Imagine a greener future", "srcUrl": "/Asset_Archive/ONWeb/content/0028/852/148/assets/220300_10-M7552_Sustainability_IM_HamNavBanner_US_SM.svg?v=2"}, "background": {"linkData": {"to": "/browse/info.do?cid=1160383&mlink=5151,1,w_sustainability_hamnav_bottom"}, "image": {"alt": "An Old Navy customer stands beneath a clear blue sky with fluffy clouds.", "srcUrl": "/Asset_Archive/ONWeb/content/0028/852/148/assets/220300_10-M7552_Sustainability_IM_HamNavBanner_US_SM.jpg?v=2", "style": {"display": "block", "box-sizing": "border-box"}}, "style": {"width": "100%", "display": "block", "padding": "0", "margin": "0 auto"}, "desktopStyle": {}}}}]}}, "search-CIID": "27876874", "desc": "contains search terms for animated search", "search": {"type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["<PERSON><PERSON>", "Leggings", "Sweatshirt", "Flannel Shirts", "<PERSON><PERSON><PERSON>"]}}, "appsflyer-smart-banner": {"sitewide-appsflyer-smart-banner-ciid": "27881906", "data": {"meta": {"excludePageTypes": [""]}}}, "edfslarge": {"instanceName": "edfs-header-large", "description": "11-19-21", "sitewide-edfs-large-ciid": "********", "type": "sitewide", "name": "MktEdfsLarge", "tileStyle": {"display": "flex", "height": "40px", "alignItems": "center", "margin-top": "1px"}, "experimentRunning": true, "data": {"lazy": false, "shouldWaitForOptimizely": true, "defaultData": {"text": "FREE SHIPPING ON $50+ FOR REWARDS MEMBERS", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/Asset_Archive/AllBrands/edfs/edfsLegal-US.html?file=edfsContent_on_122221.js", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "SIGN IN OR JOIN", "path": "/my-account/sign-in", "style": {"letterSpacing": "0", "fontSize": "1em", "display": "inline", "fontWeight": "400", "position": "relative", "top": "-1.5px", "fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ON Sans Text, 'Open Sans', 'Gap Sans', Helvetica, Arial, Roboto, sans-serif"}}}}, "edfssmall": {"instanceName": "edfs-header-small", "sitewide-edfs-small-ciid": "********", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"large": "80px", "small": "50px"}, "isVisible": {"large": false, "small": true}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto", "fontSize": "10px"}, "components": [{"type": "sitewide", "name": "MktEdfsSmall", "data": {"lazy": false, "experimentRunning": false, "styles": {"headline": {"padding": "0", "fontSize": "10px"}, "detailsButton": {"paddingRight": "0"}}, "defaultData": {"textStrong": "Free Shipping", "text": "ON $50+ FOR REWARDS MEMBERS", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/Asset_Archive/AllBrands/edfs/edfsLegal-US.html?file=edfsContent_on_122221.js", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "SIGN IN OR JOIN", "path": "/my-account/sign-in", "style": {"letterSpacing": "0", "display": "inline", "position": "relative", "top": "-1px", "fontSize": "10px", "fontWeight": "400", "paddingRight": "0", "fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ON Sans Text, 'Open Sans', 'Gap Sans', Helvetica, Arial, Roboto, sans-serif"}}}}]}}}}, "hamburgerNavBanner": {"CIID": "********", "type": "builtin", "name": "div", "data": {"props": {"class": "hamNavMaskShop", "style": {"fontFamily": "Avenir Next,Gap Sans,Helvetica,Arial,Roboto,sans-serif", "display": "none", "background": "#0466CA", "fontSize": 0, "width": "100%"}}, "components": [{"type": "builtin", "name": "a", "data": {"props": {"href": "/browse/info.do?cid=1165246&mlink=5058,********,maskshop_hamnav", "style": {"textDecoration": "none", "color": "#FFFFFF", "width": "100%"}}, "components": [{"type": "builtin", "name": "div", "data": {"props": {"style": {"fontSize": "18px", "fontWeight": "bold", "height": "24px", "paddingLeft": "1rem", "paddingTop": ".25rem", "textTransform": "uppercase"}}, "components": ["The Mask Shop"]}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"fontSize": "14px", "paddingBottom": "10px", "letterSpacing": "-.5px", "paddingLeft": "1rem"}}, "components": ["Shop masks from our family of brands."]}}]}}, {"type": "builtin", "name": "img", "data": {"props": {"style": {"float": "right", "paddingRight": "1.3rem"}, "src": "/Asset_Archive/BRWeb/content/0019/448/010/assets/hamChevron.svg", "alt": "chevron"}}}]}}, "topnav": {"sitewide-topnav-ciid": "28738018", "sitewide-topnav-desc": "removed meganav ab test", "type": "sitewide", "name": "MegaNav", "data": {"isNavSticky": true, "classStyles": {"divisionLink._selected ": "color: #003764;", "meganav:not(.custom) .catnav--header > span ": "margin-top: 12px", "meganav:not(.custom) .catnav--header > a ": "margin-top: 12px", "meganav .wcdNavLimit .meganav-column": "display:inline-block;", "meganav-category-group.MatchingGiftsfortheFamily": "margin-bottom:0px;width:100%;float:left;", "meganav .catnav--header": "float:left;letter-spacing:0px;", "topnav .divisionLink": "letter-spacing: .7px !important; text-transform: capitalize !important; font-weight: 400; color: #000 !important", "topnav .divisionLink._selected": "font-weight: bold !important; color: #003764 !important", "topnav a.divisionLink:hover": "font-weight: bold !important; color: #003764 !important", "topNavLink:nth-child(8) .wcdNavLimit, .topNavLink:nth-child(9) .wcdNavLimit": "position:relative;overflow-x:hidden;", "wcdNavLimit ul.custom": "margin-bottom:40px;", "topNavLink .wcdNavLimit ul.custom .division-header": "position:absolute;background-color:#f7f7f7;font-weight:700;color:#000000;font-size:.875rem;padding:.5em 0 .5em .5em;", "topNavLink .wcd-toddler .wcdNavLimit ul.custom .division-header": "width:33.5%;", "topNavLink .wcd-baby .wcdNavLimit ul.custom .division-header": "width:31%;", "topNavLink .wcdNavLimit ul.custom .division-header span": "font-weight:400;", "meganav .catnav--header a": "color: #000000;", "meganav .catnav--header a:hover": "color: #003764 !important;", "meganav.wcd-new .catnav--item a:hover": "color: #003764 !important; border-bottom: 1px solid #003764", "meganav.wcd-new .catnav--item span": "padding-bottom: 2px", "meganav.wcd-new .catnav--header > a::after": "content: ' >'", "meganav.wcd-gifts .catnav--header > a::after": "content: ' >'", "meganav.wcd-womenwomensplus .catnav--header > a::after": "content: ' >'", "meganav.wcd-men .catnav--header > a::after": "content: ' >'", "meganav.wcd-girls .catnav--header > a::after": "content: ' >'", "meganav.wcd-boys .catnav--header > a::after": "content: ' >'", "meganav.wcd-toddler .catnav--header > a::after": "content: ' >'", "meganav.wcd-baby .catnav--header > a::after": "content: ' >'", "meganav.wcd-maternity .catnav--header > a::after": "content: ' >'", "meganav.wcd-familyoutfits .catnav--header > a::after": "content: ' >'", "meganav.wcd-genderneutral .catnav--header > a::after": "content: ' >'", "meganav.wcd-sale .catnav--header > a::after": "content: ' >'", "meganav.wcd-masks .catnav--header > a::after": "content: ' >'", "meganav.wcd-familypajamas .catnav--header > a::after": "content: ' >'", "meganav.wcd-womenwomensplus .wcdNavLimit .meganav-column:nth-child(3)": "width: 220px", "meganav.wcd-toddler .meganav-column": "width: 220px", "meganav.wcd-baby .meganav-column": "width: 200px"}, "activeDivisions": [{"name": "New!", "divisionId": ["/browse/category.do?cid=10018&mlink=5151,topNavNA,visnav", "10018"], "displaySubMenu": false, "megaNavOrder": [["3007540", "3007638"], ["3007550"], ["3007557", "3007566"], ["3007575", "3007610"], ["3007664", "3007625"]], "exclusionIds": [], "customStyles": {"*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Women & Women’s Plus", "subtitle": "New! 0-30 & XS-4X", "divisionId": ["/browse/division.do?cid=5360&mlink=5151,topNav,visnav", "5360"], "megaNavOrder": [["1039280", "1182491", "5363", "1016051"], ["1036191"], ["55182", "1182446"], ["1019570", "1179124", "1176447"], ["<ul class='catnav-links'><li class='catnav--item'><a data-categoryid='15292' href='/browse/category.do?cid=15292&mlink=5151,w_mnav_tile' class='catnav--item--link' style='position: relative; display: block; max-width: 183px;'><span><img class='topNav_na_w_img--11012018' src='/Asset_Archive/ONWeb/content/0028/738/018/assets/220316_57_M6562_Dresses_W_Site_Meganav_USCA.jpg' alt='A woman in a checkered yellow and white babydoll style dress, paired with a baseball cap and a jean jacket.'><img style='position: absolute; left:0; bottom:0;' src='/Asset_Archive/ONWeb/content/0028/738/018/assets/220316_57_M6562_Dresses_W_Site_Meganav_USCA.svg' alt='Shop Dresses'></span></a></li></ul>"]], "numberOfColumns": {"1036191": 1}, "exclusionIds": ["1121199"], "customStyles": {"26190": {"colorScheme": "sale"}, "96964": {"colorScheme": "sale"}, "1134300": {"colorScheme": "sale", "inlineStyle": {"font-weight": "bold"}}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Men", "divisionId": ["/browse/division.do?cid=5155&mlink=5151,topNav,visnav", "5155"], "megaNavOrder": [["1039290", "5158", "55197"], ["1036209"], ["1145656", "1031097", "1114600"], ["1179148"], ["<ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1031103' href='/browse/category.do?cid=1031103&mlink=5151,m_mnav_tile' class='catnav--item--link' style='position: relative; display: block; max-width: 183px;'><span><img class='topNav_na_w_img--11012018' src='/Asset_Archive/ONWeb/content/0028/738/018/assets/220114_85-M6941_Active_M_Meganav_USCA.jpg' alt='A male model wearing green fleece, dark blue short and white active jacket.'><img style='position: absolute; left:0; bottom:0;' src='/Asset_Archive/ONWeb/content/0028/738/018/assets/220114_85-M6941_Active_M_Meganav_USCA.svg' alt='Shop activewear'></span></a></li></ul>"]], "numberOfColumns": {"1036209": 1}, "exclusionIds": ["1121202"], "customStyles": {"26061": {"colorScheme": "sale"}, "97035": {"colorScheme": "sale"}, "1134756": {"colorScheme": "sale", "inlineStyle": {"font-weight": "bold"}}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Girls", "divisionId": ["/browse/division.do?cid=6027&mlink=5151,topNav,visnav", "6027"], "megaNavOrder": [["1107779", "1092613", "1182490", "48771", "1119128", "54857"], ["1036216"], ["1007069", "1015504", "1125471"], ["1179135"], ["<ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1013969' href='/browse/category.do?cid=1013969&mlink=5151,g_mnav_tile' class='catnav--item--link' style='position: relative; display: block; max-width: 183px;'><span><img class='topNav_na_w_img--11012018' src='/Asset_Archive/ONWeb/content/0028/738/018/assets/220114_82_M6943_JanuaryRefresh_G_Meganav_USCA.jpg' alt='A young model is wearing pink long sleeve top and a tie-dye active bottom.'><img  style='position: absolute; left:0; bottom:0;' src='/Asset_Archive/ONWeb/content/0028/738/018/assets/220114_82_M6943_JanuaryRefresh_G_Meganav_USCA.svg' alt='Shop activewear'></span></a></li></ul>"]], "numberOfColumns": {"1036216": 1}, "exclusionIds": ["1121223"], "customStyles": {"26175": {"colorScheme": "sale"}, "96906": {"colorScheme": "sale"}, "1134507": {"colorScheme": "sale", "inlineStyle": {"font-weight": "bold"}}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Boys", "divisionId": ["/browse/division.do?cid=5910&mlink=5151,topNav,visnav", "5910"], "megaNavOrder": [["1107816", "1071417", "48805", "1119129", "1137413", "54865"], ["1036210"], ["1006914", "1015530", "1125295"], ["1179139"], ["<ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1013976' href='/browse/category.do?cid=1013976&mlink=5151,b_mnav_tile' class='catnav--item--link' style='position: relative; display: block; max-width: 183px;'><span><img class='topNav_na_w_img--11012018' src='/Asset_Archive/ONWeb/content/0028/738/018/assets/220114_82_M6944_JanuaryRefresh_B_Meganav_USCA.jpg' alt='A young model is wearing dark color hoodie and pink sweatpants.'><img style='position: absolute; left:0; bottom:0;' src='/Asset_Archive/ONWeb/content/0028/738/018/assets/220114_82_M6944_JanuaryRefresh_B_Meganav_USCA.svg' alt='Shop activewear'></span></a></li></ul>"]], "numberOfColumns": {"1036210": 1}, "exclusionIds": ["1121192"], "customStyles": {"26073": {"colorScheme": "sale"}, "96945": {"colorScheme": "sale"}, "1134509": {"colorScheme": "sale", "inlineStyle": {"font-weight": "bold"}}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=6241&mlink=5151,topNav,visnav", "6241"], "megaNavOrder": [["<div class='toddlergirls-division-header division-header'>TODDLER GIRLS <span>(12M-6T)</span></div>", "1073646", "1017563", "54835"], ["<div class='division-header-spacer'></div>", "1073647"], ["<div class='toddlerboys-division-header division-header'>TODDLER BOYS <span>(12M-6T)</span></div>", "1073756", "1119612", "1071067", "1040818"], ["<div class='division-header-spacer'></div>", "1073744"]], "exclusionIds": ["1071074", "1071075", "1121187", "1121188"], "customStyles": {"26619": {"colorScheme": "sale"}, "26785": {"colorScheme": "sale"}, "53699": {"colorScheme": "sale"}, "97017": {"colorScheme": "sale"}, "1134456": {"colorScheme": "sale", "inlineStyle": {"font-weight": "bold"}}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Baby", "divisionId": ["/browse/division.do?cid=51375&mlink=5151,topNav,visnav", "51375"], "megaNavOrder": [["<div class='toddlerboys-division-header division-header'>BABY GIRLS <span>(0-24M)</span></div>", "1077947", "1017553", "60396"], ["<div class='division-header-spacer'></div>", "1077949"], ["<div class='toddlerboys-division-header division-header'>BABY BOYS <span>(0-24M)</span></div>", "1078107", "1077229", "1006541"], ["<div class='division-header-spacer'></div>", "1077950"]], "exclusionIds": ["1077228", "1077230", "1121207", "1121205"], "customStyles": {"51646": {"colorScheme": "sale"}, "51666": {"colorScheme": "sale"}, "96918": {"colorScheme": "sale"}, "96919": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Maternity", "divisionId": ["/browse/division.do?cid=5758&mlink=5151,topNav,visnav", "5758"], "megaNavOrder": [["1065183", "7791", "55185"], ["1036208"], ["1092435", "1179148"]], "numberOfColumns": {"1036208": 2}, "exclusionIds": ["1121193"], "customStyles": {"26239": {"colorScheme": "sale"}, "96921": {"colorScheme": "sale"}, "1134523": {"colorScheme": "sale", "inlineStyle": {"font-weight": "bold"}}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Family Outfits", "divisionId": ["/browse/category.do?cid=1189220&mlink=5151,topNav,visnav", "1183013"], "megaNavOrder": [["1189249"], ["<ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1189221' href='/browse/category.do?cid=1189221&mlink=5151,fam_mnav_tile' class='catnav--item--link' style='position: relative; display: block; max-width: 183px;'><span><img class='topNav_na_w_img--11012018' src='/Asset_Archive/ONWeb/content/0028/738/018/assets/220307_34_M7701_FamilyOutfits_FAM_Meganav_USCA.jpg?v=2' alt='A family — 2 adults, 1 child— enjoying the warm weather and dresses in pink color shirt, plaid dress, floral-print shirt and pants.'><img  style='position: absolute; left:0; bottom:0;' src='/Asset_Archive/ONWeb/content/0028/738/018/assets/220307_34_M7701_FamilyOutfits_FAM_Meganav_USCA.svg?v=2' alt='Shop spring outfits'></span></a></li></ul>"]], "exclusionIds": [""], "customStyles": {"*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Gender Neutral", "divisionId": ["/browse/category.do?cid=1174663&mlink=5151,topNav,visnav", "1174657"], "megaNavOrder": [["1179650"], ["1179665"], ["1179666"], ["1179667"], ["1179668"]], "exclusionIds": [""], "customStyles": {"*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Sale", "divisionId": ["/browse/category.do?cid=26190&mlink=5151,topNav,visnav", "1183117"], "megaNavOrder": [["1183119", "1183123"], ["1183125"], ["1183128", "1183131"], ["1183143", "1183145"], ["1183146", "1183147"]], "exclusionIds": [""], "customStyles": {"*": {"inlineStyle": {"letter-spacing": "0"}}}}]}}, "onflyoutedubanner": {"sitewide-onflyoutedubanner-ciid": "28738018", "name": "LayoutComponent", "type": "sitewide", "instanceName": "mktg-flyout-banner", "experimentRunning": false, "useGreyLoadingEffect": false, "data": {"lazy": false, "defaultHeight": {"large": "50px", "small": "0px"}, "isVisible": {"large": true, "small": false}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "justifyContent": "center", "margin": "1% auto 0 auto", "maxWidth": "1140px"}, "components": [{"type": "sitewide", "name": "Dismissible", "data": {"component": {"name": "div", "type": "builtin", "data": {"props": {"style": {"display": "inline-flex", "flexDirection": "row", "marginTop": "0", "justifyContent": "flex-start", "width": "100%", "maxWidth": "1140px"}}, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "wcdNote": "custom position styles for dismissible btn", "data": {"lazy": false, "defaultHeight": "0px", "html": "<style>.meganav.wcd-womenwomensplus button {top: 38%; right: .75%; opacity: 0 !important}</style>"}}, {"name": "LayeredContentModule", "type": "sitewide", "instanceName": "skinny-banner", "data": {"excludePageTypes": [], "lazy": false, "container": {"className": "", "style": {"background": "linear-gradient(90deg, #f5f1e3 50%, #fee700 50%)", "display": "block"}, "desktopStyle": {"width": "100%", "background": "linear-gradient(90deg, #f5f1e3 62.5%, #f5f1e3 62.5%)", "display": "block"}}, "background": {"linkData": {"to": "/browse/info.do?cid=1180206&mlink=5151,1,w_mnav_skinny_banner"}, "image": {"alt": "Dear women everywhere, Now, all women's styles are made in sizes 0-30, XS-4X, with no difference in price and no special sections.", "srcUrl": "/Asset_Archive/ONWeb/content/0028/738/018/assets/210820_67-M2133_BodEquality_W_MegaNavSkinnyBnnr_US.svg", "style": {"display": "block", "box-sizing": "border-box"}}, "style": {"width": "100%", "display": "block", "padding": "0", "margin": "0 auto"}, "desktopStyle": {"display": "block", "maxWidth": "1440px"}}}}]}}, "id": "<PERSON><PERSON><PERSON><PERSON>", "redisplayAfter": "5000"}}]}}}}, "hamnav": {"sitewide-hamnav-ciid": "28738018", "data": {"exclusionIds": ["49708", "49709", "1090014", "1091221"]}}}, "home": {"type": "home", "name": "HomeMultiSimple", "instanceName": "041822-HP", "home-ciid": "28959445", "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "wcdNote": "Adds custom CSS file for rendering the new mobile CTA primary styles (4 half-width buttons with 1 full-width flyout)", "data": {"lazy": false, "defaultHeight": "0px", "html": "<link rel=\"stylesheet\" href=\"/Asset_Archive/ONWeb/content/0028/959/445/css/mobile-cta.css?v=5\"><style type=\"text/css\">.slick-list{ overflow: hidden !important}</style>"}}, {"instanceName": "dpg-banner", "instanceDesc": "DPG-placeholder-ON", "type": "builtin", "name": "div", "experimentRunning": true, "redpointExperimentRunning": false, "useGreyLoadingEffect": false, "desktop": {"height": "0"}, "mobile": {"height": 0}, "data": {"lazy": false, "shouldWaitForOptimizely": true, "defaultHeight": {"large": "0px", "small": "0px"}, "isVisible": {"large": true, "small": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"width": 0, "height": 0}}}}, {"instanceName": "041822-oh-my-deals-primary-banner", "name": "LayeredContentModule", "type": "sitewide", "meta": {"lazy": false}, "data": {"defaultHeight": {"small": "278px", "large": "111px"}, "lazy": false, "background": {"linkData": {"to": "/browse/category.do?cid=1165714&mlink=5151,1,HP_Prim_1_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_DealsBanner_US_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_DealsBanner_US_XL.svg", "alt": "Oh my deals, styles from $8.", "style": {"display": "block"}}}, "container": {"style": {"maxWidth": "1400px", "margin": "0 auto", "position": "relative", "paddingTop": "10px"}, "desktopStyle": {"paddingTop": "20px"}}, "ctaList": {"className": "four-ctas-on-mobile show-flyout", "style": {"textAlign": "center", "padding": "0", "justifyContent": "space-between", "flexDirection": "row", "flexWrap": "nowrap", "margin": "1rem auto"}, "desktopStyle": {"position": "relative", "top": "100%", "left": "50%", "transform": "translate(-50%, 0)", "display": "flex", "width": "100%", "padding": "0", "margin": "0 auto", "justifyContent": "space-evenly", "flexWrap": "nowrap", "a": {"width": "auto", "flex": "0 1 auto", "backgroundColor": "transparent", "textDecoration": "underline", "borderRadius": "0px", "border": "none", "color": "#000000", "textAlign": "center", "padding": "1.5% 0", "letterSpacing": "0.4px", "fontWeight": "700", "fontSize": "calc(11px + (23 - 12) * ((100vw - 768px) / (3000 - 768)))", "height": "auto", "@media (min-width: 1400px)": {"fontSize": "14px"}}}, "ctas": [{"composableButtonData": {"children": "Women & Women's Plus"}, "linkData": {"to": "/browse/category.do?cid=1165714&mlink=5151,1,HP_Prim_1_b"}, "className": "women"}, {"composableButtonData": {"children": "Men"}, "linkData": {"to": "/browse/category.do?cid=1135640&mlink=5151,1,HP_Prim_1_b"}, "className": "men"}, {"composableButtonData": {"children": "Girls"}, "linkData": {"to": "/browse/category.do?cid=1187085&mlink=5151,1,HP_Prim_1_b"}, "className": "girls"}, {"composableButtonData": {"children": "Boys"}, "linkData": {"to": "/browse/category.do?cid=1188346&mlink=5151,1,HP_Prim_1_b"}, "className": "boys"}, {"composableButtonData": {"children": "Toddler Girls"}, "linkData": {"to": "/browse/category.do?cid=1186906#pageId=0&department=165&mlink=5151,1,HP_Prim_1_b"}, "className": "toddler-girls"}, {"composableButtonData": {"children": "Toddler Boys"}, "linkData": {"to": "/browse/category.do?cid=1185646#pageId=0&department=166&mlink=5151,1,HP_Prim_1_b"}, "className": "toddler-boys"}, {"composableButtonData": {"children": "Baby Girls"}, "linkData": {"to": "/browse/category.do?cid=1185648#pageId=0&department=165&mlink=5151,1,HP_Prim_1_b"}, "className": "baby-girls"}, {"composableButtonData": {"children": "Baby Boys"}, "linkData": {"to": "/browse/category.do?cid=1185650#pageId=0&department=166&mlink=5151,1,HP_Prim_1_b"}, "className": "baby-boys"}, {"composableButtonData": {"children": "Maternity"}, "linkData": {"to": "/browse/category.do?cid=1136836&mlink=5151,1,HP_Prim_1_b"}, "className": "maternity"}, {"buttonDropdownData": {"customClasses": "show-for-four-cta-only", "heading": {"text": "Shop for the Fam"}, "style": {"desktop": {"display": "none"}, "mobile": {"width": "100%", "lineHeight": 1, "margin": "0", "textAlign": "center", "letterSpacing": "1px", "padding": "1rem 0"}}, "submenu": [{"text": "Toddler Girls", "href": "/browse/category.do?cid=1186906#pageId=0&department=165&mlink=5151,1,HP_Prim_1_b"}, {"text": "Toddler Boys", "href": "/browse/category.do?cid=1185646#pageId=0&department=166&mlink=5151,1,HP_Prim_1_b"}, {"text": "Baby Girls", "href": "/browse/category.do?cid=1185648#pageId=0&department=165&mlink=5151,1,HP_Prim_1_b"}, {"text": "Baby Boys", "href": "/browse/category.do?cid=1185650#pageId=0&department=166&mlink=5151,1,HP_Prim_1_b"}, {"text": "Maternity", "href": "/browse/category.do?cid=1136836&mlink=5151,1,HP_Prim_1_b"}]}}]}}}, {"instanceName": "041822-oh-my-deals-slide-transition-animation", "name": "LayoutComponent", "type": "sitewide", "meta": {"lazy": false}, "data": {"defaultHeight": {"small": "360px", "large": "388px"}, "lazy": false, "desktop": {"shouldDisplay": true, "data": {"classes": "", "style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "maxWidth": "1400px", "width": "100%", "margin": "0 auto"}, "components": [{"instanceName": "carousel-desktop-3-slides", "name": "Carousel", "type": "sitewide", "data": {"carouselOptions": {"slidesToShow": 1, "slidesToScroll": 1, "autoplay": true, "speed": 2000, "autoplaySpeed": 2000, "fade": false, "displayPlayPauseBtn": true, "dots": false, "dotsClass": "slick-dots", "infinite": true, "displayArrows": {"desktop": false, "mobile": false}, "pauseOnHover": false, "prevArrowUrl": "", "nextArrowUrl": ""}, "buttonSetting": {"buttonStyle": {"position": "absolute", "top": "2%", "bottom": "auto", "right": "2%", "left": "auto", "transform": "translate(0,0)", "height": "2em", "width": "2em", "zIndex": "0", "@media (min-width: 768px)": {"right": "2%"}}}, "style": {"maxWidth": "1400px", "margin": "0 auto", "position": "relative"}, "components": [{"instanceName": "frame1", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1165714&mlink=5151,1,HP_Prim_2_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s1_US_XL.jpg", "alt": "A group of models wearing shirts, high waisted powersoft loose shorts, active jackets and bucket hats.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s1_US_XL.svg", "alt": "Weekend you. Activewear from $12."}, "container": {"style": {"position": "relative"}}}}, {"instanceName": "frame2", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1165714&mlink=5151,1,HP_Prim_2_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s2_US_XL.jpg", "alt": "A range of models are dressed in clothing from Old Navy's active spring collection.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s2_US_XL.svg", "alt": "Explorer you."}, "container": {"style": {"position": "relative"}}}}, {"instanceName": "frame3", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1165714&mlink=5151,1,HP_Prim_2_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s3_US_XL.jpg", "alt": "Female models wearing colorful high waisted powersoft loose shorts, dresses, sports bra, skirts, active jackets.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s3_US_XL.svg", "alt": "Go you."}, "container": {"style": {"position": "relative"}}}}]}}]}}, "mobile": {"shouldDisplay": true, "data": {"classes": "", "style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto"}, "components": [{"name": "div", "type": "builtin", "instanceName": "catnav-row1", "data": {"style": {"margin": "0 auto", "textAlign": "center", "& .slick-dots": {"bottom": "5px !important", "background": "none"}, "& .slick-slide > div > div": {"display": "block !important"}}, "desktopStyle": {"margin": "0 auto", "maxWidth": "1400px"}, "components": [{"instanceName": "carousel-mobile-4-slides", "name": "Carousel", "type": "sitewide", "data": {"carouselOptions": {"slidesToShow": 1, "slidesToScroll": 1, "autoplay": true, "speed": 2000, "autoplaySpeed": 2000, "fade": false, "displayPlayPauseBtn": true, "dots": false, "dotsClass": "slick-dots", "infinite": true, "displayArrows": {"desktop": false, "mobile": false}, "pauseOnHover": false, "prevArrowUrl": "", "nextArrowUrl": ""}, "buttonSetting": {"buttonStyle": {"position": "absolute", "top": "2%", "bottom": "auto", "right": "2%", "left": "auto", "transform": "translate(0,0)", "height": "2em", "width": "2em", "zIndex": "0", "@media (min-width: 768px)": {"right": "10%"}}}, "style": {"maxWidth": "1400px", "margin": "0 auto", "position": "relative"}, "components": [{"instanceName": "frame1", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1165714&mlink=5151,1,HP_Prim_2_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s1_US_SM.jpg", "alt": "A group of models wearing shirts, high waisted powersoft loose shorts, active jackets and bucket hats.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s1_US_SM.svg", "alt": "Weekend you. Activewear from $12."}, "container": {"style": {"position": "relative"}}}}, {"instanceName": "frame2", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1165714&mlink=5151,1,HP_Prim_2_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s2_US_SM.jpg", "alt": "A range of models are dressed in clothing from Old Navy's active spring collection.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s2_US_SM.svg", "alt": "Explorer you."}, "container": {"style": {"position": "relative"}}}}, {"instanceName": "frame3", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1165714&mlink=5151,1,HP_Prim_2_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s3_US_SM.jpg", "alt": "Female models wearing colorful high waisted powersoft loose shorts, dresses, sports bra, skirts, active jackets.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s3_US_SM.svg", "alt": "Go you."}, "container": {"style": {"position": "relative"}}}}, {"instanceName": "frame4", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1165714&mlink=5151,1,HP_Prim_2_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s4_US_SM.jpg", "alt": "Two young models wearing powersoft loose shorts, quarter zip sweatshirt and long sleeve jean shirt.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M6332_Athleisure_HP_Hero_s4_US_SM.svg", "alt": "Activewear from $12."}, "container": {"style": {"position": "relative"}}}}]}}]}}]}}}}, {"instanceName": "041822-oh-my-deals-html-text", "name": "LayoutComponent", "type": "sitewide", "meta": {"lazy": true}, "data": {"defaultHeight": {"small": "53px", "large": "40px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "maxWidth": "1400px", "margin": "0 auto", "position": "relative", "textAlign": "center"}, "components": [{"name": "a", "type": "builtin", "data": {"style": {"display": "inline-block", "color": "#0019ff", "background": "transparent", "fontSize": "3.4722222222222223vw", "fontWeight": "500", "textAlign": "center", "whiteSpace": "pre-line", "margin": "3% auto", "padding": "0", "lineHeight": "1.28", "letterSpacing": "0.10416666666666667vw", "& span": {"font-weight": "bold"}}, "desktopStyle": {"display": "inline-block", "fontSize": "min(1.7857142857142856vw, 25px)", "letterSpacing": "min(0.05357142857142857vw, .75px)", "width": "auto", "margin": "1.5% auto"}, "props": {"href": "/browse/category.do?cid=1165714&mlink=5151,1,HP_Prim_2_a", "tid": "HP_Prim_2_a"}, "components": [{"name": "span", "type": "builtin", "data": {"components": ["PowerSoft"]}}, " & ", {"name": "span", "type": "builtin", "data": {"components": ["StretchTech"]}}, " are down for anything,\nbecause you’re up for everything."], "desktopComponents": [{"name": "span", "type": "builtin", "data": {"components": ["PowerSoft"]}}, " & ", {"name": "span", "type": "builtin", "data": {"components": ["StretchTech"]}}, " are down for anything, because you’re up for everything."]}}]}}}}, {"instanceName": "041822-oh-my-deals-two-ctas", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"defaultHeight": {"small": "102px", "large": "48px"}, "background": {"image": {"srcUrl": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='0' height='0'><rect width='100%' height='100%' style='fill:transparent'/></svg>", "desktopSrcUrl": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='0' height='0'><rect width='100%' height='100%' style='fill:transparent'/></svg>", "alt": " ", "style": {"display": "block"}}, "style": {}, "desktopStyle": {"height": "48px", "marginBottom": "2.55%"}}, "container": {"style": {"position": "relative", "paddingBottom": ".5rem", "display": "block"}, "desktopStyle": {"margin": "0 auto", "paddingBottom": "0", "maxWidth": "1400px", "display": "block"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"margin": "0 auto", "justifyContent": "center", "alignItems": "center", "textAlign": "center", "& > a": {"fontSize": "0.9735rem", "height": "48px", "letterSpacing": "normal", "backgroundColor": "#FFFFFF", "width": "90%", "margin": "0 auto 7.5px", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764"}, "& > a:first-child": {"marginTop": "1rem"}, "& > div": {"width": "90%", "margin": "0 auto 7.5px"}, "& button": {"padding": "0.625rem 0", "fontSize": "0.9rem", "width": "100%", "margin": "0"}}, "desktopStyle": {"position": "absolute", "top": "0", "display": "flex", "flexDirection": "row", "justifyContent": "space-between", "alignItems": "flex-start", "width": "60%", "maxWidth": "1400px", "left": "50%", "transform": "translate(-50%, 0)", "zIndex": "33", "margin": "0 auto", "& > a": {"backgroundColor": "#FFFFFF", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem 0.5rem", "whiteSpace": "normal", "lineHeight": "1", "width": "45%", "margin": "0"}, "& > a:first-child": {"marginTop": "0"}, "& > div": {"width": "45%", "margin": "0"}, "& button": {"height": "48px", "width": "100%"}, "& ul": {"minWidth": "100%", "width": "100%", "left": "50%", "transform": "translate(-50%, 0)"}, "@media (min-width: 768px) and (max-width: 1200px)": {"width": "80%"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop Sale"}, "submenu": [{"text": "Women & Women's Plus", "href": "/browse/category.do?cid=1165714&mlink=5151,1,HP_Prim_2_b", "trackingId": "HP_Prim_2_b"}, {"text": "Men", "href": "/browse/category.do?cid=1135640&mlink=5151,1,HP_Prim_2_b", "trackingId": "HP_Prim_2_b"}, {"text": "Girls", "href": "/browse/category.do?cid=1187085&mlink=5151,1,HP_Prim_2_b", "trackingId": "HP_Prim_2_b"}, {"text": "Boys", "href": "/browse/category.do?cid=1188346&mlink=5151,1,HP_Prim_2_b", "trackingId": "HP_Prim_2_b"}, {"text": "Toddler Girls", "href": "/browse/category.do?cid=1186906#pageId=0&department=165&mlink=5151,1,HP_Prim_2_b", "trackingId": "HP_Prim_2_b"}, {"text": "Toddler Boys", "href": "/browse/category.do?cid=1185646#pageId=0&department=166&mlink=5151,1,HP_Prim_2_b", "trackingId": "HP_Prim_2_b"}, {"text": "Baby Girls", "href": "/browse/category.do?cid=1185648#pageId=0&department=165&mlink=5151,1,HP_Prim_2_b", "trackingId": "HP_Prim_2_b"}, {"text": "Baby Boys", "href": "/browse/category.do?cid=1185650#pageId=0&department=166&mlink=5151,1,HP_Prim_2_b", "trackingId": "HP_Prim_2_b"}, {"text": "Maternity", "href": "/browse/category.do?cid=1136836&mlink=5151,1,HP_Prim_2_b", "trackingId": "HP_Prim_2_b"}], "style": {"desktop": {"backgroundColor": "#FFFFFF", "margin": "0 auto", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem 0.5rem", "whiteSpace": "normal", "lineHeight": "1", "width": "95%"}, "mobile": {"backgroundColor": "#FFFFFF", "width": "90%", "margin": "0 auto", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764"}}}}, {"buttonDropdownData": {"heading": {"text": "Shop Activewear"}, "submenu": [{"text": "Women & Women's Plus", "href": "/browse/category.do?cid=5508&mlink=5151,1,HP_Prim_2_c", "trackingId": "HP_Prim_2_c"}, {"text": "Men", "href": "/browse/category.do?cid=1031103&mlink=5151,1,HP_Prim_2_c", "trackingId": "HP_Prim_2_c"}, {"text": "Girls", "href": "/browse/category.do?cid=1013969&mlink=5151,1,HP_Prim_2_c", "trackingId": "HP_Prim_2_c"}, {"text": "Boys", "href": "/browse/category.do?cid=1013976&mlink=5151,1,HP_Prim_2_c", "trackingId": "HP_Prim_2_c"}, {"text": "Maternity", "href": "/browse/category.do?cid=1053229&mlink=5151,1,HP_Prim_2_c", "trackingId": "HP_Prim_2_c"}], "style": {"desktop": {"backgroundColor": "#FFFFFF", "margin": "0 auto", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem 0.5rem", "whiteSpace": "normal", "lineHeight": "1", "width": "95%"}, "mobile": {"backgroundColor": "#FFFFFF", "width": "90%", "margin": "0 auto", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764"}}}}]}}}, {"instanceName": "041822-neutral-fashion-spring-svg-strip", "name": "LayeredContentModule", "meta": {"lazy": true}, "data": {"defaultHeight": {"small": "58px", "large": "27px"}, "container": {"style": {"position": "relative", "margin": "0 auto 1rem", "maxWidth": "1400px"}, "desktopStyle": {"marginBottom": "1.9%"}}, "background": {"linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,HP_Prim_3_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Headline_US_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Headline_US_XL.svg", "alt": "Spring just got a glow-up.", "style": {"display": "block"}}}}}, {"instanceName": "041822-neutral-fashion-slide-transition-animation", "name": "LayoutComponent", "type": "sitewide", "meta": {"lazy": true}, "data": {"defaultHeight": {"small": "340px", "large": "388px"}, "desktop": {"shouldDisplay": true, "data": {"classes": "", "style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "maxWidth": "1400px", "width": "100%", "margin": "0 auto"}, "components": [{"instanceName": "carousel-desktop-2-slides", "name": "Carousel", "type": "sitewide", "data": {"carouselOptions": {"slidesToShow": 1, "slidesToScroll": 1, "autoplay": true, "speed": 2000, "autoplaySpeed": 2000, "fade": false, "displayPlayPauseBtn": true, "dots": false, "dotsClass": "slick-dots", "infinite": true, "displayArrows": {"desktop": false, "mobile": false}, "pauseOnHover": false, "prevArrowUrl": "", "nextArrowUrl": ""}, "buttonSetting": {"buttonStyle": {"position": "absolute", "top": "2%", "bottom": "auto", "right": "2%", "left": "auto", "transform": "translate(0,0)", "height": "2em", "width": "2em", "zIndex": "0", "@media (min-width: 768px)": {"right": "2%"}}}, "style": {"maxWidth": "1400px", "margin": "0 auto", "position": "relative"}, "components": [{"instanceName": "frame1", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,HP_Prim_3_a", "title": "Styles from $8."}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s1_US_SM.jpg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s1_US_XL.jpg?v=1", "alt": "A female model wearing fit & flare smocked off the shoulder maxi dress and a male model wearing white t-shirt, plaid shirt and short.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s1_US_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s1_US_XL.svg", "alt": "Styles from $8."}, "container": {"style": {"position": "relative"}}}}, {"instanceName": "frame2", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,HP_Prim_3_a", "title": "Styles from $8."}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s2_US_SM.jpg?v=1", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s2_US_XL.jpg?v=1", "alt": "Image features an array of shirts, shorts, dresses, jeans in various complementary colors and patterns.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s2_US_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s2_US_XL.svg", "alt": "Styles from $8."}, "container": {"style": {"position": "relative"}}}}]}}]}}, "mobile": {"shouldDisplay": true, "data": {"classes": "", "style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto"}, "components": [{"name": "div", "type": "builtin", "instanceName": "catnav-row1", "data": {"style": {"margin": "0 auto", "textAlign": "center", "& .slick-dots": {"bottom": "5px !important", "background": "none"}, "& .slick-slide > div > div": {"display": "block !important"}}, "desktopStyle": {"margin": "0 auto", "maxWidth": "1400px"}, "components": [{"instanceName": "carousel-mobile-2-slides", "name": "Carousel", "type": "sitewide", "data": {"carouselOptions": {"slidesToShow": 1, "slidesToScroll": 1, "autoplay": true, "speed": 2000, "autoplaySpeed": 2000, "fade": false, "displayPlayPauseBtn": true, "dots": false, "dotsClass": "slick-dots", "infinite": true, "displayArrows": {"desktop": false, "mobile": false}, "pauseOnHover": false, "prevArrowUrl": "", "nextArrowUrl": ""}, "buttonSetting": {"buttonStyle": {"position": "absolute", "top": "2%", "bottom": "auto", "right": "2%", "left": "auto", "transform": "translate(0,0)", "height": "2em", "width": "2em", "zIndex": "0", "@media (min-width: 768px)": {"right": "10%"}}}, "style": {"maxWidth": "1400px", "margin": "0 auto", "position": "relative"}, "components": [{"instanceName": "frame1", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,HP_Prim_3_a", "title": "Styles from $8."}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s1_US_SM.jpg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s1_US_XL.jpg?v=1", "alt": "A female model wearing fit & flare smocked off the shoulder maxi dress and a male model wearing white t-shirt, plaid shirt and short.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s1_US_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s1_US_XL.svg", "alt": "Styles from $8."}, "container": {"style": {"position": "relative"}}}}, {"instanceName": "frame2", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,HP_Prim_3_a", "title": "Styles from $8."}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s2_US_SM.jpg?v=1", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s2_US_XL.jpg?v=1", "alt": "Image features an array of shirts, shorts, dresses, jeans in various complementary colors and patterns.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s2_US_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s2_US_XL.svg", "alt": "Styles from $8."}, "container": {"style": {"position": "relative"}}}}, {"instanceName": "frame3", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,HP_Prim_3_a", "title": "Styles from $8."}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s3_US_SM.jpg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s3_US_XL.jpg", "alt": "Female models wearing waist defined printed dress, jean overall, tank top, jean short and white shirt.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s3_US_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220418_32_M7935_Neutrals_HPSec_Hero_s3_US_XL.svg", "alt": "Styles from $8."}, "container": {"style": {"position": "relative"}}}}]}}]}}]}}}}, {"instanceName": "041822-neutral-fashion-html-text", "name": "LayoutComponent", "type": "sitewide", "meta": {"lazy": true}, "data": {"defaultHeight": {"small": "53px", "large": "40px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "maxWidth": "1400px", "margin": "0 auto", "position": "relative", "textAlign": "center"}, "components": [{"name": "a", "type": "builtin", "data": {"style": {"display": "inline-block", "color": "#000000", "background": "transparent", "fontSize": "3.4722222222222223vw", "fontWeight": "500", "textAlign": "center", "whiteSpace": "pre-line", "margin": "3% auto", "padding": "0", "lineHeight": "1.28", "letterSpacing": "0.10416666666666667vw", "& span": {"font-style": "italic"}}, "desktopStyle": {"display": "inline-block", "fontSize": "min(1.7857142857142856vw, 25px)", "letterSpacing": "min(0.05357142857142857vw, .75px)", "width": "auto", "margin": "1.5% auto"}, "props": {"href": "/browse/category.do?cid=10018&mlink=5151,1,HP_Prim_3_a", "tid": "HP_Prim_3_a"}, "components": ["New, neutral, and just the\nright amount of dressy-", {"name": "span", "type": "builtin", "data": {"components": ["ish"]}}, "."], "desktopComponents": ["New, neutral, and just the right amount of dressy-", {"name": "span", "type": "builtin", "data": {"components": ["ish"]}}, "."]}}]}}}}, {"instanceName": "041822-neutral-fashion-single-cta", "name": "LayeredContentModule", "type": "sitewide", "meta": {"lazy": true}, "data": {"defaultHeight": {"small": "58px", "large": "48px"}, "background": {"image": {"srcUrl": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='767' height='102'><rect width='100%' height='100%' style='fill:transparent'/></svg>", "desktopSrcUrl": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='1400' height='48'><rect width='100%' height='100%' style='fill:transparent'/></svg>", "alt": " ", "style": {"display": "block"}}, "style": {"display": "none"}, "desktopStyle": {"display": "block", "height": "48px", "marginBottom": "2%"}}, "container": {"style": {"position": "relative", "marginBottom": "4%"}, "desktopStyle": {"margin": "0 auto", "maxWidth": "1400px"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"margin": "0 auto 1rem", "justifyContent": "center", "alignItems": "center", "textAlign": "center", "& > a": {"fontSize": "0.9rem", "height": "48px", "letterSpacing": "normal", "backgroundColor": "#FFFFFF", "width": "90%", "margin": "0 auto 7.5px", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764"}, "& > div": {"width": "90%", "margin": "0 auto 7.5px"}, "& button": {"padding": "0.625rem 0", "fontSize": "0.9rem", "width": "100%", "margin": "0"}, "@media (max-width: 767px)": {"& > a": {"height": "44px"}}}, "desktopStyle": {"position": "absolute", "top": "0", "maxWidth": "1400px", "left": "50%", "transform": "translate(-50%, 0)", "zIndex": "33", "margin": "0 auto", "& > a": {"backgroundColor": "#FFFFFF", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem 0.5rem", "whiteSpace": "normal", "lineHeight": "1", "width": "378px", "height": "48px", "margin": "0"}, "& > div": {"width": "100%", "margin": "0"}, "& button": {"width": "378px", "height": "48px"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop New Arrivals"}, "submenu": [{"text": "Women & Women's Plus", "href": "/browse/category.do?cid=10018&mlink=5151,1,HP_Prim_3_b", "trackingId": "HP_Prim_3_b"}, {"text": "Men", "href": "/browse/category.do?cid=11174&mlink=5151,1,HP_Prim_3_b", "trackingId": "HP_Prim_3_b"}, {"text": "Girls", "href": "/browse/category.do?cid=6036&mlink=5151,1,HP_Prim_3_b", "trackingId": "HP_Prim_3_b"}, {"text": "Boys", "href": "/browse/category.do?cid=5918&mlink=5151,1,HP_Prim_3_b", "trackingId": "HP_Prim_3_b"}, {"text": "Toddler Girls", "href": "/browse/category.do?cid=6825#pageId=0&department=165&mlink=5151,1,HP_Prim_3_b", "trackingId": "HP_Prim_3_b"}, {"text": "Toddler Boys", "href": "/browse/category.do?cid=6157#pageId=0&department=166&mlink=5151,1,HP_Prim_3_b", "trackingId": "HP_Prim_3_b"}, {"text": "Baby Girls", "href": "/browse/category.do?cid=37505#pageId=0&department=165&mlink=5151,1,HP_Prim_3_b", "trackingId": "HP_Prim_3_b"}, {"text": "Baby Boys", "href": "/browse/category.do?cid=37508#pageId=0&department=166&mlink=5151,1,HP_Prim_3_b", "trackingId": "HP_Prim_3_b"}, {"text": "Maternity", "href": "/browse/category.do?cid=8454&mlink=5151,1,HP_Prim_3_b", "trackingId": "HP_Prim_3_b"}], "style": {"desktop": {"backgroundColor": "#FFFFFF", "margin": "0rem auto", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem 0.5rem", "whiteSpace": "normal", "lineHeight": "1", "minWidth": "335px"}, "mobile": {"backgroundColor": "#FFFFFF", "width": "90%", "margin": "0 auto 1rem", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764"}}}}]}}}, {"instanceName": "040522-certona-rows", "name": "LayoutComponent", "type": "sitewide", "useGreyLoadingEffect": false, "experimentRunning": false, "meta": {"lazy": true}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"display": "flex", "margin": " 0 auto 1rem", "maxWidth": "1400px", "flexDirection": "column", "flexWrap": "nowrap", "justifyContent": "flex-start", "height": "100%"}, "props": {"className": "optimizely-swap-positions"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"mobile": {"margin": "1rem auto 0", "width": "99%"}, "meta": {"lazy": true}, "desktop": {"width": "85%", "boxSizing": "border-box", "margin": "0 auto 1%", "paddingLeft": "50px", "paddingRight": "50px", "textAlign": "center"}}, "data": {"source": "c<PERSON>a", "placeholderStyles": {"width": "100%", "height": "350px", "overflow": "hidden"}, "layout": "carousel", "numberOfProduct": 20, "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "showMarketingFlag": true, "customBrand": "ON", "scheme": "onhome1_rr", "displayTitle": true, "certonaTitle": {"title": "We picked these just for you...", "style": {"desktop": {"display": "flex", "color": "#000000", "paddingTop": "20px", "paddingBottom": "30px", "fontSize": "calc(14px + (44 - 16) * ((100vw - 768px) / (3000 - 768)))", "fontWeight": "700", "fontFamily": "'ON Sans Text', 'Open Sans', 'Gap Sans', Helvetica, Arial, 'Roboto', sans-serif", "textAlign": "center", "textTransform": "uppercase", "justifyContent": "center", "letterSpacing": "2px"}, "mobile": {"display": "flex", "paddingTop": "0px", "paddingBottom": "10px", "fontSize": "4.5vw", "fontWeight": "700", "fontFamily": "'ON Sans Text', 'Open Sans', 'Gap Sans', Helvetica, Arial, 'Roboto', sans-serif", "color": "#000000", "textAlign": "center", "textTransform": "uppercase", "justifyContent": "center", "letterSpacing": "1px"}}}, "defaultslidesToShowSlick": 4, "defaultslidesToScrollSlick": 4, "resslidesToShowSlick": 4, "resslidesToScrollSlick": 4, "responsive": [{"breakpoint": 320, "settings": {"slidesToScroll": 1}}], "mobileSmoothScroll": true, "prevArrowSlick": "/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png", "nextArrowSlick": "/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png", "prevArrowAlt": "previous", "nextArrowAlt": "next", "arrowPosition": "1.5%", "arrowVerticalPosition": "-10%", "productTextStyles": {"productTitle": {"style": {"textAlign": "left", "color": "auto", "fontSize": ".75rem", "paddingRight": "1rem"}}, "productMarketingFlag": {"style": {"fontWeight": "bold", "fontSize": ".75rem", "float": "left", "textAlign": "left"}}, "productPrice": {"style": {"fontSize": ".75rem", "color": "auto", "float": "left"}}, "productSalePrice": {"style": {"fontSize": ".75rem", "color": "red", "float": "left"}}, "size": {"width": "auto"}}, "productCardStyles": {"style": {"width": "auto", "marginBottom": "8px", "textAlign": "center"}}, "gridLayout": {"style": {"desktop": {"display": "flex", "flex-flow": "row wrap"}, "mobile": {}}, "productsPerRow": {"desktop": 4, "mobile": 2}}}}, {"name": "ComposableButton", "meta": {"lazy": true}, "tileStyle": {"mobile": {"display": "block", "width": "90%", "margin": "0 auto .5rem", "textAlign": "center", "& a": {"backgroundColor": "#FFFFFF", "width": "100%", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.6875em 0.8em", "fontSize": "0.9rem", "letterSpacing": "normal"}}, "desktop": {"display": "none"}}, "data": {"linkProps": {"href": "/browse/info.do?cid=1113546&mlink=5151,1,<PERSON>_<PERSON><PERSON><PERSON>_More"}, "borderThickness": "medium", "bright": false, "capitalization": "uppercase", "className": "", "color": "primary", "crossBrand": false, "font": "secondary", "fullWidth": false, "size": "large", "variant": "border", "roundedCorner": "true", "buttonText": "Shop More Picks"}}]}}}}, {"name": "div", "type": "builtin", "instanceName": "new-arrivals-ppof-LCM-new", "meta": {"lazy": true}, "data": {"defaultHeight": {"small": "408px", "large": "363px"}, "style": {"margin": "0 auto 1rem", "position": "relative", "& > div:nth-child(1)": {"boxSizing": "border-box", "margin": "0 auto", "paddingLeft": "0.5rem", "paddingRight": "0.5rem", "maxWidth": "100%", "width": "100%", "minHeight": "355px"}, "& > div:nth-child(1) h2": {"display": "flex", "paddingBottom": "20px", "fontSize": "4.5vw", "fontWeight": "700", "textAlign": "center", "textTransform": "uppercase", "justifyContent": "center", "letterSpacing": "1.5px"}}, "desktopStyle": {"maxWidth": "1400px", "marginBottom": "1%", "& > div:nth-child(1)": {"width": "85%", "boxSizing": "border-box", "margin": "0 auto", "paddingLeft": "50px", "paddingRight": "50px", "textAlign": "center", "minHeight": "300px"}, "& > div:nth-child(1) h2": {"fontSize": "min(calc(14px + 28 * ((100vw - 768px) / 2232)), 22px)", "display": "flex", "fontWeight": "700", "textAlign": "center", "textTransform": "uppercase", "justifyContent": "center", "letterSpacing": "1.5px", "margin": "0 0 6%", "@media (min-width: 768px) and (max-width: 1023px)": {"margin": "0 auto 8%"}, "padding": "0"}}, "components": [{"instanceName": "na-ppof", "name": "Recommendations", "type": "home", "data": {"source": "productCategory", "apiKey": "{{API_VALUE}}", "requestUrl": "https://api.gap.com/ux/web/productdiscovery-web-experience/products/us/on?locale=en_US&cid=", "useDivPref": true, "cid": "10018", "layout": "carousel", "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "showMarketingFlag": true, "customBrand": "ON", "scheme": "", "displayTitle": true, "certonaTitle": {"title": "Even more new arrivals!", "style": {"desktop": {}, "mobile": {}}}, "defaultslidesToShowSlick": 4, "defaultslidesToScrollSlick": 4, "resslidesToShowSlick": 4, "resslidesToScrollSlick": 4, "responsive": [{"breakpoint": 320, "settings": {"slidesToScroll": 1}}], "mobileSmoothScroll": true, "prevArrowSlick": "/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png", "nextArrowSlick": "/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png", "prevArrowAlt": "previous", "nextArrowAlt": "next", "arrowPosition": "1.5%", "arrowVerticalPosition": "-10%", "productTextStyles": {"productTitle": {"style": {"fontSize": ".75rem", "textAlign": "left", "paddingRight": "0rem"}}, "productMarketingFlag": {"style": {"fontWeight": "bold", "fontSize": ".75rem", "float": "left", "textAlign": "left"}}, "productPrice": {"style": {"float": "left", "fontSize": ".75rem"}}, "productSalePrice": {"style": {"color": "red", "float": "left", "fontSize": ".75rem"}}, "size": {"width": "auto"}}, "productCardStyles": {"style": {"width": "auto", "marginBottom": "8px", "textAlign": "center"}}, "gridLayout": {"style": {"desktop": {"display": "flex", "flex-flow": "row wrap"}, "mobile": {}}, "productsPerRow": {"desktop": 4, "mobile": 2}}, "productsPerRow": {"desktop": 4, "mobile": 2}}}, {"instanceName": "exposed-ctas-desk", "name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"desktopStyle": {"position": "absolute", "left": "50%", "transform": "translate(-50%, -50%)", "width": "98%", "maxWidth": "1372px", "top": "11%", "@media (min-width: 768px) and (max-width: 1279px)": {"top": "9.5%"}}}, "ctaList": {"style": {"display": "none"}, "desktopStyle": {"display": "flex", "padding": "0", "justifyContent": "space-evenly", "flexWrap": "nowrap", "a": {"width": "auto", "height": "auto", "backgroundColor": "transparent", "textDecoration": "underline", "borderRadius": "0px", "border": "none", "color": "#000000", "textAlign": "center", "letterSpacing": "0.5px", "fontWeight": "700", "fontSize": "calc(11px + (24 - 13) * ((100vw - 768px) / (3000 - 768)))", "@media (min-width: 1372px)": {"fontSize": "14px"}, "padding": "0"}}, "container": {"style": {"position": "relative"}}, "ctas": [{"composableButtonData": {"children": "Women & Women's Plus"}, "linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,HP_NA", "title": "Women & Women's Plus"}}, {"composableButtonData": {"children": "Men"}, "linkData": {"to": "/browse/category.do?cid=11174&mlink=5151,1,HP_NA", "title": "Men"}}, {"composableButtonData": {"children": "Girls"}, "linkData": {"to": "/browse/category.do?cid=6036&mlink=5151,1,HP_NA", "title": "Girls"}}, {"composableButtonData": {"children": "Boys"}, "linkData": {"to": "/browse/category.do?cid=5918&mlink=5151,1,HP_NA", "title": "Boys"}}, {"composableButtonData": {"children": "Toddler Girls"}, "linkData": {"to": "/browse/category.do?cid=6825#pageId=0&department=165&mlink=5151,1,HP_NA", "title": "Toddler Girls"}}, {"composableButtonData": {"children": "Toddler Boys"}, "linkData": {"to": "/browse/category.do?cid=6157#pageId=0&department=166&mlink=5151,1,HP_NA", "title": "Toddler Boys"}}, {"composableButtonData": {"children": "Baby Girls"}, "linkData": {"to": "/browse/category.do?cid=37505#pageId=0&department=165&mlink=5151,1,HP_NA", "title": "Baby Girls"}}, {"composableButtonData": {"children": "Baby Boys"}, "linkData": {"to": "/browse/category.do?cid=37508#pageId=0&department=166&mlink=5151,1,HP_NA", "title": "Baby Boys"}}, {"composableButtonData": {"children": "Maternity"}, "linkData": {"to": "/browse/category.do?cid=8454&mlink=5151,1,HP_NA", "title": "Maternity"}}]}}}, {"instanceName": "shop-now-cta-mobile", "name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}, "desktopStyle": {"display": "none"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"margin": "0 auto 1rem", "justifyContent": "center", "alignItems": "center", "textAlign": "center", "& > a": {"fontSize": "0.9rem", "height": "48px", "letterSpacing": "normal", "backgroundColor": "#FFFFFF", "width": "90%", "margin": "0 auto 7.5px", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764"}, "& > div": {"width": "90%", "margin": "0 auto 7.5px"}, "& button": {"padding": "0.625rem 0", "fontSize": "0.9rem", "width": "100%", "margin": "0"}, "@media (max-width: 767px)": {"& > a": {"height": "44px"}}}, "desktopStyle": {"position": "absolute", "top": "0", "maxWidth": "1400px", "left": "50%", "transform": "translate(-50%, 0)", "zIndex": "33", "margin": "0 auto", "& > a": {"backgroundColor": "#FFFFFF", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem 0.5rem", "whiteSpace": "normal", "lineHeight": "1", "width": "378px", "height": "48px", "margin": "0"}, "& > div": {"width": "100%", "margin": "0"}, "& button": {"width": "378px", "height": "48px"}}, "className": "", "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop Now"}, "submenu": [{"href": "/browse/category.do?cid=10018&mlink=5151,1,HP_NA", "trackingId": "HP_NA_W", "text": "Women & Women's Plus"}, {"href": "/browse/category.do?cid=11174&mlink=5151,1,HP_NA", "trackingId": "HP_NA_M", "text": "Men"}, {"href": "/browse/category.do?cid=6036&mlink=5151,1,HP_NA", "trackingId": "HP_NA_G", "text": "Girls"}, {"href": "/browse/category.do?cid=5918&mlink=5151,1,HP_NA", "trackingId": "HP_NA_B", "text": "Boys"}, {"href": "/browse/category.do?cid=6825&#pageId=0&department=165&mlink=5151,1,HP_NA", "trackingId": "HP_NA_G", "text": "Toddler Girls"}, {"href": "/browse/category.do?cid=6157&#pageId=0&department=166&mlink=5151,1,HP_NA", "trackingId": "HP_NA_TB", "text": "Toddler Boys"}, {"href": "/browse/category.do?cid=37505&#pageId=0&department=165&mlink=5151,1,HP_NA", "trackingId": "HP_NA_G", "text": "Baby Girls"}, {"href": "/browse/category.do?cid=37508&#pageId=0&department=166&mlink=5151,1,HP_NA", "trackingId": "HP_NA_BB", "text": "Baby Boys"}, {"href": "/browse/category.do?cid=8454&mlink=5151,1,HP_NA", "trackingId": "HP_NA_W", "text": "Maternity"}], "style": {"desktop": {"backgroundColor": "#FFFFFF", "margin": "0rem auto", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem 0.5rem"}, "mobile": {"backgroundColor": "#FFFFFF", "width": "90%", "margin": "0 auto 1rem", "padding": "0.<PERSON><PERSON> 0", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "fontSize": ".9em !important"}}}}]}}}]}}, {"instanceName": "sustainability-desktop-mobile-frame-animation", "name": "div", "type": "builtin", "meta": {"lazy": true}, "data": {"defaultHeight": {"small": "591px", "large": "423px"}, "style": {"maxWidth": "1400px", "margin": "2% auto 0 auto"}, "components": [{"name": "Carousel", "type": "sitewide", "data": {"carouselOptions": {"slidesToShow": 1, "slidesToScroll": 1, "autoplay": true, "speed": 3, "autoplaySpeed": 3000, "fade": true, "displayPlayPauseBtn": true, "dots": false, "dotsClass": "slick-dots", "infinite": true, "displayArrows": {"desktop": false, "mobile": false}, "pauseOnHover": false, "prevArrowUrl": "", "nextArrowUrl": ""}, "buttonSetting": {"buttonStyle": {"position": "absolute", "bottom": "auto", "top": "2%", "right": "2%", "left": "auto", "transform": "translate(0,0)", "height": "2em", "width": "2em", "zIndex": "0", "@media (min-width: 768px)": {"top": "3%", "bottom": "auto%", "right": "1%", "left": "auto"}}}, "style": {"margin": "0 auto", "position": "relative"}, "components": [{"instanceName": "frame1", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1185566&mlink=5151,1,HP_Imagine_Mission_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/860/261/assets/220300_10-M7539_Sustainability_F1_IM_HP2nd_US_SM.jpg?v=1", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/860/261/assets/220300_10-M7539_Sustainability_F1_IM_HP2nd_US_XL.jpg?v=1", "alt": "A female model stands beneath a clear blue sky with fluffy clouds and wearing a white dress.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/860/261/assets/220300_10-M7539_Sustainability_IM_HP2nd_US_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/860/261/assets/220300_10-M7539_Sustainability_IM_HP2nd_US_XL.svg", "alt": "Smile. We're going sustainable."}, "container": {"style": {"position": "relative"}}}}, {"instanceName": "frame2", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1185566&mlink=5151,1,HP_Imagine_Mission_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/860/261/assets/220300_10-M7539_Sustainability_F2_IM_HP2nd_US_SM.jpg?v=1", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/860/261/assets/220300_10-M7539_Sustainability_F2_IM_HP2nd_US_XL.jpg?v=1", "alt": "Image displays three washed jeans.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/860/261/assets/220300_10-M7539_Sustainability_IM_HP2nd_US_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/860/261/assets/220300_10-M7539_Sustainability_IM_HP2nd_US_XL.svg", "alt": "85% of the fibers used in our clothing will be more sustainable by 2025."}, "container": {"style": {"position": "relative"}}}}, {"instanceName": "frame3", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1185566&mlink=5151,1,HP_Imagine_Mission_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/860/261/assets/220300_10-M7539_Sustainability_F3_IM_HP2nd_US_SM.jpg?v=1", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/860/261/assets/220300_10-M7539_Sustainability_F3_IM_HP2nd_US_XL.jpg?v=1", "alt": "A male model jumping beneath a clear blue sky with fluffy clouds and wearing a green t-shirts and jean.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0028/860/261/assets/220300_10-M7539_Sustainability_IM_HP2nd_US_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0028/860/261/assets/220300_10-M7539_Sustainability_IM_HP2nd_US_XL.svg", "alt": "Imagine a greener future."}, "container": {"style": {"position": "relative"}}}}]}}, {"instanceName": "two-ctas", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"srcUrl": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='767' height='102'><rect width='100%' height='100%' style='fill:transparent'/></svg>", "desktopSrcUrl": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='1400' height='48'><rect width='100%' height='100%' style='fill:transparent'/></svg>", "alt": " ", "style": {"display": "block"}}, "style": {"display": "none"}, "desktopStyle": {"display": "block", "height": "48px", "marginBottom": "2.875%"}}, "container": {"style": {"position": "relative", "marginBottom": "4%"}, "desktopStyle": {"margin": "1% auto", "maxWidth": "1400px"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"margin": "0 auto", "justifyContent": "center", "alignItems": "center", "textAlign": "center", "& > a": {"fontSize": "0.9rem", "height": "44px", "letterSpacing": "normal", "backgroundColor": "#FFFFFF", "width": "90%", "margin": "0 auto 7.5px", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764"}, "& > a:first-child": {"marginTop": "1rem"}, "& button": {"padding": "0.625rem 0", "fontSize": "0.9rem", "marginBottom": "2%"}}, "desktopStyle": {"position": "absolute", "top": "0", "display": "flex", "flexDirection": "row", "justifyContent": "space-between", "alignItems": "flex-start", "width": "60%", "maxWidth": "1400px", "left": "50%", "transform": "translate(-50%, 0)", "zIndex": "33", "margin": "0 auto", "& > a": {"backgroundColor": "#FFFFFF", "border": "solid 2px #003764", "height": "48px", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem 0.5rem", "whiteSpace": "normal", "lineHeight": "1", "width": "45%", "margin": "0"}, "& > a:first-child": {"marginTop": "0"}, "& > div": {"width": "45%", "margin": "0"}, "& button": {"height": "48px", "width": "100%", "marginBottom": "0"}, "& ul": {"minWidth": "100%", "width": "100%", "left": "50%", "transform": "translate(-50%, 0)"}, "@media (min-width: 768px) and (max-width: 1200px)": {"width": "80%"}}, "ctas": [{"composableButtonData": {"children": "Learn More"}, "linkData": {"to": "/browse/info.do?cid=1160383&mlink=5151,1,HP_Imagine_Mission_b"}}, {"composableButtonData": {"children": "Shop More Sustainable Styles"}, "linkData": {"to": "/browse/category.do?cid=1185566&mlink=5151,1,HP_Imagine_Mission_b"}}]}}}]}}, {"instanceName": "061421-convenience", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "meta": {"lazy": true}, "data": {"shouldWaitForOptimizely": false, "defaultHeight": {"small": "195px", "large": "151px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "1rem auto 0", "position": "relative"}, "components": [{"instanceName": "convenience-banner", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%", "maxWidth": "1400px", "margin": "0 auto"}, "mobile": {"width": "100%", "margin": "1rem auto 0"}}, "data": {"background": {"linkData": {"to": "/browse/info.do?cid=1156830&mlink=5151,1,HP_Convenience_a", "title": "Get it today with these fast and worry free options. Contactless curbside pickup, in-store pickup, shop from your phone & free returns and exchanges."}, "image": {"alt": "Get it today with these fast and worry free options. Contactless curbside pickup, in-store pickup, shop from your phone & free returns and exchanges.", "srcUrl": "/Asset_Archive/ONWeb/content/0027/841/824/assets/210614_NS-N1757_Convenience2021_HP_2nd_US_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0027/841/824/assets/210614_NS-N1757_Convenience2021_HP_2nd_US_XL.svg"}}, "container": {"style": {"position": "relative"}}}}]}}}}, {"instanceName": "011422-convenience-2-ctas", "name": "LayeredContentModule", "type": "sitewide", "data": {"defaultHeight": {"small": "102px", "large": "48px"}, "background": {"image": {"srcUrl": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='767' height='102'><rect width='100%' height='100%' style='fill:transparent'/></svg>", "desktopSrcUrl": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='1400' height='48'><rect width='100%' height='100%' style='fill:transparent'/></svg>", "alt": " ", "style": {"display": "block"}}, "style": {"display": "none"}, "desktopStyle": {"display": "block", "height": "48px", "marginBottom": "2.875%"}}, "container": {"style": {"position": "relative", "marginBottom": "4%"}, "desktopStyle": {"margin": "1% auto", "maxWidth": "1400px"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"margin": "0 auto", "justifyContent": "center", "alignItems": "center", "textAlign": "center", "& > a": {"fontSize": "0.9rem", "height": "44px", "letterSpacing": "normal", "backgroundColor": "#FFFFFF", "width": "90%", "margin": "0 auto 7.5px", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764"}, "& > a:first-child": {"marginTop": "1rem"}, "& button": {"padding": "0.625rem 0", "fontSize": "0.9rem", "marginBottom": "2%"}}, "desktopStyle": {"position": "absolute", "top": "0", "display": "flex", "flexDirection": "row", "justifyContent": "space-between", "alignItems": "flex-start", "width": "60%", "maxWidth": "1400px", "left": "50%", "transform": "translate(-50%, 0)", "zIndex": "33", "margin": "0 auto", "& > a": {"backgroundColor": "#FFFFFF", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem 0.5rem", "whiteSpace": "normal", "lineHeight": "1", "width": "45%", "height": "48px", "margin": "0"}, "& > a:first-child": {"marginTop": "0"}, "& > div": {"width": "45%", "margin": "0"}, "& button": {"height": "48px", "width": "100%", "marginBottom": "0"}, "& ul": {"minWidth": "100%", "width": "100%", "left": "50%", "transform": "translate(-50%, 0)"}, "@media (min-width: 768px) and (max-width: 1200px)": {"width": "80%"}}, "ctas": [{"composableButtonData": {"children": "Get the app", "style": {}, "desktopStyle": {}}, "linkData": {"to": "https://oldnavy.onelink.me/1lBK/f56b84f2"}}, {"composableButtonData": {"children": "Start Shopping", "style": {}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,HP_Convenience_b"}}]}}}, {"instanceName": "040122-loyalty-banner", "name": "div", "type": "builtin", "experimentRunning": true, "meta": {"lazy": true}, "data": {"lazy": true, "shouldWaitForOptimizely": false, "defaultHeight": {"small": "383px", "large": "398px"}, "style": {"width": "100%", "display": "flex", "flexDirection": "row", "flexWrap": "nowrap", "justifyContent": "flex-start", "overflowX": "scroll", "padding": "2rem 0 3rem", "margin": "0 auto", "& > div": {"marginLeft": "0", "marginRight": "0", "width": "55%", "flex": "0 0 55%", "flexBasis": "55%", "paddingBottom": "0.5rem"}}, "desktopStyle": {"overflowX": "unset", "alignItems": "stretch", "justifyContent": "space-around", "maxWidth": "1400px", "padding": "0", "margin": "0 auto 2%", "& > div": {"flex": "0 0 30%", "flexBasis": "30%", "width": "30%", "marginBottom": "2rem"}}, "components": [{"instanceName": "third-loyalty", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/info.do?cid=82635&mlink-5151,1,<PERSON>_Email_Banner", "target": "_blank"}, "image": {"alt": "Want 20% off? Get it now when you sign up for our emails. In-store & online. This offer is for new subscribers only.", "srcUrl": "/Asset_Archive/ONWeb/content/0028/897/052/assets/220404_NS-N2833_EmailAcquistionTertiary_US.svg"}}, "container": {"style": {"position": "relative", "zIndex": "31", "paddingLeft": "1rem", "paddingRight": "1rem"}, "desktopStyle": {"paddingLeft": "0", "paddingRight": "0"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"zIndex": "-1", "position": "absolute", "top": "8%", "height": "100%", "width": "95%", "display": "flex", "justifyContent": "space-between", "alignItems": "flex-end"}, "desktopStyle": {"width": "100%"}, "className": "", "ctas": [{"composableButtonData": {"children": "Let's do this", "style": {"backgroundColor": "transparent", "textTransform": "uppercase", "color": "#000000", "fontWeight": "600", "textDecoration": "underline", "letterSpacing": "normal", "@media (max-width: 400px)": {"padding": "0 0.3em"}}}, "linkData": {"target": "_blank", "to": "/browse/info.do?cid=82635&mlink-5151,1,<PERSON>_Email_Banner", "title": "Let's do this"}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "Email Acquistion Details", "src": "/buy/promo_legal_details.do?promoId=825545", "height": "500px"}}, "composableButtonData": {"children": "*Details", "style": {"backgroundColor": "transparent", "textTransform": "none", "color": "#000000", "fontSize": "0.8rem", "fontWeight": "normal", "letterSpacing": "normal", "textDecoration": "underline", "position": "absolute", "bottom": "0", "right": "4%", "@media (max-width: 400px)": {"fontSize": "0.7rem"}}}}]}}}, {"instanceName": "third-imagine", "name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "Imagining a better future. Creating a better future for future generations.", "srcUrl": "/Asset_Archive/ONWeb/content/0028/178/159/assets/211025_NS-N2516_ImagineMission_HPTertiary_US.svg"}, "background": {"linkData": {"to": "/browse/info.do?cid=1160383&mlink=5151,1,HP_Imagine_Mission_a", "title": "Imagining a better future."}, "image": {"alt": "Imagining a better future.", "srcUrl": "/Asset_Archive/ONWeb/content/0028/178/159/assets/211025_NS-N2516_ImagineMission_HPTertiary_US.jpg"}}, "container": {"style": {"position": "relative", "zIndex": "31"}, "desktopStyle": {"paddingLeft": "0", "paddingRight": "0"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"zIndex": "-1", "position": "absolute", "top": "8%", "height": "100%", "width": "100%", "display": "flex", "justifyContent": "space-between", "alignItems": "flex-end"}, "className": "", "ctas": [{"composableButtonData": {"children": "Learn More", "style": {"backgroundColor": "transparent", "textTransform": "uppercase", "color": "#000000", "fontWeight": "600", "textDecoration": "underline", "@media (max-width: 400px)": {"padding": "0 0.3em"}}}, "linkData": {"to": "/browse/info.do?cid=1160383&mlink=5151,1,HP_Imagine_Mission_b", "title": "Imagining a better future. Creating a better future for future generations."}}]}}}, {"instanceName": "third-afterpay", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/customerService/info.do?cid=82725&cs=payment_options&mlink=5151,1,HP_Afterpay_a", "title": "New ways to pay for your shopping"}, "image": {"alt": "New ways to pay for your shopping", "srcUrl": "/Asset_Archive/ONWeb/content/0019/816/939/assets/210101_NS-1522_ExperienceSP-Evergreen_Afterpay_US_HP3rd.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0019/816/939/assets/210101_NS-1522_ExperienceSP-Evergreen_Afterpay_US_HP3rd.svg"}}, "container": {"style": {"position": "relative", "zIndex": "31", "paddingLeft": "1rem", "paddingRight": "1rem"}, "desktopStyle": {"paddingLeft": "0", "paddingRight": "0"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"zIndex": "-1", "position": "absolute", "top": "8%", "height": "100%", "width": "100%", "display": "flex", "justifyContent": "space-between", "alignItems": "flex-end"}, "className": "", "ctas": [{"composableButtonData": {"children": "Learn More", "style": {"backgroundColor": "transparent", "textTransform": "uppercase", "color": "#000000", "fontWeight": "600", "textDecoration": "underline", "@media (max-width: 400px)": {"padding": "0 0.3em"}}}, "linkData": {"to": "/customerService/info.do?cid=82725&cs=payment_options&mlink=5151,1,HP_Afterpay_b", "title": "Learn More about After Pay"}}]}}}]}}, {"instanceName": "mobile-divnav-new-022622", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "meta": {"lazy": true}, "data": {"lazy": true, "defaultHeight": {"small": "766px", "large": "0px"}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"type": "builtin", "name": "div", "data": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "1rem auto", "fontSize": "1.5em", "fontWeight": "700", "letterSpacing": "1px", "textTransform": "uppercase"}, "components": ["Shop By Department"]}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "justifyContent": "space-evenly", "marginBottom": "1em"}, "components": [{"instanceName": "women", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"flex": "0 0 30%", "flexBasis": "30%", "paddingBottom": "1rem"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,hp_mnav_b", "title": "Click to shop women and women's plus"}, "image": {"alt": "Shop women and women's plus", "srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220307_25_M6619_MobileDivNav_W_WP_HP_USCA.jpg", "desktopSrcUrl": ""}}}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0.25em auto 0 auto", "fontSize": ".85em", "letterSpacing": "normal", "fontWeight": "bold", "textTransform": "uppercase"}}, "components": ["WOMEN & WOMEN'S PLUS"]}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0 auto", "fontSize": ".8em", "letterSpacing": "normal", "color": "#666666", "textTransform": "uppercase"}}, "components": ["SIZES 0 - 30"]}}]}}}}, {"instanceName": "men", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"flex": "0 0 30%", "flexBasis": "30%", "paddingBottom": "1rem"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=11174&mlink=5151,1,hp_mnav_a", "title": "Click to shop men"}, "image": {"alt": "Shop men", "srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220307_25_M6619_MobileDivNav_M_HP_USCA.jpg", "desktopSrcUrl": ""}}}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0.25em auto 0 auto", "fontSize": ".85em", "letterSpacing": "normal", "fontWeight": "bold", "textTransform": "uppercase"}}, "components": ["MEN"]}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0 auto", "fontSize": ".8em", "letterSpacing": "normal", "color": "#666666", "textTransform": "uppercase"}}, "components": ["Sizes 28 - 48"]}}]}}}}, {"instanceName": "girls", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"flex": "0 0 30%", "flexBasis": "30%", "paddingBottom": "1rem"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=6036&mlink=5151,1,hp_mnav_e", "title": "Click to shop girls"}, "image": {"alt": "Shop girls", "srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220307_25_M6619_MobileDivNav_G_HP_USCA.jpg", "desktopSrcUrl": ""}}}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0.25em auto 0 auto", "fontSize": ".85em", "letterSpacing": "normal", "fontWeight": "bold", "textTransform": "uppercase"}}, "components": ["GIRLS"]}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0 auto", "fontSize": ".8em", "letterSpacing": "normal", "color": "#666666", "textTransform": "uppercase"}}, "components": ["Sizes 5 - 20"]}}]}}}}, {"instanceName": "boys", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"flex": "0 0 30%", "flexBasis": "30%", "paddingBottom": "1rem"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=5918&mlink=5151,1,hp_mnav_f", "title": "Click to shop boys"}, "image": {"alt": "Shop boys", "srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220307_25_M6619_MobileDivNav_B_HP_USCA.jpg", "desktopSrcUrl": ""}}}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0.25em auto 0 auto", "fontSize": ".85em", "letterSpacing": "normal", "fontWeight": "bold", "textTransform": "uppercase"}}, "components": ["BOYS"]}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0 auto", "fontSize": ".8em", "letterSpacing": "normal", "color": "#666666", "textTransform": "uppercase"}}, "components": ["Sizes 5 - 20"]}}]}}}}, {"instanceName": "toddler girls", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"flex": "0 0 30%", "flexBasis": "30%", "paddingBottom": "1rem"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=6825&#pageId=0&department=165&mlink=5151,1,hp_mnav_g", "title": "Click to shop toddler girls"}, "image": {"alt": "Shop toddler girls", "srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220307_25_M6619_MobileDivNav_TG_HP_USCA.jpg", "desktopSrcUrl": ""}}}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0.25em auto 0 auto", "fontSize": ".85em", "letterSpacing": "normal", "fontWeight": "bold", "textTransform": "uppercase"}}, "components": ["TODDLER GIRLS"]}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0 auto", "fontSize": ".8em", "letterSpacing": "normal", "color": "#666666", "textTransform": "uppercase"}}, "components": ["Sizes 12M - 6T"]}}]}}}}, {"instanceName": "toddler boys", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"flex": "0 0 30%", "flexBasis": "30%", "paddingBottom": "1rem"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=6157&#pageId=0&department=166&mlink=5151,1,hp_mnav_h", "title": "Click to shop toddler boys"}, "image": {"alt": "Shop toddler boys", "srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220307_25_M6619_MobileDivNav_TB_HP_USCA.jpg", "desktopSrcUrl": ""}}}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0.25em auto 0 auto", "fontSize": ".85em", "letterSpacing": "normal", "fontWeight": "bold", "textTransform": "uppercase"}}, "components": ["TODDLER BOYS"]}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0 auto", "fontSize": ".8em", "letterSpacing": "normal", "color": "#666666", "textTransform": "uppercase"}}, "components": ["Sizes 12M - 6T"]}}]}}}}, {"instanceName": "baby girls", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"flex": "0 0 30%", "flexBasis": "30%", "paddingBottom": "1rem"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=37505&#pageId=0&department=165&mlink=5151,1,hp_mnav_i", "title": "Click to shop baby girls"}, "image": {"alt": "Shop baby girls", "srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220307_25_M6619_MobileDivNav_BG_HP_USCA.jpg", "desktopSrcUrl": ""}}}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0.25em auto 0 auto", "fontSize": ".85em", "letterSpacing": "normal", "fontWeight": "bold", "textTransform": "uppercase"}}, "components": ["BABY GIRLS"]}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0 auto", "fontSize": ".8em", "letterSpacing": "normal", "color": "#666666", "textTransform": "uppercase"}}, "components": ["Sizes 0 - 24M"]}}]}}}}, {"instanceName": "baby boys", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"flex": "0 0 30%", "flexBasis": "30%", "paddingBottom": "1rem"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=37508&#pageId=0&department=166&mlink=5151,1,hp_mnav_j", "title": "Click to shop baby boys"}, "image": {"alt": "Shop baby boys", "srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220307_25_M6619_MobileDivNav_BB_HP_USCA.jpg", "desktopSrcUrl": ""}}}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0.25em auto 0 auto", "fontSize": ".85em", "letterSpacing": "normal", "fontWeight": "bold", "textTransform": "uppercase"}}, "components": ["BABY BOYS"]}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0 auto", "fontSize": ".8em", "letterSpacing": "normal", "color": "#666666", "textTransform": "uppercase"}}, "components": ["Sizes 0 - 24M"]}}]}}}}, {"instanceName": "maternity", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"flex": "0 0 30%", "flexBasis": "30%", "paddingBottom": "1rem"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=8454&mlink=5151,1,hp_mnav_d", "title": "Click to shop maternity"}, "image": {"alt": "Shop maternity", "srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220307_25_M6619_MobileDivNav_Mat_HP_USCA.jpg", "desktopSrcUrl": ""}}}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0.25em auto 0 auto", "fontSize": ".85em", "letterSpacing": "normal", "fontWeight": "bold", "textTransform": "uppercase"}}, "components": ["MATERNITY"]}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0 auto", "fontSize": ".8em", "letterSpacing": "normal", "color": "#666666", "textTransform": "uppercase"}}, "components": ["Sizes 1 - 18"]}}]}}}}, {"instanceName": "new-arrivals", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"flex": "0 0 30%", "flexBasis": "30%", "paddingBottom": "1rem"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,hp_mnav_k", "title": "Click to shop new arrivals"}, "image": {"alt": "Shop new arrivals", "srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220307_25_M6619_MobileDivNav_NA_HP_USCA.jpg", "desktopSrcUrl": ""}}}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0.25em auto 0 auto", "fontSize": ".85em", "letterSpacing": "normal", "fontWeight": "bold", "textTransform": "uppercase"}}, "components": ["New Arrivals"]}}]}}}}, {"instanceName": "clearance", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"flex": "0 0 30%", "flexBasis": "30%", "paddingBottom": "1rem"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=96964&mlink=5151,1,hp_mnav_l", "title": "Click to shop clearance"}, "image": {"alt": "Shop clearance", "srcUrl": "/Asset_Archive/ONWeb/content/0028/959/445/assets/220307_25_M6619_MobileDivNav_Clearance_HP_USCA.svg", "desktopSrcUrl": ""}}}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"whiteSpace": "normal", "textAlign": "center", "margin": "0.25em auto 0 auto", "fontSize": ".85em", "letterSpacing": "normal", "fontWeight": "bold", "textTransform": "uppercase"}}, "components": ["Clearance"]}}]}}}}]}}}}]}}}}, {"instanceName": "pixlee-component", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "399px", "large": "390px"}, "desktop": {"shouldDisplay": true, "data": {"style": {"width": "100%", "maxWidth": "1400px", "flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "margin": "0 auto 1rem", "zIndex": "111"}, "components": [{"name": "PixleeModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%", "maxWidth": "1400px", "margin": "0 auto"}}, "data": {"apiKey": "{{API_VALUE}}", "widgetId": "2637330", "iframeMinHeight": "405px", "containerStyles": {"width": "100%", "margin": "auto"}}}, {"instanceName": "pixlee-cta-desktop", "type": "builtin", "name": "a", "tileStyle": {"desktop": {"marginTop": "10px", "marginBottom": "1rem", "textAlign": "center", "zIndex": "2"}, "mobile": {}}, "data": {"style": {"fontSize": ".9em", "fontWeight": "700", "width": "auto", "height": "44px", "textTransform": "uppercase", "letterSpacing": "1px", "backgroundColor": "#FFFFFF", "margin": "0rem auto", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "whiteSpace": "normal", "padding": "0.719em 0.8em", "display": "inline-flex", "alignItems": "center", "boxSizing": "border-box"}, "desktopStyle": {"height": "48px"}, "props": {"href": "/browse/info.do?cid=1061549&mlink=5151,1,<PERSON>_Pixlee", "class": ""}, "components": ["Show More"]}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"width": "100%", "flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "margin": "0 auto 2rem", "zIndex": "111"}, "components": [{"name": "PixleeModule", "type": "sitewide", "data": {"apiKey": "{{API_VALUE}}", "widgetId": "2637330", "iframeMinHeight": "370px", "containerStyles": {"height": "340px", "width": "100%", "margin": "auto", "paddingTop": "10px"}}}, {"instanceName": "pixlee-subcopy-mobile", "type": "builtin", "name": "a", "tileStyle": {"mobile": {"margin": "0 auto", "width": "90%", "textAlign": "center", "zIndex": "2"}, "desktop": {}}, "data": {"props": {"href": "/browse/info.do?cid=1061549&mlink=5151,1,<PERSON>_Pixlee", "class": "", "style": {"fontWeight": "700", "width": "90%", "textTransform": "uppercase", "letterSpacing": "1px", "backgroundColor": "#FFFFFF", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "whiteSpace": "normal", "lineHeight": "1", "height": "44px", "boxSizing": "border-box", "fontSize": ".9rem", "padding": ".75rem 0.8rem", "display": "block", "margin": "0 auto"}}, "components": ["Show More"]}}]}}}}, {"instanceName": "ON HP Legal", "name": "TextHeadline", "type": "sitewide", "data": {"text": "Select styles only. While supplies last. For a limited time.\nOnline & in-store prices and exclusions may vary.", "defaultHeight": "34px", "style": {"mobile": {"whiteSpace": "inherit", "margin": "2em 1em", "fontSize": ".75rem", "textAlign": "center", "fontWeight": "400", "color": "#666666", "lineHeight": "1.38"}, "desktop": {"marginTop": "2rem", "fontSize": ".75rem", "textAlign": "center", "fontWeight": "400", "color": "#666666", "lineHeight": "1.38"}}, "className": {"mobile": "", "desktop": ""}}}, {"type": "sitewide", "name": "OptimizelyPlaceholder", "instanceName": "113021-email-popup", "experimentRunning": true, "data": {"defaultHeight": {"large": "0", "small": "0"}}}]}, "meta.title.override": "Clothes For Women, Men, Kids and Baby | Free Shipping on $50", "type": "meta", "brand": "on", "meta.description": "Oldnavy.com provides the latest fashions at great prices for the whole family. Shop Men's, Women's and Kids'; departments, Womens Plus, and clothing for baby and maternity wear. Also find big and tall sizes for adults and extended sizes for kids."}