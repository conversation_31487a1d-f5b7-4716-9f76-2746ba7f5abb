// @ts-nocheck
'use client'

import { WebHierarchy } from "../types";

const data: WebHierarchy = [
  {
    parents: ["46650"],
    type: "division",
    id: "46651",
    name: "www.gap.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=46651",
    hasSubDivision: false,
    brandCode: "1",
  },
  {
    parents: ["46650"],
    type: "division",
    id: "46652",
    name: "oldnavy.gap.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=46652",
    hasSubDivision: false,
    brandCode: "3",
  },
  {
    parents: ["46650"],
    type: "division",
    id: "46653",
    name: "bananarepublic.gap.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=46653",
    hasSubDivision: false,
    brandCode: "2",
  },
  {
    parents: ["46650"],
    type: "division",
    id: "46655",
    name: "athleta.gap.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=46655",
    hasSubDivision: false,
    brandCode: "10",
  },
  {
    parents: ["46650"],
    type: "division",
    id: "1059020",
    name: "New Arrivals",
    children: [
      {
        parents: ["1059020", "46650"],
        type: "trimheader",
        id: "1126243",
        name: "CATEGORIES",
        children: [
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1006482",
            name: "All New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1006482",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "46795",
            name: "Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46795",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1006605",
            name: "Swim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1006605",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "46752",
            name: "Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46752",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "46833",
            name: "Dresses & Rompers",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46833",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "60411",
            name: "Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=60411",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1059840",
            name: "Bras & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1059840",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "91992",
            name: "Shoes & Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=91992",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1095091",
            name: "Athleta Girl New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1095091",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1059020", "46650"],
        type: "trimheader",
        id: "1090431",
        name: "FEATURED SHOPS",
        children: [
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1127909",
            name: "Sustainable Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127909",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1124095",
            name: "Camo That Moves",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1124095",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1130441",
            name: "Latest In Linen",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1130441",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1125935",
            name: "Bike Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1125935",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1130442",
            name: "Top Mother's Day Gifts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1130442",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1124791",
            name: "May Catalog ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1124791",
            customUrl: "/browse/info.do?cid=1121088",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1071357",
            name: "Back In Stock",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1071357",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1008552",
            name: "Best Sellers",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1008552",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1039658",
            name: "Looks We Love",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1039658",
            customUrl: "/browse/info.do?cid=1103988",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1107873",
            name: "Maternity",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1107873",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1093212",
            name: "Winter Travel Essentials",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1093212",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1094397",
            name: "Email Exclusive",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1094397",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1094903",
            name: "Email Exclusive",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1094903",
            hasSubDivision: false,
          },
          {
            parents: ["1059020", "46650"],
            type: "category",
            id: "1111161",
            name: "Plus HIDDEN",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1111161",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1059020",
    hasSubDivision: false,
  },
  {
    parents: ["46650"],
    type: "division",
    id: "46692",
    name: "Activity",
    children: [
      {
        parents: ["46692", "46650"],
        type: "trimheader",
        id: "1060178",
        name: "HOW WE INNOVATE",
        children: [
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "1125067",
            name: "Powervita",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1125067",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "1125066",
            name: "Powerlift",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1125066",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "1124154",
            name: "Featherweight Stretch",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1124154",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "1125588",
            name: "Swiftlite",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1125588",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "1129702",
            name: "Nirvana",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1129702",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "1117779",
            name: "Seamless Design",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1117779",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "1120562",
            name: "Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1120562",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["46692", "46650"],
        type: "trimheader",
        id: "1060194",
        name: "ACTIVITIES",
        children: [
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "1112011",
            name: "All Activities",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1112011",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "46715",
            name: "Hike",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46715",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "46711",
            name: "Run & Train",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46711",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "46693",
            name: "Yoga & Studio",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46693",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "1022034",
            name: "Work, Commute & Travel",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1022034",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "46730",
            name: "Tennis/Golf",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46730",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "1035426",
            name: "Water Sports",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1035426",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "1131480",
            name: "UPF 50+ Sun Protection",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1131480",
            hasSubDivision: false,
          },
          {
            parents: ["46692", "46650"],
            type: "category",
            id: "1130630",
            name: "Workout Essentials",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1130630",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=46692",
    hasSubDivision: false,
  },
  {
    parents: ["46650"],
    type: "division",
    id: "46793",
    name: "Bottoms",
    children: [
      {
        parents: ["46793", "46650"],
        type: "trimheader",
        id: "1060184",
        name: "CATEGORIES",
        children: [
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "1025878",
            name: "All Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1025878",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "1059481",
            name: "Tights",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1059481",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "1059471",
            name: "Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1059471",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "1108900",
            name: "Denim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1108900",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "1046322",
            name: "Joggers & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1046322",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "1058485",
            name: "Capris",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1058485",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "1059479",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1059479",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "1132157",
            name: "Select Skorts: $15 Off",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1132157",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "86354",
            name: "Skorts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=86354",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "1067743",
            name: "Pant Finder",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1067743",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "1089437",
            name: "Most Loved Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1089437",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "1125943",
            name: "City Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1125943",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "spacer",
            id: "1073608",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1073608",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "1128953",
            name: "NEW MARKDOWNS",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1128953",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["46793", "46650"],
        type: "trimheader",
        id: "1060705",
        name: "SPECIAL SIZES",
        children: [
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "46814",
            name: "Plus",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46814",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "46812",
            name: "Petite",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46812",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "46813",
            name: "Tall",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46813",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["46793", "46650"],
        type: "trimheader",
        id: "1060784529812347",
        name: "FANCY PANTS",
        children: [
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "123456",
            name: "Plus",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46814",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "3456789",
            name: "Petite",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46812",
            hasSubDivision: false,
          },
          {
            parents: ["46793", "46650"],
            type: "category",
            id: "9876543",
            name: "Tall",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46813",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=46793",
    hasSubDivision: false,
  },
  {
    parents: ["46650"],
    type: "division",
    id: "46750",
    name: "Tops ",
    children: [
      {
        parents: ["46750", "46650"],
        type: "trimheader",
        id: "1060187",
        name: "CATEGORIES",
        children: [
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1032080",
            name: "All Tops ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1032080",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1011678",
            name: "Tanks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1011678",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1011679",
            name: "Short Sleeve",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1011679",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1011680",
            name: "Long Sleeve",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1011680",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1066282",
            name: "Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1066282",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1005761",
            name: "Sweatshirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1005761",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1090333",
            name: "Wraps",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1090333",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "84117",
            name: "Sweaters  ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=84117",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1009710",
            name: "Support Tops",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1009710",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1047787",
            name: "Most Loved Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1047787",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1070197",
            name: "CYA: Longer Length Tops",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1070197",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "spacer",
            id: "46776",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/categorySearch.do?cid=46776",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1128967",
            name: "NEW MARKDOWNS",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1128967",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["46750", "46650"],
        type: "trimheader",
        id: "1060704",
        name: "SPECIAL SIZES",
        children: [
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1108862",
            name: "Plus",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1108862",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "1030569",
            name: "Petite",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1030569",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "46774",
            name: "Tall",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46774",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["46750", "46650"],
        type: "trimheader",
        id: "FANCY TOPS ID",
        name: "FANCY TOPS",
        children: [
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "FANCY TOPS PLUS ID",
            name: "Plus",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1108862",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "FANCY TOPS PETITE ID",
            name: "Petite",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1030569",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "FANCY TOPS TALL ID",
            name: "Tall",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46774",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["46750", "46650"],
        type: "trimheader",
        id: "MORE TOPS ID",
        name: "MORE TOPS WITH A REALLY LONG NAME BECAUSE OF REASONS",
        children: [
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "MORE TOPS PLUS ID",
            name: "Plus",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1108862",
            hasSubDivision: false,
          },
          {
            parents: ["46750", "46650"],
            type: "category",
            id: "MORE TOPS PETITE ID",
            name: "Petite",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1030569",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=46750",
    hasSubDivision: false,
  },
  {
    parents: ["46650"],
    type: "division",
    id: "1111334",
    name: "Bras & Underwear",
    children: [
      {
        parents: ["1111334", "46650"],
        type: "trimheader",
        id: "1111335",
        name: "CATEGORIES",
        children: [
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1111336",
            name: "All Bras & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1111336",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1038916",
            name: "Sports Bras",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1038916",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1111339",
            name: "A-C Bras",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1111339",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1111340",
            name: "D-DD Bras",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1111340",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1116424",
            name: "Post-Mastectomy Friendly",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1116424",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1107874",
            name: "Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1107874",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "spacer",
            id: "1128978",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1128978",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1128972",
            name: "NEW MARKDOWNS",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1128972",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1111334", "46650"],
        type: "trimheader",
        id: "NEW STUFF ID",
        name: "NEW STUFF",
        children: [
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "FIRST NEW THING ID",
            name: "FIRST NEW THING",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1111336",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1128972",
            name: "NEW MARKDOWNS",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1128972",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1111334", "46650"],
        type: "trimheader",
        id: "MORE NEW STUFF ID",
        name: "MORE NEW STUFF",
        children: [
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1111336",
            name: "All Bras & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1111336",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "spacer",
            id: "1128978",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1128978",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1128972",
            name: "NEW MARKDOWNS",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1128972",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1111334", "46650"],
        type: "trimheader",
        id: "EVEN MORE NEW STUFF ID",
        name: "EVEN MORE NEW STUFF",
        children: [
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1111336",
            name: "All Bras & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1111336",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "spacer",
            id: "1128978",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1128978",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1128972",
            name: "NEW MARKDOWNS",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1128972",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1111334", "46650"],
        type: "trimheader",
        id: "SPORTS ID",
        name: "SPORTS",
        children: [
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1111336",
            name: "All Bras & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1111336",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "spacer",
            id: "1128978",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1128978",
            hasSubDivision: false,
          },
          {
            parents: ["1111334", "46650"],
            type: "category",
            id: "1128972",
            name: "NEW MARKDOWNS",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1128972",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1111334",
    hasSubDivision: false,
  },
  {
    parents: ["46650"],
    type: "division",
    id: "60402",
    name: "Jackets ",
    children: [
      {
        parents: ["60402", "46650"],
        type: "trimheader",
        id: "1063663",
        name: "CATEGORIES",
        children: [
          {
            parents: ["60402", "46650"],
            type: "category",
            id: "1017102",
            name: "All Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1017102",
            hasSubDivision: false,
          },
          {
            parents: ["60402", "46650"],
            type: "category",
            id: "1081043",
            name: "UPF 50+ Sun Protection",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1081043",
            hasSubDivision: false,
          },
          {
            parents: ["60402", "46650"],
            type: "category",
            id: "1009206",
            name: "Lightweight",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1009206",
            hasSubDivision: false,
          },
          {
            parents: ["60402", "46650"],
            type: "category",
            id: "1017105",
            name: "Hoodies & Sweatshirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1017105",
            hasSubDivision: false,
          },
          {
            parents: ["60402", "46650"],
            type: "category",
            id: "1064721",
            name: "Rain",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1064721",
            hasSubDivision: false,
          },
          {
            parents: ["60402", "46650"],
            type: "category",
            id: "1001223",
            name: "Insulated & Down",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1001223",
            hasSubDivision: false,
          },
          {
            parents: ["60402", "46650"],
            type: "spacer",
            id: "1074957",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1074957",
            hasSubDivision: false,
          },
          {
            parents: ["60402", "46650"],
            type: "category",
            id: "1128982",
            name: "NEW MARKDOWNS",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1128982",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["60402", "46650"],
        type: "trimheader",
        id: "1063664",
        name: "SPECIAL SIZES",
        children: [
          {
            parents: ["60402", "46650"],
            type: "category",
            id: "1108863",
            name: "Plus",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1108863",
            hasSubDivision: false,
          },
          {
            parents: ["60402", "46650"],
            type: "category",
            id: "1067236",
            name: "Petite",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1067236",
            hasSubDivision: false,
          },
          {
            parents: ["60402", "46650"],
            type: "category",
            id: "1113992",
            name: "Tall",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1113992",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=60402",
    hasSubDivision: false,
  },
  {
    parents: ["46650"],
    type: "division",
    id: "46831",
    name: "Dresses & Rompers",
    children: [
      {
        parents: ["46831", "46650"],
        type: "trimheader",
        id: "1060173",
        name: "CATEGORIES",
        children: [
          {
            parents: ["46831", "46650"],
            type: "category",
            id: "89745",
            name: "All Dresses & Rompers",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=89745",
            hasSubDivision: false,
          },
          {
            parents: ["46831", "46650"],
            type: "category",
            id: "1009207",
            name: "T-Shirt Dresses",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1009207",
            hasSubDivision: false,
          },
          {
            parents: ["46831", "46650"],
            type: "category",
            id: "1059769",
            name: "Rompers",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1059769",
            hasSubDivision: false,
          },
          {
            parents: ["46831", "46650"],
            type: "category",
            id: "1096392",
            name: "Woven Dresses",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1096392",
            hasSubDivision: false,
          },
          {
            parents: ["46831", "46650"],
            type: "category",
            id: "1099194",
            name: "Swim Dresses & Cover-Ups",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1099194",
            hasSubDivision: false,
          },
          {
            parents: ["46831", "46650"],
            type: "category",
            id: "1087045",
            name: "Skorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1087045",
            hasSubDivision: false,
          },
          {
            parents: ["46831", "46650"],
            type: "spacer",
            id: "46850",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/categorySearch.do?cid=46850",
            hasSubDivision: false,
          },
          {
            parents: ["46831", "46650"],
            type: "category",
            id: "1128987",
            name: "NEW MARKDOWNS",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1128987",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["46831", "46650"],
        type: "trimheader",
        id: "1060710",
        name: "SPECIAL SIZES",
        children: [
          {
            parents: ["46831", "46650"],
            type: "category",
            id: "1108864",
            name: "Plus",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1108864",
            hasSubDivision: false,
          },
          {
            parents: ["46831", "46650"],
            type: "category",
            id: "46847",
            name: "Petite",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46847",
            hasSubDivision: false,
          },
          {
            parents: ["46831", "46650"],
            type: "category",
            id: "46848",
            name: "Tall",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46848",
            hasSubDivision: false,
          },
          {
            parents: ["46831", "46650"],
            type: "categorysearch",
            id: "1003173",
            name: "Test Size Category",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/categorySearch.do?cid=1003173",
            hasSubDivision: false,
          },
          {
            parents: ["46831", "46650"],
            type: "category",
            id: "1026332",
            name: "TESTING - autopop",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1026332",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=46831",
    hasSubDivision: false,
  },
  {
    parents: ["46650"],
    type: "division",
    id: "46656",
    name: "Swim",
    children: [
      {
        parents: ["46656", "46650"],
        type: "trimheader",
        id: "1130421",
        name: "CATEGORIES",
        children: [
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1124384",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1124384",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1031353",
            name: "All Swim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1031353",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1076564",
            name: "Tankini Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1076564",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1026473",
            name: "Bikini Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1026473",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1026482",
            name: "Swim Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1026482",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "97463",
            name: "One Pieces",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=97463",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "93635",
            name: "Bra-Sized Swim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=93635",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1099237",
            name: "Swim Shorts & Skirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1099237",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "97464",
            name: "UPF Rashguards & Cover-ups",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=97464",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1122175",
            name: "Resort",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1122175",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1130415",
            name: "Rib Swim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1130415",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1126107",
            name: "Aqualuxe",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1126107",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1100202",
            name: "Shop By Print",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1100202",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1099512",
            name: "Swim",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1099512",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1110453",
            name: "NEW MARKDOWNS",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1110453",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1132781",
            name: "Swim Quiz",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1132781",
            customUrl: "/browse/info.do?cid=1127248",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["46656", "46650"],
        type: "trimheader",
        id: "1110451",
        name: "SWIM: 40% OFF",
        children: [
          {
            parents: ["46656", "46650"],
            type: "category",
            id: "1110452",
            name: "All Swim Sale ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1110452",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "spacer",
            id: "1099676",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1099676",
            hasSubDivision: false,
          },
          {
            parents: ["46656", "46650"],
            type: "spacer",
            id: "46676",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/categorySearch.do?cid=46676",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=46656",
    hasSubDivision: false,
  },
  {
    parents: ["46650"],
    type: "division",
    id: "46866",
    name: "Shoes & Accessories",
    children: [
      {
        parents: ["46866", "46650"],
        type: "trimheader",
        id: "1060098",
        name: "SHOES",
        children: [
          {
            parents: ["46866", "46650"],
            type: "category",
            id: "1032089",
            name: "All Shoes",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1032089",
            hasSubDivision: false,
          },
          {
            parents: ["46866", "46650"],
            type: "category",
            id: "1015862",
            name: "Sandals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1015862",
            hasSubDivision: false,
          },
          {
            parents: ["46866", "46650"],
            type: "category",
            id: "1015838",
            name: "Sneakers",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1015838",
            hasSubDivision: false,
          },
          {
            parents: ["46866", "46650"],
            type: "category",
            id: "100023",
            name: "Boots",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=100023",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["46866", "46650"],
        type: "trimheader",
        id: "1060101",
        name: "ACCESSORIES",
        children: [
          {
            parents: ["46866", "46650"],
            type: "category",
            id: "1032096",
            name: "All Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1032096",
            hasSubDivision: false,
          },
          {
            parents: ["46866", "46650"],
            type: "category",
            id: "46881",
            name: "All Bags: 30% Off",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46881",
            hasSubDivision: false,
          },
          {
            parents: ["46866", "46650"],
            type: "category",
            id: "46887",
            name: "Socks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46887",
            hasSubDivision: false,
          },
          {
            parents: ["46866", "46650"],
            type: "category",
            id: "1013846",
            name: "Hats & Headbands",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1013846",
            hasSubDivision: false,
          },
          {
            parents: ["46866", "46650"],
            type: "category",
            id: "1071601",
            name: "Water Bottles",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1071601",
            hasSubDivision: false,
          },
          {
            parents: ["46866", "46650"],
            type: "category",
            id: "46883",
            name: "Yoga Gear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=46883",
            hasSubDivision: false,
          },
          {
            parents: ["46866", "46650"],
            type: "category",
            id: "1074899",
            name: "All Gloves",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1074899",
            hasSubDivision: false,
          },
          {
            parents: ["46866", "46650"],
            type: "spacer",
            id: "46890",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/categorySearch.do?cid=46890",
            hasSubDivision: false,
          },
          {
            parents: ["46866", "46650"],
            type: "category",
            id: "1128995",
            name: "NEW MARKDOWNS",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1128995",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=46866",
    hasSubDivision: false,
  },
  {
    parents: ["46650"],
    type: "division",
    id: "1054832",
    name: "Athleta Girl",
    children: [
      {
        parents: ["1054832", "46650"],
        type: "trimheader",
        id: "1060198",
        name: "CATEGORIES",
        children: [
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1067955",
            name: "All Girl",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1067955",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1054835",
            name: "Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1054835",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1055327",
            name: "Tops  ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1055327",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1082880",
            name: "Sweatshirts & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1082880",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1054836",
            name: "Swim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1054836",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1111614",
            name: "Bras",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1111614",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1054837",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1054837",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "sale",
            id: "1073226",
            name: "SALE",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1073226",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "spacer",
            id: "1075714",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1075714",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1054832", "46650"],
        type: "trimheader",
        id: "1060197",
        name: "FEATURED SHOPS",
        children: [
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1054844",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1054844",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1130135",
            name: "Summer Camp",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1130135",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1101428",
            name: "Shop By Sport",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1101428",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1104648",
            name: "Team Up: Women + Girls",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1104648",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1117578",
            name: "Best Sellers",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1117578",
            hasSubDivision: false,
          },
          {
            parents: ["1054832", "46650"],
            type: "category",
            id: "1114293",
            name: "New! Available in XXL",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1114293",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1054832",
    hasSubDivision: false,
  },
  {
    parents: ["46650"],
    type: "division",
    id: "49936",
    name: "Sale",
    children: [
      {
        parents: ["49936", "46650"],
        type: "trimheader",
        id: "1060189",
        name: "CATEGORIES",
        children: [
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "1023728",
            name: "All Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1023728",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "50000",
            name: "Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=50000",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "49982",
            name: "Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=49982",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "49994",
            name: "Jackets & Vests",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=49994",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "50004",
            name: "Dresses",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=50004",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "50014",
            name: "Bras & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=50014",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "1079822",
            name: "Swim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1079822",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "50010",
            name: "Shoes",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=50010",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "50012",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=50012",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "1073221",
            name: "Athleta Girl",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1073221",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["49936", "46650"],
        type: "trimheader",
        id: "1060188",
        name: "FEATURED SHOPS",
        children: [
          {
            parents: ["49936", "46650"],
            type: "category",
            id: "1026746",
            name: "New Markdowns",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1026746",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "category",
            id: "1065352",
            name: "Final Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1065352",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "category",
            id: "1085717",
            name: "Athleta Girl: New Markdowns",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1085717",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "category",
            id: "1099100",
            name: "Winter Layers: up to 50% off",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1099100",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "category",
            id: "1088188",
            name: "Warehouse Sale",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1088188",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["49936", "46650"],
        type: "header",
        id: "1029032",
        name: "SPECIAL SIZES",
        children: [
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "1029036",
            name: "Plus",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1029036",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "1029035",
            name: "Tall",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1029035",
            hasSubDivision: false,
          },
          {
            parents: ["49936", "46650"],
            type: "sale",
            id: "1029037",
            name: "Petite",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1029037",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=49936",
    hasSubDivision: false,
  },
  {
    parents: ["46650"],
    type: "division",
    id: "1074539",
    name: "Our Values",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1074539",
    customUrl: "/browse/info.do?cid=1074427",
    hasSubDivision: false,
  },
  {
    parents: ["46650"],
    type: "division",
    id: "1015650",
    name: "Staging",
    children: [
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1071308",
        name: "PROMOS",
        children: [
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1059894",
            name: "Our Favorite Deals",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1059894",
            customUrl: "Evergreen - Promo",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1109630",
            name: "Girls Dept Autopop",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1109630",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1108950",
            name: "20% Off Swim Sale Exclusions",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1108950",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1112579",
            name: "Category Group Top Test",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1112579",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1077334",
            name: "Ca$hmere",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1077334",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1096791",
            name: "Denim",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1096791",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1102549",
            name: "3rd Party Accessories",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1102549",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "spacer",
            id: "1104564",
            name: "-------------------------",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1104564",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1045813",
            name: "OUTFIT TEST",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1045813",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1067625",
            name: "Pantfinder Summer Practice",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1067625",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "spacer",
            id: "1029893",
            name: "Space",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/info.do?cid=1029893",
            hasSubDivision: false,
          },
        ],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1028830",
        name: "Categories",
        children: [
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1087913",
            name: "Co-Creation Collection",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1087913",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1060963",
            name: "AUTOPOP TEST",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1060963",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1129128",
            name: "Summer 1 New Arrivals-Julie",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1129128",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1131647",
            name: "All Bras",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1131647",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1081700",
            name: "Spring 2&3 Preview Links 2019",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1081700",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1127191",
            name: "Swim Quiz",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1127191",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1124070",
            name: "Summer 1 Preview Links",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1124070",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1065606",
            name: "Summer 4 - Diana",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1065606",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1108790",
            name: "Sweaters",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1108790",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1096437",
            name: "Mira SPR3 Swim ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1096437",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1099551",
            name: "Mira SUM1 Sorting",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1099551",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1077290",
            name: "Mira SUM1 Activity ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1077290",
            hasSubDivision: false,
          },
        ],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1075312",
        name: "Mira Sale",
        children: [
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1088268",
            name: "Mira",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1088268",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1068280",
            name: "Mira - Markdowns",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1068280",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1082545",
            name: "Final Sale TEST",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1082545",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1074666",
            name: "Diana's Playground",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1074666",
            hasSubDivision: false,
          },
        ],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1075299",
        name: "NEW ARRIVALS",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1075300",
        name: "LOOKBOOKS",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1075301",
        name: "ACTIVITY",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1075302",
        name: "OUR VALUES",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1075303",
        name: "BOTTOMS",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1075304",
        name: "TOPS & BRAS",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1075305",
        name: "JACKETS",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1075307",
        name: "DRESSES",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1075308",
        name: "SHOES & ACCESSORIES",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1075309",
        name: "SWIM",
        children: [
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1090347",
            name: "GIRL sustainability proofing  ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1090347",
            hasSubDivision: false,
          },
        ],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1015650", "46650"],
        type: "header",
        id: "1075313",
        name: "SALE",
        children: [
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1086217",
            name: "For Design Reference",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1086217",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1088841",
            name: "empty",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1088841",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1090379",
            name: "Plus TEST",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1090379",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1090987",
            name: "DPG Template Test",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1090987",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1125398",
            name: "Caraa",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1125398",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1132074",
            name: "TEST - 4/23 AKAMAI HOMEPAGE",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1132074",
            hasSubDivision: false,
          },
          {
            parents: ["1015650", "46650"],
            type: "category",
            id: "1132085",
            name: "TEST - 4/23 HIKE ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1132085",
            hasSubDivision: false,
          },
        ],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1015650",
    hasSubDivision: false,
  },
];

export default data;
