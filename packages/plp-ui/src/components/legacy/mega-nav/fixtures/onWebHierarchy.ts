// @ts-nocheck
'use client'

const data = [
  {
    parents: ["5151"],
    type: "division",
    id: "36704",
    name: "oldnavy.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=36704",
    hasSubDivision: false,
    brandCode: "3",
  },
  {
    parents: ["5151"],
    type: "division",
    id: "36706",
    name: "gap.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=36706",
    hasSubDivision: false,
    brandCode: "1",
  },
  {
    parents: ["5151"],
    type: "division",
    id: "36705",
    name: "bananarepublic.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=36705",
    hasSubDivision: false,
    brandCode: "2",
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1186992",
    name: "New!",
    children: [
      {
        parents: ["1186992", "5151"],
        type: "trimheader",
        id: "3007540",
        name: "New & Now for Women & Women's Plus",
        children: [
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007710",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007710",
            customUrl: "/browse/category.do?cid=10018",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007541",
            name: "$20 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007541",
            customUrl: "/browse/category.do?cid=1174785",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007542",
            name: "The Easter Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007542",
            customUrl: "/browse/category.do?cid=3007311",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007544",
            name: "Spring Outfits for the Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007544",
            customUrl: "/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007543",
            name: "Fashion Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007543",
            customUrl: "/browse/category.do?cid=1167062",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007545",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007545",
            customUrl: "/browse/category.do?cid=1159705",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007546",
            name: "Everyday Magic: Styles Starting From $6",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007546",
            customUrl: "/browse/category.do?cid=1173836",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007863",
            name: "Matching Mother’s Day Looks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007863",
            customUrl: "/browse/category.do?cid=1156596",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007547",
            name: "The Going Greener Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007547",
            customUrl: "/browse/category.do?cid=1185566",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=10018",
        hasSubDivision: false,
      },
      {
        parents: ["1186992", "5151"],
        type: "trimheader",
        id: "3007550",
        name: "New & Now for Men",
        children: [
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007711",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007711",
            customUrl: "/browse/category.do?cid=11174",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007551",
            name: "$20 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007551",
            customUrl: "/browse/category.do?cid=1149166",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007552",
            name: "The Easter Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007552",
            customUrl: "/browse/category.do?cid=1153232",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007553",
            name: "Spring Outfits For The Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007553",
            customUrl: "/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007683",
            name: "Customer Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007683",
            customUrl: "/browse/category.do?cid=3007624",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007554",
            name: "Everyday Magic: Styles Starting From $6",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007554",
            customUrl: "/browse/category.do?cid=1174380",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007555",
            name: "Wear To Work",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007555",
            customUrl: "/browse/category.do?cid=1128999",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007556",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007556",
            customUrl: "/browse/category.do?cid=1172329",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=11174",
        hasSubDivision: false,
      },
      {
        parents: ["1186992", "5151"],
        type: "trimheader",
        id: "3007557",
        name: "New & Now for Girls",
        children: [
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007712",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007712",
            customUrl: "/browse/category.do?cid=6036",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007558",
            name: "Everyday Magic: Styles Starting From $5",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007558",
            customUrl: "/browse/category.do?cid=1152083",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007559",
            name: "$10 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007559",
            customUrl: "/browse/category.do?cid=1189553",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007560",
            name: "The Easter Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007560",
            customUrl: "/browse/category.do?cid=1174634",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007561",
            name: "Spring Outfits For The Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007561",
            customUrl: "/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007562",
            name: "Fashion Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007562",
            customUrl: "/browse/category.do?cid=1167102",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007563",
            name: "Gender Neutral",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007563",
            customUrl: "/browse/category.do?cid=1174664",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007564",
            name: "Character License Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007564",
            customUrl: "/browse/category.do?cid=1164345",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007565",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007565",
            customUrl: "/browse/category.do?cid=1172235",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=6036",
        hasSubDivision: false,
      },
      {
        parents: ["1186992", "5151"],
        type: "trimheader",
        id: "3007566",
        name: "New & Now for Boys",
        children: [
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007713",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007713",
            customUrl: "/browse/category.do?cid=5918",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007567",
            name: "Everyday Magic: Styles Starting From $5",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007567",
            customUrl: "/browse/category.do?cid=1152085",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007568",
            name: "$10 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007568",
            customUrl: "/browse/category.do?cid=1144657",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007569",
            name: "The Easter Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007569",
            customUrl: "/browse/category.do?cid=1152354",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007570",
            name: "Spring Outfits For The Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007570",
            customUrl: "/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007571",
            name: "Fashion Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007571",
            customUrl: "/browse/category.do?cid=1167115",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007572",
            name: "Gender Neutral",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007572",
            customUrl: "/browse/category.do?cid=1174664",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007573",
            name: "Character License Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007573",
            customUrl: "/browse/category.do?cid=1164344",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007574",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007574",
            customUrl: "/browse/category.do?cid=1151205",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=5918",
        hasSubDivision: false,
      },
      {
        parents: ["1186992", "5151"],
        type: "trimheader",
        id: "3007575",
        name: "New & Now for Toddler Girls",
        children: [
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007714",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007714",
            customUrl: "/browse/category.do?cid=6825",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007576",
            name: "Everyday Magic: Styles Starting From $5",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007576",
            customUrl: "/browse/category.do?cid=1152020",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007577",
            name: "$10 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007577",
            customUrl: "/browse/category.do?cid=3007178",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007578",
            name: "The Easter Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007578",
            customUrl: "/browse/category.do?cid=1127820",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007579",
            name: "Spring Outfits For The Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007579",
            customUrl: "/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007662",
            name: "Fashion Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007662",
            customUrl: "/browse/category.do?cid=1167158",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007663",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007663",
            customUrl: "/browse/category.do?cid=1124658",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007581",
            name: "Unisex",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007581",
            customUrl: "/browse/category.do?cid=1174665",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007583",
            name: "Mix & Match Essentials",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007583",
            customUrl: "/browse/category.do?cid=1189660",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=6825",
        hasSubDivision: false,
      },
      {
        parents: ["1186992", "5151"],
        type: "trimheader",
        id: "3007610",
        name: "New & Now for Toddler Boys",
        children: [
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007715",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007715",
            customUrl: "/browse/category.do?cid=6157",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007611",
            name: "Everyday Magic: Styles Starting From $5",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007611",
            customUrl: "/browse/category.do?cid=1152021",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007627",
            name: "$10 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007627",
            customUrl: "/browse/category.do?cid=3007211",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007628",
            name: "The Easter Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007628",
            customUrl: "/browse/category.do?cid=1127821",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007629",
            name: "Spring Outfits For The Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007629",
            customUrl: "/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007639",
            name: "Fashion Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007639",
            customUrl: "/browse/category.do?cid=1167163",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007651",
            name: "Unisex",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007651",
            customUrl: "/browse/category.do?cid=1174665",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007582",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007582",
            customUrl: "/browse/category.do?cid=1124658",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007618",
            name: "Mix & Match Essentials",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007618",
            customUrl: "/browse/category.do?cid=1189665",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=5918",
        hasSubDivision: false,
      },
      {
        parents: ["1186992", "5151"],
        type: "trimheader",
        id: "3007664",
        name: "New & Now for Baby Girls",
        children: [
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007716",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007716",
            customUrl: "/browse/category.do?cid=37505",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007652",
            name: "Everyday Magic: Styles Starting From $5",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007652",
            customUrl: "/browse/category.do?cid=1152096",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007653",
            name: "$10 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007653",
            customUrl: "/browse/category.do?cid=3007182",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007654",
            name: "The Easter Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007654",
            customUrl: "/browse/category.do?cid=1151934",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007655",
            name: "Spring Outfits For The Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007655",
            customUrl: "/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007656",
            name: "Fashion Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007656",
            customUrl: "/browse/category.do?cid=1167172",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007657",
            name: "Unisex",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007657",
            customUrl: "/browse/category.do?cid=1174666",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007658",
            name: "Newborn Essentials",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007658",
            customUrl: "/browse/category.do?cid=1139620",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007659",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007659",
            customUrl: "/browse/category.do?cid=1188508",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007660",
            name: "Baby Gifts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007660",
            customUrl: "/browse/category.do?cid=1189650",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=37505",
        hasSubDivision: false,
      },
      {
        parents: ["1186992", "5151"],
        type: "trimheader",
        id: "3007625",
        name: "New & Now for Baby Boys",
        children: [
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007717",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007717",
            customUrl: "/browse/category.do?cid=37508",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007626",
            name: "Everyday Magic: Styles Starting From $5",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007626",
            customUrl: "/browse/category.do?cid=1152094",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007613",
            name: "$10 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007613",
            customUrl: "/browse/category.do?cid=3007203",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007612",
            name: "The Easter Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007612",
            customUrl: "/browse/category.do?cid=1151935",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007614",
            name: "Spring Outfits For The Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007614",
            customUrl: "/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007615",
            name: "Fashion Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007615",
            customUrl: "/browse/category.do?cid=1167173",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007616",
            name: "Unisex",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007616",
            customUrl: "/browse/category.do?cid=1174666",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007645",
            name: "Newborn Essentials",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007645",
            customUrl: "/browse/category.do?cid=1139637",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007617",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007617",
            customUrl: "/browse/category.do?cid=1188512",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007646",
            name: "Baby Gifts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007646",
            customUrl: "/browse/category.do?cid=1189654",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=37508",
        hasSubDivision: false,
      },
      {
        parents: ["1186992", "5151"],
        type: "trimheader",
        id: "3007638",
        name: "New & Now for Maternity",
        children: [
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "1187027",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1187027",
            customUrl: "/browse/category.do?cid=8454",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007865",
            name: "Matching Mother’s Day Looks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007865",
            customUrl: "/browse/category.do?cid=1156596",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007640",
            name: "Everyday Magic: Styles Starting From $7",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007640",
            customUrl: "/browse/category.do?cid=1174344",
            hasSubDivision: false,
          },
          {
            parents: ["1186992", "5151"],
            type: "category",
            id: "3007641",
            name: "Spring Outfits For The Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007641",
            customUrl: "/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=8454",
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1186992",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "95265",
    name: "Athleta.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=95265",
    hasSubDivision: false,
    brandCode: "10",
  },
  {
    parents: ["5151"],
    type: "division",
    id: "5360",
    name: "Women & Women's Plus",
    children: [
      {
        type: "headerless-group",
        children: [
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1089790",
            name: "Monica HIDDEN srt?",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1089790",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1165449",
            name: "Kari HIDDEN SORT",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1165449",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1184608",
            name: "Copy For Keeli",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1184608",
            customUrl:
              "/browse/category.do?cid=1184608&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1184640",
            name: "Copy of Styles Starting at $8",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1184640",
            customUrl:
              "/browse/category.do?cid=1184640&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1185032",
            name: "Copy of Shirts & Blouses ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1185032",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1162208",
            name: "?SHERYL'S HIDDEN Sort",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1162208",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1158992",
            name: "AMs Hidden",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1158992",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1091184",
            name: "Gayles Sort",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1091184",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1046705",
            name: "Sim's sort",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1046705",
            hasSubDivision: false,
          },
        ],
      },
      {
        parents: ["5360", "5151"],
        type: "trimheader",
        id: "1039280",
        name: "New & Now",
        children: [
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "10018",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=10018",
            customUrl: "/browse/category.do?cid=10018&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1174785",
            name: "$20 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174785",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "3007311",
            name: "The Easter Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007311",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "3007908",
            name: "Wear to Work",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007908",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "3007346",
            name: "Spring Outfits for the Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007346",
            customUrl: "https://oldnavy.gap.com/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1167062",
            name: "Fashion Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1167062",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1159705",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1159705",
            customUrl:
              "/browse/category.do?cid=1159705&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1173836",
            name: "Everyday Magic: Styles Starting from $6",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1173836",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1156596",
            name: "Matching Mother’s Day Looks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1156596",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1185566",
            name: "The Going Greener Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185566",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=10018&tlink=meganav%3AWomen%3ANew%20%26%20Now%3ANew%20Arrivals#pageId=0&department=136",
        hasSubDivision: false,
      },
      {
        parents: ["5360", "5151"],
        type: "header",
        id: "1182491",
        name: "Featured Shops",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["5360", "5151"],
        type: "trimheader",
        id: "1036191",
        name: "Shop Women's Categories",
        children: [
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1185233",
            name: "Shop All Women’s",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185233",
            customUrl:
              "/browse/category.do?cid=1185233&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "15292",
            name: "Dresses ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=15292",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "72091",
            name: "Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=72091",
            customUrl: "/browse/category.do?cid=72091&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1035712",
            name: "T-shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1035712",
            customUrl:
              "/browse/category.do?cid=1035712&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "72087",
            name: "Shirts & Blouses ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=72087",
            customUrl: "/browse/category.do?cid=72087&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1051876",
            name: "Jumpsuits & Rompers",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1051876",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1124176",
            name: "Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1124176",
            customUrl:
              "/browse/category.do?cid=1124176&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "85729",
            name: "Jeans ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85729",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "5475",
            name: "Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5475",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1011528",
            name: "Leggings",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1011528",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "35158",
            name: "Shorts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=35158",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "79586",
            name: "Skirts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=79586",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "72808",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=72808",
            customUrl: "/browse/category.do?cid=72808&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "20408",
            name: "Sweaters ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=20408",
            customUrl: "/browse/category.do?cid=20408&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "55474",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=55474",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "68066",
            name: "Swimsuits",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=68066",
            customUrl: "/browse/category.do?cid=68066&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "50058",
            name: "Pajamas & Loungewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=50058",
            customUrl: "/browse/category.do?cid=50058&#pageId=0&department=136",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/division.do?cid=5360&tlink=meganav%3AWomen%3AShop%20Women%27s%20Categories",
        hasSubDivision: false,
      },
      {
        parents: ["5360", "5151"],
        type: "header",
        id: "55182",
        name: "Old Navy Active",
        children: [
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "5508",
            name: "Shop All Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5508",
            customUrl: "/browse/category.do?cid=5508&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "3007314",
            name: "Activewear New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007314",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1030828",
            name: "Activewear Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1030828",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1031032",
            name: "Activewear Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1031032",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1034235",
            name: "Sports Bras ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1034235",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=5508&tlink=meganav%3AWomen%3AOld%20Navy%20Active%3AShop%20All%20Activewear#pageId=0&department=136",
        hasSubDivision: false,
      },
      {
        parents: ["5360", "5151"],
        type: "header",
        id: "1182446",
        name: "Lounge, Sleepwear & Intimates",
        children: [
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1184271",
            name: "Explore O.N.L.Y. Old Navy Loves You",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1184271",
            customUrl: "/browse/info.do?cid=1183591",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1182448",
            name: "Bras & Bralettes",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1182448",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1182449",
            name: "Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1182449",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1182450",
            name: "Sleep",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1182450",
            customUrl:
              "/browse/category.do?cid=1182450&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1182451",
            name: "Loungewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1182451",
            customUrl:
              "/browse/category.do?cid=1182451&#pageId=0&department=136",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1182448",
        hasSubDivision: false,
      },
      {
        parents: ["5360", "5151"],
        type: "header",
        id: "1019570",
        name: "Shoes & Accessories",
        children: [
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "55147",
            name: "Shoes",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=55147",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1000106",
            name: "Socks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1000106",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "14675",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=14675",
            customUrl: "/browse/category.do?cid=14675&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1054603",
            name: "Sunglasses",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1054603",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1066122",
            name: "Jewelry",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1066122",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1164877",
            name: "Face Masks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1164877",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=14675&tlink=meganav%3AWomen%3AShoes%20%26%20Accessories%3AAccessories#pageId=0&department=136",
        hasSubDivision: false,
      },
      {
        parents: ["5360", "5151"],
        type: "header",
        id: "1179124",
        name: "Home + Family ",
        children: [
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1179125",
            name: "Family Fun",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179125",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1179127",
            name: "For the Home",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179127",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1179126",
            name: "Pet Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179126",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1179125&#pageId=0&department=136",
        hasSubDivision: false,
      },
      {
        parents: ["5360", "5151"],
        type: "header",
        id: "1176447",
        name: "Beauty & Wellness",
        children: [
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1176448",
            name: "Makeup",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1176448",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1176451",
            name: "Personal Care",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1176451",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1176448",
        hasSubDivision: false,
      },
      {
        parents: ["5360", "5151"],
        type: "header",
        id: "1016051",
        name: "Deals",
        children: [
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1165714",
            name: "Deals from $8",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1165714",
            customUrl:
              "/browse/category.do?cid=1165714&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1149355",
            name: "60% Off Fashion Faves",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1149355",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1170612",
            name: "1000s of Styles from $6",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1170612",
            customUrl:
              "/browse/category.do?cid=1170612&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "sale",
            id: "96964",
            name: "Clearance ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=96964",
            customUrl: "/browse/category.do?cid=96964&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "sale",
            id: "26190",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=26190",
            customUrl: "/browse/category.do?cid=26190&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1160572",
            name: "Denim Shorts for Women",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1160572",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1160574",
            name: "Pajamas",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1160574",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=26190&tlink=meganav%3AWomen%3ADeals%3ASale#pageId=0&department=136",
        hasSubDivision: false,
      },
      {
        parents: ["5360", "5151"],
        type: "header",
        id: "1176894",
        name: "Old Navy Active - TEST HEADER",
        children: [
          {
            parents: ["5360", "5151"],
            type: "spacer",
            id: "1100226",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1100226",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1176898",
        hasSubDivision: false,
      },
      {
        parents: ["5360", "5151"],
        type: "trimheader",
        id: "1069633",
        name: "Gift Cards",
        children: [
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "35459",
            name: "Gift Cards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=35459",
            customUrl: "/customerService/info.do?cid=35433",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "spacer",
            id: "1100228",
            name: "Spacer ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1100228",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1144745",
            name: "TEST CATEGORY",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1144745",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1160568",
            name: "Jeans",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1160568",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1181585",
            name: "Lynn Hidden Sort",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1181585",
            hasSubDivision: false,
          },
          {
            parents: ["5360", "5151"],
            type: "category",
            id: "1184340",
            name: "test 1",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1184340",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=5360",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "49708",
    name: "Women's Petite",
    children: [
      {
        parents: ["49708", "5151"],
        type: "header",
        id: "53849",
        name: "Latest & Greatest",
        children: [
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "41714",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41714",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "spacer",
            id: "53911",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=53911",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["49708", "5151"],
        type: "trimheader",
        id: "1036205",
        name: "Shop by Category",
        children: [
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "41725",
            name: "Dresses & Jumpsuits",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41725",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "41716",
            name: "Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41716",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "41718",
            name: "Blouses & Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41718",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "41721",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41721",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "41722",
            name: "Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41722",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "1131346",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1131346",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "41724",
            name: "Skirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41724",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "41719",
            name: "Sweaters",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41719",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "41720",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41720",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "41726",
            name: "Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41726",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["49708", "5151"],
        type: "header",
        id: "1179257",
        name: "Home + Family ",
        children: [
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "1179258",
            name: "Family Fun",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179258",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "1179259",
            name: "For the Home",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179259",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "spacer",
            id: "55820",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=55820",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1179125&#pageId=0&department=136",
        hasSubDivision: false,
      },
      {
        parents: ["49708", "5151"],
        type: "header",
        id: "55198",
        name: "Deals",
        children: [
          {
            parents: ["49708", "5151"],
            type: "sale",
            id: "1159082",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1159082",
            customUrl:
              "/browse/category.do?cid=1159082&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "sale",
            id: "1159100",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1159100",
            customUrl:
              "/browse/category.do?cid=1159100&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "spacer",
            id: "1017006",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1017006",
            hasSubDivision: false,
          },
          {
            parents: ["49708", "5151"],
            type: "category",
            id: "56015",
            name: "GiftCards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=56015",
            customUrl: "/customerService/info.do?cid=35433",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=49708",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "49709",
    name: "Women's Tall",
    children: [
      {
        parents: ["49709", "5151"],
        type: "header",
        id: "53883",
        name: "Latest & Greatest",
        children: [
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "41728",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41728",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "spacer",
            id: "1160041",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1160041",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["49709", "5151"],
        type: "trimheader",
        id: "1036206",
        name: "Shop by Category",
        children: [
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "41738",
            name: "Dresses & Jumpsuits",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41738",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "41730",
            name: "Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41730",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "49922",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=49922",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "41734",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41734",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "1131345",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1131345",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "41737",
            name: "Skirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41737",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "41736",
            name: "Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41736",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "41733",
            name: "Sweaters",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41733",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "41740",
            name: "Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41740",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "1125419",
            name: "Swim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1125419",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "1162717",
            name: "Pajamas & Loungewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1162717",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["49709", "5151"],
        type: "header",
        id: "1179261",
        name: "Home + Family ",
        children: [
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "1179262",
            name: "Family Fun",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179262",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "1179263",
            name: "For the Home",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179263",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "spacer",
            id: "56413",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=56413",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["49709", "5151"],
        type: "header",
        id: "55201",
        name: "Deals",
        children: [
          {
            parents: ["49709", "5151"],
            type: "sale",
            id: "1159154",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1159154",
            customUrl:
              "/browse/category.do?cid=1159154&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "sale",
            id: "1159175",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1159175",
            customUrl:
              "/browse/category.do?cid=1159175&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "spacer",
            id: "53912",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=53912",
            hasSubDivision: false,
          },
          {
            parents: ["49709", "5151"],
            type: "category",
            id: "56017",
            name: "GiftCards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=56017",
            customUrl: "/customerService/info.do?cid=35433",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=49709",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "5585",
    name: "Women's Plus",
    children: [
      {
        type: "headerless-group",
        children: [
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "1011624",
            name: "TEST OWL",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1011624",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "1032074",
            name: "TEST Old Navy Active",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1032074",
            hasSubDivision: false,
          },
        ],
      },
      {
        parents: ["5585", "5151"],
        type: "trimheader",
        id: "1039164",
        name: "New & Now ",
        children: [
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "5597",
            name: "New Arrivals",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=5597",
            customUrl: "/browse/category.do?cid=5597&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "1174343",
            name: "Everyday Magic: Everything $15 & Under",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1174343",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=5597&tlink=meganav%3AWomen%27s%20Plus%3ANew%20%26%20Now%20%3ANew%20Arrivals#pageId=0&department=136",
        hasSubDivision: false,
      },
      {
        parents: ["5585", "5151"],
        type: "header",
        id: "55183",
        name: "Deals",
        children: [
          {
            parents: ["5585", "5151"],
            type: "sale",
            id: "96949",
            name: "Clearance",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=96949",
            customUrl: "/browse/category.do?cid=96949&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "sale",
            id: "26231",
            name: "Sale",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=26231",
            customUrl: "/browse/category.do?cid=26231&#pageId=0&department=136",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=26231&tlink=meganav%3AWomen%27s%20Plus%3ADeals%3ASale#pageId=0&department=136",
        hasSubDivision: false,
      },
      {
        parents: ["5585", "5151"],
        type: "trimheader",
        id: "1036207",
        name: "Shop Women’s Plus Categories",
        children: [
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "7240",
            name: "Tops",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=7240",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "46550",
            name: "Tees ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=46550",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "1126786",
            name: "Bottoms",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1126786",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "65100",
            name: "Shorts",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=65100",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "5671",
            name: "Pants",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=5671",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "5668",
            name: "Jeans",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=5668",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "1042659",
            name: "Activewear",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1042659",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "1099712",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1099712",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "96016",
            name: "Sweaters",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=96016",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "65080",
            name: "Swim",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=65080",
            customUrl: "/browse/category.do?cid=65080&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "5681",
            name: "Pajamas & Loungewear ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=5681",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/division.do?cid=5585&tlink=meganav%3AWomen%27s%20Plus%3AShop%20Women’s%20Plus%20Categories",
        hasSubDivision: false,
      },
      {
        parents: ["5585", "5151"],
        type: "header",
        id: "1092433",
        name: "Shoes & Accessories",
        children: [
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "55164",
            name: "Shoes",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=55164",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "1016287",
            name: "Accessories",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1016287",
            customUrl:
              "/browse/category.do?cid=1016287&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "1066134",
            name: "Jewelry",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1066134",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "1085365",
            name: "Beauty",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1085365",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "spacer",
            id: "5707",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=5707",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "1118333",
            name: "Staging",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1118333",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "spacer",
            id: "11280",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=11280",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=1016287&tlink=meganav%3AWomen%27s%20Plus%3AShoes%20%26%20Accessories%3AAccessories#pageId=0&department=136",
        hasSubDivision: false,
      },
      {
        parents: ["5585", "5151"],
        type: "trimheader",
        id: "1100167",
        name: "Gift Cards",
        children: [
          {
            parents: ["5585", "5151"],
            type: "category",
            id: "35461",
            name: "Gift Cards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=35461",
            customUrl: "/customerService/info.do?cid=35433",
            hasSubDivision: false,
          },
          {
            parents: ["5585", "5151"],
            type: "spacer",
            id: "1100229",
            name: "Spacer ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1100229",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=5585",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "5155",
    name: "Men",
    children: [
      {
        type: "headerless-group",
        children: [
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1116768",
            name: "MEGAN'S TEST SUBCAT NOT ACTIVE",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1116768",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "3007126",
            name: "****Archived Subcategories****",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=3007126",
            hasSubDivision: false,
          },
        ],
      },
      {
        parents: ["5155", "5151"],
        type: "trimheader",
        id: "1039290",
        name: "New & Now",
        children: [
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "11174",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=11174",
            customUrl: "/browse/category.do?cid=11174&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1183298",
            name: "Spring Outfits for the Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183298",
            customUrl: "https://oldnavy.gap.com/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1174380",
            name: "Everyday Magic: Styles Starting from $6",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174380",
            customUrl:
              "/browse/category.do?cid=1174380&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "3007624",
            name: "Customer Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007624",
            customUrl:
              "/browse/category.do?cid=3007624&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1128999",
            name: "Wear to Work",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1128999",
            customUrl:
              "/browse/category.do?cid=1128999&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1172329",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1172329",
            customUrl:
              "/browse/category.do?cid=1172329&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1105661",
            name: "Shop By Size",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1105661",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=11174&tlink=meganav%3AMen%3ANew%20%26%20Now%3ANew%20Arrivals#pageId=0&department=75",
        hasSubDivision: false,
      },
      {
        parents: ["5155", "5151"],
        type: "trimheader",
        id: "1036209",
        name: "Shop Men’s Categories",
        children: [
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1031099",
            name: "Shop All Men's",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1031099",
            customUrl:
              "/browse/category.do?cid=1031099&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1126985",
            name: "Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1126985",
            customUrl:
              "/browse/category.do?cid=1126985&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "5249",
            name: "T-Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5249",
            customUrl: "/browse/category.do?cid=5249&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "5271",
            name: "Graphic T-Shirts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5271",
            customUrl: "/browse/category.do?cid=5271&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1010005",
            name: "Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1010005",
            customUrl:
              "/browse/category.do?cid=1010005&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1174474",
            name: "Polos",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174474",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "5226",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5226",
            customUrl: "/browse/category.do?cid=5226&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1126951",
            name: "Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1126951",
            customUrl:
              "/browse/category.do?cid=1126951&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "5199",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5199",
            customUrl: "/browse/category.do?cid=5199&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "5211",
            name: "Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5211",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "66124",
            name: "Swimsuits",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=66124",
            customUrl: "/browse/category.do?cid=66124&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1016048",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1016048",
            customUrl:
              "/browse/category.do?cid=1016048&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "63315",
            name: "Sweaters",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=63315",
            customUrl: "/browse/category.do?cid=63315&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "72305",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=72305",
            customUrl: "/browse/category.do?cid=72305&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "38529",
            name: "Pajamas & Loungewear ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=38529",
            customUrl: "/browse/category.do?cid=38529&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1017042",
            name: "Socks & Underwear ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1017042",
            customUrl:
              "/browse/category.do?cid=1017042&#pageId=0&department=75",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/division.do?cid=5155&tlink=meganav%3AMen%3AShop%20Men’s%20Categories",
        hasSubDivision: false,
      },
      {
        parents: ["5155", "5151"],
        type: "header",
        id: "1031097",
        name: "Old Navy Active",
        children: [
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1031103",
            name: "Shop All Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1031103",
            customUrl:
              "/browse/category.do?cid=1031103&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1053127",
            name: "Activewear Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1053127",
            customUrl:
              "/browse/category.do?cid=1053127&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1053133",
            name: "Activewear Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1053133",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1154696",
            name: "Golf Essentials ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1154696",
            customUrl:
              "/browse/category.do?cid=1154696&#pageId=0&department=75",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=1031103&tlink=meganav%3AMen%3AOld%20Navy%20Active%3AShop%20All%20Activewear#pageId=0&department=75",
        hasSubDivision: false,
      },
      {
        parents: ["5155", "5151"],
        type: "header",
        id: "1145656",
        name: "Shoes & Accessories",
        children: [
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1016049",
            name: "Shoes",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1016049",
            customUrl:
              "/browse/category.do?cid=1016049&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1126947",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1126947",
            customUrl:
              "/browse/category.do?cid=1126947&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1187130",
            name: "Face Masks ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1187130",
            customUrl: "/browse/category.do?cid=1164877",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "spacer",
            id: "5320",
            name: "SPACER",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=5320",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=1126947&tlink=meganav%3AMen%3AShoes%20%26%20Accessories%3AAccessories#pageId=0&department=75",
        hasSubDivision: false,
      },
      {
        parents: ["5155", "5151"],
        type: "header",
        id: "1179128",
        name: "Home + Family",
        children: [
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1179130",
            name: "Family Fun",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179130",
            customUrl:
              "/browse/category.do?cid=1179130&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1179133",
            name: "For the Home",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179133",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1179134",
            name: "Pet Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179134",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1179130&#pageId=0&department=75",
        hasSubDivision: false,
      },
      {
        parents: ["5155", "5151"],
        type: "header",
        id: "55197",
        name: "Deals",
        children: [
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1135640",
            name: "Deals from $8",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1135640",
            customUrl:
              "/browse/category.do?cid=1135640&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1149357",
            name: "60% Off Fashion Faves",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1149357",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "3007975",
            name: "1000s of Styles from $6",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=3007975",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "sale",
            id: "97035",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=97035",
            customUrl: "/browse/category.do?cid=97035&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "sale",
            id: "26061",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=26061",
            customUrl: "/browse/category.do?cid=26061&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "spacer",
            id: "1178471",
            name: "SPACER",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1178471",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=26061&tlink=meganav%3AMen%3ADeals%3ASale#pageId=0&department=75",
        hasSubDivision: false,
      },
      {
        parents: ["5155", "5151"],
        type: "trimheader",
        id: "1069574",
        name: "Gift Cards",
        children: [
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "35460",
            name: "Gift Cards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=35460",
            customUrl: "/customerService/info.do?cid=35433",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "spacer",
            id: "1100230",
            name: "Spacer ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1100230",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1156599",
            name: "Joggers for Men",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1156599",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1163159",
            name: "Ari's Sort ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1163159",
            hasSubDivision: false,
          },
          {
            parents: ["5155", "5151"],
            type: "category",
            id: "1076839",
            name: "Brenda's Sort",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1076839",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=5155",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1090014",
    name: "Men's Big",
    children: [
      {
        parents: ["1090014", "5151"],
        type: "header",
        id: "1090395",
        name: "New & Now",
        children: [
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85343",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85343",
            customUrl: "/browse/category.do?cid=85343&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "1142057",
            name: "#oldnavystyle",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1142057",
            customUrl: "/browse/category.do?cid=1141047&showDF=true",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "1140935",
            name: "Wear to Work",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1140935",
            customUrl: "/browse/category.do?cid=1128999&showDF=true",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "1142081",
            name: "Family Matching",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1142081",
            customUrl: "/browse/category.do?cid=1091856&showDF=true",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1090014", "5151"],
        type: "header",
        id: "1090428",
        name: "Old Navy Active",
        children: [
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85370",
            name: "Shop All Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85370",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "1142626",
            name: "Activewear Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1142626",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "1142639",
            name: "Activewear Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1142639",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1090014", "5151"],
        type: "trimheader",
        id: "1109490",
        name: "Shop by Category",
        children: [
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85383",
            name: "Tees",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85383",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85379",
            name: "Graphic Tees",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85379",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85386",
            name: "Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85386",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "1090481",
            name: "Polos",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1090481",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85399",
            name: "Shorts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85399",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85401",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85401",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85400",
            name: "Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85400",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85381",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85381",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "1092636",
            name: "Sweaters",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1092636",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85388",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85388",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85389",
            name: "Swim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85389",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85403",
            name: "Pajamas & Loungewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85403",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "1090485",
            name: "Shoes & Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1090485",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "category",
            id: "85404",
            name: "Socks & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=85404",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1090014", "5151"],
        type: "header",
        id: "1090501",
        name: "Deals",
        children: [
          {
            parents: ["1090014", "5151"],
            type: "sale",
            id: "1090627",
            name: "SALE",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1090627",
            customUrl:
              "/browse/category.do?cid=1090627&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["1090014", "5151"],
            type: "sale",
            id: "1090514",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1090514",
            customUrl:
              "/browse/category.do?cid=1090514&#pageId=0&department=75",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1090014",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1091221",
    name: "Men's Tall",
    children: [
      {
        parents: ["1091221", "5151"],
        type: "header",
        id: "1091241",
        name: "New & Now",
        children: [
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091244",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091244",
            customUrl:
              "/browse/category.do?cid=1091244&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1142097",
            name: "#oldnavystyle",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1142097",
            customUrl: "/browse/category.do?cid=1141047&showDF=true",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1140977",
            name: "Wear To Work",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1140977",
            customUrl: "/browse/category.do?cid=1128999&showDF=true",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1142105",
            name: "Family Matching",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1142105",
            customUrl: "/browse/category.do?cid=1091856&showDF=true",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1091221", "5151"],
        type: "header",
        id: "1091282",
        name: "Old Navy Active",
        children: [
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091285",
            name: "Shop All Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091285",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1142661",
            name: "Activewear Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1142661",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1142663",
            name: "Activewear Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1142663",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1154695",
            name: "Golf Essentials ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1154695",
            customUrl: "/browse/category.do?cid=1154696&showDF=true",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1091221", "5151"],
        type: "trimheader",
        id: "1109491",
        name: "Shop by Category",
        children: [
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091226",
            name: "Tees",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091226",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091240",
            name: "Graphic Tees",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091240",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091245",
            name: "Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091245",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091243",
            name: "Polos",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091243",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091331",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091331",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091265",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091265",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091304",
            name: "Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091304",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091248",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091248",
            customUrl:
              "/browse/category.do?cid=1091248&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1092692",
            name: "Sweaters",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1092692",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091305",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091305",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091341",
            name: "Swim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091341",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091261",
            name: "Pajamas & Loungewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091261",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091250",
            name: "Shoes & Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091250",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "category",
            id: "1091254",
            name: "Socks & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091254",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1091221", "5151"],
        type: "header",
        id: "1091231",
        name: "Deals",
        children: [
          {
            parents: ["1091221", "5151"],
            type: "sale",
            id: "1091308",
            name: "SALE",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091308",
            customUrl:
              "/browse/category.do?cid=1091308&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["1091221", "5151"],
            type: "sale",
            id: "1091310",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1091310",
            customUrl:
              "/browse/category.do?cid=1091310&#pageId=0&department=75",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1091221",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "6027",
    name: "Girls",
    children: [
      {
        parents: ["6027", "5151"],
        type: "header",
        id: "1119128",
        name: "Shop By Size ",
        children: [
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "39656",
            name: "Shop By Size",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=39656",
            customUrl: "/browse/category.do?cid=39656&#pageId=0&department=48",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=39656&tlink=meganav%3AGirls%3AShop%20By%20Size%20%3AShop%20By%20Size#pageId=0&department=48",
        hasSubDivision: false,
      },
      {
        parents: ["6027", "5151"],
        type: "header",
        id: "1092613",
        name: "New & Now",
        children: [
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "6036",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6036",
            customUrl: "/browse/category.do?cid=6036&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1152083",
            name: "Everyday Magic: Styles Starting from $5",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1152083",
            customUrl:
              "/browse/category.do?cid=1152083&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1189553",
            name: "$10 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189553",
            customUrl:
              "/browse/category.do?cid=1189553&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1174634",
            name: "The Easter Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174634",
            customUrl:
              "/browse/category.do?cid=1174634&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "3007343",
            name: "Spring Outfits for the Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007343",
            customUrl: "https://oldnavy.gap.com/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1167102",
            name: "Fashion Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1167102",
            customUrl:
              "/browse/category.do?cid=1167102&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1168762",
            name: "Gender Neutral",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1168762",
            customUrl: "/browse/category.do?cid=1174664",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1164345",
            name: "Character License Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1164345",
            customUrl:
              "/browse/category.do?cid=1164345&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1172235",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1172235",
            customUrl:
              "/browse/category.do?cid=1172235&#pageId=0&department=48",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=6036&tlink=meganav%3AGirls%3ANew%20%26%20Now%3ANew%20Arrivals#pageId=0&department=48",
        hasSubDivision: false,
      },
      {
        parents: ["6027", "5151"],
        type: "header",
        id: "1182490",
        name: "Featured Shops",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["6027", "5151"],
        type: "trimheader",
        id: "1036216",
        name: "Shop Girls Categories",
        children: [
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1185229",
            name: "Shop All Girl's",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185229",
            customUrl:
              "/browse/category.do?cid=1185229&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1153930",
            name: "Multipacks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1153930",
            customUrl:
              "/browse/category.do?cid=1153930&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "39288",
            name: "Dresses",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=39288",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1129193",
            name: "Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1129193",
            customUrl:
              "/browse/category.do?cid=1129193&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "6070",
            name: "T-shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6070",
            customUrl: "/browse/category.do?cid=6070&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "6072",
            name: "Graphic T-Shirts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6072",
            customUrl: "/browse/category.do?cid=6072&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "97071",
            name: "Shirts & Blouses ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=97071",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1157128",
            name: "Jumpsuits & Rompers",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1157128",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1127019",
            name: "Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127019",
            customUrl:
              "/browse/category.do?cid=1127019&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "6054",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6054",
            customUrl: "/browse/category.do?cid=6054&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "45013",
            name: "Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=45013",
            customUrl: "/browse/category.do?cid=45013&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "40903",
            name: "Shorts & Skirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=40903",
            customUrl: "/browse/category.do?cid=40903&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "53936",
            name: "Leggings",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=53936",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "42646",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=42646",
            customUrl: "/browse/category.do?cid=42646&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "41748",
            name: "Sweaters",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41748",
            customUrl: "/browse/category.do?cid=41748&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "60050",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=60050",
            customUrl: "/browse/category.do?cid=60050&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "45211",
            name: "Swimsuits",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=45211",
            customUrl: "/browse/category.do?cid=45211&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "6100",
            name: "Pajamas",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6100",
            customUrl: "/browse/category.do?cid=6100&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "96212",
            name: "Socks & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=96212",
            customUrl: "/browse/category.do?cid=96212&#pageId=0&department=48",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/division.do?cid=6027&tlink=meganav%3AGirls%3AShop%20Girls%20Categories",
        hasSubDivision: false,
      },
      {
        parents: ["6027", "5151"],
        type: "header",
        id: "1007069",
        name: "Old Navy Active",
        children: [
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1013969",
            name: "Shop All Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1013969",
            customUrl:
              "/browse/category.do?cid=1013969&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1062949",
            name: "Activewear Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1062949",
            customUrl:
              "/browse/category.do?cid=1062949&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1062950",
            name: "Activewear Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1062950",
            customUrl:
              "/browse/category.do?cid=1062950&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1178204",
            name: "Sports Bras",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1178204",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1168925",
            name: "Matching Sets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1168925",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=1013969&tlink=meganav%3AGirls%3AOld%20Navy%20Active%3AShop%20All%20Activewear#pageId=0&department=48",
        hasSubDivision: false,
      },
      {
        parents: ["6027", "5151"],
        type: "header",
        id: "1125471",
        name: "Shoes & Accessories",
        children: [
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "6109",
            name: "Shoes",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6109",
            customUrl: "/browse/category.do?cid=6109&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1125472",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1125472",
            customUrl:
              "/browse/category.do?cid=1125472&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1187132",
            name: "Face Masks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1187132",
            customUrl: "/browse/category.do?cid=1164877",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=1125472&tlink=meganav%3AGirls%3AShoes%20%26%20Accessories%3AAccessories#pageId=0&department=48",
        hasSubDivision: false,
      },
      {
        parents: ["6027", "5151"],
        type: "header",
        id: "1015504",
        name: "Uniforms",
        children: [
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "36516",
            name: "Shop All Uniforms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=36516",
            customUrl: "/browse/category.do?cid=36516&#pageId=0&department=48",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=36516&tlink=meganav%3AGirls%3AUniform%20Shop%3AShop%20All%20Uniform#pageId=0&department=48",
        hasSubDivision: false,
      },
      {
        parents: ["6027", "5151"],
        type: "header",
        id: "1179135",
        name: "Home + Family",
        children: [
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1179136",
            name: "Family Fun",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179136",
            customUrl:
              "/browse/category.do?cid=1179125&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1179137",
            name: "For the Home",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179137",
            customUrl:
              "/browse/category.do?cid=1179127&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1161228",
            name: "Toys & Games",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1161228",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1179125&#pageId=0&department=48",
        hasSubDivision: false,
      },
      {
        parents: ["6027", "5151"],
        type: "header",
        id: "54857",
        name: "Deals",
        children: [
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1149351",
            name: "60% Off Fashion Faves",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1149351",
            customUrl:
              "/browse/category.do?cid=1149351&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1187085",
            name: "Deals from $8",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1187085",
            customUrl:
              "/browse/category.do?cid=1187085&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1187738",
            name: "1000s of Styles from $6",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1187738",
            customUrl:
              "/browse/category.do?cid=1187738&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "sale",
            id: "96906",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=96906",
            customUrl: "/browse/category.do?cid=96906&#pageId=0&department=48",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "sale",
            id: "26175",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=26175",
            customUrl: "/browse/category.do?cid=26175&#pageId=0&department=48",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=26175&tlink=meganav%3AGirls%3ADeals%3ASale#pageId=0&department=48",
        hasSubDivision: false,
      },
      {
        parents: ["6027", "5151"],
        type: "trimheader",
        id: "1069652",
        name: "Gift Cards",
        children: [
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "35468",
            name: "Gift Cards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=35468",
            customUrl: "/customerService/info.do?cid=35433",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "spacer",
            id: "6119",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=6119",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "spacer",
            id: "11284",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=11284",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1104553",
            name: "BH TEST CATEGORY",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1104553",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1136645",
            name: "Taylor HIDDEN",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1136645",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1168086",
            name: "Jill WIP Cat",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1168086",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1171606",
            name: "Boys Activewear",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1171606",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1168832",
            name: "Kids Q1 2022 Sets CCs",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1168832",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1174287",
            name: "Girls Activewear pt.2",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1174287",
            hasSubDivision: false,
          },
          {
            parents: ["6027", "5151"],
            type: "category",
            id: "1183061",
            name: "Copy of Kids Daily Deals",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1183061",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=6027",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "5910",
    name: "Boys",
    children: [
      {
        parents: ["5910", "5151"],
        type: "header",
        id: "1119129",
        name: "Shop By Size ",
        children: [
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "39659",
            name: "Shop By Size",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=39659",
            customUrl: "/browse/category.do?cid=39659&#pageId=0&department=16",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=39659&tlink=meganav%3ABoys%3AShop%20By%20Size%20%3AShop%20By%20Size#pageId=0&department=16",
        hasSubDivision: false,
      },
      {
        parents: ["5910", "5151"],
        type: "header",
        id: "1071417",
        name: "New & Now",
        children: [
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "5918",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5918",
            customUrl: "/browse/category.do?cid=5918&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1152085",
            name: "Everyday Magic: Styles Starting from $5",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1152085",
            customUrl:
              "/browse/category.do?cid=1152085&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1144657",
            name: "$10 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1144657",
            customUrl:
              "/browse/category.do?cid=1144657&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1152354",
            name: "The Easter Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1152354",
            customUrl:
              "/browse/category.do?cid=1152354&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1188487",
            name: "Spring Outfits for the Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188487",
            customUrl: "https://oldnavy.gap.com/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1167115",
            name: "Fashion Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1167115",
            customUrl:
              "/browse/category.do?cid=1167115&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1168757",
            name: "Gender Neutral",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1168757",
            customUrl: "/browse/category.do?cid=1174664",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1164344",
            name: "Character License Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1164344",
            customUrl:
              "/browse/category.do?cid=1164344&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1151205",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1151205",
            customUrl:
              "/browse/category.do?cid=1151205&#pageId=0&department=16",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=5918&tlink=meganav%3ABoys%3ANew%20%26%20Now%3ANew%20Arrivals#pageId=0&department=16",
        hasSubDivision: false,
      },
      {
        parents: ["5910", "5151"],
        type: "trimheader",
        id: "1036210",
        name: "Shop Boys Categories",
        children: [
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1185232",
            name: "Shop All Boy's",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185232",
            customUrl:
              "/browse/category.do?cid=1185232&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1153777",
            name: "Multipacks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1153777",
            customUrl:
              "/browse/category.do?cid=1153777&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1127027",
            name: "Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127027",
            customUrl:
              "/browse/category.do?cid=1127027&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "36097",
            name: "T-shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=36097",
            customUrl: "/browse/category.do?cid=36097&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "5954",
            name: "Graphic T-Shirts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5954",
            customUrl: "/browse/category.do?cid=5954&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "13469",
            name: "Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=13469",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "36943",
            name: "Polos",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=36943",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1127025",
            name: "Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127025",
            customUrl:
              "/browse/category.do?cid=1127025&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "5936",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5936",
            customUrl: "/browse/category.do?cid=5936&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "5934",
            name: "Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5934",
            customUrl: "/browse/category.do?cid=5934&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "16604",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=16604",
            customUrl: "/browse/category.do?cid=16604&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "51197",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=51197",
            customUrl: "/browse/category.do?cid=51197&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "41893",
            name: "Sweaters",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41893",
            customUrl: "/browse/category.do?cid=41893&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "60051",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=60051",
            customUrl: "/browse/category.do?cid=60051&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "5948",
            name: "Swimsuits",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5948",
            customUrl: "/browse/category.do?cid=5948&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "36319",
            name: "Pajamas",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=36319",
            customUrl: "/browse/category.do?cid=36319&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "5988",
            name: "Socks & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5988",
            customUrl: "/browse/category.do?cid=5988&#pageId=0&department=16",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/division.do?cid=5910&tlink=meganav%3ABoys%3AShop%20Boys%20Categories",
        hasSubDivision: false,
      },
      {
        parents: ["5910", "5151"],
        type: "header",
        id: "1006914",
        name: "Old Navy Active",
        children: [
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1013976",
            name: "Shop All Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1013976",
            customUrl:
              "/browse/category.do?cid=1013976&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1057961",
            name: "Activewear Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1057961",
            customUrl:
              "/browse/category.do?cid=1057961&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1057963",
            name: "Activewear Bottoms ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1057963",
            customUrl:
              "/browse/category.do?cid=1057963&#pageId=0&department=16",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=1013976&tlink=meganav%3ABoys%3AOld%20Navy%20Active%3AShop%20All%20Activewear#pageId=0&department=16",
        hasSubDivision: false,
      },
      {
        parents: ["5910", "5151"],
        type: "header",
        id: "1125295",
        name: "Shoes & Accessories",
        children: [
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "5984",
            name: "Shoes ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5984",
            customUrl: "/browse/category.do?cid=5984&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1125301",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1125301",
            customUrl:
              "/browse/category.do?cid=1125301&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1158701",
            name: "Face Masks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1158701",
            customUrl: "/browse/category.do?cid=1164877",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=1125301&tlink=meganav%3ABoys%3AShoes%20%26%20Accessories%3AAccessories#pageId=0&department=16",
        hasSubDivision: false,
      },
      {
        parents: ["5910", "5151"],
        type: "header",
        id: "1015530",
        name: "Uniforms",
        children: [
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "5924",
            name: "Shop All Uniforms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5924",
            customUrl: "/browse/category.do?cid=5924&#pageId=0&department=16",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=5924&tlink=meganav%3ABoys%3AUniform%20Shop%3AShop%20All%20Uniform#pageId=0&department=16",
        hasSubDivision: false,
      },
      {
        parents: ["5910", "5151"],
        type: "header",
        id: "1179139",
        name: "Home + Family",
        children: [
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1179140",
            name: "Family Fun",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179140",
            customUrl:
              "/browse/category.do?cid=1179130&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1179142",
            name: "For the Home",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179142",
            customUrl:
              "/browse/category.do?cid=1179133&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1179143",
            name: "Pet Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179143",
            customUrl:
              "/browse/category.do?cid=1179134&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1161232",
            name: "Toys & Games",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1161232",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1179133&#pageId=0&department=16",
        hasSubDivision: false,
      },
      {
        parents: ["5910", "5151"],
        type: "header",
        id: "54865",
        name: "Deals",
        children: [
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1148007",
            name: "60% Off Fashion Faves",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1148007",
            customUrl:
              "/browse/category.do?cid=1148007&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1188346",
            name: "Deals from $8",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188346",
            customUrl:
              "/browse/category.do?cid=1188346&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1187740",
            name: "1000s of Styles from $6",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1187740",
            customUrl:
              "/browse/category.do?cid=1187740&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "sale",
            id: "96945",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=96945",
            customUrl: "/browse/category.do?cid=96945&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "sale",
            id: "26073",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=26073",
            customUrl: "/browse/category.do?cid=26073&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "spacer",
            id: "6000",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=6000",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=26073&tlink=meganav%3ABoys%3ADeals%3ASale#pageId=0&department=16",
        hasSubDivision: false,
      },
      {
        parents: ["5910", "5151"],
        type: "trimheader",
        id: "1069634",
        name: "Gift Cards",
        children: [
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "35463",
            name: "Gift Cards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=35463",
            customUrl: "/customerService/info.do?cid=35433",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "spacer",
            id: "1100190",
            name: "Spacer ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1100190",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1133796",
            name: "Tommys Sort 2",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1133796",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1145627",
            name: "TYLER'S HIDDEN Sort",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1145627",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1134588",
            name: "Tommy WIP 2",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1134588",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1187429",
            name: "Tommy Sit & Stay",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1187429",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1187430",
            name: "Zahava WIP",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1187430",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1182358",
            name: "Family Matching Halloween",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1182358",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1152920",
            name: "Jason's WIP",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1152920",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1152924",
            name: "Jason's WIP #2",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1152924",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1157004",
            name: "AUTOPOP WIP",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1157004",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1156906",
            name: "Gender Neutral 2021 List",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1156906",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1163063",
            name: "Category Page Test V2",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1163063",
            hasSubDivision: false,
          },
          {
            parents: ["5910", "5151"],
            type: "category",
            id: "1172158",
            name: "Boys Plus",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1172158",
            customUrl:
              "/browse/category.do?cid=1172158&#pageId=0&department=16",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=5910",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "6241",
    name: "Toddler",
    children: [
      {
        type: "headerless-group",
        children: [
          {
            parents: ["6241", "5151"],
            type: "spacer",
            id: "1071896",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1071896",
            hasSubDivision: false,
          },
          {
            parents: ["6241", "5151"],
            type: "spacer",
            id: "1071923",
            name: "Spacer ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1071923",
            hasSubDivision: false,
          },
          {
            parents: ["6241", "5151"],
            type: "spacer",
            id: "1071926",
            name: "Spacer ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1071926",
            hasSubDivision: false,
          },
          {
            parents: ["6241", "5151"],
            type: "spacer",
            id: "1071924",
            name: "Spacer ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1071924",
            hasSubDivision: false,
          },
        ],
      },
      {
        parents: ["6241", "5151"],
        type: "header",
        id: "1071869",
        name: "START SHOPPING",
        children: [
          {
            parents: ["6241", "5151"],
            type: "category",
            id: "1071077",
            name: "Toddler Girls 12M-6T",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1071077",
            hasSubDivision: false,
          },
          {
            parents: ["6241", "5151"],
            type: "category",
            id: "1071078",
            name: "Toddler Boys 12M-6T",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1071078",
            hasSubDivision: false,
          },
          {
            parents: ["6241", "5151"],
            type: "category",
            id: "1086620",
            name: "Hidden Sort Krysti TB",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1086620",
            hasSubDivision: false,
          },
          {
            parents: ["6241", "5151"],
            type: "category",
            id: "1071255",
            name: "autopop test OW",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1071255",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=6241",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1068820",
    name: "Toddler Girls",
    children: [
      {
        parents: ["1068820", "5151"],
        type: "header",
        id: "1017563",
        name: "Shop by Size",
        children: [
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1017715",
            name: "Toddler Girls 12M-6T",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1017715",
            customUrl:
              "/browse/category.do?cid=1017715&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1071074",
            name: "Toddler Boys 12M-6T",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1071074",
            customUrl: "/browse/category.do?cid=37045",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=1017715&tlink=meganav%3AToddler%3AShop%20by%20Size%3AToddler%20Girls%2012M-6T#pageId=0&department=165",
        hasSubDivision: false,
      },
      {
        parents: ["1068820", "5151"],
        type: "header",
        id: "1040806",
        name: "Toddler Girls",
        children: [],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1068820", "5151"],
        type: "trimheader",
        id: "1073646",
        name: "New & Now ",
        children: [
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "6825",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6825",
            customUrl: "/browse/category.do?cid=6825&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1152020",
            name: "Everyday Magic: Styles Starting from $5",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1152020",
            customUrl:
              "/browse/category.do?cid=1152020&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "3007178",
            name: "$10 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007178",
            customUrl:
              "/browse/category.do?cid=3007178&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "3007342",
            name: "Spring Outfits for the Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007342",
            customUrl: "https://oldnavy.gap.com/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1167158",
            name: "Fashion Favorites ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1167158",
            customUrl:
              "/browse/category.do?cid=1167158&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1163211",
            name: "Unisex",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1163211",
            customUrl: "/browse/category.do?cid=1174665",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1124658",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1124658",
            customUrl:
              "/browse/category.do?cid=1124658&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1189660",
            name: "Matching Tops & Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189660",
            customUrl:
              "/browse/category.do?cid=1189660&#pageId=0&department=165",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=6825&tlink=meganav%3AToddler%3ANew%20%26%20Now%20%3ANew%20Arrivals#pageId=0&department=165",
        hasSubDivision: false,
      },
      {
        parents: ["1068820", "5151"],
        type: "trimheader",
        id: "1073647",
        name: "Shop Toddler Girls Categories",
        children: [
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1185224",
            name: "Shop All Toddler Girl's",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185224",
            customUrl:
              "/browse/category.do?cid=1185224&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "47926",
            name: "Dresses & Jumpsuits",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=47926",
            customUrl: "/browse/category.do?cid=47926&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1034893",
            name: "Multipacks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1034893",
            customUrl:
              "/browse/category.do?cid=1034893&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "6287",
            name: "Sets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6287",
            customUrl: "/browse/category.do?cid=6287&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1127046",
            name: "Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127046",
            customUrl:
              "/browse/category.do?cid=1127046&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "6285",
            name: "T-shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6285",
            customUrl: "/browse/category.do?cid=6285&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "36374",
            name: "Graphic T-Shirts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=36374",
            customUrl: "/browse/category.do?cid=36374&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1026838",
            name: "Blouses & Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1026838",
            customUrl:
              "/browse/category.do?cid=1026838&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1127051",
            name: "Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127051",
            customUrl:
              "/browse/category.do?cid=1127051&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "37227",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=37227",
            customUrl: "/browse/category.do?cid=37227&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "41941",
            name: "Leggings",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41941",
            customUrl: "/browse/category.do?cid=41941&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "63664",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=63664",
            customUrl: "/browse/category.do?cid=63664&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1044626",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1044626",
            customUrl:
              "/browse/category.do?cid=1044626&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "62284",
            name: "Sweaters",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=62284",
            customUrl: "/browse/category.do?cid=62284&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "53858",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=53858",
            customUrl: "/browse/category.do?cid=53858&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "50123",
            name: "Swimsuits",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=50123",
            customUrl: "/browse/category.do?cid=50123&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "6297",
            name: "Pajamas",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6297",
            customUrl: "/browse/category.do?cid=6297&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "37243",
            name: "Shoes",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=37243",
            customUrl: "/browse/category.do?cid=37243&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "42311",
            name: "Socks & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=42311",
            customUrl: "/browse/category.do?cid=42311&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "44500",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=44500",
            customUrl: "/browse/category.do?cid=44500&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1166627",
            name: "Toys & Books",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1166627",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/division.do?cid=6241&tlink=meganav%3AToddler%3AShop%20Toddler%20Girls%20Categories",
        hasSubDivision: false,
      },
      {
        parents: ["1068820", "5151"],
        type: "header",
        id: "54835",
        name: "Deals",
        children: [
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1187742",
            name: "1000s of Styles from $6",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1187742",
            customUrl:
              "/browse/category.do?cid=1187742&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1149354",
            name: "60% Off Fashion Faves",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1149354",
            customUrl:
              "/browse/category.do?cid=1149354&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1186906",
            name: "Deals from $8",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1186906",
            customUrl:
              "/browse/category.do?cid=1186906&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "sale",
            id: "97017",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=97017",
            customUrl: "/browse/category.do?cid=97017&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "sale",
            id: "26785",
            name: "SALE",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=26785",
            customUrl: "/browse/category.do?cid=26785&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "spacer",
            id: "1100222",
            name: "Spacer ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1100222",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=26785&tlink=meganav%3AToddler%3ADeals%3ASALE#pageId=0&department=165",
        hasSubDivision: false,
      },
      {
        parents: ["1068820", "5151"],
        type: "trimheader",
        id: "1069828",
        name: "Gift Cards ",
        children: [
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "35466",
            name: "Gift Cards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=35466",
            customUrl: "/customerService/info.do?cid=35433",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "spacer",
            id: "1100221",
            name: "Spacer ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1100221",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1160569",
            name: "Toddler Clothes",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1160569",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1163309",
            name: "Tommy WIP subcat",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1163309",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1163498",
            name: "Tommy WIP subcat v2",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1163498",
            hasSubDivision: false,
          },
          {
            parents: ["1068820", "5151"],
            type: "category",
            id: "1167901",
            name: "Sarah's Hidden Sort",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1167901",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1068820",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "6149",
    name: "Toddler Boys",
    children: [
      {
        parents: ["6149", "5151"],
        type: "header",
        id: "1071067",
        name: "Shop By Size",
        children: [
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "37045",
            name: "Toddler Boys 12M-6T",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=37045",
            customUrl: "/browse/category.do?cid=37045&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1071075",
            name: "Toddler Girls 12M-6T",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1071075",
            customUrl: "/browse/category.do?cid=1017715",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=37045&tlink=meganav%3AToddler%3AShop%20By%20Size%3AToddler%20Boys%2012M-6T#pageId=0&department=166",
        hasSubDivision: false,
      },
      {
        parents: ["6149", "5151"],
        type: "header",
        id: "1040807",
        name: "Toddler Boys",
        children: [],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["6149", "5151"],
        type: "trimheader",
        id: "1073756",
        name: "New & Now ",
        children: [
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "6157",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6157",
            customUrl: "/browse/category.do?cid=6157&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1152021",
            name: "Everyday Magic: Styles Starting from $5",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1152021",
            customUrl:
              "/browse/category.do?cid=1152021&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "3007211",
            name: "$10 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007211",
            customUrl:
              "/browse/category.do?cid=3007211&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "3007344",
            name: "Spring Outfits for the Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007344",
            customUrl: "https://oldnavy.gap.com/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1167163",
            name: "Fashion Favorites ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1167163",
            customUrl:
              "/browse/category.do?cid=1167163&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1163210",
            name: "Unisex",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1163210",
            customUrl: "/browse/category.do?cid=1174665",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1124659",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1124659",
            customUrl:
              "/browse/category.do?cid=1124659&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1189665",
            name: "Matching Tops & Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189665",
            customUrl:
              "/browse/category.do?cid=1189665&#pageId=0&department=166",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=6157&tlink=meganav%3AToddler%3ANew%20%26%20Now%20%3ANew%20Arrivals#pageId=0&department=166",
        hasSubDivision: false,
      },
      {
        parents: ["6149", "5151"],
        type: "trimheader",
        id: "1073744",
        name: "Shop Toddler Boys Categories",
        children: [
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1185225",
            name: "Shop All Toddler Boy's",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185225",
            customUrl:
              "/browse/category.do?cid=1185225&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1030394",
            name: "Multipacks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1030394",
            customUrl:
              "/browse/category.do?cid=1030394&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1180145",
            name: "Sets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1180145",
            customUrl:
              "/browse/category.do?cid=1180145&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1127056",
            name: "Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127056",
            customUrl:
              "/browse/category.do?cid=1127056&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "37195",
            name: "T-shirts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=37195",
            customUrl: "/browse/category.do?cid=37195&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "9619",
            name: "Graphic T-Shirts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=9619",
            customUrl: "/browse/category.do?cid=9619&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "41785",
            name: "Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41785",
            customUrl: "/browse/category.do?cid=41785&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1127055",
            name: "Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127055",
            customUrl:
              "/browse/category.do?cid=1127055&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "37255",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=37255",
            customUrl: "/browse/category.do?cid=37255&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "37257",
            name: "Pants ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=37257",
            customUrl: "/browse/category.do?cid=37257&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1075847",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1075847",
            customUrl:
              "/browse/category.do?cid=1075847&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1044462",
            name: "Sweatshirts & Sweatpants ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1044462",
            customUrl:
              "/browse/category.do?cid=1044462&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "53862",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=53862",
            customUrl: "/browse/category.do?cid=53862&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1075848",
            name: "Swimsuits ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1075848",
            customUrl:
              "/browse/category.do?cid=1075848&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "34722",
            name: "Pajamas",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=34722",
            customUrl: "/browse/category.do?cid=34722&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1036281",
            name: "Shoes",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1036281",
            customUrl:
              "/browse/category.do?cid=1036281&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "13130",
            name: "Socks & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=13130",
            customUrl: "/browse/category.do?cid=13130&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "40231",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=40231",
            customUrl: "/browse/category.do?cid=40231&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1166629",
            name: "Toys & Books",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1166629",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/division.do?cid=6241&tlink=meganav%3AToddler%3AShop%20Toddler%20Boys%20Categories",
        hasSubDivision: false,
      },
      {
        parents: ["6149", "5151"],
        type: "header",
        id: "1040818",
        name: "Deals",
        children: [
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1149356",
            name: "60% Off Fashion Faves",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1149356",
            customUrl:
              "/browse/category.do?cid=1149356&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1187091",
            name: "1000s of Styles from $6",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1187091",
            customUrl:
              "/browse/category.do?cid=1187091&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1185646",
            name: "Deals from $8",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185646",
            customUrl:
              "/browse/category.do?cid=1185646&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "sale",
            id: "53699",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=53699",
            customUrl: "/browse/category.do?cid=53699&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "sale",
            id: "26619",
            name: "SALE",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=26619",
            customUrl: "/browse/category.do?cid=26619&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "spacer",
            id: "1100223",
            name: "Spacer ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1100223",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=26619&tlink=meganav%3AToddler%3ADeals%3ASALE#pageId=0&department=166",
        hasSubDivision: false,
      },
      {
        parents: ["6149", "5151"],
        type: "trimheader",
        id: "1071868",
        name: "Gift Cards",
        children: [
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1071066",
            name: "Gift Cards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1071066",
            customUrl: "/customerService/info.do?cid=35433",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "spacer",
            id: "1107357",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1107357",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1020605",
            name: "Tyler's Sortwork Helper",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1020605",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1162833",
            name: "Tommy's WIP",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1162833",
            hasSubDivision: false,
          },
          {
            parents: ["6149", "5151"],
            type: "category",
            id: "1163576",
            name: "Tommy's WIP V2",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1163576",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=6149",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "51375",
    name: "Baby",
    children: [
      {
        type: "headerless-group",
        children: [
          {
            parents: ["51375", "5151"],
            type: "spacer",
            id: "1077213",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1077213",
            hasSubDivision: false,
          },
          {
            parents: ["51375", "5151"],
            type: "spacer",
            id: "1077215",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1077215",
            hasSubDivision: false,
          },
          {
            parents: ["51375", "5151"],
            type: "spacer",
            id: "1077217",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1077217",
            hasSubDivision: false,
          },
          {
            parents: ["51375", "5151"],
            type: "spacer",
            id: "1077219",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1077219",
            hasSubDivision: false,
          },
        ],
      },
      {
        parents: ["51375", "5151"],
        type: "header",
        id: "1077220",
        name: "START SHOPPING",
        children: [
          {
            parents: ["51375", "5151"],
            type: "category",
            id: "1077222",
            name: "Baby Girls 0-24M",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1077222",
            hasSubDivision: false,
          },
          {
            parents: ["51375", "5151"],
            type: "category",
            id: "1077223",
            name: "Baby Boys 0-24M",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1077223",
            hasSubDivision: false,
          },
          {
            parents: ["51375", "5151"],
            type: "spacer",
            id: "1006548",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1006548",
            hasSubDivision: false,
          },
          {
            parents: ["51375", "5151"],
            type: "spacer",
            id: "1006550",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1006550",
            hasSubDivision: false,
          },
          {
            parents: ["51375", "5151"],
            type: "category",
            id: "1049714",
            name: "Hidden Sorts",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1049714",
            hasSubDivision: false,
          },
          {
            parents: ["51375", "5151"],
            type: "category",
            id: "1049715",
            name: "Test Sorts",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1049715",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=51375",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1076495",
    name: "Baby Girls",
    children: [
      {
        parents: ["1076495", "5151"],
        type: "header",
        id: "1017553",
        name: "Shop by Size",
        children: [
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1035468",
            name: "Baby Girls 0-24M",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1035468",
            customUrl:
              "/browse/category.do?cid=1035468&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1077228",
            name: "Baby Boys 0-24M",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1077228",
            customUrl:
              "/browse/category.do?cid=1035469&#pageId=0&department=166",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=1035468&tlink=meganav%3ABaby%3AShop%20by%20Size%3ABaby%20Girls%200-24M#pageId=0&department=165",
        hasSubDivision: false,
      },
      {
        parents: ["1076495", "5151"],
        type: "header",
        id: "51296",
        name: "Baby Girls",
        children: [],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1076495", "5151"],
        type: "trimheader",
        id: "1077947",
        name: "New & Now ",
        children: [
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "37505",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=37505",
            customUrl: "/browse/category.do?cid=37505&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1152096",
            name: "Everyday Magic: Styles Starting from $5",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1152096",
            customUrl:
              "/browse/category.do?cid=1152096&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "3007182",
            name: "$10 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007182",
            customUrl:
              "/browse/category.do?cid=3007182&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "3007360",
            name: "Spring Outfits for the Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007360",
            customUrl: "https://oldnavy.gap.com/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1167172",
            name: "Fashion Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1167172",
            customUrl:
              "/browse/category.do?cid=1167172&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1163212",
            name: "Unisex",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1163212",
            customUrl: "/browse/category.do?cid=1174666",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1139620",
            name: "Newborn Essentials",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139620",
            customUrl:
              "/browse/category.do?cid=1139620&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1188508",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188508",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1189650",
            name: "Baby Gifts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189650",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=37505&tlink=meganav%3ABaby%3ANew%20%26%20Now%20%3ANew%20Arrivals#pageId=0&department=165",
        hasSubDivision: false,
      },
      {
        parents: ["1076495", "5151"],
        type: "trimheader",
        id: "1077949",
        name: "Shop Baby Girls Categories",
        children: [
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1185226",
            name: "Shop All Baby Girl's",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185226",
            customUrl:
              "/browse/category.do?cid=1185226&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "6269",
            name: "Dresses & Jumpsuits",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6269",
            customUrl: "/browse/category.do?cid=6269&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1141840",
            name: "Multipacks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1141840",
            customUrl:
              "/browse/category.do?cid=1141840&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "64257",
            name: "Sets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=64257",
            customUrl: "/browse/category.do?cid=64257&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "23270",
            name: "Bodysuits & Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=23270",
            customUrl: "/browse/category.do?cid=23270&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "54054",
            name: "One-Pieces",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=54054",
            customUrl: "/browse/category.do?cid=54054&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1127079",
            name: "Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127079",
            customUrl:
              "/browse/category.do?cid=1127079&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1038607",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1038607",
            customUrl:
              "/browse/category.do?cid=1038607&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1141674",
            name: "Leggings",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1141674",
            customUrl:
              "/browse/category.do?cid=1141674&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "92537",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=92537",
            customUrl: "/browse/category.do?cid=92537&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "73891",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=73891",
            customUrl: "/browse/category.do?cid=73891&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1141935",
            name: "Sweaters",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1141935",
            customUrl:
              "/browse/category.do?cid=1141935&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1142904",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1142904",
            customUrl:
              "/browse/category.do?cid=1142904&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "54738",
            name: "Swimsuits ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=54738",
            customUrl: "/browse/category.do?cid=54738&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "51737",
            name: "Pajamas",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=51737",
            customUrl: "/browse/category.do?cid=51737&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "37246",
            name: "Shoes",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=37246",
            customUrl: "/browse/category.do?cid=37246&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1127078",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127078",
            customUrl:
              "/browse/category.do?cid=1127078&#pageId=0&department=165",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/division.do?cid=51375&tlink=meganav%3ABaby%3AShop%20Baby%20Girls%20Categories",
        hasSubDivision: false,
      },
      {
        parents: ["1076495", "5151"],
        type: "header",
        id: "60396",
        name: "Deals",
        children: [
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1186929",
            name: "1000s of Styles from $6",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1186929",
            customUrl:
              "/browse/category.do?cid=1186929&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1161657",
            name: "60% Off Fashion Faves",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1161657",
            customUrl:
              "/browse/category.do?cid=1161657&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1185648",
            name: "Deals from $8",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185648",
            customUrl:
              "/browse/category.do?cid=1185648&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "sale",
            id: "96918",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=96918",
            customUrl: "/browse/category.do?cid=96918&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "sale",
            id: "51646",
            name: "SALE",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=51646",
            customUrl: "/browse/category.do?cid=51646&#pageId=0&department=165",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "spacer",
            id: "1107638",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1107638",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=51646&tlink=meganav%3ABaby%3ADeals%3ASALE#pageId=0&department=165",
        hasSubDivision: false,
      },
      {
        parents: ["1076495", "5151"],
        type: "trimheader",
        id: "1070200",
        name: "Gift Cards",
        children: [
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "51441",
            name: "Gift Cards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=51441",
            customUrl: "/customerService/info.do?cid=35433",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1159304",
            name: "Sarahs Hidden Sort ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1159304",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "spacer",
            id: "1100192",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1100192",
            hasSubDivision: false,
          },
          {
            parents: ["1076495", "5151"],
            type: "category",
            id: "1160573",
            name: "Baby Hats",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1160573",
            customUrl: "SEO CLP Testing",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1076495",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1076496",
    name: "Baby Boys",
    children: [
      {
        parents: ["1076496", "5151"],
        type: "header",
        id: "1077229",
        name: "Shop By Size",
        children: [
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1035469",
            name: "Baby Boys 0-24M",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1035469",
            customUrl:
              "/browse/category.do?cid=1035469&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1077230",
            name: "Baby Girls 0-24M",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1077230",
            customUrl: "/browse/category.do?cid=1035468",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=1035469&tlink=meganav%3ABaby%3AShop%20By%20Size%3ABaby%20Boys%200-24M#pageId=0&department=166",
        hasSubDivision: false,
      },
      {
        parents: ["1076496", "5151"],
        type: "header",
        id: "51294",
        name: "Baby Boys",
        children: [],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1076496", "5151"],
        type: "trimheader",
        id: "1078107",
        name: "New & Now ",
        children: [
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "37508",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=37508",
            customUrl: "/browse/category.do?cid=37508&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1152094",
            name: "Everyday Magic: Styles Starting from $5",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1152094",
            customUrl:
              "/browse/category.do?cid=1152094&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "3007203",
            name: "$10 & Under Steals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007203",
            customUrl:
              "/browse/category.do?cid=3007203&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "3007361",
            name: "Spring Outfits for the Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007361",
            customUrl: "https://oldnavy.gap.com/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1167173",
            name: "Fashion Favorites",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1167173",
            customUrl:
              "/browse/category.do?cid=1167173&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1163213",
            name: "Unisex",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1163213",
            customUrl: "/browse/category.do?cid=1174666",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1139637",
            name: "Newborn Essentials",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1139637",
            customUrl:
              "/browse/category.do?cid=1139637&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1188512",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1188512",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1189654",
            name: "Baby Gifts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189654",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=37508&tlink=meganav%3ABaby%3ANew%20%26%20Now%20%3ANew%20Arrivals#pageId=0&department=166",
        hasSubDivision: false,
      },
      {
        parents: ["1076496", "5151"],
        type: "trimheader",
        id: "1077950",
        name: "Shop Baby Boys Categories",
        children: [
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1185227",
            name: "Shop All Baby Boy's",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185227",
            customUrl:
              "/browse/category.do?cid=1185227&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1141846",
            name: "Multipacks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1141846",
            customUrl:
              "/browse/category.do?cid=1141846&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "64269",
            name: "Sets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=64269",
            customUrl: "/browse/category.do?cid=64269&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "23276",
            name: "Bodysuits & Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=23276",
            customUrl: "/browse/category.do?cid=23276&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "6177",
            name: "One-Pieces ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=6177",
            customUrl: "/browse/category.do?cid=6177&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1127083",
            name: "Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127083",
            customUrl:
              "/browse/category.do?cid=1127083&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "37258",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=37258",
            customUrl: "/browse/category.do?cid=37258&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1141698",
            name: "Leggings",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1141698",
            customUrl:
              "/browse/category.do?cid=1141698&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "92522",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=92522",
            customUrl: "/browse/category.do?cid=92522&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "73896",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=73896",
            customUrl: "/browse/category.do?cid=73896&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1141929",
            name: "Sweaters",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1141929",
            customUrl:
              "/browse/category.do?cid=1141929&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1142911",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1142911",
            customUrl:
              "/browse/category.do?cid=1142911&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "54743",
            name: "Swimsuits",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=54743",
            customUrl: "/browse/category.do?cid=54743&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "51738",
            name: "Pajamas",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=51738",
            customUrl: "/browse/category.do?cid=51738&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "37874",
            name: "Shoes",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=37874",
            customUrl: "/browse/category.do?cid=37874&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1127067",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1127067",
            customUrl:
              "/browse/category.do?cid=1127067&#pageId=0&department=166",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/division.do?cid=51375&tlink=meganav%3ABaby%3AShop%20Baby%20Boys%20Categories",
        hasSubDivision: false,
      },
      {
        parents: ["1076496", "5151"],
        type: "header",
        id: "1006541",
        name: "Deals",
        children: [
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1149457",
            name: "60% off Fashion Faves",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1149457",
            customUrl:
              "/browse/category.do?cid=1149457&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1186931",
            name: "1000s of Styles from $6",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1186931",
            customUrl:
              "/browse/category.do?cid=1186931&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1185650",
            name: "Deals from $8",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185650",
            customUrl:
              "/browse/category.do?cid=1185650&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "sale",
            id: "96919",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=96919",
            customUrl: "/browse/category.do?cid=96919&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "sale",
            id: "51666",
            name: "SALE",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=51666",
            customUrl: "/browse/category.do?cid=51666&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "spacer",
            id: "1100191",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1100191",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=51666&tlink=meganav%3ABaby%3ADeals%3ASALE#pageId=0&department=166",
        hasSubDivision: false,
      },
      {
        parents: ["1076496", "5151"],
        type: "trimheader",
        id: "1077235",
        name: "Gift Cards",
        children: [
          {
            parents: ["1076496", "5151"],
            type: "category",
            id: "1077236",
            name: "Gift Cards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1077236",
            customUrl: "/customerService/info.do?cid=35433",
            hasSubDivision: false,
          },
          {
            parents: ["1076496", "5151"],
            type: "spacer",
            id: "1100227",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1100227",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1076496",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "5758",
    name: "Maternity",
    children: [
      {
        parents: ["5758", "5151"],
        type: "header",
        id: "1065183",
        name: "New & Now ",
        children: [
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "8454",
            name: "New Arrivals",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=8454",
            customUrl: "/browse/category.do?cid=8454&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "3007866",
            name: "Matching Mother’s Day Looks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007866",
            customUrl: "/browse/category.do?cid=1156596",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1174344",
            name: "Everyday Magic: Styles Starting from $7",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174344",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "3007373",
            name: "Spring Outfits for the Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007373",
            customUrl: "https://oldnavy.gap.com/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=8454&tlink=meganav%3AMaternity%3ANew%20%26%20Now%20%3ANew%20Arrivals#pageId=0&department=136",
        hasSubDivision: false,
      },
      {
        parents: ["5758", "5151"],
        type: "header",
        id: "7791",
        name: "Ideas & Inspiration",
        children: [
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "56185",
            name: "Shop by Trimester",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=56185",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1178509",
            name: "Maternity Friendly Styles",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1178509",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1042532",
            name: "Maternity Starter Kit",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1042532",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1099220",
            name: "Hospital Bag Checklist",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1099220",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1001098",
            name: "Newborn & New Mom",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1001098",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=56185&tlink=meganav%3AMaternity%3AIdeas%20%26%20Inspiration%3AShop%20by%20Trimester",
        hasSubDivision: false,
      },
      {
        parents: ["5758", "5151"],
        type: "trimheader",
        id: "1036208",
        name: "Shop Maternity Categories",
        children: [
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1185228",
            name: "Shop All Maternity",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1185228",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1090999",
            name: "Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1090999",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "5798",
            name: "T-shirts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5798",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "48687",
            name: "Dresses & Jumpsuits ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=48687",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1126887",
            name: "Bottoms",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1126887",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "65527",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=65527",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "5848",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5848",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "5856",
            name: "Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5856",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "58363",
            name: "Leggings",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=58363",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1053229",
            name: "Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1053229",
            customUrl:
              "/browse/category.do?cid=1053229&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1161926",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1161926",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1177845",
            name: "Bras & Underwear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1177845",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "41578",
            name: "Nursing",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=41578",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "65081",
            name: "Swimsuits",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=65081",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "50200",
            name: "Coats & Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=50200",
            customUrl: "/browse/category.do?cid=55474",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "5816",
            name: "Sweaters",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5816",
            customUrl: "/browse/category.do?cid=20408",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "5874",
            name: "Pajamas & Loungewear ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=5874",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1189322",
            name: "Kits & Sets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189322",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/division.do?cid=5758&tlink=meganav%3AMaternity%3AShop%20Maternity%20Categories",
        hasSubDivision: false,
      },
      {
        parents: ["5758", "5151"],
        type: "header",
        id: "1092435",
        name: "Shoes & Accessories",
        children: [
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "92998",
            name: "Shoes",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=92998",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "92969",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=92969",
            customUrl: "/browse/category.do?cid=92969&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1085372",
            name: "Makeup",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1085372",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=92969&tlink=meganav%3AMaternity%3AShoes%20%26%20Accessories%3AAccessories#pageId=0&department=136",
        hasSubDivision: false,
      },
      {
        parents: ["5758", "5151"],
        type: "header",
        id: "1179148",
        name: "Home + Family ",
        children: [
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1179151",
            name: "Family Fun",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179151",
            customUrl:
              "/browse/category.do?cid=1179125&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1179159",
            name: "For the Home",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179159",
            customUrl:
              "/browse/category.do?cid=1179127&#pageId=0&department=136",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1179125&#pageId=0&department=136",
        hasSubDivision: false,
      },
      {
        parents: ["5758", "5151"],
        type: "header",
        id: "55185",
        name: "Deals",
        children: [
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1136836",
            name: "Deals from $8",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1136836",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "sale",
            id: "96921",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=96921",
            customUrl: "/browse/category.do?cid=96921&#pageId=0&department=136",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "sale",
            id: "26239",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=26239",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "spacer",
            id: "5880",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=5880",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl:
          "/browse/category.do?cid=26239&tlink=meganav%3AMaternity%3ADeals%3ASale",
        hasSubDivision: false,
      },
      {
        parents: ["5758", "5151"],
        type: "trimheader",
        id: "1100168",
        name: "Gift Cards",
        children: [
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "35462",
            name: "Gift Cards",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=35462",
            customUrl: "/customerService/info.do?cid=35433",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1018756",
            name: "BEST SELLER FLAGS",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1018756",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "spacer",
            id: "11282",
            name: "Spacer",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=11282",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1060295",
            name: "2nd-3rd Trimester HIDDEN",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1060295",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1060296",
            name: "Whole 9 Months HIDDEN",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1060296",
            hasSubDivision: false,
          },
          {
            parents: ["5758", "5151"],
            type: "category",
            id: "1123231",
            name: "Activewear",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1123231",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=5758",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1183013",
    name: "Family Outfits ",
    children: [
      {
        parents: ["1183013", "5151"],
        type: "trimheader",
        id: "1189249",
        name: "Fashion for the Fam",
        children: [
          {
            parents: ["1183013", "5151"],
            type: "category",
            id: "1189221",
            name: "Spring Outfits for the Family",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189221",
            hasSubDivision: false,
          },
          {
            parents: ["1183013", "5151"],
            type: "category",
            id: "1189186",
            name: "Family Swim",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189186",
            hasSubDivision: false,
          },
          {
            parents: ["1183013", "5151"],
            type: "category",
            id: "3007864",
            name: "Matching Mother’s Day Looks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=3007864",
            customUrl: "/browse/category.do?cid=1156596",
            hasSubDivision: false,
          },
          {
            parents: ["1183013", "5151"],
            type: "category",
            id: "1189220",
            name: "Family Pajamas",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189220",
            hasSubDivision: false,
          },
          {
            parents: ["1183013", "5151"],
            type: "category",
            id: "1189223",
            name: "Vacation Shop",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189223",
            hasSubDivision: false,
          },
          {
            parents: ["1183013", "5151"],
            type: "category",
            id: "1189219",
            name: "Family Activewear",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1189219",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1189220",
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1183013",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1174657",
    name: "Gender Neutral",
    children: [
      {
        parents: ["1174657", "5151"],
        type: "header",
        id: "1179650",
        name: "Featured Collections",
        children: [
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1179788",
            name: "Clothes in Common for All",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179788",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1179837",
            name: "Clothes in Common for Adult",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179837",
            customUrl:
              "/browse/category.do?cid=1179837&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1179791",
            name: "Clothes in Common for Kids",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1179791",
            customUrl:
              "/browse/category.do?cid=1179791&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1182471",
            name: "Clothes in Common for Toddler",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1182471",
            customUrl:
              "/browse/category.do?cid=1182471&#pageId=0&department=166",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1179788",
        hasSubDivision: false,
      },
      {
        parents: ["1174657", "5151"],
        type: "header",
        id: "1179665",
        name: "Shop Adult",
        children: [
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174663",
            name: "Shop All",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174663",
            customUrl:
              "/browse/category.do?cid=1174663&#pageId=0&department=2180",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174918",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174918",
            customUrl:
              "/browse/category.do?cid=1174918&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174916",
            name: "T-Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174916",
            customUrl:
              "/browse/category.do?cid=1174916&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1176466",
            name: "Shirts ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1176466",
            customUrl:
              "/browse/category.do?cid=1176466&#pageId=0&department=75",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1187528",
            name: "Sweaters ",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1187528",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174920",
            name: "Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174920",
            customUrl:
              "/browse/category.do?cid=1174920&#pageId=0&department=75",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1174663",
        hasSubDivision: false,
      },
      {
        parents: ["1174657", "5151"],
        type: "header",
        id: "1179666",
        name: "Shop Kids",
        children: [
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174664",
            name: "Shop All",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174664",
            customUrl:
              "/browse/category.do?cid=1174664&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174919",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174919",
            customUrl:
              "/browse/category.do?cid=1174919&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174917",
            name: "T-Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174917",
            customUrl:
              "/browse/category.do?cid=1174917&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1177703",
            name: "Jackets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1177703",
            customUrl:
              "/browse/category.do?cid=1177703&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1177254",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1177254",
            customUrl:
              "/browse/category.do?cid=1177254&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174609",
            name: "Jeans & Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174609",
            customUrl:
              "/browse/category.do?cid=1174609&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174922",
            name: "Pajamas",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174922",
            customUrl:
              "/browse/category.do?cid=1174922&#pageId=0&department=16",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174923",
            name: "Shoes & Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174923",
            customUrl:
              "/browse/category.do?cid=1174923&#pageId=0&department=16",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1174664",
        hasSubDivision: false,
      },
      {
        parents: ["1174657", "5151"],
        type: "header",
        id: "1179667",
        name: "Shop Toddler",
        children: [
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174665",
            name: "Shop All",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174665",
            customUrl:
              "/browse/category.do?cid=1174665&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174942",
            name: "T-Shirts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174942",
            customUrl:
              "/browse/category.do?cid=1174942&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174943",
            name: "Sweatshirts & Sweatpants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174943",
            customUrl:
              "/browse/category.do?cid=1174943&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1176413",
            name: "Jeans",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1176413",
            customUrl:
              "/browse/category.do?cid=1176413&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1176412",
            name: "Shorts",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1176412",
            customUrl:
              "/browse/category.do?cid=1176412&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174944",
            name: "Jackets & Layering",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174944",
            customUrl:
              "/browse/category.do?cid=1174944&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174945",
            name: "Pajamas",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174945",
            customUrl:
              "/browse/category.do?cid=1174945&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174946",
            name: "Multipacks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174946",
            customUrl:
              "/browse/category.do?cid=1174946&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174947",
            name: "Shoes & Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174947",
            customUrl:
              "/browse/category.do?cid=1174947&#pageId=0&department=166",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1174665",
        hasSubDivision: false,
      },
      {
        parents: ["1174657", "5151"],
        type: "header",
        id: "1179668",
        name: "Shop Baby",
        children: [
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174666",
            name: "Shop All",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174666",
            customUrl:
              "/browse/category.do?cid=1174666&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174948",
            name: "Bodysuits & Tops",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174948",
            customUrl:
              "/browse/category.do?cid=1174948&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174949",
            name: "One-Pieces & Sets",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174949",
            customUrl:
              "/browse/category.do?cid=1174949&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174951",
            name: "Leggings & Pants",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174951",
            customUrl:
              "/browse/category.do?cid=1174951&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174955",
            name: "New Baby Essentials",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174955",
            customUrl:
              "/browse/category.do?cid=1174955&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174952",
            name: "Jackets & Layering",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174952",
            customUrl:
              "/browse/category.do?cid=1174952&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174953",
            name: "Pajamas",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174953",
            customUrl:
              "/browse/category.do?cid=1174953&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174954",
            name: "Multipacks",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174954",
            customUrl:
              "/browse/category.do?cid=1174954&#pageId=0&department=166",
            hasSubDivision: false,
          },
          {
            parents: ["1174657", "5151"],
            type: "category",
            id: "1174956",
            name: "Shoes & Accessories",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1174956",
            customUrl:
              "/browse/category.do?cid=1174956&#pageId=0&department=166",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=1174666",
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1174657",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1183117",
    name: "Sale",
    children: [
      {
        parents: ["1183117", "5151"],
        type: "header",
        id: "1183119",
        name: "Women & Women's Plus",
        children: [
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183120",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183120",
            customUrl: "/browse/category.do?cid=26190",
            hasSubDivision: false,
          },
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183121",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183121",
            customUrl: "/browse/category.do?cid=96964",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=26190",
        hasSubDivision: false,
      },
      {
        parents: ["1183117", "5151"],
        type: "header",
        id: "1183123",
        name: "Maternity",
        children: [
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183122",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183122",
            customUrl: "/browse/category.do?cid=26239",
            hasSubDivision: false,
          },
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183124",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183124",
            customUrl: "/browse/category.do?cid=96921",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=26190",
        hasSubDivision: false,
      },
      {
        parents: ["1183117", "5151"],
        type: "header",
        id: "1183125",
        name: "Men",
        children: [
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183126",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183126",
            customUrl: "/browse/category.do?cid=26061",
            hasSubDivision: false,
          },
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183127",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183127",
            customUrl: "/browse/category.do?cid=97035",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=26061",
        hasSubDivision: false,
      },
      {
        parents: ["1183117", "5151"],
        type: "header",
        id: "1183128",
        name: "Girls",
        children: [
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183129",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183129",
            customUrl: "/browse/category.do?cid=26175",
            hasSubDivision: false,
          },
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183130",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183130",
            customUrl: "/browse/category.do?cid=96906",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=26175",
        hasSubDivision: false,
      },
      {
        parents: ["1183117", "5151"],
        type: "header",
        id: "1183131",
        name: "Boys",
        children: [
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183132",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183132",
            customUrl: "/browse/category.do?cid=26073",
            hasSubDivision: false,
          },
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183133",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183133",
            customUrl: "/browse/category.do?cid=96945",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=26073",
        hasSubDivision: false,
      },
      {
        parents: ["1183117", "5151"],
        type: "header",
        id: "1183143",
        name: "Toddler Girls",
        children: [
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183135",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183135",
            customUrl: "/browse/category.do?cid=26785",
            hasSubDivision: false,
          },
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183136",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183136",
            customUrl: "/browse/category.do?cid=97017",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=26785",
        hasSubDivision: false,
      },
      {
        parents: ["1183117", "5151"],
        type: "header",
        id: "1183145",
        name: "Toddler Boys",
        children: [
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183138",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183138",
            customUrl: "/browse/category.do?cid=26619",
            hasSubDivision: false,
          },
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183139",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183139",
            customUrl: "/browse/category.do?cid=53699",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=26619",
        hasSubDivision: false,
      },
      {
        parents: ["1183117", "5151"],
        type: "header",
        id: "1183146",
        name: "Baby Girls",
        children: [
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183140",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183140",
            customUrl: "/browse/category.do?cid=51646",
            hasSubDivision: false,
          },
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183142",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183142",
            customUrl: "/browse/category.do?cid=96918",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=51646",
        hasSubDivision: false,
      },
      {
        parents: ["1183117", "5151"],
        type: "header",
        id: "1183147",
        name: "Baby Boys",
        children: [
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183141",
            name: "Sale",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183141",
            customUrl: "/browse/category.do?cid=51666",
            hasSubDivision: false,
          },
          {
            parents: ["1183117", "5151"],
            type: "category",
            id: "1183148",
            name: "Clearance",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1183148",
            customUrl: "/browse/category.do?cid=96919",
            hasSubDivision: false,
          },
        ],
        hidden: false,
        selected: false,
        customUrl: "/browse/category.do?cid=51666",
        hasSubDivision: false,
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1183117",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "68656",
    name: "PROMO EXCLUSIONS",
    children: [
      {
        type: "headerless-group",
        children: [
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "69248",
            name: "Dresses (All Divs)",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=69248",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1012870",
            name: "Womens Division",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1012870",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1012852",
            name: "Boys Division",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1012852",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1012856",
            name: "Girls Division",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1012856",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1012860",
            name: "Baby Girl",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1012860",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1012862",
            name: "Toddler Girl",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1012862",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1012863",
            name: "Toddler Boy",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1012863",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1012872",
            name: "Maternity",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1012872",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1012873",
            name: "Mens",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1012873",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1052365",
            name: "Baby Boy",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1052365",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1019214",
            name: "Womens Accessories",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1019214",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1052358",
            name: "Women's Plus ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1052358",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1027373",
            name: "Jeans (All Divs)",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1027373",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1031372",
            name: "Category Tag: Active by Old Navy ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1031372",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1058184",
            name: "EXCLUDES Kids/Baby, HD",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1058184",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1058788",
            name: "Auto Apply Clearance Exclusion CID",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1058788",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1068365",
            name: "Jewelry",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1068365",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1080953",
            name: "Regular Price Styles ",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1080953",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1088144",
            name: "CDA EVERGREEN EXCLUSIONS: HD,TOD/2DW,BS,LP,Beauty,FM,EM",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1088144",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1090813",
            name: "US | TOD/2DW, Face Masks, Jewelry, Sunglasses, Third Party",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1090813",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1091059",
            name: "US | Evergreen Exclusions: HD, BS, HIN, TOD/2DW, EDM, Face Masks, Sunglasses, Jewelry, Third Party",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1091059",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1095549",
            name: "Licensed Product",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1095549",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1118167",
            name: "Kids View Graphics Filter",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1118167",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1158280",
            name: "US | Face Masks",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1158280",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1184665",
            name: "US | Long-Term All TODs",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1184665",
            hasSubDivision: false,
          },
          {
            parents: ["68656", "5151"],
            type: "category",
            id: "1187965",
            name: "US | Non-Standard Exclusions: BS, TOD/2DW, EDM, Face Masks, Jewelry, Sunglasses, Third Party",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1187965",
            hasSubDivision: false,
          },
        ],
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=68656",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1040594",
    name: "Division Sandbox",
    children: [
      {
        type: "headerless-group",
        children: [
          {
            parents: ["1040594", "5151"],
            type: "category",
            id: "1042089",
            name: "Mexico_Women",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1042089",
            hasSubDivision: false,
          },
          {
            parents: ["1040594", "5151"],
            type: "category",
            id: "1042091",
            name: "Mexico_Men",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1042091",
            hasSubDivision: false,
          },
          {
            parents: ["1040594", "5151"],
            type: "category",
            id: "1042095",
            name: "Mexico_Baby",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1042095",
            hasSubDivision: false,
          },
          {
            parents: ["1040594", "5151"],
            type: "category",
            id: "1047323",
            name: "Japan_Men",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1047323",
            hasSubDivision: false,
          },
          {
            parents: ["1040594", "5151"],
            type: "category",
            id: "1047431",
            name: "Japan_Women",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1047431",
            hasSubDivision: false,
          },
          {
            parents: ["1040594", "5151"],
            type: "category",
            id: "1047432",
            name: "Japan_Girls",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1047432",
            hasSubDivision: false,
          },
        ],
      },
      {
        parents: ["1040594", "5151"],
        type: "header",
        id: "1073075",
        name: "Search Colors",
        children: [
          {
            parents: ["1040594", "5151"],
            type: "category",
            id: "1073079",
            name: "Red",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1073079",
            hasSubDivision: false,
          },
          {
            parents: ["1040594", "5151"],
            type: "category",
            id: "1110787",
            name: "Copy of New Arrivals",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1110787",
            hasSubDivision: false,
          },
          {
            parents: ["1040594", "5151"],
            type: "category",
            id: "1090995",
            name: "DPG Template Test",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/info.do?cid=1090995",
            hasSubDivision: false,
          },
          {
            parents: ["1040594", "5151"],
            type: "category",
            id: "1172821",
            name: "Test Category US",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1172821",
            hasSubDivision: false,
          },
        ],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1040594",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1092178",
    name: "Optimizely Training",
    children: [
      {
        type: "headerless-group",
        children: [
          {
            parents: ["1092178", "5151"],
            type: "category",
            id: "1158261",
            name: "Warehouse Sale",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1158261",
            hasSubDivision: false,
          },
        ],
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1092178",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1118141",
    name: "hillcity.com",
    children: [],
    hidden: false,
    selected: false,
    link: "/browse/categorySearch.do?cid=1118141",
    hasSubDivision: false,
    brandCode: "36",
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1169854",
    name: "Something For Everyone",
    children: [
      {
        parents: ["1169854", "5151"],
        type: "header",
        id: "1169857",
        name: "Family Matching",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
      {
        parents: ["1169854", "5151"],
        type: "header",
        id: "1169856",
        name: "Gender Neutral Styles",
        children: [],
        hidden: true,
        selected: false,
        hasSubDivision: false,
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=1169854",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "1175026",
    name: "Our Values",
    children: [
      {
        type: "headerless-group",
        children: [
          {
            parents: ["1175026", "5151"],
            type: "category",
            id: "1175053",
            name: "Imagine Mission",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/info.do?cid=1175053",
            customUrl: "/browse/info.do?cid=1160383",
            hasSubDivision: false,
          },
        ],
      },
    ],
    hidden: false,
    selected: false,
    link: "/browse/division.do?cid=1175026",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "3007128",
    name: "Men's Archive Division",
    children: [
      {
        type: "headerless-group",
        children: [
          {
            parents: ["3007128", "5151"],
            type: "category",
            id: "3007129",
            name: "Archive 01",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=3007129",
            hasSubDivision: false,
          },
        ],
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=3007128",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "3007183",
    name: "Women's Archive Division",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=3007183",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "3007296",
    name: "Boy's Archive Division",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=3007296",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "3007297",
    name: "Girl's Archive Division",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=3007297",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "3007298",
    name: "Toddler Boys Archive Division",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=3007298",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "3007299",
    name: "Baby Boys Archive Division",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=3007299",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "3007300",
    name: "Maternity Archive Division",
    children: [
      {
        type: "headerless-group",
        children: [
          {
            parents: ["3007300", "5151"],
            type: "category",
            id: "1066139",
            name: "Jewelry",
            children: [],
            hidden: false,
            selected: false,
            link: "/browse/category.do?cid=1066139",
            hasSubDivision: false,
          },
          {
            parents: ["3007300", "5151"],
            type: "category",
            id: "1060510",
            name: "Americana HIDDEN",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1060510",
            hasSubDivision: false,
          },
          {
            parents: ["3007300", "5151"],
            type: "category",
            id: "1122082",
            name: "Staging",
            children: [],
            hidden: true,
            selected: false,
            link: "/browse/category.do?cid=1122082",
            hasSubDivision: false,
          },
        ],
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=3007300",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "3007303",
    name: "Toddler Girls Archive Division",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=3007303",
    hasSubDivision: false,
  },
  {
    parents: ["5151"],
    type: "division",
    id: "3007304",
    name: "Baby Girls Archive Division",
    children: [
      {
        type: "headerless-group",
      },
    ],
    hidden: true,
    selected: false,
    link: "/browse/division.do?cid=3007304",
    hasSubDivision: false,
  },
];

export default data;
