// @ts-nocheck
import { render, fireEvent, screen, within, waitFor, act } from "test-utils";
import React from "react";
import generateMegaNavProps from "../../../adapter";
import * as keyboardNavigation from "..";
jest.mock("..", () => {
	const orig = jest.requireActual("..");
return {...orig,
__esModule:true};
})
import { NavDivisions } from "../../../NavDivisions";
import atMegaNavContent from "../../../fixtures/at-mega-nav-content.json";
import atWebHierarchy from "../../../fixtures/atWebHierarchy";
import { INCREASE_TIMEOUT } from "@ecom-next/plp-ui/legacy/utils";

const ESCAPE = { keyCode: 27 };
const SPACEBAR = { keyCode: 32 };
const LEFT_KEY = { keyCode: 37 };
const RIGHT_KEY = { keyCode: 39 };
const DOWN_KEY = { keyCode: 40 };
const UP_KEY = { keyCode: 38 };

describe("when meganav is navigated using the keyboard", () => {
  const topNavData = atMegaNavContent.data;
  const abSeg = {};
  const enabledFeatures = {};
  const megaNavProps = generateMegaNavProps(
    {
      webHierarchy: atWebHierarchy,
      data: topNavData,
      selectedNodes: [],
      criticalCss: [],
      brandName: "at",
    },
    false,
    enabledFeatures,
    abSeg
  );
  const focusTopNav = () => {
    const navdivisions = screen.getByTestId("topnav-navdivisions");
    fireEvent.focus(navdivisions);
  };

  let component;

  beforeEach(() => {
    jest.spyOn(keyboardNavigation, "default");
    component = render(
      <NavDivisions
        abSeg={abSeg}
        divisions={megaNavProps.divisions}
        enabledFeatures={enabledFeatures}
        fetchMegaNavData={jest.fn()}
      />
    );
    focusTopNav();
  });

  const getDivisionLink = (role, name) =>
    screen.getAllByRole(role, {
      name,
    })[0];

  const getFirstDivision = () => getDivisionLink("button", /new arrivals/i);
  const getSecondDivision = () => getDivisionLink("button", /activity/i);
  const getLastDivision = () => getDivisionLink("button", /sale/i);

  describe("when on the topnav bar", () => {
    describe("when spacebar is used to open a flyout", () => {
      beforeEach(async () => {
        await waitFor(() =>
          fireEvent.keyDown(
            getDivisionLink("button", /new arrivals/i),
            SPACEBAR
          )
        );
      }, INCREASE_TIMEOUT);
      it(
        "should render a flyout for the division",
        async () => {
          await waitFor(() =>
            expect(screen.getByTestId("megaNav")).toBeInTheDocument()
          );
        },
        INCREASE_TIMEOUT
      );

      it(
        "should close the division flyout when pressed again",
        async () => {
          await waitFor(() =>
            expect(screen.getByTestId("megaNav")).toHaveStyleRule(
              "display",
              "block"
            )
          );
          fireEvent.keyDown(
            getDivisionLink("button", /new arrivals/i),
            SPACEBAR
          );
          await waitFor(() =>
            expect(screen.getByTestId("megaNav")).toHaveStyleRule(
              "display",
              "none"
            )
          );
        },
        INCREASE_TIMEOUT
      );
    });

    describe("when the right arrow is pressed", () => {
      it("should focus the next division link if a division link is after", () => {
        getFirstDivision().focus();
        fireEvent.keyDown(getFirstDivision(), RIGHT_KEY);

        expect(getSecondDivision()).toHaveFocus();
      });

      it("should retain focus on last division when no following division exists", () => {
        component.divsionLinkRefIndex = topNavData.length;

        getLastDivision().focus();
        fireEvent.keyDown(getLastDivision(), RIGHT_KEY);

        expect(getLastDivision()).toHaveFocus();
      });

      it(
        "should close previous flyout when using right key to navigate to next",
        async () => {
          fireEvent.keyDown(getFirstDivision(), SPACEBAR);
          fireEvent.keyDown(getFirstDivision(), RIGHT_KEY);
          await waitFor(() => {
            expect(screen.getByText("All New Arrivals")).not.toBeVisible();
          });
        },
        INCREASE_TIMEOUT
      );
    });

    describe("when the left arrow is pressed", () => {
      it("should retain focus on first division if no division precedes it", () => {
        getFirstDivision().focus();

        fireEvent.keyDown(getFirstDivision(), LEFT_KEY);

        expect(getFirstDivision()).toHaveFocus();
      });

      it("should move focus to previous division if one exists", () => {
        const previousButton = getDivisionLink("button", /athleta girl/i);
        getLastDivision().focus();
        fireEvent.keyDown(getLastDivision(), LEFT_KEY);

        expect(previousButton).toHaveFocus();
      });

      it("should close current flyout when navigating to previous division", () => {
        fireEvent.keyDown(getLastDivision(), SPACEBAR);

        fireEvent.keyDown(getLastDivision(), LEFT_KEY);

        expect(screen.getByText("All Sale")).not.toBeVisible();
        expect(screen.getByText("All Girl")).toBeVisible();
      });

      it("should not open flyout of the division we are navigating to when flyout of the current division is not open", () => {
        fireEvent.keyDown(getLastDivision(), LEFT_KEY);

        expect(screen.queryByText("All Sale")).not.toBeInTheDocument();
      });
    });

    describe("when a mix of left and right arrows are pressed", () => {
      it("should correctly navigate using both left and right arrows", () => {
        getFirstDivision().focus();
        fireEvent.keyDown(getFirstDivision(), RIGHT_KEY);
        fireEvent.keyDown(getSecondDivision(), LEFT_KEY);
        fireEvent.keyDown(getFirstDivision(), RIGHT_KEY);

        expect(getSecondDivision()).toHaveFocus();
      });
    });

    describe("when escape key is used on a division link", () => {
      beforeEach(() => {
        getFirstDivision().focus();
        fireEvent.keyDown(getFirstDivision(), SPACEBAR);
        fireEvent.keyDown(getFirstDivision(), ESCAPE);
      });
      it("should close the open flyout", () => {
        expect(screen.getByText("All New Arrivals")).not.toBeVisible();
      });

      it("should retain focus on current division after closing the open flyout", () => {
        expect(getFirstDivision()).toHaveFocus();
      });
    });

    describe("when down arrow key is used to navigate the meganav divisions", () => {
      it("should move focus to a category link if flyout exists", () => {
        fireEvent.keyDown(getFirstDivision(), SPACEBAR);
        fireEvent.keyDown(getFirstDivision(), DOWN_KEY);

        expect(
          screen.getByRole("link", { name: "categories all new arrivals" })
        ).toHaveFocus();
      });
    });

    describe("when up arrow key is used to navigate the meganav divisions", () => {
      it("should retain focus on current division", () => {
        getFirstDivision().focus();
        fireEvent.keyDown(getFirstDivision(), UP_KEY);

        expect(getFirstDivision()).toHaveFocus();
      });
    });
  });

  describe("when on flyout", () => {
    const getFirstCategory = () =>
      screen.getByRole("link", {
        name: "categories all new arrivals",
      });
    const getSecondCategory = () =>
      screen.getByRole("link", {
        name: "categories bottoms",
      });
    describe("when down arrow key is pressed", () => {
      beforeEach(() => {
        fireEvent.keyDown(getFirstDivision(), SPACEBAR);
        fireEvent.keyDown(getFirstDivision(), DOWN_KEY);
      });

      it("should move focus to the category below", () => {
        fireEvent.keyDown(getFirstCategory(), DOWN_KEY);
        expect(getSecondCategory()).toHaveFocus();
      });

      it("should move focus to next column when pressed on last category link", () => {
        const firstColLastLink = screen.getByRole("link", {
          name: "categories athleta girl new arrivals",
        });
        const secondColFirstLink = screen.getByRole("link", {
          name: "featured shops sustainable favorites",
        });

        firstColLastLink.focus();
        fireEvent.keyDown(firstColLastLink, DOWN_KEY);

        expect(secondColFirstLink).toHaveFocus();
      });

      it("should open the next flyout when pressed on last category link", () => {
        const lastLink = screen.getByRole("link", {
          name: "featured shops best sellers",
        });

        lastLink.focus();
        fireEvent.keyDown(lastLink, DOWN_KEY);

        expect(screen.getByText("HOW WE INNOVATE")).toBeVisible();
      });

      it("should move focus to next division link when pressed on last category in the last column", () => {
        const lastLink = screen.getByRole("link", {
          name: "featured shops best sellers",
        });

        lastLink.focus();
        fireEvent.keyDown(lastLink, DOWN_KEY);
        expect(getSecondDivision()).toHaveFocus();
      });
    });

    describe("when up arrow key is pressed", () => {
      beforeEach(() => {
        fireEvent.keyDown(getFirstDivision(), SPACEBAR);
        getSecondCategory().focus();
      });

      it("should move focus to the category above", () => {
        fireEvent.keyDown(getSecondCategory(), UP_KEY);
        expect(getFirstCategory()).toHaveFocus();
      });

      describe("when navigating between two columns", () => {
        it("should move focus to last element of first column when pressed on first element of second column", () => {
          const jackets = getDivisionLink("button", /jackets/i);
          fireEvent.keyDown(jackets, SPACEBAR);

          const secondColFirstLink = within(jackets.closest("li")).getByRole(
            "link",
            {
              name: "special sizes plus",
            }
          );

          const firstColLastLink = within(jackets.closest("li")).getByRole(
            "link",
            {
              name: "categories new markdowns",
            }
          );

          secondColFirstLink.focus();
          fireEvent.keyDown(secondColFirstLink, UP_KEY);

          expect(firstColLastLink).toHaveFocus();
        });
      });

      describe("when moving from the first category link in a category group to the preceding category group with split columns", () => {
        it("should move focus to last element of second column of the preceding category group", () => {
          const jackets = getDivisionLink("button", /jackets/i);
          fireEvent.keyDown(jackets, SPACEBAR);
          const secondColFirstLink = within(jackets.closest("li")).getByRole(
            "link",
            {
              name: "special sizes plus",
            }
          );
          const secondColLastLink = within(jackets.closest("li")).getByRole(
            "link",
            {
              name: "categories new markdowns",
            }
          );
          secondColFirstLink.focus();
          fireEvent.keyDown(secondColFirstLink, UP_KEY);
          expect(secondColLastLink).toHaveFocus();
        });
      });

      it("should move focus to current division link when pressed on first category", () => {
        getFirstCategory().focus();
        fireEvent.keyDown(getFirstCategory(), UP_KEY);

        expect(getFirstDivision()).toHaveFocus();
      });
    });

    describe("when left arrow key is pressed", () => {
      beforeEach(() => {
        fireEvent.keyDown(getFirstDivision(), SPACEBAR);

        getFirstCategory().focus();
      });

      describe("when navigating between split columns", () => {
        it("should move focus to the first category in the left column", () => {
          const splitColumns = screen.getAllByRole("list");
          const rightColumn = splitColumns[1];

          const rightCategory = within(rightColumn).getAllByRole("link")[0];
          rightCategory.focus();

          fireEvent.keyDown(rightCategory, LEFT_KEY);

          const leftColumn = splitColumns[0];
          const leftCategory = within(leftColumn).getAllByRole("link")[0];

          expect(leftCategory).toHaveFocus();
        });
      });

      describe("when navigating to a column with split columns", () => {
        it("should move focus to the first category in the rightmost split column", () => {
          const firstLinkInRightmostColumnInFirstMeganavColumn =
            screen.getByRole("link", { name: "categories jackets" });
          const firstCategoryInSecondMeganavColumn = screen.getByRole("link", {
            name: "featured shops sustainable favorites",
          });

          firstCategoryInSecondMeganavColumn.focus();
          fireEvent.keyDown(firstCategoryInSecondMeganavColumn, LEFT_KEY);

          expect(firstLinkInRightmostColumnInFirstMeganavColumn).toHaveFocus();
        });
      });

      describe("when navigating to non-split meganav columns", () => {
        it("should move focus to the first category in the previous meganav column", () => {
          fireEvent.keyDown(getLastDivision(), SPACEBAR);

          const leftCategory = screen.getByRole("link", {
            name: "featured shops new markdowns",
          });
          const rightCategory = screen.getByRole("link", {
            name: "categories all sale",
          });

          fireEvent.keyDown(rightCategory, LEFT_KEY);
          expect(leftCategory).toHaveFocus();
        });
      });
    });

    describe("when right arrow key is pressed", () => {
      beforeEach(() => {
        fireEvent.keyDown(getFirstDivision(), SPACEBAR);
        getFirstCategory().focus();
      });

      describe("when navigating between split columns", () => {
        it("should move focus to the first category in the right column", () => {
          const leftCategory = screen.getByRole("link", {
            name: "categories all new arrivals",
          });
          fireEvent.keyDown(leftCategory, RIGHT_KEY);
          const rightCategory = screen.getByRole("link", {
            name: "categories jackets",
          });
          expect(rightCategory).toHaveFocus();
        });
      });

      describe("when navigating to non-split meganav columns", () => {
        it("should move focus to the first category in the right meganav column", () => {
          fireEvent.keyDown(getLastDivision(), SPACEBAR);
          const leftCategory = screen.getByRole("link", {
            name: "featured shops new markdowns",
          });
          fireEvent.keyDown(leftCategory, RIGHT_KEY);
          const rightCategory = screen.getByRole("link", {
            name: "categories all sale",
          });
          expect(rightCategory).toHaveFocus();
        });
      });
    });

    describe("when escape key is used within the open flyout", () => {
      it("should close flyout", () => {
        getFirstDivision().focus();
        fireEvent.keyDown(getFirstDivision(), SPACEBAR);
        fireEvent.keyDown(getFirstDivision(), DOWN_KEY);
        getFirstCategory().focus();
        fireEvent.keyDown(getFirstCategory(), ESCAPE);
        expect(screen.getByTestId("megaNav")).toHaveStyleRule(
          "display",
          "none"
        );
      });
    });
  });
});
