// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Search Response transformation > With Products > When we have didYouMean message > should call the search header response mapper to adapt the data 1`] = `
{
  "autoCorrectedText": "disney",
  "facetData": {
    "appliedFacets": {
      "color": [],
      "department": [
        {
          "applied": "true",
          "count": 29,
          "facetName": "department",
          "id": "Gender Neutral",
          "isActive": "true",
          "localeName": "Gender Neutral",
          "name": "Gender Neutral",
          "value": "Gender Neutral",
        },
      ],
      "price": [],
      "reviewScore": [],
      "size": [],
      "sleeveLength": [],
      "style": [],
    },
    "facetOptions": [
      {
        "displayName": "department",
        "facetDisplay": "radio",
        "facetLayout": "list",
        "isActive": undefined,
        "localizedName": "department",
        "name": "department",
        "options": [
          {
            "count": 69,
            "facetName": "department",
            "id": "Baby Boys",
            "isActive": "true",
            "localeName": "Baby Boys",
            "name": "Baby Boys",
            "value": "Baby Boys",
          },
          {
            "count": 56,
            "facetName": "department",
            "id": "Baby Girls",
            "isActive": "true",
            "localeName": "Baby Girls",
            "name": "Baby Girls",
            "value": "Baby Girls",
          },
          {
            "count": 46,
            "facetName": "department",
            "id": "Boys",
            "isActive": "true",
            "localeName": "Boys",
            "name": "Boys",
            "value": "Boys",
          },
          {
            "applied": "true",
            "count": 29,
            "facetName": "department",
            "id": "Gender Neutral",
            "isActive": "true",
            "localeName": "Gender Neutral",
            "name": "Gender Neutral",
            "value": "Gender Neutral",
          },
          {
            "count": 54,
            "facetName": "department",
            "id": "Girls",
            "isActive": "true",
            "localeName": "Girls",
            "name": "Girls",
            "value": "Girls",
          },
          {
            "count": 4,
            "facetName": "department",
            "id": "Men",
            "isActive": "true",
            "localeName": "Men",
            "name": "Men",
            "value": "Men",
          },
        ],
        "order": 1,
        "selectionType": "single-select",
        "type": "simple",
      },
      {
        "displayName": "style",
        "facetDisplay": "checkbox",
        "facetLayout": "list",
        "isActive": undefined,
        "localizedName": "style",
        "name": "style",
        "options": [
          {
            "count": 3,
            "facetName": "style",
            "id": "Accessories",
            "isActive": "true",
            "localeName": "Accessories",
            "name": "Accessories",
            "value": "Accessories",
          },
          {
            "count": 4,
            "facetName": "style",
            "id": "Backpack",
            "isActive": "true",
            "localeName": "Backpack",
            "name": "Backpack",
            "value": "Backpack",
          },
          {
            "count": 1,
            "facetName": "style",
            "id": "Bags",
            "isActive": "true",
            "localeName": "Bags",
            "name": "Bags",
            "value": "Bags",
          },
          {
            "count": 2,
            "facetName": "style",
            "id": "Hats",
            "isActive": "true",
            "localeName": "Hats",
            "name": "Hats",
            "value": "Hats",
          },
          {
            "count": 1,
            "facetName": "style",
            "id": "Lunchbox",
            "isActive": "true",
            "localeName": "Lunchbox",
            "name": "Lunchbox",
            "value": "Lunchbox",
          },
          {
            "count": 1,
            "facetName": "style",
            "id": "Sets",
            "isActive": "true",
            "localeName": "Sets",
            "name": "Sets",
            "value": "Sets",
          },
          {
            "count": 3,
            "facetName": "style",
            "id": "Shoes",
            "isActive": "true",
            "localeName": "Shoes",
            "name": "Shoes",
            "value": "Shoes",
          },
          {
            "count": 1,
            "facetName": "style",
            "id": "Shorts",
            "isActive": "true",
            "localeName": "Shorts",
            "name": "Shorts",
            "value": "Shorts",
          },
          {
            "count": 6,
            "facetName": "style",
            "id": "Sleepwear",
            "isActive": "true",
            "localeName": "Sleepwear",
            "name": "Sleepwear",
            "value": "Sleepwear",
          },
          {
            "count": 2,
            "facetName": "style",
            "id": "Socks",
            "isActive": "true",
            "localeName": "Socks",
            "name": "Socks",
            "value": "Socks",
          },
          {
            "count": 1,
            "facetName": "style",
            "id": "Sweatshirts",
            "isActive": "true",
            "localeName": "Sweatshirts",
            "name": "Sweatshirts",
            "value": "Sweatshirts",
          },
          {
            "count": 5,
            "facetName": "style",
            "id": "T shirts",
            "isActive": "true",
            "localeName": "T shirts",
            "name": "T shirts",
            "value": "T shirts",
          },
        ],
        "order": 2,
        "selectionType": "multi-select",
        "type": "simple",
      },
      {
        "displayName": "size",
        "isActive": true,
        "name": "size",
        "order": 3,
        "sizeVariants": [
          {
            "id": "husky",
            "name": "Husky",
            "styleGroups": [
              {
                "id": "tops (size)",
                "name": "Tops (Size)",
                "sizes": [
                  {
                    "description": "L",
                    "facetName": "size",
                    "id": "husky|tops|size|10:l",
                    "name": "10",
                    "selected": false,
                    "tagDisplayLabel": "Husky Tops: 10",
                    "value": "husky|tops|size|10:l",
                  },
                  {
                    "description": "XL",
                    "facetName": "size",
                    "id": "husky|tops|size|12:xl",
                    "name": "12",
                    "selected": false,
                    "tagDisplayLabel": "Husky Tops: 12",
                    "value": "husky|tops|size|12:xl",
                  },
                  {
                    "description": "XXL",
                    "facetName": "size",
                    "id": "husky|tops|size|14/16:xxl",
                    "name": "14/16",
                    "selected": false,
                    "tagDisplayLabel": "Husky Tops: 14/16",
                    "value": "husky|tops|size|14/16:xxl",
                  },
                ],
              },
            ],
          },
          {
            "id": "regular",
            "name": "Regular",
            "styleGroups": [
              {
                "id": "one pieces & sets (size)",
                "name": "One pieces & sets (Size)",
                "sizes": [
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|0-3 m",
                    "name": "0-3 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 0-3 M",
                    "value": "regular|one pieces & sets|size|0-3 m",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|2t",
                    "name": "2T",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 2T",
                    "value": "regular|one pieces & sets|size|2t",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|3-6 m",
                    "name": "3-6 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 3-6 M",
                    "value": "regular|one pieces & sets|size|3-6 m",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|3t",
                    "name": "3T",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 3T",
                    "value": "regular|one pieces & sets|size|3t",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|4t",
                    "name": "4T",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 4T",
                    "value": "regular|one pieces & sets|size|4t",
                  },
                  {
                    "description": "XS",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|4:xs",
                    "name": "4",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 4",
                    "value": "regular|one pieces & sets|size|4:xs",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|5t",
                    "name": "5T",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 5T",
                    "value": "regular|one pieces & sets|size|5t",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|6-12 m",
                    "name": "6-12 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 6-12 M",
                    "value": "regular|one pieces & sets|size|6-12 m",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|6t",
                    "name": "6T",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 6T",
                    "value": "regular|one pieces & sets|size|6t",
                  },
                  {
                    "description": "S",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|6:s",
                    "name": "6",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 6",
                    "value": "regular|one pieces & sets|size|6:s",
                  },
                  {
                    "description": "M",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|8:m",
                    "name": "8",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 8",
                    "value": "regular|one pieces & sets|size|8:m",
                  },
                  {
                    "description": "L",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|10:l",
                    "name": "10",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 10",
                    "value": "regular|one pieces & sets|size|10:l",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|12-18 m",
                    "name": "12-18 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 12-18 M",
                    "value": "regular|one pieces & sets|size|12-18 m",
                  },
                  {
                    "description": "XL",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|12:xl",
                    "name": "12",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 12",
                    "value": "regular|one pieces & sets|size|12:xl",
                  },
                  {
                    "description": "XXL",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|14:xxl",
                    "name": "14",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 14",
                    "value": "regular|one pieces & sets|size|14:xxl",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|one pieces & sets|size|18-24 m",
                    "name": "18-24 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular One pieces & sets: 18-24 M",
                    "value": "regular|one pieces & sets|size|18-24 m",
                  },
                ],
              },
              {
                "id": "accessories (size)",
                "name": "Accessories (Size)",
                "sizes": [
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|1/2",
                    "name": "1/2",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 1/2",
                    "value": "regular|accessories|size|1/2",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|1",
                    "name": "1",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 1",
                    "value": "regular|accessories|size|1",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|2",
                    "name": "2",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 2",
                    "value": "regular|accessories|size|2",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|3/4",
                    "name": "3/4",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 3/4",
                    "value": "regular|accessories|size|3/4",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|3",
                    "name": "3",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 3",
                    "value": "regular|accessories|size|3",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|4",
                    "name": "4",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 4",
                    "value": "regular|accessories|size|4",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|5/6",
                    "name": "5/6",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 5/6",
                    "value": "regular|accessories|size|5/6",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|5",
                    "name": "5",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 5",
                    "value": "regular|accessories|size|5",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|10/11",
                    "name": "10/11",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 10/11",
                    "value": "regular|accessories|size|10/11",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|11",
                    "name": "11",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 11",
                    "value": "regular|accessories|size|11",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|12/13",
                    "name": "12/13",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 12/13",
                    "value": "regular|accessories|size|12/13",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|12",
                    "name": "12",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 12",
                    "value": "regular|accessories|size|12",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|13",
                    "name": "13",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: 13",
                    "value": "regular|accessories|size|13",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|l/xl",
                    "name": "L/XL",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: L/XL",
                    "value": "regular|accessories|size|l/xl",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|l",
                    "name": "L",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: L",
                    "value": "regular|accessories|size|l",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|m",
                    "name": "M",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: M",
                    "value": "regular|accessories|size|m",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|one size",
                    "name": "ONE SIZE",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: ONE SIZE",
                    "value": "regular|accessories|size|one size",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|s/m",
                    "name": "S/M",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: S/M",
                    "value": "regular|accessories|size|s/m",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|accessories|size|s",
                    "name": "S",
                    "selected": false,
                    "tagDisplayLabel": "Regular Accessories: S",
                    "value": "regular|accessories|size|s",
                  },
                ],
              },
              {
                "id": "tops (size)",
                "name": "Tops (Size)",
                "sizes": [
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|tops|size|0-3 m",
                    "name": "0-3 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 0-3 M",
                    "value": "regular|tops|size|0-3 m",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|tops|size|2t",
                    "name": "2T",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 2T",
                    "value": "regular|tops|size|2t",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|tops|size|3-6 m",
                    "name": "3-6 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 3-6 M",
                    "value": "regular|tops|size|3-6 m",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|tops|size|3t",
                    "name": "3T",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 3T",
                    "value": "regular|tops|size|3t",
                  },
                  {
                    "description": "XS",
                    "facetName": "size",
                    "id": "regular|tops|size|4/5:xs",
                    "name": "4/5",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 4/5",
                    "value": "regular|tops|size|4/5:xs",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|tops|size|4t",
                    "name": "4T",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 4T",
                    "value": "regular|tops|size|4t",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|tops|size|5t",
                    "name": "5T",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 5T",
                    "value": "regular|tops|size|5t",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|tops|size|6-12 m",
                    "name": "6-12 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 6-12 M",
                    "value": "regular|tops|size|6-12 m",
                  },
                  {
                    "description": "S",
                    "facetName": "size",
                    "id": "regular|tops|size|6/7:s",
                    "name": "6/7",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 6/7",
                    "value": "regular|tops|size|6/7:s",
                  },
                  {
                    "description": "M",
                    "facetName": "size",
                    "id": "regular|tops|size|8:m",
                    "name": "8",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 8",
                    "value": "regular|tops|size|8:m",
                  },
                  {
                    "description": "L",
                    "facetName": "size",
                    "id": "regular|tops|size|10:l",
                    "name": "10",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 10",
                    "value": "regular|tops|size|10:l",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|tops|size|12-18 m",
                    "name": "12-18 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 12-18 M",
                    "value": "regular|tops|size|12-18 m",
                  },
                  {
                    "description": "XL",
                    "facetName": "size",
                    "id": "regular|tops|size|12:xl",
                    "name": "12",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 12",
                    "value": "regular|tops|size|12:xl",
                  },
                  {
                    "description": "XXL",
                    "facetName": "size",
                    "id": "regular|tops|size|14/16:xxl",
                    "name": "14/16",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 14/16",
                    "value": "regular|tops|size|14/16:xxl",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|tops|size|18-24 m",
                    "name": "18-24 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular Tops: 18-24 M",
                    "value": "regular|tops|size|18-24 m",
                  },
                ],
              },
              {
                "id": "bottoms (size)",
                "name": "Bottoms (Size)",
                "sizes": [
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|bottoms|size|0-3 m",
                    "name": "0-3 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular Bottoms: 0-3 M",
                    "value": "regular|bottoms|size|0-3 m",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|bottoms|size|3-6 m",
                    "name": "3-6 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular Bottoms: 3-6 M",
                    "value": "regular|bottoms|size|3-6 m",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|bottoms|size|6-12 m",
                    "name": "6-12 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular Bottoms: 6-12 M",
                    "value": "regular|bottoms|size|6-12 m",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|bottoms|size|12-18 m",
                    "name": "12-18 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular Bottoms: 12-18 M",
                    "value": "regular|bottoms|size|12-18 m",
                  },
                  {
                    "description": "",
                    "facetName": "size",
                    "id": "regular|bottoms|size|18-24 m",
                    "name": "18-24 M",
                    "selected": false,
                    "tagDisplayLabel": "Regular Bottoms: 18-24 M",
                    "value": "regular|bottoms|size|18-24 m",
                  },
                ],
              },
            ],
          },
        ],
        "type": "complex",
      },
      {
        "displayName": "color",
        "facetDisplay": "swatch",
        "facetLayout": "grid",
        "isActive": undefined,
        "localizedName": "color",
        "name": "color",
        "options": [
          {
            "count": 2,
            "facetName": "color",
            "id": "black",
            "isActive": "true",
            "localeName": "black",
            "name": "black",
            "value": undefined,
          },
          {
            "count": 7,
            "facetName": "color",
            "id": "blue",
            "isActive": "true",
            "localeName": "blue",
            "name": "blue",
            "value": undefined,
          },
          {
            "count": 1,
            "facetName": "color",
            "id": "brown",
            "isActive": "true",
            "localeName": "brown",
            "name": "brown",
            "value": undefined,
          },
          {
            "count": 4,
            "facetName": "color",
            "id": "gray",
            "isActive": "true",
            "localeName": "gray",
            "name": "gray",
            "value": undefined,
          },
          {
            "count": 5,
            "facetName": "color",
            "id": "green",
            "isActive": "true",
            "localeName": "green",
            "name": "green",
            "value": undefined,
          },
          {
            "count": 6,
            "facetName": "color",
            "id": "multi",
            "isActive": "true",
            "localeName": "multi",
            "name": "multi",
            "value": undefined,
          },
          {
            "count": 1,
            "facetName": "color",
            "id": "pink",
            "isActive": "true",
            "localeName": "pink",
            "name": "pink",
            "value": undefined,
          },
          {
            "count": 1,
            "facetName": "color",
            "id": "purple",
            "isActive": "true",
            "localeName": "purple",
            "name": "purple",
            "value": undefined,
          },
          {
            "count": 4,
            "facetName": "color",
            "id": "red",
            "isActive": "true",
            "localeName": "red",
            "name": "red",
            "value": undefined,
          },
          {
            "count": 3,
            "facetName": "color",
            "id": "white",
            "isActive": "true",
            "localeName": "white",
            "name": "white",
            "value": undefined,
          },
          {
            "count": 2,
            "facetName": "color",
            "id": "yellow",
            "isActive": "true",
            "localeName": "yellow",
            "name": "yellow",
            "value": undefined,
          },
        ],
        "order": 4,
        "selectionType": "multi-select",
        "type": "simple",
      },
      {
        "appliedRange": undefined,
        "displayName": "Price",
        "facetDisplay": "checkbox",
        "facetLayout": "list",
        "facetName": "price",
        "id": "undefined-undefined",
        "isActive": undefined,
        "localeName": "price",
        "localizedName": "price",
        "maxOptions": [
          "80",
          "70",
          "60",
          "50",
          "40",
          "30",
          "20",
          "10",
        ],
        "minOptions": [
          "10",
          "20",
          "30",
          "40",
          "50",
          "60",
          "70",
        ],
        "name": "price",
        "order": 5,
        "range": {
          "max": 80,
          "min": 10,
        },
        "selectionType": "multi-select",
        "type": "range",
        "value": "undefined-undefined",
      },
      {
        "displayName": "Review Score",
        "facetDisplay": "ratings",
        "facetLayout": "list",
        "isActive": undefined,
        "localizedName": "Review Score",
        "name": "reviewScore",
        "options": [
          {
            "facetName": "reviewScore",
            "id": "4",
            "isActive": "true",
            "localeName": "4 & up",
            "name": "4 & up",
            "value": "4",
          },
          {
            "facetName": "reviewScore",
            "id": "3",
            "isActive": "true",
            "localeName": "3 & up",
            "name": "3 & up",
            "value": "3",
          },
          {
            "facetName": "reviewScore",
            "id": "2",
            "isActive": "true",
            "localeName": "2 & up",
            "name": "2 & up",
            "value": "2",
          },
          {
            "facetName": "reviewScore",
            "id": "1",
            "isActive": "true",
            "localeName": "1 & up",
            "name": "1 & up",
            "value": "1",
          },
        ],
        "order": 6,
        "selectionType": "single-select",
        "type": "simple",
      },
      {
        "displayName": "Sleeve Length",
        "facetDisplay": "checkbox",
        "facetLayout": "list",
        "isActive": undefined,
        "localizedName": "Sleeve Length",
        "name": "sleeveLength",
        "options": [
          {
            "count": 2,
            "facetName": "sleeveLength",
            "id": "Long Sleeve",
            "isActive": "true",
            "localeName": "Long Sleeve",
            "name": "Long Sleeve",
            "value": "Long Sleeve",
          },
          {
            "count": 2,
            "facetName": "sleeveLength",
            "id": "Short Sleeve",
            "isActive": "true",
            "localeName": "Short Sleeve",
            "name": "Short Sleeve",
            "value": "Short Sleeve",
          },
        ],
        "selectionType": "multi-select",
        "type": "simple",
      },
    ],
    "hasTags": true,
  },
  "gridData": [
    {
      "altText": "GapKids &#124 Star Wars&#153 100% Organic Cotton Graphic T-Shirt",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "825562082",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 40% Off At Checkout",
      },
      "name": "GapKids &#124 Star Wars&#153 100% Organic Cotton Graphic T-Shirt",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 16.99,
        "currentMinPrice": 9.99,
        "localizedCurrentMaxPrice": "$16.99",
        "localizedCurrentMinPrice": "$9.99",
        "localizedRegularMaxPrice": "$24.95",
        "localizedRegularMinPrice": "$24.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 2,
        "regularMaxPrice": 24.95,
        "regularMinPrice": 24.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "825562082",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "196",
      "reviewScore": "4.9",
      "sellerName": "SAE-A TRADING CO., LTD",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "colorName": "grey heather",
          "colorStyleName": "GapKids &#124 Star Wars&#153 100% Organic Cotton Graphic T-Shirt",
          "id": "825562082",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "M",
          "productImage": "/webcontent/0029/198/228/cn29198228.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 100% Organic Cotton Graphic T-Shirt grey heather",
          "url": "/webcontent/0029/197/899/cn29197899.jpg",
        },
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "colorName": "purple",
          "colorStyleName": "GapKids &#124 Star Wars&#153 100% Organic Cotton Graphic T-Shirt",
          "id": "825562092",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "M",
          "productImage": "/webcontent/0029/196/811/cn29196811.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 100% Organic Cotton Graphic T-Shirt purple",
          "url": "/webcontent/0029/196/664/cn29196664.jpg",
        },
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "colorName": "brown",
          "colorStyleName": "GapKids &#124 Star Wars&#153 100% Organic Cotton Graphic T-Shirt",
          "id": "825562102",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "M",
          "productImage": "/webcontent/0029/196/760/cn29196760.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 100% Organic Cotton Graphic T-Shirt brown",
          "url": "/webcontent/0029/196/668/cn29196668.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "babyGap &#124 Disney 100% Organic Cotton Simba PJ Set",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "430754002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "babyGap &#124 Disney 100% Organic Cotton Simba PJ Set",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 24,
        "currentMinPrice": 24,
        "localizedCurrentMaxPrice": "$24.0",
        "localizedCurrentMinPrice": "$24.0",
        "localizedRegularMaxPrice": "$34.95",
        "localizedRegularMinPrice": "$34.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 34.95,
        "regularMinPrice": 34.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "430754002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "0",
      "reviewScore": "0",
      "sellerName": "NK SOURCING PTE LTD",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "french almond yellow",
          "colorStyleName": "babyGap &#124 Disney 100% Organic Cotton Simba PJ Set",
          "id": "430754002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0029/506/399/cn29506399.jpg",
          "productImageAltText": "babyGap &#124 Disney 100% Organic Cotton Simba PJ Set french almond yellow",
          "url": "/webcontent/0029/506/344/cn29506344.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Disney Mickey Mouse Baseball Hat",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "543437002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Disney Mickey Mouse Baseball Hat",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 19,
        "currentMinPrice": 19,
        "localizedCurrentMaxPrice": "$19.0",
        "localizedCurrentMinPrice": "$19.0",
        "localizedRegularMaxPrice": "$24.95",
        "localizedRegularMinPrice": "$24.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 24.95,
        "regularMinPrice": 24.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "543437002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "7",
      "reviewScore": "4.9",
      "sellerName": "HEDERA INTERNATIONAL TRADING L",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "blue",
          "colorStyleName": "GapKids &#124 Disney Mickey Mouse Baseball Hat",
          "id": "543437002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0052/401/546/cn52401546.jpg",
          "productImageAltText": "GapKids &#124 Disney Mickey Mouse Baseball Hat blue",
          "url": "/webcontent/0052/401/392/cn52401392.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Disney Recycled Mickey Mouse Backpack",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "665141002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Disney Recycled Mickey Mouse Backpack",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 41,
        "currentMinPrice": 41,
        "localizedCurrentMaxPrice": "$41.0",
        "localizedCurrentMinPrice": "$41.0",
        "localizedRegularMaxPrice": "$59.95",
        "localizedRegularMinPrice": "$59.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 59.95,
        "regularMinPrice": 59.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "665141002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "3",
      "reviewScore": "5",
      "sellerName": "ORIENTAL SMART ASIA PACIFIC LI",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "admiral blue",
          "colorStyleName": "GapKids &#124 Disney Recycled Mickey Mouse Backpack",
          "id": "665141002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0053/248/871/cn53248871.jpg",
          "productImageAltText": "GapKids &#124 Disney Recycled Mickey Mouse Backpack admiral blue",
          "url": "/webcontent/0053/248/824/cn53248824.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "babyGap &#124 Disney 100% Organic Cotton Cars PJ Set",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "663756002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "babyGap &#124 Disney 100% Organic Cotton Cars PJ Set",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 24,
        "currentMinPrice": 24,
        "localizedCurrentMaxPrice": "$24.0",
        "localizedCurrentMinPrice": "$24.0",
        "localizedRegularMaxPrice": "$34.95",
        "localizedRegularMinPrice": "$34.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 34.95,
        "regularMinPrice": 34.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "663756002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "5",
      "reviewScore": "5",
      "sellerName": "HANSAE CO LTD",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "new off white",
          "colorStyleName": "babyGap &#124 Disney 100% Organic Cotton Cars PJ Set",
          "id": "663756002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0053/493/655/cn53493655.jpg",
          "productImageAltText": "babyGap &#124 Disney 100% Organic Cotton Cars PJ Set new off white",
          "url": "/webcontent/0053/493/202/cn53493202.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "babyGap &#124 Disney 100% Organic Cotton Metallic Minnie Mouse PJ Set",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "663767002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "babyGap &#124 Disney 100% Organic Cotton Metallic Minnie Mouse PJ Set",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 24,
        "currentMinPrice": 24,
        "localizedCurrentMaxPrice": "$24.0",
        "localizedCurrentMinPrice": "$24.0",
        "localizedRegularMaxPrice": "$34.95",
        "localizedRegularMinPrice": "$34.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 34.95,
        "regularMinPrice": 34.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "663767002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "1",
      "reviewScore": "5",
      "sellerName": "NK SOURCING PTE LTD",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "new off white",
          "colorStyleName": "babyGap &#124 Disney 100% Organic Cotton Metallic Minnie Mouse PJ Set",
          "id": "663767002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0053/167/471/cn53167471.jpg",
          "productImageAltText": "babyGap &#124 Disney 100% Organic Cotton Metallic Minnie Mouse PJ Set new off white",
          "url": "/webcontent/0053/167/457/cn53167457.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "babyGap &#124 Star Wars &#153 Sweat Short Set",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "825313002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 40% Off At Checkout",
      },
      "name": "babyGap &#124 Star Wars &#153 Sweat Short Set",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 24.99,
        "currentMinPrice": 24.99,
        "localizedCurrentMaxPrice": "$24.99",
        "localizedCurrentMinPrice": "$24.99",
        "localizedRegularMaxPrice": "$49.95",
        "localizedRegularMinPrice": "$49.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 2,
        "regularMaxPrice": 49.95,
        "regularMinPrice": 49.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "825313002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "13",
      "reviewScore": "4.8",
      "sellerName": undefined,
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "colorName": "douglas fir green",
          "colorStyleName": "babyGap &#124 Star Wars &#153 Sweat Short Set",
          "id": "825313002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "M",
          "productImage": "/webcontent/0028/241/063/cn28241063.jpg",
          "productImageAltText": "babyGap &#124 Star Wars &#153 Sweat Short Set douglas fir green",
          "url": "/webcontent/0028/240/905/cn28240905.jpg",
        },
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "colorName": "pink champagne",
          "colorStyleName": "babyGap &#124 Star Wars &#153 Sweat Short Set",
          "id": "825313012",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "M",
          "productImage": "/webcontent/0028/241/074/cn28241074.jpg",
          "productImageAltText": "babyGap &#124 Star Wars &#153 Sweat Short Set pink champagne",
          "url": "/webcontent/0028/240/906/cn28240906.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "babyGap &#124 Star Wars&#153 100% Organic Cotton Grogu&#153 PJ Set",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "868520002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "50% Off! Limited-Time Deal",
      },
      "name": "babyGap &#124 Star Wars&#153 100% Organic Cotton Grogu&#153 PJ Set",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 17,
        "currentMinPrice": 17,
        "localizedCurrentMaxPrice": "$17.0",
        "localizedCurrentMinPrice": "$17.0",
        "localizedRegularMaxPrice": "$34.95",
        "localizedRegularMinPrice": "$34.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 34.95,
        "regularMinPrice": 34.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "868520002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "11",
      "reviewScore": "4.7",
      "sellerName": undefined,
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "50% Off! Limited-Time Deal",
          },
          "colorName": "citron yellow",
          "colorStyleName": "babyGap &#124 Star Wars&#153 100% Organic Cotton Grogu&#153 PJ Set",
          "id": "868520002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "50% Off! Limited-Time Deal",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0028/793/890/cn28793890.jpg",
          "productImageAltText": "babyGap &#124 Star Wars&#153 100% Organic Cotton Grogu&#153 PJ Set citron yellow",
          "url": "/webcontent/0028/793/853/cn28793853.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "babyGap &#124 Disney 100% Organic Cotton Mickey Mouse Graphic T-Shirt",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "601905002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 40% Off At Checkout",
      },
      "name": "babyGap &#124 Disney 100% Organic Cotton Mickey Mouse Graphic T-Shirt",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 19.99,
        "currentMinPrice": 19.99,
        "localizedCurrentMaxPrice": "$19.99",
        "localizedCurrentMinPrice": "$19.99",
        "localizedRegularMaxPrice": "$24.95",
        "localizedRegularMinPrice": "$24.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 2,
        "regularMaxPrice": 24.95,
        "regularMinPrice": 24.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "601905002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "32",
      "reviewScore": "4.8",
      "sellerName": "SAE-A TRADING CO., LTD",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "colorName": "surf spray",
          "colorStyleName": "babyGap &#124 Disney 100% Organic Cotton Mickey Mouse Graphic T-Shirt",
          "id": "601905002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "M",
          "productImage": "/webcontent/0052/808/419/cn52808419.jpg",
          "productImageAltText": "babyGap &#124 Disney 100% Organic Cotton Mickey Mouse Graphic T-Shirt surf spray",
          "url": "/webcontent/0052/808/032/cn52808032.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Star Wars&#153 Interactive Graphic T-Shirt",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "759210002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "50% Off! Limited-Time Deal",
      },
      "name": "GapKids &#124 Star Wars&#153 Interactive Graphic T-Shirt",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 14,
        "currentMinPrice": 14,
        "localizedCurrentMaxPrice": "$14.0",
        "localizedCurrentMinPrice": "$14.0",
        "localizedRegularMaxPrice": "$29.95",
        "localizedRegularMinPrice": "$29.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 29.95,
        "regularMinPrice": 29.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "759210002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "135",
      "reviewScore": "4.8",
      "sellerName": undefined,
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "50% Off! Limited-Time Deal",
          },
          "colorName": "grey heather",
          "colorStyleName": "GapKids &#124 Star Wars&#153 Interactive Graphic T-Shirt",
          "id": "759210002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "50% Off! Limited-Time Deal",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0027/646/759/cn27646759.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 Interactive Graphic T-Shirt grey heather",
          "url": "/webcontent/0027/646/557/cn27646557.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Marvel Graphic T-Shirt",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "668014002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Marvel Graphic T-Shirt",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 23,
        "currentMinPrice": 23,
        "localizedCurrentMaxPrice": "$23.0",
        "localizedCurrentMinPrice": "$23.0",
        "localizedRegularMaxPrice": "$29.95",
        "localizedRegularMinPrice": "$29.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 29.95,
        "regularMinPrice": 29.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "668014002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "7",
      "reviewScore": "5",
      "sellerName": "PRATIBHA SYNTEX LTD",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "shirting blue",
          "colorStyleName": "GapKids &#124 Marvel Graphic T-Shirt",
          "id": "668014002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0052/983/052/cn52983052.jpg",
          "productImageAltText": "GapKids &#124 Marvel Graphic T-Shirt shirting blue",
          "url": "/webcontent/0052/982/937/cn52982937.jpg",
        },
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "moonless night",
          "colorStyleName": "GapKids &#124 Marvel Graphic T-Shirt",
          "id": "668014012",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0052/981/073/cn52981073.jpg",
          "productImageAltText": "GapKids &#124 Marvel Graphic T-Shirt moonless night",
          "url": "/webcontent/0052/980/911/cn52980911.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Marvel Recycled Backpack",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "665139002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Marvel Recycled Backpack",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 41,
        "currentMinPrice": 41,
        "localizedCurrentMaxPrice": "$41.0",
        "localizedCurrentMinPrice": "$41.0",
        "localizedRegularMaxPrice": "$59.95",
        "localizedRegularMinPrice": "$59.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 59.95,
        "regularMinPrice": 59.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "665139002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "4",
      "reviewScore": "5",
      "sellerName": "ORIENTAL SMART ASIA PACIFIC LI",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "modern red",
          "colorStyleName": "GapKids &#124 Marvel Recycled Backpack",
          "id": "665139002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0053/214/407/cn53214407.jpg",
          "productImageAltText": "GapKids &#124 Marvel Recycled Backpack modern red",
          "url": "/webcontent/0053/214/254/cn53214254.jpg",
        },
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "true black",
          "colorStyleName": "GapKids &#124 Marvel Recycled Backpack",
          "id": "665139012",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0053/248/940/cn53248940.jpg",
          "productImageAltText": "GapKids &#124 Marvel Recycled Backpack true black",
          "url": "/webcontent/0053/248/821/cn53248821.jpg",
        },
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "dark night",
          "colorStyleName": "GapKids &#124 Marvel Recycled Backpack",
          "id": "665139022",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0053/248/932/cn53248932.jpg",
          "productImageAltText": "GapKids &#124 Marvel Recycled Backpack dark night",
          "url": "/webcontent/0053/248/823/cn53248823.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Star Wars&#153 Grogu&#153 Slippers",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "406122002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Star Wars&#153 Grogu&#153 Slippers",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 35,
        "currentMinPrice": 35,
        "localizedCurrentMaxPrice": "$35.0",
        "localizedCurrentMinPrice": "$35.0",
        "localizedRegularMaxPrice": "$44.95",
        "localizedRegularMinPrice": "$44.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 44.95,
        "regularMinPrice": 44.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "406122002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "22",
      "reviewScore": "4.7",
      "sellerName": "KINGSBURG INDUSTRIES CO LTD (N",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "multi",
          "colorStyleName": "GapKids &#124 Star Wars&#153 Grogu&#153 Slippers",
          "id": "406122002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0051/855/063/cn51855063.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 Grogu&#153 Slippers multi",
          "url": "/webcontent/0051/855/061/cn51855061.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Star Wars&#153 Crew Socks (3-Pack)",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "543373002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Star Wars&#153 Crew Socks (3-Pack)",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 15,
        "currentMinPrice": 15,
        "localizedCurrentMaxPrice": "$15.0",
        "localizedCurrentMinPrice": "$15.0",
        "localizedRegularMaxPrice": "$19.95",
        "localizedRegularMinPrice": "$19.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 19.95,
        "regularMinPrice": 19.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "543373002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "8",
      "reviewScore": "5",
      "sellerName": "ZHEJIANG JASAN HOLDING GROUP C",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "multi",
          "colorStyleName": "GapKids &#124 Star Wars&#153 Crew Socks (3-Pack)",
          "id": "543373002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0052/660/508/cn52660508.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 Crew Socks (3-Pack) multi",
          "url": "/webcontent/0052/659/651/cn52659651.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Star Wars&#153 100% Recycled Mando&#153 PJ Set",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "455288002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 40% Off At Checkout",
      },
      "name": "GapKids &#124 Star Wars&#153 100% Recycled Mando&#153 PJ Set",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 34.99,
        "currentMinPrice": 34.99,
        "localizedCurrentMaxPrice": "$34.99",
        "localizedCurrentMinPrice": "$34.99",
        "localizedRegularMaxPrice": "$49.95",
        "localizedRegularMinPrice": "$49.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 2,
        "regularMaxPrice": 49.95,
        "regularMinPrice": 49.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "455288002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "29",
      "reviewScore": "4.9",
      "sellerName": "WUXI JINMAO FOREIGN TRADE CO L",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "colorName": "green",
          "colorStyleName": "GapKids &#124 Star Wars&#153 100% Recycled Mando&#153 PJ Set",
          "id": "455288002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "M",
          "productImage": "/webcontent/0029/242/259/cn29242259.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 100% Recycled Mando&#153 PJ Set green",
          "url": "/webcontent/0029/241/802/cn29241802.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Star Wars&#153 Recycled Backpack",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "665177002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Star Wars&#153 Recycled Backpack",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 41,
        "currentMinPrice": 41,
        "localizedCurrentMaxPrice": "$41.0",
        "localizedCurrentMinPrice": "$41.0",
        "localizedRegularMaxPrice": "$59.95",
        "localizedRegularMinPrice": "$59.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 59.95,
        "regularMinPrice": 59.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "665177002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "0",
      "reviewScore": "0",
      "sellerName": "ORIENTAL SMART ASIA PACIFIC LI",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "grey",
          "colorStyleName": "GapKids &#124 Star Wars&#153 Recycled Backpack",
          "id": "665177002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0053/248/879/cn53248879.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 Recycled Backpack grey",
          "url": "/webcontent/0053/248/827/cn53248827.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Star Wars&#153 Recycled Lunchbag",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "665167002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Star Wars&#153 Recycled Lunchbag",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 24,
        "currentMinPrice": 24,
        "localizedCurrentMaxPrice": "$24.0",
        "localizedCurrentMinPrice": "$24.0",
        "localizedRegularMaxPrice": "$34.95",
        "localizedRegularMinPrice": "$34.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 34.95,
        "regularMinPrice": 34.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "665167002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "0",
      "reviewScore": "0",
      "sellerName": "ORIENTAL SMART ASIA PACIFIC LI",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "grey",
          "colorStyleName": "GapKids &#124 Star Wars&#153 Recycled Lunchbag",
          "id": "665167002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0053/214/538/cn53214538.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 Recycled Lunchbag grey",
          "url": "/webcontent/0053/271/305/cn53271305.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Star Wars&#153 Graphic Sneakers",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "406082002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 40% Off At Checkout",
      },
      "name": "GapKids &#124 Star Wars&#153 Graphic Sneakers",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 34.99,
        "currentMinPrice": 34.99,
        "localizedCurrentMaxPrice": "$34.99",
        "localizedCurrentMinPrice": "$34.99",
        "localizedRegularMaxPrice": "$49.95",
        "localizedRegularMinPrice": "$49.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 2,
        "regularMaxPrice": 49.95,
        "regularMinPrice": 49.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "406082002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "9",
      "reviewScore": "4.7",
      "sellerName": undefined,
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "colorName": "olive green",
          "colorStyleName": "GapKids &#124 Star Wars&#153 Graphic Sneakers",
          "id": "406082002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "M",
          "productImage": "/webcontent/0029/365/638/cn29365638.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 Graphic Sneakers olive green",
          "url": "/webcontent/0029/365/569/cn29365569.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Star Wars&#153 Graphic Crew Socks (3-Pack)",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "406209002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Star Wars&#153 Graphic Crew Socks (3-Pack)",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 15,
        "currentMinPrice": 15,
        "localizedCurrentMaxPrice": "$15.0",
        "localizedCurrentMinPrice": "$15.0",
        "localizedRegularMaxPrice": "$19.95",
        "localizedRegularMinPrice": "$19.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 19.95,
        "regularMinPrice": 19.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "406209002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "37",
      "reviewScore": "4.9",
      "sellerName": undefined,
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "multi",
          "colorStyleName": "GapKids &#124 Star Wars&#153 Graphic Crew Socks (3-Pack)",
          "id": "406209002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0029/447/144/cn29447144.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 Graphic Crew Socks (3-Pack) multi",
          "url": "/webcontent/0029/447/076/cn29447076.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Star Wars&#153 Flip Flops",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "543392002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Star Wars&#153 Flip Flops",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 13,
        "currentMinPrice": 13,
        "localizedCurrentMaxPrice": "$13.0",
        "localizedCurrentMinPrice": "$13.0",
        "localizedRegularMaxPrice": "$16.95",
        "localizedRegularMinPrice": "$16.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 16.95,
        "regularMinPrice": 16.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "543392002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "2",
      "reviewScore": "5",
      "sellerName": "KINGSBURG INDUSTRIES CO LTD",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "modern red",
          "colorStyleName": "GapKids &#124 Star Wars&#153 Flip Flops",
          "id": "543392002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0052/713/573/cn52713573.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 Flip Flops modern red",
          "url": "/webcontent/0052/713/404/cn52713404.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Star Wars&#153 100% Organic Cotton PJ Set",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "868982002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Star Wars&#153 100% Organic Cotton PJ Set",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 34.99,
        "currentMinPrice": 34.99,
        "localizedCurrentMaxPrice": "$34.99",
        "localizedCurrentMinPrice": "$34.99",
        "localizedRegularMaxPrice": "$44.95",
        "localizedRegularMinPrice": "$44.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 2,
        "regularMaxPrice": 44.95,
        "regularMinPrice": 44.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "868982002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "7",
      "reviewScore": "5",
      "sellerName": undefined,
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "buxton blue",
          "colorStyleName": "GapKids &#124 Star Wars&#153 100% Organic Cotton PJ Set",
          "id": "868982002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "M",
          "productImage": "/webcontent/0052/660/010/cn52660010.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 100% Organic Cotton PJ Set buxton blue",
          "url": "/webcontent/0052/659/854/cn52659854.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "babyGap &#124 StarWars&#153 Yoda Hoodie",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "739090002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "babyGap &#124 StarWars&#153 Yoda Hoodie",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 35,
        "currentMinPrice": 35,
        "localizedCurrentMaxPrice": "$35.0",
        "localizedCurrentMinPrice": "$35.0",
        "localizedRegularMaxPrice": "$39.95",
        "localizedRegularMinPrice": "$39.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 39.95,
        "regularMinPrice": 39.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "739090002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "121",
      "reviewScore": "4.9",
      "sellerName": undefined,
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "desert sage",
          "colorStyleName": "babyGap &#124 StarWars&#153 Yoda Hoodie",
          "id": "739090002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0019/893/179/cn19893179.jpg",
          "productImageAltText": "babyGap &#124 StarWars&#153 Yoda Hoodie desert sage",
          "url": "/webcontent/0019/892/871/cn19892871.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Star Wars&#153 Interactive Graphic T-Shirt",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "727586012",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 40% Off At Checkout",
      },
      "name": "GapKids &#124 Star Wars&#153 Interactive Graphic T-Shirt",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 19.99,
        "currentMinPrice": 19.99,
        "localizedCurrentMaxPrice": "$19.99",
        "localizedCurrentMinPrice": "$19.99",
        "localizedRegularMaxPrice": "$29.95",
        "localizedRegularMinPrice": "$29.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 2,
        "regularMaxPrice": 29.95,
        "regularMinPrice": 29.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "727586012",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "44",
      "reviewScore": "4.7",
      "sellerName": undefined,
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "colorName": "new off white",
          "colorStyleName": "GapKids &#124 Star Wars&#153 Interactive Graphic T-Shirt",
          "id": "727586012",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 40% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "M",
          "productImage": "/webcontent/0027/206/456/cn27206456.jpg",
          "productImageAltText": "GapKids &#124 Star Wars&#153 Interactive Graphic T-Shirt new off white",
          "url": "/webcontent/0027/206/296/cn27206296.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Marvel Recycled Lunchbag",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "665265002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Marvel Recycled Lunchbag",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 24,
        "currentMinPrice": 24,
        "localizedCurrentMaxPrice": "$24.0",
        "localizedCurrentMinPrice": "$24.0",
        "localizedRegularMaxPrice": "$34.95",
        "localizedRegularMinPrice": "$34.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 3,
        "regularMaxPrice": 34.95,
        "regularMinPrice": 34.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "665265002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "2",
      "reviewScore": "5",
      "sellerName": "ORIENTAL SMART ASIA PACIFIC LI",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "modern red",
          "colorStyleName": "GapKids &#124 Marvel Recycled Lunchbag",
          "id": "665265002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0053/214/484/cn53214484.jpg",
          "productImageAltText": "GapKids &#124 Marvel Recycled Lunchbag modern red",
          "url": "/webcontent/0053/214/468/cn53214468.jpg",
        },
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "dark night",
          "colorStyleName": "GapKids &#124 Marvel Recycled Lunchbag",
          "id": "665265012",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "P",
          "productImage": "/webcontent/0053/248/956/cn53248956.jpg",
          "productImageAltText": "GapKids &#124 Marvel Recycled Lunchbag dark night",
          "url": "/webcontent/0053/248/840/cn53248840.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Marvel Avengers Weekend Bag",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "775781002",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Marvel Avengers Weekend Bag",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 74.95,
        "currentMinPrice": 74.95,
        "localizedCurrentMaxPrice": "$74.95",
        "localizedCurrentMinPrice": "$74.95",
        "localizedRegularMaxPrice": "$74.95",
        "localizedRegularMinPrice": "$74.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 1,
        "regularMaxPrice": 74.95,
        "regularMinPrice": 74.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "775781002",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "0",
      "reviewScore": "0",
      "sellerName": "ORIENTAL SMART ASIA PACIFIC LI",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "vintage navy",
          "colorStyleName": "GapKids &#124 Marvel Avengers Weekend Bag",
          "id": "775781002",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "R",
          "productImage": "/webcontent/0053/517/579/cn53517579.jpg",
          "productImageAltText": "GapKids &#124 Marvel Avengers Weekend Bag vintage navy",
          "url": "/webcontent/0053/517/577/cn53517577.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
    {
      "altText": "GapKids &#124 Marvel Superhero Trucker Hat",
      "avImages": {
        "av1QuicklookImagePath": undefined,
        "av2QuicklookImagePath": undefined,
        "av3QuicklookImagePath": undefined,
        "av4QuicklookImagePath": undefined,
        "av5QuicklookImagePath": undefined,
        "av6QuicklookImagePath": undefined,
        "av9QuicklookImagePath": undefined,
        "p1QuicklookImagePath": undefined,
      },
      "businessCatalogItemId": "775151012",
      "categoryLargeImage": undefined,
      "defaultSizeVariantId": "1",
      "inventoryStatusId": 0,
      "isDropship": false,
      "marketingFlag": {
        "badgingName": "",
        "marketingFlagName": "Extra 30% Off At Checkout",
      },
      "name": "GapKids &#124 Marvel Superhero Trucker Hat",
      "outOfStock": false,
      "price": {
        "currentMaxPrice": 24.95,
        "currentMinPrice": 24.95,
        "localizedCurrentMaxPrice": "$24.95",
        "localizedCurrentMinPrice": "$24.95",
        "localizedRegularMaxPrice": "$24.95",
        "localizedRegularMinPrice": "$24.95",
        "maxPercentageOff": 0,
        "minPercentageOff": 0,
        "priceType": 1,
        "regularMaxPrice": 24.95,
        "regularMinPrice": 24.95,
      },
      "pristineImages": {
        "pristine1ImagePath": undefined,
      },
      "productID": "775151012",
      "productURL": "",
      "quicklookImage": undefined,
      "reviewCount": "0",
      "reviewScore": "0",
      "sellerName": "HEDERA INTERNATIONAL TRADING L",
      "swatchesProps": [
        {
          "colorMarketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "colorName": "modern red",
          "colorStyleName": "GapKids &#124 Marvel Superhero Trucker Hat",
          "id": "775151012",
          "inStock": true,
          "marketingFlag": {
            "badgingName": "",
            "marketingFlagName": "Extra 30% Off At Checkout",
          },
          "mergeType": "NONE",
          "priceType": "R",
          "productImage": "/webcontent/0053/481/757/cn53481757.jpg",
          "productImageAltText": "GapKids &#124 Marvel Superhero Trucker Hat modern red",
          "url": "/webcontent/0053/481/699/cn53481699.jpg",
        },
      ],
      "url": undefined,
      "vendorId": undefined,
      "vid": "REGULAR",
      "zoomImages": {
        "av1ZoomImagePath": undefined,
        "p01ZoomImagePath": undefined,
      },
    },
  ],
  "paginator": {
    "pageNumberRequested": 0,
    "pageNumberTotal": 1,
    "pageSize": 200,
  },
  "spellingSuggestions": [],
  "totalItemCount": 29,
}
`;
