// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`PsDataProvider - usePsData > Failure > should return empty object in case of api error 1`] = `
{
  "appliedFacetsQuery": {},
  "appliedSizeModelOptions": {
    "sizeModelToggleValue": "",
  },
  "appliedSortOptions": {
    "sortByDir": "",
    "sortByField": "",
  },
  "dispatch": [Function],
  "onFacetEngagement": [Function],
  "setAppliedFacetsQuery": [Function],
  "setAppliedSizeModelOptions": [Function],
  "setAppliedSortOptions": [Function],
  "state": {
    "data": {
      "cid": "123456",
      "facets": {
        "appliedFacets": {},
        "facetOptions": [],
        "hasTags": false,
      },
      "inlineFacets": {
        "facetOptions": [],
      },
      "orderedFacets": {
        "appliedFacets": {},
        "facetOptions": [],
      },
      "outOfStock": false,
      "pagination": {
        "pageNumberRequested": 0,
        "pageNumberTotal": 0,
        "pageSize": 300,
      },
      "productCards": {},
      "productImages": {},
      "productInfos": {},
      "productsGridIds": [],
      "quickAdd": {},
      "searchText": "",
      "sizeModelOptions": undefined,
      "sortingOptions": undefined,
      "totalItemCount": 0,
    },
    "isLoading": false,
    "isSuccess": true,
  },
}
`;

exports[`PsDataProvider - usePsData > Success > should fetch PS API and return the transformed data for usePsData 1`] = `
{
  "appliedFacetsQuery": {},
  "appliedSizeModelOptions": {
    "sizeModelToggleValue": "",
  },
  "appliedSortOptions": {
    "sortByDir": "",
    "sortByField": "",
  },
  "dispatch": [Function],
  "onFacetEngagement": [Function],
  "setAppliedFacetsQuery": [Function],
  "setAppliedSizeModelOptions": [Function],
  "setAppliedSortOptions": [Function],
  "state": {
    "data": {
      "cid": "123456",
      "facets": {
        "appliedFacets": {
          "color": [],
          "price": [],
          "reviewCount": [],
          "rise": [],
          "size": [],
          "style": [],
        },
        "facetOptions": [
          {
            "displayName": "style",
            "facetDisplay": "checkbox",
            "facetLayout": "list",
            "isActive": true,
            "localizedName": "style",
            "name": "style",
            "options": [
              {
                "facetName": "style",
                "id": "123457",
                "isActive": "true",
                "localeName": "Skinny",
                "name": "Skinny",
                "value": "123457",
              },
            ],
            "order": 2,
            "selectionType": "multi-select",
            "type": "simple",
          },
          {
            "displayName": "size",
            "isActive": true,
            "name": "size",
            "order": 3,
            "sizeVariants": [
              {
                "id": "regular",
                "name": "Regular",
                "styleGroups": [
                  {
                    "id": "bottoms (size)",
                    "name": "Bottoms (Size)",
                    "sizes": [
                      {
                        "description": "xxs/XS",
                        "facetName": "size",
                        "id": "regular|bottoms|size|00:xxs/xs",
                        "name": "00",
                        "selected": false,
                        "tagDisplayLabel": "Regular Bottoms: 00",
                        "value": "regular|bottoms|size|00:xxs/xs",
                      },
                      {
                        "description": "xxs/xs",
                        "facetName": "size",
                        "id": "regular|bottoms|size|01:xxs/xs",
                        "name": "01",
                        "selected": false,
                        "tagDisplayLabel": "Regular Bottoms: 01",
                        "value": "regular|bottoms|size|01:xxs/xs",
                      },
                    ],
                  },
                  {
                    "id": "bottoms (inseam)",
                    "name": "Bottoms (InSeam)",
                    "sizes": [
                      {
                        "description": "",
                        "facetName": "size",
                        "id": "regular|bottoms|inseam|short",
                        "name": "shrt",
                        "selected": false,
                        "tagDisplayLabel": "Regular Bottoms: shrt",
                        "value": "regular|bottoms|inseam|short",
                      },
                    ],
                  },
                  {
                    "id": "pants (size)",
                    "name": "pants (Size)",
                    "sizes": [
                      {
                        "description": "xxs/TP",
                        "facetName": "size",
                        "id": "regular|pants|size|00:xxs/xs",
                        "name": "00",
                        "selected": false,
                        "tagDisplayLabel": "Regular pants: 00",
                        "value": "regular|pants|size|00:xxs/xs",
                      },
                    ],
                  },
                ],
              },
            ],
            "type": "complex",
          },
          {
            "displayName": "Color",
            "facetDisplay": "swatch",
            "facetLayout": "grid",
            "isActive": true,
            "localizedName": "Color",
            "name": "color",
            "options": [
              {
                "facetName": "color",
                "id": "black",
                "isActive": "true",
                "localeName": "black",
                "name": "black",
                "value": undefined,
              },
            ],
            "order": 4,
            "selectionType": "multi-select",
            "type": "simple",
          },
          {
            "appliedRange": undefined,
            "displayName": "Price",
            "facetDisplay": "checkbox",
            "facetLayout": "list",
            "facetName": "price",
            "id": "undefined-undefined",
            "isActive": true,
            "localeName": "price",
            "localizedName": "price",
            "maxOptions": [
              "130",
              "120",
              "110",
              "100",
              "90",
              "80",
              "70",
              "60",
              "50",
              "40",
              "30",
              "20",
              "10",
            ],
            "minOptions": [
              "10",
              "20",
              "30",
              "40",
              "50",
              "60",
              "70",
              "80",
              "90",
              "100",
              "110",
              "120",
            ],
            "name": "price",
            "order": 5,
            "range": {
              "max": 130,
              "min": 10,
            },
            "selectionType": "multi-select",
            "type": "range",
            "value": "undefined-undefined",
          },
          {
            "displayName": "Rise",
            "facetDisplay": "checkbox",
            "facetLayout": "list",
            "isActive": true,
            "localizedName": "Rise",
            "name": "rise",
            "options": [
              {
                "facetName": "rise",
                "id": "high",
                "isActive": "true",
                "localeName": "High",
                "name": "High",
                "value": "high",
              },
            ],
            "selectionType": "multi-select",
            "type": "simple",
          },
          {
            "appliedRange": undefined,
            "displayName": "Review Count",
            "facetDisplay": "checkbox",
            "facetLayout": "list",
            "facetName": "reviewCount",
            "id": "reviewCount",
            "isActive": true,
            "localeName": "Review Count",
            "localizedName": "Review Count",
            "maxOptions": [
              "15",
              "10",
              "5",
            ],
            "minOptions": [
              "10",
            ],
            "name": "reviewCount",
            "range": {
              "max": 15,
              "min": 10,
            },
            "selectionType": "multi-select",
            "type": "range",
            "value": "undefined-undefined",
          },
        ],
        "hasTags": false,
      },
      "inlineFacets": {
        "facetOptions": [],
      },
      "orderedFacets": {
        "appliedFacets": {},
        "facetOptions": [],
      },
      "outOfStock": false,
      "pagination": {
        "pageNumberRequested": 0,
        "pageNumberTotal": 1,
        "pageSize": 300,
      },
      "productCards": {
        "872677001": {
          "outOfStock": false,
        },
      },
      "productImages": {
        "872677001": {
          "index": 0,
          "productUrl": "http://localhost:3000/browse/product.do?pid=872677001&cid=1124498&vid=1",
          "src": "/webcontent/0051/851/315/cn51851315.jpg",
        },
      },
      "productInfos": {
        "872677001": {
          "pcid": "872677001",
          "productMarketingFlag": {
            "marketingFlagName": "",
          },
          "productName": {
            "clickable": true,
            "link": "http://localhost:3000/browse/product.do?pid=872677001&cid=1124498&vid=1",
            "name": "Mid Rise Destructed Universal Legging Jeans with Washwell",
          },
          "productPrice": {
            "adapter": {
              "currentMaxPrice": "24.97",
              "currentMinPrice": "24.97",
              "localizedRegularMaxPrice": "$",
              "maxPercentageOff": "64",
              "minPercentageOff": "64",
              "priceType": "2",
              "regularMaxPrice": "69.99",
              "regularMinPrice": "69.99",
            },
          },
        },
      },
      "productsGridIds": [
        {
          "productId": "872677001",
        },
      ],
      "quickAdd": {
        "872677001": {
          "productName": "Mid Rise Destructed Universal Legging Jeans with Washwell",
          "sellerName": undefined,
        },
      },
      "searchText": "",
      "sizeModelOptions": undefined,
      "sortingOptions": undefined,
      "totalItemCount": 1,
    },
    "isLoading": false,
    "isSuccess": true,
  },
}
`;
