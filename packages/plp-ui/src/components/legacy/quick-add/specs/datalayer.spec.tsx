// @ts-nocheck
import { mount, act } from "test-utils";
import { QuickAdd as PdpQuickAdd } from "@ecom-next/product/legacy/quick-add";
import { BrandInfoProvider } from "@ecom-next/sitewide/brand-info-provider";
import ProductImage from "..";
import {
  OnAddToBagCallback,
  ProcessedResponse,
  QuickAddProps,
  ReportingData,
} from "../types";
import { DataLayer } from "@ecom-next/core/legacy/app-state-provider/types";


jest.mock("@ecom-next/product/legacy/quick-add");

const defaultProduct = {
  productId: "*********",
  productName: "Super Skinny Jeans",
};
const productsStartTest = [
  {
    ...defaultProduct,
    mergeType: "NONE",
  },
  {
    ...defaultProduct,
    mergeType: "STYLE",
  },
] as Array<QuickAddProps>;

const defaultProductAddToBag = {
  isDropShipEnabled: true,
  ...defaultProduct,
  sellerId: "654321",
  sellerName: "SAN DISTRIBUTION COMPANY",
  mergeType: "NONE",
};
const productsAddToBagTest = [
  {
    ...defaultProductAddToBag,
  },
  {
    ...defaultProductAddToBag,
    mergeType: "STYLE",
  },
] as Array<QuickAddProps>;

describe("Quick add - datalayer", () => {
  productsStartTest.forEach((product) => {
    it("onQuickAddStart", () => {
      const utag = {
        viewWith: jest.fn(),
      };

      PdpQuickAdd.mockImplementation(() => <div>quick add</div>);

      const wrapper = mount(
        <BrandInfoProvider
          abbrBrand="gap"
          abbrNameForTealium="gp"
          brand="gap"
          brandCode={1}
        >
          <ProductImage
            productId={product.productId}
            productName={product.productName}
            mergeType={product.mergeType}
          />
        </BrandInfoProvider>,
        {
          appState: {
            datalayer: utag as unknown as DataLayer,
            abbrNameForTealium: "gp",
            breadcrumbs: {
              category: {
                name: "Jeans",
              },
            },
          },
          breakpoint: "x-large",
        }
      );

      const toggleButton = wrapper.find("button");
      toggleButton.simulate("click");

      expect(utag.viewWith).toHaveBeenCalledWith([
        {
          name: "quickadd_start",
          data: {
            event_name: "quickadd_start",
            page_name: "gp:browse:QuickAddStart",
            page_type: "QuickAddStart",
            product_brand: ["GAP"],
            product_category: ["Jeans"],
            product_cc_id: ["*********"],
            product_id: ["123456"],
            product_inventory_status: "",
            product_name: ["Super Skinny Jeans"],
            product_page_type:
              product.mergeType === "STYLE" ? ["Super PDP"] : ["Standard PDP"],
            product_dropship: ["false"],
            product_seller_name: ["GAP"],
            product_seller_id: [undefined],
            product_primary_category: ["Jeans"],
          },
        },
      ]);
    });
  });

  describe("onAddToBag", () => {
    productsAddToBagTest.forEach((product) => {
      const utag = {
        viewWith: jest.fn(),
      };
      const onAddToBagProp = jest.fn();

      const mockProcessedResponse = { status: "SUCCESS" } as ProcessedResponse;
      const mockReportingData = {
        value1: "value1",
        value2: "value2",
      } as unknown as ReportingData;

      beforeAll(() => {
        PdpQuickAdd.mockImplementation(
          ({ onAddToBag }: { onAddToBag: OnAddToBagCallback }) => (
            <button
              aria-label="button"
              className="mocked-quick-add-btn"
              onClick={() => {
                onAddToBag(mockProcessedResponse, mockReportingData);
              }}
              type="button"
            />
          )
        );

        const wrapper = mount(
          <BrandInfoProvider
            abbrBrand="gap"
            abbrNameForTealium="gp"
            brand="gap"
            brandCode={1}
          >
            <ProductImage
              isDropShipEnabled={product.isDropShipEnabled}
              onAddToBag={onAddToBagProp}
              productId={product.productId}
              productName={product.productName}
              sellerId={product.sellerId}
              sellerName={product.sellerName}
              mergeType={product.mergeType}
            />
          </BrandInfoProvider>,
          {
            appState: {
              datalayer: utag as unknown as DataLayer,
              abbrNameForTealium: "gp",
              breadcrumbs: {
                category: {
                  name: "Jeans",
                },
              },
            },
          }
        );

        const toggleButton = wrapper.find("button");
        toggleButton.simulate("click");

        const quickAddMockedButtton = wrapper.find(".mocked-quick-add-btn");
        quickAddMockedButtton.simulate("click");
      });

      it("onAddToBagWithDatalayer", () => {
        expect(utag.viewWith).toHaveBeenNthCalledWith(2, [
          {
            name: "cart_add",
            data: {
              ...mockReportingData,
              event_name: "cart_add",
              page_name: "gp:browse:QuickAdd",
              page_type: "QuickAdd",
              product_brand: ["GAP"],
              product_cc_id: ["*********"],
              product_id: ["123456"],
              product_inventory_status: undefined,
              product_page_type:
                product.mergeType === "STYLE"
                  ? ["Super PDP"]
                  : ["Standard PDP"],
              product_primary_category: ["Jeans"],
              product_category: ["Jeans"],
              product_dropship: ["true"],
              product_seller_name: ["SAN DISTRIBUTION COMPANY"],
              product_seller_id: ["654321"],
            },
          },
        ]);
      });

      it("onAddToBagProp", () => {
        expect(onAddToBagProp).toHaveBeenCalledWith(
          mockProcessedResponse,
          mockReportingData
        );
      });
    });
  });
});
