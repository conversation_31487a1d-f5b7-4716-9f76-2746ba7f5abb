// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Sticky Wrapper > base snapshot > renders BR Redesign state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders BR Redesign state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders BR Redesign state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: none;
  color: #000;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: none;
  color: #000;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders BR Redesign state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: none;
  color: #000;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: none;
  color: #000;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders BR Redesign state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: none;
  color: #000;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: none;
  color: #000;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders BR Redesign state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: none;
  color: #000;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: none;
  color: #000;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders BR Redesign state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders BR Redesign state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders BR Redesign state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders BR Redesign state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders BR Redesign state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders BR Redesign state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders default state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders default state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders default state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: none;
  color: #000;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: none;
  color: #000;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders default state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: none;
  color: #000;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: none;
  color: #000;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders default state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: none;
  color: #000;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: none;
  color: #000;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders default state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: none;
  color: #000;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: none;
  color: #000;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders default state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders default state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders default state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders default state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders default state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders default state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders with Marketing Banner state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders with Marketing Banner state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders with Marketing Banner state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: transparent;
  color: #f6f5f2;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: transparent;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders with Marketing Banner state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: transparent;
  color: #f6f5f2;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: transparent;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders with Marketing Banner state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: transparent;
  color: #f6f5f2;
  border-color: transparent;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: transparent;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders with Marketing Banner state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: transparent;
  color: #f6f5f2;
  border-color: transparent;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: transparent;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders with Marketing Banner state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders with Marketing Banner state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders with Marketing Banner state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders with Marketing Banner state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders with Marketing Banner state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders with Marketing Banner state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders without Marketing Banner state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders without Marketing Banner state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders without Marketing Banner state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: none;
  color: #000;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: none;
  color: #000;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders without Marketing Banner state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: none;
  color: #000;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: none;
  color: #000;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders without Marketing Banner state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: none;
  color: #000;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: none;
  color: #000;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders without Marketing Banner state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 button {
  background-color: none;
  color: #000;
}

.emotion-0 button::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button:hover {
  background-color: #000;
  color: #f6f5f2;
}

.emotion-0 button:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 button#filterButton::before {
  content: none;
}

.emotion-0 select {
  background-color: none;
  color: #000;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000000;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0 select:hover {
  background-color: #000;
  color: #f6f5f2;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
}

.emotion-0 select:hover::before {
  content: "";
  background: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23f6f5f2;fill-rule:evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  width: 1.5rem;
  height: 2rem;
  display: block;
  position: absolute;
  right: 1px;
  top: 1px;
  -webkit-background-position: 2px 18px;
  background-position: 2px 18px;
}

.emotion-0>div {
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 0;
  padding-top: 0;
}

.emotion-0>div .limit-width,
.emotion-0>div [data-testid="grid-buttons-container"] {
  display: block;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders without Marketing Banner state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders without Marketing Banner state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders without Marketing Banner state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders without Marketing Banner state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders without Marketing Banner state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > base snapshot > renders without Marketing Banner state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders default state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders default state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders default state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 .filter_button {
  min-height: 4rem;
  border: none;
  padding: 0 2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: none;
  color: #000;
}

.emotion-0>div {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 0;
  padding-top: 0;
  -webkit-transition: all 0.5s ease-in;
  transition: all 0.5s ease-in;
}

.emotion-0>div .limit-width {
  display: block;
}

.emotion-0>div [data-testid="grid-buttons-container"] {
  min-height: 4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-0>div .filter-container {
  min-height: 4.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 98%;
  background-color: #2C2824;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders default state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 .filter_button {
  min-height: 4rem;
  border: none;
  padding: 0 2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: none;
  color: #000;
}

.emotion-0>div {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 0;
  padding-top: 0;
  -webkit-transition: all 0.5s ease-in;
  transition: all 0.5s ease-in;
}

.emotion-0>div .limit-width {
  display: block;
}

.emotion-0>div [data-testid="grid-buttons-container"] {
  min-height: 4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-0>div .filter-container {
  min-height: 4.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 98%;
  background-color: #2C2824;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders default state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 .filter_button {
  min-height: 4rem;
  border: none;
  padding: 0 2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: none;
  color: #000;
}

.emotion-0>div {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 0;
  padding-top: 0;
  -webkit-transition: all 0.5s ease-in;
  transition: all 0.5s ease-in;
}

.emotion-0>div .limit-width {
  display: block;
}

.emotion-0>div [data-testid="grid-buttons-container"] {
  min-height: 4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-0>div .filter-container {
  min-height: 4.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 98%;
  background-color: #2C2824;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders default state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 .filter_button {
  min-height: 4rem;
  border: none;
  padding: 0 2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: none;
  color: #000;
}

.emotion-0>div {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 0;
  padding-top: 0;
  -webkit-transition: all 0.5s ease-in;
  transition: all 0.5s ease-in;
}

.emotion-0>div .limit-width {
  display: block;
}

.emotion-0>div [data-testid="grid-buttons-container"] {
  min-height: 4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-0>div .filter-container {
  min-height: 4.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 98%;
  background-color: #2C2824;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders default state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders default state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders default state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders default state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders default state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders default state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders with Marketing Banner state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders with Marketing Banner state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders with Marketing Banner state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 .filter_button {
  min-height: 4rem;
  border: none;
  padding: 0 2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  color: #f6f5f2;
}

.emotion-0>div {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 0;
  padding-top: 0;
  -webkit-transition: all 0.5s ease-in;
  transition: all 0.5s ease-in;
}

.emotion-0>div .limit-width {
  display: block;
}

.emotion-0>div [data-testid="grid-buttons-container"] {
  min-height: 4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-0>div .filter-container {
  min-height: 4.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 98%;
  background-color: #2C2824;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders with Marketing Banner state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 .filter_button {
  min-height: 4rem;
  border: none;
  padding: 0 2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  color: #f6f5f2;
}

.emotion-0>div {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 0;
  padding-top: 0;
  -webkit-transition: all 0.5s ease-in;
  transition: all 0.5s ease-in;
}

.emotion-0>div .limit-width {
  display: block;
}

.emotion-0>div [data-testid="grid-buttons-container"] {
  min-height: 4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-0>div .filter-container {
  min-height: 4.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 98%;
  background-color: #2C2824;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders with Marketing Banner state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 .filter_button {
  min-height: 4rem;
  border: none;
  padding: 0 2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  color: #f6f5f2;
}

.emotion-0>div {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 0;
  padding-top: 0;
  -webkit-transition: all 0.5s ease-in;
  transition: all 0.5s ease-in;
}

.emotion-0>div .limit-width {
  display: block;
}

.emotion-0>div [data-testid="grid-buttons-container"] {
  min-height: 4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-0>div .filter-container {
  min-height: 4.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 98%;
  background-color: #2C2824;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders with Marketing Banner state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 .filter_button {
  min-height: 4rem;
  border: none;
  padding: 0 2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  color: #f6f5f2;
}

.emotion-0>div {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 0;
  padding-top: 0;
  -webkit-transition: all 0.5s ease-in;
  transition: all 0.5s ease-in;
}

.emotion-0>div .limit-width {
  display: block;
}

.emotion-0>div [data-testid="grid-buttons-container"] {
  min-height: 4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-0>div .filter-container {
  min-height: 4.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 98%;
  background-color: #2C2824;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders with Marketing Banner state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders with Marketing Banner state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders with Marketing Banner state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders with Marketing Banner state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders with Marketing Banner state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders with Marketing Banner state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders without Marketing Banner state correctly > Athleta 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders without Marketing Banner state correctly > Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders without Marketing Banner state correctly > BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 .filter_button {
  min-height: 4rem;
  border: none;
  padding: 0 2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: none;
  color: #000;
}

.emotion-0>div {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 0;
  padding-top: 0;
  -webkit-transition: all 0.5s ease-in;
  transition: all 0.5s ease-in;
}

.emotion-0>div .limit-width {
  display: block;
}

.emotion-0>div [data-testid="grid-buttons-container"] {
  min-height: 4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-0>div .filter-container {
  min-height: 4.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 98%;
  background-color: #2C2824;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders without Marketing Banner state correctly > BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 .filter_button {
  min-height: 4rem;
  border: none;
  padding: 0 2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: none;
  color: #000;
}

.emotion-0>div {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 0;
  padding-top: 0;
  -webkit-transition: all 0.5s ease-in;
  transition: all 0.5s ease-in;
}

.emotion-0>div .limit-width {
  display: block;
}

.emotion-0>div [data-testid="grid-buttons-container"] {
  min-height: 4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-0>div .filter-container {
  min-height: 4.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 98%;
  background-color: #2C2824;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders without Marketing Banner state correctly > BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 .filter_button {
  min-height: 4rem;
  border: none;
  padding: 0 2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: none;
  color: #000;
}

.emotion-0>div {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 0;
  padding-top: 0;
  -webkit-transition: all 0.5s ease-in;
  transition: all 0.5s ease-in;
}

.emotion-0>div .limit-width {
  display: block;
}

.emotion-0>div [data-testid="grid-buttons-container"] {
  min-height: 4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-0>div .filter-container {
  min-height: 4.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 98%;
  background-color: #2C2824;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders without Marketing Banner state correctly > BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 .filter_button {
  min-height: 4rem;
  border: none;
  padding: 0 2rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: none;
  color: #000;
}

.emotion-0>div {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-bottom: 0;
  padding-top: 0;
  -webkit-transition: all 0.5s ease-in;
  transition: all 0.5s ease-in;
}

.emotion-0>div .limit-width {
  display: block;
}

.emotion-0>div [data-testid="grid-buttons-container"] {
  min-height: 4rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-0>div .filter-container {
  min-height: 4.5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  opacity: 98%;
  background-color: #2C2824;
}

<div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders without Marketing Banner state correctly > Gap 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders without Marketing Banner state correctly > Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders without Marketing Banner state correctly > GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders without Marketing Banner state correctly > GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders without Marketing Banner state correctly > OldNavy 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;

exports[`Sticky Wrapper > br redesign slice 2 snapshot > renders without Marketing Banner state correctly > OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    class="emotion-0"
    data-testid="sticky-rail"
  />
</DocumentFragment>
`;
