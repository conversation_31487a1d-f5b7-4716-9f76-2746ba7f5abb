// @ts-nocheck
import { createFacets, generateNoProductsResponse } from '../bloomreach-helper';
import { bloomreachEqualMinMaxPriceResponse, bloomreachExpectedPriceTransformation, bloomreachPriceResponse, bloomreachPriceResponseUpdated, searchFacetOptionListMaxUpdated } from './fixture/bloomreach-helper-fixture';
import { campaign, expectedTransformedNoProductsResponse, expectedTransformedNoProductsResponseWithCampagin } from './fixture/bloomreach-mapper-fixture';

beforeEach(() => {
  jest.resetAllMocks();
});

describe('BloomReach Helper Fns', () => {
  describe('generateNoProductsResponse', () => {
    it('should use provided searchText', () => {
      const searchText = 'asdfadsfdsf';
      const noProductResponse = generateNoProductsResponse(searchText);
      expect(noProductResponse).toEqual(expectedTransformedNoProductsResponse);
    });
    it('should use provided campaign data', () => {
      const searchText = 'asdfadsfdsf';
      const noProductResponse = generateNoProductsResponse(searchText, campaign);
      expect(noProductResponse).toEqual(expectedTransformedNoProductsResponseWithCampagin);
    });
  });

  describe('createFacets', () => {
    const facetFields = {
      price: bloomreachPriceResponse.stats.stats_fields.sale_price,
    };

    describe('when provided facets', () => {
      let originalHash;
      beforeEach(() => {
        originalHash = window.location.hash;
      });
      afterEach(() => {
        global.window.location.hash = originalHash;
      });
      describe('the department facet', () => {
        it('should sort the department options in the desired order', () => {
          const currentFacets = createFacets({
            department: [
              {
                count: 762,
                name: 'Women',
              },
              {
                count: 361,
                name: 'Girls',
              },
              {
                count: 361,
                name: 'Men',
              },
              {
                count: 361,
                name: 'Boys',
              },
              {
                count: 361,
                name: 'Baby Boys',
              },
              {
                count: 361,
                name: 'Baby Girls',
              },
              {
                count: 361,
                name: 'Some other department',
              },
            ],
          });
          const expectedFacets = {
            isActive: 'true',
            searchFacetId: 'department',
            searchFacetName: 'Department',
            searchFacetOptionGroupList: {
              searchFacetOptionList: [
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Women',
                  searchFacetOptionName: 'Women',
                  searchFacetOptionValue: 'Women',
                },
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Men',
                  searchFacetOptionName: 'Men',
                  searchFacetOptionValue: 'Men',
                },
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Girls',
                  searchFacetOptionName: 'Girls',
                  searchFacetOptionValue: 'Girls',
                },
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Boys',
                  searchFacetOptionName: 'Boys',
                  searchFacetOptionValue: 'Boys',
                },
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Baby Girls',
                  searchFacetOptionName: 'Baby Girls',
                  searchFacetOptionValue: 'Baby Girls',
                },
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Baby Boys',
                  searchFacetOptionName: 'Baby Boys',
                  searchFacetOptionValue: 'Baby Boys',
                },
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Some other department',
                  searchFacetOptionName: 'Some other department',
                  searchFacetOptionValue: 'Some other department',
                },
              ],
            },
          };

          expect(currentFacets).toEqual({
            searchFacetList: [expectedFacets],
          });
        });
        describe('and response contain one department', () => {
          it('should provide department, style, size, price, and color facet', () => {
            global.window.location.hash = '';
            const currentFacets = createFacets({
              style: [{ count: 330, name: 'Dresses' }],
              color_facet: [{ count: 330, name: 'blue' }],
              sizes: [
                {
                  count: 2,
                  name: 'one pieces & sets|size|regular|up to 7 lbs|',
                },
              ],
              department: [
                {
                  count: 21,
                  name: 'mens',
                },
              ],
              price: { max: 79.99, min: 0.01 },
            });
            const expectedFacets = [
              {
                isActive: 'true',
                searchFacetId: 'department',
                searchFacetName: 'Department',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isActive: 'true',
                      isSelected: 'false',
                      searchFacetOptionId: 'mens',
                      searchFacetOptionName: 'mens',
                      searchFacetOptionValue: 'mens',
                    },
                  ],
                },
              },
              {
                isActive: 'true',
                searchFacetId: 'style',
                searchFacetName: 'Style',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'Dresses',
                      searchFacetOptionName: 'Dresses',
                      searchFacetOptionValue: 'Dresses',
                    },
                  ],
                },
              },
              {
                isActive: 'true',
                searchFacetId: 'size',
                searchFacetName: 'Size',
                searchFacetOptionGroupList: [
                  {
                    searchFacetOptionGroupId: 'one pieces & sets',
                    searchFacetOptionGroupList: [
                      {
                        parentSearchFacetOptionGroupId: 'one pieces & sets',
                        searchFacetOptionGroupId: 'size',
                        searchFacetOptionGroupList: [
                          {
                            parentSearchFacetOptionGroupId: 'size',
                            searchFacetOptionGroupId: '1',
                            searchFacetOptionGroupName: 'regular',
                            searchFacetOptionList: [
                              {
                                isActive: 'true',
                                isSelected: 'false',
                                parentSearchFacetOptionGroupId: 'regular',
                                searchFacetOptionId: '_one pieces & sets_size_regular_up to 7 lbs',
                                searchFacetOptionName: 'up to 7 lbs',
                                searchFacetOptionValue:
                                  '_one pieces & sets_size_regular_up to 7 lbs',
                              },
                            ],
                          },
                        ],
                        searchFacetOptionGroupName: 'size',
                      },
                    ],
                    searchFacetOptionGroupName: 'one pieces & sets',
                  },
                ],
              },
              {
                isActive: 'true',
                searchFacetId: 'color',
                searchFacetName: 'Color',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'blue',
                      searchFacetOptionName: 'blue',
                      searchFacetOptionValue: 'blue',
                    },
                  ],
                },
              },
              {
                isActive: 'true',
                searchFacetId: 'price',
                searchFacetName: 'Price',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'MIN',
                      searchFacetOptionName: '0.01',
                      searchFacetOptionValue: '0.01',
                    },
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'MAX',
                      searchFacetOptionName: '79.99',
                      searchFacetOptionValue: '79.99',
                    },
                  ],
                },
              },
            ];

            expect(currentFacets.searchFacetList).toEqual(expectedFacets);
          });

          it('should provide department, style, size, price, and color facet when alwaysShowDepartmentFacet toggle is set to true ', () => {
            global.window.location.hash = '';
            const facets = {
              style: [{ count: 330, name: 'Dresses' }],
              color_facet: [{ count: 330, name: 'blue' }],
              sizes: [
                {
                  count: 2,
                  name: 'one pieces & sets|size|regular|up to 7 lbs|',
                },
              ],
              department: [
                {
                  count: 21,
                  name: 'mens',
                },
              ],
              price: { max: 79.99, min: 0.01 },
            };
            const options = {
              alwaysShowDepartmentFacet: true,
            };
            const currentFacets = createFacets(facets, options);
            const expectedFacets = [
              {
                isActive: 'true',
                searchFacetId: 'department',
                searchFacetName: 'Department',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'mens',
                      searchFacetOptionName: 'mens',
                      searchFacetOptionValue: 'mens',
                    },
                  ],
                },
              },
              {
                isActive: 'true',
                searchFacetId: 'style',
                searchFacetName: 'Style',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'Dresses',
                      searchFacetOptionName: 'Dresses',
                      searchFacetOptionValue: 'Dresses',
                    },
                  ],
                },
              },
              {
                isActive: 'true',
                searchFacetId: 'size',
                searchFacetName: 'Size',
                searchFacetOptionGroupList: [
                  {
                    searchFacetOptionGroupId: 'one pieces & sets',
                    searchFacetOptionGroupList: [
                      {
                        parentSearchFacetOptionGroupId: 'one pieces & sets',
                        searchFacetOptionGroupId: 'size',
                        searchFacetOptionGroupList: [
                          {
                            parentSearchFacetOptionGroupId: 'size',
                            searchFacetOptionGroupId: '1',
                            searchFacetOptionGroupName: 'regular',
                            searchFacetOptionList: [
                              {
                                isActive: 'true',
                                isSelected: 'false',
                                parentSearchFacetOptionGroupId: 'regular',
                                searchFacetOptionId: '_one pieces & sets_size_regular_up to 7 lbs',
                                searchFacetOptionName: 'up to 7 lbs',
                                searchFacetOptionValue:
                                  '_one pieces & sets_size_regular_up to 7 lbs',
                              },
                            ],
                          },
                        ],
                        searchFacetOptionGroupName: 'size',
                      },
                    ],
                    searchFacetOptionGroupName: 'one pieces & sets',
                  },
                ],
              },
              {
                isActive: 'true',
                searchFacetId: 'color',
                searchFacetName: 'Color',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'blue',
                      searchFacetOptionName: 'blue',
                      searchFacetOptionValue: 'blue',
                    },
                  ],
                },
              },
              {
                isActive: 'true',
                searchFacetId: 'price',
                searchFacetName: 'Price',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'MIN',
                      searchFacetOptionName: '0.01',
                      searchFacetOptionValue: '0.01',
                    },
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'MAX',
                      searchFacetOptionName: '79.99',
                      searchFacetOptionValue: '79.99',
                    },
                  ],
                },
              },
            ];

            expect(currentFacets.searchFacetList).toEqual(expectedFacets);
          });
        });
        describe('and response contain many departments', () => {
          describe('no department selected', () => {
            it('should provide department, price, and color facet', () => {
              const currentFacets = createFacets({
                style: [{ count: 330, name: 'Dresses' }],
                color_facet: [{ count: 330, name: 'blue' }],
                sizes: [
                  {
                    count: 2,
                    name: 'one pieces & sets|size|regular|up to 7 lbs|',
                  },
                ],
                department: [
                  {
                    count: 21,
                    name: 'mens',
                  },
                  {
                    count: 21,
                    name: 'womens',
                  },
                ],
                price: { max: 79.99, min: 0.01 },
              });
              const expectedFacets = [
                {
                  isActive: 'true',
                  searchFacetId: 'department',
                  searchFacetName: 'Department',
                  searchFacetOptionGroupList: {
                    searchFacetOptionList: [
                      {
                        isSelected: 'false',
                        isActive: 'true',
                        searchFacetOptionId: 'mens',
                        searchFacetOptionName: 'mens',
                        searchFacetOptionValue: 'mens',
                      },
                      {
                        isSelected: 'false',
                        isActive: 'true',
                        searchFacetOptionId: 'womens',
                        searchFacetOptionName: 'womens',
                        searchFacetOptionValue: 'womens',
                      },
                    ],
                  },
                },
                {
                  isActive: 'true',
                  searchFacetId: 'color',
                  searchFacetName: 'Color',
                  searchFacetOptionGroupList: {
                    searchFacetOptionList: [
                      {
                        isSelected: 'false',
                        isActive: 'true',
                        searchFacetOptionId: 'blue',
                        searchFacetOptionName: 'blue',
                        searchFacetOptionValue: 'blue',
                      },
                    ],
                  },
                },
                {
                  isActive: 'true',
                  searchFacetId: 'price',
                  searchFacetName: 'Price',
                  searchFacetOptionGroupList: {
                    searchFacetOptionList: [
                      {
                        isSelected: 'false',
                        isActive: 'true',
                        searchFacetOptionId: 'MIN',
                        searchFacetOptionName: '0.01',
                        searchFacetOptionValue: '0.01',
                      },
                      {
                        isSelected: 'false',
                        isActive: 'true',
                        searchFacetOptionId: 'MAX',
                        searchFacetOptionName: '79.99',
                        searchFacetOptionValue: '79.99',
                      },
                    ],
                  },
                },
              ];

              expect(currentFacets.searchFacetList).toEqual(expectedFacets);
            });
          });
          describe('one department selected', () => {
            it('should provide department, size, style, price, and color facet', () => {
              const testHash = 'department=mens';
              global.window.location.hash = testHash;
              const currentFacets = createFacets({
                style: [{ count: 330, name: 'Dresses' }],
                color_facet: [{ count: 330, name: 'blue' }],
                sizes: [
                  {
                    count: 2,
                    name: 'one pieces & sets|size|regular|up to 7 lbs|',
                  },
                ],
                department: [
                  {
                    count: 21,
                    name: 'mens',
                  },
                  {
                    count: 21,
                    name: 'womens',
                  },
                ],
                price: { max: 79.99, min: 0.01 },
              });
              const expectedFacets = [
                {
                  isActive: 'true',
                  searchFacetId: 'department',
                  searchFacetName: 'Department',
                  searchFacetOptionGroupList: {
                    searchFacetOptionList: [
                      {
                        isSelected: 'true',
                        isActive: 'true',
                        searchFacetOptionId: 'mens',
                        searchFacetOptionName: 'mens',
                        searchFacetOptionValue: 'mens',
                      },
                      {
                        isSelected: 'false',
                        isActive: 'true',
                        searchFacetOptionId: 'womens',
                        searchFacetOptionName: 'womens',
                        searchFacetOptionValue: 'womens',
                      },
                    ],
                  },
                },
                {
                  isActive: 'true',
                  searchFacetId: 'style',
                  searchFacetName: 'Style',
                  searchFacetOptionGroupList: {
                    searchFacetOptionList: [
                      {
                        isSelected: 'false',
                        isActive: 'true',
                        searchFacetOptionId: 'Dresses',
                        searchFacetOptionName: 'Dresses',
                        searchFacetOptionValue: 'Dresses',
                      },
                    ],
                  },
                },
                {
                  isActive: 'true',
                  searchFacetId: 'size',
                  searchFacetName: 'Size',
                  searchFacetOptionGroupList: [
                    {
                      searchFacetOptionGroupId: 'one pieces & sets',
                      searchFacetOptionGroupList: [
                        {
                          parentSearchFacetOptionGroupId: 'one pieces & sets',
                          searchFacetOptionGroupId: 'size',
                          searchFacetOptionGroupList: [
                            {
                              parentSearchFacetOptionGroupId: 'size',
                              searchFacetOptionGroupId: '1',
                              searchFacetOptionGroupName: 'regular',
                              searchFacetOptionList: [
                                {
                                  isActive: 'true',
                                  isSelected: 'false',
                                  parentSearchFacetOptionGroupId: 'regular',
                                  searchFacetOptionId:
                                    '_one pieces & sets_size_regular_up to 7 lbs',
                                  searchFacetOptionName: 'up to 7 lbs',
                                  searchFacetOptionValue:
                                    '_one pieces & sets_size_regular_up to 7 lbs',
                                },
                              ],
                            },
                          ],
                          searchFacetOptionGroupName: 'size',
                        },
                      ],
                      searchFacetOptionGroupName: 'one pieces & sets',
                    },
                  ],
                },
                {
                  isActive: 'true',
                  searchFacetId: 'color',
                  searchFacetName: 'Color',
                  searchFacetOptionGroupList: {
                    searchFacetOptionList: [
                      {
                        isSelected: 'false',
                        isActive: 'true',
                        searchFacetOptionId: 'blue',
                        searchFacetOptionName: 'blue',
                        searchFacetOptionValue: 'blue',
                      },
                    ],
                  },
                },
                {
                  isActive: 'true',
                  searchFacetId: 'price',
                  searchFacetName: 'Price',
                  searchFacetOptionGroupList: {
                    searchFacetOptionList: [
                      {
                        isSelected: 'false',
                        isActive: 'true',
                        searchFacetOptionId: 'MIN',
                        searchFacetOptionName: '0.01',
                        searchFacetOptionValue: '0.01',
                      },
                      {
                        isSelected: 'false',
                        isActive: 'true',
                        searchFacetOptionId: 'MAX',
                        searchFacetOptionName: '79.99',
                        searchFacetOptionValue: '79.99',
                      },
                    ],
                  },
                },
              ];

              expect(currentFacets.searchFacetList).toEqual(expectedFacets);
            });
          });
        });
      });

      describe('the price facet', () => {
        it('should return a formatted facet list item', () => {
          expect(createFacets(facetFields)).toEqual(bloomreachExpectedPriceTransformation);
        });

        describe('range provided in currentURL', () => {
          it('should set MIN_SELECTED and MAX_SELECTED based on currentURL', () => {
            window.location.href = 'http://localhost/#&price=1-40';
            const currentFacets = createFacets({
              price: bloomreachPriceResponseUpdated.stats.stats_fields.sale_price,
            });
            const {
              searchFacetOptionList: priceFacetOptionList,
            } = currentFacets.searchFacetList[0].searchFacetOptionGroupList;

            expect(priceFacetOptionList[2]).toEqual(searchFacetOptionListMaxUpdated[2]);
            expect(priceFacetOptionList[3]).toEqual(searchFacetOptionListMaxUpdated[3]);
            expect(priceFacetOptionList[0].searchFacetOptionId).toBe('MIN');
            expect(priceFacetOptionList[1].searchFacetOptionId).toBe('MAX');
            expect(priceFacetOptionList[2].searchFacetOptionId).toBe('MIN_SELECTED');
            expect(priceFacetOptionList[3].searchFacetOptionId).toBe('MAX_SELECTED');
          });
        });

        describe('range not provided in currentURL', () => {
          it('should not set MIN_SELECTED or MAX_SELECTED', () => {
            const currentFacets = createFacets({
              price: bloomreachPriceResponseUpdated.stats.stats_fields.sale_price,
            });
            const {
              searchFacetOptionList: priceFacetOptionList,
            } = currentFacets.searchFacetList[0].searchFacetOptionGroupList;

            expect(priceFacetOptionList.length).toBe(2);
            expect(priceFacetOptionList[0].searchFacetOptionId).toBe('MIN');
            expect(priceFacetOptionList[1].searchFacetOptionId).toBe('MAX');
          });
        });

        describe('when max and min are equal', () => {
          it('should still include price in the response', () => {
            const currentFacets = createFacets({
              price: bloomreachEqualMinMaxPriceResponse.stats.stats_fields.sale_price,
            });
            expect(currentFacets.searchFacetList.length).toBe(1);
          });

          it('should include price in the response when alwaysShowPriceFacet toggle is set to true', () => {
            const facets = {
              price: bloomreachEqualMinMaxPriceResponse.stats.stats_fields.sale_price,
            };
            const options = { alwaysShowPriceFacet: true };
            const currentFacets = createFacets(facets, options);
            const {
              searchFacetOptionList: priceFacetOptionList,
            } = currentFacets.searchFacetList[0].searchFacetOptionGroupList;

            expect(currentFacets.searchFacetList.length).toBe(1);
            expect(priceFacetOptionList.length).toBe(2);
          });
        });
      });

      describe('the style facet', () => {
        const departmentFacets = [{ count: 1, name: 'Womens' }];

        describe('no styles selected in currentURL', () => {
          it('should transform the style facet', () => {
            const currentFacets = createFacets({
              style: [
                { count: 330, name: 'Dresses' },
                { count: 31, name: 'One Pieces' },
                { count: 23, name: 'T shirts' },
              ],
              department: departmentFacets,
            });
            const styleOptionList =
              currentFacets.searchFacetList[1].searchFacetOptionGroupList.searchFacetOptionList;
            const expectedFacets = [
              {
                isActive: 'true',
                searchFacetId: 'department',
                searchFacetName: 'Department',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isActive: 'true',
                      isSelected: 'false',
                      searchFacetOptionId: 'Womens',
                      searchFacetOptionName: 'Womens',
                      searchFacetOptionValue: 'Womens',
                    },
                  ],
                },
              },
              {
                isActive: 'true',
                searchFacetId: 'style',
                searchFacetName: 'Style',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'Dresses',
                      searchFacetOptionName: 'Dresses',
                      searchFacetOptionValue: 'Dresses',
                    },
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'One Pieces',
                      searchFacetOptionName: 'One Pieces',
                      searchFacetOptionValue: 'One Pieces',
                    },
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'T shirts',
                      searchFacetOptionName: 'T shirts',
                      searchFacetOptionValue: 'T shirts',
                    },
                  ],
                },
              },
            ];
            expect(currentFacets.searchFacetList).toEqual(expectedFacets);
            expect(styleOptionList.filter(option => option.isSelected === 'false').length).toBe(3);
          });
        });

        describe('at least one style selected in currentURL', () => {
          it('should transform the style facet and select style in URL selected to true', () => {
            const testHash = 'style=One Pieces,T shirts';
            global.window.location.hash = testHash;
            const currentFacets = createFacets({
              style: [
                { count: 330, name: 'Dresses' },
                { count: 31, name: 'One Pieces' },
                { count: 23, name: 'T shirts' },
              ],
              department: departmentFacets,
            });
            const expectedFacets = [
              {
                isActive: 'true',
                searchFacetId: 'department',
                searchFacetName: 'Department',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isActive: 'true',
                      isSelected: 'false',
                      searchFacetOptionId: 'Womens',
                      searchFacetOptionName: 'Womens',
                      searchFacetOptionValue: 'Womens',
                    },
                  ],
                },
              },
              {
                isActive: 'true',
                searchFacetId: 'style',
                searchFacetName: 'Style',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isSelected: 'false',
                      isActive: 'true',
                      searchFacetOptionId: 'Dresses',
                      searchFacetOptionName: 'Dresses',
                      searchFacetOptionValue: 'Dresses',
                    },
                    {
                      isSelected: 'true',
                      isActive: 'true',
                      searchFacetOptionId: 'One Pieces',
                      searchFacetOptionName: 'One Pieces',
                      searchFacetOptionValue: 'One Pieces',
                    },
                    {
                      isSelected: 'true',
                      isActive: 'true',
                      searchFacetOptionId: 'T shirts',
                      searchFacetOptionName: 'T shirts',
                      searchFacetOptionValue: 'T shirts',
                    },
                  ],
                },
              },
            ];
            expect(currentFacets.searchFacetList).toEqual(expectedFacets);
            const styleOptionList =
              currentFacets.searchFacetList[1].searchFacetOptionGroupList.searchFacetOptionList;
            const dressFacet = styleOptionList.shift();

            expect(dressFacet.isSelected).toBe('false');
            expect(styleOptionList.filter(option => option.isSelected === 'true').length).toBe(2);
          });
        });
      });

      describe('the size facet', () => {
        it('should not transform the size facet when department not selected', () => {
          const currentFacets = createFacets({
            sizes: [{ count: 2, name: 'one pieces & sets|size|regular|up to 7 lbs|' }],
          });
          expect(currentFacets).toEqual({ searchFacetList: [] });
        });

        describe('the department is selected', () => {
          it('should transform the size facet', () => {
            global.window.location.hash = 'department=ponoma';
            const currentFacets = createFacets({
              sizes: [
                {
                  count: 2,
                  name: 'one pieces & sets|size|regular|up to 7 lbs|',
                },
              ],
            });
            const expectedFacets = {
              isActive: 'true',
              searchFacetId: 'size',
              searchFacetName: 'Size',
              searchFacetOptionGroupList: [
                {
                  searchFacetOptionGroupId: 'one pieces & sets',
                  searchFacetOptionGroupList: [
                    {
                      parentSearchFacetOptionGroupId: 'one pieces & sets',
                      searchFacetOptionGroupId: 'size',
                      searchFacetOptionGroupList: [
                        {
                          parentSearchFacetOptionGroupId: 'size',
                          searchFacetOptionGroupId: '1',
                          searchFacetOptionGroupName: 'regular',
                          searchFacetOptionList: [
                            {
                              isActive: 'true',
                              isSelected: 'false',
                              parentSearchFacetOptionGroupId: 'regular',
                              searchFacetOptionId: '_one pieces & sets_size_regular_up to 7 lbs',
                              searchFacetOptionName: 'up to 7 lbs',
                              searchFacetOptionValue: '_one pieces & sets_size_regular_up to 7 lbs',
                            },
                          ],
                        },
                      ],
                      searchFacetOptionGroupName: 'size',
                    },
                  ],
                  searchFacetOptionGroupName: 'one pieces & sets',
                },
              ],
            };

            expect(currentFacets).toEqual({
              searchFacetList: [expectedFacets],
            });
            expect(
              currentFacets.searchFacetList[0].searchFacetOptionGroupList[0]
                .searchFacetOptionGroupList[0].searchFacetOptionGroupList[0]
                .searchFacetOptionList[0].isSelected,
            ).toBe('false');
          });

          describe('there is one preselected size in the url', () => {
            it('should return size as selected', () => {
              window.location.hash =
                'department=Baby%20Girls&size=one%20pieces%20%26%20sets-size%3A_one%20pieces%20%26%20sets_size_regular_up%20to%207%20lbs';

              const currentFacets = createFacets({
                sizes: [
                  {
                    count: 2,
                    name: 'one pieces & sets|size|regular|up to 7 lbs|',
                  },
                ],
              });
              const expectedFacets = {
                isActive: 'true',
                searchFacetId: 'size',
                searchFacetName: 'Size',
                searchFacetOptionGroupList: [
                  {
                    searchFacetOptionGroupId: 'one pieces & sets',
                    searchFacetOptionGroupList: [
                      {
                        parentSearchFacetOptionGroupId: 'one pieces & sets',
                        searchFacetOptionGroupId: 'size',
                        searchFacetOptionGroupList: [
                          {
                            parentSearchFacetOptionGroupId: 'size',
                            searchFacetOptionGroupId: '1',
                            searchFacetOptionGroupName: 'regular',
                            searchFacetOptionList: [
                              {
                                isActive: 'true',
                                isSelected: 'true',
                                parentSearchFacetOptionGroupId: 'regular',
                                searchFacetOptionId: '_one pieces & sets_size_regular_up to 7 lbs',
                                searchFacetOptionName: 'up to 7 lbs',
                                searchFacetOptionValue:
                                  '_one pieces & sets_size_regular_up to 7 lbs',
                              },
                            ],
                          },
                        ],
                        searchFacetOptionGroupName: 'size',
                      },
                    ],
                    searchFacetOptionGroupName: 'one pieces & sets',
                  },
                ],
              };
              expect(currentFacets).toEqual({
                searchFacetList: [expectedFacets],
              });
            });
          });

          describe('there are more than one preselected sizes in the url', () => {
            it('should return size as selected', () => {
              window.location.hash =
                'department=Baby%20Girls&size=one%20pieces%20%26%20sets-size%3A_one%20pieces%20%26%20sets_size_regular_up%20to%207%20lbs%2C_one%20pieces%20%26%20sets_size_regular_12-18%20m';
              const currentFacets = createFacets({
                sizes: [
                  {
                    count: 2,
                    name: 'one pieces & sets|size|regular|up to 7 lbs|',
                  },
                  { count: 9, name: 'one pieces & sets|size|regular|12-18 m|' },
                ],
              });
              const expectedFacets = {
                isActive: 'true',
                searchFacetId: 'size',
                searchFacetName: 'Size',
                searchFacetOptionGroupList: [
                  {
                    searchFacetOptionGroupId: 'one pieces & sets',
                    searchFacetOptionGroupList: [
                      {
                        parentSearchFacetOptionGroupId: 'one pieces & sets',
                        searchFacetOptionGroupId: 'size',
                        searchFacetOptionGroupList: [
                          {
                            parentSearchFacetOptionGroupId: 'size',
                            searchFacetOptionGroupId: '1',
                            searchFacetOptionGroupName: 'regular',
                            searchFacetOptionList: [
                              {
                                isActive: 'true',
                                isSelected: 'true',
                                parentSearchFacetOptionGroupId: 'regular',
                                searchFacetOptionId: '_one pieces & sets_size_regular_up to 7 lbs',
                                searchFacetOptionName: 'up to 7 lbs',
                                searchFacetOptionValue:
                                  '_one pieces & sets_size_regular_up to 7 lbs',
                              },
                              {
                                isActive: 'true',
                                isSelected: 'true',
                                parentSearchFacetOptionGroupId: 'regular',
                                searchFacetOptionId: '_one pieces & sets_size_regular_12-18 m',
                                searchFacetOptionName: '12-18 m',
                                searchFacetOptionValue: '_one pieces & sets_size_regular_12-18 m',
                              },
                            ],
                          },
                        ],
                        searchFacetOptionGroupName: 'size',
                      },
                    ],
                    searchFacetOptionGroupName: 'one pieces & sets',
                  },
                ],
              };
              expect(currentFacets).toEqual({
                searchFacetList: [expectedFacets],
              });
            });
          });
          describe('when the size has 5 descriptors', () => {
            it('should return 5 concatenated size descriptors', () => {
              window.location.hash = 'department=Baby%20Girls';
              const currentFacets = createFacets({
                sizes: [
                  {
                    count: 2,
                    name: 'one pieces & sets|size|regular|up to 7 lbs|xs',
                  },
                ],
              });
              const expectedFacets = {
                isActive: 'true',
                searchFacetId: 'size',
                searchFacetName: 'Size',
                searchFacetOptionGroupList: [
                  {
                    searchFacetOptionGroupId: 'one pieces & sets',
                    searchFacetOptionGroupList: [
                      {
                        parentSearchFacetOptionGroupId: 'one pieces & sets',
                        searchFacetOptionGroupId: 'size',
                        searchFacetOptionGroupList: [
                          {
                            parentSearchFacetOptionGroupId: 'size',
                            searchFacetOptionGroupId: '1',
                            searchFacetOptionGroupName: 'regular',
                            searchFacetOptionList: [
                              {
                                attributeMap: {
                                  dimensionId: 1,
                                  index: 0,
                                  searchFacetOptionName2: 'xs',
                                  variantId: 1,
                                },
                                isActive: 'true',
                                isSelected: 'false',
                                parentSearchFacetOptionGroupId: 'regular',
                                searchFacetOptionId:
                                  '_one pieces & sets_size_regular_up to 7 lbs_xs',
                                searchFacetOptionName: 'up to 7 lbs',
                                searchFacetOptionValue:
                                  '_one pieces & sets_size_regular_up to 7 lbs_xs',
                              },
                            ],
                          },
                        ],
                        searchFacetOptionGroupName: 'size',
                      },
                    ],
                    searchFacetOptionGroupName: 'one pieces & sets',
                  },
                ],
              };
              expect(currentFacets).toEqual({
                searchFacetList: [expectedFacets],
              });
            });
          });
        });
      });
      describe('the color facet', () => {
        describe('if there is no preselected color in the URL', () => {
          it('should transform the response and set isSelected to false for all colors', () => {
            const currentFacets = createFacets({
              color_facet: [{ count: 1, name: 'green' }, { count: 2, name: 'blue' }],
            });
            const expectedFacets = [
              {
                isActive: 'true',
                searchFacetId: 'color',
                searchFacetName: 'Color',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isActive: 'true',
                      isSelected: 'false',
                      searchFacetOptionId: 'green',
                      searchFacetOptionName: 'green',
                      searchFacetOptionValue: 'green',
                    },
                    {
                      isActive: 'true',
                      isSelected: 'false',
                      searchFacetOptionId: 'blue',
                      searchFacetOptionName: 'blue',
                      searchFacetOptionValue: 'blue',
                    },
                  ],
                },
              },
            ];
            expect(currentFacets.searchFacetList).toEqual(expectedFacets);
          });
        });
        describe('if there is a color selected', () => {
          it('should set isSelected to true for that color', () => {
            const testHash = 'color=blue';
            window.location.hash = testHash;
            const currentFacets = createFacets({
              color_facet: [{ count: 1, name: 'green' }, { count: 2, name: 'blue' }],
            });
            const expectedFacets = [
              {
                isActive: 'true',
                searchFacetId: 'color',
                searchFacetName: 'Color',
                searchFacetOptionGroupList: {
                  searchFacetOptionList: [
                    {
                      isActive: 'true',
                      isSelected: 'false',
                      searchFacetOptionId: 'green',
                      searchFacetOptionName: 'green',
                      searchFacetOptionValue: 'green',
                    },
                    {
                      isActive: 'true',
                      isSelected: 'true',
                      searchFacetOptionId: 'blue',
                      searchFacetOptionName: 'blue',
                      searchFacetOptionValue: 'blue',
                    },
                  ],
                },
              },
            ];
            expect(currentFacets.searchFacetList).toEqual(expectedFacets);
          });
        });
        it('should sort colors', () => {
          const currentFacets = createFacets({
            color_facet: [{ count: 2, name: 'blue' }, { count: 1, name: 'green' }],
          });
          const expectedFacets = [
            {
              isActive: 'true',
              searchFacetId: 'color',
              searchFacetName: 'Color',
              searchFacetOptionGroupList: {
                searchFacetOptionList: [
                  {
                    isActive: 'true',
                    isSelected: 'false',
                    searchFacetOptionId: 'green',
                    searchFacetOptionName: 'green',
                    searchFacetOptionValue: 'green',
                  },
                  {
                    isActive: 'true',
                    isSelected: 'false',
                    searchFacetOptionId: 'blue',
                    searchFacetOptionName: 'blue',
                    searchFacetOptionValue: 'blue',
                  },
                ],
              },
            },
          ];
          expect(currentFacets.searchFacetList).toEqual(expectedFacets);
        });
      });
      describe('always', () => {
        it('should return valid facets', () => {
          const testHash = '';
          global.window.location.hash = testHash;
          const currentFacets = createFacets({
            paper: [{ count: 330, name: 'Dresses' }],
            style: [{ count: 330, name: 'Dresses' }],
            color_facet: [{ count: 330, name: 'blue' }],
            sizes: [{ count: 2, name: 'one pieces & sets|size|regular|up to 7 lbs|' }],
            department: [
              {
                count: 21,
                name: 'mens',
              },
            ],
          });
          const expectedFacets = [
            {
              isActive: 'true',
              searchFacetId: 'department',
              searchFacetName: 'Department',
              searchFacetOptionGroupList: {
                searchFacetOptionList: [
                  {
                    isActive: 'true',
                    isSelected: 'false',
                    searchFacetOptionId: 'mens',
                    searchFacetOptionName: 'mens',
                    searchFacetOptionValue: 'mens',
                  },
                ],
              },
            },
            {
              isActive: 'true',
              searchFacetId: 'style',
              searchFacetName: 'Style',
              searchFacetOptionGroupList: {
                searchFacetOptionList: [
                  {
                    isSelected: 'false',
                    isActive: 'true',
                    searchFacetOptionId: 'Dresses',
                    searchFacetOptionName: 'Dresses',
                    searchFacetOptionValue: 'Dresses',
                  },
                ],
              },
            },
            {
              isActive: 'true',
              searchFacetId: 'size',
              searchFacetName: 'Size',
              searchFacetOptionGroupList: [
                {
                  searchFacetOptionGroupId: 'one pieces & sets',
                  searchFacetOptionGroupList: [
                    {
                      parentSearchFacetOptionGroupId: 'one pieces & sets',
                      searchFacetOptionGroupId: 'size',
                      searchFacetOptionGroupList: [
                        {
                          parentSearchFacetOptionGroupId: 'size',
                          searchFacetOptionGroupId: '1',
                          searchFacetOptionGroupName: 'regular',
                          searchFacetOptionList: [
                            {
                              isActive: 'true',
                              isSelected: 'false',
                              parentSearchFacetOptionGroupId: 'regular',
                              searchFacetOptionId: '_one pieces & sets_size_regular_up to 7 lbs',
                              searchFacetOptionName: 'up to 7 lbs',
                              searchFacetOptionValue: '_one pieces & sets_size_regular_up to 7 lbs',
                            },
                          ],
                        },
                      ],
                      searchFacetOptionGroupName: 'size',
                    },
                  ],
                  searchFacetOptionGroupName: 'one pieces & sets',
                },
              ],
            },
            {
              isActive: 'true',
              searchFacetId: 'color',
              searchFacetName: 'Color',
              searchFacetOptionGroupList: {
                searchFacetOptionList: [
                  {
                    isSelected: 'false',
                    isActive: 'true',
                    searchFacetOptionId: 'blue',
                    searchFacetOptionName: 'blue',
                    searchFacetOptionValue: 'blue',
                  },
                ],
              },
            },
          ];
          expect(currentFacets.searchFacetList).toEqual(expectedFacets);
        });
      });
    });

    describe('when provided facets and configuration', () => {
      let originalHash;
      beforeEach(() => {
        originalHash = window.location.hash;
      });
      afterEach(() => {
        global.window.location.hash = originalHash;
      });
      describe('Color Facet', () => {
        it('should use the color_facet field to build the facet', () => {
          const TEST_CONFIG = {
            enabledFeatures: {
              'search-bloomreach': true,
              'autosuggest-bloomreach': true,
            },
          };
          const currentFacets = createFacets(
            {
              color_groups: [{ count: 1, name: 'pink' }, { count: 2, name: 'purple' }],
              colors: [{ count: 1, name: 'mango' }, { count: 2, name: 'banana' }],
              color_facet: [{ count: 1, name: 'green' }, { count: 2, name: 'blue' }],
            },
            TEST_CONFIG,
          );
          const expectedFacets = [
            {
              isActive: 'true',
              searchFacetId: 'color',
              searchFacetName: 'Color',
              searchFacetOptionGroupList: {
                searchFacetOptionList: [
                  {
                    isActive: 'true',
                    isSelected: 'false',
                    searchFacetOptionId: 'green',
                    searchFacetOptionName: 'green',
                    searchFacetOptionValue: 'green',
                  },
                  {
                    isActive: 'true',
                    isSelected: 'false',
                    searchFacetOptionId: 'blue',
                    searchFacetOptionName: 'blue',
                    searchFacetOptionValue: 'blue',
                  },
                ],
              },
            },
          ];
          expect(currentFacets.searchFacetList).toEqual(expectedFacets);
        });
      });
    });

    describe('when flex facets', () => {
      const expectedCoreFacets = [
        {
          isActive: 'true',
          searchFacetId: 'department',
          searchFacetName: 'Department',
          searchFacetOptionGroupList: {
            searchFacetOptionList: [
              {
                isActive: 'true',
                isSelected: 'false',
                searchFacetOptionId: 'mens',
                searchFacetOptionName: 'mens',
                searchFacetOptionValue: 'mens',
              },
            ],
          },
        },
        {
          isActive: 'true',
          searchFacetId: 'style',
          searchFacetName: 'Style',
          searchFacetOptionGroupList: {
            searchFacetOptionList: [
              {
                isSelected: 'false',
                isActive: 'true',
                searchFacetOptionId: 'Dresses',
                searchFacetOptionName: 'Dresses',
                searchFacetOptionValue: 'Dresses',
              },
            ],
          },
        },
        {
          isActive: 'true',
          searchFacetId: 'size',
          searchFacetName: 'Size',
          searchFacetOptionGroupList: [
            {
              searchFacetOptionGroupId: 'one pieces & sets',
              searchFacetOptionGroupList: [
                {
                  parentSearchFacetOptionGroupId: 'one pieces & sets',
                  searchFacetOptionGroupId: 'size',
                  searchFacetOptionGroupList: [
                    {
                      parentSearchFacetOptionGroupId: 'size',
                      searchFacetOptionGroupId: '1',
                      searchFacetOptionGroupName: 'regular',
                      searchFacetOptionList: [
                        {
                          isActive: 'true',
                          isSelected: 'false',
                          parentSearchFacetOptionGroupId: 'regular',
                          searchFacetOptionId: '_one pieces & sets_size_regular_up to 7 lbs',
                          searchFacetOptionName: 'up to 7 lbs',
                          searchFacetOptionValue: '_one pieces & sets_size_regular_up to 7 lbs',
                        },
                      ],
                    },
                  ],
                  searchFacetOptionGroupName: 'size',
                },
              ],
              searchFacetOptionGroupName: 'one pieces & sets',
            },
          ],
        },
        {
          isActive: 'true',
          searchFacetId: 'color',
          searchFacetName: 'Color',
          searchFacetOptionGroupList: {
            searchFacetOptionList: [
              {
                isSelected: 'false',
                isActive: 'true',
                searchFacetOptionId: 'blue',
                searchFacetOptionName: 'blue',
                searchFacetOptionValue: 'blue',
              },
            ],
          },
        },
        {
          isActive: 'true',
          searchFacetId: 'price',
          searchFacetName: 'Price',
          searchFacetOptionGroupList: {
            searchFacetOptionList: [
              {
                isSelected: 'false',
                isActive: 'true',
                searchFacetOptionId: 'MIN',
                searchFacetOptionName: '0.01',
                searchFacetOptionValue: '0.01',
              },
              {
                isSelected: 'false',
                isActive: 'true',
                searchFacetOptionId: 'MAX',
                searchFacetOptionName: '79.99',
                searchFacetOptionValue: '79.99',
              },
            ],
          },
        },
      ];

      describe('when feature flag is enabled', () => {
        const configuration = {
          enabledFeatures: {
            'search-flex-facets': true,
          },
        };

        it('should build flex facets', () => {
          const expectedFitFacet = {
            isActive: 'true',
            searchFacetId: 'fit',
            searchFacetName: 'Fit',
            searchFacetOptionGroupList: {
              searchFacetOptionList: [
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Fitted',
                  searchFacetOptionName: 'Fitted',
                  searchFacetOptionValue: 'Fitted',
                },
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Loose',
                  searchFacetOptionName: 'Loose',
                  searchFacetOptionValue: 'Loose',
                },
              ],
            },
          };

          const currentFacets = createFacets(
            {
              fit: [{ count: 330, name: 'Fitted' }, { count: 220, name: 'Loose' }],
              style: [{ count: 330, name: 'Dresses' }],
              color_facet: [{ count: 330, name: 'blue' }],
              sizes: [
                {
                  count: 2,
                  name: 'one pieces & sets|size|regular|up to 7 lbs|',
                },
              ],
              department: [
                {
                  count: 21,
                  name: 'mens',
                },
              ],
              price: { max: 79.99, min: 0.01 },
            },
            {},
            configuration,
          );

          expect(currentFacets.searchFacetList).toEqual([...expectedCoreFacets, expectedFitFacet]);
        });

        it('should not sort the flex facets', () => {
          const expectedFirstFacetFit = {
            isActive: 'true',
            searchFacetId: 'fit',
            searchFacetName: 'Fit',
            searchFacetOptionGroupList: {
              searchFacetOptionList: [
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Fitted',
                  searchFacetOptionName: 'Fitted',
                  searchFacetOptionValue: 'Fitted',
                },
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Loose',
                  searchFacetOptionName: 'Loose',
                  searchFacetOptionValue: 'Loose',
                },
              ],
            },
          };

          const expectedSecondFacetCollection = {
            isActive: 'true',
            searchFacetId: 'collection',
            searchFacetName: 'Collection',
            searchFacetOptionGroupList: {
              searchFacetOptionList: [
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Long Sleeve',
                  searchFacetOptionName: 'Long Sleeve',
                  searchFacetOptionValue: 'Long Sleeve',
                },
                {
                  isActive: 'true',
                  isSelected: 'false',
                  searchFacetOptionId: 'Short Sleeve',
                  searchFacetOptionName: 'Short Sleeve',
                  searchFacetOptionValue: 'Short Sleeve',
                },
              ],
            },
          };

          const currentFacets = createFacets(
            {
              fit: [{ count: 330, name: 'Fitted' }, { count: 220, name: 'Loose' }],
              collection: [
                { count: 400, name: 'Long Sleeve' },
                { count: 220, name: 'Short Sleeve' },
              ],
              style: [{ count: 330, name: 'Dresses' }],
              color_facet: [{ count: 330, name: 'blue' }],
              sizes: [
                {
                  count: 2,
                  name: 'one pieces & sets|size|regular|up to 7 lbs|',
                },
              ],
              department: [
                {
                  count: 21,
                  name: 'mens',
                },
              ],
              price: { max: 79.99, min: 0.01 },
            },
            {},
            configuration,
          );
          expect(currentFacets.searchFacetList).toEqual([
            ...expectedCoreFacets,
            expectedFirstFacetFit,
            expectedSecondFacetCollection,
          ]);
        });
      });
      describe('when feature flag is disabled', () => {
        const configuration = {};

        it('should remove flex facets from build', () => {
          const currentFacets = createFacets(
            {
              Fit: [{ count: 330, name: 'Fitted' }, { count: 220, name: 'Loose' }],
              style: [{ count: 330, name: 'Dresses' }],
              color_facet: [{ count: 330, name: 'blue' }],
              sizes: [
                {
                  count: 2,
                  name: 'one pieces & sets|size|regular|up to 7 lbs|',
                },
              ],
              department: [
                {
                  count: 21,
                  name: 'mens',
                },
              ],
              price: { max: 79.99, min: 0.01 },
            },
            {},
            configuration,
          );

          expect(currentFacets.searchFacetList).toEqual(expectedCoreFacets);
        });
      });
    });
  });
});
