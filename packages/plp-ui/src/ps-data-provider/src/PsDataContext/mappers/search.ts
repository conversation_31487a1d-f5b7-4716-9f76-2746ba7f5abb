import { AdaptedProduct } from '@/types/ps-api-response-types';
import { ProductGridIdState, ProductCardState, ProductImageState, ProductInfoState, QuickAddState } from '../../types';

export const searchToPsDataProviderMapper = (products: AdaptedProduct[]) => {
  const productsGridIds: ProductGridIdState[] = [];
  const productCards: Record<string, ProductCardState> = {};
  const productImages: Record<string, ProductImageState> = {};
  const productInfos: Record<string, ProductInfoState> = {};
  const quickAdd: Record<string, QuickAddState> = {};

  products?.forEach(product => {
    if (product) {
      productsGridIds.push({ productId: product.productID });
      productCards[product.productID] = {
        outOfStock: product.outOfStock,
      };
      productImages[product.productID] = {
        index: 0,
        src: product.url,
      };
      productInfos[product.productID] = {
        pcid: product.productID,
        productMarketingFlag: {
          marketingFlag: product.marketingFlag?.marketingFlagName,
        },
        productName: {
          name: product.name,
          clickable: !product.outOfStock,
        },
        productPrice: {
          adapter: {
            currentMaxPrice: product.price?.currentMaxPrice.toString(),
            currentMinPrice: product.price?.currentMinPrice.toString(),
            maxPercentageOff: product.price?.maxPercentageOff?.toString(),
            minPercentageOff: product.price?.minPercentageOff?.toString(),
            priceType: product.price?.priceType?.toString(),
            regularMaxPrice: product.price?.regularMaxPrice.toString(),
            regularMinPrice: product.price?.regularMinPrice.toString(),
            localizedRegularMaxPrice: product.price?.localizedRegularMaxPrice,
          },
        },
      };
      quickAdd[product.productID] = {
        productName: product.name,
      };
    }
  });

  return {
    productCards,
    productImages,
    productInfos,
    productsGridIds,
    quickAdd,
  };
};
