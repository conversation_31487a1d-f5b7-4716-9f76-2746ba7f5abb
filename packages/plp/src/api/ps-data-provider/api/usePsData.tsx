import { useQuery } from '@tanstack/react-query';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { customAttribute } from '@ecom-next/core/components/reporting';
import { useConstructor, useFacetsOrder, useOutOfStock } from '@ecom-next/plp';
import { useRef } from 'react';
import { getAdaptedData } from '../../ps-api-mappers';
import { PsDataProviderContext } from '../reducer/types';
import { AppliedFacets, Direction, Field, SortByAdapterResult, SortByDir, SortByField } from '../../../types/plp-data';
import { PsData } from '../../../types/ps-api-response';
import { usePLPState } from '../../../data/plp-state-provider';
import { getCookieValue } from '../../../utils/window-helper';
import { AbSeg, Brands, Market, PLPFeaturesForApi } from '../../../types';
import { removeInvalidHashParams } from '../hooks/helpers/facets-query-builder';
import { IndexableFacet } from '../../../data/plp-data-layer/types';
import { CATEGORY_PAGE, PageType, SEARCH_PAGE } from '../types';
import { hasSizeParamForSearchPageInterim, mapSizeFacetForSearchPageInterim } from '../hooks/helpers/interimForSearchPage/interimSizeHashMapper';
import { HYBRID_PAGE_LIMIT, PAGE_LIMIT } from './constants';

type PsApiQueryParameters = {
  appliedFacets?: AppliedFacets;
  cid?: string;
  ignoreInventory?: boolean;
  pageNumber?: number;
  previewDate?: string;
  searchText?: string;
  sortByOptions?: SortByAdapterResult;
  storeId?: string | null;
};

const brandObjectAbbreviation: Record<string, string> = {
  at: 'pgAT',
  br: 'pgBR',
  brfs: 'pgBRFS',
  gap: 'pgGP',
  gapfs: 'pgGPFS',
  on: 'pgON',
};

const appliedFacetsToQueryParameters = (appliedFacets?: AppliedFacets, pageType?: string) => {
  if (!appliedFacets) {
    return '';
  }
  const searchParams = new URLSearchParams();

  const singleFacets = (facetName: string) => facetName === 'price' || facetName === 'department';

  Object.values(appliedFacets).forEach(options => {
    options?.forEach(({ facetName = '', value, id }) => {
      const facetValue = pageType === CATEGORY_PAGE ? id : value;
      if (searchParams.has(facetName) && !singleFacets(facetName)) {
        searchParams.set(facetName, `${searchParams.get(facetName)},${facetValue}`);
      } else {
        searchParams.set(facetName, facetValue as string);
      }
    });
  });
  return searchParams.toString();
};

const setDirection = (appliedDirection: SortByDir) => {
  const direction: SortByDir = appliedDirection === Direction.Unset ? '' : appliedDirection;
  const fixedDirection: string = direction ?? Direction.Ascending;
  return fixedDirection;
};

const setField = (appliedDirection: SortByDir, appliedField: SortByField) => {
  const field: SortByField = appliedDirection === '' ? '' : appliedField;
  const fixedField: string = field ?? Field.Price;
  return fixedField;
};

const sortByToQueryParameters = (sortByOptions?: SortByAdapterResult) => {
  if (sortByOptions && sortByOptions.sortByDir && sortByOptions.sortByField) {
    const searchParams = new URLSearchParams();
    const direction = setDirection(sortByOptions?.sortByDir);
    const field = setField(direction, sortByOptions?.sortByField);
    if (field) {
      field === 'new' ? searchParams.set('sortByField', 'newness') : searchParams.set('sortByField', field);
    }
    if (direction) {
      searchParams.set('sortByDir', direction);
    }
    return searchParams.toString();
  }
  return '';
};

const storeIdToQueryParameters = (storeId?: string | null) => {
  if (storeId) {
    const searchParams = new URLSearchParams();
    searchParams.set('storeId', storeId);
    return searchParams.toString();
  }
  return '';
};

const checkIfIsNotFactory = (ABSegIndex: string): boolean => ABSegIndex !== 'pgGPFS' && ABSegIndex !== 'pgBRFS';

const checkMarket = (market: string, ABSegIndex: string): string => {
  return market === 'ca' && checkIfIsNotFactory(ABSegIndex) ? `${ABSegIndex}CA` : ABSegIndex;
};

const getVendor = (brandName: Brands, market: Market, abSeg: AbSeg) => {
  const ABSegIndex = brandObjectAbbreviation[brandName];
  const formattedABSegIndex = checkMarket(market, ABSegIndex);
  const ABSegValue = abSeg?.[formattedABSegIndex];

  return ABSegValue === 'p' ? 'Certona' : '';
};

const parseResIdFromCookie = () => {
  const resIdCookie = getCookieValue('RES_TRACKINGID');
  return resIdCookie;
};

const getDepartmentIdFromUrl = (url = ''): string => {
  const urlHash = url.indexOf('#') !== -1 ? url.slice(url.indexOf('#')) : '';
  const departmentId = urlHash?.includes('department=') ? urlHash.split('department=')[1]?.split('&')[0] || '' : '';
  return departmentId;
};

const getCategoryVendorParams = () => {
  const categoryVendor = 'constructorio';
  const clientId = getCookieValue('ConstructorioID_client_id') || '0';
  const sessionId = getCookieValue('ConstructorioID_session_id') || '0';
  const camCookie = getCookieValue('cam');
  const externalCustomerId = camCookie?.split('|')?.[0];
  const clientUid = camCookie ? externalCustomerId : '';

  return { categoryVendor, clientId, sessionId, clientUid };
};

const getSearchVendorParams = () => {
  const searchVendor = 'constructorio';
  const clientId = getCookieValue('ConstructorioID_client_id') || '0';
  const sessionId = getCookieValue('ConstructorioID_session_id') || '0';
  const camCookie = getCookieValue('cam');
  const externalCustomerId = camCookie?.split('|')?.[0];
  const clientUid = camCookie ? externalCustomerId : '';

  return { searchVendor, clientId, sessionId, clientUid };
};

const fetchProducts = async (url: string, pageType: PageType, queryParameters: string, cid = '', searchText = '', plpFeaturesForApi: PLPFeaturesForApi) => {
  const mountedUrl = `${url}?${queryParameters}`;
  const options = {
    headers: {
      'x-client-application-name': 'Browse',
    },
  };
  const response = await fetch(mountedUrl, options);
  const data: PsData = await response.json();

  if (!data?.products?.length || data.products.length <= 0) {
    customAttribute('NO_PRODUCTS_FOUND_EVENT', 'No Products Found');
  }

  const apiResponseTimeMs = new Date().getTime();
  customAttribute('API_RESPONSE_TIME_EVENT', apiResponseTimeMs.toString());

  if (!response.ok) {
    throw new Error(response.statusText);
  }
  if ([CATEGORY_PAGE, SEARCH_PAGE].includes(pageType) && typeof window !== 'undefined') {
    const responseKey = pageType === CATEGORY_PAGE ? '__CATEGORY_PAGE_RESPONSE__' : '__SEARCH_PAGE_RESPONSE__';
    window[responseKey] = data;
  }
  return { ...getAdaptedData(data, pageType, plpFeaturesForApi), cid, searchText, queryParameters };
};

const getHashQueryString = () => {
  if (typeof window !== 'undefined') {
    let hashQueryString = window.location.hash;
    const newParameterCheck = hashQueryString.includes('sortByField=new');
    if (newParameterCheck) {
      hashQueryString = hashQueryString.replace('sortByField=new', 'sortByField=newness');
    }
    return hashQueryString;
  }
  return '';
};

const handlePreSelectedFacets = (indexableFacets: IndexableFacet[] = [], style: string = '', departmentId: string) => {
  if (typeof window !== 'undefined') {
    const indexableFacetsQuery: string[] = indexableFacets
      .filter(({ facetData }) => facetData?.id)
      .map(({ facetName, facetData }) => `${facetName}=${facetData?.id}`);
    const styleQuery = style ? [`style=${style}`] : [];
    const departmentQuery = departmentId ? [`department=${departmentId}`] : [];
    const preSelectedFacetsQueryString = [...indexableFacetsQuery, ...styleQuery, ...departmentQuery].join('&');
    if (preSelectedFacetsQueryString) {
      history.pushState(null, '', `#${preSelectedFacetsQueryString}`);
    }
  }
};

export const usePsData = (
  url: string,
  pageType: PageType,
  { cid, searchText, appliedFacets, sortByOptions, pageNumber = 0, storeId }: PsApiQueryParameters,
  isHybrid = false
) => {
  const searchParams = new URLSearchParams();
  const { previewDate } = useAppState();

  if (appliedFacets) {
    const { sortByDir, sortByField, storeId } = appliedFacets;

    if (sortByDir || sortByField || storeId) {
      delete appliedFacets.sortByDir;
      delete appliedFacets.sortByField;
      delete appliedFacets.storeId;
    }
  }
  const appliedFacetsQueryParameters = new URLSearchParams(appliedFacetsToQueryParameters(appliedFacets, pageType));
  const hashQueryString = getHashQueryString();
  const hashParametersToQueryParameters =
    appliedFacetsQueryParameters.size === 0 && typeof window !== 'undefined' && hashQueryString
      ? new URLSearchParams(removeInvalidHashParams(hashQueryString))
      : new URLSearchParams();

  if (hashParametersToQueryParameters.get('sortByDir') === 'unset') {
    hashParametersToQueryParameters.delete('sortByDir');
    hashParametersToQueryParameters.delete('sortByField');
  }
  const storeIdQueryParameters = !hashParametersToQueryParameters.get('storeId') ? new URLSearchParams(storeIdToQueryParameters(storeId)) : '';
  const sortByQueryParameters =
    !hashParametersToQueryParameters.get('sortByDir') && !hashParametersToQueryParameters.get('sortByField')
      ? new URLSearchParams(sortByToQueryParameters(sortByOptions))
      : '';

  const { locale, brand, market, abSeg, customUrl, params } = usePLPState();
  const departmentId = getDepartmentIdFromUrl(customUrl);
  const isStateAppliedOnInitialLoad = useRef(false);

  if (!isStateAppliedOnInitialLoad.current && typeof window !== 'undefined') {
    params && !window.location.hash && handlePreSelectedFacets(params?.indexableFacets, params?.style, departmentId);
    isStateAppliedOnInitialLoad.current = true;
  }

  const isCatConstructorEnabled = useConstructor();
  const isSoldOut = useOutOfStock();
  const facetsOrder = useFacetsOrder();

  const plpFeaturesForApi = { facetsOrder };

  const pageIdFromHash = hashParametersToQueryParameters.get('pageId');
  const pageNumberToUse = pageNumber || parseInt(pageIdFromHash || '0', 10);
  hashParametersToQueryParameters.delete('pageId');

  searchParams.set('pageSize', PAGE_LIMIT);
  searchParams.set('pageNumber', `${pageNumberToUse}`);

  if (pageType === CATEGORY_PAGE) {
    searchParams.set('ignoreInventory', `${isSoldOut}`);
    cid && searchParams.set('cid', cid);
    if (isCatConstructorEnabled) {
      const { categoryVendor, clientId, sessionId, clientUid } = getCategoryVendorParams();
      categoryVendor && searchParams.set('vendor', categoryVendor);
      clientId && searchParams.set('client_id', clientId);
      sessionId && searchParams.set('session_id', sessionId);
      clientUid && searchParams.set('uid', clientUid);
    } else {
      const vendor = getVendor(brand, market, abSeg);
      const trackingId = parseResIdFromCookie();
      vendor && searchParams.set('vendor', vendor);
      trackingId && searchParams.set('trackingid', trackingId);
    }
    searchParams.set('includeMarketingFlagsDetails', 'true');

    if (facetsOrder.isDynamicFacetsEnabled) {
      searchParams.set('enableDynamicFacets', 'true');
    }

    if (isHybrid) {
      searchParams.set('pageSize', HYBRID_PAGE_LIMIT);
    }
  }

  if (pageType === SEARCH_PAGE) {
    const { searchVendor, clientId, sessionId, clientUid } = getSearchVendorParams();
    searchText && searchParams.set('keyword', searchText);
    searchVendor && searchParams.set('searchVendor', searchVendor);
    clientId && searchParams.set('client_id', clientId);
    sessionId && searchParams.set('session_id', sessionId);
    clientUid && searchParams.set('uid', clientUid);
    // TODO: remove after migration to new size hash params on search page
    if (hasSizeParamForSearchPageInterim(hashParametersToQueryParameters, pageType)) {
      const sizeHash = hashParametersToQueryParameters.get('size');
      const mappedHashSize = mapSizeFacetForSearchPageInterim(sizeHash).join(',');
      hashParametersToQueryParameters.set('size', mappedHashSize);
    }
  }

  const previewDateFromUrl = typeof window !== 'undefined' && new URLSearchParams(window.location.search).get('previewDate');
  const resolvedPreviewDate = previewDate || previewDateFromUrl;

  if (resolvedPreviewDate) {
    appliedFacetsQueryParameters.set('previewDate', resolvedPreviewDate);
    appliedFacetsQueryParameters.set('mode', 'wip');
  }

  searchParams.set('brand', brand);
  searchParams.set('locale', locale);
  searchParams.set('market', market);

  const storeIdQuery = storeId ? `&${storeIdQueryParameters}` : '';
  const queryParameters = `${searchParams}&${appliedFacetsQueryParameters}&${hashParametersToQueryParameters}&${sortByQueryParameters}${storeIdQuery}`;
  const cleanedQueryParameters = queryParameters.replace(/&{2,}/g, '&');
  const formattedQueryParams = cleanedQueryParameters.endsWith('&') ? cleanedQueryParameters.slice(0, -1) : cleanedQueryParameters;
  const { data, error, isLoading, isFetching, isSuccess, isError } = useQuery<PsDataProviderContext>({
    queryKey: ['psData', url, formattedQueryParams],
    queryFn: () => fetchProducts(url, pageType, formattedQueryParams, cid, searchText, plpFeaturesForApi),
  });

  return {
    data,
    error,
    isLoading: isLoading || isFetching,
    isSuccess,
    isError,
  };
};
