import { ChangeEvent } from 'react';
import { Radio } from '@ecom-next/core/fabric/radio';
import { StarRatings } from '@ecom-next/core/fabric/star-ratings';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import isOptionApplied from '../../../collaborators/isOptionApplied';
import { useFacets } from '../../../../../../../../../api/ps-data-provider/hooks';
import { FacetBarData } from '../../../../../../../../../api/ps-data-provider/hooks/helpers/types';
import { SimpleFacet, Option } from '../../../types';
import { RATING_FACET_OPTION, RATING_FACET_ALL_REVIEWED, RATING_FACET_AND_UP } from './localization-tokens';

interface StarRatingChangeEvent {
  applied: boolean;
  facetName: string;
  id: string;
  name: string;
}

export function StarRatingSelector({ options, facetName }: SimpleFacet & { facetName: string }) {
  const { localize } = useLocalize();
  const facetBarData = useFacets();
  const { onFacetChangeHandler, appliedFacetsQuery } = facetBarData;
  const handlerFn = onFacetChangeHandler(facetBarData as FacetBarData);

  const handleRatingChange = (event: StarRatingChangeEvent) => {
    handlerFn(event);
  };

  const renderStarRating = (value: string) => (
    <div className='plp_grid-drawer-rating-facet'>
      {value !== '1' ? (
        <>
          <StarRatings
            ariaLabel={localize(RATING_FACET_OPTION, { value })}
            ratingValue={Number(value)}
            ratingSize='medium'
            showRatingValue={false}
            postText={localize(RATING_FACET_AND_UP)}
            className='plp_review-ratings--medium'
          />
        </>
      ) : (
        <span>{localize(RATING_FACET_ALL_REVIEWED)}</span>
      )}
    </div>
  );

  const getAppliedValue = (option: Option) => (appliedFacetsQuery ? isOptionApplied(option, appliedFacetsQuery, facetName) : false);

  return (
    <div className='plp_grid-drawer__radio-options'>
      {options?.map(option => (
        <div key={option.id} className='plp_grid_drawer_radio-option'>
          <Radio
            id={option.id}
            value={option.id}
            checked={getAppliedValue(option)}
            emphasisLabel={renderStarRating(option.value || '')}
            name={facetName}
            onChange={(e: ChangeEvent<HTMLInputElement>) => {
              handleRatingChange({
                facetName,
                id: option.id,
                name: option.name,
                applied: e.target.checked,
              });
            }}
          />
        </div>
      ))}
    </div>
  );
}
