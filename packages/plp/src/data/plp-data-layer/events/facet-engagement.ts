import { DataLayer } from '@ecom-next/sitewide/app-state-provider';
import { PageDataModel } from '@mfe/data-layer';
import { AppProps } from '../types';
import getPageAttributes from '../page-attributes/attributes';
import { FacetData, ModelToggle } from './types';
import { dynamicFacetNameEnabled } from './adapters/facet';
import { engagementsString } from './adapters/adapter';

export const facetEngagementDatalayerEvent = (
  datalayer: DataLayer,
  queryString: string,
  props: AppProps,
  facetData: FacetData,
  isDynamicStyleFacetNameEnabled: boolean,
  appliedQuickFacets: string[],
  modelToggleData: ModelToggle
): void => {
  try {
    datalayer.link({
      event_name: 'refinement',
      ...getPageAttributes(props),
      facet_engagement: engagementsString({
        queryString,
        facetData,
        appliedQuickFacets,
        modelToggleData,
      }),
      ...(dynamicFacetNameEnabled(facetData, isDynamicStyleFacetNameEnabled)
        ? {
            test_coe: dynamicFacetNameEnabled(facetData, isDynamicStyleFacetNameEnabled),
          }
        : {}),
    } as unknown as PageDataModel);
  } catch (err) {
    datalayer.link({
      ...(props.businessUnitId ? { business_unit_id: props.businessUnitId } : {}),
      error_message: `[Facet Engagement Data Layer]: error: ${err instanceof Error ? err.message : 'Unknown error'}`,
    });
  }
};
