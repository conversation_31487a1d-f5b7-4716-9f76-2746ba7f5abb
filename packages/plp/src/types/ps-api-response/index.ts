import { PsCategories } from './categories';
import { PsFacet } from './facet';
import { PsMetadata } from './metadata';
import { PsPagination } from './pagination';
import { PsProduct, PsStyleProduct } from './product';

export interface PsData {
  categories: Array<PsCategories>;
  facets: Array<PsFacet>;
  locale: string;
  metadata?: PsMetadata;
  pagination: PsPagination;
  personalizedSortRecommenderInfo?: PsPersonalizedSortRecommenderInfo;
  products: Array<PsProduct> | Array<PsStyleProduct>;
  sortByDir: string;
  sortByField: string;
  storeId?: string;
  totalColors: string;
  totalProducts?: string;
}

export interface PsPersonalizedSortRecommenderInfo {
  personalizedSortRecommenderRequest: string;
  sortRecommenderResponsePersonalized: string;
}

export * from './categories';
export * from './color';
export * from './facet';
export * from './image';
export * from './marketing-flag';
export * from './metadata';
export * from './pagination';
export * from './product';
