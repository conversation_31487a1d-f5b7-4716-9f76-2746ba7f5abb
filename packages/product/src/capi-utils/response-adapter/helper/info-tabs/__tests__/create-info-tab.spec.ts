import { dropshipCopyHeaders, regularCopyHeaders } from '../../../mocks/chas-copy-headers-mock';
import { createInfoTab } from '../create-info-tab';

describe('Given getInfoTabs', () => {
  const locale = 'en_US';
  const styleId = '443224';

  test('When receives a copyHeader with the id "overview", then should return an infoTab with all properties', () => {
    const header = { ...regularCopyHeaders[0] };
    const infoTab = createInfoTab(header, styleId, locale);

    expect(infoTab).toStrictEqual({
      bulletAttributes: ['These pants are made with quick dry fabric with moisture wicking comfort stretch.', 'Elasticized waist.', 'Front slant pockets.'],
      copyAttributes: ['Excellent day-to-day pants.'],
      infoTabName: 'Product Details',
      modelSizes: [
        [
          { height: `5'9"`, size: `4` },
          { height: `5'11"`, size: `10` },
        ],
      ],
      notes: [],
    });
  });

  test('When receives a copyHeader with the id "fabric", then should return an infoTab with only infoTabName and bulletAttributes', () => {
    const header = { ...regularCopyHeaders[2] };
    const infoTab = createInfoTab(header, styleId, locale);

    expect(infoTab).toStrictEqual({
      bulletAttributes: ['94% Nylon, 6% Spandex.'],
      infoTabName: 'Fabric + Care',
    });
  });

  test('When receives a copyHeader with the any other id, then should return an infoTab without modelSizes', () => {
    const header = { ...dropshipCopyHeaders[0] };
    const infoTab = createInfoTab(header, styleId, locale);

    expect(infoTab).toStrictEqual({
      bulletAttributes: [
        'Access Rail from Floor is 34"',
        'Access Rail from Top Mattress Support Position is 16.5"',
        'Access Rail from Bottom Mattress Support Position is 26.5"',
        'Assembled 54.25"W x 30.75"D x 34"H',
      ],
      copyAttributes: ['Perfect for your baby from day 1 on.'],
      infoTabName: 'Product Details',
      notes: [],
    });
  });
});
