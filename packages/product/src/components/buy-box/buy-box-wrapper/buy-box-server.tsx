import { PageParams } from '@ecom-next/sitewide/pages/PageWrapper';
import { Brands, getPageContext } from '@ecom-next/utils/server';
import { Breakpoint, XLARGE } from '@ecom-next/core/components/breakpoint-provider';
import bopisExclusion from '@pdp/src/app/components/buy-box/collaborators/bopis-exclusion';
import { CID } from '@ecom-next/marketing-ui';
import { setupFeatures } from '../../../providers/features-provider/use-features';
import { FeatureFlags } from '../../../hooks/use-feature-flag';
import { cacheableCapiDataPromise, cacheableEnabledFeaturesPromise, cacheableFeaturesConfig } from '../../../pages/getReWrittenData';
import type { AppWrapperApps } from '../../../pages/Product';
import { ProductInfo } from '../../product-info/ProductInfo';
import { ProductTitle } from '../../product-title/ProductTitle';
import { getUrlParamsString } from '../../../pages/helpers';
import Price from '../components/product-price/ProductPrice';
import { DimensionGroups } from '../components/dimension-group/DimensionGroups';
import { Fulfillment, getFulfillmentStatus } from '../components/fulfillment/Fulfillment';
import { ColorPicker } from '../components/color-picker/ColorPicker';
import { PaymentMethods } from '../components/payment-methods/PaymentMethods';
import { getAppConfigData, getPageContextData, getPageStateParamsData } from '../../../pages/getPageState';
import { getLayoutFromFeatures } from '../components/color-picker/collaborators/get-config';
import { AddToBagButton } from '../components/add-to-bag/AddToBagButton';
import { VariantGroup } from '../components/variant-group/VariantGroup';
import { DropshipShippingAndReturns } from '../components/dropship-shipping-and-returns';
import { ExcludedFromPromotions } from '../components/excluded-from-promotions';
import { UserFeedback } from '../components/user-feedback/UserFeedback';
import { StarRatingsWithDrawer } from '../components/star-ratings/StarRatingsWithDrawer';
import { HoverZoomImage } from '../../product-images/HoverZoomImage';
import { FeaturesConfig } from '../../../pages/getFeatureConfig';
import { MarketingFlagClient } from '../components/marketing-flag/MarketingFlagClient';
import { BuyBoxClient } from './BuyBoxClient';

export const BuyBoxServer = async ({
  searchParams,
  recommendationsConfig,
  productMarketing,
}: PageParams & Omit<AppWrapperApps, 'breadcrumbs'> & { recommendationsConfig: FeaturesConfig['recommendationsConfig'] }) => {
  const requestParamString = getUrlParamsString(searchParams);
  const capiData = await cacheableCapiDataPromise(requestParamString);
  const { brandName, market, locale, shippingAndReturnsData } = getPageContextData();

  const { enabledFeatures, featureVariables, abSeg } = await cacheableEnabledFeaturesPromise(searchParams);

  const {
    appConfig: { brandCodeUrls },
  } = await getAppConfigData(searchParams);

  const {
    params: { cid },
  } = getPageStateParamsData(searchParams);

  const layout = getLayoutFromFeatures({ enabledFeatures, featureVariables, brandName, market });

  const { isGiftCard, bopisExclusionStyleDate, isDropShip, isBopisDisplayable } = capiData.productData;

  const { useFeatureFlag, useFeatureVariables } = setupFeatures({ enabledFeatures, featureVariables, brandName, market, abSeg });

  const bopisFlag = useFeatureFlag(FeatureFlags.bopis);
  const { showBOPIS } = useFeatureVariables(FeatureFlags.dropship);
  const bopisExclusionStyle = bopisExclusion(bopisExclusionStyleDate);
  const isBopisUpdatesAtCcLevelFlag = useFeatureFlag(FeatureFlags.bopisExclusionUpdates);
  const { useFromCapi: displayPercentageOffFromCapi } = useFeatureVariables(FeatureFlags.percentageOffOnPricing);

  const shouldRenderFulfillment = getFulfillmentStatus({
    isDropShip,
    showBOPIS,
    bopisFlag,
    bopisExclusionStyle,
    isGiftCard,
    isBopisUpdatesAtCcLevelFlag,
    isBopisDisplayable,
  });
  const {
    dropshipShippingAndReturnsEnabled,
    hasAssemblyInstructions,
    lawLabelLookupUrl,
    marketingContainerEnabled,
    marketingContainerPlacement,
    pdpClothingLabelEnabled,
    showShippingAndReturns,
    scarcityMessageEnabled,
    isPercentageEnabled,
    reviews: { isReviewsDrawerEnabled },
  } = await cacheableFeaturesConfig(searchParams);
  const containerPositions = { higher: 'a', lower: 'b' };

  const { isDesktop } = getPageContext();

  const renderMobileMarketingContainer = (position: string) => {
    if (!marketingContainerEnabled || marketingContainerPlacement.mobile !== position) return null;

    return (
      <Breakpoint is='smallerThan' size={XLARGE}>
        {productMarketing}
      </Breakpoint>
    );
  };

  return (
    <BuyBoxClient isDesktop={isDesktop} brandName={brandName}>
      <HoverZoomImage />

      <div className='flex flex-col gap-4'>
        <div className='hidden sm:block'>
          <ProductTitle enabledFeatures={enabledFeatures} />
          <MarketingFlagClient isStyleLevelFlag isSuperPdpPh2={enabledFeatures['pdp-super-ph2']} />
        </div>
        {!isGiftCard && (
          <div className='pdp-margin-top-s-components'>
            <div className='flex justify-between'>
              <div className='pdp-title-price-wrapper' data-testid='pdp-title-price-wrapper'>
                <Price isPercentageEnabled={isPercentageEnabled} priceStyles='title-price' />
                <ExcludedFromPromotions productData={capiData.productData} />
              </div>
              <StarRatingsWithDrawer
                isReviewsDrawerEnabled={isReviewsDrawerEnabled}
                ratingSize='medium'
                brandName={brandName}
                requestParamString={requestParamString}
                searchParams={searchParams}
              />
            </div>
            <MarketingFlagClient isStyleLevelFlag={false} />
          </div>
        )}
      </div>
      <div className='pdp-margin-bottom-components pdp-margin-top-components'>
        <ColorPicker brandName={brandName as Brands} parentComponent='buybox' layout={layout} isPercentageEnabled={isPercentageEnabled} isDesktop={isDesktop} />
      </div>
      <div className='pdp-buy-box-size-selector-container pdp-margin-bottom-components flex flex-col'>
        <VariantGroup />
        <DimensionGroups parentComponent='buybox' enabledFeatures={enabledFeatures} featureVariables={featureVariables} brandName={brandName} market={market} />
      </div>

      {dropshipShippingAndReturnsEnabled && <DropshipShippingAndReturns productData={capiData.productData} />}

      <UserFeedback brandName={brandName as Brands} />

      <div className='pdp-margin-bottom-components pdp-margin-top-components w-full'>
        <AddToBagButton
          brandName={brandName as Brands}
          brandCodeUrls={brandCodeUrls}
          market={market}
          locale={locale}
          abSeg={abSeg}
          cid={cid as CID}
          isPercentageEnabled={isPercentageEnabled}
          scarcityMessageEnabled={scarcityMessageEnabled}
          displayPercentageOffFromCapi={displayPercentageOffFromCapi}
          isGiftCard={isGiftCard}
          recommendationsConfig={recommendationsConfig}
        />
        <PaymentMethods searchParams={searchParams} />
      </div>

      {shouldRenderFulfillment && (
        <div className='pdp-margin-bottom-components'>
          <Fulfillment searchParams={searchParams} isDesktop={isDesktop} />
        </div>
      )}

      {renderMobileMarketingContainer(containerPositions.higher)}

      <ProductInfo
        brandName={brandName}
        pdpClothingLabelEnabled={pdpClothingLabelEnabled}
        hasAssemblyInstructions={hasAssemblyInstructions}
        showShippingAndReturns={showShippingAndReturns}
        isGiftCard={isGiftCard}
        lawLabelLookupUrl={lawLabelLookupUrl}
        shippingAndReturnsData={shippingAndReturnsData}
      />

      {renderMobileMarketingContainer(containerPositions.lower)}
    </BuyBoxClient>
  );
};
