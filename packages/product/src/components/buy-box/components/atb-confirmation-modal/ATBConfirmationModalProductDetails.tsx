import { decodeAmpersands } from './helpers/utility';
import { DisplayAddToBagItemProps } from './types';

type ATBConfirmationModalProductDetailsProps = {
  addedQuantity: number;
  color: string;
  isGiftCard?: boolean;
  localize: DisplayAddToBagItemProps['localize'];
  pickupTodayInfo: string;
  shouldDisplayPickup?: boolean;
  size: string;
};

export const ATBConfirmationModalProductDetails = ({
  addedQuantity,
  color,
  isGiftCard = false,
  localize,
  pickupTodayInfo,
  shouldDisplayPickup = true,
  size,
}: ATBConfirmationModalProductDetailsProps) => {
  const labelPickup = localize('pdp.atbConfirmation.label.storePickup');
  const labelSize = localize('pdp.atbConfirmation.label.size');
  const labelColor = localize('pdp.atbConfirmation.label.color');
  const labelQuantity = localize('pdp.atbConfirmation.label.qty');
  return (
    <>
      <li className='atb-confirmation-modal__product-description-wrapper'>
        <span className='atb-confirmation-modal__product-description-label'>{labelColor}</span>
        <span
          className='atb-confirmation-modal__product-description'
          dangerouslySetInnerHTML={{
            __html: decodeAmpersands(color),
          }}
        />
      </li>
      {!isGiftCard && (
        <li className='atb-confirmation-modal__product-description-wrapper'>
          <span className='atb-confirmation-modal__product-description-label'>{labelSize}</span>
          <span
            className='atb-confirmation-modal__product-description'
            dangerouslySetInnerHTML={{
              __html: decodeAmpersands(size || ''),
            }}
          />
        </li>
      )}
      <li className='atb-confirmation-modal__product-description-wrapper'>
        <span className='atb-confirmation-modal__product-description-label'>{labelQuantity}</span>
        <span className='atb-confirmation-modal__product-description quantity'>{addedQuantity}</span>
      </li>
      {shouldDisplayPickup && (
        <li className='atb-confirmation-modal__pickup-info' data-testid='pickup-info'>
          <span className='atb-confirmation-modal__product-pickup-label'>{labelPickup}</span>
          <span className='atb-confirmation-modal__product-pickup-description'>{pickupTodayInfo}</span>
        </li>
      )}
    </>
  );
};
