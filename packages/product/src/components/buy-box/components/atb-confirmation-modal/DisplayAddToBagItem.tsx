'use Client';

import { Fragment } from 'react';

import { useShallow } from 'zustand/react/shallow';
import { useBuyBoxStore } from '../../../../providers/buybox-provider';
import { DisplayAddToBagItemProps } from './types';
import { AddToBagProductPrice } from './AddToBagProductPrice';
import { getPickupInfo, getReturnsMessage } from './helpers/utility';
import { ATBConfirmationModalProductDetails } from './ATBConfirmationModalProductDetails';
import { ATBBackOrderMessage, ATBConfirmationModalReturnMessage } from './ATBConfirmationModalMessages';
import { ATBConfirmationModalProductTitle } from './ATBConfirmationModalProductTitle';

export const DisplayAddToBagItem = ({
  localize,
  atbResponse,
  active = false,
  enabled,
  selectedStore,
  isPercentageEnabled,
  displayPercentageOffFromCapi,
  isGiftCard,
}: DisplayAddToBagItemProps): JSX.Element => {
  const { items = [], productData = [], backOrderMessageWithDate = '', percentageOffFromCAPI = '' } = atbResponse || { items: [], productData: [] };
  const [
    {
      color = '',
      eligibleReturnLocationCode = '',
      inventoryStatus = 'IN_STOCK',
      priceTypeId = 1,
      productURL = '',
      productImage: { summaryImagePath = '' },
      productStyleDescription = '',
      size = '',
    } = {
      productImage: {},
    },
  ] = productData;

  const isSalePrice = priceTypeId !== 1;
  const shouldShowReturnsMessage = eligibleReturnLocationCode === 'N' || eligibleReturnLocationCode === 'M';
  const isOnBackOrder = inventoryStatus === 'ON_ORDER';

  const [
    { productPrice: { localizedRegularPrice = '', localizedSalePrice = '' }, addedQuantity = 1 } = {
      productPrice: {},
    },
  ] = items;
  const { isBopisDisplayable } = useBuyBoxStore(
    useShallow(state => ({
      isBopisDisplayable: state.isBopisDisplayable,
    }))
  );

  const backorderHTML = backOrderMessageWithDate || '';
  const shouldDisplayPickup = enabled && active && isBopisDisplayable;
  const pickupTodayInfo = getPickupInfo(selectedStore);
  const altAddedToBag = localize('pdp.atbConfirmation.title.altAddedToBag');

  const percentageOffText = displayPercentageOffFromCapi && percentageOffFromCAPI ? localize('price.percentage_off', { value: percentageOffFromCAPI }) : '';
  const returnTypeMessage = getReturnsMessage(eligibleReturnLocationCode, localize);

  return (
    <Fragment>
      <div className='atb-confirmation-modal-image-product-details-wrapper' data-testid='product-card'>
        <a className='atb-confirmation-modal__product-link' href={productURL} tabIndex={0}>
          <img alt={altAddedToBag} className='atb-confirmation-modal__image' src={summaryImagePath} />
        </a>

        <div className='atb-confirmation-modal-product-details-wrapper'>
          <ATBConfirmationModalProductTitle productURL={productURL} productStyleDescription={productStyleDescription} />

          <ul id={`productDetails`} className='atb-confirmation-modal__product-details-wrapper'>
            <ATBConfirmationModalProductDetails
              color={color}
              isGiftCard={isGiftCard}
              size={size}
              localize={localize}
              addedQuantity={addedQuantity}
              shouldDisplayPickup={shouldDisplayPickup}
              pickupTodayInfo={pickupTodayInfo}
            />
            {
              <AddToBagProductPrice
                isSalePrice={isSalePrice}
                localizedRegularPrice={localizedRegularPrice}
                isPercentageEnabled={isPercentageEnabled}
                percentageOffText={percentageOffText}
                localizedSalePrice={localizedSalePrice}
              />
            }

            {shouldShowReturnsMessage && <ATBConfirmationModalReturnMessage returnTypeMessage={returnTypeMessage} />}
          </ul>
        </div>
      </div>

      {isOnBackOrder && <ATBBackOrderMessage backorderHTML={backorderHTML} />}
    </Fragment>
  );
};
