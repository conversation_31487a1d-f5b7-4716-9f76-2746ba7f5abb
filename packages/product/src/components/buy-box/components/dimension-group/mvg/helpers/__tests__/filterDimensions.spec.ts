import { AdaptedVariantCustomerChoice, DimensionsList } from '../../../../../../../pages/services/capi-aggregation-service';
import { filterDimensions } from '../filterDimensions';

const dimensions1 = {
  dimensionGroupId: 'sizeDimension1',
  dimensions: [
    {
      bopis_in_stock: true,
      isInstock: true,
      dimension: '26W',
      availableSkus: [],
      sort_order: '1',
    },
    {
      bopis_in_stock: true,
      isInstock: true,
      dimension: '28W',
      availableSkus: [],
      sort_order: '1',
    },
    {
      bopis_in_stock: true,
      isInstock: true,
      dimension: '30W',
      availableSkus: [],
      sort_order: '1',
    },
  ],
  label: 'waist',
  selectedDimension: '',
} as DimensionsList;

const dimensions2 = {
  dimensionGroupId: 'sizeDimension2',
  dimensions: [
    {
      bopis_in_stock: true,
      isInstock: true,
      dimension: '26L',
      availableSkus: [],
      sort_order: '1',
    },
    {
      bopis_in_stock: true,
      isInstock: true,
      dimension: '28L',
      availableSkus: [],
      sort_order: '1',
    },
    {
      bopis_in_stock: true,
      isInstock: true,
      dimension: '30L',
      availableSkus: [],
      sort_order: '1',
    },
    {
      bopis_in_stock: true,
      isInstock: true,
      dimension: '32L',
      availableSkus: [],
      sort_order: '1',
    },
  ],
  label: 'Length',
  selectedDimension: '',
} as DimensionsList;

const color = {
  sizes: [
    {
      sizeDimension1: '26W',
      sizeDimension2: '26L',
    },
    {
      sizeDimension1: '26W',
      sizeDimension2: '28L',
    },
    {
      sizeDimension1: '26W',
      sizeDimension2: '30L',
    },
    {
      sizeDimension1: '28W',
      sizeDimension2: '26L',
    },
    {
      sizeDimension1: '28W',
      sizeDimension2: '28L',
    },
    {
      sizeDimension1: '28W',
      sizeDimension2: '30L',
    },
    {
      sizeDimension1: '28W',
      sizeDimension2: '32L',
    },
    {
      sizeDimension1: '30W',
      sizeDimension2: '28L',
    },
    {
      sizeDimension1: '30W',
      sizeDimension2: '30L',
    },
    {
      sizeDimension1: '30W',
      sizeDimension2: '32L',
    },
  ],
} as unknown as AdaptedVariantCustomerChoice;

describe('filterDimensions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  test('dimension should not be filtered if length of dimensions less than 1', () => {
    const result = filterDimensions([dimensions1], color);
    expect(result).toStrictEqual([dimensions1]);
  });

  test('dimension should not be filtered if there is no selection', () => {
    const result = filterDimensions([dimensions1, dimensions2], color);
    expect(result).toStrictEqual([dimensions1, dimensions2]);
  });

  test('dimension2 should be filtered when dimension1 is selected', () => {
    const selectedDimensions1 = { ...dimensions1, selectedDimension: '26W' };
    const filteredDimensions = [
      {
        bopis_in_stock: true,
        isInstock: true,
        dimension: '26L',
        availableSkus: [],
        sort_order: '1',
      },
      {
        bopis_in_stock: true,
        isInstock: true,
        dimension: '28L',
        availableSkus: [],
        sort_order: '1',
      },
      {
        bopis_in_stock: true,
        isInstock: true,
        dimension: '30L',
        availableSkus: [],
        sort_order: '1',
      },
      {
        bopis_in_stock: true,
        isInstock: true,
        dimension: '32L',
        availableSkus: [],
        sort_order: '1',
      },
    ];
    const expectedDimensions = [selectedDimensions1, { ...dimensions2, dimensions: filteredDimensions }];
    const result = filterDimensions([selectedDimensions1, dimensions2], color);
    expect(result).toStrictEqual(expectedDimensions);
  });

  test('dimensions should be filtered when dimensions selected', () => {
    const selectedDimensions1 = { ...dimensions1, selectedDimension: '26W' };
    const selectedDimensions2 = { ...dimensions2, selectedDimension: '26L' };
    const filteredDimensions1 = [
      {
        bopis_in_stock: true,
        isInstock: true,
        dimension: '26W',
        availableSkus: [],
        sort_order: '1',
      },
      {
        bopis_in_stock: true,
        isInstock: true,
        dimension: '28W',
        availableSkus: [],
        sort_order: '1',
      },
      {
        availableSkus: [],
        bopis_in_stock: true,
        dimension: '30W',
        isInstock: true,
        sort_order: '1',
      },
    ];
    const filteredDimensions2 = [
      {
        bopis_in_stock: true,
        isInstock: true,
        dimension: '26L',
        availableSkus: [],
        sort_order: '1',
      },
      {
        bopis_in_stock: true,
        isInstock: true,
        dimension: '28L',
        availableSkus: [],
        sort_order: '1',
      },
      {
        bopis_in_stock: true,
        isInstock: true,
        dimension: '30L',
        availableSkus: [],
        sort_order: '1',
      },
      {
        availableSkus: [],
        bopis_in_stock: true,
        dimension: '32L',
        isInstock: true,
        sort_order: '1',
      },
    ];

    const expectedDimensions = [
      { ...selectedDimensions1, dimensions: filteredDimensions1 },
      { ...selectedDimensions2, dimensions: filteredDimensions2 },
    ];
    const result = filterDimensions([selectedDimensions1, selectedDimensions2], color);
    expect(result).toStrictEqual(expectedDimensions);
  });
});
