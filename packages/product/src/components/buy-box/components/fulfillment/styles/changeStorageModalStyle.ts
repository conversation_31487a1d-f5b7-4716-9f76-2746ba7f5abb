import { tv } from 'tailwind-variants';
import { useFeatureFlag } from '../../../../../hooks/use-feature-flag/use-feature-flag';

export const changeStoreModalStyles = (hasChildren?: boolean) => {
  const wrapperJustify = hasChildren ? 'justify-between' : 'justify-evenly';
  const wrapperLocationWidth = hasChildren ? 'max-w-[400px]' : 'max-w-full';
  const wrapperDetailsWidth = hasChildren ? 'max-w-[335px]' : 'max-w-full';

  // eslint-disable-next-line
  const isPercentageOffOn = useFeatureFlag('pdp-percentage-off');
  const onContentWrapperStyles = isPercentageOffOn
    ? `on:[&_.panel-details_.swatches]:py-0 on:[&_.panel-details_.swatches_.swatch-group]:pt-0 on:[&_.panel-details_.swatches_.swatch-group]:pl-0 `
    : '';

  // Brand specific styles
  const gapDoneButtonStyles = `gap:bg-b1 gap:text-wh gap:font-[1.0625rem] gap:font-alt gapfs:bg-b1 gapfs:text-wh gapfs:font-[1.0625rem] gapfs:font-alt`;
  const atDoneButtonStyles = `at:bg-b1 at:text-wh at:font-brand at:capitalize`;
  const onDoneButtonStyles = `on:bg-b1 on:text-wh on:font-alt on:h-11 on:text-[1.1875rem]`;
  const brDoneButtonStyles = `br:bg-b1 br:text-wh br:font-brand br:capitalize brfs:bg-b1 brfs:text-wh brfs:font-brand brfs:capitalize`;

  const brStoreListStyles = `br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0`;
  const atStoreListStyles = `at:pt-0`;
  const gapStoreListStyles = `gap:pt-0 gapfs:pt-0`;
  const onStoreListStyles = `on:pt-0`;

  const brInStockStyles = `br:[&_p]:font-brand br:[&_p]:text-base brfs:[&_p]:font-brand brfs:[&_p]:text-base`;
  const atInStockStyles = `at:[&_p]:font-brand at:[&_p]:text-base`;
  const onInStockStyles = `on:[&_p]:font-brand on:[&_p]:text-base`;
  const gapInStockStyles = `gap:[&_p]:font-brand gap:[&_p]:text-base gapfs:[&_p]:font-brand gapfs:[&_p]:text-base`;

  const atMessageForPickupStyles = `at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem]`;
  const onMessageForPickupStyles = `on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]`;
  const brMessageForPickupStyles = `br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem]`;
  const gapMessageForPickupStyles = `gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem]`;

  // Default styles with brands applied
  const contentWrapperStyles = `md:[&_.panel-details_.swatches]:w-full md:[&_.panel-details_.swatches]:left-0 ${wrapperJustify} md:[&_.panel-details]:${wrapperDetailsWidth} md:[&_.panel-location]:${wrapperLocationWidth} lg:[&_.panel-location]:${wrapperLocationWidth} lg:[&_.panel-location]:pt-0 ${onContentWrapperStyles} md:gap-6`;
  const errorCopyStyles = `font-w-full text-left mt-4 mb-2 text-bk`;
  const visuallyHideStyles = `font-alt relative h-1 w-1 overflow-hidden invisible`;
  const doneButtonWrapperStyles = `bg-wh shadow-none fixed p-4 pb-8 bottom-0 left-0 [@supports(position:sticky)]:sticky [@supports(position:sticky)]:w-full [@supports(position:sticky)]:p-4 [@supports(position:sticky)]:px-0`;
  const doneButtonStyles = `w-full h-10 text-center text-base font-medium uppercase [&:hover]:opacity-60 ${gapDoneButtonStyles} ${atDoneButtonStyles} ${onDoneButtonStyles} ${brDoneButtonStyles}`;
  const inStockStyles = `flex align-center justify-between mt-5 ${brInStockStyles} ${onInStockStyles} ${gapInStockStyles} ${atInStockStyles}`;
  const storeListStyles = `pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 ${brStoreListStyles} ${atStoreListStyles} ${gapStoreListStyles} ${onStoreListStyles}`;
  const storeCardStyles = `relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:font-brand [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-xs [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:leading-6 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand`;
  const selectionStyles = `flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand`;
  const radioButtonStyles = `border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px]`;
  const messageForPickupStyles = `flex font-bold mt-1 ${atMessageForPickupStyles} ${brMessageForPickupStyles} ${gapMessageForPickupStyles} ${onMessageForPickupStyles}`;

  return tv({
    /**
     * Returns all styles as Tailwind selectors
     * @return {key: string} keys to implement on components className
     */
    slots: {
      contentWrapperStyles,
      errorCopyStyles,
      visuallyHideStyles,
      doneButtonWrapperStyles,
      doneButtonStyles,
      messageForPickupStyles,
      inStockStyles,
      storeListStyles,
      storeCardStyles,
      selectionStyles,
      radioButtonStyles,
    },
  });
};
