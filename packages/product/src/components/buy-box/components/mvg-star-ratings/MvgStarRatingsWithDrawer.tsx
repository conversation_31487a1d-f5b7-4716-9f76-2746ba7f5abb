import pino from 'pino';
import { SearchParams } from '../../../../pages/getPageState';
import { cacheableReviewRatingsV2Promise } from '../../../../pages/getReWrittenData';
import { MVGReviews } from '../../../../components/reviews/MVGReviews';
import { MvgStarRatingsWithDrawerClient } from './MvgStarRatingsWithDrawerClient';

type MvgStarRatingProps = {
  brandName: string;
  isReviewsDrawerEnabled: boolean;
  pdpLogger: pino.BaseLogger;
  ratingSize?: 'small' | 'medium' | 'large';
  requestParamString: string;
  searchParams: SearchParams;
};

export const MvgStarRatingsWithDrawer = async ({
  requestParamString,
  brandName,
  ratingSize = 'medium',
  searchParams,
  isReviewsDrawerEnabled,
  pdpLogger,
}: MvgStarRatingProps) => {
  const { reviewRatings } = await cacheableReviewRatingsV2Promise(requestParamString, pdpLogger);

  return (
    <MvgStarRatingsWithDrawerClient isReviewsDrawerEnabled={isReviewsDrawerEnabled} brandName={brandName} reviewRatings={reviewRatings} ratingSize={ratingSize}>
      <MVGReviews searchParams={searchParams} pdpLogger={pdpLogger} />;
    </MvgStarRatingsWithDrawerClient>
  );
};
