'use client';

import { useShallow } from 'zustand/shallow';
import { useMemo } from 'react';
import { MVGReviewsDrawerProvider } from '../../../reviews/ReviewsDrawer/reviews-drawer-provider/MVGReviewsDrawerProvider';
import { MVGReviewsDrawer } from '../../../reviews/ReviewsDrawer/MVGReviewDrawer';
import { ReviewHistogram } from '../../../../pages/services/reviews';
import { useMVGBuyBoxStore } from '../../../../providers/mvg-buybox-provider';
import { MvgStarRatingsWrapper } from './MvgStartRatingsWrapper';

type MvgStarRatingProps = {
  brandName: string;
  children: React.ReactNode;
  isReviewsDrawerEnabled: boolean;
  ratingSize?: 'small' | 'medium' | 'large';
  reviewRatings: ReviewHistogram;
};

export const MvgStarRatingsWithDrawerClient = ({ brandName, ratingSize = 'medium', reviewRatings, isReviewsDrawerEnabled, children }: MvgStarRatingProps) => {
  const { selectedStyle } = useMVGBuyBoxStore(
    useShallow(state => ({
      selectedStyle: state.selectedStyle,
    }))
  );

  const mvgReviewRatings = useMemo(
    () => ({
      average_rating: selectedStyle?.rating?.average_rating ?? reviewRatings.average_rating ?? 0,
      rating_count: selectedStyle?.rating?.rating_count ?? reviewRatings.rating_count ?? 0,
      review_count: selectedStyle?.rating?.review_count ?? reviewRatings.review_count ?? 0,
    }),
    [selectedStyle]
  );

  return (
    <MVGReviewsDrawerProvider>
      <MvgStarRatingsWrapper isReviewsDrawerEnabled={isReviewsDrawerEnabled} brandName={brandName} reviewRatings={mvgReviewRatings} ratingSize={ratingSize} />
      <MVGReviewsDrawer>{children || null}</MVGReviewsDrawer>
    </MVGReviewsDrawerProvider>
  );
};
