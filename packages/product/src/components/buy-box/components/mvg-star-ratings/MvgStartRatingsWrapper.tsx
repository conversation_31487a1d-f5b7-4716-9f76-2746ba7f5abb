'use client';
import { StarRatings as StarRatingsComponent } from '@ecom-next/core/fabric/star-ratings';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useCallback, useContext } from 'react';
import { ReviewHistogram } from '../../../../pages/services/reviews';
import { ReviewsDrawerContext } from '../../../reviews/ReviewsDrawer/reviews-drawer-provider';
import { MvgStarRatingProps } from './MvgStarRatings';

const getRatingCount = (reviewRatings: ReviewHistogram): number => {
  return reviewRatings?.rating_count || 0;
};

const getReviewCountLabel = (reviewCount: number, singularLabel: string, pluralLabel: string, hideRatingLabel = false): string => {
  let labelText = '';

  if (hideRatingLabel) {
    return `${reviewCount}`;
  }

  if (reviewCount === 1) {
    labelText = singularLabel;
  }

  if (reviewCount > 1) {
    labelText = pluralLabel;
  }

  return labelText;
};

const scrollToReviewWidget = (): void => {
  const lazyLoadReviewContainer = document.querySelector('.pdp-render-reviews') as HTMLElement;
  const reviewsHeader = document.querySelector('#pdp-reviews-widget-header') as HTMLElement;
  const reviewsWidget = document.querySelector('.pdp-reviews-widget') as HTMLElement;

  let scrollTarget = reviewsHeader;

  if (lazyLoadReviewContainer) {
    scrollTarget = lazyLoadReviewContainer;
  }

  reviewsWidget?.focus({ preventScroll: true });
  scrollTarget.scrollIntoView();
};

const openWriteAReviewForm = (): void => {
  const writeAReviewTrigger = document.querySelector('.pr-snippet-write-review-link') as HTMLElement;

  if (writeAReviewTrigger && writeAReviewTrigger.click) {
    writeAReviewTrigger.click();
  }
};

export const MvgStarRatingsWrapper = ({
  reviewRatings,
  ratingSize,
  isReviewsDrawerEnabled = false,
}: Omit<MvgStarRatingProps, 'requestParamString' | 'mock' | 'pdpLogger'> & { isReviewsDrawerEnabled?: boolean; reviewRatings: ReviewHistogram }) => {
  const starRating = (reviewRatings && reviewRatings?.average_rating) || 0;
  const { localize } = useLocalize();

  const ratingCount = getRatingCount(reviewRatings);
  const singularLabelRating = localize('pdp.ratings.title.textSingular', { ratingCount });
  const pluralLabelRatings = localize('pdp.ratings.title.textPlural', { ratingCount });
  const writeFirstReviewLabel = localize('pdp.writeReview.text');
  const ratingCountLabel = ratingCount === 0 ? undefined : getReviewCountLabel(ratingCount, singularLabelRating, pluralLabelRatings, true);
  const reviewRatingsAriaLabel = localize(ratingCount === 0 ? 'fui.ReviewRatings.ariaLabelNoRatings' : 'fui.ReviewRatings.new-ariaLabel', {
    starRating,
    ratingCount,
  });

  const scrollToReview = ratingCount && !isReviewsDrawerEnabled;
  const openReviewsDrawer = ratingCount && isReviewsDrawerEnabled;
  const { setReviewsDrawerIsOpen, setForceReviewForm } = useContext(ReviewsDrawerContext);

  const openReviewDrawer = () => {
    const hasReviews = reviewRatings?.review_count > 0;
    setReviewsDrawerIsOpen(true);
    if (!hasReviews) {
      setForceReviewForm(true);
    }
  };

  const handleOnClick = () => {
    if (scrollToReview) {
      scrollToReviewWidget();
    } else if (openReviewsDrawer) {
      openReviewDrawer();
    } else {
      openWriteAReviewForm();
    }
  };

  const handleWriteReviewClick = useCallback(
    () => (isReviewsDrawerEnabled ? openReviewDrawer() : openWriteAReviewForm()),
    [isReviewsDrawerEnabled, reviewRatings]
  );

  return (
    <div className='pdp-rating-snippet-wrapper grid'>
      <button onClick={handleOnClick} tabIndex={0}>
        <StarRatingsComponent
          ariaLabel={reviewRatingsAriaLabel}
          className='pdp-write-review custom-review-ratings flex justify-end'
          ratingValue={starRating}
          postText={ratingCountLabel}
          ratingSize={ratingSize}
          showRatingValue={false}
          showUnderlineForRatingValue
        />
      </button>

      {!ratingCountLabel && (
        <button
          className='pdp-write-review underline'
          data-testid='pdp-write-review'
          aria-label={reviewRatingsAriaLabel}
          onClick={handleWriteReviewClick}
          tabIndex={0}
        >
          {writeFirstReviewLabel}
        </button>
      )}
    </div>
  );
};
