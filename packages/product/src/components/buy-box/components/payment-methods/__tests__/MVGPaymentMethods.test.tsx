import { screen } from '@testing-library/react';
import pino from 'pino';
import { <PERSON><PERSON> } from '@ecom-next/utils/server';
import * as dataFetchers from '../../../../../pages/getReWrittenData';
import { MVGPaymentMethods } from '../MVGPaymentMethods';
import { CapiV3AggregationServiceRaw } from '../../../../../pages/services/capi-aggregation-service';
import { adaptedDataMock } from '../../../../../pages/services/capi-aggregation-service/v3/__tests__/adaptedDataMock';
import { wrapMvgInTestApp } from '../../../../../test-utils/appWrapper';
import { atMock } from '../../../../../pages/services/capi-aggregation-service/v3/__fixtures__/atMock';

jest.mock('../../../../../pages/getReWrittenData', () => ({
  cacheableCapiDataV3Promise: jest.fn(),
  cacheableFeaturesConfigMVG: jest.fn(),
}));

jest.mock('../../../../../pages/getPageState', () => ({
  getPageContextData: () => ({
    locale: 'en-US',
    displayName: 'Test',
  }),
}));

jest.mock('../MVGPaymentMethodsClient', () => {
  return function MVGPaymentMethodsClient() {
    return <div>MVGPaymentMethodsClient</div>;
  };
});

describe('MVGPaymentMethods', () => {
  const mockLogger = pino();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render MVGPaymentMethodsClient when payment methods are enabled', async () => {
    dataFetchers.cacheableFeaturesConfigMVG.mockResolvedValue({
      paymentMethods: {
        afterPay: {
          apThreshold: 100,
          apCategories: 'ALL',
          apHide: false,
          afterpayFeatureFlag: true,
        },
        payPal: {
          paypalFeatureFlag: true,
          ppPaypal: true,
          ppThreshold: 50,
        },
      },
    });

    dataFetchers.cacheableCapiDataV3Promise.mockResolvedValue({
      selectedStyle: {
        gift_card: false,
        primary_category_id: '123',
      },
    });

    wrapMvgInTestApp(
      await MVGPaymentMethods({
        searchParams: 'test',
        mock: '1',
        pdpLogger: mockLogger,
      }),
      {
        productData: {
          ...adaptedDataMock,
          selectedStyle: {
            gift_card: false,
            primary_category_id: '123',
          },
          brand: Brands.Gap,
          styles: {
            '599749': { ...atMock.styles['599749'], description: undefined },
            '985218': { ...atMock.styles['985218'], description: undefined },
          },
        } as unknown as CapiV3AggregationServiceRaw,
      }
    );

    expect(screen.getByText('MVGPaymentMethodsClient')).toBeInTheDocument();
  });

  it('should return empty fragment when payment methods are disabled', async () => {
    dataFetchers.cacheableFeaturesConfigMVG.mockResolvedValue({
      paymentMethods: {
        afterPay: { afterpayFeatureFlag: false },
        payPal: { paypalFeatureFlag: false },
      },
    });

    dataFetchers.cacheableCapiDataV3Promise.mockResolvedValue({
      selectedStyle: {
        gift_card: false,
        isDropShip: false,
      },
    });

    wrapMvgInTestApp(
      await MVGPaymentMethods({
        searchParams: 'test',
        mock: '1',
        pdpLogger: mockLogger,
      }),
      {
        productData: {
          ...adaptedDataMock,
          selectedStyle: {
            gift_card: false,
            primary_category_id: '123',
          },

          brand: Brands.Gap,
          styles: {
            '599749': { ...atMock.styles['599749'], description: undefined },
            '985218': { ...atMock.styles['985218'], description: undefined },
          },
        } as unknown as CapiV3AggregationServiceRaw,
      }
    );

    expect(screen.queryByText('MVGPaymentMethodsClient')).not.toBeInTheDocument();
  });

  it('Snapshot', async () => {
    dataFetchers.cacheableFeaturesConfigMVG.mockResolvedValue({
      paymentMethods: {
        afterPay: {
          apThreshold: 100,
          apCategories: 'ALL',
          apHide: false,
          afterpayFeatureFlag: true,
        },
        payPal: {
          paypalFeatureFlag: true,
          ppPaypal: true,
          ppThreshold: 50,
        },
      },
    });

    dataFetchers.cacheableCapiDataV3Promise.mockResolvedValue({
      selectedStyle: {
        gift_card: false,
        primary_category_id: '123',
      },
    });

    const { container } = wrapMvgInTestApp(
      await MVGPaymentMethods({
        searchParams: 'test',
        mock: '1',
        pdpLogger: mockLogger,
      }),
      {
        productData: {
          ...adaptedDataMock,
          selectedStyle: {
            gift_card: false,
            primary_category_id: '123',
          },
          brand: Brands.Gap,
          styles: {
            '599749': { ...atMock.styles['599749'], description: undefined },
            '985218': { ...atMock.styles['985218'], description: undefined },
          },
        } as unknown as CapiV3AggregationServiceRaw,
      }
    );
    expect(container).toMatchSnapshot();
  });
});
