/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { fireEvent, render, screen, act } from '@ecom-next/core/test/test-helper';
import { MvgBuyBoxClient } from '../mvg-buy-box-client';
import { FeatureConfigMVGType } from '../../../../pages/getReWrittenData';
import { useMVGBuyBoxStore } from '../../../../providers/mvg-buybox-provider';

jest.mock('../../../../providers/mvg-buybox-provider');
jest.mock('../../../../providers/pdp-reporter-provider/v3/mvg-reporter-provider');

describe('<MvgBuyBoxClient />', () => {
  let mockGetBoundingClientRect: jest.Mock;
  let originalScrollY: any;
  let originalInnerHeight: any;
  let currentScrollY = 0;
  const windowHeight = 800;
  const elementInitialTop = 100;
  const elementHeight = 400;

  beforeAll(() => {
    originalScrollY = Object.getOwnPropertyDescriptor(window, 'scrollY');
    originalInnerHeight = Object.getOwnPropertyDescriptor(window, 'innerHeight');

    Object.defineProperty(window, 'scrollY', {
      configurable: true,
      get: () => currentScrollY,
    });
    Object.defineProperty(window, 'innerHeight', {
      configurable: true,
      get: () => windowHeight,
    });
  });

  afterAll(() => {
    if (originalScrollY) Object.defineProperty(window, 'scrollY', originalScrollY);
    if (originalInnerHeight) Object.defineProperty(window, 'innerHeight', originalInnerHeight);
  });

  beforeEach(() => {
    jest.clearAllMocks();
    currentScrollY = 0; // Reset scroll for each test

    mockGetBoundingClientRect = jest.fn().mockImplementation(() => {
      const buyBoxElement = screen.queryByTestId('buy-box-wrapper-id');
      let currentStyledTop = elementInitialTop;
      if (buyBoxElement && buyBoxElement.style.top) {
        currentStyledTop = parseFloat(buyBoxElement.style.top);
      } else {
        currentStyledTop = elementInitialTop;
      }
      return {
        top: currentStyledTop,
        left: 0,
        bottom: currentStyledTop + elementHeight,
        right: 100,
        height: elementHeight,
        width: 50,
        x: 0,
        y: currentStyledTop,
        toJSON: () => ({}),
      };
    });
    Element.prototype.getBoundingClientRect = mockGetBoundingClientRect;

    (useMVGBuyBoxStore as jest.Mock).mockReturnValue({
      selectedStyle: { description: 'Test Product', rating: {} },
      selectedCustomerChoice: 'Test Color',
    });
  });

  describe('Smart scrolling event listener', () => {
    const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
    const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');
    const baseProps = {
      featureConfigs: {} as FeatureConfigMVGType,
      children: <div></div>,
    };

    describe('Desktop', () => {
      it('should add listeners and set initial style top correctly when isDesktop is true (br brand)', () => {
        currentScrollY = 50;
        render(<MvgBuyBoxClient {...baseProps} brandName='br' isDesktop={true} />);
        const buyBoxWrapper = screen.getByTestId('buy-box-wrapper-id');
        // Calculation for br brand (viewportTopBoundary: 160, footerOffset: 66)
        // elementBounds.top = 100 (mocked initial)
        // desiredViewportTop = 100 (delta is 0)
        // elementOverflow = 400 - (800 - 66) = -334
        // newStyleTop = max(100, 334) = 334
        // newStyleTop = min(334, 160) = 160
        expect(buyBoxWrapper).toHaveStyle({ top: '160px' });
        expect(addEventListenerSpy).toHaveBeenCalledWith('scroll', expect.any(Function));
        expect(addEventListenerSpy).toHaveBeenCalledWith('pageshow', expect.any(Function));
      });

      it('should set initial style top correctly (gap brand)', () => {
        currentScrollY = 20;
        render(<MvgBuyBoxClient {...baseProps} brandName='gap' isDesktop={true} />);
        const buyBoxWrapper = screen.getByTestId('buy-box-wrapper-id');
        // Calculation for gap brand (viewportTopBoundary: 16, footerOffset: 16)
        // elementBounds.top = 100
        // desiredViewportTop = 100
        // elementOverflow = 400 - (800 - 16) = -384
        // newStyleTop = max(100, 384) = 384
        // newStyleTop = min(384, 16) = 16
        expect(buyBoxWrapper).toHaveStyle({ top: '16px' });
      });

      it('should update style top on scroll (br brand)', () => {
        render(<MvgBuyBoxClient {...baseProps} brandName='br' isDesktop={true} />);
        const buyBoxWrapper = screen.getByTestId('buy-box-wrapper-id');
        // Initial: top: 160px, prevScroll = 0

        mockGetBoundingClientRect.mockReturnValue({
          top: 160,
          height: elementHeight,
          bottom: 160 + elementHeight,
          left: 0,
          right: 0,
          x: 0,
          y: 160,
          toJSON: () => ({}),
        });
        act(() => {
          currentScrollY = 100;
          fireEvent.scroll(window);
        });
        expect(buyBoxWrapper).toHaveStyle({ top: '160px' }); // Stays pinned

        // Scroll with larger element height
        mockGetBoundingClientRect.mockReturnValue({
          top: 160,
          height: 1000,
          bottom: 160 + 1000,
          left: 0,
          right: 0,
          x: 0,
          y: 160,
          toJSON: () => ({}),
        });
        act(() => {
          currentScrollY = 300; // prevScroll was 100
          fireEvent.scroll(window);
        });
        // prevScroll = 100, currentScroll = 300. Delta = 200.
        // elementBounds.top = 160, height = 1000
        // desiredViewportTop = 160 - 200 = -40
        // elementOverflow = 1000 - (800 - 66) = 266. -elementOverflow = -266
        // newStyleTop = max(-40, -266) = -40
        // newStyleTop = min(-40, 160) = -40
        expect(buyBoxWrapper).toHaveStyle({ top: '-40px' });
      });

      it('should recalculate position on page refresh or navigation event', () => {
        currentScrollY = 100;
        render(<MvgBuyBoxClient {...baseProps} brandName='br' isDesktop={true} />);
        const buyBoxWrapper = screen.getByTestId('buy-box-wrapper-id');
        // Initial top: 160px

        currentScrollY = 200;
        mockGetBoundingClientRect.mockReturnValue({
          top: 160,
          height: elementHeight,
          bottom: 160 + elementHeight,
          left: 0,
          right: 0,
          x: 0,
          y: 160,
          toJSON: () => ({}),
        });
        act(() => {
          const event = new PageTransitionEvent('pageshow', { persisted: true });
          fireEvent(window, event);
        });
        expect(buyBoxWrapper).toHaveStyle({ top: '160px' }); // Recalculated to pin
      });
    });

    describe('Mobile', () => {
      it('should not addEventListener for scrolling or pageshow when isDesktop is false', () => {
        render(<MvgBuyBoxClient {...baseProps} brandName='br' isDesktop={false} />);
        fireEvent.scroll(window, { target: { scrollY: 200 } });
        expect(addEventListenerSpy).not.toHaveBeenCalledWith('scroll', expect.any(Function));
        expect(addEventListenerSpy).not.toHaveBeenCalledWith('pageshow', expect.any(Function));
      });
    });

    it('should remove scroll and pageshow event listeners on unmount', () => {
      const { unmount } = render(<MvgBuyBoxClient {...baseProps} brandName='br' isDesktop={true} />);
      const scrollListener = addEventListenerSpy.mock.calls.find((call: string[]) => call[0] === 'scroll')?.[1];
      const pageshowListener = addEventListenerSpy.mock.calls.find((call: string[]) => call[0] === 'pageshow')?.[1];

      unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledWith('scroll', scrollListener);
      expect(removeEventListenerSpy).toHaveBeenCalledWith('pageshow', pageshowListener);
    });
  });
});
