import pino from 'pino';
import { SearchParams } from '@product-page/pages/getPageState';
import { getPageContext } from '@ecom-next/utils/server';
import { Breakpoint, XLARGE } from '@ecom-next/core/components/breakpoint-provider';
import { ReactNode } from 'react';
import { MVGProductInfo } from '../../product-info/mvg/MVGProductInfo';
// import { Brands } from '@ecom-next/utils/server';
// import bopisExclusion from '@pdp/src/app/components/buy-box/collaborators/bopis-exclusion';
// import { CID } from '@ecom-next/marketing-ui';
// import { setupFeatures } from '../../../providers/features-provider/use-features';
// import { FeatureFlags } from '../../../hooks/use-feature-flag';
// import { Segments } from '../../../providers/abseg-provider/utils';
import {
  cacheableCapiDataV3Promise,
  // cacheableCapiDataPromise,
  // cacheableEnabledFeaturesPromise,
  // cacheableFeaturesConfig,
  cacheableFeaturesConfigMVG,
  // getUrlParamsString,
  // cacheableMarketingFlagData,
} from '../../../pages/getReWrittenData';
// import { ProductInfo } from '../../product-info/ProductInfo';
import { MVGColorPicker } from '../components/color-picker/mvg/MVGColorPicker';
import { MvgProductTitleClient } from '../../product-title';
import { MVGMarketingFlagClient } from '../components/marketing-flag/MVGMarketingFlagClient';
// import { Fulfillment, getFulfillmentStatus } from '../components/fulfillment/mvg/FulFillment';
import { MvgFulfillment } from '../components/fulfillment/mvg/MvgFulfillment';
// import { getUrlParamsString } from '../../../pages/helpers';
// import Price from '../components/product-price/ProductPrice';
// import { DimensionGroups } from '../components/dimension-group/DimensionGroups';
// import { ColorPicker } from '../components/color-picker/ColorPicker';
// import { PageParams } from '@ecom-next/sitewide/pages/PageWrapper';
import { MVGPaymentMethods } from '../components/payment-methods/MVGPaymentMethods';
import { MVGAddToBagButton } from '../components/add-to-bag/mvg/MVGAddToBagButton';
import { MvgProductPrice } from '../components/product-price/mvg/MvgProductPrice';
import { MvgStarRatingsWithDrawer } from '../components/mvg-star-ratings/MvgStarRatingsWithDrawer';
import { MvgVariantGroupClient } from '../components/variant-group/mvg/MvgVariantGroupClient';
import { MvgUserFeedback } from '../components/user-feedback/mvg/MvgUserFeedback';
import { MvgExcludedFromPromotionsClient } from '../components/excluded-from-promotions/mvg/MvgExcludedFromPromotionsClient';
import { MvgDropshipShippingAndReturns } from '../components/dropship-shipping-and-returns/MvgDropshipShippingAndReturn';
import { MVGDimensionGroupsClient } from '../components/dimension-group/mvg/MVGDimensionGroupsClient';
import { MvgStickyContainerClient } from '../components/mvg-sticky-container/MvgStickyContainerClient';
import { MvgStickyContainerDetails } from '../components/mvg-sticky-container/MvgStickContainerDetails';
import { MvgBuyBoxClient } from './mvg-buy-box-client';

// import { getLayoutFromFeatures } from '../components/color-picker/collaborators/get-config';
// import { VariantGroup } from '../components/variant-group/VariantGroup';
// import { DropshipShippingAndReturns } from '../components/dropship-shipping-and-returns';
// import { ExcludedFromPromotions } from '../components/excluded-from-promotions';
// import { UserFeedback } from '../components/user-feedback/UserFeedback';
// import { StarRatingsWithDrawer } from '../components/star-ratings/StarRatingsWithDrawer';

export type MvgBuyBoxServerProps = {
  brand: string;
  pdpLogger: pino.BaseLogger;
  productMarketing: ReactNode;
  requestParamString: string;
  searchParams: SearchParams;
};

export const MVGBuyBoxServer = async ({ requestParamString, pdpLogger, searchParams, brand, productMarketing }: MvgBuyBoxServerProps) => {
  // const capiData = await cacheableCapiDataPromise(requestParamString);
  // const { enabledFeatures, featureVariables, abSeg } = await cacheableEnabledFeaturesPromise(searchParams);
  // const {
  //   appConfig: { brandCodeUrls },
  // } = await getAppConfigData(searchParams);
  // const {
  //   params: { cid },
  // } = getPageStateParamsData(searchParams);
  // const layout = getLayoutFromFeatures({ enabledFeatures, featureVariables, brandName, market });
  // const { isGiftCard, bopisExclusionStyleDate, isDropShip, isBopisDisplayable } = capiData.productData;
  // const { useFeatureFlag, useAbseg, useFeatureVariables } = setupFeatures({ enabledFeatures, featureVariables, brandName, market, abSeg });
  // const percentageOffSegment = useAbseg(Segments.percentageOffOnPricing);
  // const isPercentageEnabled = useFeatureFlag(FeatureFlags.percentageOffOnPricing) && percentageOffSegment === 'a';
  // const bopisFlag = useFeatureFlag(FeatureFlags.bopis);
  // const bopisExclusionStyle = bopisExclusion(bopisExclusionStyleDate);
  // const { useFromCapi: displayPercentageOffFromCapi } = useFeatureVariables(FeatureFlags.percentageOffOnPricing.key);
  // const shouldRenderFulfillment = getFulfillmentStatus({
  //   isDropShip,
  //   showBOPIS,
  //   bopisFlag,
  //   bopisExclusionStyle,
  //   isGiftCard,
  //   isBopisUpdatesAtCcLevelFlag,
  //   isBopisDisplayable,
  // });
  const [featureConfigs, capiData] = await Promise.all([
    cacheableFeaturesConfigMVG(requestParamString, pdpLogger),
    cacheableCapiDataV3Promise(requestParamString, pdpLogger),
  ]);
  // const { marketingFlag = '' } = await cacheableMarketingFlagData(requestParamString);
  const containerPositions = { higher: 'a', lower: 'b' };

  const {
    isDropShipEnabled,
    isBopisDisplayable,
    dropshipShippingAndReturnsEnabled,
    reviews: { isReviewsDrawerEnabled },
    marketingContainerEnabled,
    marketingContainerPlacement,
  } = featureConfigs;

  const { isDesktop } = getPageContext();

  if (!capiData) {
    return <></>;
  }

  const sharedProps = { requestParamString, pdpLogger };

  const renderMobileMarketingContainer = (position: string) => {
    if (!marketingContainerEnabled || marketingContainerPlacement.mobile !== position) {
      return null;
    }

    return (
      <Breakpoint is='smallerThan' size={XLARGE}>
        {productMarketing}
      </Breakpoint>
    );
  };

  return (
    <MvgBuyBoxClient featureConfigs={featureConfigs} isDesktop={isDesktop} brandName={brand}>
      {!isDesktop && (
        <MvgStickyContainerClient>
          <MvgProductTitleClient />
          <div className='flex items-start'>
            <MvgStickyContainerDetails>
              <div className='pdp-title-price-wrapper'>
                <MvgProductPrice {...sharedProps} priceStyles='title-price' />
              </div>
            </MvgStickyContainerDetails>
            <MvgStarRatingsWithDrawer isReviewsDrawerEnabled={isReviewsDrawerEnabled} brandName={brand} searchParams={searchParams} {...sharedProps} />
          </div>
        </MvgStickyContainerClient>
      )}
      <div className='buy-box_wrapper w-100 flex flex-col items-start p-4' data-testid='buy-box-wrapper'>
        <div id='buy-box-wrapper-id' className='buy-box-wrapper sticky top-0 w-full'>
          <div className='pdp-margin-bottom-components hidden sm:block'>
            <MvgProductTitleClient />
            <MVGMarketingFlagClient isStyleLevelFlag />
          </div>
          {!capiData.selectedStyle?.gift_card && (
            <div className='pdp-margin-top-s-components'>
              <div className='flex w-full justify-between'>
                <div className='pdp-sticky-container-breakpoint' id='pdp-sticky-container-breakpoint'></div>
                <div className='pdp-title-price-wrapper' data-testid='pdp-title-price-wrapper'>
                  <MvgProductPrice {...sharedProps} priceStyles='title-price' />
                  <MvgExcludedFromPromotionsClient isDropShipEnabled={isDropShipEnabled} />
                </div>
                <MvgStarRatingsWithDrawer isReviewsDrawerEnabled={isReviewsDrawerEnabled} brandName={brand} searchParams={searchParams} {...sharedProps} />
              </div>
              <MVGMarketingFlagClient isStyleLevelFlag={false} />
            </div>
          )}
          <div className='pdp-margin-bottom-components pdp-margin-top-components' id='pdp-color-picker'>
            <MVGColorPicker parentComponent='buybox' {...sharedProps} />
          </div>
          <div className='pdp-buy-box-size-selector-container flex flex-col'>
            <MvgVariantGroupClient>
              <MVGDimensionGroupsClient parentComponent='buybox' />
            </MvgVariantGroupClient>
          </div>
          {dropshipShippingAndReturnsEnabled && <MvgDropshipShippingAndReturns />}
          <MvgUserFeedback brandName={capiData.brand} />
          <div className='pdp-margin-bottom-components pdp-margin-top-components w-full'>
            <MVGAddToBagButton {...sharedProps} isBopisDisplayable={isBopisDisplayable} />
            <MVGPaymentMethods {...sharedProps} />
          </div>
          {isBopisDisplayable && (
            <div className='pdp-margin-bottom-components'>
              <MvgFulfillment searchParams={searchParams} pdpLogger={pdpLogger} />
            </div>
          )}

          {renderMobileMarketingContainer(containerPositions.higher)}

          <MVGProductInfo {...sharedProps} />

          {renderMobileMarketingContainer(containerPositions.lower)}
        </div>
      </div>
    </MvgBuyBoxClient>
  );
};
