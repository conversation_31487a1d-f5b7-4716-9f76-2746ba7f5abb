/* eslint-disable jest/no-disabled-tests */
import { useStoreFetch } from '@ecom-next/core/migration/bopis';
import { fireEvent, render, waitFor } from '@testing-library/react';
import debounce from 'lodash/debounce';
import React, { useRef } from 'react';

import { Brand } from '@ecom-next/utils/server';
import userEvent from '@testing-library/user-event';
import ChangeStoreModal from '../';
import { inventorySkuApi, oosInventorySkuApi, stores } from '../data/mock';
import type { ChangeStoreModalRefType } from '../';
import { useSkuAvailabilityQuery } from '../../../hooks/use-availability';
import { ApiConfigType } from '../types';
import { ChangeStoreModalTestContext } from '../spec/fixture';

jest.mock('@ecom-next/core/migration/bopis');
jest.mock('../../../hooks/use-availability');
jest.mock('lodash/debounce');

const changeStoreModalProps = {
  LASConfig: {
    apiKey: 'some_api_key',
    url: 'some_url',
  } as ApiConfigType,
  children: <div>TEST</div>,
  onClose: jest.fn(),
  onDone: jest.fn(),
  onOpen: jest.fn(),
  zipCode: '94123',
  clientId: 'some_client_id',
  container: 'some_container',
  selectedStoreId: 'some_store_id',
  sku: 'some_sku',
  useStorage: false,
};

const getStorePromise = (zipCode: string) => {
  return Promise.resolve({
    fetchStatus: 'complete',
    postalCode: zipCode,
    stores: zipCode === '94123' ? stores : stores.slice(1),
  });
};

const mockGetStores = jest.fn(zipCode => getStorePromise(zipCode));
const mockSkuAvailabilityQuery = jest.fn(() => Promise.resolve(inventorySkuApi));

const mockDebounce = () => {
  (debounce as jest.Mock).mockImplementation(fn => fn);
};

const setup = () => {
  (useStoreFetch as jest.Mock).mockImplementation(() => ({ getStores: mockGetStores }));
  (useSkuAvailabilityQuery as jest.Mock).mockImplementation(() => mockSkuAvailabilityQuery);
};

const renderChangeStoreModal = ({ props = {}, withRef = false }, brandName?: Brand) => {
  const newProps = {
    ...changeStoreModalProps,
    ...props,
  };
  const ChangeStoreModalWithDummyButton = () => {
    const changeStoreModalRef = useRef<ChangeStoreModalRefType>(null);
    return (
      <>
        <button onClick={() => changeStoreModalRef.current?.openModal?.()} type='button'>
          Open with ref
        </button>
        <ChangeStoreModal ref={changeStoreModalRef} {...newProps} />
      </>
    );
  };
  return render(
    <ChangeStoreModalTestContext brandName={brandName}>
      {withRef ? <ChangeStoreModalWithDummyButton /> : <ChangeStoreModal {...newProps} />}
    </ChangeStoreModalTestContext>
  );
};

describe.skip('<ChangeStoreModal />', () => {
  beforeEach(() => {
    setup();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should open the modal correctly with open button', async () => {
    const { getByText, getByRole, getByTestId } = renderChangeStoreModal({});
    const changeStoreButton = getByRole('button', { name: 'Change Store' });
    fireEvent.click(changeStoreButton);
    await waitFor(() => {
      expect(getByText('4 stores found')).toBeInTheDocument();
    });
    const modalCloseButton = getByTestId('close-icon');
    expect(modalCloseButton).toBeInTheDocument();
  });

  test('should close the modal correctly with close button', async () => {
    const { getByText, getByTestId } = renderChangeStoreModal({});
    const changeStoreButton = getByText('Change Store');
    fireEvent.click(changeStoreButton);
    await waitFor(() => {
      expect(getByText('4 stores found')).toBeInTheDocument();
    });
    const modalCloseButton = getByTestId('close-icon');
    fireEvent.click(modalCloseButton);
    await waitFor(() => {
      expect(modalCloseButton).not.toBeInTheDocument();
    });
  });

  test('should call onOpen callback correctly when opening the modal', async () => {
    const { getByText } = renderChangeStoreModal({});
    const changeStoreButton = getByText('Change Store');
    fireEvent.click(changeStoreButton);
    expect(changeStoreModalProps.onOpen.mock.calls).toHaveLength(1);
    await waitFor(() => {
      expect(getByText('4 stores found')).toBeInTheDocument();
    });
  });

  test('should call onClose callback correctly when closing the modal', async () => {
    const { getByText, getByTestId } = renderChangeStoreModal({});
    const changeStoreButton = getByText('Change Store');
    fireEvent.click(changeStoreButton);
    await waitFor(() => {
      expect(getByText('4 stores found')).toBeInTheDocument();
    });
    const modalCloseButton = getByTestId('close-icon');
    fireEvent.click(modalCloseButton);
    expect(changeStoreModalProps.onClose.mock.calls).toHaveLength(1);
  });

  test('should render zipCode prop on zipCode input', async () => {
    const { container, getByText } = renderChangeStoreModal({});
    const changeStoreButton = getByText('Change Store');
    fireEvent.click(changeStoreButton);
    await waitFor(() => {
      expect(getByText('4 stores found')).toBeInTheDocument();
    });
    const zipCodeInput = container.querySelector('input');
    expect(zipCodeInput).toHaveValue('94123');
  });

  test('should call getStores when modal opens the first time', async () => {
    const { getByText } = renderChangeStoreModal({});
    const changeStoreButton = getByText('Change Store');
    fireEvent.click(changeStoreButton);
    await waitFor(() => {
      expect(getByText('4 stores found')).toBeInTheDocument();
    });
    expect(mockGetStores.mock.calls).toHaveLength(1);
  });

  test('should call PAS when modal opens', async () => {
    const { getByText } = renderChangeStoreModal({});
    const changeStoreButton = getByText('Change Store');
    fireEvent.click(changeStoreButton);
    await waitFor(() => {
      expect(mockSkuAvailabilityQuery.mock.calls).toHaveLength(1);
    });
  });

  test('should display "4 stores found" in store list', async () => {
    const { getByText } = renderChangeStoreModal({});
    const changeStoreButton = getByText('Change Store');
    fireEvent.click(changeStoreButton);
    await waitFor(() => {
      expect(getByText('4 stores found')).toBeInTheDocument();
    });
  });

  test('should call getStores when zipCode changes', async () => {
    mockDebounce();
    const { getByText, getByLabelText } = renderChangeStoreModal({});
    const changeStoreButton = getByText('Change Store');
    fireEvent.click(changeStoreButton);
    await waitFor(() => {
      expect(mockGetStores.mock.calls).toHaveLength(1);
    });
    const inputZipCode = getByLabelText('Zip Code');
    await waitFor(() => {
      fireEvent.change(inputZipCode, { target: { value: '92132' } });
    });
    expect(mockGetStores.mock.calls).toHaveLength(2);
  });

  test('should call PAS when store list changes', async () => {
    mockDebounce();
    const { getByText, getByLabelText } = renderChangeStoreModal({});
    const changeStoreButton = getByText('Change Store');
    fireEvent.click(changeStoreButton);
    let inputZipCode: HTMLElement;
    await waitFor(() => {
      inputZipCode = getByLabelText('Zip Code');
    });
    fireEvent.change(inputZipCode, { target: { value: '92132' } });
    await waitFor(() => {
      expect(mockGetStores.mock.calls).toHaveLength(2);
      expect(mockSkuAvailabilityQuery.mock.calls).toHaveLength(2);
    });
  });

  test('should not select any store in list if no selectedStoreId property is provided', async () => {
    const { getByText, container } = renderChangeStoreModal({});
    await waitFor(() => {
      const changeStoreButton = getByText('Change Store');
      fireEvent.click(changeStoreButton);
    });
    const selectedButtons = container.querySelectorAll('.selected');
    expect(selectedButtons).toHaveLength(0);
  });

  test('should select the specific store when selectedStoreId is provided', async () => {
    const { getByText, getByLabelText, container } = renderChangeStoreModal({});
    const changeStoreButton = getByText('Change Store');
    fireEvent.click(changeStoreButton);
    await waitFor(() => {
      const buttons = container.querySelectorAll('.pdp-button--radio');
      fireEvent.click(buttons[1]);

      const selectedStoreNode = getByLabelText(stores[1].storeName);
      expect(selectedStoreNode).toHaveClass('selected');
    });
  });

  describe('useStorage is true', () => {
    test('should call getStores with correct props', async () => {
      const { getByText } = renderChangeStoreModal({
        props: {
          useStorage: true,
        },
      });
      await waitFor(() => {
        const changeStoreButton = getByText('Change Store');
        fireEvent.click(changeStoreButton);
      });
      expect(mockGetStores.mock.calls).toHaveLength(1);
      expect(mockGetStores.mock.calls[0][0]).toEqual('94123');
      expect(mockGetStores.mock.calls[0][1]).toEqual(false);
    });
  });

  describe('useStorage is false', () => {
    test('should call getStores with correct props', async () => {
      const { getByText } = renderChangeStoreModal({});
      await waitFor(() => {
        const changeStoreButton = getByText('Change Store');
        fireEvent.click(changeStoreButton);
      });
      expect(mockGetStores.mock.calls).toHaveLength(1);
      expect(mockGetStores.mock.calls[0][0]).toEqual('94123');
      expect(mockGetStores.mock.calls[0][1]).toEqual(true);
    });
  });

  describe('inStock filter', () => {
    test('disable in-stock filter should show all stores', async () => {
      const { getByText, getByLabelText } = renderChangeStoreModal({});
      const changeStoreButton = getByText('Change Store');
      fireEvent.click(changeStoreButton);
      await waitFor(() => {
        const filterButton = getByLabelText('Show In-Stock Stores Only OFF');
        expect(filterButton).not.toBeChecked();
        expect(getByText('4 stores found')).toBeInTheDocument();
      });
    });

    test('enable in-stock filter should be showing just stores within the stock', async () => {
      const { getByText, getByLabelText } = renderChangeStoreModal({});
      await waitFor(() => {
        const changeStoreButton = getByText('Change Store');
        fireEvent.click(changeStoreButton);
      });
      expect(getByText('4 stores found')).toBeInTheDocument();
      await waitFor(() => {
        const filterButton = getByLabelText(/Show In-Stock Stores Only/);
        fireEvent.click(filterButton);
      });
      expect(getByLabelText('Show In-Stock Stores Only ON')).toBeChecked();
      expect(getByText('2 stores found')).toBeInTheDocument();
    });

    test('enable in-stock when no have stores in stock should return an error message', async () => {
      const mockUnavailable = jest.fn(() => Promise.resolve(oosInventorySkuApi));
      (useSkuAvailabilityQuery as jest.Mock).mockImplementation(() => mockUnavailable);
      const { getByText, getByLabelText } = renderChangeStoreModal({});
      await waitFor(() => {
        const changeStoreButton = getByText('Change Store');
        fireEvent.click(changeStoreButton);
      });
      expect(getByText('4 stores found')).toBeInTheDocument();
      await waitFor(() => {
        const filterButton = getByLabelText(/Show In-Stock Stores Only/);
        fireEvent.click(filterButton);
      });
      expect(getByLabelText('Show In-Stock Stores Only ON')).toBeChecked();
      expect(getByText("We're sorry but we can't find this item in stock nearby. Try another size, color, or zip code.")).toBeInTheDocument();
    });
  });

  describe('Change Store Modal Errors', () => {
    test('should display en error message when no stores to display', async () => {
      const mockNoStores = () =>
        Promise.resolve({
          fetchStatus: 'complete',
          postalCode: '94123',
          stores: [],
        });
      (useStoreFetch as jest.Mock).mockImplementation(() => ({ getStores: mockNoStores }));
      const { getByText } = renderChangeStoreModal({});
      await waitFor(() => {
        const changeStoreButton = getByText('Change Store');
        fireEvent.click(changeStoreButton);
      });

      await waitFor(() => {
        const errorText = "We're sorry but we can't find this item in stock nearby. Try another size, color, or zip code.";

        expect(getByText(errorText)).toBeInTheDocument();
      });
    });

    test('should display an zipCode not valid when las return the message', async () => {
      const mockLasError = () =>
        Promise.resolve({
          errorMessage: 'zipCode could not be found',
          fetchStatus: 'error',
          stores: [],
        });
      (useStoreFetch as jest.Mock).mockImplementation(() => ({ getStores: mockLasError }));
      const { getByText } = renderChangeStoreModal({});
      const changeStoreButton = getByText('Change Store');
      fireEvent.click(changeStoreButton);
      await waitFor(() => {
        expect(getByText("Zip code can't be found. Try again.")).toBeInTheDocument();
      });
    });

    test('should display change store unavailable when zipcode returns error message', async () => {
      const mockLasError = () =>
        Promise.resolve({
          errorMessage: 'error message',
          fetchStatus: 'error',
          stores: [],
        });
      (useStoreFetch as jest.Mock).mockImplementation(() => ({ getStores: mockLasError }));

      const { getByText } = renderChangeStoreModal({});
      const changeStoreButton = getByText('Change Store');
      fireEvent.click(changeStoreButton);
      await waitFor(() => {
        expect(getByText('Change Store is unavailable. Please try again later.')).toBeInTheDocument();
      });
    });
  });

  describe('store details functionality', () => {
    test('should show store info when store details button is clicked', async () => {
      const { getByText, getAllByText } = renderChangeStoreModal({});
      const changeStoreButton = getByText('Change Store');
      fireEvent.click(changeStoreButton);
      await waitFor(() => {
        const detailButtons = getAllByText('Store Details');
        fireEvent.click(detailButtons[0]);
        expect(detailButtons[0]).toHaveAttribute('aria-expanded', 'true');
      });
    });

    test('should hide store info for originally selected store when a different store details is clicked', async () => {
      const { getByText, getAllByText } = renderChangeStoreModal({});
      const changeStoreButton = getByText('Change Store');
      fireEvent.click(changeStoreButton);
      await waitFor(() => {
        const detailButtons = getAllByText('Store Details');
        fireEvent.click(detailButtons[0]);
        expect(detailButtons[0]).toHaveAttribute('aria-expanded', 'true');
        fireEvent.click(detailButtons[1]);
        expect(detailButtons[0]).toHaveAttribute('aria-expanded', 'false');
      });
    });

    test('when another store details button is clicked, it should show the store info', async () => {
      const { getByText, getAllByText } = renderChangeStoreModal({});
      const changeStoreButton = getByText('Change Store');
      fireEvent.click(changeStoreButton);
      await waitFor(() => {
        const detailButtons = getAllByText('Store Details');
        fireEvent.click(detailButtons[0]);
        fireEvent.click(detailButtons[1]);
        expect(detailButtons[1]).toHaveAttribute('aria-expanded', 'true');
      });
    });
  });

  describe('translations', () => {
    test('should replace specific i18n key using the translation property', async () => {
      const { getByText } = renderChangeStoreModal({
        props: {
          translation: {
            'changeStoreModal.openModalButton': 'changeStoreModal.zipCodeNotFound',
          },
        },
      });

      await waitFor(() => {
        expect(getByText("Zip code can't be found. Try again.")).toBeInTheDocument();
      });
    });
  });

  describe('<ChangeStoreModal/> with ref', () => {
    test('should open the modal from outside the component using the reference', async () => {
      const user = userEvent.setup();
      const { getByText, queryByText } = renderChangeStoreModal({ withRef: true });
      const openWithRefButton = getByText('Open with ref');
      expect(queryByText('pdp.selection.label.zipCode')).not.toBeInTheDocument();
      await user.click(openWithRefButton);
      expect(getByText('Zip Code')).toBeInTheDocument();
    });
  });

  describe('<ChangeStoreModal/> curbsideEnabled', () => {
    test('When curbsideEnabled FALSE', async () => {
      const { getByText, queryByText } = renderChangeStoreModal({
        props: { curbsideEnabled: false },
        withRef: true,
      });
      const openWithRefButton = getByText('Open with ref');
      fireEvent.click(openWithRefButton);
      await waitFor(() => {
        expect(queryByText('changeStoreModal.pickupFilter.allTypes')).not.toBeInTheDocument();
      });
    });
  });

  describe('<ChangeStoreModal/> children', () => {
    test('When there are NO children components, then should NOT render panel-details', async () => {
      const { getByText, container } = renderChangeStoreModal({
        props: { children: null },
      });
      const changeStoreButton = getByText('Change Store');
      fireEvent.click(changeStoreButton);
      await waitFor(() => {
        const panelDetails = container.getElementsByClassName('panel-details');
        expect(panelDetails).toHaveLength(0);
      });
    });

    test('When there are any children components, then should render panel-details', async () => {
      const { getByText, container } = renderChangeStoreModal({});
      const changeStoreButton = getByText('Change Store');
      fireEvent.click(changeStoreButton);
      await waitFor(() => {
        const panelDetails = container.getElementsByClassName('panel-details');
        expect(panelDetails).toHaveLength(1);
      });
    });
  });
});
