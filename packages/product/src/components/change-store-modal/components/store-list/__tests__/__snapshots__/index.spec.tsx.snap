// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<StoreList /> fulfillment renders correctly for - AT &  1`] = `
<section
  className="stores pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0 at:pt-0 gap:pt-0 gapfs:pt-0 on:pt-0"
>
  <p
    aria-live="polite"
    className="stores--number"
  >
    5 stores found
  </p>
  <ul
    className="stores--list "
  >
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="ABQ UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              ABQ UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            />
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, ALBUQUERQUE, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s1 in-stock relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="EMBARCADERO CENTER"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio selected"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              EMBARCADERO CENTER
            </p>
            <p
              aria-label="2002.68 miles"
              className="store-distance"
            >
              2002.68
               miles
            </p>
            <p
              className="store-availability"
            >
              In Stock
            </p>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={true}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={false}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  3 Embarcadero Center, San Francisco, CA 94111
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:4153918826"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="EMBARCADERO CENTER"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              EMBARCADERO CENTER
            </p>
            <p
              aria-label="2002.68 miles"
              className="store-distance"
            >
              2002.68
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  3 Brazilian Center, San Francisco, CA 58741
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:415587695"
                      target="_parent"
                    >
                      (415) 587-695
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-g3 not-available relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="FERNANDO UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={true}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              FERNANDO UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <p
              className="store-availability"
            >
              Out of Stock
            </p>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, FERNANDO, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="MARINAS BEACH UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              MARINAS BEACH UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, MARINA, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
  </ul>
</section>
`;

exports[`<StoreList /> fulfillment renders correctly for - BR & brColors = true 1`] = `
<section
  className="stores pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0 at:pt-0 gap:pt-0 gapfs:pt-0 on:pt-0"
>
  <p
    aria-live="polite"
    className="stores--number"
  >
    5 stores found
  </p>
  <ul
    className="stores--list "
  >
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="ABQ UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              ABQ UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            />
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, ALBUQUERQUE, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s1 in-stock relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="EMBARCADERO CENTER"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio selected"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              EMBARCADERO CENTER
            </p>
            <p
              aria-label="2002.68 miles"
              className="store-distance"
            >
              2002.68
               miles
            </p>
            <p
              className="store-availability"
            >
              In Stock
            </p>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={true}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={false}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  3 Embarcadero Center, San Francisco, CA 94111
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:4153918826"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="EMBARCADERO CENTER"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              EMBARCADERO CENTER
            </p>
            <p
              aria-label="2002.68 miles"
              className="store-distance"
            >
              2002.68
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  3 Brazilian Center, San Francisco, CA 58741
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:415587695"
                      target="_parent"
                    >
                      (415) 587-695
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-g3 not-available relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="FERNANDO UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={true}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              FERNANDO UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <p
              className="store-availability"
            >
              Out of Stock
            </p>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, FERNANDO, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="MARINAS BEACH UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              MARINAS BEACH UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, MARINA, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
  </ul>
</section>
`;

exports[`<StoreList /> fulfillment renders correctly for - BRFS &  1`] = `
<section
  className="stores pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0 at:pt-0 gap:pt-0 gapfs:pt-0 on:pt-0"
>
  <p
    aria-live="polite"
    className="stores--number"
  >
    5 stores found
  </p>
  <ul
    className="stores--list "
  >
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="ABQ UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              ABQ UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            />
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, ALBUQUERQUE, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s1 in-stock relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="EMBARCADERO CENTER"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio selected"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              EMBARCADERO CENTER
            </p>
            <p
              aria-label="2002.68 miles"
              className="store-distance"
            >
              2002.68
               miles
            </p>
            <p
              className="store-availability"
            >
              In Stock
            </p>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={true}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={false}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  3 Embarcadero Center, San Francisco, CA 94111
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:4153918826"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="EMBARCADERO CENTER"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              EMBARCADERO CENTER
            </p>
            <p
              aria-label="2002.68 miles"
              className="store-distance"
            >
              2002.68
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  3 Brazilian Center, San Francisco, CA 58741
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:415587695"
                      target="_parent"
                    >
                      (415) 587-695
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-g3 not-available relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="FERNANDO UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={true}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              FERNANDO UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <p
              className="store-availability"
            >
              Out of Stock
            </p>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, FERNANDO, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="MARINAS BEACH UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              MARINAS BEACH UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, MARINA, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
  </ul>
</section>
`;

exports[`<StoreList /> fulfillment renders correctly for - GAP &  1`] = `
<section
  className="stores pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0 at:pt-0 gap:pt-0 gapfs:pt-0 on:pt-0"
>
  <p
    aria-live="polite"
    className="stores--number"
  >
    5 stores found
  </p>
  <ul
    className="stores--list "
  >
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="ABQ UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              ABQ UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            />
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, ALBUQUERQUE, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s1 in-stock relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="EMBARCADERO CENTER"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio selected"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              EMBARCADERO CENTER
            </p>
            <p
              aria-label="2002.68 miles"
              className="store-distance"
            >
              2002.68
               miles
            </p>
            <p
              className="store-availability"
            >
              In Stock
            </p>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={true}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={false}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  3 Embarcadero Center, San Francisco, CA 94111
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:4153918826"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="EMBARCADERO CENTER"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              EMBARCADERO CENTER
            </p>
            <p
              aria-label="2002.68 miles"
              className="store-distance"
            >
              2002.68
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  3 Brazilian Center, San Francisco, CA 58741
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:415587695"
                      target="_parent"
                    >
                      (415) 587-695
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-g3 not-available relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="FERNANDO UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={true}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              FERNANDO UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <p
              className="store-availability"
            >
              Out of Stock
            </p>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, FERNANDO, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="MARINAS BEACH UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              MARINAS BEACH UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, MARINA, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
  </ul>
</section>
`;

exports[`<StoreList /> fulfillment renders correctly for - GAPFS &  1`] = `
<section
  className="stores pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0 at:pt-0 gap:pt-0 gapfs:pt-0 on:pt-0"
>
  <p
    aria-live="polite"
    className="stores--number"
  >
    5 stores found
  </p>
  <ul
    className="stores--list "
  >
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="ABQ UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              ABQ UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            />
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, ALBUQUERQUE, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s1 in-stock relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="EMBARCADERO CENTER"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio selected"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              EMBARCADERO CENTER
            </p>
            <p
              aria-label="2002.68 miles"
              className="store-distance"
            >
              2002.68
               miles
            </p>
            <p
              className="store-availability"
            >
              In Stock
            </p>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={true}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={false}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  3 Embarcadero Center, San Francisco, CA 94111
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:4153918826"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="EMBARCADERO CENTER"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              EMBARCADERO CENTER
            </p>
            <p
              aria-label="2002.68 miles"
              className="store-distance"
            >
              2002.68
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  3 Brazilian Center, San Francisco, CA 58741
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:415587695"
                      target="_parent"
                    >
                      (415) 587-695
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-g3 not-available relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="FERNANDO UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={true}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              FERNANDO UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <p
              className="store-availability"
            >
              Out of Stock
            </p>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, FERNANDO, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="MARINAS BEACH UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              MARINAS BEACH UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, MARINA, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
  </ul>
</section>
`;

exports[`<StoreList /> fulfillment renders correctly for - ON &  1`] = `
<section
  className="stores pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0 at:pt-0 gap:pt-0 gapfs:pt-0 on:pt-0"
>
  <p
    aria-live="polite"
    className="stores--number"
  >
    5 stores found
  </p>
  <ul
    className="stores--list "
  >
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="ABQ UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              ABQ UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            />
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, ALBUQUERQUE, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s1 in-stock relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="EMBARCADERO CENTER"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio selected"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              EMBARCADERO CENTER
            </p>
            <p
              aria-label="2002.68 miles"
              className="store-distance"
            >
              2002.68
               miles
            </p>
            <p
              className="store-availability"
            >
              In Stock
            </p>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={true}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={false}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  3 Embarcadero Center, San Francisco, CA 94111
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:4153918826"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="EMBARCADERO CENTER"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              EMBARCADERO CENTER
            </p>
            <p
              aria-label="2002.68 miles"
              className="store-distance"
            >
              2002.68
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  3 Brazilian Center, San Francisco, CA 58741
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:415587695"
                      target="_parent"
                    >
                      (415) 587-695
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-g3 not-available relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="FERNANDO UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={true}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              FERNANDO UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <p
              className="store-availability"
            >
              Out of Stock
            </p>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, FERNANDO, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
    <li
      className="stores--list-item"
    >
      <section
        className="[&::before]:bg-crossbrand-s2 low-inventory relative p-4 box-border shadow-[2px_1px_2px_0_rgba(102,102,102,0.5)] [&::before]:content-[''] [&::before]:absolute [&::before]:top-0 [&::before]:left-0 [&::before]:w-1 [&::before]:h-full [&.in-stock::before]:bg-crossbrand-s1 [&.low-stock::before]:bg-crossbrand-s2 [&.low-inventory::before]:bg-crossbrand-s2 [&_.store-distance]:text-g1 [&_.store-distance]:text-[0.9375rem] [&_.store-distance]:font-brand [&_.store-distance]:uppercase [&_.store-distance]:absolute [&_.store-distance]:right-0 [&_.store-distance]:top-0 [&_.store-distance]:leading-7 br:[&_.store-distance]:leading-6 [&_.store-toggle]:order-1 [&_.store-toggle]:bg-none [&_.store-toggle]:border-none [&_.store-toggle]:at:text-g1 [&_.store-toggle]:br:text-g1 [&_.store-toggle]:on:text-g1 [&_.store-toggle]:gap:text-g1 [&_.store-toggle]:gapfs:text-g1 [&_.store-toggle]:brfs:text-g1 [&_.store-toggle]:text-3.5 [&_.store-toggle]:p-0 [&_.store-toggle]:relative [&_.store-toggle]:max-w-[5.625rem] [&_.store-toggle]:text-left [&_.store-toggle]:font-brand [&_.store-toggle::before]:border-r-2 [&_.store-toggle::before]:border-r-g2 [&_.store-toggle::before]:border-b-2 [&_.store-toggle::before]:border-b-g2 [&_.store-toggle::before]:content-[''] [&_.store-toggle::before]:h-[6px] [&_.store-toggle::before]:absolute [&_.store-toggle::before]:right-[-15px] [&_.store-toggle::before]:top-[7px] [&_.store-toggle::before]:transition-[transform_0.5s_ease_0s] [&_.store-toggle::before]:w-[6px] [&_.store-toggle[aria-expanded="false"]::before]:rotate-[45deg] [&_.store-toggle[aria-expanded="true"]::before]:rotate-[225deg] [&_.store-availability]:font-brand [&_.store-availability]:text-[1rem] [&_.store-availability]:font-bold [&_.store-availability]:mt-2 [&_.store-features]:text-[0.875rem] [&_.store-features]:mt-1 [&_.store-features]:font-brand [&_.store-details]:text-[0.875rem] [&_.store-details]:font-brand [&_.store-details]:mt-2 [&_.store-details]:grid [&_.store-details--wrapper]:order-0 [&_.store-details--wrapper]:font-brand [&_.store-details--wrapper[aria-hidden="true"]]:hidden [&_.store-details--address]:text-g2 [&_.store-details--address]:text-xs [&_.store-details--address]:mb-2 [&_.store-details--address]:w-full [&_.store-details--info-wrapper]:mb-2 [&_.store-details--info-wrapper]:aria-hidden:block [&_.store-details--info-wrapper]:[aria-hidden="false"]:block [&_.store-details--info-wraper]:text-xs [&_.store-details--info-wraper]:mb-2 br:[&_.store-details--phone]:text-b1 [&_.store-details--phone]:text-xs [&_.store-details--phone]:mb-2 [&_.store-details--phone]:text-g1 [&_.store-details--hours-title]:text-bold [&_.store-details--hours-title]:text-xs [&_.store-details--hours-title]:mb-1 [&_.store-details--hours]:text-g2 [&_.store-details--hours]:text-xs [&_.store-details--hours]:leading-relaxed at:[&_.store-details--hours_p]:font-brand br:[&_.store-details--hours_p]:font-brand"
      >
        <div
          className="flex p-0 relative [&_.pdp-button--radio]:flex [&_.pdp-button--radio]:justify-center [&_.pdp-button--radio]:align-center [&_.pdp-button--radio]:relative [&_.pdp-button--radio]:border-1 [&_.pdp-button--radio]:border-bk [&_.pdp-button--radio]:w-6 [&_.pdp-button--radio]:h-6 [&_.pdp-button--radio]:bg-wh [&_.pdp-button--radio]:rounded-full [&_.pdp-button--radio]:p-0 [&_.pdp-button--radio]:aspect-[1/1] [&_.pdp-button--radio:focus]:outline-none [&_.pdp-button--radio.focus-visible]:shadow-sm [&_.pdp-button--radio.focus-visible]:shadow-b2 [&_.pdp-button--radio.selected]:bg-b1 [&_.pdp-button--radio.selected]:border-1 [&_.pdp-button--radio.selected]:border-b1 [&_.pdp-button--radio.selected::after]:content-[''] [&_.pdp-button--radio.selected::after]:block [&_.pdp-button--radio.selected::after]:rotate-45 [&_.pdp-button--radio:disabled::after]:content-[''] [&_.pdp-button--radio:disabled::after]:block [&_.pdp-button--radio:disabled::after]:rotate-45 [&_.pdp-button--radio.selected::after]:border-wh [&_.pdp-button--radio.selected::after]:w-1.5 [&_.pdp-button--radio.selected::after]:h-2.5 [&_.pdp-button--radio.selected::after]:-mt-0.5 [&_.pdp-button--radio.selected::after]:border-0 [&_.pdp-button--radio.selected::after]:border-b-[0.15rem] [&_.pdp-button--radio.selected::after]:border-r-[0.15rem] [&_.pdp-button--radio:disabled]:border-1 [&_.pdp-button--radio:disabled]:border-g4 [&_.pdp-button--radio:disabled]:bg-wh [&_.pdp-button--radio:disabled]:[cursor:not-allowed] [&_.pdp-button--radio:disabled_+_.pdp-button--content_.pdp-button--label]:text-gray-54 [&_.pdp-button--radio::after]:border-g4 [&_.pdp-button--radio::after]:w-0 [&_.pdp-button--radio::after]:h-full [&_.pdp-button--radio::after]:border-1 [&_.pdp-button--radio::after]:border-b-1 [&_.pdp-button--radio::after]:border-r-1 [&_.pdp-button--content]:ml-2 [&_.pdp-button--content]:w-full [&_.pdp-button--label]:text-base [&_.pdp-button--label]:uppercase [&_.pdp-button--label]:w-[55%] [&_.pdp-button--label]:leading-normal on:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-base br:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-b1 brfs:[&_.pdp-button--label]:text-base gap:[&_.pdp-button--label]:text-[0.9375rem] gapfs:[&_.pdp-button--label]:text-[0.9375rem] [&_.pdp-button--label]:font-brand"
        >
          <button
            aria-label="MARINAS BEACH UPTOWN"
            className="border-[1px] after:translate-y-[6px] after:disabled:translate-y-[0px] after:disabled:border-[1px] pdp-button--radio"
            disabled={false}
            onClick={[Function]}
            type="button"
          />
          <div
            className="pdp-button--content"
          >
            <p
              aria-hidden="true"
              className="pdp-button--label"
            >
              MARINAS BEACH UPTOWN
            </p>
            <p
              aria-label="2607.89 miles"
              className="store-distance"
            >
              2607.89
               miles
            </p>
            <div
              className="low-stock-message flex font-bold mt-1 at:[&>.low-stock-message--label]:mt-[-0.125rem] at:[&>.tooltip-container]:before:ml-[-2.125rem] br:[&>.tooltip-container]:before:ml-[-2.063rem] brfs:[&>.tooltip-container]:before:ml-[-2.063rem] gap:[&>.tooltip-container]:before:ml-[-1.813rem] gapfs:[&>.tooltip-container]:before:ml-[-1.813rem] on:[&>.low-stock-message--label]:mt-[-0.063rem] on:[&>.tooltip-container]:before:ml-[-2.313rem]"
            >
              <p
                className="low-stock-message--label"
              >
                Low Stock
              </p>
              <div
                className="[&>div]:position-[unset_!important] [&>div>div]:bottom-[5rem] [&>div>div]:shadow-[0px_1px_2px_0px_rgba(0,0,0,0.3)] [&>div>div]:flex [&>div>div]:filter-none [&>div>div]:!opacity-100 [&>div>div]:p-0 [&>div>div]:pointer-events-auto [&>div>div]:top-[unset] [&>div>div>div]:m-0 [&>div>div>div]:last-of-type:m-0 [&>div>div>div]:last-of-type:bottom-[-22px] [&>div>div>div]:last-of-type:overflow-x-hidden [&>div>div>div]:last-of-type:overflow-y-hidden [&>div>div>div]:last-of-type:whitespace-normal [&>div>div>div]:last-of-type:w-[44px] [&>div>div>div]:last-of-type:h-[22px] [&>div>div>div]:last-of-type:left-[121px] [&>div>div>div]:last-of-type::after:bottom-[11px] [&>div>div>div]:last-of-type::after:left-[11px] [&>div>div>div]:last-of-type::after:overflow-y-hidden [&>div>div>div]:last-of-type::after:overflow-x-hidden [&>div>div>button]:self-baseline [&>div>div>button]:bg-transparent [&>div>div>button]:border-none [&>div>div>button]:text-[rgb(102,102,102)] [&>div>div>button]:font-normal [&>div>div>button]:mr-0 [&>div>div>button]:mt-[1px] [&>div>div>button]:outline-none"
              >
                <div>
                  <button
                    aria-label="changeStoreModal.lowStockOpenTooltipAriaLabel"
                    className="transparent border-none outline-none"
                    onClick={[Function]}
                    type="button"
                  >
                    <span
                      className="[&_svg]:fill-g2"
                    >
                      <svg
                        height="20"
                        width="20"
                      >
                        <g
                          fill="none"
                          transform="translate(.011)"
                        >
                          <path
                            d="M9.2 8.6h1.6v6.6H9.2z"
                            fill="#FFF"
                          />
                        </g>
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <p
              className="store-features"
            >
              Curbside and In-Store Pickup Available
            </p>
            <div
              className="store-details"
            >
              <button
                aria-expanded={false}
                aria-label="Store Details"
                className="store-toggle"
                onClick={[Function]}
                type="button"
              >
                Store Details
              </button>
              <div
                aria-hidden={true}
                className="store-details--wrapper"
              >
                <p
                  className="store-details--address"
                >
                  2240 Q STREET NE, MARINA, NM 87110
                </p>
                <div
                  className="store-details--info-wrapper"
                >
                  <p
                    className="store-details--phone"
                  >
                    <a
                      href="tel:5058812378"
                      target="_parent"
                    >
                      (*************
                    </a>
                  </p>
                  <div
                    className="store-details--hours"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </li>
  </ul>
</section>
`;

exports[`<StoreList /> fulfillment renders correctly without stores for - AT &  1`] = `
<section
  className="stores pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0 at:pt-0 gap:pt-0 gapfs:pt-0 on:pt-0"
>
  <p
    aria-live="polite"
    className="stores--number"
  >
    We're sorry but we can't find this item in stock nearby. Try another size, color, or zip code.
  </p>
  <ul
    className="stores--list "
  />
</section>
`;

exports[`<StoreList /> fulfillment renders correctly without stores for - BR & brColors = true 1`] = `
<section
  className="stores pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0 at:pt-0 gap:pt-0 gapfs:pt-0 on:pt-0"
>
  <p
    aria-live="polite"
    className="stores--number"
  >
    We're sorry but we can't find this item in stock nearby. Try another size, color, or zip code.
  </p>
  <ul
    className="stores--list "
  />
</section>
`;

exports[`<StoreList /> fulfillment renders correctly without stores for - BRFS &  1`] = `
<section
  className="stores pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0 at:pt-0 gap:pt-0 gapfs:pt-0 on:pt-0"
>
  <p
    aria-live="polite"
    className="stores--number"
  >
    We're sorry but we can't find this item in stock nearby. Try another size, color, or zip code.
  </p>
  <ul
    className="stores--list "
  />
</section>
`;

exports[`<StoreList /> fulfillment renders correctly without stores for - GAP &  1`] = `
<section
  className="stores pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0 at:pt-0 gap:pt-0 gapfs:pt-0 on:pt-0"
>
  <p
    aria-live="polite"
    className="stores--number"
  >
    We're sorry but we can't find this item in stock nearby. Try another size, color, or zip code.
  </p>
  <ul
    className="stores--list "
  />
</section>
`;

exports[`<StoreList /> fulfillment renders correctly without stores for - GAPFS &  1`] = `
<section
  className="stores pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0 at:pt-0 gap:pt-0 gapfs:pt-0 on:pt-0"
>
  <p
    aria-live="polite"
    className="stores--number"
  >
    We're sorry but we can't find this item in stock nearby. Try another size, color, or zip code.
  </p>
  <ul
    className="stores--list "
  />
</section>
`;

exports[`<StoreList /> fulfillment renders correctly without stores for - ON &  1`] = `
<section
  className="stores pt-4 [&_.stores--number]:font-brand [&_.stores--number]:font-bold [&_.stores--number]:text-base [&_.stores--number]:mt-4 [&_.stores--number]:mb-2 [&_.stores--number]:text-g1 [&_.stores--list]:pr-1 [&_.stores--list]:pt-2 [&_.stores--list]:pb-24 [&_stores--list]:[@supports(position:sticky)]:pb-0 md:[&_.stores--list]:overflow-y-scroll md:[&_.stores--list]:max-h-[20.5rem] md:[&_.stores--list]:pb-0 [&_.stores--list--tooltip]:pt-11 [&_.stores--list-item]:mb-4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:bg-g4 br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:rounded-s br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb:hover]:bg-g3 br:lg:[&_.stores--list]:[::-webkit-scrollbar]:bg-transparent br:lg:[&_.stores--list]:[::-webkit-scrollbar-thumb]:w-1.5 br:lg:[&_.stores--list-item]:bg-wh br:pt-0 brfs:pt-0 at:pt-0 gap:pt-0 gapfs:pt-0 on:pt-0"
>
  <p
    aria-live="polite"
    className="stores--number"
  >
    We're sorry but we can't find this item in stock nearby. Try another size, color, or zip code.
  </p>
  <ul
    className="stores--list "
  />
</section>
`;
