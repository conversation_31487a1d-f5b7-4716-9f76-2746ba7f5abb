.onReviewListStylesValue {
  header,
  h1,
  h2,
  h3,
  span,
  div,
  p,
  button,
  .pr-accordion-btn svg {
    font-family: theme('fontFamily.brand') !important;
    font-weight: 400;
    color: theme('colors.b1');
    font-size: 1rem;
    line-height: 1.38 !important;
    box-shadow: none;
  }
  .pr-review-display {
    h1 {
      color: '';
    }
  }
  .pr-review-filter-clear-all {
    color: '';
  }
  .pr-rd-header .pr-rd-content-block {
    margin-bottom: 0 !important;
  }
  .pr-read-review .pr-rd-content-block,
  .p-w-r .pr-review-display .pr-rd-content-block {
    margin-bottom: 0 !important;
    overflow: visible;
  }
  .pr-alert div {
    color: unset;
  }
  .pr-rd-flag-review-btn {
    color: theme('colors.b1') !important;
  }
  .pr-rd-main-header-search {
    width: 100% !important;
  }
  .pr-rd-helpful-action-group {
    overflow-y: hidden;
  }
  .pr-rd-main-header {
    display: flex;
    flex-wrap: wrap;
    padding: 0 15px 15px !important;
    margin-bottom: 0 !important;
    > div:first-child {
      width: 44% !important;
      @media (max-width: 840px) {
        width: 100% !important;
      }
    }
    span + div {
      order: 2;
      width: 100%;
    }
    .pr-rd-review-header-contents {
      display: none;
      flex-wrap: wrap;
      order: 2;
      flex-grow: 1;
      column-gap: 10px;
      padding: 10px 0 !important;
      html[lang='en-US'] &:before {
        content: 'Filter Reviews By' !important;
      }
      html[lang='en-CA'] &:before {
        content: 'Filter Reviews By' !important;
      }
      html[lang='fr-CA'] &:before {
        content: 'Filtrer les commentaires par' !important;
      }
      &:before {
        color: rgb(var(--pdp-color-black-1300));
        flex-basis: 100%;
        width: inherit;
        text-align: left !important;
        padding-left: 5px;
        display: table !important;
      }
      @media (max-width: 450px) {
        &:before {
          font-size: 17px;
          padding-left: 0;
          flex-basis: unset;
          margin-left: 10px;
        }
      }
      .pr-multiselect {
        flex: 1 1 auto;
        padding: 0 10px;
        @media (max-width: 840px) {
          width: 100% !important;
        }
        .pr-multiselect-button {
          width: 100% !important;
          border-color: theme('colors.g1');
          .pr-multiselect-button-label {
            color: theme('colors.g1') !important;

            @media (max-width: 450px) {
              font-size: 16px;
            }
          }
          .pr-caret-icon {
            .pr-caret-icon__line {
              stroke: theme('colors.g1') !important;
              stroke-width: 6 !important;
            }
          }
        }
        ul.pr-multiselect-options {
          li:hover {
            .pr-multiselect-item-label {
              color: '';
            }
          }
        }
      }
      .pr-multiselect-button-chest,
      .pr-multiselect-button-hipsrear,
      .pr-multiselect-button-overallsize,
      .pr-multiselect-button-rise,
      .pr-multiselect-button-waist,
      .pr-multiselect-button-width,
      .pr-multiselect-button-length {
        display: block;
      }
      @media (min-width: 540px) {
        width: 70% !important;
        padding: 15px 0 !important;
        &:before {
          padding-left: 0;
        }
        .pr-multiselect {
          flex: 0 0 auto;
          padding: 0;
          .pr-multiselect-button {
            width: auto;
          }
        }
      }
    }
    .pr-rd-main-header-search-sort {
      margin-left: auto;
      padding: 15px 5px !important;
      order: 1;
      display: flex;
      width: '';
      flex-grow: 2;
      justify-content: flex-end;

      @media (max-width: 841px) {
        display: flex;
        flex-direction: column;
        width: 100% !important;
      }

      @media (min-width: 840px) {
        max-width: '';
        width: '' !important;
        margin-left: auto;
      }

      @media (min-width: 540px) {
        order: 2;
        padding: 15px 0 !important;
      }
      @media (max-width: 450px) {
        padding: 15px 5px !important;
      }
      .pr-rd-main-header-search {
        display: none;
        flex-grow: 1;
        padding-top: 0 !important;
        order: 1;
        width: 100%;
        @media (min-width: 840px) {
          padding-top: 27px !important;
          max-width: 340px;
          margin-left: 32px !important;
        }
        @media (max-width: 840px) {
          padding: 0px 0px 0px 4px !important;
          width: 100% !important;
          margin-top: 9px;
        }

        input {
          background-color: '';
          @media (max-width: 450px) {
            font-size: 16px;
          }
        }
      }
      .pr-rd-search-container .pr-rd-search-reviews-input {
        input {
          border-color: theme('colors.b1') !important;
          padding: 8px 40px 8px 8px !important;
          height: 40px !important;
          border-radius: 0px;
          &::placeholder {
            color: #000000a8;
            font-size: 16px;
            font-weight: 400;
          }
          :focus {
            box-shadow: none !important;
          }
        }

        input + button:focus {
          box-shadow: none !important;
        }

        input:focus + button span svg path {
          fill: theme('colors.wh') !important;
        }

        input:focus + button {
          border: none !important;
        }

        .pr-rd-search-reviews-icon-button {
          background-color: theme('colors.b1') !important;
          height: 100%;
          width: unset !important;
          aspect-ratio: 1/1;
          svg {
            width: 13px;
            height: 13px;
          }
        }
      }
      .pr-rd-review-header-sorts {
        flex-grow: 1;
        width: 100% !important;
        position: relative;
        float: none !important;
        padding: 0 !important;

        html[lang='en-US'] &::before {
          content: 'Sort By' !important;
        }
        html[lang='en-CA'] &::before {
          content: 'Sort By' !important;
        }
        html[lang='fr-CA'] &::before {
          content: 'Trier par' !important;
        }
        &::before {
          color: rgb(var(--pdp-color-black-1300));
          display: table;
        }
        @media (min-width: 840px) {
          max-width: 340px;
        }
        @media (max-width: 840px) {
          padding: 0px 0px 0px 4px !important;
          width: 100% !important;
        }
        @media (max-width: 450px) {
          &::before {
            font-size: 17px;
          }
        }

        .pr-rd-sort-group {
          width: 100% !important;
          @media (min-width: 841px) {
            max-width: 400px;
          }
          padding: 0 !important;
          .pr-rd-sort {
            width: 100% !important;
            margin: 5px 10px 0 0 !important;
            border: 1px solid theme('colors.b1');
            appearance: none;
            padding: 8px !important;
            max-width: none;
            font-family: theme('fontFamily.brand');
            font-weight: 400;
            color: theme('colors.b1');
            font-size: 1rem;
            line-height: 1.38;

            @media (min-width: 540px) {
              max-width: '';
            }

            @media (max-width: 450px) {
              font-size: 16px;
              &:before {
                top: 40px;
              }
            }
          }
          .search-arrow {
            position: absolute;
            right: 10px;
            top: 52px;
            transform: translateY(-50%);
            cursor: pointer;
            pointer-events: none;
          }
        }

        /* there is an unexpect dropdown in the sort-by content block */
        /* this style should be removed once Power Review fix this issue.*/
        div.pr-rd-sort-group:nth-of-type(2) {
          display: none;
        }
      }
    }

    .pr-review-filter-headline {
      color: rgb(var(--pdp-color-black-1200)) !important;
    }

    .pr-review-filter-clear-all {
      color: rgb(var(--pdp-color-black-1200)) !important;
    }

    .pr-rd-review-total {
      display: inline-block;
      text-align: center;
      width: 100%;
      order: 3;
      font-weight: theme('fontWeight.light') !important;
      font-size: 0.875rem !important;
      color: var(--gray-90) !important;
      font-family: theme('fontFamily.brand');
      border-bottom: 1px solid #ccc !important;
      padding: 20px 0;

      @media (min-width: 540px) {
        order: 1;
        padding: 0 0 16px;
        font-size: 18px !important;
        margin: 0;
      }

      @media (min-width: 841px) {
        text-align: left;
      }
    }
  }
  .pr-review {
    display: flex;
    flex-wrap: wrap;
    padding: 20px 0 20px !important;
    margin: 0 15px !important;
    border-bottom: 1px solid #ccc !important;
    position: relative;

    @media (max-width: 790px) {
      margin: 0 15px 15px !important;
    }
    .pr-rd-header {
      width: 100%;

      @media (max-width: 568px) {
        margin-bottom: 0;
      }
      .pr-rd-review-headline {
        font-family: theme('fontFamily.brand') !important;
        font-weight: 400;
        font-size: 1.625rem;
        margin: 0 !important;
        float: none !important;
      }

      .pr-rd-star-rating {
        display: inline;
        float: none !important;
        height: '';
        .pr-rating-stars {
          .pr-star-v4 {
            width: 20px !important;
            height: 20px !important;
            background-size: 20px 20px !important;
            overflow: hidden !important;
            &:before,
            &:after {
              height: 20px !important;
              background-size: 20px 20px !important;
            }
          }

          .pr-star-v4.pr-star-v4-25-filled {
            &:before {
              width: 100%;
            }
            &:after {
              width: 0;
            }
          }
          .pr-star-v4.pr-star-v4-50-filled {
            &:before {
              width: 100%;
            }
            &:after {
              width: 0;
            }
          }
          .pr-star-v4.pr-star-v4-75-filled {
            &:before {
              width: 100%;
            }
            &:after {
              width: 0;
            }
          }
        }

        .pr-star-v4.pr-star-v4-25-filled {
          &:before {
            width: 33%;
          }
          &:after {
            width: 67%;
            background-position-x: 100%;
          }
        }

        .pr-star-v4.pr-star-v4-50-filled {
          &:before {
            width: 50%;
          }
          &:after {
            width: 50%;
            background-position-x: 100%;
          }
        }

        .pr-star-v4.pr-star-v4-75-filled {
          &:before {
            width: 67%;
          }
          &:after {
            width: 33%;
            background-position-x: 100%;
          }
        }

        .pr-snippet-rating-decimal {
          display: none;
        }
      }
    }

    .pr-rd-description {
      position: static;
      width: 100%;
      .pr-rd-side-content-block {
        display: flex;
        position: static !important;
        float: none !important;

        .pr-rd-reviewer-details {
          .pr-rd-author-nickname {
            float: left;
            &:after {
              content: ', ';
              font-size: 1rem;
              line-height: 1.38 !important;
              font-family: theme('fontFamily.brand');
              font-weight: theme('fontWeight.normal');
              color: rgb(var(--pdp-color-black-1300));
            }

            span {
              font-size: 1rem;
              line-height: 1.38 !important;
              font-family: theme('fontFamily.brand');
              color: rgb(var(--pdp-color-black-1300));

              @media (max-width: 568px) {
                font-size: 14px !important;
              }
            }
            .pr-rd-bold {
              display: none;
            }
          }
          .pr-rd-author-submission-date {
            display: inline-block;
            float: none !important;
            time {
              font-size: 1rem;
              line-height: 1.38 !important;
              font-family: theme('fontFamily.brand');
              color: rgb(var(--pdp-color-black-1300));
              margin-left: 1px;

              @media (max-width: 568px) {
                font-size: 14px !important;
              }
            }
            .pr-rd-bold {
              display: none;
            }
          }
          .pr-rd-demographic,
          .pr-rd-author-location {
            display: none;
          }
        }

        .pr-rd-reviewer-type {
          &.pr-verified_buyer .pr-rd-badging-text {
            word-break: normal;
          }
          span {
            font-size: 1rem;
            line-height: 1.38 !important;
            font-family: theme('fontFamily.brand');
            font-weight: 400;
            color: rgb(var(--pdp-color-black-1300));

            @media (max-width: 568px) {
              font-size: 0.875rem !important;
            }
          }

          .pr-badging-icon {
            circle {
              fill: theme('colors.bk');
            }
          }

          &.pr-verified_buyer {
            .pr-badging-icon {
              display: none;
            }
          }
        }

        .pr-verified_buyer,
        .pr-rd-review-disclosure {
          position: absolute;
          bottom: 0em;
          margin: 0 0 10px 0 !important;
          &:before {
            content: '';
            background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTRweCIgaGVpZ2h0PSIxNHB4IiB2aWV3Qm94PSIwIDAgMTQgMTQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+SWNvbjwvdGl0bGU+CiAgICA8ZyBpZD0iUG93ZXItUmV2aWV3cyIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9Ik1vYmlsZSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTE3LjAwMDAwMCwgLTYxNjMuMDAwMDAwKSI+CiAgICAgICAgICAgIDxnIGlkPSJJY29uIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxNy4wMDAwMDAsIDYxNjMuMDAwMDAwKSI+CiAgICAgICAgICAgICAgICA8cGF0aCBkPSJNNywxNCBDMTAuODY1OTkzMiwxNCAxNCwxMC44NjU5OTMyIDE0LDcgQzE0LDMuMTM0MDA2NzUgMTAuODY1OTkzMiwwIDcsMCBDMy4xMzQwMDY3NSwwIDAsMy4xMzQwMDY3NSAwLDcgQzAsMTAuODY1OTkzMiAzLjEzNDAwNjc1LDE0IDcsMTQgWiIgaWQ9IkNvbWJpbmVkLVNoYXBlIiBmaWxsPSIjMDAwMDAwIj48L3BhdGg+CiAgICAgICAgICAgICAgICA8cG9seWdvbiBpZD0iUmVjdGFuZ2xlIiBmaWxsPSIjRkZGRkZGIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg3LjIyMDUwMCwgNi41MjA1MDApIHJvdGF0ZSgtMzE1LjAwMDAwMCkgdHJhbnNsYXRlKC03LjIyMDUwMCwgLTYuNTIwNTAwKSAiIHBvaW50cz0iNy43IDIuOCA5LjU0MSAyLjggOS41NDEgMTAuMjQxIDQuOSAxMC4yNDEgNC45IDguNDU4ODc3NDcgNy43IDguNDU4ODc3NDciPjwvcG9seWdvbj4KICAgICAgICAgICAgPC9nPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+)
              no-repeat !important;
            width: 14px;
            height: 14px;
            display: inline-block;
            position: relative;
            top: 2px;
          }
        }

        .pr-rd-review-disclosure {
          font-size: 1rem;
          line-height: 1.38 !important;
          font-family: theme('fontFamily.brand');
          font-weight: 400;
          color: rgb(var(--pdp-color-black-1300));
          &:before {
            left: -3px;
          }
        }

        .pr-rd-reviewer-type:not(.pr-staff_reviewer) + .pr-rd-reviewer-type,
        .pr-rd-reviewer-type:not(.pr-staff_reviewer) + .pr-rd-review-disclosure {
          left: 8.9em;
          padding-left: 0.9375rem;
        }
      }

      .pr-rd-description-text {
        font-size: 1rem;
        line-height: 1.38 !important;
        font-family: theme('fontFamily.brand');
        font-weight: 400;
        color: rgb(var(--pdp-color-black-1300)) !important;
        width: 75% !important;
        margin: 15px 0 5px !important;
      }
    }

    .pr-accordion {
      &.pr-rd-content-block {
        margin-bottom: 30px !important;
      }
      .pr-accordion-btn {
        span {
          color: theme('colors.g1');
          text-decoration: underline;
          font-size: 0.875rem;
        }

        .pr-caret-icon {
          width: auto;
          svg {
            width: 14px;
            height: 14px;
            .pr-caret-icon__line {
              stroke: theme('colors.g1');
            }
          }
        }

        &:focus {
          box-shadow: none !important;
        }
      }

      &.pr-accordion-collapsed {
        .pr-accordion-content {
          max-height: 60px;
        }
      }

      .pr-accordion-content {
        display: flex;
        flex-direction: column-reverse;
        overflow: hidden !important;
        .pr-rd-sliders {
          order: 2;
          .pr-rd-sliders_slider {
            @media (max-width: 568px) {
              width: 100%;
            }
            .pr-rd-sliders_title {
              font-size: 1rem;
              line-height: 1.38 !important;
              font-family: theme('fontFamily.brand');
              font-weight: 400;
              color: rgb(var(--pdp-color-black-1300));
            }

            .pr-size-fit {
              .pr-size-fit_slider {
                .pr-size-fit_slider_node-group {
                  .pr-size-fit_slider_node {
                    background-color: #ccc;
                    height: 8px;
                    width: 7px;
                  }

                  .pr-size-fit_slider_node--active {
                    background: theme('colors.bk');
                    height: 16px;
                    width: 16px;
                  }
                }

                .pr-size-fit_slider_line {
                  border-top-color: #ccc;
                  border-top-width: 3px;
                  top: 40%;
                }
              }

              .pr-size-fit_label {
                font-family: theme('fontFamily.brand');
                font-weight: 400;
                color: rgb(var(--pdp-color-black-1300));
                font-size: 0.875rem;
                line-height: 1.38 !important;
              }
            }
          }
        }

        .pr-rd-subratings {
          display: flex;
          flex-direction: row;
          order: 2;

          @media (max-width: 569px) {
            display: grid;
          }

          .pr-rd-def-list {
            dt,
            dd {
              min-width: 0;
            }

            dt {
              font-weight: 400 !important;
              padding-right: 15px 0;
              height: 25px;

              &:after {
                content: ':';
                font-size: 1rem;
                line-height: 1.38 !important;
                font-family: theme('fontFamily.brand');
                font-weight: 400;
              }
            }

            dd {
              font-size: 1rem;
              line-height: 1.38 !important;
              font-family: theme('fontFamily.brand');
              font-weight: 400;
              color: rgb(var(--pdp-color-black-1300));
            }

            .pr-snippet-stars {
              .pr-snippet-stars-png {
                overflow: hidden;
              }
              .pr-rating-stars {
                align-items: center;
                display: flex;
                justify-content: space-between;
                margin: 8px 0 0 5px;
                position: relative;
                width: 200px;
                &::before {
                  content: '';
                  position: absolute;
                  top: 50%;
                  left: 0;
                  width: 97%;
                  height: 3px;
                  background-color: var(--gray-20);
                  transform: translateY(-50%);
                  z-index: 0;
                }
              }
              .pr-star-v4 {
                height: 8px !important;
                width: 8px !important;
                border-radius: 50%;
                z-index: 1;
                background-color: #ccc;
                border: 1px solid transparent;
                &.pr-star-v4-100-filled {
                  &:last-child {
                    position: relative;
                    &:before {
                      background-color: theme('colors.bk');
                      border-radius: 50%;
                      content: '';
                      height: 16px;
                      position: absolute;
                      top: -5px;
                      width: 16px;
                      right: -3px;
                    }
                  }
                }
                &.pr-star-v4-0-filled {
                  position: relative;
                }
                &.pr-star-v4-100-filled + .pr-star-v4-0-filled {
                  &:before {
                    background-color: theme('colors.b1');
                    border-radius: 50%;
                    content: '';
                    height: 16px;
                    position: absolute;
                    top: -5px;
                    width: 16px;
                    right: 43px;
                  }
                }
              }
              .pr-snippet-rating-decimal {
                display: none;
              }
            }
          }
        }
        .pr-rd-images {
          order: 2;
        }
        .pr-rd-footer {
          background: none;
          .pr-rd-helpful-action {
            & a {
              color: rgb(var(--pdp-color-black-1300)) !important;
              font-family: theme('fontFamily.brand');
              font-weight: 400;
              text-decoration: underline;
            }
            .pr-rd-helpful-action-legend,
            .pr-rd-helpful-action-btn-group {
              display: none;
            }
          }
        }
        .pr-rd-bottomline {
          display: none;
        }
      }
    }
  }
  @media (max-width: 567px) {
    .p-w-r .pr-review-display .pr-rd-main-header-with-filters .pr-rd-review-header-contents {
      flex-direction: row;
    }
  }
  .pr-rd-main-footer {
    border: none !important;
    margin-bottom: 0;
    padding: 30px 0 !important;
    .pr-rd-content-block {
      width: 30% !important;
      margin: auto;
      text-align: center !important;
      min-width: 175px;
      .pr-rd-review-position {
        margin: auto;
        float: none !important;
        white-space: nowrap;
        margin-bottom: 0;
        @media (min-width: 540px) {
          margin-bottom: -33px;
        }
        @media (min-width: 790px) {
          margin-bottom: -23px !important;
        }
        & span {
          font-weight: 400;
          color: rgb(var(--pdp-color-black-1300));
        }
      }
      .pr-rd-to-top {
        display: none;
      }
      .pr-rd-pagination {
        border: none !important;
        margin: 0 10px auto;
        & span {
          display: none;
        }
        @media (min-width: 540px) {
          width: 100%;
          margin: 0 auto;
        }
      }
      @media (min-width: 540px) {
        min-width: 340px;
        .pr-rd-review-position {
          padding-left: 21px;
        }
        .pr-rd-pagination {
          a.pr-rd-pagination-btn.pr-rd-pagination-btn--previous {
            float: left;
            &:before {
              visibility: visible;
              background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB2aWV3Qm94PSIwIDAgMTUgMjIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+YXJyb3c8L3RpdGxlPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImFycm93IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgLTEuMDAwMDAwKSIgZmlsbD0iIzMzMzMzMyIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPHBvbHlnb24gaWQ9IlNoYXBlIiBwb2ludHM9IjE0LjM5NCAxOS40MjM1IDYuOTY5IDEyIDE0LjM5NCA0LjU3NSAxMS4yMTI1IDEuMzkzNSAwLjYwNiAxMiAxMS4yMTI1IDIyLjYwNSI+PC9wb2x5Z29uPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+)
                no-repeat !important;
              content: '';
              display: inline-block;
              height: 0.625rem;
              position: relative;
              left: 0;
              top: 7px;
              vertical-align: top;
              width: 1rem !important;
              transform: rotate(0deg);
            }
          }
          a.pr-rd-pagination-btn.pr-rd-pagination-btn--next:last-child {
            float: right;
            &:after {
              visibility: visible;
              background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB2aWV3Qm94PSIwIDAgMTUgMjIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+YXJyb3c8L3RpdGxlPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImFycm93IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgLTEuMDAwMDAwKSIgZmlsbD0iIzMzMzMzMyIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPHBvbHlnb24gaWQ9IlNoYXBlIiBwb2ludHM9IjE0LjM5NCAxOS40MjM1IDYuOTY5IDEyIDE0LjM5NCA0LjU3NSAxMS4yMTI1IDEuMzkzNSAwLjYwNiAxMiAxMS4yMTI1IDIyLjYwNSI+PC9wb2x5Z29uPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+)
                no-repeat !important;
              content: '';
              display: inline-block;
              height: 0.625rem;
              position: relative;
              left: 0;
              top: 7px;
              transform: rotate(180deg) scaleX(1);
              vertical-align: top;
              width: 1rem !important;
            }
          }
        }
      }
    }
  }
  .pr-rd-main-footer a {
    color: theme('colors.b1') !important;
    font-family: theme('fontFamily.brand');
    font-weight: 400;
    text-decoration: underline;
  }
  .pr-rd-bottomline {
    display: none;
  }
  .pr-read-review.pr-rd-display-desktop .pr-review-condensed .pr-rd-reviewed-at,
  .pr-read-review.pr-rd-display-tablet .pr-review-condensed .pr-rd-reviewed-at,
  .pr-review-display.pr-rd-display-desktop .pr-review-condensed .pr-rd-reviewed-at,
  .pr-review-display.pr-rd-display-tablet .pr-review-condensed .pr-rd-reviewed-at,
  .pr-rd-reviewed-at {
    display: none !important;
  }
}
.onPdpReviewVoting {
  .pr-rd-helpful-action-legend,
  .pr-rd-helpful-action-btn-group {
    overflow: visible !important;
    display: block !important;
  }
  .pr-helpful-voting,
  .pr-rd-helpful-action-btn-group {
    display: flex;
    margin-left: 5px;
    & .pr-helpful-btn[aria-disabled] {
      opacity: 60%;
    }
    & .pr-helpful-btn:hover {
      background: none;
      > span {
        > svg {
          > g {
            > path {
              fill: #767676;
            }
          }
        }
      }
      & .pr-helpful-count {
        color: #767676;
      }
    }
    & .pr-helpful-btn:focus {
      box-shadow: none;
    }
    & .pr-helpful-btn {
      padding-top: 2.2px;
      padding-bottom: 2.2px;
      padding-left: 3.2px;
      padding-right: 3.2px;
      width: 45px;
      height: 24px;
      line-height: 0px;
      position: relative;
      border: none;
      margin: 0px;
      margin-right: 2px;
      & .pr-helpful-count {
        font-size: 12px;
      }
      > span {
        width: 0px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        > svg {
          width: 24px;
        }
      }
      & .pr-thumbs-icon-down {
        top: 50%;
      }
      & .pr-helpful-count {
        right: 15px;
        font-weight: theme('fontWeight.font-weight-regular-font-weight');
      }
    }
    & button.pr-helpful-btn.pr-helpful-yes.pr-helpful-active,
    button.pr-helpful-btn.pr-helpful-no.pr-helpful-active {
      background-color: transparent !important;
      > span {
        > svg {
          > g {
            > path {
              fill: #4b72a2 !important;
            }
          }
        }
      }
      & .pr-helpful-count {
        color: #4b72a2 !important;
      }
    }
  }
  .pr-rd-helpful-action-group {
    display: flex;
    align-items: center;
  }
  .pr-rd-helpful-action-legend {
    & .pr-rd-helpful-text {
      color: theme('colors.bk');
      white-space: nowrap;
      font-weight: 500;
      font-size: 1rem;
    }
  }
  .pr-rd-helpful-text {
    vertical-align: top;
  }
  .pr-rd-flag-review-container {
    position: absolute;
    bottom: 2vh;
  }
}
.onIsReviewsSearch {
  .pr-rd-review-header-contents {
    @media (min-width: 540px) {
      width: 33% !important;
    }
  }
  .pr-rd-main-header-search {
    overflow: visible !important;
    display: block !important;
  }
}
.onRedesignReviewListStylesValue {
  .p-w-r {
    .pr-review-display,
    .pr-snippet-stars,
    header.pr-rd-main-header > div.pr-rd-review-header-contents,
    .pr-multiselect-button,
    .pr-multiselect-item,
    select.pr-rd-sort {
      background-color: theme('colors.wh') !important;
    }
    .pr-review-display {
      a {
        color: '';
      }
      .pr-rd-no-reviews {
        margin-top: 18px;
        a {
          background-color: theme('colors.wh');
          color: theme('colors.bk') !important;
          border-radius: 0 !important;
          border: 1px solid theme('colors.b1');
          width: 100%;
          padding: 11px 75px;
          font-weight: theme('fontWeight.font-weight-regular-font-weight');

          &:hover {
            background-color: theme('colors.b1');
            color: theme('colors.wh') !important;
            text-decoration: none;
          }

          @media (max-width: 375px) {
            padding: 7px 50px;
          }
        }
      }
      .pr-rd-description.pr-rd-content-block {
        position: static;
      }
    }
  }
}
.onRedesignReviewListStylesValue.onHasDrawer {
  .p-w-r {
    .pr-review-display,
    .pr-snippet-stars,
    header.pr-rd-main-header > div.pr-rd-review-header-contents,
    .pr-multiselect-button,
    .pr-multiselect-item,
    select.pr-rd-sort {
      background-color: transparent !important;
    }
    .pr-review-display {
      a {
        color: '';
      }
    }
  }
}
