'use client';
import { useCallback, useMemo } from 'react';

import { addPageAction, reportTiming } from '@ecom-next/core/components/reporting';
import { waitFor } from '../../../util/poll';
import { LoadTiming, useScriptLoaderProvider } from '../../../legacy/packages/script-loader-provider';

const URL = 'https://ui.powerreviews.com/stable/4.1/ui.js';

async function pollWindow(): Promise<void> {
  try {
    await waitFor(() => window.pwr, { interval: 500, maxWait: 30000 });

    window.pwr =
      window.pwr ||
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      function pwrFallback(...params: Array<any>) {
        window.pwr.q = window.pwr.q || [];
        window.pwr.q.push(...params);
      };

    addPageAction('PowerReviewsLoadSuccess', {});
    reportTiming('PowerReviewsLoaded');

    return await Promise.resolve();
  } catch (error: unknown) {
    addPageAction('PowerReviewsLoadError', {});
    // eslint-disable-next-line no-console
    console.error(new Error(`[${(error as Error)?.message || error}]: failure loading Power Reviews`));
    return Promise.reject(new Error(error as string));
  }
}

type UsePowerReviewsType = {
  initPowerReviews: () => Promise<void>;
};

export function usePowerReviews(shouldLoadReviewsScript: boolean): UsePowerReviewsType {
  const { addScriptToQueue } = useScriptLoaderProvider();
  const loadScript = useCallback((): Promise<void> => {
    return new Promise((resolve, reject) => {
      const scriptConfig = {
        loadOn: LoadTiming.load,
        onError: reject,
        onLoad: resolve,
        optionalProps: {
          type: 'text/javascript',
        },
        url: URL,
      };
      addScriptToQueue(scriptConfig);
    }).then(() => pollWindow());
  }, [addScriptToQueue]);

  const initPowerReviews = useMemo(() => (shouldLoadReviewsScript ? loadScript : pollWindow), [shouldLoadReviewsScript, loadScript]);

  return { initPowerReviews };
}
