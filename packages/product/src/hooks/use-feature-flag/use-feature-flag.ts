import { useContext, useMemo } from 'react';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { FeatureFlagsContext } from '@ecom-next/core/legacy/feature-flags';

type FeatureVariable = {
  [name: string]: string | Record<string, string>;
};

export type FeatureFlag = {
  featureVariables?: Record<string, FeatureVariable>;
  isBrandAgnostic: boolean;
  key: string;
};

type FeatureFlagKeys =
  | 'LPOCertonaReviews'
  | 'PDPPowerReviews'
  | 'PDPLoadPowerReviews'
  | 'LPOHybridReviews'
  | 'storageEffect'
  | 'PDPVideoRepeated'
  | 'aiRecommendations'
  | 'aiReviews'
  | 'atBuyboxRedesign2024'
  | 'atRedesign'
  | 'atRedesign2024'
  | 'awaitAddToBag'
  | 'bopis'
  | 'bopisExclusionUpdates'
  | 'brRedesign2024'
  | 'brRedesign2024Ph2'
  | 'brWhiteBackground'
  | 'brfsRedesign2024'
  | 'certonaRecommendations'
  | 'chasBopis'
  | 'chasPdp'
  | 'chasQuickAdd'
  | 'displayRatingOnRecommendations'
  | 'draprAnalytics'
  | 'dropship'
  | 'findmine'
  | 'findmineATLayoutUpdate'
  | 'gapBuyboxRedesign2024'
  | 'hybridLandingPage'
  | 'landingPageOptimization'
  | 'redesignedImageGalleryEnabled'
  | 'loadCertonaThroughPDP'
  | 'loadCertonaThroughSitewide'
  | 'loadDraprScriptFromPDP'
  | 'loadFindmineScriptFromPDP'
  | 'optimizeImageLoad'
  | 'pdpMiniBag'
  | 'percentageOffOnPricing'
  | 'pmcs'
  | 'quickAddOnRecommendations'
  | 'redesign2024Ph2'
  | 'scarcityMessage'
  | 'showVideoOnRedesign'
  | 'showPricesAndMarketingOnRecommendations'
  | 'siteWideMiniBag'
  | 'sizeSampling'
  | 'specialOOSTemplate'
  | 'stickyAddToBag'
  | 'superPDPPh2'
  | 'usePDPTemplate'
  | 'pdpLightInsituModal'
  | 'pdpPaypal'
  | 'pdpAfterpay'
  | 'bricks'
  | 'crosslinksAdeptmind'
  | 'hideShipping'
  | 'marketingContainer'
  | 'reviewsDrawer'
  | 'pdpClothingLabel'
  | 'bottomBreadcrumbs'
  | 'pdpCAPIv3'
  | 'pdpMVGRefresh'
  | 'pdpColorPickerLayout'
  | 'pdpHUIRedesign'
  | 'PDPFulfillmentSelectionV2'
  | 'PDPReviewSearch'
  | 'PDPReviewVoting'
  | 'pdpImageGalleryXL'
  | 'pdpFacebookMetadata'
  | 'pdpHiddenPhotoGallery'
  | 'insituMarketingContainer'
  | 'pixlee';

type FeatureFlagsHelperType = Record<FeatureFlagKeys, FeatureFlag>;

export const FeatureFlags: FeatureFlagsHelperType = {
  LPOCertonaReviews: {
    isBrandAgnostic: true,
    key: 'pdp-lpo-power-reviews',
  },
  PDPReviewVoting: {
    isBrandAgnostic: false,
    key: 'pdp-review-voting',
  },
  PDPReviewSearch: {
    isBrandAgnostic: false,
    key: 'pdp-review-search',
  },
  PDPLoadPowerReviews: {
    isBrandAgnostic: true,
    key: 'pdp-mfe-load-power-reviews',
  },
  PDPFulfillmentSelectionV2: {
    isBrandAgnostic: true,
    key: 'pdp-fulfillment-selection-v2',
  },
  pdpHiddenPhotoGallery: {
    isBrandAgnostic: false,
    key: 'hidden-photogallery',
  },
  PDPPowerReviews: {
    isBrandAgnostic: true,
    key: 'pdp-power-reviews',
  },
  LPOHybridReviews: {
    isBrandAgnostic: true,
    key: 'pdp-lpo-hybrid-power-reviews',
  },
  redesignedImageGalleryEnabled: {
    isBrandAgnostic: true,
    key: 'pdp-enable-image-gallery-redesign',
  },
  storageEffect: {
    isBrandAgnostic: true,
    key: 'pdp-enable-storage-effect',
  },
  PDPVideoRepeated: {
    isBrandAgnostic: false,
    key: 'pdp-video-repeated',
  },
  aiRecommendations: {
    isBrandAgnostic: false,
    key: 'pdp-ai-recommendations',
  },
  aiReviews: {
    isBrandAgnostic: false,
    key: 'pdp-ai-reviews',
  },
  atBuyboxRedesign2024: {
    isBrandAgnostic: true,
    key: 'at-buybox-2024',
  },
  atRedesign: {
    isBrandAgnostic: true,
    key: 'at-redesign-2023',
  },
  atRedesign2024: {
    isBrandAgnostic: true,
    key: 'at-redesign-2024',
  },
  awaitAddToBag: {
    isBrandAgnostic: true,
    key: 'pdp-add-to-bag-await',
  },
  bopis: {
    isBrandAgnostic: false,
    key: 'bopis',
  },
  bopisExclusionUpdates: {
    isBrandAgnostic: true,
    key: 'pdp-bopis-exclusion-updates',
  },
  bottomBreadcrumbs: {
    isBrandAgnostic: true,
    key: 'bottom-product-breadcrumbs',
  },
  bricks: {
    isBrandAgnostic: false,
    key: 'pdp-bricks',
  },
  brRedesign2024: {
    isBrandAgnostic: true,
    key: 'br-redesign-2024',
  },
  brRedesign2024Ph2: {
    isBrandAgnostic: true,
    key: 'br-redesign-2024-ph2',
  },
  brWhiteBackground: {
    isBrandAgnostic: true,
    key: 'br-white-background',
  },
  brfsRedesign2024: {
    isBrandAgnostic: true,
    key: 'brfs-redesign-2024',
  },
  chasBopis: {
    isBrandAgnostic: true,
    key: 'pdp-chas-bopis',
  },
  certonaRecommendations: {
    isBrandAgnostic: false,
    key: 'pdp-recommendations',
  },
  chasPdp: {
    isBrandAgnostic: true,
    key: 'pdp-chas',
  },
  chasQuickAdd: {
    isBrandAgnostic: true,
    key: 'pdp-chas-quick-add',
  },
  crosslinksAdeptmind: {
    isBrandAgnostic: true,
    key: 'adeptmind-crosslinks-pdp',
  },
  displayRatingOnRecommendations: {
    isBrandAgnostic: true,
    key: 'pdp-recommendations-ratings',
  },
  draprAnalytics: {
    isBrandAgnostic: false,
    key: 'pdp-drapr-analytics-script',
  },
  dropship: {
    featureVariables: {
      showBOPIS: { name: 'showBOPIS' },
      showDrapr: { name: 'showDrapr' },
      showFindMine: { name: 'showFindMine' },
      showOtherPaymentMethods: { name: 'showOtherPaymentMethods' },
      showSizeGuide: { name: 'showSizeGuide' },
      showTrueFit: { name: 'showTrueFit' },
    },
    isBrandAgnostic: false,
    key: 'pdp-dropship',
  },
  findmine: {
    isBrandAgnostic: true,
    key: 'findmine',
  },
  findmineATLayoutUpdate: {
    isBrandAgnostic: true,
    key: 'findmine-athleta-layout-update',
  },
  gapBuyboxRedesign2024: {
    isBrandAgnostic: true,
    key: 'gap-buybox-2024',
  },
  hideShipping: {
    isBrandAgnostic: false,
    key: 'pdp-hide-shipping',
  },
  hybridLandingPage: {
    isBrandAgnostic: false,
    key: 'pdp-lpo-plp',
  },
  insituMarketingContainer: {
    isBrandAgnostic: true,
    key: 'pdp-insitu-marketing-container',
  },
  landingPageOptimization: {
    isBrandAgnostic: false,
    key: 'pdp-lpo-certona',
  },
  loadCertonaThroughPDP: {
    isBrandAgnostic: true,
    key: 'pdp-mfe-load-certona',
  },
  loadCertonaThroughSitewide: {
    isBrandAgnostic: true,
    key: 'swf-load-certona-locally',
  },
  loadDraprScriptFromPDP: {
    isBrandAgnostic: false,
    key: 'pdp-mfe-load-drapr-script',
  },
  loadFindmineScriptFromPDP: {
    isBrandAgnostic: true,
    key: 'pdp-mfe-load-findmine',
  },
  marketingContainer: {
    isBrandAgnostic: true,
    key: 'pdp-marketing-container',
  },
  optimizeImageLoad: {
    isBrandAgnostic: true,
    key: 'optimize-image-loads',
  },
  pdpLightInsituModal: {
    isBrandAgnostic: false,
    key: 'pdp-atb-conf',
  },
  pdpCAPIv3: {
    isBrandAgnostic: true,
    key: 'pdp-capi-v3',
  },
  pdpMVGRefresh: {
    isBrandAgnostic: false,
    key: 'pdp-mvg-refresh',
  },
  pdpMiniBag: {
    isBrandAgnostic: true,
    key: 'pdp-mini-bag',
  },
  percentageOffOnPricing: {
    featureVariables: {
      useFromCapi: { name: 'useFromCapi' },
    },
    isBrandAgnostic: false,
    key: 'pdp-percentage-off',
  },
  pmcs: {
    isBrandAgnostic: false,
    key: 'pdp-pmcs',
  },
  quickAddOnRecommendations: {
    isBrandAgnostic: true,
    key: 'pdp-quick-add-product-recommendations',
  },
  redesign2024Ph2: {
    isBrandAgnostic: false,
    key: 'redesign-2024-ph2',
  },
  reviewsDrawer: {
    isBrandAgnostic: false,
    key: 'pdp-review-snapshot-drawer',
  },
  scarcityMessage: {
    isBrandAgnostic: true,
    key: 'pdp-scarcity',
  },
  showPricesAndMarketingOnRecommendations: {
    isBrandAgnostic: true,
    key: 'pdp-recommendations-pricing',
  },
  showVideoOnRedesign: {
    isBrandAgnostic: false,
    key: 'pdp-redesign-video',
  },
  siteWideMiniBag: {
    isBrandAgnostic: true,
    key: 'bag-ui-mini-bag',
  },
  sizeSampling: {
    isBrandAgnostic: false,
    key: 'pdp-size-sampling',
  },
  specialOOSTemplate: {
    isBrandAgnostic: false,
    key: 'pdp-special-oos',
  },
  stickyAddToBag: {
    isBrandAgnostic: false,
    key: 'pdp-sticky-add-to-bag',
  },
  superPDPPh2: {
    isBrandAgnostic: true,
    key: 'pdp-super-ph2',
  },
  usePDPTemplate: {
    isBrandAgnostic: false,
    key: 'use-pdp-template',
  },
  pdpPaypal: {
    isBrandAgnostic: true,
    key: 'pdp-paypal',
  },
  pdpAfterpay: {
    isBrandAgnostic: true,
    key: 'pdp-afterpay',
  },
  pdpClothingLabel: {
    isBrandAgnostic: false,
    key: 'pdp-clothing-label',
  },
  pdpColorPickerLayout: {
    isBrandAgnostic: false,
    key: 'pdp-color-swatch-layout',
    featureVariables: {
      default: { name: 'default' },
      stacked: { name: 'stacked' },
    },
  },
  pdpHUIRedesign: {
    isBrandAgnostic: false,
    key: 'pdp-hui-redesign',
  },
  pdpImageGalleryXL: {
    isBrandAgnostic: false,
    key: 'pdp-use-xl-image',
  },
  pdpFacebookMetadata: {
    isBrandAgnostic: false,
    key: 'pdp-facebook-metadata',
  },
  pixlee: {
    isBrandAgnostic: true,
    key: 'pdp-pixleeegc-widget',
    featureVariables: {
      showPixleeCategoryWidget: { name: 'showPixleeCategoryWidget' },
    },
  },
};

function useFeatureKey(feature: FeatureFlag): string {
  const { key, isBrandAgnostic } = feature;
  const { brandName, market } = useAppState();
  return isBrandAgnostic ? key : `${key}-${market}-${brandName}`;
}

export function useFeature(feature: FeatureFlag): boolean {
  const { enabledFeatures } = useContext(FeatureFlagsContext);

  const featureKey = useFeatureKey(feature);
  return useMemo(() => !!enabledFeatures[featureKey], [enabledFeatures, featureKey]);
}

export function useFeatureFlag(featureName: string): boolean {
  const { brandName, market } = useAppState();
  const { enabledFeatures } = useContext(FeatureFlagsContext);
  const featureEnabled = useMemo(() => !!enabledFeatures[`${featureName}-${market}-${brandName}`], [brandName, market, enabledFeatures, featureName]);

  return featureEnabled || false;
}

export function useBrandAgnosticFeatureFlag(featureName: string): boolean {
  const { enabledFeatures } = useContext(FeatureFlagsContext);
  const featureEnabled = useMemo(() => !!enabledFeatures[featureName], [enabledFeatures, featureName]);

  return featureEnabled || false;
}

export function useBrandAgnosticFeatureVariables(featureName: string): Record<string, unknown> {
  const { featureVariables = {} } = useContext(FeatureFlagsContext);

  const allVariables = useMemo(() => featureVariables[featureName], [featureVariables, featureName]);

  return allVariables || {};
}
