// @ts-nocheck
'use client'

import { AppStateProvider } from '@ecom-next/sitewide/app-state-provider';
import { LocalizationProvider } from '@ecom-next/sitewide/localization-provider';
import { Brands, StitchStyleProvider } from "@ecom-next/core/react-stitch";
import { action } from '@storybook/addon-actions';
import { Description } from '@storybook/addon-docs/blocks';
import type { ComponentMeta } from '@storybook/react';
import React from 'react';

import ChangeStoreModal from '..';
import readme from '../README.md';
import { LOCALIZATION_DATA } from './data/change-store-localization-data';
import { storeInfoMock } from './data/store-info-mock';
import { optionalContentStyles, textStyles } from './optional-panel-styles';

const Doc = () => <Description markdown={readme} />;
export default {
  argTypes: {
    LASConfig: {
      control: { type: 'object' },
      description: 'API Config',
      table: {
        category: 'Configuration Props',
      },
    },
    PASConfig: {
      control: { type: 'object' },
      description: 'API Config',
      table: {
        category: 'Configuration Props',
      },
    },
    brand: {
      control: { type: 'radio' },
      description: 'Brand options',
      options: [...Object.values(Brands)],
      table: {
        category: 'Behavioral Props',
      },
    },
    container: {
      control: { type: 'string' },
      description: 'Class name for the Change Store Modal container',
      table: {
        category: 'Configuration Props',
      },
    },
    curbsideEnabled: {
      control: { type: 'boolean' },
      description:
        'Controls if Curbside pickup is enabled or disabled. OBS: This feature will be removed soon, since the Curbside option will no longer be available',
      table: {
        category: 'Behavioral Props',
      },
    },
    id: {
      control: { type: 'string' },
      description: 'Product id that will be showed by Change Store Modal',
      table: {
        category: 'Configuration Props',
      },
    },
    onClose: {
      control: { type: '() => {}' },
      description: 'Callback function which updates Bopis Inventory',
      table: {
        category: 'Notification Props',
      },
    },
    onDone: {
      control: { type: '(data: ProductColor, status: Status) => {}' },
      description: 'Add to bag callback function. This event is activated after the API call ends',
      table: {
        category: 'Notification Props',
      },
    },
    onOpen: {
      control: { type: '() => {}' },
      description: 'Callback function which reports the Change Store Modal opening to Tealium',
      table: {
        category: 'Notification Props',
      },
    },
    onSelectStore: {
      control: { type: '() => {}' },
      description: 'Callback function which updates Bopis Inventory',
      table: {
        category: 'Notification Props',
      },
    },
    openButtonClassName: {
      control: { type: 'string' },
      description: 'Class name for the button which opens the Change Store Modal',
      table: {
        category: 'Configuration Props',
      },
    },
    openModalStyle: {
      control: { type: 'string' },
      description: 'Defines Change Store Modal styles',
      table: {
        category: 'Behavioral Props',
      },
    },
    selectedStoreId: {
      control: { type: 'string' },
      description: 'Id of the selected store',
      table: {
        category: 'Configuration Props',
      },
    },
    showOptionalContent: {
      control: { type: 'boolean' },
      description: 'This event ',
      table: {
        category: 'OPTIONAL',
      },
    },
    sku: {
      control: { type: 'string' },
      description: 'Product identifier (Stock Keeping Unit) ',
      table: {
        category: 'Configuration Props',
      },
    },
    translation: {
      control: { type: 'object' },
      description: 'Returns the translation keys',
      table: {
        category: 'Behavioral Props',
      },
    },
    useStorage: {
      control: { type: 'boolean' },
      description: 'Controls if storage is enabled/disabled',
      table: {
        category: 'Behavioral Props',
      },
    },
    zipCode: {
      control: { type: 'string' },
      description: 'zipCode from the selected store',
      table: {
        category: 'Configuration Props',
      },
    },
  },
  args: {
    brand: Brands.Gap,
    curbsideEnabled: true,
    showOptionalContent: true,
    translation: LOCALIZATION_DATA.translations['en-US'].translation,
    useStorage: true,
  },
  component: ChangeStoreModal,
  parameters: {
    actions: {
      handles: ['onOpen'],
    },
    controls: {
      expanded: true,
    },
    docs: {
      page: Doc,
    },
    fetchMock: {
      mocks: [
        {
          headers: {},
          matcher: {
            url: /\/commerce\/locations\/stores/,
          },
          response: () => storeInfoMock,
        },
      ],
    },
    layout: 'centered',
  },
  title: 'Components/Change Store Modal',
} as ComponentMeta<typeof ChangeStoreModal>;

type ChangeStoreModalTemplateProps = {
  brand: Brands;
  showOptionalContent: boolean;
};

const Template = ({ brand, showOptionalContent }: ChangeStoreModalTemplateProps) => {
  return (
    <AppStateProvider
      value={{
        brandName: brand,
        contentType: 'ecom',
        country: 'us',
        env: 'stage',
        errorLogger: () => {},
        finalConfig: {
          brandCodeUrls: {
            us: {
              gap: {
                oidcUrl: 'https://api.gap.com',
              },
            },
          },
        },
        locale: 'en_US',
        market: 'us',
        pasConfig: {
          query: {
            size: 40,
          },
        },
        pid: 'pid',
      }}
    >
      <LocalizationProvider {...LOCALIZATION_DATA}>
        <StitchStyleProvider brand={brand}>
          <ChangeStoreModal
            container=".package-modal"
            curbsideEnabled
            id="128128983"
            LASConfig={{
              query: {
                size: 40,
              },
            }}
            onClose={action('onClose')}
            onDone={action('onDone')}
            onOpen={action('onOpen')}
            onSelectStore={action('onSelectStore')}
            openButtonClassName="your-custom-class"
            openModalStyle={false}
            optionalContentMock={() => console.log('it works')}
            PASConfig={{
              query: {
                size: 40,
              },
            }}
            selectedStoreId="150"
            showOptionalContent={showOptionalContent}
            sku="7602950022402"
            translation
            useStorage={false}
            zipCode="94016"
          >
            <div css={optionalContentStyles(showOptionalContent)}>
              <h1 css={textStyles}>This is an optional content. Check the documentation to know more.</h1>
            </div>
          </ChangeStoreModal>
        </StitchStyleProvider>
      </LocalizationProvider>
    </AppStateProvider>
  );
};

export const Default = Template.bind({});
