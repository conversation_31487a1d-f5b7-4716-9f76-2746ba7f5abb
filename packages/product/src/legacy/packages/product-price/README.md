# @product-page/legacy/product-price

# Product Price

A React component that contains components for rendering various price cases.

## Product Price Variations

### Live demos

More information and demos for ProductPrice can be found on our [Storybook page](https://product-page-storybook.apps.cfcommerce.dev.azeus.gaptech.com/?path=/story/pdp-storybook--page).

> To check all the price cases covered here, visit this [page](https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/price-case)

### 1 - Price component (a wrapper component that covers SimpleRangeDisplay, MarkdownPriceDisplay and BasePriceDisplay scenarios)

```jsx
import Price from '@product-page/legacy/product-price';
<Price
  price={ProductPrice}
  hoverPrice?={string}
  selectedPrice?={string}
/>
```

### Type:

```ts
type ProductPrice = {
  rawCurrentPrice: number;
  rawRegularPrice: number;
  localizedRegularPrice: string;
  localizedCurrentPrice: string;
  currencySymbol: string;
  percentageOff: string | number;
  priceType: number;
};
```

### Props

1. _price_

   **Description:** Price object containing different ranges; price cases will be displayed depending on the values received.

   **Required:** yes

2. _hoverPrice_

   **Type:** `string`

   **Description:** When the user hovers over a different color swatch, the displayed price is updated.

   **Required**: no

   **Default value:** ""

3. _selectedPrice_

   **Type**: `string`

   **Description:** It deals with the localized version of the main price displayed.

   **Required**: no

   **Default value:** ""

---

### 2 - BasePriceDisplay

```jsx
import { BasePriceDisplay } from "@product-page/legacy/product-price";
<BasePriceDisplay
  regularMinPrice={number}
  selectedPrice={string}
  hoverPrice={string}
/>;
```

### Props

1. _regularMinPrice_

   **Type**: `number`

   **Description:** If the selectedPrice is less than the regularMinPrice, the price is highlighted.

   **Required**: yes

2. _selectedPrice_

   **Type**: `string`

   **Description:** It deals with the localized version of the main price displayed.

   **Required**: yes

3. _hoverPrice_

   **Type**: `string`

   **Description:** When the user hovers over a different color swatch, the displayed price is updated.

   **Required**: yes

---

## 3 - ConditionalStrikethroughPrice

```jsx
import { ConditionalStrikethroughPrice } from '@product-page/legacy/product-price';
<ConditionalStrikethroughPrice
  brand={string, brand abbreviation}
  priceType={number}
  ariaLabel={string}
  priceDisplay={React node}
/>
```

### Props

1. _brand_

   **Type**: `string`

   **Description:** Brand abbreviation

   **Required**: yes

2. _priceType_

   **Type**: `number`

   **Description:** Checks if the price should be strikethrough. For GAPFS, AT, ON, and BR the price will be strikethrough for both types (2 and 3). For GAP and BRFS the price will be strikethrough only when the type is 2.

   **Required**: yes

3. _ariaLabel_

   **Type**: `string`

   **Description:** The value is used to provide a textual description for the ConditionalStrikethroughPrice price.

   **Required**: yes

4. _priceDisplay_

   **Type**: `ReactNode`

   **Description:** Price value to be interpolated

   **Required**: yes

5. _isBrColors2023Enabled_

   **Type**: `boolean`

   **Description:** It alters the colors of the component for BR brands depending on whether the flag is enabled.

   **Required**: no

   **Default value:** `false`

---

## 4 - MarkdownPriceDisplay

```jsx
import { MarkdownPriceDisplay } from '@product-page/legacy/product-price';
<MarkdownPriceDisplay
  classes?={string, custom css classes}
  ariaLabel={string}
  brand={string, brand abbreviation}
  conditionalStrikethrough?={boolean}
  maxPercentageOff={number}
  minPercentageOff={number}
  percentageOffText={string}
  priceDisplay={React node}
  priceType={number}
/>
```

### Props

1. _classes_

   **Type**: `string`

   **Description:** It enables customers to offer customized styling classes.

   **Required**: no

   **Default value:** `null`

2. _ariaLabel_

   **Type**: `string`

   **Description:** When applied, this value is used to provide a textual description for the ConditionalStrikethroughPrice value.

   **Required**: yes

3. _brand_

   **Type**: `string`

   **Description:** Brand abbreviation

   **Required**: yes

4. _conditionalStrikethrough_

   **Type**: `boolean`

   **Description:** Strikethrough is applied to the price.

   **Required**: no

   **Default value:** `false`

5. _maxPercentageOff_

   **Type**: `number`

   **Description:** The price's maximum percentage off range, consumed by PercentageOffText internally

   **Required**: yes

6. _minPercentageOff_

   **Type**: `number`

   **Description:** The price's minimum percentage off range, consumed by PercentageOffText internally

   **Required**: yes

7. _priceDisplay_

   **Type**: `ReactNode`

   **Description:** Price value to be interpolated

   **Required**: yes

8. _priceType_

   **Type**: `number`

   **Description:** Checks if the price should be strikethrough

   **Required**: yes

9. _percentageOffText_

   **Type**: `string`

   **Description:** The price's percentage off text, consumed by PercentageOffText internally

   **Required**: yes

10. _isBrColors2023Enabled_

    **Type**: `boolean`

    **Description:** It alters the colors of the component for BR brands depending on whether the flag is enabled.

    **Required**: no

    **Default value:** `false`

---

## 5 - PercentageOffText

```jsx
import { PercentageOffText } from "@product-page/legacy/product-price";
<PercentageOffText
  minPercentageOff={number}
  maxPercentageOff={number}
  percentageOffText={string}
/>;
```

### Props

1. _minPercentageOff_

   **Type**: `number`

   **Description:** The price's minimum percentage off range

   **Required**: yes

2. _maxPercentageOff_

   **Type**: `number`

   **Description:** The price's maximum percentage off range

   **Required**: yes

3. _percentageOffText_

   **Type**: `string`

   **Description:** Value to be interpolated

   **Required**: yes

4. _isBrColors2023Enabled_

   **Type**: `boolean`

   **Description:** It alters the colors of the component for BR brands depending on whether the flag is enabled.

   **Required**: no

   **Default value:** `false`

---

## 6 - SimplePriceDisplay

```jsx
import { SimplePriceDisplay } from '@product-page/legacy/product-price';
<SimplePriceDisplay
  children={React node}
  ariaLabel?={string}
  highlightSecondary={boolean}
  lineItem?={boolean}
/>
```

### Props

1. _children_

   **Type**: `ReactNode`

   **Description:** Value to be interpolated

   **Required**: yes

2. _ariaLabel_

   **Type**: `string`

   **Description:** When applied, this value is used to provide a textual description for the price container.

   **Required**: no

   **Default value:** `""`

3. _highlightSecondary_

   **Type**: `boolean`

   **Description:** When true, it applies custom styling.

   **Required**: yes

4. _lineItem_

   **Type**: `boolean`

   **Description:** It applies a custom line separator beneath the price.

   **Required**: no

   **Default value:** `false`

5. _isBrColors2023Enabled_

   **Type**: `boolean`

   **Description:** It alters the colors of the component for BR brands depending on whether the flag is enabled.

   **Required**: no

   **Default value:** `false`

---

## 7 - SimpleRangeDisplay

```jsx
import { SimpleRangeDisplay } from '@product-page/legacy/product-price';
<SimpleRangeDisplay
  children={React node}
  ariaLabel?={string}
/>
```

## Props

1. _children_

   **Type**: `ReactNode`

   **Description:** Value to be interpolated

   **Required**: yes

2. _ariaLabel_

   **Type**: `string`

   **Description:** When applied, this value is used to provide a textual description for the price container.

   **Required**: no

   **Default value:** `""`

---

### Dependencies

Price components are built for usage in MFE applications. In an effort to keep this package as small as possible, we have specified all `core-ui` and major dependencies as `peerDependencies`. You will be required to install them as needed. To view the list of required dependencies run:

```sh
$ npm info @product-page/legacy/product-price peerDependencies
```

For the React providers listed in `peerDependencies` it will be required that these are included in your main application entry component, above the `ProductPrice` component.

- [`AppStateProvider`][app-state-provider]
- [`LocalizationProvider`][localization-provider]
- [`ThemeProvider`][theme-provider]
- [`StitchStyleProvider`][stitch-style-provider]

```html
<AppStateProvider {...}>
  <LocalizationProvider {...}>
    <ThemeProvider {...}>
      <StitchStyleProvider {...}>
        <YourAppThatUsesProductPrice />
      </StitchStyleProvider>
    </ThemeProvider>
  </LocalizationProvider>
</AppStateProvider>
```

### Localization

This component requires that the following keys be present in your localization JSON files for each required language:

```
pdp.price.nowCurrentPrice
pdp.currencySymbol
pdp.price.percentageOff
pdp.price.regularPriceAriaLabel
pdp.price.currentPriceRangeAriaLabel
pdp.price.regularPriceRangeAriaLabel
```

PDP versions of these keys for English and French can be found at https://github.gapinc.com/ecomfrontend/product-page/tree/main/state-builder/static-data/i18n

### :notebook: Notes

**Style Deduplication:** This package relies on `@core-ui/react-stitch` (`emotion`) for styling. If you're app uses the basic server rendering that works automatically with `emotion`, you may need to add a [`CacheProvider`][cache-provider] to deduplicate injected style tags.
