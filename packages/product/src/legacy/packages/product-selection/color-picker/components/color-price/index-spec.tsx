// @ts-nocheck
/* eslint-disable react/display-name */
import { Brands } from '@ecom-next/core/legacy/utility';
import { render } from '@testing-library/react';
import React from 'react';

import type { ProductPriceStyleLevel } from '../../../../../types/product-data/style-level';
import { paramsExtraFlags, setupSnapshots } from '../../../../helpers/util/setup-snapshots';
import { TestApp } from '../../../index';
import { useColorPickerContext } from '../../context/use-color-picker-context';
import { ColorPrice } from '.';
import { PRICE_DATA } from './mocks/price-data';

jest.mock('../../context/use-color-picker-context');

const useColorPickerContextMock = (percentageOffConfig = { show: false, useFromCapi: false }): void => {
  (useColorPickerContext as jest.Mock).mockReturnValue({ percentageOffConfig });
};

const matchSnapshotsAllBrands = setupSnapshots();

const getColorPrice = (price: ProductPriceStyleLevel) => (brandName: Brands, enabledFeatures) => {
  return (
    <TestApp brandMarketCtxValue={{ brandName }} brandName={brandName} featureFlagsCtxValue={{ enabledFeatures }}>
      <ColorPrice brand={brandName} id="color-id" price={price} />
    </TestApp>
  );
};

const brandsWithoutON = [
  { brand: Brands.Athleta },
  { brand: Brands.BananaRepublic },
  { brand: Brands.BananaRepublicFactoryStore },
  { brand: Brands.Gap },
  { brand: Brands.GapFactoryStore },
];

const allBrands = [...brandsWithoutON, { brand: Brands.OldNavy }];

const renderColorPrice = (brandName, props = {}) => {
  return render(
    <TestApp
      brandName={brandName}
      translations={{
        'en-US': {
          translation: { 'pdp.price.percentageOff': '{{value}}% off' },
        },
      }}
    >
      <ColorPrice brand={brandName} id="color-id" {...props} />
    </TestApp>
  );
};

describe('<ColorPrice />', () => {
  beforeEach(() => {
    useColorPickerContextMock();
  });

  describe('Regular Price', () => {
    matchSnapshotsAllBrands(getColorPrice(PRICE_DATA.regularPrice), 'renders correctly', {
      flags: paramsExtraFlags.atRedesign2024,
    });
  });

  describe('Range Price', () => {
    matchSnapshotsAllBrands(getColorPrice(PRICE_DATA.range), 'renders correctly', {
      flags: paramsExtraFlags.atRedesign2024,
    });
  });

  describe('Old Range - New Range', () => {
    matchSnapshotsAllBrands(getColorPrice(PRICE_DATA.oldRangeNewRange), 'renders correctly', {
      flags: paramsExtraFlags.atRedesign2024,
    });
  });

  describe('Old Range - New Price', () => {
    matchSnapshotsAllBrands(getColorPrice(PRICE_DATA.oldRangeNewPrice), 'renders correctly', {
      flags: paramsExtraFlags.atRedesign2024,
    });
  });

  describe('Old Price - New Range', () => {
    matchSnapshotsAllBrands(getColorPrice(PRICE_DATA.oldPriceNewRange), 'renders correctly', {
      flags: paramsExtraFlags.atRedesign2024,
    });
  });

  describe('Old Price - New Price', () => {
    matchSnapshotsAllBrands(getColorPrice(PRICE_DATA.oldPriceNewPrice), 'renders correctly', {
      flags: paramsExtraFlags.atRedesign2024,
    });
  });

  describe('New Range - Same Max', () => {
    matchSnapshotsAllBrands(getColorPrice(PRICE_DATA.newRangeSameMax), 'renders correctly', {
      flags: paramsExtraFlags.atRedesign2024,
    });
  });

  describe('Percentage off config', () => {
    test('should render percentage off for ON when percentageOffConfig.show is true', () => {
      const props = {
        price: PRICE_DATA.oldPriceNewPrice,
      };
      useColorPickerContextMock({ show: true, useFromCapi: true });
      const { container } = renderColorPrice(Brands.OldNavy, props);
      const percentageOffElm = container.getElementsByClassName('product-price__percentage-off')[0];

      expect(percentageOffElm).toHaveTextContent('50% Off');
    });

    test.each(allBrands)(
      'should not display percentage off for $brand when percentageOffConfig.show is false',
      ({ brand }) => {
        const props = {
          price: PRICE_DATA.oldPriceNewPrice,
        };
        useColorPickerContextMock({ show: false, useFromCapi: true });
        const { container } = renderColorPrice(brand, props);
        const percentageOffElm = container.getElementsByClassName('product-price__percentage-off')[0];

        expect(percentageOffElm).toBeUndefined();
      }
    );
  });
});
