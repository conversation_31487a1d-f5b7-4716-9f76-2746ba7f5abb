// @ts-nocheck
'use client'

/***/
/***/
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { jsx } from "@ecom-next/core/react-stitch";
import { ErrorBoundary } from '@ecom-next/core/legacy/error-boundary';
import { Fragment, memo } from 'react';

import type { BackOrderMessage } from '../../types/back-order-message';
import type { ProductDimension, ProductDimensionItem } from '../../types/dimensions';
import type { OnHoverDimension, OnUpdateDimension } from '..';
import { useProductSelectionContext } from '../use-product-selection-context';
import { groupLabelStyles, titleStyles } from '../variant-group/brands-styles/gap.styles';
import { DimensionsList } from './components/dimension-list';
import { DimensionListWithSizeSampling } from './components/dimension-list-with-size-sampling';
import type { SizeSamplingCallBacksType } from './components/size-sampling/types/size-sampling-callbacks-type';
import { dimensionGroupStyles } from './helpers/brand-styles/dimension-group-styles/index.styles';
import { dimensionGroupThemeStyles } from './helpers/brand-styles/dimension-radio-container-styles/index.styles';
import { dimensionsStyles } from './helpers/brand-styles/dimensions-styles/index.styles';
import { notificationMessagingStyles } from './helpers/brand-styles/index.styles';

export const enum DimensionPickerMode {
  InStockOrBopisInStock = 0,
  InStock = 1,
  BopisInStock = 2,
}

export type DimensionPickerFeatureConfig = {
  isAtBuyBoxRedesign2024?: boolean;
  isBrBrfsRedesign?: boolean;
  isBrRedesign2024Ph2?: boolean;
  isBrfsRedesign2024Ph2?: boolean;
  isDropShipProduct: boolean;
  isGapBuyBoxRedesign2024?: boolean;
  sizeSamplingConfig?: SizeSamplingConfig;
};

export type SizeSamplingConfig = {
  backOrderMessage?: BackOrderMessage;
  enabledCategories: string;
  flag: boolean;
  minReviewThreshold: number;
  primaryCategoryId: string;
  reviewRatings: { review_count: number };
  segment: string;
  styleId: string;
};

type DimensionPickerProps = {
  errorLogger?: () => void;
  featureConfig?: DimensionPickerFeatureConfig;
  isSizeSamplingEnabled?: boolean;
  mode?: DimensionPickerMode;
  onHoverDimensionTrigger?: OnHoverDimension;
  onUpdateDimensionTrigger?: OnUpdateDimension;
  renderLowStockMessage?: JSX.Element;
  renderSizeGuideButton?: JSX.Element;
  sizeSamplingEvents?: SizeSamplingCallBacksType;
};

type GapDimensionGroupLabelProps = {
  label: string;
  renderSizeGuideButton?: JSX.Element;
};

const DimensionPickerMemoized = ({
  errorLogger = () => {},
  onHoverDimensionTrigger = () => {},
  onUpdateDimensionTrigger = () => {},
  featureConfig = {
    isAtBuyBoxRedesign2024: false,
    isBrBrfsRedesign: false,
    isDropShipProduct: false,
    isGapBuyBoxRedesign2024: false,
    sizeSamplingConfig: {
      backOrderMessage: { ariaLabelText: '', mainText: '' },
      enabledCategories: '',
      flag: false,
      minReviewThreshold: 0,
      primaryCategoryId: '',
      reviewRatings: { review_count: 0 },
      segment: '',
      styleId: '',
    },
  },
  isSizeSamplingEnabled = false,
  mode = DimensionPickerMode.InStockOrBopisInStock,
  sizeSamplingEvents = {},
  renderSizeGuideButton,
  renderLowStockMessage,
}: DimensionPickerProps): JSX.Element => {
  const { displayedDimensions, selectedVariant } = useProductSelectionContext();
  const { localize } = useLocalize();

  return (
    <Fragment>
      {displayedDimensions.map((dimension: ProductDimension): JSX.Element => {
        const defaulVariantTab = 1;
        const { dimensionGroupId, label, dimensions, selectedDimension } = dimension;
        const id = `variant-${selectedVariant?.id || defaulVariantTab}-${dimensionGroupId}`;
        const {
          isDropShipProduct,
          sizeSamplingConfig,
          isBrBrfsRedesign,
          isGapBuyBoxRedesign2024,
          isAtBuyBoxRedesign2024,
        } = featureConfig;
        const isOneSizeProduct = dimensions?.length === 1 && dimensionGroupId === 'sizeDimension1';
        const isLong =
          (isBrBrfsRedesign && dimensions?.some(dimension => dimension.name.length > 2)) ||
          (isGapBuyBoxRedesign2024 && dimensions?.some(dimension => dimension.name.length > 7));
        const isFirstDimensionGroup = dimensionGroupId === 'sizeDimension1';

        const DimensionGroupLabel = ({ label, renderSizeGuideButton }: GapDimensionGroupLabelProps) => {
          return (
            <div className="pdp-dimension__group-label-container" css={groupLabelStyles}>
              <span className="pdp-dimension__group-label" css={titleStyles}>
                {label}
              </span>

              {isFirstDimensionGroup && renderSizeGuideButton}
            </div>
          );
        };

        return (
          <ErrorBoundary
            key={dimensionGroupId}
            componentName="Dimension Group"
            errorComponent={
              <div
                className="messaging-notification messaging-notification--error"
                css={notificationMessagingStyles}
                role="alert"
              >
                {localize('pdp.dimensionPicker.errorMessage.generalError')}
              </div>
            }
            errorLogger={errorLogger}
          >
            <div
              aria-labelledby={`swatch-label--${dimensionGroupId}`}
              className="radio-container radio-container--dimension"
              css={dimensionGroupThemeStyles(isSizeSamplingEnabled, isDropShipProduct)}
              data-testid="dimension-group"
              role="radiogroup"
            >
              {isGapBuyBoxRedesign2024 && (
                <DimensionGroupLabel label={label} renderSizeGuideButton={renderSizeGuideButton} />
              )}
              {isBrBrfsRedesign ? (
                <div className="radio-container__wrapper">
                  <span className="radio-container__label" id={`swatch-label--${dimensionGroupId}`}>
                    {label}
                  </span>
                </div>
              ) : (
                !isGapBuyBoxRedesign2024 && (
                  <span className="radio-container__label" id={`swatch-label--${dimensionGroupId}`}>
                    {label}
                    <span aria-hidden="true">:</span>
                  </span>
                )
              )}

              <div className="dimensions" css={dimensionsStyles(isOneSizeProduct)(isLong)}>
                <div
                  className="dimensions-group"
                  css={dimensionGroupStyles(isOneSizeProduct, isLong)}
                  id={`${id}-dimensions-group`}
                >
                  {isSizeSamplingEnabled ? (
                    <DimensionListWithSizeSampling
                      dimensionGroupId={dimensionGroupId as string}
                      dimensions={dimensions as ProductDimensionItem[]}
                      featureConfig={{
                        isAtBuyBoxRedesign2024,
                        isBrBrfsRedesign,
                        sizeSamplingConfig: sizeSamplingConfig as SizeSamplingConfig,
                      }}
                      id={id}
                      isLong={isLong}
                      isOneSizeProduct={isOneSizeProduct}
                      label={label}
                      onHoverDimensionTrigger={onHoverDimensionTrigger}
                      onUpdateDimensionTrigger={onUpdateDimensionTrigger}
                      selectedDimension={selectedDimension as string}
                      sizeSamplingEvents={sizeSamplingEvents}
                    />
                  ) : (
                    <DimensionsList
                      dimensions={dimensions as ProductDimensionItem[]}
                      featureConfig={{
                        isAtBuyBoxRedesign2024,
                        isBrBrfsRedesign,
                      }}
                      id={id}
                      isLong={isLong}
                      isOneSizeProduct={isOneSizeProduct}
                      label={label}
                      mode={mode}
                      onHoverDimensionTrigger={onHoverDimensionTrigger}
                      onUpdateDimensionTrigger={onUpdateDimensionTrigger}
                      selectedDimension={selectedDimension as string}
                    />
                  )}

                  {isGapBuyBoxRedesign2024 && isFirstDimensionGroup && (
                    <div style={{ flex: '1 0 100%' }}>{renderLowStockMessage}</div>
                  )}
                </div>
              </div>
            </div>
          </ErrorBoundary>
        );
      })}
    </Fragment>
  );
};

export const DimensionPicker = memo(DimensionPickerMemoized);
