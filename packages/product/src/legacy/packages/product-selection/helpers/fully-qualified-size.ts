// @ts-nocheck
'use client'

import type { ProductDimension } from '../../types/dimensions';

export const isFullyQualifiedSize = (dimensions: ProductDimension[]): boolean =>
  dimensions.every(dimension => dimension.selectedDimension);
/**
 * Determines if the size a user has selected is fully qualified
 * (that is, if there are N possible size dimensions,
 * the user has selected a value for all N of them)
 * @param {Array} dimensions - Array of Objects defining the dimensions state
 * @returns {Boolean} - If a given size is fully qualified
 */
