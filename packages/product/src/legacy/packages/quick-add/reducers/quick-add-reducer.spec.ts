// @ts-nocheck
import extraMeasurements from '../story/extra-measurements';
import MultiDimensionsPSS from '../story/multi-dimensions.json';
import oneSizeProducts from '../story/one-size-product';
import singleDimensionPSS from '../story/single-dimension-product.json';
import { initQuickAddState, quickAddReducer } from './quick-add-reducer';

const initialState = {
  inProgress: false,
  outOfStockDimensions: {},
  quantity: 1,
  response: {},
  selectedDimensions: {},
  selectedSku: null,
  selectedVariant: MultiDimensionsPSS.variants[0],
  unselectedDimensions: '',
  variants: MultiDimensionsPSS.variants.map(variant => ({
    ...variant,
    dimensions: variant.dimensions.map(({ label, measurements }) => ({
      label,
      measurements: Object.assign([], measurements),
    })),
  })),
};

describe('initial state', () => {
  test('sets first variant as selected variant', () => {
    const state = initQuickAddState(singleDimensionPSS.variants);
    expect(state.selectedVariant).toMatchObject(singleDimensionPSS.variants[0]);
  });

  test('sets first quantity as selected quantity', () => {
    const state = initQuickAddState(singleDimensionPSS.variants);
    expect(state.quantity).toBe(1);
  });

  test('sets out of stock dimensions from first variant', () => {
    const state = initQuickAddState(singleDimensionPSS.variants);
    const outOfStockItems = singleDimensionPSS.variants[0].skus
      .filter(sku => !sku.inStock)
      .map(sku => sku.dimensions[0].measurement);

    expect(state.outOfStockDimensions).toMatchObject({ Size: new Set(outOfStockItems) });
  });

  test('sets selectedSku to null if any of the sku has empty dimensions', () => {
    const skusWithInvalidDimensions = {
      ...MultiDimensionsPSS.variants[0],
      skus: [...MultiDimensionsPSS.variants[0].skus],
    };

    // sets dimension of one of the sku to empty
    skusWithInvalidDimensions.skus[1].dimensions.length = 0;

    const state = initQuickAddState(MultiDimensionsPSS.variants);

    expect(state.selectedSku).toBeNull();
  });

  test('sets out of stock measurements for multi dimension variants if one of the dimension has only regular present', () => {
    const variants = [
      {
        dimensions: [
          {
            label: 'Size',
            measurements: ['0', '2'],
          },
          {
            label: 'Inseam',
            measurements: ['Regular'],
          },
        ],
        id: 1,
        inStock: true,
        name: 'Regular',
        skus: [
          {
            backOrderDate: null,
            dimensions: [
              {
                label: 'Size',
                measurement: '0',
              },
              {
                label: 'Inseam',
                measurement: 'Regular',
              },
            ],
            inStock: false,
            skuId: '4499470020001',
          },
          {
            backOrderDate: null,
            dimensions: [
              {
                label: 'Size',
                measurement: '2',
              },
              {
                label: 'Inseam',
                measurement: 'Regular',
              },
            ],
            inStock: true,
            skuId: '4499470020002',
          },
        ],
      },
    ];

    const state = initQuickAddState(variants);
    const totalOutOfStockSkus = variants[0].skus.filter(
      sku => !sku.inStock && sku.dimensions[1].measurement === 'Regular'
    );

    const expectedOOS = {
      Size: new Set(totalOutOfStockSkus.map(sku => sku.dimensions[0].measurement)),
    };

    expect(state.outOfStockDimensions).toStrictEqual(expectedOOS);
  });
});

describe('action: VARIANT_CHANGE', () => {
  test('selects a variant', () => {
    const state = quickAddReducer(initialState, {
      payload: { variant: MultiDimensionsPSS.variants[1] },
      type: 'VARIANT_CHANGE',
    });
    expect(state.selectedVariant).toStrictEqual(MultiDimensionsPSS.variants[1]);
  });

  test('selects a sku if all dimensions are selected', () => {
    const state = quickAddReducer(
      {
        ...initialState,
        selectedDimensions: {
          Size: '4',
        },
      },
      {
        payload: { variant: MultiDimensionsPSS.variants[1] },
        type: 'VARIANT_CHANGE',
      }
    );
    expect(state.selectedSku.skuId).toBe('4499470020410');
  });

  test('sets out of stock measurements for single dimension variants', () => {
    const initialState = initQuickAddState(singleDimensionPSS.variants);
    const selectedVariant = singleDimensionPSS.variants[1];
    const outOfStockItemLabel = selectedVariant.dimensions[0].label;
    const state = quickAddReducer(initialState, {
      payload: {
        variant: selectedVariant,
      },
      type: 'VARIANT_CHANGE',
    });

    const totalOutOfStockSkus = selectedVariant.skus
      .filter(({ inStock }) => !inStock)
      .map(sku => sku.dimensions[0].measurement);

    expect(state.outOfStockDimensions).toStrictEqual({
      [outOfStockItemLabel]: new Set(totalOutOfStockSkus),
    });
  });

  test('sets out of stock measurements for multi dimension variants if atleast one dimension is selected', () => {
    const selectedVariant = MultiDimensionsPSS.variants[0];
    const selectedDimensions = {
      Size: '2',
    };
    const totalOutOfStockSkus = selectedVariant.skus.filter(
      sku => !sku.inStock && sku.dimensions[0].measurement === '2'
    );

    const state = quickAddReducer(
      {
        ...initialState,
        selectedDimensions,
      },
      {
        payload: {
          variant: selectedVariant,
        },
        type: 'VARIANT_CHANGE',
      }
    );

    const expectedOOS = {
      Inseam: new Set(totalOutOfStockSkus.map(sku => sku.dimensions[1].measurement)),
    };

    expect(state.outOfStockDimensions).toStrictEqual(expectedOOS);
  });

  test('updates unselected dimension labels', () => {
    const selectedVariant = MultiDimensionsPSS.variants[0];
    const selectedDimensions = {
      Size: '2',
    };

    const state = quickAddReducer(
      {
        ...initialState,
        selectedDimensions,
        unselectedDimensions: 'size & inseam',
      },
      {
        payload: {
          variant: selectedVariant,
        },
        type: 'VARIANT_CHANGE',
      }
    );

    const expectedUnselectedLabels = 'inseam';

    expect(state.unselectedDimensions).toStrictEqual(expectedUnselectedLabels);
  });
});

describe('action: DIMENSION_CHANGE', () => {
  test('selects a dimension', () => {
    const state = quickAddReducer(initialState, {
      payload: {
        dimension: MultiDimensionsPSS.variants[0].dimensions[0],
        measurement: MultiDimensionsPSS.variants[0].dimensions[0].measurements[2],
      },
      type: 'DIMENSION_CHANGE',
    });
    const size4 = MultiDimensionsPSS.variants[0].dimensions[0].measurements[2];

    expect(state.selectedDimensions).toStrictEqual({ Size: size4 });
  });

  test('selects a sku if all dimensions are selected', () => {
    const state = quickAddReducer(
      {
        ...initialState,
        selectedDimensions: {
          Size: '4',
        },
      },
      {
        payload: {
          dimension: MultiDimensionsPSS.variants[0].dimensions[1],
          measurement: MultiDimensionsPSS.variants[0].dimensions[1].measurements[0],
        },
        type: 'DIMENSION_CHANGE',
      }
    );
    expect(state.selectedSku.skuId).toBe('4499470020401');
  });

  test('does not select a sku unless all dimensions are selected', () => {
    const state = quickAddReducer(initialState, {
      payload: {
        dimension: MultiDimensionsPSS.variants[0].dimensions[0],
        measurement: MultiDimensionsPSS.variants[0].dimensions[0].measurements[2],
      },
      type: 'DIMENSION_CHANGE',
    });
    expect(state.selectedSku).toBeNull();
  });

  test('sets out of stock dimensions', () => {
    const state = quickAddReducer(
      {
        ...initialState,
        selectedDimensions: {
          Size: '2',
        },
      },
      {
        payload: {
          dimension: MultiDimensionsPSS.variants[0].dimensions[1],
          measurement: MultiDimensionsPSS.variants[0].dimensions[1].measurements[1],
        },
        type: 'DIMENSION_CHANGE',
      }
    );

    const totalOutOfStockSkus = initialState.selectedVariant.skus.filter(
      sku => !sku.inStock && sku.dimensions[0].measurement === '2'
    );
    const outOfStockMeasurements = {
      Inseam: new Set(totalOutOfStockSkus.map(sku => sku.dimensions[1].measurement)),
      Size: new Set(totalOutOfStockSkus.map(sku => sku.dimensions[0].measurement)),
    };

    expect(state.outOfStockDimensions).toStrictEqual(outOfStockMeasurements);
  });

  test('returns out of stock dimensions when a size is selected', () => {
    const selectedVariant = MultiDimensionsPSS.variants[0];
    const selectedDimensions = MultiDimensionsPSS.variants[0].dimensions[1];
    const selectedMeasurement = selectedDimensions.measurements[2]; // Long

    const state = quickAddReducer(
      {
        ...initialState,
        selectedVariant,
      },
      {
        payload: {
          dimension: selectedDimensions,
          measurement: selectedMeasurement,
        },
        type: 'DIMENSION_CHANGE',
      }
    );

    expect(state.outOfStockDimensions).toStrictEqual({
      Size: new Set(['4']),
    });
  });

  test('shows only valid dimensions from the second set of dimensions if there are more than one level dimensions', () => {
    const selectedVariant = MultiDimensionsPSS.variants[0];
    const selectedDimensions = selectedVariant.dimensions[0]; // Size
    const selectedMeasurement = '20'; // Size: 20
    const validOtherDimensions = { label: 'Inseam', measurements: ['Regular'] };
    const state = quickAddReducer(
      {
        ...initialState,
        selectedDimensions: {},
        selectedVariant,
      },
      {
        payload: {
          dimension: selectedDimensions,
          measurement: selectedMeasurement,
        },
        type: 'DIMENSION_CHANGE',
      }
    );
    expect(state.selectedVariant.dimensions[1]).toStrictEqual(validOtherDimensions);
  });

  test('shows only valid dimensions from the opposite set of dimensions if there are more than one level dimensions', () => {
    const selectedVariant = MultiDimensionsPSS.variants[0];
    const selectedDimensions = selectedVariant.dimensions[1]; // Inseam
    const selectedMeasurement = selectedDimensions[2]; // Long

    const validSkusFromAboveSelection = MultiDimensionsPSS.variants[0].skus.filter(
      s =>
        s.dimensions.filter(d => d.label === selectedDimensions.label && d.measurement === selectedMeasurement).length
    );

    const validOppositeDimensions = {
      label: 'Size',
      measurements: validSkusFromAboveSelection.map(s => s.dimensions[0].measurement),
    };

    const state = quickAddReducer(
      {
        ...initialState,
        selectedDimensions: {},
        selectedVariant,
      },
      {
        payload: {
          dimension: selectedDimensions,
          measurement: selectedMeasurement,
        },
        type: 'DIMENSION_CHANGE',
      }
    );

    expect(state.selectedVariant.dimensions[0]).toStrictEqual(validOppositeDimensions);
  });
});

describe('OneSize Product', () => {
  test('should have dimension pre-selected if it is a one size product', () => {
    const defaultVariant = oneSizeProducts.oneSizeProductResponse.variants[0];
    const state = initQuickAddState(oneSizeProducts.oneSizeProductResponse.variants);
    const expectedSelectedDimension = {
      [defaultVariant.dimensions[0].label]: defaultVariant.dimensions[0].measurements[0],
    };

    expect(state.selectedDimensions).toMatchObject(expectedSelectedDimension);
  });

  test('pre-selectes size for multi dimension product with single measurement in each dimension', () => {
    const defaultVariant = oneSizeProducts.oneSizeMultiDimensionResponse.variants[0];
    const state = initQuickAddState(oneSizeProducts.oneSizeMultiDimensionResponse.variants);
    const expectedSelectedDimension = {
      [defaultVariant.dimensions[0].label]: defaultVariant.dimensions[0].measurements[0],
      [defaultVariant.dimensions[1].label]: defaultVariant.dimensions[1].measurements[0],
    };

    expect(state.selectedDimensions).toMatchObject(expectedSelectedDimension);
  });

  describe('action: ADD_TO_BAG_REQUEST', () => {
    test('returns with inProgress true for ADD_TO_BAG_REQUEST type', () => {
      expect(quickAddReducer(initialState, { type: 'ADD_TO_BAG_REQUEST' }).inProgress).toStrictEqual(true);
    });
  });

  describe('action: ADD_TO_BAG_RESPONSE', () => {
    const payload = {
      response: {
        status: 'SUCCESS',
      },
    };

    test('returns with inProgress false for ADD_TO_BAG_RESPONSE type', () => {
      expect(quickAddReducer(initialState, { payload, type: 'ADD_TO_BAG_RESPONSE' }).inProgress).toStrictEqual(false);
    });

    test('sets response from action payload if type matches ADD_TO_BAG_RESPONSE', () => {
      expect(quickAddReducer(initialState, { payload, type: 'ADD_TO_BAG_RESPONSE' }).response).toMatchObject(
        payload.response
      );
    });
  });
});

/**
 * context: https://gapinc.atlassian.net/browse/EVAL-2470
 */
describe('Product with extra measurement values', () => {
  test('marks the measurements that are missing from collection of skus as out of stock', () => {
    const state = initQuickAddState(extraMeasurements.variants);

    expect(state.outOfStockDimensions.Size.has('1X')).toBe(true);
    expect(state.outOfStockDimensions.Size.has('2X')).toBe(true);
    expect(state.outOfStockDimensions.Size.has('3X')).toBe(true);
  });
});

describe('action: QUANTITY_CHANGE', () => {
  test('selects a quantity', () => {
    const initialState = initQuickAddState(singleDimensionPSS.variants);
    const state = quickAddReducer(initialState, {
      payload: { quantity: 5 },
      type: 'QUANTITY_CHANGE',
    });
    expect(state.quantity).toBe(5);
  });
});
