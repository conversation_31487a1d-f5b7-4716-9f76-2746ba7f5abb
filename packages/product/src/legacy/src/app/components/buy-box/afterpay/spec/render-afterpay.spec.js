// @ts-nocheck
import TestApp from '@pdp/spec/test-app';
import { setupSnapshots } from '@pdp/packages/helpers/util/setup-snapshots';
import { fireEvent, render } from '@testing-library/react';
import React from 'react';

import afterpayStyles from '../afterpay.styles';
import { RenderAfterpay } from '../render-afterpay';
import { useAfterpayFeature } from '../use-afterpay';

const matchSnapshotsAllBrands = setupSnapshots();
jest.mock('../use-afterpay');
jest.mock('../../../../helpers/script-helper', () => () => ({
  addScriptToPage: ({ onload }) => {
    onload();
  },
  scriptAlreadyExists: () => false,
}));

const mockUseAfterpayFeature = (apEnabled, ppEnabled) => ({
  apEnabled,
  apThreshold: '0,',
  currencyCode: 'USD',
  currencySymbol: '$',
  locale: 'en_US',
  ppEnabled,
});

const afterpayClassMock = config => ({
  config,
  generateInstallments: jest.fn(),
  init: jest.fn(),
  modal: {},
});
const cacheAfterpayClass = global.presentAfterpay;

describe('RenderAfterpay', () => {
  afterAll(() => {
    global.presentAfterpay = cacheAfterpayClass;
  });

  matchSnapshotsAllBrands(brandName => {
    return (
      <TestApp brandName={brandName}>
        <div css={afterpayStyles} />
      </TestApp>
    );
  }, 'renders correctly');
  test('should render afterpay if apEnabled is true', () => {
    useAfterpayFeature.mockImplementation(() => mockUseAfterpayFeature(true, false));
    global.presentAfterpay = afterpayClassMock;

    const { container } = render(
      <TestApp>
        <RenderAfterpay price={1999} />
      </TestApp>
    );

    fireEvent.load(window);

    const apContainer = container.querySelector('.ap-paragraph');

    expect(apContainer).toHaveTextContent('pdp.afterpay.afterpayEligibleContent');
  });

  test('should render paypal if ppEnabled is true', () => {
    useAfterpayFeature.mockImplementation(() => mockUseAfterpayFeature(false, true));
    global.presentAfterpay = afterpayClassMock;

    const { container } = render(
      <TestApp>
        <RenderAfterpay apEnabled ppEnabled price={1999} />
      </TestApp>
    );

    fireEvent.load(window);

    const ppContainer = container.querySelector('#paypal-message-text');

    expect(ppContainer).toBeInTheDocument();
  });

  test('should render paypal and afterpay if ppEnabled and apEnabled are true', () => {
    useAfterpayFeature.mockImplementation(() => mockUseAfterpayFeature(true, true));
    global.presentAfterpay = afterpayClassMock;

    const { container } = render(
      <TestApp>
        <RenderAfterpay apEnabled ppEnabled price={1999} />
      </TestApp>
    );

    fireEvent.load(window);

    const ppContainer = container.querySelector('#paypal-message-text');
    const apContainer = container.querySelector('.ap-paragraph');

    expect(ppContainer).toBeInTheDocument();
    expect(apContainer).toHaveTextContent('pdp.afterpay.afterpayEligibleContent');
  });

  test('should not render afterpay if apEnabled is false', () => {
    useAfterpayFeature.mockImplementation(() => mockUseAfterpayFeature(false, true));
    global.presentAfterpay = afterpayClassMock;

    const { container } = render(
      <TestApp>
        <RenderAfterpay price={1999} />
      </TestApp>
    );

    fireEvent.load(window);

    const apContainer = container.querySelector('.ap-learn-more-link');
    expect(apContainer).toBeNull();
  });

  test('should not render paypal if ppEnabled is false', () => {
    useAfterpayFeature.mockImplementation(() => mockUseAfterpayFeature(true, false));
    global.presentAfterpay = afterpayClassMock;

    const { container } = render(
      <TestApp>
        <RenderAfterpay price={1999} />
      </TestApp>
    );

    fireEvent.load(window);

    const ppContainer = container.querySelector('.pp-learn-more-link');
    expect(ppContainer).toBeNull();
  });

  test('renders afterpay when onload is fired', () => {
    useAfterpayFeature.mockImplementation(() => mockUseAfterpayFeature(true, false));
    global.presentAfterpay = afterpayClassMock;

    const { container } = render(
      <TestApp>
        <RenderAfterpay price={1999} />
      </TestApp>
    );
    fireEvent.load(window);

    expect(container.querySelector('.ap-paragraph')).not.toBeNull();
  });

  test('render afterpay if the document readyState is complete', () => {
    useAfterpayFeature.mockImplementation(() => mockUseAfterpayFeature(true, false));
    global.presentAfterpay = afterpayClassMock;

    const { container } = render(
      <TestApp>
        <RenderAfterpay price={1999} />
      </TestApp>
    );

    expect(container.querySelector('.ap-paragraph')).not.toBeNull();
  });
});
