// @ts-nocheck
import { useAddToBagMediatorActions } from '@pdp/packages/add-to-bag/hooks/use-add-to-bag-mediator-actions';
import { act, renderHook } from '@testing-library/react-hooks';

import { useProtectedAddToBagRequest } from './use-protected-add-to-bag-request';

jest.mock('@pdp/packages/add-to-bag/hooks/use-add-to-bag-mediator-actions', () => ({
  useAddToBagMediatorActions: jest.fn(),
}));

describe('useProtectedAddToBagRequest', () => {
  const mockDisableAddToBag = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useAddToBagMediatorActions as jest.Mock).mockReturnValue({ disableAddToBag: mockDisableAddToBag });
  });

  describe('should prevent duplicate requests while add-to-bag button is still processing', () => {
    describe('when feature is enabled', () => {
      test('then should call method disabling add to bag button', () => {
        const setupProps = {
          featureConfig: { isProtectedAddToBagRequestEnabled: true },
          status: true,
        };

        const { result } = renderHook(() => useProtectedAddToBagRequest());

        act(() => {
          result.current.toggleAddToBagStatus(setupProps);
          expect(mockDisableAddToBag).toHaveBeenCalledWith(setupProps.status);
        });
      });

      test('and then after processing, should call method enabling add to bag button', () => {
        const setupProps = {
          featureConfig: { isProtectedAddToBagRequestEnabled: true },
          status: false,
        };

        const { result } = renderHook(() => useProtectedAddToBagRequest());

        act(() => {
          result.current.toggleAddToBagStatus(setupProps);
          expect(mockDisableAddToBag).toHaveBeenCalledWith(setupProps.status);
        });
      });
    });

    describe('when feature is disabled', () => {
      test('then should not call disableAddToBag method', () => {
        const { result } = renderHook(() => useProtectedAddToBagRequest());

        act(() => {
          result.current.toggleAddToBagStatus({
            featureConfig: { isProtectedAddToBagRequestEnabled: false },
            status: true,
          });
          const mockDisableAddToBag = jest.fn();
          (useAddToBagMediatorActions as jest.Mock).mockReturnValue({ disableAddToBag: mockDisableAddToBag });

          expect(mockDisableAddToBag).not.toHaveBeenCalled();
        });
      });
    });
  });
});
