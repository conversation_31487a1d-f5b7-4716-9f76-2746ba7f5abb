// @ts-nocheck
import { addPageAction } from '@ecom-next/core/reporting';
import type { ProductData } from '@pdp/types/product-data/style-level';
import { useProductSelectionContext } from '@product-page/legacy/product-selection';
import { act, renderHook } from '@testing-library/react-hooks';
import type { ReactNode } from 'react';
import React from 'react';

import { scrollToYPosition } from '../../../helpers/scroll-helper';
import { useAppState } from '../../../hooks/use-app-state';
import getProductImages from '../../photo-carousel/adapters/getImages';
import {
  DraprFitOnlyDrawerContext,
  DraprFitOnlyDrawerProvider,
} from '../drapr-fit-only-drawer/drapr-fit-only-drawer-provider';
import { useDrapr } from './use-drapr';
import { useDraprFitOnly } from './use-drapr-fit-only';
import { useDraprFitOnlyEvent } from './use-drapr-fit-only-event';
import { useDraprWidget } from './use-drapr-widget';

jest.useFakeTimers();

jest.mock('@ecom-next/sitewide/app-state-provider');
jest.mock('./use-drapr');
jest.mock('../../photo-carousel/adapters/getImages');
jest.mock('../../../helpers/scroll-helper');
jest.mock('./use-drapr-fit-only-event');
jest.mock('./use-drapr-widget');
jest.mock('@ecom-next/core/reporting');
jest.mock('@product-page/legacy/product-selection');

const useProductSelectionContextMock = () => {
  (useProductSelectionContext as jest.Mock).mockReturnValue({
    selectedColor: {
      colorName: 'green',
    },
    variants: [],
  });
};

describe('useDraprFitOnly Hook', () => {
  const productData: ProductData = {
    name: 'Mid Rise Denim Joggers',
    primaryCategoryId: '1',
    productImages: [],
    selectedColor: {},
    styleId: '2',
  };

  const mainImage = [
    {
      imageMapId: 'main',
      large: '/webcontent/0028/453/983/cn28453983.jpg',
      medium: '/webcontent/0028/454/032/cn28454032.png',
      small: '/webcontent/0028/454/017/cn28454017.jpg',
      thumbnail: '/webcontent/0028/454/020/cn28454020.jpg',
      xlarge: '/webcontent/0028/454/013/cn28454013.jpg',
    },
  ];

  const mockBridge = new EventTarget();
  mockBridge.fireEvent = mockBridge.dispatchEvent;

  const useDraprWidgetMock = (isFitOnly = true) => {
    (useDraprWidget as jest.Mock).mockReturnValue({ isFitOnly });
  };

  beforeAll(() => {
    global.window.draprViewerBridge = mockBridge;
    jest.spyOn(mockBridge, 'fireEvent').mockImplementation();
    (useDraprFitOnlyEvent as jest.Mock).mockReturnValue({
      recommendedSize: jest.fn(),
    });
    useProductSelectionContextMock();
  });

  const useAppStateMock = (brand = 'gap') => {
    (useAppState as jest.Mock).mockReturnValue({
      brandName: brand,
      productData,
    });
  };

  afterAll(() => {
    delete global.window.draprViewerBridge;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    (useDrapr as jest.Mock).mockReturnValue(true);
    useAppStateMock();
    (getProductImages as jest.Mock).mockReturnValue(mainImage);
    useDraprWidgetMock();
  });

  test('returns drapr #isOpen false by default', () => {
    const { result } = renderHook(() => useDraprFitOnly());

    expect(result.current.isOpen).toBe(false);
  });

  test('should fire draprlite-modal-initialized event when opening the drawer', () => {
    const { result } = renderHook(() => useDraprFitOnly());
    result.current.openDrawer('fitOnlyButtonDefault');

    expect(mockBridge.fireEvent).toHaveBeenCalledWith('draprlite-modal-initialized', {
      imageData: mainImage[0],
      title: productData.name,
    });
  });

  test('creates a new-relic pageAction when opening the drawer from default button', () => {
    const { result } = renderHook(() => useDraprFitOnly());
    result.current.openDrawer('fitOnlyButtonDefault');

    expect(addPageAction).toHaveBeenCalledWith('DraprFitDefaultCTAClick', {
      cid: '1',
      pid: '2',
    });
  });

  test('creates a new-relic pageAction when opening the drawer from widget', () => {
    const { result } = renderHook(() => useDraprFitOnly());
    result.current.openDrawer('fitOnlyButtonWidget');

    expect(addPageAction).toHaveBeenCalledWith('DraprFitWidgetCTAClick', {
      cid: '1',
      pid: '2',
    });
  });

  describe('#scrollToElement', () => {
    const wrapper = ({ children }: { children: ReactNode }) => (
      <DraprFitOnlyDrawerProvider>{children}</DraprFitOnlyDrawerProvider>
    );

    const mockScrollToHelper = jest.fn();

    beforeEach(() => {
      (scrollToYPosition as jest.Mock).mockImplementation(mockScrollToHelper);

      const anchorElement = document.createElement('div');
      anchorElement.classList.add('pdp-drapr-link-click');
      document.body.appendChild(anchorElement);
    });

    test('should call scrollToElement when the closeDrawer is called with the #notifyRecommendationGenerated', () => {
      const { result } = renderHook(() => useDraprFitOnly(), { wrapper });

      act(() => {
        result.current.openDrawer('fitOnlyButtonDefault');
      });

      act(() => {
        result.current.notifyRecommendationGeneratedSuccessfully('213');
      });

      act(() => {
        result.current.closeDrawer();
        jest.runAllTimers();
      });

      expect(mockScrollToHelper).toHaveBeenCalled();
    });

    test('should not call scrollToElement when the closeDrawer is without the #notifyRecommendationGenerated', () => {
      const { result } = renderHook(() => useDraprFitOnly(), { wrapper });

      act(() => {
        result.current.openDrawer('fitOnlyButtonDefault');
        jest.runAllTimers();
      });

      act(() => {
        result.current.closeDrawer();
      });

      expect(mockScrollToHelper).not.toHaveBeenCalled();
    });

    test('should not call scrollToElement when #notifyRecommendationGenerated but drawer never opened', () => {
      const { result } = renderHook(() => useDraprFitOnly(), { wrapper });
      act(() => {
        result.current.notifyRecommendationGeneratedSuccessfully('213');
        jest.runAllTimers();
      });

      expect(mockScrollToHelper).not.toHaveBeenCalled();
    });
  });

  describe('#displayFitOnly', () => {
    test.each([
      {
        draprScriptLoaded: true,
        isDraprFitOnlyErrored: false,
        isDrawerOpenedAtleastOnce: false,
        isFitOnly: false,
      },
      {
        draprScriptLoaded: true,
        isDraprFitOnlyErrored: true,
        isDrawerOpenedAtleastOnce: false,
        isFitOnly: true,
      },
      {
        draprScriptLoaded: false,
        isDraprFitOnlyErrored: false,
        isDrawerOpenedAtleastOnce: false,
        isFitOnly: true,
      },
      {
        draprScriptLoaded: true,
        isDraprFitOnlyErrored: true,
        isDrawerOpenedAtleastOnce: false,
        isFitOnly: true,
      },
    ])(
      'should return #displayFitOnly false when isFitOnly: $isFitOnly, isDraprFitOnlyErrored: $isDraprFitOnlyErrored, draprScriptLoaded: $draprScriptLoaded',
      ({ isFitOnly, isDraprFitOnlyErrored, draprScriptLoaded, isDrawerOpenedAtleastOnce }) => {
        useDraprWidgetMock(isFitOnly);

        const wrapper = ({ children }: { children: ReactNode }) => (
          <DraprFitOnlyDrawerContext.Provider
            value={{
              draprRecommendedSize: undefined,
              draprScriptLoaded,
              isDraprFitOnlyErrored,
              isDrawerOpenedAtleastOnce,
              isOpen: false,
              recommendationGenerated: false,
              setRecommendationGenerated: jest.fn(),
            }}
          >
            {children}
          </DraprFitOnlyDrawerContext.Provider>
        );

        const { result } = renderHook(() => useDraprFitOnly(), { wrapper });

        expect(result.current.displayFitOnly).toBeFalsy();
      }
    );

    test('should return #displayFitOnly true when all the required states have the correct value', () => {
      useDraprWidgetMock(true);

      const wrapper = ({ children }: { children: ReactNode }) => (
        <DraprFitOnlyDrawerContext.Provider
          value={{
            draprRecommendedSize: undefined,
            draprScriptLoaded: true,
            isDraprFitOnlyErrored: false,
            isOpen: false,
            recommendationGenerated: false,
            setRecommendationGenerated: jest.fn(),
          }}
        >
          {children}
        </DraprFitOnlyDrawerContext.Provider>
      );

      const { result } = renderHook(() => useDraprFitOnly(), { wrapper });

      expect(result.current.displayFitOnly).toBeTruthy();
    });

    test('should return #displayFitOnly true when drapr errored, but drawer was opened at least once', () => {
      useDraprWidgetMock(true);

      const wrapper = ({ children }: { children: ReactNode }) => (
        <DraprFitOnlyDrawerContext.Provider
          value={{
            draprRecommendedSize: undefined,
            draprScriptLoaded: true,
            isDraprFitOnlyErrored: true,
            isDrawerOpenedAtleastOnce: true,
            isOpen: false,
            recommendationGenerated: false,
            setRecommendationGenerated: jest.fn(),
          }}
        >
          {children}
        </DraprFitOnlyDrawerContext.Provider>
      );

      const { result } = renderHook(() => useDraprFitOnly(), { wrapper });

      expect(result.current.displayFitOnly).toBeTruthy();
    });
  });
});
