// @ts-nocheck
'use client'

import type { SerializedStyles, Theme } from "@ecom-next/core/react-stitch";
import { css, getFontWeight } from "@ecom-next/core/react-stitch";
import { setupRedesignStyles } from '@product-page/legacy/styles';
import { getGeneralColorByBrand, sdsBreakpoint, visuallyHide } from '@pdp/packages/styles/brand-styles/utils/util';

import { colorMap } from '../../../../../packages/styles/utility/mappings';
import {
  fulfillmentMethodStylesForBR as fulfillmentMethodRedesignStylesForBR,
  openModalStyleForBR as openModalRedesignStyleForBR,
} from '../../templates/br/components/fulfillment/brand-styles/br.styles';

export const fulfillmentMethodStylesForBR = (theme: Theme): SerializedStyles => {
  const { getTextColor } = getGeneralColorByBrand(theme);
  const setStyles = setupRedesignStyles(theme).isBrBrfsRedesign;

  const defaultStyles = css`
    margin-bottom: 1rem;

    * {
      ${theme.font.primary};
      font-size: 0.875rem;
      color: ${getTextColor(theme)};
    }

    .fulfillment-method {
      font-size: 0.875rem;
      padding: 0;
      position: relative;
      margin: 1.75rem 0;
      ${theme.font.primary};

      @media (min-width: ${sdsBreakpoint.medium}) and (min-aspect-ratio: 1 / 1), (min-width: ${sdsBreakpoint.xLarge}) {
        width: 100%;
      }
      .radio-container.checked {
        .fulfillment-method-pickup__status,
        .free-fast-shipping {
          ${getFontWeight('bold')};
        }
      }
    }

    .fulfillment-members-shipping-text {
      display: block;
      position: relative;
      text-align: center;
      margin-top: 1.5rem;
      margin-bottom: -2.312rem;
      z-index: 2;
      color: ${theme.color.g2};
      font-size: 0.75rem;
      letter-spacing: 1px;
    }

    #swatch-label--Fulfillment {
      ${visuallyHide()}
    }

    .bopis-label-wrapper {
      margin-left: calc(1.5em + 0.75rem);
    }

    .radio-container {
      margin-right: 0.375rem;
      position: relative;
      margin-bottom: 0;
    }

    .fulfillment-method-ship {
      display: flex;
    }

    .fulfillment-method-ship__info,
    .fulfillment-method-pickup__status {
      ${theme.font.secondary}
      line-height: 1.4;
      font-size: 0.875rem;
      ${getFontWeight('semiBold')};
      margin-top: -0.1rem;
      color: ${getTextColor(theme)};
      a {
        color: ${getTextColor(theme)};
        text-decoration: underline;
      }
    }

    .fulfillment-method-ship__info {
      ${getFontWeight('regular')};
    }

    .fulfillment-method-ship__status--free {
      text-transform: uppercase;
    }

    .fulfillment-method-ship__info--unavailable {
      opacity: ${theme.brLightUnavailableOpacity};
      color: ${getTextColor(theme)};
    }

    .fulfillment-method-ship__message-unavailable {
      opacity: ${theme.brLightUnavailableOpacity};
      color: ${getTextColor(theme)};
      ${getFontWeight('light')};
      letter-spacing: 0.5px;
    }

    .fulfillment-method-pickup__status > .fulfillment-method-pickup__price {
      font-size: 0.875rem;
      ${getFontWeight('regular')};
      text-transform: uppercase;

      @media (min-width: ${sdsBreakpoint.xLarge}) {
        font-size: 0.875rem;
      }
    }

    .fulfillment-method-pickup {
      border-bottom: 0;
      display: flex;
      flex-direction: column;
      margin-bottom: 1rem;
      padding-bottom: 0;
    }

    .fulfillment-method-pickup__info {
      min-width: 0;
    }

    .fulfillment-method-pickup__location {
      display: flex;
      margin-left: calc(1.5em + 0.75rem);
      color: ${getTextColor(theme)};

      button {
        padding: 0;
        border: 1px solid transparent;
        background: none;
        padding-bottom: 4px;

        &:hover {
          border-bottom: 1px solid ${colorMap.whiteCreame};
        }
      }
    }

    .fulfillment-method-pickup__no-selection {
      ${theme.font.secondary}
      font-size: 0.875rem;
      color: ${getTextColor(theme)};
      ${getFontWeight('regular')};
    }

    .fulfillment-method-pickup__store-link {
      border-bottom: 1px solid transparent;
      display: inline-flex;
      font-size: 0.875rem;
      letter-spacing: 0.5px;
      line-height: 0.875rem;
      margin-top: 0.125rem;
      min-width: 0;
      text-decoration: none;
      color: ${theme.color.inverse.b1};

      &:hover {
        text-decoration: none;
      }

      &.disabled {
        color: ${getTextColor(theme)};
        opacity: ${theme.brLightUnavailableOpacity};
        outline: none;
      }

      &.disabled:hover {
        cursor: default;
        border-bottom: 1px solid transparent;
      }
    }

    .fulfillment-method-pickup__info--unavailable {
      p,
      a {
        color: ${getTextColor(theme)};
        opacity: ${theme.brLightUnavailableOpacity};
      }
    }

    .fulfillment-method-pickup__store-name {
      margin-right: 0.4rem;
      margin-top: ${theme.isFulfillmentV2Enabled ? 0 : ''};
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .fulfillment-method-pickup__city-state {
      white-space: nowrap;
      margin-top: ${theme.isFulfillmentV2Enabled ? 0 : ''};
    }

    .fulfillment-method-pickup__notification {
      margin-top: 0.5rem;
      margin-bottom: 0.5rem;
    }

    .fulfillment-method-pickup__notification[aria-live='assertive'] {
      border-color: ${setStyles('', theme.color.r1)};
      span svg path {
        fill: ${setStyles('', theme.color.r1)};
      }
    }

    .fulfillment-v2__panel {
      border-top-style: none !important;
      border-bottom-style: none !important;
      margin: auto auto -1.875rem;
      background-color: transparent;
    }

    .fulfillment-v2__toggle {
      font-size: 0.875rem;
      ${getFontWeight('demiBold')};
      padding-top: 1.25rem;
      padding-right: 1.5rem;
      padding-bottom: 1.5rem;
      position: relative;
      margin: 1.25rem auto -1.5rem;
      max-width: 250px;
      text-align: center;
      text-transform: none;
      background: transparent;
      color: ${getTextColor(theme)};
      background-color: transparent;

      @media (max-width: ${sdsBreakpoint.small}) {
        max-width: 280px;
        margin: 1rem auto -1.5rem;
      }
    }
  `;

  return setStyles(fulfillmentMethodRedesignStylesForBR, defaultStyles);
};

export const openModalStyleForBR = (theme: Theme): SerializedStyles => {
  const { getTextColor } = getGeneralColorByBrand(theme);
  const setStyles = setupRedesignStyles(theme).isBrBrfsRedesign;

  const defaultStyles = css`
    text-decoration: underline;
    background: none;
    width: auto;
    margin: 0;
    padding: 0;
    text-align: inherit;
    font: inherit;
    ${getFontWeight('regular')};
    background-color: transparent;
    border: none;
    color: ${getTextColor(theme)};
    cursor: pointer;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
    line-height: 1.125rem;
    padding-left: 0;
    padding-right: 0;
    right: 0;
    text-transform: capitalize;
    top: 0;
    margin-left: calc(1.5em + 0.75rem);

    @media (min-width: ${sdsBreakpoint.xLarge}) {
      font-size: 0.875rem;
      line-height: 1.125rem;
    }

    @media (max-width: ${sdsBreakpoint.xLarge}) {
      margin-left: ${theme.isFulfillmentV2Enabled ? 'calc(1.8em + 0.5rem)' : 'calc(1.8em + 0.75rem)'};
    }
    &:hover {
      text-decoration: underline;
    }
  `;

  return setStyles(openModalRedesignStyleForBR, defaultStyles);
};
