// @ts-nocheck
'use client'

/* eslint-disable react-hooks/rules-of-hooks */
import { ModelSizeEnum, useMixedGrid, useModelSizeStorage } from '@ecom-next/core/legacy/model-size-storage';
import React, { createContext, useState } from 'react';

import { setURLParameter } from '../../helpers/url-helper';
import { useAppState } from '../../hooks/use-app-state';
import { useBrandAgnosticFeatureVariables } from '../../hooks/use-feature-flag';
import type { ModelSizeContextProps, ModelSizeProviderProps } from './model-size-types';

const modelSizeContextDefaultValues = {
  initializeModelSizeSelected: () => {},
  modelSizeSelected: 0,
  modelSizes: [],
  updateModelSizeSelected: () => {},
};

export const ModelSizeContext = createContext<ModelSizeContextProps>(modelSizeContextDefaultValues);

const modelSizeOptions = [ModelSizeEnum.SMALLEST, ModelSizeEnum.MEDIUM, ModelSizeEnum.LARGEST];

const storageValueIsInvalid = (storageValue?: string) => {
  return !storageValue || !modelSizeOptions.includes(storageValue as ModelSizeEnum);
};

const getInitialValue = (storageValue: string, defaultModelSizeIndex = 1): number => {
  const storageModelSizeIndex = modelSizeOptions.indexOf(storageValue as ModelSizeEnum);
  return storageModelSizeIndex >= 0 ? storageModelSizeIndex : defaultModelSizeIndex;
};

const setModelSizeURLParameter = (sizeIndex: number) => {
  setURLParameter({
    modelSize: modelSizeOptions[sizeIndex],
  });
};

export const ModelSizeProvider = ({
  children,
  modelSizes,
  preSelectedModelSize,
}: ModelSizeProviderProps): JSX.Element => {
  const isServerSide = typeof window === 'undefined';

  if (isServerSide) {
    const { updateModelSizeSelected, initializeModelSizeSelected } = modelSizeContextDefaultValues;
    const modelSizeIndex = modelSizeOptions.indexOf(preSelectedModelSize as ModelSizeEnum);

    return (
      <ModelSizeContext.Provider
        value={{
          initializeModelSizeSelected,
          modelSizeSelected: modelSizeIndex,
          modelSizes,
          updateModelSizeSelected,
        }}
      >
        {children}
      </ModelSizeContext.Provider>
    );
  }

  const { defaultSize } = useBrandAgnosticFeatureVariables('pdp-sizeinclusivity');
  const { brandName } = useAppState();
  const defaultModelSizeIndex = defaultSize?.[brandName] ? modelSizeOptions.indexOf(defaultSize[brandName]) : 1;

  const { setModelSizeSelected, getModelSizeSelected } = useModelSizeStorage();
  const { setMixedGrid } = useMixedGrid();
  const storageValue = preSelectedModelSize || getModelSizeSelected();
  const setModelSizeSelectedInLocalStorage = (newValue: number) => {
    setModelSizeSelected(modelSizeOptions[newValue]);
  };
  const initialModelSizeSelected = getInitialValue(storageValue, defaultModelSizeIndex);

  if (storageValueIsInvalid(storageValue)) {
    setModelSizeSelectedInLocalStorage(initialModelSizeSelected);
  }

  const [modelSizeSelected, setModelSizeSelectedInState] = useState(initialModelSizeSelected);

  const updateModelSizeSelected = (newValue: number) => {
    setMixedGrid(false);
    setModelSizeSelectedInState(newValue);
    setModelSizeSelectedInLocalStorage(newValue);
    setModelSizeURLParameter(newValue);
  };

  const initializeModelSizeSelected = (modelSize: ModelSizeEnum) => {
    const modelSizeIndex = modelSizeOptions.indexOf(modelSize);

    if (modelSize && modelSizeIndex >= 0 && modelSizeIndex < modelSizes.length) {
      updateModelSizeSelected(modelSizeIndex);
    } else {
      setModelSizeURLParameter(modelSizeSelected);
      setMixedGrid(false);
    }
  };

  return (
    <ModelSizeContext.Provider
      value={{ initializeModelSizeSelected, modelSizeSelected, modelSizes, updateModelSizeSelected }}
    >
      {children}
    </ModelSizeContext.Provider>
  );
};
