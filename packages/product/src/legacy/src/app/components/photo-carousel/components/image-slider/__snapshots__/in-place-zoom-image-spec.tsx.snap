// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<InPlaceZoomImage /> <ImageSlider/> snaps Renders In Place Zoom styles for at 1`] = `
<div
  style={
    {
      "fontFamily": "var(--font-phantom-sans),Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <div
    className="in-place-zoom-image-wrapper"
    css={[Function]}
  >
    <img
      className="pdp-in-place-zoom-image"
      css={[Function]}
      onClick={[Function]}
      onError={[Function]}
      onLoad={[Function]}
      onMouseDown={[Function]}
      src="xlarge.jpg"
    />
  </div>
</div>
`;

exports[`<InPlaceZoomImage /> <ImageSlider/> snaps Renders In Place Zoom styles for br 1`] = `
<div
  style={
    {
      "fontFamily": "var(--font-lynstone),Helvetica,Arial,sans-serif",
    }
  }
>
  <div
    className="in-place-zoom-image-wrapper"
    css={[Function]}
  >
    <img
      className="pdp-in-place-zoom-image"
      css={[Function]}
      onClick={[Function]}
      onError={[Function]}
      onLoad={[Function]}
      onMouseDown={[Function]}
      src="xlarge.jpg"
    />
  </div>
</div>
`;

exports[`<InPlaceZoomImage /> <ImageSlider/> snaps Renders In Place Zoom styles for brfs 1`] = `
<div
  style={
    {
      "fontFamily": "var(--font-lynstone),Helvetica,Arial,sans-serif",
    }
  }
>
  <div
    className="in-place-zoom-image-wrapper"
    css={[Function]}
  >
    <img
      className="pdp-in-place-zoom-image"
      css={[Function]}
      onClick={[Function]}
      onError={[Function]}
      onLoad={[Function]}
      onMouseDown={[Function]}
      src="xlarge.jpg"
    />
  </div>
</div>
`;

exports[`<InPlaceZoomImage /> <ImageSlider/> snaps Renders In Place Zoom styles for gap 1`] = `
<div
  style={
    {
      "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <div
    className="in-place-zoom-image-wrapper"
    css={[Function]}
  >
    <img
      className="pdp-in-place-zoom-image"
      css={[Function]}
      onClick={[Function]}
      onError={[Function]}
      onLoad={[Function]}
      onMouseDown={[Function]}
      src="xlarge.jpg"
    />
  </div>
</div>
`;

exports[`<InPlaceZoomImage /> <ImageSlider/> snaps Renders In Place Zoom styles for gapfs 1`] = `
<div
  style={
    {
      "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <div
    className="in-place-zoom-image-wrapper"
    css={[Function]}
  >
    <img
      className="pdp-in-place-zoom-image"
      css={[Function]}
      onClick={[Function]}
      onError={[Function]}
      onLoad={[Function]}
      onMouseDown={[Function]}
      src="xlarge.jpg"
    />
  </div>
</div>
`;

exports[`<InPlaceZoomImage /> <ImageSlider/> snaps Renders In Place Zoom styles for on 1`] = `
<div
  style={
    {
      "fontFamily": "var(--font-gotham),Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <div
    className="in-place-zoom-image-wrapper"
    css={[Function]}
  >
    <img
      className="pdp-in-place-zoom-image"
      css={[Function]}
      onClick={[Function]}
      onError={[Function]}
      onLoad={[Function]}
      onMouseDown={[Function]}
      src="xlarge.jpg"
    />
  </div>
</div>
`;

exports[`<InPlaceZoomImage /> renders correctly 1`] = `
<div
  style={
    {
      "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <div
    className="in-place-zoom-image-wrapper"
    css={[Function]}
  >
    <img
      className="pdp-in-place-zoom-image"
      css={[Function]}
      onClick={[Function]}
      onError={[Function]}
      onLoad={[Function]}
      onMouseDown={[Function]}
      src="xlarge.jpg"
    />
  </div>
</div>
`;
