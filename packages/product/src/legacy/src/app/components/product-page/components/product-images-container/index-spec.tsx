// @ts-nocheck
import TestApp, { defaultBrandMarketData } from '@pdp/spec/test-app';
import { setupSnapshots } from '@pdp/packages/helpers/util/setup-snapshots';
import { render } from '@testing-library/react';
import React, { Fragment } from 'react';

import { useSizeInclusivityFlag } from '../../../../hooks/use-size-inclusivity-flag';
import { ModelSizeContext } from '../../../model-size-provider';
import type { ModelSizeContextProps } from '../../../model-size-provider/model-size-types';
import PhotoBrick from '../../../photo-carousel/components/photo-brick';
import type { PhotoCarouselProps } from '../../../photo-carousel/index';
import PhotoCarousel from '../../../photo-carousel/index';
import ImagesMappingHelpers, { getActiveImages, isMappingValid } from './collaborators/images-mapping-helper';
import { images } from './data/images-mock';
import type { ProductImagesContainerProps } from './index';
import { ProductImagesContainer } from './index';
import { hoverZoomStyles } from './index.styles';

jest.mock('./collaborators/images-mapping-helper');
jest.mock('../../../photo-carousel');
jest.mock('../../../photo-carousel/components/photo-brick');
jest.mock('../../../../hooks/use-size-inclusivity-flag');
jest.mock('../../../model-size-selector', () => {
  return function ModelSizeSelector() {
    return <>ModelSizeSelector</>;
  };
});

jest.mock('react-universal-component', () => ({
  __esModule: true,
  default: (spec, options) => {
    const universal = jest.requireActual('react-universal-component').default; // Get original default behavior from the library.

    const UniversalComponent = universal(spec, options); // Call original function.
    UniversalComponent.preload(); // Preload component.

    return UniversalComponent; // Return loaded Universal Component.
  },
}));

const matchSnapshotsAllBrands = setupSnapshots();

const sizeInclusivityFlagEnabled = {
  'pdp-sizeinclusivity': true,
};

const replaceThirdImageByVideoFlag = (enabled = true) => ({
  'pdp-replace-third-image-with-video-us-on': enabled,
});

const sizeInclusivityFlagDisabled = {
  'pdp-replace-third-image-with-video-us-on': false,
  'pdp-sizeinclusivity': false,
};

const stylistVideosFlagEnabled = {
  'pdp-stylist-videos': true,
};

const stylistVideosFlagDisabled = {
  'pdp-stylist-videos': false,
};

const sizeInclusivitySegmentEnabled = {
  gap51: 'a',
  on51: 'a',
};

const stylistcVideosSegmentEnabled = {
  gap86: 'a',
  on86: 'a',
};

const disabledABSeg = {
  gap51: 'x',
  gap86: 'x',
  on51: 'x',
  on86: 'x',
};

const imagesMapping = {
  gap: {
    '8000': {
      largest: ['AV7', 'AV6', 'AV2', 'AV3'],
      medium: ['main', 'AV1', 'AV3', 'AV2'],
      smallest: ['AV4', 'AV5', 'AV3', 'AV2'],
    },
    default: {
      largest: ['AV6', 'AV7', 'AV2', 'AV3'],
      medium: ['main', 'AV1', 'AV2', 'AV3'],
      smallest: ['AV4', 'AV5', 'AV2', 'AV3'],
    },
  },
  on: {
    '8000': {
      largest: ['AV7', 'AV6', 'AV2', 'AV3'],
      medium: ['main', 'AV1', 'AV3', 'AV2'],
      smallest: ['AV4', 'AV5', 'AV3', 'AV2'],
    },
    default: {
      largest: ['AV6', 'AV7', 'AV2', 'AV3'],
      medium: ['main', 'AV1', 'AV2', 'AV3'],
      smallest: ['AV4', 'AV5', 'AV2', 'AV3'],
    },
  },
};

const featureVariables = {
  'pdp-sizeinclusivity': {
    imagesMapping,
  },
};

const breakpointLarge = {
  greaterOrEqualTo: () => true,
  size: 'large',
  smallerThan: () => false,
};

const breakpointSmall = {
  greaterOrEqualTo: () => false,
  size: 'small',
  smallerThan: () => true,
};

const defaultProps: ProductImagesContainerProps = {
  bricksLoaded: () => {},
  errorLogger: () => {},
  hoverImage: '',
  images,
  isPhotoBricksEnabled: true,
  mainImageLoaded: false,
  p1ImageLoaded: () => Promise.resolve(),
  productTitle: 'product-title',
  reporter: {
    reportAltImageClick: () => {},
    reportModelSizeSelectorLoad: jest.fn(),
    reportVideoPlayed: () => {},
    whenTealiumReadyOnce: jest.fn(),
  },
  selectedColorName: 'blue',
  showHoverImage: false,
};

const modelSizeCtxtDefaultValue: ModelSizeContextProps = {
  initializeModelSizeSelected: () => {},
  modelSizeSelected: 1,
  modelSizes: [{ height: '100', size: '100' }],
  updateModelSizeSelected: () => {},
};

const setup = (
  props = defaultProps,
  enabledFeatures: any,
  abSegValue: any,
  breakpointCtxValue = breakpointLarge,
  modelSizeCtxValue = modelSizeCtxtDefaultValue,
  testAppProps = {}
) =>
  render(
    <TestApp
      {...testAppProps}
      abSegValue={abSegValue}
      breakpointCtxValue={breakpointCtxValue}
      featureFlagsCtxValue={{ enabledFeatures, featureVariables }}
    >
      <ModelSizeContext.Provider value={modelSizeCtxValue}>
        <ProductImagesContainer {...props} />
      </ModelSizeContext.Provider>
    </TestApp>
  );

const getHoverZoomStyles = (abSegValue, brandName: Brands, enabledFeatures) => {
  return (
    <TestApp abSegValue={abSegValue} brand={brandName} featureFlagsCtxValue={{ enabledFeatures }}>
      <ModelSizeContext.Provider value={modelSizeCtxtDefaultValue}>
        <div css={hoverZoomStyles} />
      </ModelSizeContext.Provider>
    </TestApp>
  );
};
const useSizeInclusivityFlagMock = (sizeInclusivityEnabledValue = false): void => {
  (useSizeInclusivityFlag as jest.Mock).mockImplementation(() => ({
    imagesMapping,
    sizeInclusivityEnabled: sizeInclusivityEnabledValue,
  }));
};
function photoCarouselWithImagesCountMockSetup() {
  jest.mock('../../../photo-carousel', () => {
    return function PhotoCarousel() {
      return jest.fn();
    };
  });

  PhotoCarousel.mockImplementation((props: PhotoCarouselProps) => (
    <>PhotoCarousel images count: {props.images.length}</>
  ));
}

function photoCarouselDefaultMockSetup() {
  jest.mock('../../../photo-carousel', () => {
    return function PhotoCarousel() {
      return jest.fn();
    };
  });

  PhotoCarousel.mockImplementation(() => <>PhotoCarousel</>);
}

const textBasedOnShouldReorderImage = ['not reordering', 'reordering'];
const photoBrickReorderingText = `PhotoBrick - ${textBasedOnShouldReorderImage[1]}`;
function photoBrickSetup() {
  jest.mock('../../../photo-carousel/components/photo-brick', () => {
    return function PhotoBrick() {
      return jest.fn();
    };
  });

  PhotoBrick.mockImplementation((props: any) => {
    const { shouldReorderImages } = props;
    const text = shouldReorderImages ? textBasedOnShouldReorderImage[1] : textBasedOnShouldReorderImage[0];
    return <Fragment>PhotoBrick - {text}</Fragment>;
  });
}

describe('HoverZoom Styles Snapshots', () => {
  matchSnapshotsAllBrands(getHoverZoomStyles, 'renders correctly');
});

describe('<ProductImagesContainer />', () => {
  beforeEach(() => {
    (getActiveImages as jest.Mock).mockReturnValue(() => jest.fn());
    useSizeInclusivityFlagMock(true, imagesMapping);
    defaultProps.reporter.whenTealiumReadyOnce.mockReset();
    defaultProps.reporter.reportModelSizeSelectorLoad.mockReset();
    photoBrickSetup();
  });

  afterAll(() => {
    jest.resetModules();
  });

  afterEach(() => {
    jest.resetAllMocks();
    jest.clearAllMocks();
  });

  test('should render PhotoBrick component when isPhotoBricksEnabled is true and breakpoint is large', () => {
    const { getByText } = setup(defaultProps, sizeInclusivityFlagEnabled, disabledABSeg, breakpointLarge);
    expect(getByText(photoBrickReorderingText)).toBeInTheDocument();
  });

  test('should render PhotoCarousel component when isPhotoBricksEnabled is false and breakpoint is large', () => {
    photoCarouselDefaultMockSetup();

    const { getByText } = setup(
      { ...defaultProps, isPhotoBricksEnabled: false },
      sizeInclusivityFlagEnabled,
      disabledABSeg,
      breakpointLarge
    );
    expect(getByText('PhotoCarousel')).toBeInTheDocument();
  });

  test('should render PhotoCarousel component when isPhotoBricksEnabled is true and breakpoint is smaller than large', () => {
    photoCarouselDefaultMockSetup();

    const { getByText } = setup(defaultProps, sizeInclusivityFlagEnabled, disabledABSeg, breakpointSmall);
    expect(getByText('PhotoCarousel')).toBeInTheDocument();
  });

  test(`shouldn't call getActiveImages helper when the size inclusivity feature flag is enabled and the segment is disabled`, () => {
    setup(defaultProps, sizeInclusivityFlagEnabled, disabledABSeg, breakpointLarge);
    expect(getActiveImages).not.toHaveBeenCalled();
  });

  test('should send shouldReorderImages as true when new size inclusivity enabled and the images mapping is invalid', () => {
    jest.mock('./collaborators/images-mapping-helper', () => {
      return {
        isMappingValid: jest.fn(),
      };
    });

    isMappingValid.mockImplementation(() => false);

    photoCarouselDefaultMockSetup();

    const { container } = setup(defaultProps, sizeInclusivityFlagEnabled, sizeInclusivitySegmentEnabled);

    expect(isMappingValid).toHaveBeenCalled();
    expect(container.innerHTML.includes(photoBrickReorderingText)).toBeTruthy();
  });

  test('should send shouldReorderImages as true when there is no modelsizes for the product', () => {
    jest.mock('./collaborators/images-mapping-helper', () => {
      return {
        isMappingValid: jest.fn(),
      };
    });

    isMappingValid.mockImplementation(() => true);

    photoCarouselDefaultMockSetup();

    const { container } = setup(defaultProps, sizeInclusivityFlagEnabled, sizeInclusivitySegmentEnabled, undefined, {
      ...modelSizeCtxtDefaultValue,
      modelSizes: [],
    });

    expect(container.innerHTML.includes(photoBrickReorderingText)).toBeTruthy();
  });

  test('should call getActiveImages helper when the size inclusivity feature flag and segment are enabled and the images mapping is valid', () => {
    jest.mock('./collaborators/images-mapping-helper', () => {
      return {
        isMappingValid: jest.fn(),
      };
    });

    isMappingValid.mockImplementation(() => true);

    photoCarouselDefaultMockSetup();

    setup(defaultProps, sizeInclusivityFlagEnabled, sizeInclusivitySegmentEnabled);
    expect(getActiveImages).toHaveBeenCalled();
  });

  test('should not call getActiveImages helper when images mapping is not valid', () => {
    jest.mock('./collaborators/images-mapping-helper', () => {
      return {
        isMappingValid: jest.fn(),
      };
    });
    isMappingValid.mockImplementation(() => false);
    setup(defaultProps, sizeInclusivityFlagEnabled, sizeInclusivitySegmentEnabled);
    expect(getActiveImages).not.toHaveBeenCalled();
  });

  test('should remove AV9 images when pdp-stylist-videos is true and the abseg gap86 is a', () => {
    photoCarouselWithImagesCountMockSetup();

    const { getByText } = setup(
      { ...defaultProps, isPhotoBricksEnabled: false },
      stylistVideosFlagEnabled,
      stylistcVideosSegmentEnabled
    );

    const imagesLength = `PhotoCarousel images count: ${defaultProps.images.length - 1}`;
    expect(imagesLength).toStrictEqual(getByText(imagesLength).textContent);
  });

  test('should not remove AV9 images when pdp-stylist-videos is true and the abseg gap86 is x', () => {
    photoCarouselWithImagesCountMockSetup();

    const { getByText } = setup(
      { ...defaultProps, isPhotoBricksEnabled: false },
      stylistVideosFlagEnabled,
      disabledABSeg
    );

    const imagesLength = `PhotoCarousel images count: ${defaultProps.images.length}`;
    expect(imagesLength).toStrictEqual(getByText(imagesLength).textContent);
  });

  test('should not remove AV9 images when pdp-stylist-videos is false and the abseg gap86 is a', () => {
    photoCarouselWithImagesCountMockSetup();

    const { getByText } = setup(
      { ...defaultProps, isPhotoBricksEnabled: false },
      stylistVideosFlagDisabled,
      stylistcVideosSegmentEnabled
    );

    const imagesLength = `PhotoCarousel images count: ${defaultProps.images.length}`;
    expect(imagesLength).toStrictEqual(getByText(imagesLength).textContent);
  });

  test('should not remove AV9 images when pdp-stylist-videos is false and the abseg gap86 is x', () => {
    photoCarouselWithImagesCountMockSetup();

    const { getByText } = setup(
      { ...defaultProps, isPhotoBricksEnabled: false },
      sizeInclusivityFlagEnabled,
      disabledABSeg
    );

    const imagesLength = `PhotoCarousel images count: ${defaultProps.images.length}`;
    expect(imagesLength).toStrictEqual(getByText(imagesLength).textContent);
  });

  test('should call reporter.whenTealiumReadyOnce to track ModelSizeSelector when the new feature flag is enabled and the segment is enabled', () => {
    setup(defaultProps, sizeInclusivityFlagEnabled, sizeInclusivitySegmentEnabled);
    expect(defaultProps.reporter.whenTealiumReadyOnce).toHaveBeenCalledWith(
      'reportModelSizeSelectorLoad',
      defaultProps.reporter.reportModelSizeSelectorLoad
    );
  });

  test('should not call reporter.whenTealiumReadyOnce to track ModelSizeSelector when the new feature flag is disabled', () => {
    useSizeInclusivityFlagMock();
    setup(defaultProps, sizeInclusivityFlagDisabled, disabledABSeg);
    expect(defaultProps.reporter.whenTealiumReadyOnce).not.toHaveBeenCalled();
  });

  describe('ON - Photo Carousel Mobile - Size Inclusivity', () => {
    const testAppProps = (brandName = 'on') => {
      return {
        brandMarketCtxValue: {
          ...defaultBrandMarketData,
          brandName,
        },
      };
    };

    const isMappingValidMock = (isValid = true) => {
      jest.spyOn(ImagesMappingHelpers, 'isMappingValid').mockReturnValue(isValid);
    };

    test('calls image reorder when brand is ON, and size inclusivity and replace-third-image-with-video flag is enabled', () => {
      photoCarouselDefaultMockSetup();
      const getImagesWithImageReplacedByVideoMock = jest.spyOn(
        ImagesMappingHelpers,
        'getImagesWithImageReplacedByVideo'
      );
      isMappingValidMock();

      setup(
        defaultProps,
        sizeInclusivityFlagEnabled,
        sizeInclusivitySegmentEnabled,
        breakpointSmall,
        modelSizeCtxtDefaultValue,
        testAppProps()
      );
      expect(getImagesWithImageReplacedByVideoMock).toHaveBeenCalledTimes(1);
    });

    test('calls image reorder when brand is ON, and size inclusivity enabled but replace-third-image-with-video flag is disabled', () => {
      photoCarouselDefaultMockSetup();
      const getImagesWithImageReplacedByVideoMock = jest.spyOn(
        ImagesMappingHelpers,
        'getImagesWithImageReplacedByVideo'
      );
      isMappingValidMock();

      const featureFlagsEnabled = {
        sizeInclusivityFlagEnabled,
        ...replaceThirdImageByVideoFlag(true),
      };

      setup(
        defaultProps,
        featureFlagsEnabled,
        disabledABSeg,
        breakpointSmall,
        modelSizeCtxtDefaultValue,
        testAppProps()
      );
      expect(getImagesWithImageReplacedByVideoMock).toHaveBeenCalledTimes(1);
    });

    test('calls image reorder when brand is ON, and size inclusivity disabled but replace-third-image-with-video flag is enabled', () => {
      photoCarouselDefaultMockSetup();
      const getImagesWithImageReplacedByVideoMock = jest.spyOn(
        ImagesMappingHelpers,
        'getImagesWithImageReplacedByVideo'
      );
      jest.spyOn(ImagesMappingHelpers, 'isMappingValid').mockReturnValue(true);

      const featureFlagsState = {
        sizeInclusivityFlagDisabled,
        ...replaceThirdImageByVideoFlag(true),
      };
      const testAppProps = {
        brandMarketCtxValue: {
          ...defaultBrandMarketData,
          brandName: 'on',
        },
      };
      setup(
        defaultProps,
        featureFlagsState,
        sizeInclusivitySegmentEnabled,
        breakpointSmall,
        modelSizeCtxtDefaultValue,
        testAppProps
      );
      expect(getImagesWithImageReplacedByVideoMock).toHaveBeenCalledTimes(1);
    });

    test('should not call image reorder when brand is ON, and size inclusivity and replace-third-image-with-video flag are disabled', () => {
      photoCarouselDefaultMockSetup();
      const getImagesWithImageReplacedByVideoMock = jest.spyOn(
        ImagesMappingHelpers,
        'getImagesWithImageReplacedByVideo'
      );
      jest.spyOn(ImagesMappingHelpers, 'isMappingValid').mockReturnValue(false);

      const featureFlagsState = {
        sizeInclusivityFlagDisabled,
        ...replaceThirdImageByVideoFlag(false),
      };
      const testAppProps = {
        brandMarketCtxValue: {
          ...defaultBrandMarketData,
          brandName: 'on',
        },
      };
      setup(
        defaultProps,
        featureFlagsState,
        sizeInclusivitySegmentEnabled,
        breakpointSmall,
        modelSizeCtxtDefaultValue,
        testAppProps
      );
      expect(getImagesWithImageReplacedByVideoMock).not.toHaveBeenCalled();
    });

    test('should not call image reorder when brand is not ON', () => {
      photoCarouselDefaultMockSetup();
      const getImagesWithImageReplacedByVideoMock = jest.spyOn(
        ImagesMappingHelpers,
        'getImagesWithImageReplacedByVideo'
      );
      jest.spyOn(ImagesMappingHelpers, 'isMappingValid').mockReturnValue(true);

      const featureFlagsState = {
        sizeInclusivityFlagDisabled,
        ...replaceThirdImageByVideoFlag(true),
      };
      const testAppProps = {
        brandMarketCtxValue: {
          ...defaultBrandMarketData,
          brandName: 'gap',
        },
      };
      setup(
        defaultProps,
        featureFlagsState,
        sizeInclusivitySegmentEnabled,
        breakpointSmall,
        modelSizeCtxtDefaultValue,
        testAppProps
      );
      expect(getImagesWithImageReplacedByVideoMock).not.toHaveBeenCalled();
    });
  });
});
