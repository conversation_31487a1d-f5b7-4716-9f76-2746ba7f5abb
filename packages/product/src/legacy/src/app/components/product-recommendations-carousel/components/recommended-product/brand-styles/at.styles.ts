// @ts-nocheck
'use client'

import type { SerializedStyles, Theme } from "@ecom-next/core/react-stitch";
import { css } from "@ecom-next/core/react-stitch";
import { pdpPseudoFocus, sdsBreakpoint, sdsLabelA, setupRedesignStyles } from '@product-page/legacy/styles';

export const recommendedProductStylesForAT = (theme: Theme): SerializedStyles => {
  const getStyles = setupRedesignStyles(theme).isAtRedesign2024;
  return css`
    ${theme.font.primary}
    color: ${theme.color.gray60};
    line-height: 1.4;
    font-size: 0.8125rem;
    margin: 0.5rem;

    .recommended-product__text {
      font-size: ${getStyles('0.875rem', '0.8125rem')};
      color: ${theme.color.gray60};
      font-weight: 400;
      padding-top: 0.4rem;
      font-family: ${theme.font.primary};
      white-space: normal;
      text-transform: none;
    }

    .recommended-product__original-price {
      color: ${theme.color.g2};
      text-decoration: line-through;
    }

    .recommended-product__percent-off {
      color: #d72c32;
      margin-left: 5px;
      text-transform: lowercase;
    }

    .recommended-product__new-price {
      color: ${theme.color.r1};
    }

    .recommended-product__price {
      color: ${theme.color.g1};
      text-transform: none;
      font-size: ${getStyles('0.875rem', '0.9375rem')};
      font-weight: ${theme.fontWeight.light};

      @media (max-width: ${sdsBreakpoint.medium}) {
        font-size: ${getStyles('0.875rem', '0.8125rem')};
      }
    }

    .recommended-product__marketing-flag {
      ${sdsLabelA(theme)}
      font-weight: ${theme.fontWeight.bold};
      color: ${theme.color.g2};
      font-size: 0.8125rem;
      text-transform: none;
      white-space: normal;
    }

    .recommended-product__anchor {
      display: block;
      height: fit-content;
      width: 100%;

      ${pdpPseudoFocus(theme)}
    }
  `;
};

export const recommendedProductImageStylesForAT =
  (isGridEnabled?: boolean) =>
  (theme: Theme): SerializedStyles => {
    const gridStyles = css`
      position: relative;
      background-color: ${theme.color.gray05};
      width: 100%;
      height: 0;
      padding-bottom: ${133.333}%;

      .recommended-product__img-src {
        display: block;
        width: 100%;
        height: auto;
      }
    `;

    const defaultStyles = css`
      position: relative;
      background-color: ${theme.color.gray05};
      height: 0;
      padding-bottom: ${133.333}%;
    `;

    return isGridEnabled ? gridStyles : defaultStyles;
  };
