// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<ReviewsContainer /> drawerEnabled = false Renders PowerReviews for at Brand 1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
}

<div
  style={
    {
      "fontFamily": "var(--font-phantom-sans),Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <div
    css={[Function]}
  >
    <div
      className="pdp-customer-photos-widget"
    >
      <div
        className="pdp-customer-review-photos"
        id="pr-reviewImageDisplay"
      />
    </div>
    <div
      className="pdp-reviews-widget"
      tabIndex={0}
    >
      <div
        style={
          {
            "fontFamily": "var(--font-phantom-sans),Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
          }
        }
      >
        <h2
          css={[Function]}
          id="pdp-reviews-widget-header"
        >
          <span
            className="review-separator"
          >
            pdp.reviews.title.text
          </span>
        </h2>
      </div>
      <div
        css={[Function]}
        data-testid="place-holder-snapshot"
        style={
          {
            "overflow": "hidden",
            "padding": "1.5rem 0rem",
          }
        }
      >
        <div
          className="loader-placeholder emotion-0"
        />
      </div>
      <div
        css={[Function]}
      >
        <div
          className="pdp-review-snapshot"
          id="pr-reviewSnapshot"
        />
      </div>
      <div
        className="pdp-review-listing"
        id="pr-reviewList"
      />
    </div>
  </div>
</div>
`;

exports[`<ReviewsContainer /> drawerEnabled = false Renders PowerReviews for br Brand 1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
}

<div
  style={
    {
      "fontFamily": "var(--font-lynstone),Helvetica,Arial,sans-serif",
    }
  }
>
  <div
    css={[Function]}
  >
    <div
      className="pdp-customer-photos-widget"
    >
      <div
        className="pdp-customer-review-photos"
        id="pr-reviewImageDisplay"
      />
    </div>
    <div
      className="pdp-reviews-widget"
      tabIndex={0}
    >
      <div
        style={
          {
            "fontFamily": "var(--font-lynstone),Helvetica,Arial,sans-serif",
          }
        }
      >
        <h2
          css={[Function]}
          id="pdp-reviews-widget-header"
        >
          <span
            className="review-separator"
          >
            pdp.reviews.title.text
          </span>
        </h2>
      </div>
      <div
        css={[Function]}
        data-testid="place-holder-snapshot"
        style={
          {
            "overflow": "hidden",
            "padding": "1.5rem 0rem",
          }
        }
      >
        <div
          className="loader-placeholder emotion-0"
        />
      </div>
      <div
        css={[Function]}
      >
        <div
          className="pdp-review-snapshot"
          id="pr-reviewSnapshot"
        />
      </div>
      <div
        className="pdp-review-listing"
        id="pr-reviewList"
      />
    </div>
  </div>
</div>
`;

exports[`<ReviewsContainer /> drawerEnabled = false Renders PowerReviews for brfs Brand 1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
}

<div
  style={
    {
      "fontFamily": "var(--font-lynstone),Helvetica,Arial,sans-serif",
    }
  }
>
  <div
    css={[Function]}
  >
    <div
      className="pdp-customer-photos-widget"
    >
      <div
        className="pdp-customer-review-photos"
        id="pr-reviewImageDisplay"
      />
    </div>
    <div
      className="pdp-reviews-widget"
      tabIndex={0}
    >
      <div
        style={
          {
            "fontFamily": "var(--font-lynstone),Helvetica,Arial,sans-serif",
          }
        }
      >
        <h2
          css={[Function]}
          id="pdp-reviews-widget-header"
        >
          <span
            className="review-separator"
          >
            pdp.reviews.title.text
          </span>
        </h2>
      </div>
      <div
        css={[Function]}
        data-testid="place-holder-snapshot"
        style={
          {
            "overflow": "hidden",
            "padding": "1.5rem 0rem",
          }
        }
      >
        <div
          className="loader-placeholder emotion-0"
        />
      </div>
      <div
        css={[Function]}
      >
        <div
          className="pdp-review-snapshot"
          id="pr-reviewSnapshot"
        />
      </div>
      <div
        className="pdp-review-listing"
        id="pr-reviewList"
      />
    </div>
  </div>
</div>
`;

exports[`<ReviewsContainer /> drawerEnabled = false Renders PowerReviews for gap Brand 1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
}

<div
  style={
    {
      "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <div
    css={[Function]}
  >
    <div
      className="pdp-customer-photos-widget"
    >
      <div
        className="pdp-customer-review-photos"
        id="pr-reviewImageDisplay"
      />
    </div>
    <div
      className="pdp-reviews-widget"
      tabIndex={0}
    >
      <div
        style={
          {
            "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
          }
        }
      >
        <h2
          css={[Function]}
          id="pdp-reviews-widget-header"
        >
          <span
            className="review-separator"
          >
            pdp.reviews.title.text
          </span>
        </h2>
      </div>
      <div
        css={[Function]}
        data-testid="place-holder-snapshot"
        style={
          {
            "overflow": "hidden",
            "padding": "1.5rem 0rem",
          }
        }
      >
        <div
          className="loader-placeholder emotion-0"
        />
      </div>
      <div
        css={[Function]}
      >
        <div
          className="pdp-review-snapshot"
          id="pr-reviewSnapshot"
        />
      </div>
      <div
        className="pdp-review-listing"
        id="pr-reviewList"
      />
    </div>
  </div>
</div>
`;

exports[`<ReviewsContainer /> drawerEnabled = false Renders PowerReviews for gapfs Brand 1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
}

<div
  style={
    {
      "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <div
    css={[Function]}
  >
    <div
      className="pdp-customer-photos-widget"
    >
      <div
        className="pdp-customer-review-photos"
        id="pr-reviewImageDisplay"
      />
    </div>
    <div
      className="pdp-reviews-widget"
      tabIndex={0}
    >
      <div
        style={
          {
            "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
          }
        }
      >
        <h2
          css={[Function]}
          id="pdp-reviews-widget-header"
        >
          <span
            className="review-separator"
          >
            pdp.reviews.title.text
          </span>
        </h2>
      </div>
      <div
        css={[Function]}
        data-testid="place-holder-snapshot"
        style={
          {
            "overflow": "hidden",
            "padding": "1.5rem 0rem",
          }
        }
      >
        <div
          className="loader-placeholder emotion-0"
        />
      </div>
      <div
        css={[Function]}
      >
        <div
          className="pdp-review-snapshot"
          id="pr-reviewSnapshot"
        />
      </div>
      <div
        className="pdp-review-listing"
        id="pr-reviewList"
      />
    </div>
  </div>
</div>
`;

exports[`<ReviewsContainer /> drawerEnabled = false Renders PowerReviews for on Brand 1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
}

<div
  style={
    {
      "fontFamily": "var(--font-gotham),Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <div
    css={[Function]}
  >
    <div
      className="pdp-customer-photos-widget"
    >
      <div
        className="pdp-customer-review-photos"
        id="pr-reviewImageDisplay"
      />
    </div>
    <div
      className="pdp-reviews-widget"
      tabIndex={0}
    >
      <div
        style={
          {
            "fontFamily": "var(--font-gotham),Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
          }
        }
      >
        <h2
          css={[Function]}
          id="pdp-reviews-widget-header"
        >
          <span
            className="review-separator"
          >
            pdp.reviews.title.text
          </span>
        </h2>
      </div>
      <div
        css={[Function]}
        data-testid="place-holder-snapshot"
        style={
          {
            "overflow": "hidden",
            "padding": "1.5rem 0rem",
          }
        }
      >
        <div
          className="loader-placeholder emotion-0"
        />
      </div>
      <div
        css={[Function]}
      >
        <div
          className="pdp-review-snapshot"
          id="pr-reviewSnapshot"
        />
      </div>
      <div
        className="pdp-review-listing"
        id="pr-reviewList"
      />
    </div>
  </div>
</div>
`;

exports[`<ReviewsContainer /> drawerEnabled = true Renders PowerReviews for br Brand 1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
}

<div
  style={
    {
      "fontFamily": "var(--font-lynstone),Helvetica,Arial,sans-serif",
    }
  }
>
  <div
    css={[Function]}
  >
    <div
      className="pdp-customer-photos-widget"
    >
      <div
        className="pdp-customer-review-photos"
        id="pr-reviewImageDisplay"
      />
    </div>
    <div
      className="pdp-reviews-widget"
      tabIndex={0}
    >
      <div
        style={
          {
            "fontFamily": "var(--font-lynstone),Helvetica,Arial,sans-serif",
          }
        }
      >
        <h2
          css={[Function]}
          id="pdp-reviews-widget-header"
        >
          <span
            className="review-separator"
          >
            pdp.reviews.title.text
          </span>
        </h2>
      </div>
      <div
        css={[Function]}
        data-testid="place-holder-snapshot"
        style={
          {
            "overflow": "hidden",
            "padding": "1.5rem 0rem",
          }
        }
      >
        <div
          className="loader-placeholder emotion-0"
        />
      </div>
      <div
        css={[Function]}
      >
        <div
          className="pdp-review-snapshot"
          id="pr-reviewSnapshot"
        />
      </div>
      <div
        className="pdp-review-listing"
        id="pr-reviewList"
      />
    </div>
  </div>
</div>
`;

exports[`<ReviewsContainer /> drawerEnabled = true Renders PowerReviews for brfs Brand 1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
}

<div
  style={
    {
      "fontFamily": "var(--font-lynstone),Helvetica,Arial,sans-serif",
    }
  }
>
  <div
    css={[Function]}
  >
    <div
      className="pdp-customer-photos-widget"
    >
      <div
        className="pdp-customer-review-photos"
        id="pr-reviewImageDisplay"
      />
    </div>
    <div
      className="pdp-reviews-widget"
      tabIndex={0}
    >
      <div
        style={
          {
            "fontFamily": "var(--font-lynstone),Helvetica,Arial,sans-serif",
          }
        }
      >
        <h2
          css={[Function]}
          id="pdp-reviews-widget-header"
        >
          <span
            className="review-separator"
          >
            pdp.reviews.title.text
          </span>
        </h2>
      </div>
      <div
        css={[Function]}
        data-testid="place-holder-snapshot"
        style={
          {
            "overflow": "hidden",
            "padding": "1.5rem 0rem",
          }
        }
      >
        <div
          className="loader-placeholder emotion-0"
        />
      </div>
      <div
        css={[Function]}
      >
        <div
          className="pdp-review-snapshot"
          id="pr-reviewSnapshot"
        />
      </div>
      <div
        className="pdp-review-listing"
        id="pr-reviewList"
      />
    </div>
  </div>
</div>
`;

exports[`<ReviewsContainer /> drawerEnabled = true Renders PowerReviews for gap Brand 1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
}

<div
  style={
    {
      "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <div
    css={[Function]}
  >
    <div
      className="pdp-customer-photos-widget"
    >
      <div
        className="pdp-customer-review-photos"
        id="pr-reviewImageDisplay"
      />
    </div>
    <div
      className="pdp-reviews-widget"
      tabIndex={0}
    >
      <div
        style={
          {
            "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
          }
        }
      >
        <h2
          css={[Function]}
          id="pdp-reviews-widget-header"
        >
          <span
            className="review-separator"
          >
            pdp.reviews.title.text
          </span>
        </h2>
      </div>
      <div
        css={[Function]}
        data-testid="place-holder-snapshot"
        style={
          {
            "overflow": "hidden",
            "padding": "1.5rem 0rem",
          }
        }
      >
        <div
          className="loader-placeholder emotion-0"
        />
      </div>
      <div
        css={[Function]}
      >
        <div
          className="pdp-review-snapshot"
          id="pr-reviewSnapshot"
        />
      </div>
      <div
        className="pdp-review-listing"
        id="pr-reviewList"
      />
    </div>
  </div>
</div>
`;

exports[`<ReviewsContainer /> drawerEnabled = true Renders PowerReviews for gapfs Brand 1`] = `
@keyframes animation-0 {
  0%, 25% {
    -webkit-background-position: 100% 100%;
    background-position: 100% 100%;
  }

  90%, 100% {
    -webkit-background-position: 0 0;
    background-position: 0 0;
  }
}

.emotion-0 {
  background: linear-gradient(300.82deg, #D9D9D9 6.57%, #FBFAFA 51.24%, #D9D9D9 93.09%);
  position: relative;
  -webkit-background-size: 300% 100%;
  background-size: 300% 100%;
  -webkit-animation: animation-0 0.9s alternate 0s infinite ease-in-out;
  animation: animation-0 0.9s alternate 0s infinite ease-in-out;
}

<div
  style={
    {
      "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <div
    css={[Function]}
  >
    <div
      className="pdp-customer-photos-widget"
    >
      <div
        className="pdp-customer-review-photos"
        id="pr-reviewImageDisplay"
      />
    </div>
    <div
      className="pdp-reviews-widget"
      tabIndex={0}
    >
      <div
        style={
          {
            "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
          }
        }
      >
        <h2
          css={[Function]}
          id="pdp-reviews-widget-header"
        >
          <span
            className="review-separator"
          >
            pdp.reviews.title.text
          </span>
        </h2>
      </div>
      <div
        css={[Function]}
        data-testid="place-holder-snapshot"
        style={
          {
            "overflow": "hidden",
            "padding": "1.5rem 0rem",
          }
        }
      >
        <div
          className="loader-placeholder emotion-0"
        />
      </div>
      <div
        css={[Function]}
      >
        <div
          className="pdp-review-snapshot"
          id="pr-reviewSnapshot"
        />
      </div>
      <div
        className="pdp-review-listing"
        id="pr-reviewList"
      />
    </div>
  </div>
</div>
`;
