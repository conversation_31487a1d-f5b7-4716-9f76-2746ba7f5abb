// @ts-nocheck
'use client';

import type { Theme } from '@ecom-next/core/react-stitch';
import { css, getFontWeight } from '@ecom-next/core/react-stitch';
import type { InterpolationPrimitive } from '@emotion/serialize';
import { setupRedesignStyles } from '@product-page/legacy/styles';
import { getBrBackgroundColor } from '@product-page/legacy/styles/brand-styles/utils/br.styles';
import { sdsBreakpoint } from '@pdp/packages/styles/brand-styles/utils/util';
import { colorMap } from '@pdp/packages/styles/utility/mappings';

import { useFeatureFlag } from '../../../../../../hooks/use-feature-flag';

export const drawerReviewContainerStylesForBR = (theme: Theme, localize = null): InterpolationPrimitive => {
  const brSnapShotDarkGrayStar =
    'PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2IDBsLTMuNzc3IDExLjQ1OUgwbDkuODg5IDcuMDgxTDYuMTEgMzBsOS44OS03LjA4MkwyNS44ODggMzBsLTMuNzc4LTExLjQ2TDMyIDExLjQ2SDE5Ljc3N3oiIGZpbGw9IiMyQzI4MjQiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==';

  const brSnapShotLightGrayStar =
    'PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2IDBsLTMuNzc3IDExLjQ1OUgwbDkuODg5IDcuMDgxTDYuMTEgMzBsOS44OS03LjA4MkwyNS44ODggMzBsLTMuNzc4LTExLjQ2TDMyIDExLjQ2SDE5Ljc3N3oiIGZpbGw9IiNjY2NjIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=';

  const darkGrayStar =
    'PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCAzMiAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNi4wNjE2IDIxLjY2NjdMNi40MTgyMiAyOS44MzJMOS42MzY5OCAxOC4zMzMzTDAgMTEuMDAxM0wxMS42MTA3IDEwLjkxNzNMMTYuMDYxNiAwTDIwLjM4OTMgMTAuOTE3M0wzMiAxMS4wMDEzTDIyLjQ4NjMgMTguMzMzM0wyNS42OTg1IDMwTDE2LjA2MTYgMjEuNjY2N1oiIGZpbGw9IiM1NDUxNEMiLz4KPC9zdmc+Cg==';

  const lightGrayStar =
    'PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE2IDBsLTMuNzc3IDExLjQ1OUgwbDkuODg5IDcuMDgxTDYuMTEgMzBsOS44OS03LjA4MkwyNS44ODggMzBsLTMuNzc4LTExLjQ2TDMyIDExLjQ2SDE5Ljc3N3oiIGZpbGw9IiNFREVDRUMiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==';

  const brSnapShotDarkerGrayStar =
    'PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCAzMiAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNi4wNjE2IDIxLjY2NjdMNi40MTgyMiAyOS44MzJMOS42MzY5OCAxOC4zMzMzTDAgMTEuMDAxM0wxMS42MTA3IDEwLjkxNzNMMTYuMDYxNiAwTDIwLjM4OTMgMTAuOTE3M0wzMiAxMS4wMDEzTDIyLjQ4NjMgMTguMzMzM0wyNS42OTg1IDMwTDE2LjA2MTYgMjEuNjY2N1oiIGZpbGw9IiMyRDI5MjUiLz4KPC9zdmc+Cg==';

  const brDropdownCaretIcon =
    'PHN2ZyB3aWR0aD0iMTVweCIgaGVpZ2h0PSIxNXB4IiB2aWV3Qm94PSItOSAtMTAgNTIgNTIiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGc+PHBvbHlsaW5lIGNsYXNzPSJwci1jYXJldC1pY29uX19saW5lIiBmaWxsPSJub25lIiBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iNiIgcG9pbnRzPSIyMy43LDMxLjUgOC4zLDE2IDIzLjcsMC41ICI+PC9wb2x5bGluZT48L2c+PC9zdmc+';

  const showReviewSearchField = () => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const pdpReviewSearch = useFeatureFlag('pdp-review-search');
    return css`
      .pr-rd-main-header-search {
        ${!pdpReviewSearch ? 'display: none' : 'overflow: visible'}
      }
    `;
  };
  const textStyles = theme => {
    return css`
      ${theme.font.primary}
      font-size: 0.75rem !important;
      font-weight: normal;
      line-height: 1.38;
    `;
  };
  const reviewHeaderSearchField = (theme: Theme) => css`
    .pr-rd-search-container {
      .pr-rd-search-reviews-input {
        @media (max-width: 840px) {
          padding: 0px 5px 0 0 !important;
        }

        input {
          height: auto !important;
          background-color: ${getBrBackgroundColor(theme)};
          border-color: rgb(var(--pdp-color-tan-200)) !important;
          border-radius: 0px;
          ::placeholder {
            color: ${theme.color.g2};
          }
          :focus {
            box-shadow: none !important;
          }
        }

        input + button:focus {
          box-shadow: none !important;
        }

        input:focus + button span svg path {
          fill: white !important;
        }

        input:focus + button {
          border: none !important;
        }

        .pr-rd-search-reviews-icon-button {
          background-color: rgb(var(--pdp-color-tan-200)) !important;
          height: 100% !important;
          width: 3.5rem !important;
          svg {
            width: 13px;
            height: 13px;
          }
        }
      }
    }
  `;

  const setStyles = setupRedesignStyles(theme).isBrBrfsRedesign;

  return css`
    margin-top: -0.625rem;
    ${theme.font.primary}

    @media (min-width: ${sdsBreakpoint.xLarge}) {
      margin-top: -0.313rem;
    }

    background-color: ${getBrBackgroundColor(theme)} !important;

    h2 {
      span.review-separator {
        color: rgb(var(--pdp-color-black-1400));
        font-size: 1.25rem;
        font-weight: ${getFontWeight('regular').fontWeight};
        letter-spacing: 1px;
        margin-left: -0.5rem;
        &::before,
        &::after {
          border-top: 1px solid ${`${theme.color.inverse.b1}80`};
        }
      }
    }

    .pr-media-modal {
      .modal__body {
        max-height: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
        ::before {
          ${theme.font.primary}
          content: '${localize && localize('pdp.Reviews.rateReview')}';
          border-bottom: 1px ${colorMap.brBorderColor2023} solid;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 64px;
          align-content: center;
          text-align: center;
          font-size: 12px;
        }

        .button__close {
          z-index: 10004;
          margin-top: 20px;
          margin-right: 10px;

          width: 20px;
          height: 20px;

          .pr-cross-icon {
            width: 20px;
            height: 20px;
          }
        }

        > div {
          padding: 2rem;
        }
      }
    }

    .pr-star-v4.pr-star-v4-100-filled {
      background: url(data:image/svg+xml;base64,${brSnapShotDarkerGrayStar}) no-repeat;
    }

    .pr-star-v4.pr-star-v4-0-filled {
      background: url(data:image/svg+xml;base64,${lightGrayStar}) no-repeat;
    }

    .pr-star-v4.pr-star-v4-25-filled,
    .pr-star-v4.pr-star-v4-50-filled,
    .pr-star-v4.pr-star-v4-75-filled {
      background: none !important;
      padding: 0 !important;

      &::before,
      &::after {
        content: '';
        display: inline-block;
        height: 30px;
      }

      &::before {
        background: url(data:image/svg+xml;base64,${darkGrayStar}) no-repeat;
        background-size: 18px !important;
      }

      &::after {
        background: url(data:image/svg+xml;base64,${lightGrayStar}) no-repeat;
        background-size: 18px !important;
      }
    }

    .p-w-r .pr-content-collection-form .pr-media-modal .modal__body {
      visibility: visible;
      max-height: 90%;
      padding: 1rem 1.75rem;
      background-color: ${getBrBackgroundColor(theme)};
      ${setStyles('border-radius: 0;')}

      @media (min-width: ${sdsBreakpoint.medium}) {
        ::-webkit-scrollbar-thumb {
          background-color: ${theme.color.g4};
          border-radius: 5px;

          :hover {
            background-color: ${theme.color.g3};
          }
        }

        ::-webkit-scrollbar {
          background-color: transparent;
          width: 7px;
        }
      }

      .pr-rating-stars,
      .pr-star-v4.pr-star-v4-100-filled,
      .pr-star-v4.pr-star-v4-0-filled {
        background-color: ${getBrBackgroundColor(theme)};
      }

      .pr-star-v4.pr-star-v4-100-filled {
        background: url(data:image/svg+xml;base64,${brSnapShotDarkGrayStar}) 0px 0px / 32px 30px no-repeat !important;
      }

      .pr-star-v4.pr-star-v4-0-filled {
        background: url(data:image/svg+xml;base64,${brSnapShotLightGrayStar}) 0px 0px / 32px 30px no-repeat !important;
      }

      @media (min-width: 600px) {
        padding: 2rem;
      }
    }
    .p-w-r .pr-content-collection-form .pr-media-modal {
      .button__close .pr-cross-icon__line {
        stroke: ${theme.color.b2};
      }
      .button__close:hover .pr-cross-icon__line {
        stroke: ${theme.color.b2};
      }
    }
    .p-w-r .pr-rating-only_heading {
      color: ${theme.color.b2};
    }

    .p-w-r a {
      color: rgb(var(--pdp-color-black-1400));
    }

    .p-w-r :focus,
    .p-w-r a:focus {
      stroke: ${theme.color.b2};
    }

    .modal__body .p-w-r {
      ${theme.font.primary}
      .pr-rating-stars .pr-star-v4 {
        width: 2.5rem !important;
        height: 2.5rem !important;
      }

      .pr-header-required,
      .pr-rating-form-group .pr-control-label {
        position: absolute;
        height: 1px;
        width: 1px;
        overflow: hidden;
        clip: rect(0 0 0 0);
      }

      .pr-header {
        margin-top: 1.25rem;

        .pr-rating-stars {
          margin: 0;
        }

        .pr-header-table {
          position: relative;
          padding-top: 2rem;
        }

        .pr-table-cell {
          display: inline-block;
          max-width: 75%;

          @media (max-width: ${sdsBreakpoint.small}) {
            display: flex;
            padding-left: 0;
            padding-right: 0;
          }
        }

        .pr-header-product-img {
          width: auto;
        }

        .pr-header-title {
          font-size: 1.375rem;
          color: ${theme.color.b2};
          position: absolute;
          top: 0;
          left: 0;
          margin: 0px;
        }

        .pr-header-product-name {
          font-size: 1.125rem;
        }

        .pr-header-product-name a {
          color: ${theme.color.b2};

          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .pr-required-indicator {
        color: rgb(var(--color-red-500));
      }

      .pr-header-product-img img {
        margin-top: 10px;
        margin-left: -10px;
        max-width: 100px;
      }

      .pr-header-product-img img[alt*='Product Image Unavailable'] {
        margin-left: 0px;
      }

      #pr-war-form {
        ${theme.font.primary}
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto;
        grid-row-gap: 2rem;
        grid-column-gap: 1rem;

        input,
        textarea {
          font-size: 1rem;
        }

        @media (min-width: ${sdsBreakpoint.xLarge}) {
          input,
          textarea {
            font-size: 0.875rem;
          }
        }

        @media (min-width: 600px) {
          grid-row-gap: 3rem;
        }
      }

      .pr-control-label span {
        font-size: 12px;
        color: ${theme.color.b2};
      }

      .form-group.pr-rating-form-group .pr-rating-stars {
        margin-bottom: 0;
      }

      .form-group {
        position: relative;
        margin-bottom: 0;
        grid-column-start: 1;
        grid-column-end: 3;
        order: 3;
      }

      .pr-rating-form-group,
      .pr-headline-form-group,
      .pr-comments-form-group,
      .pr-name-form-group {
        order: 1;
      }

      .pr-email_collection-form-group {
        order: 2;
      }

      label,
      legend.pr-control-label {
        margin: 0 0 0.75rem;
        font-weight: 300;
      }

      .pr-star-v4.pr-star-v4-0-filled,
      .pr-star-v4.pr-star-v4-100-filled {
        background-size: 2.5rem 2.5rem;
        background-color: white;
      }

      .pr-star-v4,
      .pr-star-v4-0-filled,
      .pr-star-v4-100-filled {
        width: 2.5rem;
        height: 2.5rem;
        margin-right: 0.5rem;
        margin-bottom: 0;
      }

      .pr-file-input-btn-group {
        display: block;
      }

      .pr-footer {
        display: flex;
        flex-direction: column;
        border-top: none;
        padding-top: 0;
        text-transform: initial;
      }

      .pr-subscript {
        order: 2;
        text-align: center;
        color: ${theme.color.bk};
        font-size: 12px;
      }

      .pr-pull-right {
        order: 3;
      }

      .pr-btn-fileinput {
        color: ${theme.color.b2};
        padding: 0.75rem 0;
        text-align: center;
        font-size: 1rem;
        display: flex;
        text-transform: uppercase;
        justify-content: center;
        order: 1;
        border: 1px solid rgb(var(--pdp-color-tan-200));
        background-color: rgb(var(--pdp-color-tan-200));
        width: 234px;
        margin: 0 auto;
        height: 56px;
      }

      .pr-btn-fileinput:active:hover {
        color: ${theme.color.bk};
      }

      .pr-btn-fileinput:hover {
        background-color: rgb(var(--pdp-color-tan-200));
        color: ${theme.color.bk};
        border: none;
      }

      .pr-btn-fileinput:active:focus {
        background-color: rgb(var(--pdp-color-tan-200));
        color: ${theme.color.bk};
      }

      .pr-btn-fileinput svg {
        display: none;
      }

      .pr-btn-fileinput::before {
        content: url('data:image/svg+xml,<svg width="13" height="11" viewBox="0 0 11 10" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><rect x="0.5" width="10" height="10" fill="url(%23pattern0_5599_216029)"/><defs><pattern id="pattern0_5599_216029" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="%23image0_5599_216029" transform="scale(0.00195312)"/></pattern><image id="image0_5599_216029" width="512" height="512" xlink:href="data:image/png;base64,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"/></defs></svg>');
        margin-right: 0.5rem;
        margin-top: 0.125rem;
      }

      .pr-btn-fileinput:hover::before {
        content: url('data:image/svg+xml,<svg width="13" height="11" viewBox="0 0 11 10" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><rect x="0.5" width="10" height="10" fill="url(%23pattern0_5599_216029)"/><defs><pattern id="pattern0_5599_216029" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="%23image0_5599_216029" transform="scale(0.00195312)"/></pattern><image id="image0_5599_216029" width="512" height="512" xlink:href="data:image/png;base64,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"/></defs></svg>');
      }

      .pr-file-input-btn-group .pr-file-input-label {
        font-size: 1.125rem;
        display: none;
      }

      .pr-accessible-btn {
        background-color: rgb(var(--pdp-color-tan-200));
        color: ${theme.color.bk};
        font-weight: 400;
        border: none;
        color: ${theme.color.b1};
        width: 100%;
        padding: 1rem 0;
        margin: 0.5rem auto 2rem;
        text-transform: uppercase;
      }

      .pr-accessible-btn:hover {
        background-color: none;
        color: none;
      }

      .pr-accessible-btn:focus {
        color: none;
      }

      .pr-accessible-btn:focus,
      a:active:hover {
        background-color: rgb(var(--pdp-color-tan-200));
        color: ${theme.color.bk};
        border-color: transparent;
      }

      .form-group.pr-height-form-group {
        margin-top: 1rem;
        grid-column-start: 1;
        grid-column-end: 2;
      }

      .form-group.pr-weight-form-group {
        margin-top: 1rem;
        grid-column-start: 2;
        grid-column-end: 3;
      }

      #pr-height-input,
      #pr-weight-input {
        color: ${theme.color.b2};
      }

      #pr-lengthpurchased-input {
        max-width: 100%;
      }

      .pr-width-form-group {
        position: relative;
      }

      .pr-clear-all-radios {
        position: absolute;
        bottom: 50px;
        color: ${theme.color.b2};
        right: 0;
      }

      .btn-group-radio-horizontal-linked {
        position: relative;
        margin-top: 0;
        display: flex;

        &::before {
          content: '';
          position: absolute;
          width: 100%;
          height: 3px;
          top: 50%;
          transform: translateY(-50%);
          background-color: ${theme.color.gray10};
        }

        .pr-btn {
          position: relative;
          margin: 0;
          padding: 38px 0 0;
          white-space: normal;
          text-align: center;
          text-transform: capitalize;
          height: 26px;
          background-color: transparent;
          border: 0;
          text-transform: capitalize;
        }

        .pr-btn::before {
          content: '';
          position: absolute;
          top: 50%;
          height: 1.25rem;
          width: 1.25rem;
          background-color: ${theme.color.wh};
          border: 2px solid ${theme.color.gray54};
          border-radius: 50%;
        }

        .pr-btn:nth-of-type(1) {
          text-align: left;
          color: ${theme.color.b2};

          &::before {
            left: 0;
            transform: translate(0, -50%);
          }
        }

        .pr-btn:nth-of-type(2) {
          text-align: center;
          color: ${theme.color.b2};

          &::before {
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }

        .pr-btn:nth-of-type(3) {
          text-align: right;
          color: ${theme.color.b2};

          &::before {
            right: 0;
            transform: translate(0, -50%);
          }
        }

        .pr-btn:hover {
          color: ${theme.color.g1};

          &::before {
            border-color: ${theme.color.gray54};
            background-color: ${theme.color.gray54};
          }
        }

        .pr-btn.active {
          color: ${theme.color.g1};
          background-color: inherit;
        }

        .pr-btn.active::before,
        .pr-btn.active:hover::before {
          border-color: ${theme.color.b2};
          background-color: ${theme.color.b2};
        }

        input.focus-visible + label {
          outline: none;
          box-shadow: 0 0 0 0 !important;
        }

        input[type='radio']:focus,
        input[type='radio']:focus + label {
          outline: none;
        }

        input[type='radio'].focus-visible + label::before {
          outline: 0;
          box-shadow: 0 0 0 3px ${theme.crossBrand.color.b2};
        }
      }

      button.focus-visible,
      a.focus-visible,
      .form-control:focus {
        border-color: inherit;
        outline: 0;
        box-shadow: 0 0 0 3px ${theme.crossBrand.color.b2};
      }

      .pr-facebook-btn,
      .pr-instagram-btn,
      .pr-media_videourl-form-group,
      .form-group.pr-overallquality-form-group,
      .pr-bottomline-form-group,
      .pr-pros-form-group,
      .pr-cons-form-group,
      .pr-describeyourself-form-group,
      .pr-bestuses-form-group,
      .pr-location-form-group {
        display: none;
      }
    }

    #pr-review-display {
      margin: 0;
    }

    .pr-rd-main-header {
      padding: 0 !important;

      .pr-rd-review-header-contents,
      .pr-rd-main-header-search-sort {
        .pr-rd-review-header-sorts:before,
        &:before {
          text-transform: uppercase !important;
          font-size: 0.75rem !important;
          color: ${theme.color.b1} !important;
          margin-bottom: 0.75rem;
        }
        @media (max-width: ${sdsBreakpoint.small}) {
          .pr-rd-review-header-sorts:before {
            margin-bottom: 2rem !important;
          }
          &:before {
            margin-bottom: 1.25rem !important;
          }
        }

        #pr-rd-sort-by,
        .pr-rd-search-reviews-input input,
        .pr-multiselect button {
          padding: 1.25rem 1.5rem !important;
          border-color: ${colorMap.brBorderColor2023} !important;
          font-size: 0.75rem !important;
          text-transform: uppercase !important;
          color: rgb(var(--pdp-color-gray-1000)) !important;
          border-radius: ${setStyles('0 !important')};
        }

        .pr-rd-search-reviews-input input {
          line-height: 1.2rem;
          &:focus {
            font-size: 1rem !important;
          }
        }

        @media (min-width: ${sdsBreakpoint.xLarge}) {
          .pr-rd-search-reviews-input input {
            &:focus {
              font-size: 0.75rem !important;
            }
          }
        }

        .pr-rd-main-header-search {
          height: auto !important;
          padding-top: 0 !important;
        }
        .pr-multiselect-button-label,
        .pr-rd-main-header .pr-rd-review-header-contents .pr-multiselect .pr-multiselect-button .pr-multiselect-button-label {
          text-transform: uppercase;
        }
        .pr-rd-search-container .pr-rd-search-reviews-input input {
          height: auto !important;
          border-color: ${colorMap.brBorderColor2023} !important;
        }
        .pr-rd-review-header-sorts.pr-rd-main-header-sorts-w-search {
          margin-bottom: 0.75rem;
        }

        .pr-rd-search-container .pr-rd-search-reviews-input .pr-rd-search-reviews-icon-button {
          background-color: rgb(var(--pdp-color-tan-200)) !important;
          border-color: ${colorMap.brBorderColor2023} !important;
          .pr-search-icon {
            transform: scaleX(-1) !important;
            svg {
              path {
                fill: rgb(var(--pdp-color-gray-1000)) !important;
              }
            }
          }
        }
      }

      .pr-rd-review-total {
        text-transform: uppercase !important;
        font-size: 0.75rem !important;
        color: ${theme.color.b1} !important;
        padding-bottom: 0.75rem !important;
        margin-bottom: 3rem !important;
        border-bottom: 1px solid ${colorMap.brBorderColor2023} !important;
        font-weight: normal !important;
        width: 100%;
      }
      .pr-rd-main-header-search-sort {
        margin-bottom: 2rem;
        padding: 0 !important;
      }
      .pr-rd-review-header-contents {
        padding: 0 !important;
        &:before {
          margin-bottom: 0;
        }
        gap: 0.75rem !important;
      }

      display: flex;
      flex-wrap: wrap;
      margin-bottom: 0 !important;

      > div:first-child {
        width: 100% !important;
      }

      span + div {
        order: 2;
        width: 100%;
      }

      .pr-rd-review-header-contents {
        display: none;
        flex-wrap: wrap;
        justify-content: space-between;
        order: 3;
        flex-grow: 1;
        padding: 0 !important;
        margin-bottom: 3rem !important;
        border: none !important;

        &:before {
          content: '${localize('pdp.Reviews.list.filterBy')}' !important;
          text-align: left !important;
          padding-left: 5px;
          display: table !important;
        }

        @media (max-width: ${sdsBreakpoint.small}) {
          &:before {
            font-size: 17px;
            padding-left: 0;
          }
        }

        .pr-multiselect {
          flex-grow: 1 !important;
          margin-right: 0 !important;
          padding: 0 5px;

          @media (max-width: ${sdsBreakpoint.small}) {
            margin-right: 1px !important;
            padding: 0;
          }

          .pr-multiselect-button {
            width: 100% !important;
            border-color: rgb(var(--pdp-color-black-1400));
            .pr-multiselect-button-label {
              color: rgb(var(--pdp-color-gray-1000)) !important;
              padding-right: 18px;
              font-size: 0.75rem;
              font-weight: 350;

              @media (max-width: ${sdsBreakpoint.small}) {
                font-size: 16px;
              }
            }
            .pr-caret-icon {
              .pr-caret-icon__line {
                stroke: rgb(var(--pdp-color-black-1400)) !important;
                stroke-width: 6 !important;
              }
            }
          }

          ul.pr-multiselect-options {
            li:hover {
              .pr-multiselect-item-label {
                color: #333;
              }
            }
          }
        }

        .pr-multiselect-button-chest,
        .pr-multiselect-button-hipsrear,
        .pr-multiselect-button-overallsize,
        .pr-multiselect-button-rise,
        .pr-multiselect-button-waist,
        .pr-multiselect-button-width,
        .pr-multiselect-button-length {
          display: block;
        }

        @media (min-width: 540px) {
          padding: 0;
          margin-bottom: 3rem;

          &:before {
            padding-left: 0;
          }

          .pr-multiselect {
            flex-grow: 1 !important;
            padding: 0;

            &:first-child {
              margin-right: 7px;
            }
            .pr-multiselect-button {
              min-width: auto !important;
            }
          }
        }
      }

      .pr-rd-main-header-search-sort {
        margin-left: auto;
        order: 2;
        display: flex;
        width: 100% !important;
        justify-content: flex-end;

        @media (max-width: 841px) {
          display: flex;
          flex-direction: column;
          width: 100% !important;
        }

        @media (min-width: 840px) {
          width: 100% !important;
          margin-left: auto;
        }

        @media (min-width: 540px) {
          order: 2;
          display: flex;
          flex-direction: column;
          padding: 0 !important;
        }

        @media (max-width: ${sdsBreakpoint.small}) {
          padding: 0px !important;
        }

        ${showReviewSearchField()}

        .pr-rd-main-header-search {
          width: auto !important;
          order: 1;

          @media (max-width: 840px) {
            padding: 0px;
            width: auto !important;
          }

          input {
            @media (max-width: ${sdsBreakpoint.small}) {
              font-size: 16px;
            }
          }
        }

        ${reviewHeaderSearchField(theme)}

        .pr-rd-review-header-sorts {
          width: auto !important;
          position: relative;
          float: none !important;
          padding: 0 !important;

          &:before {
            content: '${localize('pdp.Reviews.list.sortBy')}' !important;
            color: ${theme.color.bk};
            display: table;
            font-size: 0.875rem;
          }

          @media (max-width: 840px) {
            padding: 0px;
            width: auto !important;
          }

          @media (max-width: ${sdsBreakpoint.small}) {
            &:before {
              font-size: 17px;
            }
          }

          .pr-rd-sort-group {
            width: 100% !important;
            padding: 0 !important;
            position: relative;

            @media (max-width: 840px) {
              padding: 0 5px 0 0;
            }

            &:before {
              position: absolute;
              content: '';
              pointer-events: none;
              height: 15px;
              width: 15px;
              background: url(data:image/svg+xml;base64,${brDropdownCaretIcon}) no-repeat;
              margin: 22px;
              right: 0;
              transform: rotate(270deg);
            }

            .pr-rd-sort {
              width: 100% !important;
              border: 1px solid rgb(var(--pdp-color-black-1400));
              appearance: none;
              padding: 8px !important;
              max-width: none;
              ${textStyles(theme)};
              color: ${theme.color.bk} !important;

              @media (max-width: ${sdsBreakpoint.small}) {
                font-size: 16px;
                &:before {
                  top: 40px;
                }
              }
            }
          }

          /* there is an unexpect dropdown in the sort-by content block */
          /* this style should be removed once Power Review fix this issue.*/
          div.pr-rd-sort-group:nth-of-type(2) {
            display: none;
          }
        }
      }
    }

    .pr-submit {
      margin-top: 3rem;

      .pr-logo-container {
        margin-top: 3rem;
      }
    }
  `;
};
