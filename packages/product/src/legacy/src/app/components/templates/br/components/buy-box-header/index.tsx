// @ts-nocheck
'use client'

import { Breakpoint, XLARGE } from '@ecom-next/core/breakpoint-provider';
import type { Theme } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';
import React from 'react';

import { MarketingFlagBuyBox } from '../../../../../components/buy-box/components/marketing-flag';
import type { MarketingFlagInTitleProps } from '../../../../../components/buy-box/components/marketing-flag/types';
import Title from '../../../../../components/buy-box/components/title/index';
import Show from '../../../../../components/show';

type BuxBoxHeaderProps = {
  fontClass?: string;
  marketingFlagProps: MarketingFlagInTitleProps;
  productTitle: string;
  titleBoxStyles?: (theme: Theme) => InterpolationPrimitive;
};

const getShowProductTitleArea = (marketingFlagProps: MarketingFlagInTitleProps) =>
  marketingFlagProps?.colorPromoFlag || marketingFlagProps?.gidMessage || marketingFlagProps?.marketingFlag;

export const BrBuyBoxHeader = (props: BuxBoxHeaderProps): JSX.Element => {
  const { fontClass, titleBoxStyles, productTitle, marketingFlagProps } = props;
  return (
    <>
      <div className={`clearfix ${fontClass}`} css={titleBoxStyles}>
        <Title productTitle={productTitle} />
        <Breakpoint is="greaterOrEqualTo" size={XLARGE}>
          <Show when={!!getShowProductTitleArea(marketingFlagProps)}>
            <div className="pdp-title-box">
              <div className="pdp-title-box__marketing">
                <MarketingFlagBuyBox {...marketingFlagProps} />
              </div>
            </div>
          </Show>
        </Breakpoint>
      </div>
    </>
  );
};
