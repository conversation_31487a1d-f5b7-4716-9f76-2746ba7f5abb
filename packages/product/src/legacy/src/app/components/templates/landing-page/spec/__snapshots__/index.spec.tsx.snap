// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LandingPageOptimization renders correctly LandingPageOptimization 1`] = `
<div
  style={
    {
      "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
    }
  }
>
  <main
    className="landingPageOptimization"
    css={[Function]}
  >
    <div
      className="container"
      css={[Function]}
    >
      <section
        className="productWrapper"
        css={[Function]}
      >
        <section
          className="photoCarouselWrapper"
          css={[Function]}
        >
          <div
            css={[Function]}
          >
            <div
              className="slick-slider slick-initialized"
              dir="ltr"
            >
              <div
                className="slick-list"
                onClick={[Function]}
                onKeyDown={[Function]}
                onMouseDown={[Function]}
                onMouseLeave={null}
                onMouseMove={null}
                onMouseUp={[Function]}
                onTouchCancel={null}
                onTouchEnd={[Function]}
                onTouchMove={null}
                onTouchStart={[Function]}
                style={{}}
              >
                <div
                  className="slick-track"
                  onMouseEnter={[Function]}
                  onMouseLeave={[Function]}
                  onMouseOver={[Function]}
                  style={
                    {
                      "WebkitTransform": "translate3d(0px, 0px, 0px)",
                      "WebkitTransition": "",
                      "msTransform": "translateX(0px)",
                      "opacity": 1,
                      "transform": "translate3d(0px, 0px, 0px)",
                      "transition": "",
                    }
                  }
                >
                  <div
                    aria-hidden={false}
                    className="slick-slide slick-active slick-current"
                    data-index={0}
                    onClick={[Function]}
                    style={
                      {
                        "outline": "none",
                        "width": 0,
                      }
                    }
                    tabIndex="-1"
                  >
                    <div>
                      <div
                        className="product-image"
                        style={
                          {
                            "display": "inline-block",
                            "width": "100%",
                          }
                        }
                        tabIndex={-1}
                      >
                        <div
                          className="container"
                        >
                          <img
                            alt="1 of 8 showing, with the color Navy of product undefined"
                            height="100%"
                            src="/webcontent/0020/233/315/cn20233315.jpg"
                            srcSet="/webcontent/0020/233/352/cn20233352.jpg 202w,/webcontent/0020/233/353/cn20233353.jpg 240w,/webcontent/0020/233/348/cn20233348.jpg 260w,/webcontent/0020/233/315/cn20233315.jpg 520w,/webcontent/0020/233/317/cn20233317.jpg 1500w"
                            width="100%"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden={false}
                    className="slick-slide slick-active"
                    data-index={1}
                    onClick={[Function]}
                    style={
                      {
                        "outline": "none",
                        "width": 0,
                      }
                    }
                    tabIndex="-1"
                  >
                    <div>
                      <div
                        className="product-image"
                        style={
                          {
                            "display": "inline-block",
                            "width": "100%",
                          }
                        }
                        tabIndex={-1}
                      >
                        <div
                          className="container"
                        >
                          <img
                            alt="2 of 8 showing, with the color Navy of product undefined"
                            height="100%"
                            src="/webcontent/0020/320/234/cn20320234.jpg"
                            srcSet="/webcontent/0020/320/227/cn20320227.jpg 202w,/webcontent/0020/320/226/cn20320226.jpg 240w,/webcontent/0020/320/232/cn20320232.jpg 260w,/webcontent/0020/320/234/cn20320234.jpg 520w,/webcontent/0020/320/223/cn20320223.jpg 1500w"
                            width="100%"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden={true}
                    className="slick-slide"
                    data-index={2}
                    onClick={[Function]}
                    style={
                      {
                        "outline": "none",
                        "width": 0,
                      }
                    }
                    tabIndex="-1"
                  >
                    <div>
                      <div
                        className="product-image"
                        style={
                          {
                            "display": "inline-block",
                            "width": "100%",
                          }
                        }
                        tabIndex={-1}
                      >
                        <div
                          className="container"
                        >
                          <img
                            alt="3 of 8 showing, with the color Navy of product undefined"
                            height="100%"
                            src="/webcontent/0020/320/239/cn20320239.jpg"
                            srcSet="/webcontent/0020/320/244/cn20320244.jpg 202w,/webcontent/0020/320/242/cn20320242.jpg 240w,/webcontent/0020/320/237/cn20320237.jpg 260w,/webcontent/0020/320/239/cn20320239.jpg 520w,/webcontent/0020/320/241/cn20320241.jpg 1500w"
                            width="100%"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden={true}
                    className="slick-slide"
                    data-index={3}
                    onClick={[Function]}
                    style={
                      {
                        "outline": "none",
                        "width": 0,
                      }
                    }
                    tabIndex="-1"
                  >
                    <div>
                      <div
                        className="product-image"
                        style={
                          {
                            "display": "inline-block",
                            "width": "100%",
                          }
                        }
                        tabIndex={-1}
                      >
                        <div
                          className="container"
                        >
                          <img
                            alt="4 of 8 showing, with the color Navy of product undefined"
                            height="100%"
                            src="/webcontent/0020/191/088/cn20191088.jpg"
                            srcSet="/webcontent/0020/191/091/cn20191091.jpg 202w,/webcontent/0020/191/090/cn20191090.jpg 240w,/webcontent/0020/191/087/cn20191087.jpg 260w,/webcontent/0020/191/088/cn20191088.jpg 520w,/webcontent/0020/191/089/cn20191089.jpg 1500w"
                            width="100%"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden={true}
                    className="slick-slide"
                    data-index={4}
                    onClick={[Function]}
                    style={
                      {
                        "outline": "none",
                        "width": 0,
                      }
                    }
                    tabIndex="-1"
                  >
                    <div>
                      <div
                        className="product-image"
                        style={
                          {
                            "display": "inline-block",
                            "width": "100%",
                          }
                        }
                        tabIndex={-1}
                      >
                        <div
                          className="container"
                        >
                          <img
                            alt="5 of 8 showing, with the color Navy of product undefined"
                            height="100%"
                            src="/webcontent/0020/319/921/cn20319921.jpg"
                            srcSet="/webcontent/0020/319/917/cn20319917.jpg 202w,/webcontent/0020/319/920/cn20319920.jpg 240w,/webcontent/0020/319/912/cn20319912.jpg 260w,/webcontent/0020/319/921/cn20319921.jpg 520w,/webcontent/0020/319/901/cn20319901.jpg 1500w"
                            width="100%"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden={true}
                    className="slick-slide"
                    data-index={5}
                    onClick={[Function]}
                    style={
                      {
                        "outline": "none",
                        "width": 0,
                      }
                    }
                    tabIndex="-1"
                  >
                    <div>
                      <div
                        className="product-image"
                        style={
                          {
                            "display": "inline-block",
                            "width": "100%",
                          }
                        }
                        tabIndex={-1}
                      >
                        <div
                          className="container"
                        >
                          <img
                            alt="6 of 8 showing, with the color Navy of product undefined"
                            height="100%"
                            src="/webcontent/0020/319/921/cn20319921.jpg"
                            srcSet="/webcontent/0020/319/917/cn20319917.jpg 202w,/webcontent/0020/319/920/cn20319920.jpg 240w,/webcontent/0020/319/912/cn20319912.jpg 260w,/webcontent/0020/319/921/cn20319921.jpg 520w,/webcontent/0020/319/901/cn20319901.jpg 1500w"
                            width="100%"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden={true}
                    className="slick-slide"
                    data-index={6}
                    onClick={[Function]}
                    style={
                      {
                        "outline": "none",
                        "width": 0,
                      }
                    }
                    tabIndex="-1"
                  >
                    <div>
                      <div
                        className="product-image"
                        style={
                          {
                            "display": "inline-block",
                            "width": "100%",
                          }
                        }
                        tabIndex={-1}
                      >
                        <div
                          className="container"
                        >
                          <img
                            alt="7 of 8 showing, with the color Navy of product undefined"
                            height="100%"
                            src="/webcontent/0020/319/921/cn20319921.jpg"
                            srcSet="/webcontent/0020/319/917/cn20319917.jpg 202w,/webcontent/0020/319/920/cn20319920.jpg 240w,/webcontent/0020/319/912/cn20319912.jpg 260w,/webcontent/0020/319/921/cn20319921.jpg 520w,/webcontent/0020/319/901/cn20319901.jpg 1500w"
                            width="100%"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <button
                aria-label="Next product image"
                className="right"
                css={[Function]}
                data-testid="Arrow-right"
                onClick={[Function]}
                type="button"
              />
            </div>
          </div>
        </section>
        <section
          css={[Function]}
        >
          <div
            className="productTitleLPOWrapper"
            css={[Function]}
          >
            <div
              style={
                {
                  "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
                }
              }
            >
              <h1
                className="css-p1wad"
                dangerouslySetInnerHTML={
                  {
                    "__html": [Function],
                  }
                }
              />
            </div>
            <div
              className="pdp-title-box"
            >
              <div
                className="pdp-title-box__marketing"
                css={[Function]}
              >
                <div
                  className="messaging-wrap"
                  css={
                    [
                      [Function],
                      undefined,
                    ]
                  }
                >
                  <p
                    className="marketing-flag"
                  >
                    product data marketing flag
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
        <section
          className="buyboxWrapper"
          css={[Function]}
        >
          <div
            className="priceRatingsWrapper"
            css={[Function]}
          >
            <div
              className="pdp-review-ratings"
              css={[Function]}
            >
              <a
                className="review-ratings css-1kz5icf"
                data-testid="reviewRatings"
                tabIndex={0}
              >
                <span
                  className="css-3jhqep"
                  data-testid="reviewRatingsAriaLabel"
                >
                   fui.ReviewRatings.ariaLabelNoRatings
                </span>
                <div
                  className="css-pi3nlf"
                >
                  <div
                    className="css-8atqhb"
                  >
                    <figure
                      className="css-o8m265"
                    >
                      <span
                        aria-hidden={true}
                        css={
                          {
                            "display": "inline-block",
                            "height": "auto",
                            "minHeight": "auto",
                            "minWidth": "21px",
                            "svg": {
                              "display": "block",
                              "maxHeight": "100%",
                              "maxWidth": "100%",
                              "path": {
                                "fill": "#CCC",
                              },
                              "position": "relative",
                              "rect": {
                                "fill": "#CCC",
                              },
                              "width": "100%",
                            },
                            "width": "21px",
                          }
                        }
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 14 13"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clipRule="evenodd"
                            d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                            fill="#CCC"
                            fillRule="evenodd"
                          />
                        </svg>
                      </span>
                      <span
                        aria-hidden={true}
                        css={
                          {
                            "display": "inline-block",
                            "height": "auto",
                            "minHeight": "auto",
                            "minWidth": "21px",
                            "svg": {
                              "display": "block",
                              "maxHeight": "100%",
                              "maxWidth": "100%",
                              "path": {
                                "fill": "#CCC",
                              },
                              "position": "relative",
                              "rect": {
                                "fill": "#CCC",
                              },
                              "width": "100%",
                            },
                            "width": "21px",
                          }
                        }
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 14 13"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clipRule="evenodd"
                            d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                            fill="#CCC"
                            fillRule="evenodd"
                          />
                        </svg>
                      </span>
                      <span
                        aria-hidden={true}
                        css={
                          {
                            "display": "inline-block",
                            "height": "auto",
                            "minHeight": "auto",
                            "minWidth": "21px",
                            "svg": {
                              "display": "block",
                              "maxHeight": "100%",
                              "maxWidth": "100%",
                              "path": {
                                "fill": "#CCC",
                              },
                              "position": "relative",
                              "rect": {
                                "fill": "#CCC",
                              },
                              "width": "100%",
                            },
                            "width": "21px",
                          }
                        }
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 14 13"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clipRule="evenodd"
                            d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                            fill="#CCC"
                            fillRule="evenodd"
                          />
                        </svg>
                      </span>
                      <span
                        aria-hidden={true}
                        css={
                          {
                            "display": "inline-block",
                            "height": "auto",
                            "minHeight": "auto",
                            "minWidth": "21px",
                            "svg": {
                              "display": "block",
                              "maxHeight": "100%",
                              "maxWidth": "100%",
                              "path": {
                                "fill": "#CCC",
                              },
                              "position": "relative",
                              "rect": {
                                "fill": "#CCC",
                              },
                              "width": "100%",
                            },
                            "width": "21px",
                          }
                        }
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 14 13"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clipRule="evenodd"
                            d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                            fill="#CCC"
                            fillRule="evenodd"
                          />
                        </svg>
                      </span>
                      <span
                        aria-hidden={true}
                        css={
                          {
                            "display": "inline-block",
                            "height": "auto",
                            "minHeight": "auto",
                            "minWidth": "21px",
                            "svg": {
                              "display": "block",
                              "maxHeight": "100%",
                              "maxWidth": "100%",
                              "path": {
                                "fill": "#CCC",
                              },
                              "position": "relative",
                              "rect": {
                                "fill": "#CCC",
                              },
                              "width": "100%",
                            },
                            "width": "21px",
                          }
                        }
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 14 13"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clipRule="evenodd"
                            d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                            fill="#CCC"
                            fillRule="evenodd"
                          />
                        </svg>
                      </span>
                    </figure>
                  </div>
                  <div
                    className="css-1xp0x6i"
                  >
                    <figure
                      className="css-o8m265"
                    >
                      <span
                        aria-hidden={true}
                        css={
                          {
                            "display": "inline-block",
                            "height": "auto",
                            "minHeight": "auto",
                            "minWidth": "21px",
                            "svg": {
                              "display": "block",
                              "maxHeight": "100%",
                              "maxWidth": "100%",
                              "path": {
                                "fill": "#2B2B2B",
                              },
                              "position": "relative",
                              "rect": {
                                "fill": "#2B2B2B",
                              },
                              "width": "100%",
                            },
                            "width": "21px",
                          }
                        }
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 14 13"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clipRule="evenodd"
                            d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                            fill="#2B2B2B"
                            fillRule="evenodd"
                          />
                        </svg>
                      </span>
                      <span
                        aria-hidden={true}
                        css={
                          {
                            "display": "inline-block",
                            "height": "auto",
                            "minHeight": "auto",
                            "minWidth": "21px",
                            "svg": {
                              "display": "block",
                              "maxHeight": "100%",
                              "maxWidth": "100%",
                              "path": {
                                "fill": "#2B2B2B",
                              },
                              "position": "relative",
                              "rect": {
                                "fill": "#2B2B2B",
                              },
                              "width": "100%",
                            },
                            "width": "21px",
                          }
                        }
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 14 13"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clipRule="evenodd"
                            d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                            fill="#2B2B2B"
                            fillRule="evenodd"
                          />
                        </svg>
                      </span>
                      <span
                        aria-hidden={true}
                        css={
                          {
                            "display": "inline-block",
                            "height": "auto",
                            "minHeight": "auto",
                            "minWidth": "21px",
                            "svg": {
                              "display": "block",
                              "maxHeight": "100%",
                              "maxWidth": "100%",
                              "path": {
                                "fill": "#2B2B2B",
                              },
                              "position": "relative",
                              "rect": {
                                "fill": "#2B2B2B",
                              },
                              "width": "100%",
                            },
                            "width": "21px",
                          }
                        }
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 14 13"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clipRule="evenodd"
                            d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                            fill="#2B2B2B"
                            fillRule="evenodd"
                          />
                        </svg>
                      </span>
                      <span
                        aria-hidden={true}
                        css={
                          {
                            "display": "inline-block",
                            "height": "auto",
                            "minHeight": "auto",
                            "minWidth": "21px",
                            "svg": {
                              "display": "block",
                              "maxHeight": "100%",
                              "maxWidth": "100%",
                              "path": {
                                "fill": "#2B2B2B",
                              },
                              "position": "relative",
                              "rect": {
                                "fill": "#2B2B2B",
                              },
                              "width": "100%",
                            },
                            "width": "21px",
                          }
                        }
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 14 13"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clipRule="evenodd"
                            d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                            fill="#2B2B2B"
                            fillRule="evenodd"
                          />
                        </svg>
                      </span>
                      <span
                        aria-hidden={true}
                        css={
                          {
                            "display": "inline-block",
                            "height": "auto",
                            "minHeight": "auto",
                            "minWidth": "21px",
                            "svg": {
                              "display": "block",
                              "maxHeight": "100%",
                              "maxWidth": "100%",
                              "path": {
                                "fill": "#2B2B2B",
                              },
                              "position": "relative",
                              "rect": {
                                "fill": "#2B2B2B",
                              },
                              "width": "100%",
                            },
                            "width": "21px",
                          }
                        }
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 14 13"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clipRule="evenodd"
                            d="M7.02697 9.38889l-4.219 3.53831 1.40821-4.98276L0 4.76725l5.07966-.0364L7.02697 0l1.89337 4.73085L14 4.76725 9.83776 7.94444 11.2431 13 7.02697 9.38889z"
                            fill="#2B2B2B"
                            fillRule="evenodd"
                          />
                        </svg>
                      </span>
                    </figure>
                  </div>
                </div>
              </a>
            </div>
            <div
              className="pdp-pricing "
              css={[Function]}
            >
              <span
                className=" pdp-pricing__selected "
                css={[Function]}
              >
                $49.97
              </span>
              <h3
                aria-label="pdp.price.currentPriceRangeAriaLabel"
                className="product-price--pdp"
                css={[Function]}
                data-testid="simpleRangeDisplay"
              >
                <span
                  className="product-price--pdp__highlight"
                >
                  $NaN
                  <span
                    className="product-price--pdp__range-separator"
                  >
                    -
                  </span>
                  $NaN
                </span>
              </h3>
            </div>
          </div>
          <p
            className="label-message"
            css={
              [
                [Function],
              ]
            }
          >
            Final sale. No returns or exchanges.
          </p>
          <section
            className="colorSizeWrapper"
            css={[Function]}
          >
            <div
              aria-labelledby="swatch-label--Color"
              className="buy-box__swatch-container buy-box__swatch-container--color"
              css={[Function]}
              role="radiogroup"
            >
              <div
                className="swatch-label "
                css={[Function]}
                id="swatch-label--Color"
              >
                <span
                  aria-label="pdp.swatchLabel.ariaLabel"
                  className="swatch-label__group "
                >
                  pdp.colorPickerGroupLabel
                  : 
                </span>
                <span
                  aria-hidden="true"
                  className="swatch-label__value"
                  dangerouslySetInnerHTML={
                    {
                      "__html": "navy",
                    }
                  }
                />
              </div>
              <div
                className="swatches swatches--center swatches_color"
                css={[Function]}
              >
                <div
                  className="swatch-group swatch-group--color"
                  onScroll={[Function]}
                >
                  <div
                    className="swatch-price-group"
                    css={[Function]}
                    role="group"
                  >
                    <div
                      className="swatch-price-group__list"
                      css={
                        [
                          [Function],
                          [Function],
                        ]
                      }
                      role="group"
                    >
                      <div
                        className="css-b3pn3b"
                      >
                        <div
                          className="css-xdz0b6"
                          data-testid="swatch-and-label-container"
                          onClick={[Function]}
                        >
                          <div
                            className="swatch swatch--fixed swatch--color swatch-color__selected css-5h7d9x"
                            selected={true}
                            size="default"
                          >
                            <input
                              aria-checked={true}
                              aria-disabled={false}
                              aria-label="Navy"
                              checked={true}
                              className="css-40vz4u"
                              id="buybox-color-swatch--Navy"
                              name="color-radio"
                              onChange={[Function]}
                              onMouseEnter={[Function]}
                              onMouseLeave={[Function]}
                              type="radio"
                              value="Navy"
                            />
                            <img
                              aria-hidden={true}
                              className="css-fwb92"
                              height="100%"
                              selected={true}
                              size="default"
                              src="https://www.gap.com/webcontent/0053/216/063/cn53216063.jpg"
                              title="Navy"
                              width="100%"
                            />
                          </div>
                          <label
                            className="css-1a1fssu"
                            htmlFor="buybox-color-swatch--Navy"
                            onClick={[Function]}
                          >
                            Navy
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="variant-group--wrapper"
            >
              <div
                aria-labelledby="swatch-label--Variant"
                className="radio-container radio-container--variant"
                css={[Function]}
                role="radiogroup"
              >
                <span
                  className="radio-container__label"
                  id="swatch-label--Variant"
                >
                  pdp.variantGroupLabel
                </span>
                <div
                  className="pdp-variant"
                  css={[Function]}
                >
                  <input
                    aria-label="Regular"
                    checked={false}
                    className="pdp-variant__radio"
                    id="1"
                    name="fit-radio"
                    onBlur={[Function]}
                    onChange={[Function]}
                    type="radio"
                    value="Regular"
                  />
                  <label
                    aria-hidden={true}
                    className="pdp-variant__label"
                    htmlFor="1"
                  >
                    Regular
                  </label>
                </div>
                <div
                  className="pdp-variant selected"
                  css={[Function]}
                >
                  <input
                    aria-label="Tall"
                    checked={true}
                    className="pdp-variant__radio"
                    id="2"
                    name="fit-radio"
                    onBlur={[Function]}
                    onChange={[Function]}
                    type="radio"
                    value="Tall"
                  />
                  <label
                    aria-hidden={true}
                    className="pdp-variant__label"
                    htmlFor="2"
                  >
                    Tall
                  </label>
                </div>
              </div>
            </div>
            <div
              aria-labelledby="swatch-label--sizeDimension1"
              className="radio-container radio-container--dimension"
              css={[Function]}
              data-testid="dimension-group"
              role="radiogroup"
            >
              <span
                className="radio-container__label"
                id="swatch-label--sizeDimension1"
              >
                Waist
                <span
                  aria-hidden="true"
                >
                  :
                </span>
              </span>
              <div
                className="dimensions"
                css={[Function]}
              >
                <div
                  className="dimensions-group"
                  css={[Function]}
                  id="variant-2-sizeDimension1-dimensions-group"
                >
                  <div
                    className="dimension-list"
                    css={
                      [
                        [Function],
                        [Function],
                        [Function],
                      ]
                    }
                  >
                    <div
                      className="undefined pdp-dimension pdp-dimension--should-display-redesign-in-stock pdp-dimension--unavailable"
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                    >
                      <input
                        aria-checked={false}
                        aria-label="pdp.sizeLabel:pdp.dimensionPicker.outOfStock"
                        checked={false}
                        className="pdp-dimension__radio"
                        id="changestoremodal-variant-2-sizeDimension1-32W"
                        name="change-store-modal-Waist"
                        onChange={[Function]}
                        type="radio"
                        value="32W"
                      />
                      <label
                        aria-hidden={true}
                        className="pdp-dimension__label"
                        htmlFor="changestoremodal-variant-2-sizeDimension1-32W"
                      >
                        <span
                          className="pdp-dimension__text"
                        >
                          32W
                        </span>
                      </label>
                      <svg
                        className="swatch__out-of-stock-indicator"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <line
                          stroke="#000000"
                          strokeWidth="1"
                          x1="0"
                          x2="100%"
                          y1="40"
                          y2="0"
                        />
                      </svg>
                    </div>
                    <div
                      className="undefined pdp-dimension pdp-dimension--should-display-redesign-in-stock pdp-dimension--unavailable"
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                    >
                      <input
                        aria-checked={false}
                        aria-label="pdp.sizeLabel:pdp.dimensionPicker.outOfStock"
                        checked={false}
                        className="pdp-dimension__radio"
                        id="changestoremodal-variant-2-sizeDimension1-34W"
                        name="change-store-modal-Waist"
                        onChange={[Function]}
                        type="radio"
                        value="34W"
                      />
                      <label
                        aria-hidden={true}
                        className="pdp-dimension__label"
                        htmlFor="changestoremodal-variant-2-sizeDimension1-34W"
                      >
                        <span
                          className="pdp-dimension__text"
                        >
                          34W
                        </span>
                      </label>
                      <svg
                        className="swatch__out-of-stock-indicator"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <line
                          stroke="#000000"
                          strokeWidth="1"
                          x1="0"
                          x2="100%"
                          y1="40"
                          y2="0"
                        />
                      </svg>
                    </div>
                    <div
                      className="undefined pdp-dimension pdp-dimension--should-display-redesign-in-stock selected"
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                    >
                      <input
                        aria-checked={true}
                        aria-label="pdp.sizeLabel:35W"
                        checked={true}
                        className="pdp-dimension__radio"
                        id="changestoremodal-variant-2-sizeDimension1-35W"
                        name="change-store-modal-Waist"
                        onChange={[Function]}
                        type="radio"
                        value="35W"
                      />
                      <label
                        aria-hidden={true}
                        className="pdp-dimension__label"
                        htmlFor="changestoremodal-variant-2-sizeDimension1-35W"
                      >
                        <span
                          className="pdp-dimension__text"
                        >
                          35W
                        </span>
                      </label>
                    </div>
                    <div
                      className="undefined pdp-dimension pdp-dimension--should-display-redesign-in-stock pdp-dimension--unavailable"
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                    >
                      <input
                        aria-checked={false}
                        aria-label="pdp.sizeLabel:pdp.dimensionPicker.outOfStock"
                        checked={false}
                        className="pdp-dimension__radio"
                        id="changestoremodal-variant-2-sizeDimension1-36W"
                        name="change-store-modal-Waist"
                        onChange={[Function]}
                        type="radio"
                        value="36W"
                      />
                      <label
                        aria-hidden={true}
                        className="pdp-dimension__label"
                        htmlFor="changestoremodal-variant-2-sizeDimension1-36W"
                      >
                        <span
                          className="pdp-dimension__text"
                        >
                          36W
                        </span>
                      </label>
                      <svg
                        className="swatch__out-of-stock-indicator"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <line
                          stroke="#000000"
                          strokeWidth="1"
                          x1="0"
                          x2="100%"
                          y1="40"
                          y2="0"
                        />
                      </svg>
                    </div>
                    <div
                      className="undefined pdp-dimension pdp-dimension--should-display-redesign-in-stock"
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                    >
                      <input
                        aria-checked={false}
                        aria-label="pdp.sizeLabel:38W"
                        checked={false}
                        className="pdp-dimension__radio"
                        id="changestoremodal-variant-2-sizeDimension1-38W"
                        name="change-store-modal-Waist"
                        onChange={[Function]}
                        type="radio"
                        value="38W"
                      />
                      <label
                        aria-hidden={true}
                        className="pdp-dimension__label"
                        htmlFor="changestoremodal-variant-2-sizeDimension1-38W"
                      >
                        <span
                          className="pdp-dimension__text"
                        >
                          38W
                        </span>
                      </label>
                    </div>
                    <div
                      className="undefined pdp-dimension pdp-dimension--should-display-redesign-in-stock pdp-dimension--unavailable"
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                    >
                      <input
                        aria-checked={false}
                        aria-label="pdp.sizeLabel:pdp.dimensionPicker.outOfStock"
                        checked={false}
                        className="pdp-dimension__radio"
                        id="changestoremodal-variant-2-sizeDimension1-40W"
                        name="change-store-modal-Waist"
                        onChange={[Function]}
                        type="radio"
                        value="40W"
                      />
                      <label
                        aria-hidden={true}
                        className="pdp-dimension__label"
                        htmlFor="changestoremodal-variant-2-sizeDimension1-40W"
                      >
                        <span
                          className="pdp-dimension__text"
                        >
                          40W
                        </span>
                      </label>
                      <svg
                        className="swatch__out-of-stock-indicator"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <line
                          stroke="#000000"
                          strokeWidth="1"
                          x1="0"
                          x2="100%"
                          y1="40"
                          y2="0"
                        />
                      </svg>
                    </div>
                    <div
                      className="undefined pdp-dimension pdp-dimension--should-display-redesign-in-stock pdp-dimension--unavailable"
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                    >
                      <input
                        aria-checked={false}
                        aria-label="pdp.sizeLabel:pdp.dimensionPicker.outOfStock"
                        checked={false}
                        className="pdp-dimension__radio"
                        id="changestoremodal-variant-2-sizeDimension1-42W"
                        name="change-store-modal-Waist"
                        onChange={[Function]}
                        type="radio"
                        value="42W"
                      />
                      <label
                        aria-hidden={true}
                        className="pdp-dimension__label"
                        htmlFor="changestoremodal-variant-2-sizeDimension1-42W"
                      >
                        <span
                          className="pdp-dimension__text"
                        >
                          42W
                        </span>
                      </label>
                      <svg
                        className="swatch__out-of-stock-indicator"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <line
                          stroke="#000000"
                          strokeWidth="1"
                          x1="0"
                          x2="100%"
                          y1="40"
                          y2="0"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              aria-labelledby="swatch-label--sizeDimension2"
              className="radio-container radio-container--dimension"
              css={[Function]}
              data-testid="dimension-group"
              role="radiogroup"
            >
              <span
                className="radio-container__label"
                id="swatch-label--sizeDimension2"
              >
                Length
                <span
                  aria-hidden="true"
                >
                  :
                </span>
              </span>
              <div
                className="dimensions"
                css={[Function]}
              >
                <div
                  className="dimensions-group"
                  css={[Function]}
                  id="variant-2-sizeDimension2-dimensions-group"
                >
                  <div
                    className="dimension-list"
                    css={
                      [
                        [Function],
                        [Function],
                        [Function],
                      ]
                    }
                  >
                    <div
                      className="undefined pdp-dimension pdp-dimension--should-display-redesign-in-stock selected"
                      onMouseEnter={[Function]}
                      onMouseLeave={[Function]}
                    >
                      <input
                        aria-checked={true}
                        aria-label="pdp.sizeLabel:36L"
                        checked={true}
                        className="pdp-dimension__radio"
                        id="changestoremodal-variant-2-sizeDimension2-36L"
                        name="change-store-modal-Length"
                        onChange={[Function]}
                        type="radio"
                        value="36L"
                      />
                      <label
                        aria-hidden={true}
                        className="pdp-dimension__label"
                        htmlFor="changestoremodal-variant-2-sizeDimension2-36L"
                      >
                        <span
                          className="pdp-dimension__text"
                        >
                          36L
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
          <div
            className="collectiveMessagesWrapper"
            css={[Function]}
          />
          <section
            className="freeShippingMessageWrapper"
            css={[Function]}
          >
            <div
              css={[Function]}
            >
              <span>
                <svg
                  fill="none"
                  height="19"
                  viewBox="0 0 34 19"
                  width="34"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clipRule="evenodd"
                    d="M6 4.5C6 4.22386 6.22386 4 6.5 4L13.5 4C13.7761 4 14 4.22386 14 4.5C14 4.77614 13.7761 5 13.5 5L6.5 5C6.22386 5 6 4.77614 6 4.5Z"
                    fill="black"
                    fillRule="evenodd"
                  />
                  <path
                    clipRule="evenodd"
                    d="M6 12.5C6 12.2239 6.22386 12 6.5 12L13.5 12C13.7761 12 14 12.2239 14 12.5C14 12.7761 13.7761 13 13.5 13L6.5 13C6.22386 13 6 12.7761 6 12.5Z"
                    fill="black"
                    fillRule="evenodd"
                  />
                  <path
                    clipRule="evenodd"
                    d="M4.37114e-08 8.5C1.95703e-08 8.22386 0.223858 8 0.5 8L13.5 8C13.7761 8 14 8.22386 14 8.5C14 8.77614 13.7761 9 13.5 9L0.5 9C0.223858 9 6.78526e-08 8.77614 4.37114e-08 8.5Z"
                    fill="black"
                    fillRule="evenodd"
                  />
                  <path
                    clipRule="evenodd"
                    d="M33.9341 4.9783C33.9341 4.77958 33.8683 4.6471 33.6709 4.58086C30.7093 3.12357 27.7476 1.60003 24.7859 0.0764987C24.6164 -0.0372603 24.4953 -0.00451707 24.2979 0.0489542C24.2653 0.0577547 24.2307 0.0671159 24.1935 0.0764771C22.7456 0.805105 21.2813 1.5503 19.817 2.29549C18.3526 3.0407 16.8881 3.78596 15.4402 4.51462C15.3824 4.54366 15.3247 4.55997 15.2726 4.57471C15.2058 4.59359 15.148 4.6099 15.1111 4.6471V14.1858C15.1769 14.252 15.2427 14.252 15.3085 14.252C17.278 15.2652 19.2475 16.249 21.2363 17.2425C22.2381 17.743 23.2453 18.2461 24.2594 18.7564C24.3252 18.7564 24.391 18.8227 24.4568 18.8889H24.6542C24.6871 18.8557 24.7365 18.8227 24.7858 18.7895C24.8352 18.7564 24.8846 18.7233 24.9175 18.6902C25.878 18.2068 26.8457 17.7162 27.8182 17.2231C29.7779 16.2295 31.7573 15.226 33.7367 14.252C33.9341 14.1858 34 14.0533 34 13.8546C33.9341 10.94 33.9341 7.95913 33.9341 4.9783ZM23.5737 1.60284C23.8796 1.41558 24.2276 1.20257 24.5226 1.20257C24.7351 1.20257 24.9751 1.34073 25.2071 1.47427C25.3343 1.5475 25.4591 1.61934 25.5756 1.66626C27.4843 2.59363 29.3929 3.58724 31.3015 4.58086C31.3345 4.61397 31.3838 4.63054 31.4332 4.6471C31.4826 4.66366 31.5319 4.68022 31.5648 4.71334C31.137 4.94518 30.7092 5.16046 30.2814 5.37575C29.8536 5.59103 29.4258 5.80631 28.998 6.03815H28.7347C27.2039 5.28977 25.673 4.51214 24.1228 3.72468C23.3404 3.32726 22.5531 2.92732 21.7584 2.52739C22.3507 2.19619 22.8772 1.93122 23.4696 1.66626C23.5036 1.64572 23.5384 1.62442 23.5737 1.60284ZM23.8645 17.3654V9.48265C23.8645 9.21774 23.7986 9.08525 23.6012 9.01898L16.4932 5.44199C16.4525 5.40105 16.4118 5.38541 16.3556 5.3638C16.3209 5.35044 16.2802 5.33481 16.2299 5.3095V13.3246C16.2299 13.5234 16.3616 13.5896 16.4932 13.6559L23.6012 17.2329C23.6419 17.2738 23.6826 17.2894 23.7388 17.311C23.7735 17.3244 23.8142 17.34 23.8645 17.3654ZM24.5884 8.15789H24.3252C23.1734 7.5948 22.0381 7.03177 20.9028 6.46875C19.7675 5.90572 18.6322 5.34262 17.4804 4.77958L17.4146 4.71334C17.4553 4.6724 17.496 4.65676 17.5522 4.63515C17.5869 4.6218 17.6276 4.60616 17.6779 4.58086C18.5335 4.11717 19.3891 3.71973 20.3105 3.32228C20.4421 3.25604 20.5737 3.25604 20.7054 3.32228C21.6364 3.77742 22.5674 4.22174 23.4941 4.66399C24.8602 5.31593 26.2168 5.96335 27.5501 6.63431C27.583 6.63431 27.5994 6.65088 27.6159 6.66745C27.6324 6.68402 27.6488 6.70058 27.6817 6.70058C27.1552 6.93242 26.6452 7.18083 26.1351 7.42924C25.625 7.67765 25.115 7.92599 24.5884 8.15789ZM27.6866 16.097C29.2953 15.2801 30.9237 14.4531 32.552 13.6559C32.7495 13.5896 32.7495 13.4571 32.7495 13.3246V5.37575C32.7166 5.37575 32.6836 5.39231 32.6507 5.40887C32.6179 5.42543 32.5849 5.44199 32.552 5.44199C32.196 5.62115 31.8475 5.79274 31.504 5.96191C30.8314 6.29304 30.178 6.61474 29.5245 6.9655V10.0126L28.3399 10.6088V7.56167C27.8462 7.82664 27.3362 8.07505 26.8261 8.32346C26.316 8.57187 25.806 8.82027 25.3124 9.08525C25.1807 9.15146 25.115 9.21774 25.115 9.41644V17.3654C25.192 17.3654 25.224 17.3426 25.2505 17.3238C25.2692 17.3105 25.2851 17.2991 25.3124 17.2991C26.0974 16.9041 26.8896 16.5018 27.6866 16.097Z"
                    fill="black"
                    fillRule="evenodd"
                  />
                </svg>
                 
                pdp.freeShipping.title
              </span>
              <h2>
                pdp.freeShipping.messageWithoutBrand
              </h2>
            </div>
          </section>
          <div
            onKeyDown={[Function]}
            role="presentation"
          />
          <div
            css={[Function]}
          >
            <div
              style={
                {
                  "fontFamily": "Helvetica Neue,Helvetica,Arial,Roboto,sans-serif",
                }
              }
            >
              <div
                css={[Function]}
              >
                <div
                  className="AddToBag"
                  css={[Function]}
                  id="AddToBag"
                >
                  <div
                    css={[Function]}
                    id="AddToBagWithPrice"
                  >
                    <span
                      className="quantity-label"
                      css={[Function]}
                    >
                      pdp.quantitySelector.title
                    </span>
                    <div
                      className="pdp-quantity"
                    >
                      <div
                        className="pdp-quantity__dropdown"
                        css={[Function]}
                        onBlur={[Function]}
                        onFocus={[Function]}
                      >
                        <button
                          aria-expanded={false}
                          aria-haspopup="listbox"
                          className="pdp-quantity__control"
                          css={[Function]}
                          data-toggle={true}
                          disabled={false}
                          onClick={[Function]}
                          onKeyDown={[Function]}
                          onKeyUp={[Function]}
                          type="button"
                        >
                          1
                        </button>
                        <ul
                          className="pdp-quantity__menu"
                          css={[Function]}
                          onKeyDown={[Function]}
                          role="listbox"
                          tabIndex={-1}
                        />
                        <select
                          aria-hidden={true}
                          hidden={true}
                          name=""
                          onChange={[Function]}
                          value={1}
                        />
                      </div>
                    </div>
                    <button
                      aria-label="pdp.addToBag.ariaLabel"
                      className="add-to-bag-with-price add-to-bag-with-selected-size css-ksql31"
                      disabled={false}
                      id="AddToBag_add-to-bag__button"
                      onClick={[Function]}
                      selected={false}
                    >
                      <span>
                        pdp.addToBag.text
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <section
            css={[Function]}
          >
            <div
              css={[Function]}
              id="pdp-product-description"
            >
              <div
                className="product-description-tab-name"
              >
                pdp.productdetails.tablist.title
              </div>
              <div
                className="product-description-bullet-point-list"
              >
                <ul>
                  <li
                    className="product-description-bullet-point"
                  >
                    <span
                      dangerouslySetInnerHTML={
                        {
                          "__html": "SKINNY FIT: Mid-rise, modern, and form-fitting. Skinny from hip to hem.",
                        }
                      }
                    />
                  </li>
                </ul>
              </div>
            </div>
          </section>
          <div
            css={[Function]}
          >
            <button
              css={[Function]}
              type="button"
            >
              <a
                href="http://localhost/?"
                onClick={[Function]}
                rel="noreferrer"
                target="_blank"
              >
                pdp.lpo.fullDetailsLink
              </a>
            </button>
          </div>
        </section>
      </section>
      <section
        className="certonaRecommendationsWrapper"
        css={[Function]}
      />
      <section
        className="reviewsWrapper"
      />
    </div>
  </main>
  <div
    className="lpo-modal"
  />
</div>
`;
