// @ts-nocheck
import TestApp, { defaultBrandMarketData } from '@pdp/spec/test-app';
import { render } from '@testing-library/react';
import React from 'react';

import type { FeatureFlag } from '../constants/feature-flags';
import {
  useBrandAgnosticFeatureFlag,
  useBrandAgnosticFeatureVariables,
  useFeature,
  useFeatureFlag,
} from './use-feature-flag';

type FeatureFlagProps = {
  flagName: string;
  variableName: string;
};

function ComponentWithFeature({ feature }: { feature: FeatureFlag }) {
  const featureEnabled = useFeature(feature);
  return <>{featureEnabled ? 'on' : 'off'}</>;
}

function ComponentWithFeatureFlag({ flagName }: Partial<FeatureFlagProps>) {
  const featureEnabled = useFeatureFlag(flagName || '');
  return <>{featureEnabled ? 'on' : 'off'}</>;
}

function ComponentUsingBrandAgnosticFlag({ flagName }: Partial<FeatureFlagProps>) {
  const featureEnabled = useBrandAgnosticFeatureFlag(flagName || '');
  return <>{featureEnabled ? 'on' : 'off'}</>;
}

function ComponentUsingBrandAgnosticVariables({ flagName, variableName }: Partial<FeatureFlagProps>) {
  const featureVariables = useBrandAgnosticFeatureVariables(flagName || '');
  return <>{featureVariables[variableName || ''] ? 'on' : 'off'}</>;
}

describe('featureFlagHooks', () => {
  describe('useFeature hook', () => {
    let FeatureFlags: Record<string, FeatureFlag>;

    beforeAll(() => {
      FeatureFlags = {
        brandAgnosticFeature: {
          isBrandAgnostic: true,
          key: 'pdp-afterpay',
        },
        brandSpecificFeature: {
          isBrandAgnostic: false,
          key: 'pdp-sticky-add-to-bag',
        },
      };
    });

    describe('when feature is brand agnostic', () => {
      test('should render "on" for brandAgnosticFeature', () => {
        const { container } = render(
          <TestApp featureFlagsCtxValue={{ enabledFeatures: { 'pdp-afterpay': true } }}>
            <ComponentWithFeature feature={FeatureFlags.brandAgnosticFeature} />
          </TestApp>
        );
        expect(container.innerHTML).toEqual('on');
      });

      test('should render "off" for brandSpecificFeature', () => {
        const { container } = render(
          <TestApp featureFlagsCtxValue={{ enabledFeatures: { 'pdp-afterpay': true } }}>
            <ComponentWithFeature feature={FeatureFlags.brandSpecificFeature} />
          </TestApp>
        );
        expect(container.innerHTML).toEqual('off');
      });
    });

    describe('when feature is brand specific', () => {
      test('should render "on" for brandSpecificFeature', () => {
        const { container } = render(
          <TestApp
            brandMarketCtxValue={{ brandName: 'br', market: 'us' }}
            featureFlagsCtxValue={{ enabledFeatures: { 'pdp-sticky-add-to-bag-us-br': true } }}
          >
            <ComponentWithFeature feature={FeatureFlags.brandSpecificFeature} />
          </TestApp>
        );
        expect(container.innerHTML).toEqual('on');
      });

      test('should render "off" for brandAgnosticFeature', () => {
        const { container } = render(
          <TestApp
            brandMarketCtxValue={{ brandName: 'br', market: 'us' }}
            featureFlagsCtxValue={{ enabledFeatures: { 'pdp-sticky-add-to-bag-us-br': true } }}
          >
            <ComponentWithFeature feature={FeatureFlags.brandAgnosticFeature} />
          </TestApp>
        );
        expect(container.innerHTML).toEqual('off');
      });
    });
  });

  describe('useBrandAgnosticFeatureFlag hook', () => {
    test('should return on text as flag is present', () => {
      const featureFlagsCtxValue = {
        enabledFeatures: { 'pdp-afterpay': true },
        featureVariables: {},
      };

      const { container } = render(
        <TestApp featureFlagsCtxValue={featureFlagsCtxValue}>
          <ComponentUsingBrandAgnosticFlag flagName="pdp-afterpay" />
        </TestApp>
      );
      expect(container.innerHTML).toBe('on');
    });

    test('should return off text if flag is set to false', () => {
      const featureFlagsCtxValue = {
        enabledFeatures: { 'pdp-afterpay': false },
        featureVariables: {},
      };

      const { container } = render(
        <TestApp featureFlagsCtxValue={featureFlagsCtxValue}>
          <ComponentUsingBrandAgnosticFlag flagName="pdp-afterpay" />
        </TestApp>
      );
      expect(container.innerHTML).toBe('off');
    });

    test('should return off text if no feature flag present', () => {
      const featureFlagsCtxValue = {
        enabledFeatures: {},
        featureVariables: {},
      };

      const { container } = render(
        <TestApp featureFlagsCtxValue={featureFlagsCtxValue}>
          <ComponentUsingBrandAgnosticFlag flagName="pdp-afterpay" />
        </TestApp>
      );
      expect(container.innerHTML).toBe('off');
    });
  });

  describe('useBrandAgnosticFeatureVariables hook', () => {
    test('should render on text as brand/market specific feature variable is present', () => {
      const featureFlagsCtxValue = {
        enabledFeatures: {
          'pdp-afterpay': true,
        },
        featureVariables: {
          'pdp-afterpay': {
            'var1-us-on': '1234, 2345',
            'var2-us-on': 'ALL',
          },
        },
      };

      const { container } = render(
        <TestApp
          brandMarketCtxValue={{
            ...defaultBrandMarketData,
            brandName: 'on',
            market: 'us',
          }}
          featureFlagsCtxValue={featureFlagsCtxValue}
        >
          <ComponentUsingBrandAgnosticVariables flagName="pdp-afterpay" variableName="var1-us-on" />
        </TestApp>
      );
      expect(container.innerHTML).toBe('on');
    });

    test('should render off as no brand/market specific feature variables not present', () => {
      const featureFlagsCtxValue = {
        enabledFeatures: {
          'pdp-afterpay': true,
        },
        featureVariables: {
          'pdp-afterpay': {
            'var1-us-on': '1234, 2345',
            'var2-us-on': 'ALL',
          },
        },
      };

      const { container } = render(
        <TestApp
          brandMarketCtxValue={{
            ...defaultBrandMarketData,
            brandName: 'gap',
            market: 'us',
          }}
          featureFlagsCtxValue={featureFlagsCtxValue}
        >
          <ComponentUsingBrandAgnosticVariables flagName="pdp-afterpay" variableName="var1-us-gap" />
        </TestApp>
      );
      expect(container.innerHTML).toBe('off');
    });
  });

  describe('useFeatureFlag', () => {
    test('reads enabled feature flags', () => {
      const { container } = render(
        <TestApp>
          <ComponentWithFeatureFlag flagName="test-feature" />
        </TestApp>
      );
      expect(container.innerHTML).toBe('on');
    });

    test('respects market', () => {
      const { container } = render(
        <TestApp
          brandMarketCtxValue={{
            ...defaultBrandMarketData,
            market: 'ca',
          }}
        >
          <ComponentWithFeatureFlag flagName="test-feature" />
        </TestApp>
      );
      expect(container.innerHTML).toBe('off');
    });

    test('respects brand', () => {
      const { container } = render(
        <TestApp
          brandMarketCtxValue={{
            ...defaultBrandMarketData,
            brandName: 'br',
          }}
        >
          <ComponentWithFeatureFlag flagName="test-feature" />
        </TestApp>
      );
      expect(container.innerHTML).toBe('off');
    });
  });
});
