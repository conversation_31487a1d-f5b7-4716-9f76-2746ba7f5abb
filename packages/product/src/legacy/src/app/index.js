// @ts-nocheck
'use client'

import { AppStateProviderCore as AppStateProvider } from '@ecom-next/sitewide/app-state-provider';
import { BreakpointProviderCore as BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import { CertonaProvider } from '@ecom-next/core/legacy/certona-provider';
import { Provider as FeatureFlagsProviderFromCore } from '@ecom-next/core/legacy/feature-flags';
import { LocalizationProviderCore as LocalizationProvider } from '@ecom-next/sitewide/localization-provider';
import { MarketingProvider } from '@ecom-next/marketing-ui/legacy-marketing-provider';
import { PersonalizationProviderCore as PersonalizationProvider } from "@ecom-next/sitewide/personalization-provider";
import { StitchInverseStyleProviderCore, StitchStyleProvider } from "@ecom-next/core/react-stitch";
import { Provider as FeatureFlagsProvider } from '@ecom-next/core/legacy/feature-flags';
import MarketingComponent from '@ecom-next/core/legacy/marketing-ui';
import datalayer from '@mfe/data-layer';
import React from 'react';
import lazy from 'next/dynamic';

import { ABSegProvider } from './components/abseg-provider';
import { AppConfigProvider } from './components/app-config-provider';
import { BrandMarketProvider } from './components/brand-market-provider';
import { CustomThemeProvider } from './components/custom-theme-provider';
import { ModelSizeProvider } from './components/model-size-provider';
import ProductPageWithFlags from './components/product-page';
import configDateLocale from './helpers/i18n';
import { getStitchProviderData } from './helpers/util/get-stitch-provider-data';
import useTemplateFactory from './hooks/use-template-factory';
import { ScriptLoaderProvider } from '@product-page/legacy/script-loader-provider';

const Style = universal(({ brandName }) => import(`./styles-entry/${brandName}-product`), {
  ignoreBabelRename: true,
  loading: () => null,
});

const ProductPageApp = props => {
  const {
    abSeg,
    market = 'us',
    brandInformation = {},
    enabledFeatures = {},
    featureVariables = {},
    locale,
    appConfig,
    modelSize,
    translations,
    initialMedia,
    initialSizeClass,
    brandName,
  } = props;
  const modelSizes = props.productData?.infoTabs?.overview?.modelSizes || [];
  const pmcsFlag = `pdp-pmcs-${market}-${brandName}`;
  const isPMCSEnabled = !!enabledFeatures[pmcsFlag];
  const containerLabel = 'pdp/banner1';
  const pidString = props?.params?.pid?.toString();
  const { primarySellingStyleId } = props.productData;
  const placementId = `${primarySellingStyleId}/${containerLabel}`;
  const pmcsMarketingProps = isPMCSEnabled
    ? {
        cid: primarySellingStyleId,
        contentData: { [placementId]: props.pmcsContentData?.[`${primarySellingStyleId}/pdp/banner1`] },
      }
    : {};

  const gapRedesign2024Segment = abSeg[`${brandName.toLowerCase()}214`];
  const isGapBuyboxRedesign2024 = enabledFeatures['gap-buybox-2024'] && ['a', 'b'].includes(gapRedesign2024Segment);
  const stitchStyleProviderData = React.useMemo(() => {
    const avoidATRedesign = !enabledFeatures['at-redesign-2024'];
    const stitchData = getStitchProviderData(brandName, enabledFeatures, avoidATRedesign);
    return {
      ...stitchData,
      'gap-redesign-2024': isGapBuyboxRedesign2024,
    };
  }, [enabledFeatures, brandName]);
  const template = useTemplateFactory(props);

  const certonaConfig = () => {
    const defaultCertonaConfig = { pageType: props.pageType };
    const LPOCertonaConfig = {
      globalCertonaConfig: { itemid: pidString, pagetype: 'PRODUCTLPO', recommendations: true },
      pageType: 'PRODUCTLPO',
    };
    const OOSCertonaConfig = { pageType: 'OUTOFSTOCK' };
    const certonaConfigs = {
      LPO: LPOCertonaConfig,
      OOS: OOSCertonaConfig,
      PDP: defaultCertonaConfig,
    };

    return certonaConfigs[template] || defaultCertonaConfig;
  };

  configDateLocale(locale);

  return (
    <AppStateProvider value={{ ...props, brandName }}>
      <AppConfigProvider appConfig={appConfig}>
        <ABSegProvider abSeg={abSeg} brandName={brandName}>
          <FeatureFlagsProviderFromCore value={{ enabledFeatures, featureVariables }}>
            <FeatureFlagsProvider value={{ enabledFeatures, featureVariables }}>
              <BreakpointProvider initialMedia={initialMedia} initialSizeClass={initialSizeClass}>
                <LocalizationProvider locale={locale} translations={translations}>
                  <PersonalizationProvider
                    datalayer={datalayer}
                    errorLogger={props.errorLogger}
                    marketAgnosticBrandCode={brandInformation.marketAgnosticBrandCode}
                  >
                    <BrandMarketProvider brandInformation={brandInformation} locale={locale} market={market}>
                      <CustomThemeProvider avoidATRedesign>
                        <StitchStyleProvider brand={brandName} enabledFeatures={stitchStyleProviderData}>
                          <Style brandName={brandName} />
                          <CertonaProvider errorLogger={props.errorLogger} {...certonaConfig()}>
                            <MarketingProvider
                              {...props}
                              {...pmcsMarketingProps}
                              MarketingComponent={MarketingComponent}
                            >
                              <ModelSizeProvider
                                modelSizes={modelSizes.length ? modelSizes[0] : []}
                                preSelectedModelSize={modelSize}
                              >
                                <StitchInverseStyleProviderCore invert={false}>
                                  <ScriptLoaderProvider>
                                    <ProductPageWithFlags {...props} />
                                  </ScriptLoaderProvider>
                                </StitchInverseStyleProviderCore>
                              </ModelSizeProvider>
                            </MarketingProvider>
                          </CertonaProvider>
                        </StitchStyleProvider>
                      </CustomThemeProvider>
                    </BrandMarketProvider>
                  </PersonalizationProvider>
                </LocalizationProvider>
              </BreakpointProvider>
            </FeatureFlagsProvider>
          </FeatureFlagsProviderFromCore>
        </ABSegProvider>
      </AppConfigProvider>
    </AppStateProvider>
  );
};

export default ProductPageApp;
