// @ts-nocheck
'use client';

import type { ProductDimension } from '@pdp/packages/types/dimensions';
import type { SyntheticEvent } from 'react';

import type { ReviewHistogram } from '../../state-builder/services/reviews';
import type { AvailabilityStatus } from './product-availability';
import type { ProductColorRaw, ProductImageRaw } from './style-level-raw';
import { InventoryStatusType } from '@/src/pages/services/capi-aggregation-service';
/**
 * @description
 * This type Represents page-level product data after being processed by server-side adapters.
 * Incoming data source is from the default productstyle-service response
 */
export type ProductData = ProductPriceStyleLevel & {
  bopisExclusionStyleDate?: { endDate: string; startDate: string };
  bopisInStock?: boolean;
  categoryId: string | null;
  colorLabel: string;
  crossSellInfo: string;
  currencyCode: string;
  currencySymbol: string;
  currentColorMainImage: string;
  currentMaxPrice: string;
  currentMinPrice: string;
  customerReviews: boolean;
  defaultVariantId: string;
  disabledComponentFIS: boolean;
  excludedFromPromotions: boolean;
  fitInformation: ProductFitInformation | Record<string, unknown>;
  flammableWarningText: string[];
  freeShipping: boolean;
  gidMessage: string;
  inStock: boolean;
  mergeType?: string;
  infoTabs: ProductInfoTabs;
  isBopisDisplayable?: boolean;
  isDropShip: boolean;
  isGiftCard: boolean;
  isDraprEnabled: string;
  isReserveInStore: boolean;
  marketingFlag: string | null;
  maxQuantityAllowed: number;
  name: string;
  price: ProductPriceStyleLevel | null;
  priceAdapter: Record<string, string | number | null>;
  primaryCategoryId: string;
  primarySellingStyleId: string;
  productImages: Record<string, ProductImageRaw>;
  productTitle: string;
  productType: ProductType;
  rating?: Rating | null;
  redirect?: boolean;
  redirectUrl?: string;
  reserveInStore: boolean;
  returnType?: string;
  reviewSummaries: Record<string, any>;
  selectedColor: ProductColorRaw | ProductColor;
  selectedSize: ProductSku | Record<string, unknown>;
  selectedVariant: ProductVariant;
  sizeGuideUrl: string;
  styleId: string;
  unselectedLabels: string[];
  variants: ProductVariant[];
  vendorId: string;
  vendorName: string;
};

export type ProductVariant = {
  bopisInStock?: boolean;
  colorGroups: ProductColorGroups;
  dimensions: ProductDimension[];
  dimensionGroups?: ProductDimension[];
  fitInformation?: ProductFitInformation;
  gidMessage: string;
  id: number | null;
  inStock?: boolean;
  infoTabs?: ProductInfoTabs;
  link?: string;
  name: string;
  price: ProductPriceStyleLevel | null;
  productStyleColors: ProductColorRaw[][];
  productTitle?: string;
  reviews?: ReviewHistogram | null;
  styleId?: string;
};

export type ProductMergedVariant = {
  id: number | null;
  link: string;
  name: string;
};

export type ProductColorGroups = {
  fullprice: ProductColorGroup[];
  hasMarketingFlag: boolean;
  markdown: ProductColorGroup[];
};

export type ProductSku = {
  availability?: AvailabilityStatus;
  backOrderDate: string | null;
  bopisInStock?: boolean;
  bopisInventoryStatusId?: number;
  dropshipFlags?: string[];
  inStock: boolean;
  inventoryStatusId: number;
  reservable: boolean;
  returnType: string;
  sizeDimension1: string;
  sizeDimension2?: string;
  skuId: string;
  upcCode: string;
};

export type ProductImage = {
  colorId: string;
  id: string;
  imageMapId: string;
  large: string;
  medium: string;
  small: string;
  thumbnail: string;
  video?: string;
  xlarge: string;
};

type ProductImageData = {
  large?: string;
  medium?: string;
  small?: string;
  thumbnail?: string;
  video?: string;
  xlarge?: string;
};

export type ProductImagesData = Record<string, ProductImageData>;

export type Rating = {
  averageRating: number;
  ratingCount: number;
  ratingHistogram?: number[];
  reviewCount: number;
  reviewHistogram?: number[];
};

export type SelectedColor = {
  businessCatalogItemId: string;
  colorName: string;
  colorPaletteSeasonCode: string;
  largeImagePath: string;
  price?: ProductPriceStyleLevel;
  productStyleColorImages: string[];
  rawCurrentPrice: number;
  rawRegularPrice: number;
  sizes: ProductSku[];
};

/**
 * @description
 * Values have been parsed for math operations.
 * Represents a pricing at the style level (all variants and colors) after going through server-side adapters.
 * This accounts for the entire price range of a given product.
 * Single prices are determined by color.
 */
export type ProductPriceStyleLevel = {
  currency: string;
  isPromoPrice: boolean;
  isRegularPrice: boolean;
  isSalePrice: boolean;
  localized_max_effective_price: string;
  localized_max_regular_price: string;
  localized_min_effective_price: string;
  localized_min_regular_price: string;
  max_discount_percentage: number;
  max_effective_price: number;
  max_regular_price: number;
  min_discount_percentage: number;
  min_effective_price: number;
  min_regular_price: number;
};

export type ProductInfoTabs = {
  fabric?: {
    bulletAttributes: string[];
    infoTabName: string;
  };
  overview?: {
    bulletAttributes: string[];
    copyAttributes: string[];
    infoTabName: string;
    modelSizes?: {
      height: string;
      size: string;
    }[][];
    notes: string[];
  };
};

export type ProductFitInformation = {
  attributes: string[];
  label: string;
  localizedTitle: string;
  show: boolean;
  url: string;
};

export type ProductType = {
  styleColorDisplayName: string;
};

export type ProductInfoTabsClick = (event: SyntheticEvent<HTMLButtonElement>) => void;
