import pdpStyleGroupPriceAdapter from '../pdp-color-group-price';

describe('PDP style price adapter', () => {
  let price;

  const defaultPrice = {
    currencySymbol: '$',
    percentageOff: '50',
    rawCurrentPrice: 24.94,
    rawRegularPrice: 24.0,
  };

  const getPriceData = (priceData = defaultPrice) => {
    price = pdpStyleGroupPriceAdapter(priceData);
  };

  beforeEach(() => {
    getPriceData();
  });

  afterEach(() => {
    price = null;
  });

  test('uses rawRegularPrice for both Regular Price variants', () => {
    expect(price.regularMaxPrice).toBe(24.0);
    expect(price.regularMinPrice).toBe(24.0);
  });

  test('uses rawCurrentPrice for both Current Price variants', () => {
    expect(price.currentMaxPrice).toBe(24.94);
    expect(price.currentMinPrice).toBe(24.94);
  });

  test('represents percentage as a number', () => {
    expect(price.maxPercentageOff).toBe(50);
  });

  test('uses currencySymbol for localizedCurrencySymbol', () => {
    expect(price.localizedCurrencySymbol).toBe('$');
  });

  describe('when give an invalid value for `percentageOff`', () => {
    describe('when null', () => {
      beforeEach(() => {
        getPriceData({ ...defaultPrice, percentageOff: null });
      });

      test('should set `maxPercentageOff` to 0', () => {
        expect(price.maxPercentageOff).toBe(0);
      });

      test('should set `minPercentageOff` to 0', () => {
        expect(price.maxPercentageOff).toBe(0);
      });
    });

    describe('when undefined', () => {
      beforeEach(() => {
        getPriceData({ ...defaultPrice, percentageOff: undefined });
      });

      test('should set `maxPercentageOff` to 0', () => {
        expect(price.maxPercentageOff).toBe(0);
      });

      test('should set `minPercentageOff` to 0', () => {
        expect(price.maxPercentageOff).toBe(0);
      });
    });
  });
});
