import type { ProductMergedVariant, ProductVariant } from '@pdp/types/product-data/style-level'; // NOSONAR
import type { ProductVariantRaw } from '@pdp/types/product-data/style-level-raw'; // NOSONAR
import type { Locale } from '@ecom-next/utils/server';
import { priceTypeFormatter } from '../../../../capi-utils/response-adapter/helper/price/get-price';
import { getProductStylePrice } from '../../../../capi-utils/response-adapter/helper/price/get-product-style-price';
import colorGroupsAdapter from './color-groups';
import fitInformationAdapter from './fit-information';
import type { PriceData } from './pdp-style-price';
import pdpStylePrice from './pdp-style-price';
import sizeDimensionsAdapter from './size-dimensions';

export const isMergedVariant = (variant: ProductVariantRaw): boolean => !!variant.link;

/**
 * @param {string} variantLink
 * @desc - if valid vid is present in merged variant link, return as number, else null
 * @returns {number} || null
 */

const getVariantIdFromLink = (variantLink = ''): number | null => {
  if (!variantLink || variantLink === '') {
    return null;
  }
  const queryString = variantLink?.split('vid=');
  const vid = queryString.length > 1 ? queryString[1].split('&')[0] : null;

  return vid && parseInt(vid, 10) ? parseInt(vid, 10) : null;
};

const mergedVariant = (variant: ProductVariantRaw): ProductMergedVariant => ({
  id: getVariantIdFromLink(variant.link),
  link: variant.link || '',
  name: variant.name || '',
});

const regularVariant = (variant: ProductVariantRaw, withSize: boolean, locale: string, sizeGuideModalFlag: boolean, sizeChartId: string): ProductVariant => {
  const { fitInformation } = fitInformationAdapter({
    fitInfo: variant?.fitInformation,
    locale,
    sizeChartId,
    sizeGuideModalFlag,
  });

  let stylePrice;
  if (variant.price) {
    const priceType = priceTypeFormatter(variant?.price?.priceTypes);
    const priceData = {
      ...getProductStylePrice(variant?.price, locale as Locale),
      priceType,
    };
    stylePrice = pdpStylePrice(priceData as PriceData, priceType);
  }

  const parsedVariant = {
    colorGroups: colorGroupsAdapter(variant, locale as Locale, sizeChartId, sizeGuideModalFlag),
    dimensions: sizeDimensionsAdapter(variant),
    fitInformation: variant.fitInformation ? fitInformation : null,
    gidMessage: variant.gidMessage || '',
    id: typeof variant.variantId === 'string' ? parseInt(variant.variantId, 10) : variant.variantId,
    infoTabs: variant.infoTabs || null,
    name: variant.name || '',
    price: stylePrice || null,
    productStyleColors: variant.productStyleColors || [],
    productTitle: variant.productTitle || '',
    reviews: variant.reviews,
    styleId: variant.styleId || '',
  } as ProductVariant;

  if (withSize) {
    parsedVariant.inStock = typeof variant.inStock === 'undefined' || variant.inStock;
  }

  return parsedVariant;
};

/**
 * maps a page variant object to the shape defined in variant/types.js
 * @param {Object} variant // https://github.gapinc.com/pages/ecomfrontend/gap-future/patterns/pdp-api-data.html#variants
 * @returns {Object}
 */
export default function (
  variant: ProductVariantRaw,
  withSize = false,
  locale: string,
  sizeGuideModalFlag: boolean,
  sizeChartId: string
): ProductVariant | ProductMergedVariant {
  return isMergedVariant(variant) ? mergedVariant(variant) : regularVariant(variant, withSize, locale, sizeGuideModalFlag, sizeChartId);
}
