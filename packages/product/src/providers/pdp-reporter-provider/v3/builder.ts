// @ts-nocheck
'use client';

import { getProductPageType } from '../../../util/get-merge-type';
import { getProductInventoryStatusProp } from '../../../providers/pdp-reporter-provider/v3/get-inventory-status';
import { getOutOfStockItems } from './get-out-of-stock-items';
import { lpoData } from './lpo-data';

const mvgProductAttributes = props => {
  const {
    pageType,
    brand,
    customer_choices,
    styles,
    variants,
    metadata,
    draprEnabled,
    isFitOnly,
    selectedNodes = [],
    params: { cid = '', autosuggest = false, searchText = '', position = -1, results = 0 },
    breadcrumbs,
    dataLayerEngagement,
    featureConfigs,
    findMineEngagement,
    isDropShip,
    template,
  } = props || { featureConfigs: {} };

  const aiReviews = featureConfigs?.aiReviews;

  const firstChoiceId = Object.keys(customer_choices)[0];
  const customerChoice = customer_choices[firstChoiceId];
  const style = styles && styles[customerChoice.style_id];

  const brandName = brand;
  const productName = style?.description;
  const selectedColor = {
    description: customerChoice.description,
    colorPaletteSeasonCode: customerChoice.color_palette_season_code,
    marketingFlag: customerChoice.marketing_flags?.[0]?.content || null,
    businessCatalogItemId: customerChoice.alternate_ids.universal_customer_choice_number,
  };

  const variantData = variants[Object.keys(variants)[0]];
  const variantSkus = variantData.customer_choices[firstChoiceId]?.skus || [];

  const { name: divisionName } = selectedNodes.find(node => node.type === 'division' || node.type === 'sub-division') || {};

  const { name: categoryName } = selectedNodes.find(node => node.type === 'category' || node.type === 'sale') || { name: '' };

  const primaryCategoryName = breadcrumbs?.category?.name || '';
  const divisionNameFromBreadcrums = `${props?.breadcrumbs?.division?.name !== undefined ? `${props.breadcrumbs.division.name}` : ''}`;

  const pageName = `${divisionName || divisionNameFromBreadcrums}:${categoryName}:${cid}:product:${`${productName || primaryCategoryName}`}:${selectedColor.description}`;

  const sizesOutOfStock = getOutOfStockItems(variants);
  const productInventoryStatusProp = getProductInventoryStatusProp(variantSkus);

  const hasMarketingFlag = Array.isArray(customerChoice.marketing_flags) && customerChoice.marketing_flags.length > 0;

  const marketing_flag_data = hasMarketingFlag
    ? { product_marketing_flag: customerChoice.marketing_flags?.[0]?.content ?? 'No flag' }
    : { product_marketing_flag: 'No flag' };

  const marketing_flag_session_code_data = customerChoice.color_palette_season_code
    ? {
        product_season_code: customerChoice.color_palette_season_code,
      }
    : {};

  const vendorDetails = customerChoice.vendor_details || {};

  const productObject = {
    channel: `${brandName}:${divisionName || divisionNameFromBreadcrums}`,
    page_name: `${brandName}:browse:${pageName}`,
    page_type: `${pageType.charAt(0).toUpperCase()}${pageType?.substring(1)}`,
    pdp_oos_size: `${sizesOutOfStock}`,
    product_brand: [`${brandName.toUpperCase()}`],
    product_category: [`${categoryName}`],
    product_cc_id: [`${selectedColor.businessCatalogItemId}`],
    product_mvg_id: [`${metadata.variant_group_id}`],
    product_name: [`${productName}`],
    product_page_type: getProductPageType(metadata.variant_group, metadata.includes_merged_style),
    product_primary_category: [`${primaryCategoryName}`],
    ...productInventoryStatusProp,
    ...marketing_flag_data,
    ...marketing_flag_session_code_data,
    product_dropship: [`${isDropShip}`],
    product_seller_id: [vendorDetails.vendor_number || ''],
    product_seller_name: [vendorDetails.vendor_name || ''],
    product_variant_selected: [variantData.variantKey],
    product_price: [variantData.price?.min_regular_price],
  };

  const autosuggestVisualObject = autosuggest
    ? {
        internal_search_autosuggest_flag: autosuggest,
        internal_search_keyword: `${brandName}:autosuggest_visual:${searchText} -> position ${position}`,
        internal_search_results: results,
      }
    : null;

  const draprTrackingInfo = draprEnabled
    ? {
        drapr_interaction: 'default prodview',
      }
    : null;

  const draprVersion = isFitOnly
    ? {
        drapr_version: 'fit-only',
      }
    : null;

  const dataLayerInfo = dataLayerEngagement && {
    ...dataLayerEngagement.starRatingsData,
    ...dataLayerEngagement.badgingData,
  };

  const { display: isAIReviewSummaryPresent, enabled: isAIReviewSummaryEnabled, modelNameForTracking: aiReviewSummaryModel } = aiReviews || {};

  const reviewSummaryReporting = isAIReviewSummaryEnabled
    ? {
        ai_review_summary_model: isAIReviewSummaryPresent ? [aiReviewSummaryModel] : ['summary not available'],
      }
    : null;

  const findMineBuyClick = findMineEngagement
    ? {
        event_name: 'findmine_click',
        findmine_engagement: 'Product.do : FindMine : W1',
        findmine_name_rank: 'Product.do : FindMine : W1',
      }
    : null;

  return {
    ...productObject,
    ...autosuggestVisualObject,
    ...dataLayerInfo,
    ...draprTrackingInfo,
    ...findMineBuyClick,
    ...draprVersion,
    ...lpoData(template, variantSkus),
    ...reviewSummaryReporting,
  };
};

export { mvgProductAttributes };

export default ({ reporterContext, ...props }) => {
  const initProductData = props.productAttributesResult || mvgProductAttributes(props);

  const hasDrapr = props.draprEnabled;
  const draprData = {
    drapr_interaction: 'default prodview',
    event_name: 'default-product-view',
    product_brand: initProductData.product_brand,
    product_id: initProductData.product_id,
    product_name: initProductData.product_name,
    product_primary_category: initProductData.product_primary_category,
  };

  const draprVersion = props.isFitOnly ? { drapr_version: 'fit-only' } : {};
  reporterContext.add({
    data: initProductData,
    name: 'product mvg',
  });
  hasDrapr && reporterContext.link({ ...draprData, ...draprVersion });
};
