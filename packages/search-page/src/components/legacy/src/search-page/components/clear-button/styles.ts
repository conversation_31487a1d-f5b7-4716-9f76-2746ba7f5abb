// @ts-nocheck
import { Brands, css, forBrands, getFont, getFontWeight } from "@ecom-next/core/react-stitch";
import { DefaultStyleFn } from './types';

export const getDefaultStyles: DefaultStyleFn = ({ theme, isXLarge, isBRRedesign2023Enabled }) => {
  const isBR =
    theme.brand === Brands.BananaRepublic || theme.brand === Brands.BananaRepublicFactoryStore;
  const isAt = theme.brand === Brands.Athleta;

  const brandStyles = forBrands(theme, {
    gap: theme => css`
      ${getFont('primary')({ theme })}
      color: ${theme.color.b2};
      font-size: 0.8rem;
      padding: 0;
    `,
    gapfs: theme => css`
      ${getFont('primary')({ theme })}
      color: ${theme.color.b2};
      font-size: 0.8rem;
      padding: 0;
    `,
    on: theme => css`
      ${getFont('secondary')({ theme })}
      color: ${theme.color.b1};
      font-size: 0.8rem;
      padding: 1px 0 0 0;
    `,
    br: theme => css`
      ${getFont('primary')({ theme })}
      border: none;
      font-size: 0.75rem;
      line-height: 0.875rem;
      font-weight: ${isBRRedesign2023Enabled ? getFontWeight('light') : getFontWeight('demiBold')};
      text-transform: capitalize;
      padding: 0;
      letter-spacing: 0.3px;
      color: ${theme.color.b2};
      text-decoration: ${isBRRedesign2023Enabled ? 'underline' : null};
      text-underline-offset: ${isBRRedesign2023Enabled ? '4px' : null};
    `,
    brfs: theme => css`
      ${getFont('primary')({ theme })}
      border: none;
      font-size: 0.75rem;
      line-height: 0.875rem;
      font-weight: ${isBRRedesign2023Enabled ? getFontWeight('light') : getFontWeight('demiBold')};
      text-transform: capitalize;
      padding: 0;
      letter-spacing: 0.3px;
      color: ${theme.color.b2};
      text-decoration: ${isBRRedesign2023Enabled ? 'underline' : null};
      text-underline-offset: ${isBRRedesign2023Enabled ? '4px' : null};
    `,
    at: theme => css`
      ${getFont('secondary')({ theme })}
      color: ${theme.color.g1};
      letter-spacing: 1px;
      font-size: 0.8125rem;
      padding: 0.25rem 0;
    `,
  });

  return css`
    ${theme.utilities.unbuttonize}
    display: inline-block;
    letter-spacing: 0.0625em;
    padding: 3px;
    width: auto;
    cursor: pointer;
    text-transform: uppercase;
    ${brandStyles}
    ${!isXLarge && !isBR && !isAt && 'font-size: 1.2rem'};
  `;
};

export const getModalStyles: DefaultStyleFn = ({ theme }) => {
  const brandStyles = forBrands(theme, {
    gap: theme => css`
      ${getFont('secondary')({ theme })}
      border: 2px solid ${theme.color.b2};
      color: ${theme.color.b2};
      font-size: 1.067rem;
      padding: 0.59em;
    `,
    gapfs: theme => css`
      ${getFont('secondary')({ theme })}
      border: 2px solid ${theme.color.b2};
      color: ${theme.color.b2};
      font-size: 1.067rem;
      padding: 0.59em;
    `,
    on: theme => css`
      ${getFont('secondary')({ theme })}
      background-color: ${theme.color.g2};
      color: #ffffff;
      font-size: 1.2rem;
      padding: 0.5em;
      &:hover {
        background-color: ${theme.color.g3};
      }
    `,
    br: theme => css`
      ${getFont('secondary')({ theme })}
      font-size: 1.0625rem;
      padding: 0.48em;
      line-height: 1.25;
      background-color: ${theme.color.alpha00};
      border: 1px solid ${theme.color.g1};
      &:hover {
        color: ${theme.color.gray80};
        border-color: ${theme.color.gray80};
      }
    `,
    brfs: theme => css`
      ${getFont('secondary')({ theme })}
      font-size: 1.0625rem;
      padding: 0.48em;
      line-height: 1.25;
      background-color: ${theme.color.alpha00};
      border: 1px solid ${theme.color.g1};
      &:hover {
        color: ${theme.color.gray80};
        border-color: ${theme.color.gray80};
      }
    `,
    at: theme => css`
      background-color: ${theme.color.g2};
      color: #ffffff;
      font-size: 1.2rem;
      padding: 0.52em;
      &:hover {
        background-color: ${theme.color.gray50};
      }
      &[disabled] {
        background-color: ${theme.color.g5};
      }
    `,
  });

  return css`
    text-align: center;
    padding: 0.5rem;
    max-width: 8.75rem;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    &[disabled] {
      opacity: 0.5;
    }
    ${brandStyles}
  `;
};
