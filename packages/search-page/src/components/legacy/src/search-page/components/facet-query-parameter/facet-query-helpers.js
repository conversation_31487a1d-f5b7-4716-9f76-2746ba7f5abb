// @ts-nocheck
import { getHashKeysAndValuesFromURL } from '@ecom-next/core/url-helper';
import facetConfig, { defaultConfig } from '../facet-adapter/config';

const updateFacetList = (previousState = [], value) => {
  const optionWasSelected = previousState.includes(value);
  return optionWasSelected
    ? previousState.filter(element => element !== value)
    : [...previousState, value];
};

const rebuildObjectWithoutEmptyListValues = (object = {}) =>
  Object.fromEntries(
    Object.keys(object)
      .map(key => [key, object[key]])
      .filter(([, facetValue]) => facetValue.length > 0)
      .map(([key, value]) => [key, value]),
  );

const updateSingleSelectFacetList = (previousState = [], value) => {
  const optionWasSelected = previousState.includes(value);
  return optionWasSelected ? [] : [value];
};

const getSizeKeyValue = selectedSize => {
  const [sizeKey, ...sizeValues] = selectedSize.split(':');
  let [fullSizeValue] = sizeValues;
  const hasMultipleValues = sizeValues.length > 1;
  if (hasMultipleValues) {
    fullSizeValue = sizeValues.join(':');
  }
  return [sizeKey, fullSizeValue];
};

const updateCorrectlyKeyedSizeFacet = (previousState = {}, incomingValue) => {
  const [sizeKey, sizeValue] = getSizeKeyValue(incomingValue);
  if (!sizeKey || !sizeValue) {
    return previousState;
  }
  const updatedState = { ...previousState };
  if (updatedState[sizeKey]) {
    updatedState[sizeKey] = updateFacetList(updatedState[sizeKey], sizeValue);
  } else {
    updatedState[sizeKey] = [sizeValue];
  }
  return rebuildObjectWithoutEmptyListValues(updatedState);
};
const removeSizeFacetFromAllKeys = (previousState = {}, incomingValue) => {
  const updatedState = { ...previousState };

  Object.keys(updatedState).forEach(key => {
    updatedState[key] = updatedState[key].filter(value => value !== incomingValue);
  });
  return rebuildObjectWithoutEmptyListValues(updatedState);
};

const updateQuickFilterSizeFacet = (applied, value, updatedState, sizeOptionKey) => {
  if (updatedState[sizeOptionKey]) {
    updatedState[sizeOptionKey] =
      applied === 'true'
        ? updateFacetList(updatedState[sizeOptionKey], value)
        : updatedState[sizeOptionKey].filter(element => element !== value);
  } else {
    if (applied === 'true') {
      updatedState[sizeOptionKey] = [value];
    }
  }
  return rebuildObjectWithoutEmptyListValues(updatedState);
};
const handleQuickFilterSizeFacet = (applied, multipleSizeOptions, updatedState) => {
  let updatedSizeFacet;
  multipleSizeOptions.forEach(option => {
    const [sizeOptionKey, sizeOptionValue] = getSizeKeyValue(option);
    if (sizeOptionValue.includes(',')) {
      const sizeOptionValueArray = sizeOptionValue.split(',');
      sizeOptionValueArray.forEach(value => {
        updatedSizeFacet = updateQuickFilterSizeFacet(applied, value, updatedState, sizeOptionKey);
      });
    } else {
      updatedSizeFacet = updateQuickFilterSizeFacet(
        applied,
        sizeOptionValue,
        updatedState,
        sizeOptionKey,
      );
    }
  });
  return updatedSizeFacet;
};
const updateSizeFacet = (previousState = {}, incomingValue) => {
  let updatedSizeFacet;
  const INVALID_SIZE_KEY = 'undefined-undefined';
  const applied = incomingValue.includes('&&') && incomingValue.split('&&')[0];
  const sizeFacetValue = incomingValue.includes('&&')
    ? incomingValue.split('&&')[1]
    : incomingValue;
  if (sizeFacetValue.includes('|')) {
    const multipleSizeOptions = sizeFacetValue.split('|');

    const updatedState = { ...previousState };

    return handleQuickFilterSizeFacet(applied, multipleSizeOptions, updatedState);
  } else {
    const [sizeKey, sizeValue] = getSizeKeyValue(sizeFacetValue);
    if (sizeValue.includes(',')) {
      const sizeOptionValueArray = sizeValue.split(',');
      sizeOptionValueArray.forEach(value => {
        updatedSizeFacet = updateQuickFilterSizeFacet(applied, value, previousState, sizeKey);
      });
    } else {
      updatedSizeFacet =
        sizeKey === INVALID_SIZE_KEY
          ? removeSizeFacetFromAllKeys(previousState, sizeValue)
          : updateCorrectlyKeyedSizeFacet(previousState, sizeFacetValue);
    }
    return updatedSizeFacet;
  }
};

const updateFacetRange = (previousState, value) => value;

export const determineFacetMethodHelper = (facetName, methods) => {
  const { type = {}, selectionType = {} } = facetConfig[facetName] || defaultConfig;
  return methods?.[type]?.[selectionType];
};

export const determineUpdatedFacetState = facetName =>
  determineFacetMethodHelper(facetName, {
    simple: { 'single-select': updateSingleSelectFacetList, 'multi-select': updateFacetList },
    range: { 'single-select': updateFacetRange },
    complex: { size: updateSizeFacet },
  });

const getQueryParameterRepresentationFromList = list => list.join(',');

const getQueryParameterRepresentationFromObject = (object = {}) => {
  let queryParamString = '';
  Object.keys(object).forEach(key => {
    let keyDelimeter = '';
    if (queryParamString.length > 0) {
      keyDelimeter = '|';
    }
    queryParamString += `${keyDelimeter}${key}:${object[key].join(',')}`;
  });
  return queryParamString;
};

const getQueryParameterFromRangeValueRepresentation = value => value;

export const convertFacetValueToStringForQueryParameter = facetName =>
  determineFacetMethodHelper(facetName, {
    simple: {
      'single-select': getQueryParameterRepresentationFromList,
      'multi-select': getQueryParameterRepresentationFromList,
    },
    range: { 'single-select': getQueryParameterFromRangeValueRepresentation },
    complex: { size: getQueryParameterRepresentationFromObject },
  });

export const addFacetToFetchParameter = (initialFetchParameter, [facetType, value]) =>
  value && value.length !== 0
    ? {
        ...initialFetchParameter,
        [facetType]: convertFacetValueToStringForQueryParameter(facetType)(value),
      }
    : initialFetchParameter;

const convertCSVStringToList = csvList => csvList && csvList.split(',');

const convertDepartmentQueryStringToList = departmentString =>
  departmentString && [departmentString];

const convertSizeStringToObject = sizeString => {
  if (!sizeString) {
    return sizeString;
  }
  const updatedSize = {};
  const keys = sizeString.split('|');
  keys.forEach(keyString => {
    const [sizeKey, sizeValue] = getSizeKeyValue(keyString);
    const sizeValues = sizeValue.split(',');
    if (sizeKey && sizeValues) {
      updatedSize[sizeKey] = sizeValues;
    }
  });
  return updatedSize;
};

export const convertHashParameterValueToFacetType = facetName =>
  determineFacetMethodHelper(facetName, {
    simple: {
      'single-select': convertDepartmentQueryStringToList,
      'multi-select': convertCSVStringToList,
    },
    range: { 'single-select': stringRange => stringRange },
    complex: { size: convertSizeStringToObject },
  });

export const getFacetStateAfterHashChange = (valueFromHash, facetType) => {
  const extractedValue = convertHashParameterValueToFacetType(facetType)(valueFromHash);
  if (!extractedValue) {
    return;
  }
  return extractedValue;
};

export const buildStateObjectFromFacetsInUrl = (url, removeNonFacetKeysFromHashObject) => {
  const hashKeysAndValues = removeNonFacetKeysFromHashObject(getHashKeysAndValuesFromURL(url));
  const resultState = {};
  Object.keys(hashKeysAndValues)
    .map(key => [key, hashKeysAndValues[key]])
    .forEach(([key, value]) => {
      const updatedResultStateForKey = convertHashParameterValueToFacetType(key)(value);
      if (updatedResultStateForKey && updatedResultStateForKey !== '') {
        resultState[key] = updatedResultStateForKey;
      }
    });
  return resultState;
};

export const didFacetsInUrlChange = (newURL, oldURL, removeNonFacetKeysFromHashObject) => {
  const oldHashKeysAndValues = removeNonFacetKeysFromHashObject(
    getHashKeysAndValuesFromURL(oldURL),
  );
  const hashKeysAndValues = removeNonFacetKeysFromHashObject(getHashKeysAndValuesFromURL(newURL));

  return JSON.stringify(oldHashKeysAndValues) !== JSON.stringify(hashKeysAndValues);
};

export function getFacetFetchParametersFromStates(facetStates) {
  return Object.keys(facetStates)
    .map(key => [key, facetStates[key]])
    .reduce(addFacetToFetchParameter, {});
}
