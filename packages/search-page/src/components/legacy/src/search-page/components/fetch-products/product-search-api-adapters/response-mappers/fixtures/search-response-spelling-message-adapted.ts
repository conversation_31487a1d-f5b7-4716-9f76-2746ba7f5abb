// @ts-nocheck
import { LegacyPDSSearchResponse } from '../../../types';
export const searchedResponseAdapted: LegacyPDSSearchResponse = {
  productCategoryFacetedSearch: {
    autoCorrectedText: 'jeans',
    productCategory: {
      productCategoryPaginator: { pageNumberRequested: '0', pageNumberTotal: '1' },
    },
    searchFacetInfo: {
      searchFacetList: [
        {
          isActive: 'true',
          searchFacetId: 'department',
          searchFacetName: 'Department',
          searchFacetOptionGroupList: {
            searchFacetOptionList: [
              {
                isActive: 'true',
                isSelected: 'false',
                searchFacetOptionId: 'Baby Boys',
                searchFacetOptionName: 'Baby Boys',
                searchFacetOptionValue: 'Baby Boys',
              },
              {
                isActive: 'true',
                isSelected: 'true',
                searchFacetOptionId: 'Women',
                searchFacetOptionName: 'Women',
                searchFacetOptionValue: 'Women',
              },
            ],
          },
        },
        {
          isActive: 'true',
          searchFacetId: 'price',
          searchFacetName: 'Price',
          searchFacetOptionGroupList: {
            searchFacetOptionList: [
              {
                isActive: 'true',
                searchFacetOptionId: 'MIN',
                searchFacetOptionName: '10',
                searchFacetOptionValue: '10',
              },
              {
                isActive: 'true',
                searchFacetOptionId: 'MAX',
                searchFacetOptionName: '70',
                searchFacetOptionValue: '70',
              },
              {
                isActive: 'true',
                searchFacetOptionId: 'MIN_SELECTED',
                searchFacetOptionName: 'MIN_SELECTED',
                searchFacetOptionValue: '10',
              },
              {
                isActive: 'true',
                searchFacetOptionId: 'MAX_SELECTED',
                searchFacetOptionName: 'MAX_SELECTED',
                searchFacetOptionValue: '70',
              },
            ],
          },
        },
        {
          isActive: 'true',
          searchFacetId: 'color',
          searchFacetName: 'Color',
          searchFacetOptionGroupList: {
            searchFacetOptionList: [
              {
                isActive: 'true',
                isSelected: 'false',
                searchFacetOptionId: 'red',
                searchFacetOptionName: 'red',
                searchFacetOptionValue: 'red',
              },
              {
                isActive: 'true',
                isSelected: 'false',
                searchFacetOptionId: 'orange',
                searchFacetOptionName: 'orange',
                searchFacetOptionValue: 'orange',
              },
            ],
          },
        },
        {
          isActive: 'true',
          searchFacetId: 'reviewScore',
          searchFacetName: 'Review Score',
          searchFacetOptionGroupList: {
            searchFacetOptionList: [
              {
                isActive: 'true',
                isSelected: 'false',
                searchFacetOptionId: '4',
                searchFacetOptionImagePath: '4',
                searchFacetOptionName: '4',
                searchFacetOptionValue: '4 & up',
              },
              {
                isActive: 'true',
                isSelected: 'false',
                searchFacetOptionId: '3',
                searchFacetOptionImagePath: '3',
                searchFacetOptionName: '3',
                searchFacetOptionValue: '3 & up',
              },
              {
                isActive: 'true',
                isSelected: 'false',
                searchFacetOptionId: '2',
                searchFacetOptionImagePath: '2',
                searchFacetOptionName: '2',
                searchFacetOptionValue: '2 & up',
              },
              {
                isActive: 'true',
                isSelected: 'false',
                searchFacetOptionId: '1',
                searchFacetOptionImagePath: '1',
                searchFacetOptionName: '1',
                searchFacetOptionValue: '1 & up',
              },
            ],
          },
        },
      ],
    },
    spellingSuggestions: ['jean'],
    totalItemCount: '9',
  },
};
