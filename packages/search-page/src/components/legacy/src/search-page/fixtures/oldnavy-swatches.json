{"resourceVersion": "1.1", "resourceUrl": "/resources/bloomreach/productSearch/v1/search?cid=undefined&isFacetsEnabled=true&globalShippingCountryCode=&globalShippingCurrencyCode=&locale=en_US&pageId=0", "productCategoryFacetedSearch": {"areAllSubCatsOutfit": "false", "isOutfitCategory": "false", "searchDivName": "", "isRedirect": "false", "searchText": "red pants", "totalItemCount": 8, "searchFacetInfo": {"searchFacetList": [{"isActive": "true", "searchFacetId": "department", "searchFacetName": "Department", "searchFacetOptionGroupList": {"searchFacetOptionList": [{"isSelected": "true", "isActive": "true", "searchFacetOptionId": "Women", "searchFacetOptionName": "women", "searchFacetOptionValue": "women", "tagDisplayLabel": "women"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "Men", "searchFacetOptionName": "men", "searchFacetOptionValue": "men"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "Girls", "searchFacetOptionName": "girls", "searchFacetOptionValue": "girls"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "Boys", "searchFacetOptionName": "boys", "searchFacetOptionValue": "boys"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "Baby Girls", "searchFacetOptionName": "baby girls", "searchFacetOptionValue": "baby girls"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "Baby Boys", "searchFacetOptionName": "baby boys", "searchFacetOptionValue": "baby boys"}]}}, {"isActive": "true", "searchFacetId": "style", "searchFacetName": "Style", "searchFacetOptionGroupList": {"searchFacetOptionList": [{"isSelected": "false", "isActive": "true", "searchFacetOptionId": "Active Bottoms", "searchFacetOptionName": "Active Bottoms", "searchFacetOptionValue": "Active Bottoms"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "<PERSON>ts", "searchFacetOptionName": "<PERSON>ts", "searchFacetOptionValue": "<PERSON>ts"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "Shorts", "searchFacetOptionName": "Shorts", "searchFacetOptionValue": "Shorts"}]}}, {"isActive": "true", "searchFacetId": "sleeve length", "searchFacetName": "Sleeve <PERSON>", "searchFacetOptionGroupList": {"searchFacetOptionList": [{"isSelected": "true", "isActive": "true", "searchFacetOptionId": "Long Sleeve", "searchFacetOptionName": "Long Sleeve", "searchFacetOptionValue": "Long Sleeve"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "<PERSON>ve", "searchFacetOptionName": "<PERSON>ve", "searchFacetOptionValue": "<PERSON>ve"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "<PERSON><PERSON><PERSON><PERSON>", "searchFacetOptionName": "<PERSON><PERSON><PERSON><PERSON>", "searchFacetOptionValue": "<PERSON><PERSON><PERSON><PERSON>"}]}}, {"isActive": "true", "searchFacetId": "size", "searchFacetName": "Size", "searchFacetOptionGroupList": [{"searchFacetOptionGroupId": "bottoms", "searchFacetOptionGroupName": "bottoms", "searchFacetOptionGroupList": [{"searchFacetOptionGroupId": "size", "searchFacetOptionGroupName": "size", "parentSearchFacetOptionGroupId": "bottoms", "searchFacetOptionGroupList": [{"searchFacetOptionGroupId": "3", "searchFacetOptionGroupName": "petite", "parentSearchFacetOptionGroupId": "size", "searchFacetOptionList": [{"searchFacetOptionId": "_bottoms_size_petite_xs", "searchFacetOptionValue": "_bottoms_size_petite_xs", "searchFacetOptionName": "xs", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite"}, {"searchFacetOptionId": "_bottoms_size_petite_00_xxs/xs", "searchFacetOptionValue": "_bottoms_size_petite_00_xxs/xs", "searchFacetOptionName": "00", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "xxs/xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_0_xs", "searchFacetOptionValue": "_bottoms_size_petite_0_xs", "searchFacetOptionName": "0", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_2_xs", "searchFacetOptionValue": "_bottoms_size_petite_2_xs", "searchFacetOptionName": "2", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_4_s", "searchFacetOptionValue": "_bottoms_size_petite_4_s", "searchFacetOptionName": "4", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "s", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_6_s", "searchFacetOptionValue": "_bottoms_size_petite_6_s", "searchFacetOptionName": "6", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "s", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_8_m", "searchFacetOptionValue": "_bottoms_size_petite_8_m", "searchFacetOptionName": "8", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "m", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_10_m", "searchFacetOptionValue": "_bottoms_size_petite_10_m", "searchFacetOptionName": "10", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "m", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_12_l", "searchFacetOptionValue": "_bottoms_size_petite_12_l", "searchFacetOptionName": "12", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "l", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_14_l", "searchFacetOptionValue": "_bottoms_size_petite_14_l", "searchFacetOptionName": "14", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "l", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_16_xl", "searchFacetOptionValue": "_bottoms_size_petite_16_xl", "searchFacetOptionName": "16", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "xl", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_18_xl", "searchFacetOptionValue": "_bottoms_size_petite_18_xl", "searchFacetOptionName": "18", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "xl", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_20_xxl", "searchFacetOptionValue": "_bottoms_size_petite_20_xxl", "searchFacetOptionName": "20", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "xxl", "variantId": 1, "dimensionId": 1}}]}, {"searchFacetOptionGroupId": "1", "searchFacetOptionGroupName": "regular", "parentSearchFacetOptionGroupId": "size", "searchFacetOptionList": [{"searchFacetOptionId": "_bottoms_size_regular_s", "searchFacetOptionValue": "_bottoms_size_regular_s", "searchFacetOptionName": "s", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular"}, {"searchFacetOptionId": "_bottoms_size_regular_m", "searchFacetOptionValue": "_bottoms_size_regular_m", "searchFacetOptionName": "m", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular"}, {"searchFacetOptionId": "_bottoms_size_regular_l", "searchFacetOptionValue": "_bottoms_size_regular_l", "searchFacetOptionName": "l", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular"}, {"searchFacetOptionId": "_bottoms_size_regular_xl", "searchFacetOptionValue": "_bottoms_size_regular_xl", "searchFacetOptionName": "xl", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular"}, {"searchFacetOptionId": "_bottoms_size_regular_xxl", "searchFacetOptionValue": "_bottoms_size_regular_xxl", "searchFacetOptionName": "xxl", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular"}, {"searchFacetOptionId": "_bottoms_size_regular_00_xxs/xs", "searchFacetOptionValue": "_bottoms_size_regular_00_xxs/xs", "searchFacetOptionName": "00", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "xxs/xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_0_xs", "searchFacetOptionValue": "_bottoms_size_regular_0_xs", "searchFacetOptionName": "0", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_2_xs", "searchFacetOptionValue": "_bottoms_size_regular_2_xs", "searchFacetOptionName": "2", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_4_s", "searchFacetOptionValue": "_bottoms_size_regular_4_s", "searchFacetOptionName": "4", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "s", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_6_s", "searchFacetOptionValue": "_bottoms_size_regular_6_s", "searchFacetOptionName": "6", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "s", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_8_m", "searchFacetOptionValue": "_bottoms_size_regular_8_m", "searchFacetOptionName": "8", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "m", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_10_m", "searchFacetOptionValue": "_bottoms_size_regular_10_m", "searchFacetOptionName": "10", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "m", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_12_l", "searchFacetOptionValue": "_bottoms_size_regular_12_l", "searchFacetOptionName": "12", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "l", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_14_l", "searchFacetOptionValue": "_bottoms_size_regular_14_l", "searchFacetOptionName": "14", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "l", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_16_xl", "searchFacetOptionValue": "_bottoms_size_regular_16_xl", "searchFacetOptionName": "16", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "xl", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_18_xl", "searchFacetOptionValue": "_bottoms_size_regular_18_xl", "searchFacetOptionName": "18", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "xl", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_20_xxl", "searchFacetOptionValue": "_bottoms_size_regular_20_xxl", "searchFacetOptionName": "20", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "xxl", "variantId": 1, "dimensionId": 1}}]}, {"searchFacetOptionGroupId": "2", "searchFacetOptionGroupName": "tall", "parentSearchFacetOptionGroupId": "size", "searchFacetOptionList": [{"searchFacetOptionId": "_bottoms_size_tall_s", "searchFacetOptionValue": "_bottoms_size_tall_s", "searchFacetOptionName": "s", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall"}, {"searchFacetOptionId": "_bottoms_size_tall_xl", "searchFacetOptionValue": "_bottoms_size_tall_xl", "searchFacetOptionName": "xl", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall"}, {"searchFacetOptionId": "_bottoms_size_tall_00_xxs/xs", "searchFacetOptionValue": "_bottoms_size_tall_00_xxs/xs", "searchFacetOptionName": "00", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "xxs/xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_0_xs", "searchFacetOptionValue": "_bottoms_size_tall_0_xs", "searchFacetOptionName": "0", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_2_xs", "searchFacetOptionValue": "_bottoms_size_tall_2_xs", "searchFacetOptionName": "2", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_4_s", "searchFacetOptionValue": "_bottoms_size_tall_4_s", "searchFacetOptionName": "4", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "s", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_6_s", "searchFacetOptionValue": "_bottoms_size_tall_6_s", "searchFacetOptionName": "6", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "s", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_8_m", "searchFacetOptionValue": "_bottoms_size_tall_8_m", "searchFacetOptionName": "8", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "m", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_10_m", "searchFacetOptionValue": "_bottoms_size_tall_10_m", "searchFacetOptionName": "10", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "m", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_12_l", "searchFacetOptionValue": "_bottoms_size_tall_12_l", "searchFacetOptionName": "12", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "l", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_14_l", "searchFacetOptionValue": "_bottoms_size_tall_14_l", "searchFacetOptionName": "14", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "l", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_16_xl", "searchFacetOptionValue": "_bottoms_size_tall_16_xl", "searchFacetOptionName": "16", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "xl", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_18_xl", "searchFacetOptionValue": "_bottoms_size_tall_18_xl", "searchFacetOptionName": "18", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "xl", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_20_xxl", "searchFacetOptionValue": "_bottoms_size_tall_20_xxl", "searchFacetOptionName": "20", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "xxl", "variantId": 1, "dimensionId": 1}}]}]}, {"searchFacetOptionGroupId": "inseam", "searchFacetOptionGroupName": "inseam", "parentSearchFacetOptionGroupId": "bottoms", "searchFacetOptionGroupList": [{"searchFacetOptionGroupId": "1", "searchFacetOptionGroupName": "regular", "parentSearchFacetOptionGroupId": "inseam", "searchFacetOptionList": [{"searchFacetOptionId": "_bottoms_inseam_regular_regular", "searchFacetOptionValue": "_bottoms_inseam_regular_regular", "searchFacetOptionName": "regular", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular"}]}]}]}]}, {"isActive": "true", "searchFacetId": "color", "searchFacetName": "Color", "searchFacetOptionGroupList": {"searchFacetOptionList": [{"isSelected": "false", "isActive": "true", "searchFacetOptionId": "red", "searchFacetOptionName": "red", "searchFacetOptionValue": "red"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "green", "searchFacetOptionName": "green", "searchFacetOptionValue": "green"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "blue", "searchFacetOptionName": "blue", "searchFacetOptionValue": "blue"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "purple", "searchFacetOptionName": "purple", "searchFacetOptionValue": "purple"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "pink", "searchFacetOptionName": "pink", "searchFacetOptionValue": "pink"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "beige", "searchFacetOptionName": "beige", "searchFacetOptionValue": "beige"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "brown", "searchFacetOptionName": "brown", "searchFacetOptionValue": "brown"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "black", "searchFacetOptionName": "black", "searchFacetOptionValue": "black"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "white", "searchFacetOptionName": "white", "searchFacetOptionValue": "white"}]}}, {"isActive": "true", "searchFacetId": "price", "searchFacetName": "Price", "searchFacetOptionGroupList": {"searchFacetOptionList": [{"isActive": "true", "isSelected": "false", "searchFacetOptionId": "MIN", "searchFacetOptionName": "27", "searchFacetOptionValue": "27"}, {"isActive": "true", "isSelected": "false", "searchFacetOptionId": "MAX", "searchFacetOptionName": "59.95", "searchFacetOptionValue": "59.95"}]}, "value": {"min": 27, "max": 60}, "searchFacetOptionId": "priceTag", "minValue": 27, "maxValue": 60}]}, "productCategory": {"productCategoryPaginator": {"pageNumberTotal": "1", "pageNumberRequested": "0"}, "isOutfitCategory": "false", "childProducts": [{"parentBusinessCatalogItemId": "130046", "name": "Swatches - Link", "categoryLargeImage": {"path": "https://www.gap.com/webcontent/0027/211/506/cn27211506.jpg"}, "currentPrice": "59.95", "originalPrice": "59.95", "price": {"currentMaxPrice": "59.95", "currentMinPrice": "25.99", "localizedCurrentMaxPrice": "$59.95", "localizedCurrentMinPrice": "$25.99", "localizedRegularMaxPrice": "$59.95", "localizedRegularMinPrice": "$59.95", "minPercentageOff": "0", "maxPercentageOff": "0", "priceType": "3", "regularMaxPrice": "59.95", "regularMinPrice": "59.95"}, "defaultSizeVariantId": "1", "marketingFlag": {"marketingFlagName": "Extra 50% Off With Code PERK"}, "businessCatalogItemId": "130046962", "additionalQueryParams": "&searchText=red pants", "swatchesProps": {"size": "small", "overflowBehavior": "link", "colorArray": [{"colorName": "Panther", "id": "1", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON> Pants Ruby Red", "url": "https://www1.assets-gap.com/webcontent/0018/788/723/cn18788723.jpg"}, {"colorName": "A Stone's Throw", "id": "2", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON>ts A Stone's Throw", "url": "https://www1.assets-gap.com/webcontent/0018/788/733/cn18788733.jpg"}, {"colorName": "Panther 2", "id": "3", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON> Pants Ruby Red", "url": "https://www1.assets-gap.com/webcontent/0018/788/723/cn18788723.jpg"}, {"colorName": "A Stone's Throw 3", "id": "4", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON>ts A Stone's Throw", "url": "https://www1.assets-gap.com/webcontent/0018/788/733/cn18788733.jpg"}, {"colorName": "Panther 4", "id": "4", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON> Pants Ruby Red", "url": "https://www1.assets-gap.com/webcontent/0018/788/723/cn18788723.jpg"}, {"colorName": "A Stone's Throw 5", "id": "5", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON>ts A Stone's Throw", "url": "https://www1.assets-gap.com/webcontent/0018/788/733/cn18788733.jpg"}, {"colorName": "Panther 6", "id": "6", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON> Pants Ruby Red", "url": "https://www1.assets-gap.com/webcontent/0018/788/723/cn18788723.jpg"}, {"colorName": "A Stone's Throw 7", "id": "7", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON>ts A Stone's Throw", "url": "https://www1.assets-gap.com/webcontent/0018/788/733/cn18788733.jpg"}]}}, {"parentBusinessCatalogItemId": "130046", "name": "Swatches - <PERSON><PERSON><PERSON>", "categoryLargeImage": {"path": "https://www.gap.com/webcontent/0020/604/341/cn20604341.jpg"}, "currentPrice": "59.95", "originalPrice": "59.95", "price": {"currentMaxPrice": "59.95", "currentMinPrice": "25.99", "localizedCurrentMaxPrice": "$59.95", "localizedCurrentMinPrice": "$25.99", "localizedRegularMaxPrice": "$59.95", "localizedRegularMinPrice": "$59.95", "minPercentageOff": "0", "maxPercentageOff": "0", "priceType": "3", "regularMaxPrice": "59.95", "regularMinPrice": "59.95"}, "defaultSizeVariantId": "1", "marketingFlag": {"marketingFlagName": "Extra 50% Off With Code PERK"}, "businessCatalogItemId": "130046962", "additionalQueryParams": "&searchText=red pants", "swatchesProps": {"size": "small", "overflowBehavior": "default", "colorArray": [{"colorName": "Panther", "id": "1", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON> Pants Ruby Red", "url": "https://www1.assets-gap.com/webcontent/0018/788/723/cn18788723.jpg"}, {"colorName": "A Stone's Throw", "id": "2", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON>ts A Stone's Throw", "url": "https://www1.assets-gap.com/webcontent/0018/788/733/cn18788733.jpg"}, {"colorName": "Panther 2", "id": "3", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON> Pants Ruby Red", "url": "https://www1.assets-gap.com/webcontent/0018/788/723/cn18788723.jpg"}, {"colorName": "A Stone's Throw 3", "id": "4", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON>ts A Stone's Throw", "url": "https://www1.assets-gap.com/webcontent/0018/788/733/cn18788733.jpg"}, {"colorName": "Panther 4", "id": "4", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON> Pants Ruby Red", "url": "https://www1.assets-gap.com/webcontent/0018/788/723/cn18788723.jpg"}, {"colorName": "A Stone's Throw 5", "id": "5", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON>ts A Stone's Throw", "url": "https://www1.assets-gap.com/webcontent/0018/788/733/cn18788733.jpg"}, {"colorName": "Panther 6", "id": "6", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON> Pants Ruby Red", "url": "https://www1.assets-gap.com/webcontent/0018/788/723/cn18788723.jpg"}, {"colorName": "A Stone's Throw 7", "id": "7", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON>ts A Stone's Throw", "url": "https://www1.assets-gap.com/webcontent/0018/788/733/cn18788733.jpg"}]}}, {"parentBusinessCatalogItemId": "130046", "name": "Swatches - Carousel", "categoryLargeImage": {"path": "https://www.gap.com/webcontent/0020/512/293/cn20512293.jpg"}, "currentPrice": "59.95", "originalPrice": "59.95", "price": {"currentMaxPrice": "59.95", "currentMinPrice": "25.99", "localizedCurrentMaxPrice": "$59.95", "localizedCurrentMinPrice": "$25.99", "localizedRegularMaxPrice": "$59.95", "localizedRegularMinPrice": "$59.95", "minPercentageOff": "0", "maxPercentageOff": "0", "priceType": "3", "regularMaxPrice": "59.95", "regularMinPrice": "59.95"}, "defaultSizeVariantId": "1", "marketingFlag": {"marketingFlagName": "Extra 50% Off With Code PERK"}, "businessCatalogItemId": "130046962", "additionalQueryParams": "&searchText=red pants", "swatchesProps": {"size": "small", "overflowBehavior": "carousel", "colorArray": [{"colorName": "Panther", "id": "1", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON> Pants Ruby Red", "url": "https://www1.assets-gap.com/webcontent/0018/788/723/cn18788723.jpg"}, {"colorName": "A Stone's Throw", "id": "2", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON>ts A Stone's Throw", "url": "https://www1.assets-gap.com/webcontent/0018/788/733/cn18788733.jpg"}, {"colorName": "Panther 2", "id": "3", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON> Pants Ruby Red", "url": "https://www1.assets-gap.com/webcontent/0018/788/723/cn18788723.jpg"}, {"colorName": "A Stone's Throw 3", "id": "4", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON>ts A Stone's Throw", "url": "https://www1.assets-gap.com/webcontent/0018/788/733/cn18788733.jpg"}, {"colorName": "Panther 4", "id": "4", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON> Pants Ruby Red", "url": "https://www1.assets-gap.com/webcontent/0018/788/723/cn18788723.jpg"}, {"colorName": "A Stone's Throw 5", "id": "5", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON>ts A Stone's Throw", "url": "https://www1.assets-gap.com/webcontent/0018/788/733/cn18788733.jpg"}, {"colorName": "Panther 6", "id": "6", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON> Pants Ruby Red", "url": "https://www1.assets-gap.com/webcontent/0018/788/723/cn18788723.jpg"}, {"colorName": "A Stone's Throw 7", "id": "7", "productImage": "https://www1.assets-gap.com/webcontent/0018/788/801/cn18788801.jpg", "productImageAltText": "<PERSON><PERSON>ts A Stone's Throw", "url": "https://www1.assets-gap.com/webcontent/0018/788/733/cn18788733.jpg"}]}}]}}, "status": "normal"}