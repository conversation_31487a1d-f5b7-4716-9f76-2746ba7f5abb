[{"brandToTest": "br", "marketToTest": "us", "environment": "local", "expectedUrl": "http://brol.local.gaptechol.com:3000/"}, {"brandToTest": "on", "marketToTest": "us", "environment": "local", "expectedUrl": "http://onol.local.gaptechol.com:3000/"}, {"brandToTest": "gap", "marketToTest": "us", "environment": "local", "expectedUrl": "http://www.local.gaptechol.com:3000/"}, {"brandToTest": "at", "marketToTest": "us", "environment": "local", "expectedUrl": "http://atol.local.gaptechol.com:3000/"}, {"brandToTest": "brfs", "marketToTest": "us", "environment": "local", "expectedUrl": "http://brfol.local.factory-gaptechol.com:3000/"}, {"brandToTest": "gapfs", "marketToTest": "us", "environment": "local", "expectedUrl": "http://www.local.factory-gaptechol.com:3000/"}, {"brandToTest": "br", "marketToTest": "ca", "environment": "local", "expectedUrl": "http://brol.local.gaptechol.ca:3000/"}, {"brandToTest": "on", "marketToTest": "ca", "environment": "local", "expectedUrl": "http://onol.local.gaptechol.ca:3000/"}, {"brandToTest": "gap", "marketToTest": "ca", "environment": "local", "expectedUrl": "http://www.local.gaptechol.ca:3000/"}, {"brandToTest": "at", "marketToTest": "ca", "environment": "local", "expectedUrl": "http://atol.local.gaptechol.ca:3000/"}, {"brandToTest": "brfs", "marketToTest": "ca", "environment": "local", "expectedUrl": "http://brfol.local.factory-gaptechol.ca:3000/"}, {"brandToTest": "br", "marketToTest": "us", "environment": "test", "expectedUrl": "https://brol.test.gaptechol.com/"}, {"brandToTest": "on", "marketToTest": "us", "environment": "test", "expectedUrl": "https://onol.test.gaptechol.com/"}, {"brandToTest": "gap", "marketToTest": "us", "environment": "test", "expectedUrl": "https://www.test.gaptechol.com/"}, {"brandToTest": "at", "marketToTest": "us", "environment": "test", "expectedUrl": "https://atol.test.gaptechol.com/"}, {"brandToTest": "brfs", "marketToTest": "us", "environment": "test", "expectedUrl": "https://brfol.test.factory-gaptechol.com/"}, {"brandToTest": "gapfs", "marketToTest": "us", "environment": "test", "expectedUrl": "https://www.test.factory-gaptechol.com/"}, {"brandToTest": "br", "marketToTest": "ca", "environment": "test", "expectedUrl": "https://brol.test.gaptechol.ca/"}, {"brandToTest": "on", "marketToTest": "ca", "environment": "test", "expectedUrl": "https://onol.test.gaptechol.ca/"}, {"brandToTest": "gap", "marketToTest": "ca", "environment": "test", "expectedUrl": "https://www.test.gaptechol.ca/"}, {"brandToTest": "at", "marketToTest": "ca", "environment": "test", "expectedUrl": "https://atol.test.gaptechol.ca/"}, {"brandToTest": "brfs", "marketToTest": "ca", "environment": "test", "expectedUrl": "https://brfol.test.factory-gaptechol.ca/"}, {"brandToTest": "br", "marketToTest": "us", "environment": "stage", "expectedUrl": "https://brol.stage.gaptechol.com/"}, {"brandToTest": "on", "marketToTest": "us", "environment": "stage", "expectedUrl": "https://onol.stage.gaptechol.com/"}, {"brandToTest": "gap", "marketToTest": "us", "environment": "stage", "expectedUrl": "https://www.stage.gaptechol.com/"}, {"brandToTest": "at", "marketToTest": "us", "environment": "stage", "expectedUrl": "https://atol.stage.gaptechol.com/"}, {"brandToTest": "brfs", "marketToTest": "us", "environment": "stage", "expectedUrl": "https://brfol.stage.factory-gaptechol.com/"}, {"brandToTest": "gapfs", "marketToTest": "us", "environment": "stage", "expectedUrl": "https://www.stage.factory-gaptechol.com/"}, {"brandToTest": "br", "marketToTest": "ca", "environment": "stage", "expectedUrl": "https://brol.stage.gaptechol.ca/"}, {"brandToTest": "on", "marketToTest": "ca", "environment": "stage", "expectedUrl": "https://onol.stage.gaptechol.ca/"}, {"brandToTest": "gap", "marketToTest": "ca", "environment": "stage", "expectedUrl": "https://www.stage.gaptechol.ca/"}, {"brandToTest": "at", "marketToTest": "ca", "environment": "stage", "expectedUrl": "https://atol.stage.gaptechol.ca/"}, {"brandToTest": "brfs", "marketToTest": "ca", "environment": "stage", "expectedUrl": "https://brfol.stage.factory-gaptechol.ca/"}, {"brandToTest": "br", "marketToTest": "us", "environment": "preview", "expectedUrl": "https://brol.wip.prod.gaptecholapps.com/"}, {"brandToTest": "on", "marketToTest": "us", "environment": "preview", "expectedUrl": "https://onol.wip.prod.gaptecholapps.com/"}, {"brandToTest": "gap", "marketToTest": "us", "environment": "preview", "expectedUrl": "https://www.wip.prod.gaptecholapps.com/"}, {"brandToTest": "at", "marketToTest": "us", "environment": "preview", "expectedUrl": "https://atol.wip.prod.gaptecholapps.com/"}, {"brandToTest": "brfs", "marketToTest": "us", "environment": "preview", "expectedUrl": "https://brfol.wip.prod.factory-gaptecholapps.com/"}, {"brandToTest": "gapfs", "marketToTest": "us", "environment": "preview", "expectedUrl": "https://www.wip.prod.factory-gaptecholapps.com/"}, {"brandToTest": "br", "marketToTest": "ca", "environment": "preview", "expectedUrl": "https://brol.wip.prod.gaptecholapps.ca/"}, {"brandToTest": "on", "marketToTest": "ca", "environment": "preview", "expectedUrl": "https://onol.wip.prod.gaptecholapps.ca/"}, {"brandToTest": "gap", "marketToTest": "ca", "environment": "preview", "expectedUrl": "https://www.wip.prod.gaptecholapps.ca/"}, {"brandToTest": "at", "marketToTest": "ca", "environment": "preview", "expectedUrl": "https://atol.wip.prod.gaptecholapps.ca/"}, {"brandToTest": "brfs", "marketToTest": "ca", "environment": "preview", "expectedUrl": "https://brfol.wip.prod.factory-gaptecholapps.ca/"}, {"brandToTest": "br", "marketToTest": "us", "environment": "prod", "expectedUrl": "https://bananarepublic.gap.com/"}, {"brandToTest": "on", "marketToTest": "us", "environment": "prod", "expectedUrl": "https://oldnavy.gap.com/"}, {"brandToTest": "gap", "marketToTest": "us", "environment": "prod", "expectedUrl": "https://www.gap.com/"}, {"brandToTest": "at", "marketToTest": "us", "environment": "prod", "expectedUrl": "https://athleta.gap.com/"}, {"brandToTest": "brfs", "marketToTest": "us", "environment": "prod", "expectedUrl": "https://bananarepublicfactory.gapfactory.com/"}, {"brandToTest": "gapfs", "marketToTest": "us", "environment": "prod", "expectedUrl": "https://www.gapfactory.com/"}, {"brandToTest": "br", "marketToTest": "ca", "environment": "prod", "expectedUrl": "https://bananarepublic.gapcanada.ca/"}, {"brandToTest": "on", "marketToTest": "ca", "environment": "prod", "expectedUrl": "https://oldnavy.gapcanada.ca/"}, {"brandToTest": "gap", "marketToTest": "ca", "environment": "prod", "expectedUrl": "https://www.gapcanada.ca/"}, {"brandToTest": "at", "marketToTest": "ca", "environment": "prod", "expectedUrl": "https://athleta.gapcanada.ca/"}, {"brandToTest": "brfs", "marketToTest": "ca", "environment": "prod", "expectedUrl": "https://bananarepublicfactory.gapfactory.ca/"}]