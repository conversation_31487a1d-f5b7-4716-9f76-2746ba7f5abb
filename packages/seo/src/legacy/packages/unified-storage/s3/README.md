# S3 Storage

## Functions

* [validateHttpOptions(httpOptionsInput)](#validateHttpOptions) ⇒ <code>Agent</code>
* [inputValidation(s3Configs)](#inputValidation)
* [configureS3(s3Configs)](#configureS3) ⇒ <code>S3</code>
* [listBucketsInRepo(providedValues, s3Repo)](#listBucketsInRepo) ⇒ <code>Promise</code>
* [createBucketInRepo(providedValues, s3Repo)](#createBucketInRepo) ⇒ <code>Promise.&lt;({success: boolean, response: string}\|{success: boolean, error: any})&gt;</code>
* [deleteBucketInRepo(providedValues, s3Repo)](#deleteBucketInRepo) ⇒ <code>Promise.&lt;({success: boolean, response: string, message: string}\|{success: boolean, response: string, error: any})&gt;</code> \| <code>Promise.&lt;{success: boolean, response: string}&gt;</code>
* [bucketExists(providedValues, s3Repo)](#bucketExists) ⇒ <code>Promise</code>
* [listObjectsInBucket(providedValues, s3Repo)](#listObjectsInBucket) ⇒ <code>Promise</code>
* [objectExistsInBucket(providedValues, s3Repo)](#objectExistsInBucket) ⇒ <code>Promise</code>
* [putObjectIntoBucket(providedValues, s3Repo)](#putObjectIntoBucket) ⇒ <code>Promise.&lt;({success: boolean, response: \*, message: string}\|{success: boolean, response: string, error: \*})&gt;</code>
* [getObjectFromBucket(providedValues, s3Repo)](#getObjectFromBucket) ⇒ <code>Promise.&lt;({success: boolean, response: string, message: string}\|{success: boolean, message: string, error: any})&gt;</code>
* [deleteObjectInBucket(providedValues, s3Repo)](#deleteObjectInBucket) ⇒ <code>Promise.&lt;({success: boolean, response: string, message: string}\|{success: boolean, message: string, error: any})&gt;</code> \| <code>Promise.&lt;{success: boolean, response: string}&gt;</code>
* [deleteMultipleObjectsInBucket(providedValues, s3Repo)](#deleteMultipleObjectsInBucket) ⇒ <code>Promise.&lt;({success: boolean, response: string, message: string}\|{success: boolean, message: string, error: any})&gt;</code>
* [deleteAllObjectsInBucket(providedValues, s3Repo)](#deleteAllObjectsInBucket) ⇒ <code>Promise</code>

<a name="validateHttpOptions"></a>

## validateHttpOptions(httpOptionsInput) ⇒ <code>Agent</code>
This method validates httpOptions and throws an error if an option is not compatible with the http spec.

**Kind**: global function  

| Param | Type |
| --- | --- |
| httpOptionsInput | <code>Agent</code> | 

<a name="inputValidation"></a>

## inputValidation(s3Configs)
This method checks the required fields for S3 configuration and throws an error if any of them are missing.

**Kind**: global function  

| Param | Type |
| --- | --- |
| s3Configs | <code>Object.&lt;{url: string, httpOptions: Agent, secretAccessKey: string}&gt;</code> | 

<a name="configureS3"></a>

## configureS3(s3Configs) ⇒ <code>S3</code>
This method configures an S3 object based on the options passed into it.

**Kind**: global function  

| Param | Type |
| --- | --- |
| s3Configs | <code>Object.&lt;{url: string, accessKeyId: string, secretAccessKey: string, httpOptions: Agent}&gt;</code> | 

<a name="listBucketsInRepo"></a>

## listBucketsInRepo(providedValues, s3Repo) ⇒ <code>Promise</code>
This method lists all the buckets in an S3 cluster.

**Kind**: global function  
**Fulfil**: <code>{data: {Owner: {DisplayName: string, ID: string</code>, Buckets: {CreationDate: string, Name: string}[], success: boolean}}  
**Reject**: <code>{success: boolean, error: \*</code>}  

| Param | Type | Description |
| --- | --- | --- |
| providedValues | <code>Object.&lt;{}&gt;</code> | this is an empty object |
| s3Repo | <code>string</code> | the S3 instance to check against |

<a name="createBucketInRepo"></a>

## createBucketInRepo(providedValues, s3Repo) ⇒ <code>Promise.&lt;({success: boolean, response: string}\|{success: boolean, error: any})&gt;</code>
This method creates an S3 bucket in a given cluster.

**Kind**: global function  

| Param | Type | Description |
| --- | --- | --- |
| providedValues | <code>Object.&lt;{storageSpace: string}&gt;</code> |  |
| s3Repo | <code>string</code> | the S3 instance to check against |

<a name="deleteBucketInRepo"></a>

## deleteBucketInRepo(providedValues, s3Repo) ⇒ <code>Promise.&lt;({success: boolean, response: string, message: string}\|{success: boolean, response: string, error: any})&gt;</code> \| <code>Promise.&lt;{success: boolean, response: string}&gt;</code>
This method deletes a bucket in a given cluster.

**Kind**: global function  

| Param | Type | Description |
| --- | --- | --- |
| providedValues | <code>Object.&lt;{storageSpace: string}&gt;</code> |  |
| s3Repo | <code>string</code> | the S3 instance to check against |

<a name="bucketExists"></a>

## bucketExists(providedValues, s3Repo) ⇒ <code>Promise</code>
This method returns whether or not a given bucket exists in a cluster.

**Kind**: global function  
**Fulfil**: <code>{data: {Owner: {DisplayName: string, ID: string</code>, Buckets: {CreationDate: string, Name: string}[]}, success: boolean}}  
**Reject**: <code>{success: boolean, error: \*</code>}  

| Param | Type |
| --- | --- |
| providedValues | <code>Object.&lt;{storageSpace: string}&gt;</code> | 
| s3Repo | <code>string</code> | 

<a name="listObjectsInBucket"></a>

## listObjectsInBucket(providedValues, s3Repo) ⇒ <code>Promise</code>
This method lists the S3 objects in a given bucket.

**Kind**: global function  
**Fulfil**: <code>{success: boolean, response: {CommonPrefixes: [], Contents: {LastModified: string, Owner: {DisplayName: string, ID: string</code>, Size: number, StorageClass: string, Key: string}[], IsTruncated: boolean, Prefix: string, MaxKeys: number, Name: string}, objectsInBucket: number}}  
**Reject**: <code>{success: boolean, error: any, message: string</code>}  

| Param | Type |
| --- | --- |
| providedValues | <code>Object.&lt;{storageSpace: string, maxKeys: number, prefix: string, continuationMarker: string}&gt;</code> | 
| s3Repo | <code>string</code> | 

<a name="objectExistsInBucket"></a>

## objectExistsInBucket(providedValues, s3Repo) ⇒ <code>Promise</code>
This method returns whether or not an object exists in a given bucket.

**Kind**: global function  
**Fulfil**: <code>{success: boolean, response: {CommonPrefixes: \*[], Contents: {LastModified: string, Owner: {DisplayName: string, ID: string</code>, Size: number, StorageClass: string, Key: string}[], IsTruncated: boolean, Prefix: string, MaxKeys: number, Name: string}, objectsInBucket: number}}  
**Reject**: <code>{success: boolean, error: any, message: string</code>}  

| Param | Type |
| --- | --- |
| providedValues | <code>Object.&lt;{storageSpace: string, objectName: string}&gt;</code> | 
| s3Repo | <code>string</code> | 

<a name="putObjectIntoBucket"></a>

## putObjectIntoBucket(providedValues, s3Repo) ⇒ <code>Promise.&lt;({success: boolean, response: \*, message: string}\|{success: boolean, response: string, error: \*})&gt;</code>
This method puts in object into a given S3 bucket and returns the status of that transaction.

**Kind**: global function  

| Param | Type |
| --- | --- |
| providedValues | <code>Object.&lt;{storageSpace: string, fileName: string, fileBody: string}&gt;</code> | 
| s3Repo | <code>string</code> | 

<a name="getObjectFromBucket"></a>

## getObjectFromBucket(providedValues, s3Repo) ⇒ <code>Promise.&lt;({success: boolean, response: string, message: string}\|{success: boolean, message: string, error: any})&gt;</code>
This method returns an object from an S3 bucket and returns the status of that transaction.

**Kind**: global function  

| Param | Type |
| --- | --- |
| providedValues | <code>Object.&lt;{storageSpace: string, fileName: string}&gt;</code> | 
| s3Repo | <code>string</code> | 

<a name="deleteObjectInBucket"></a>

## deleteObjectInBucket(providedValues, s3Repo) ⇒ <code>Promise.&lt;({success: boolean, response: string, message: string}\|{success: boolean, message: string, error: any})&gt;</code> \| <code>Promise.&lt;{success: boolean, response: string}&gt;</code>
This method deletes an object in an S3 bucket and returns the status of that transaction.

**Kind**: global function  

| Param | Type |
| --- | --- |
| providedValues | <code>Object.&lt;{fileName: string, storageSpace: string}&gt;</code> | 
| s3Repo | <code>string</code> | 

<a name="deleteMultipleObjectsInBucket"></a>

## deleteMultipleObjectsInBucket(providedValues, s3Repo) ⇒ <code>Promise.&lt;({success: boolean, response: string, message: string}\|{success: boolean, message: string, error: any})&gt;</code>
This method deletes multiple objects in a given S3 bucket and returns the status of that transaction.

**Kind**: global function  

| Param | Type |
| --- | --- |
| providedValues | <code>Object.&lt;{storageSpace: string, keysArray: Array.&lt;{Keys: string}&gt;}&gt;</code> | 
| s3Repo | <code>string</code> | 

<a name="deleteAllObjectsInBucket"></a>

## deleteAllObjectsInBucket(providedValues, s3Repo) ⇒ <code>Promise</code>
This method attempts to delete up to 10,000 objects within an S3 bucket and returns the status of that transaction.

**Kind**: global function  
**Fulfil**: <code>Array&lt;Object&lt;{success: boolean, response: string, message: string</code>>>}  
**Reject**: <code>Array&lt;Object&lt;{success: boolean, message: string, error: any</code>>>}  

| Param | Type |
| --- | --- |
| providedValues | <code>Object.&lt;{storageSpace: string}&gt;</code> | 
| s3Repo | <code>string</code> | 

