{"name": "@ecom-next/shopping-bag", "version": "1.0.0", "description": "", "exports": {"./components/*": "./src/components/*.tsx", "./modules/*": "./src/modules/*.tsx", "./modules/*/types": "./src/modules/*/types.tsx", "./contexts/*": "./src/contexts/*.tsx", "./pages/*": "./src/pages/*.tsx", "./layouts/*": "./src/layouts/*.tsx", "./api/*": "./src/api/*.ts", "./hooks/*": "./src/hooks/*.ts", "./paypal-button": "./src/modules/PayPal/PayPalButton.tsx", "./paypal-shipping-change-callback": "./src/modules/PayPal/callbacks/shipping-change/shippingChangeCallback.ts", "./paypal-authorize-callback": "./src/modules/PayPal/callbacks/authorize/authorizeCallback.ts", "./paypal-types": "./src/modules/PayPal/types.ts", "./braintree-scripts": "./src/pages/BraintreeScripts.tsx", "./translations": "./src/translations/index.ts", "./order-summary": "./src/modules/OrderSummary/OrderSummary.tsx", "./product-card": "./src/modules/ProductCard/ProductCard.tsx", "./stepper": "./src/modules/Stepper/Stepper.tsx", "./promos": "./src/modules/Promos/Promos.tsx", "./apple-pay/*": "./src/components/ApplePay/*.ts", "./order-summary/line-item": "./src/modules/OrderSummary/LineItem.tsx", "./components/*/types": "./src/components/*/types.ts", "./loyalty": "./src/modules/Loyalty/Rewards.tsx", "./utils": "./src/utils/index.ts", "./http-client/*": "./src/http-client/*.ts", "./payment-types": "./src/types/payment-types.ts"}, "scripts": {"test:changedsince": "jest --changedSince=origin/main", "clean": "rm -rf dist", "format": "prettier . --write --ignore-unknown", "lint": "eslint ./src ./config ./mockData", "test": "jest", "test:watch": "jest --watch"}, "keywords": [], "dependencies": {"@ecom-next/core": "1.0.0", "@ecom-next/marketing-ui": "1.0.0", "@ecom-next/sitewide": "1.0.0", "react-intersection-observer": "9.10.3"}, "devDependencies": {"@types/applepayjs": "^14.0.6", "next": "14.2.25", "react": "18.3.1", "react-dom": "18.3.1", "tailwindcss": "^3.4.3"}, "author": "", "license": "ISC"}