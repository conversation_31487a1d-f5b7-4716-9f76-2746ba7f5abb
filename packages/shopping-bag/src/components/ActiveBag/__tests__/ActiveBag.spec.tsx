import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BreakpointProvider, BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { clientFetch } from '@ecom-next/utils/clientFetch';
import { useBagContext } from '@ecom-next/shopping-bag/contexts/ShoppingBagProvider';
import { useProductSelectionContext } from '@ecom-next/product/legacy/product-selection';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { productStyleResponseAdapter } from '@ecom-next/product/capi-product-style';
import * as snackbarModule from '@ecom-next/core/migration/snackbar';
import { useActiveBagContext } from '@ecom-next/shopping-bag/contexts/ActiveBagProvider';
import { ProductEditDetails } from '@ecom-next/shopping-bag/modules/ProductCard/ProductEditDetail';
import { ProductCard } from '@ecom-next/shopping-bag/modules/ProductCard/ProductCard';
import { FulfillmentOption } from '../../../utils/bagTypes';
import { useEnabledFeatures } from '../../../hooks/useEnabledFeatures';
import { useDatalayer } from '../../../hooks/useDataLayer';
import { ActiveBag } from '../ActiveBag';
import { getStoreInfo } from '../datalayer/getStoreInfo';
import { logNewRelicError } from '../../../utils/newrelic-logger';
import { Product } from '../../../types/xapi-response-types';
import { getMockCapiData } from './get-mock-capi-data';

// Mock the dependencies
jest.mock('@ecom-next/shopping-bag/contexts/ShoppingBagProvider');
jest.mock('../../../contexts/ActiveBagProvider');
jest.mock('@ecom-next/sitewide/hooks/usePageContext');
jest.mock('@ecom-next/sitewide/localization-provider');
jest.mock('../../../hooks/useDataLayer');
jest.mock('../../../hooks/useEnabledFeatures');
jest.mock('../datalayer/getStoreInfo');

jest.mock('@ecom-next/core/migration/snackbar', () => ({
  ...jest.requireActual('@ecom-next/core/migration/snackbar'),
  __esModule: true,
  Snackbar: jest.fn(({ children }) => {
    return <p>{children}</p>;
  }),
}));

jest.mock('@ecom-next/product/legacy-components/fulfillment-product-detail', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('@ecom-next/product/capi-product-style');

jest.mock('@ecom-next/utils/clientFetch', () => ({
  ...jest.requireActual('@ecom-next/utils/clientFetch'),
  clientFetch: jest.fn(() => Promise.resolve()),
}));

jest.mock('../../../utils/newrelic-logger', () => {
  return {
    ...jest.requireActual('../../../utils/newrelic-logger'),
    logNewRelicError: jest.fn(),
  };
});

jest.mock('../../../datalayer', () => ({
  ...jest.requireActual('../../../datalayer'),
  publishDataLayerEvent: jest.fn(),
}));

jest.mock('@ecom-next/product/legacy/product-selection', () => ({
  ...jest.requireActual('@ecom-next/core/migration/snackbar'),
  __esModule: true,
  ProductSelectionMediator: jest.fn(({ children }) => {
    return <p>{children}</p>;
  }),

  useProductSelectionContext: jest.fn().mockReturnValue({
    selectedSize: {
      inStock: true,
      skuId: '5543540023000',
    },
    sku: 'sku1',
  }),
}));

const mockPublishDataLayerEvent = jest.fn();

describe('ActiveBag', () => {
  const moveItem = { action: '' };
  const setMoveItem = jest.fn();
  const asyncDispatch = jest.fn();
  const localize = jest.fn(key => key);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const mockBreakpointContext: any = {
    smallerThan: () => false,
    minWidth: () => false,
  };
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let quantity;

  beforeEach(() => {
    quantity = 2;
    (useBagContext as jest.Mock).mockReturnValue({ moveItem, setMoveItem });
    (useEnabledFeatures as jest.Mock).mockReturnValue({ enabledFeatures: { 'bag-ui-bopis-enhacement': true } });
    (useDatalayer as jest.Mock).mockReturnValue({
      publishDataLayerEvent: mockPublishDataLayerEvent,
      getRecognitionStatus: jest.fn().mockReturnValue('authenticated'),
    });
    (useActiveBagContext as jest.Mock).mockReturnValue({
      bagState: {
        data: {
          currencySymbol: '$',
          bagAttributes: { bagItemsCount: { ship: 1, pickUp: 0 }, userStatus: 'AUTHENTICATED', reconition_status: 'authenticated' },
          productList: [
            {
              id: '1',
              productName: 'Product 1',
              size: 'M',
              productColor: 'Red',
              imageUrl: 'image1.jpg',
              productFlags: [
                { message: 'RETURN_BY_MAIL', type: 'informational', date: '' },
                { message: 'NO_RETURNS', type: 'informational', date: '' },
              ],
              promotions: [
                {
                  code: 'BAGUIUS001',
                  description: 'BAGUIUS001',
                  isAutoApply: false,
                },
              ],
              regularPrice: '100',
              finalItemPrice: '80',
              totalItemSavings: '20',
              productUrl: 'product-1',
              brandAbbrName: 'Brand1',
              quantity: 2,
              maxOrderQty: 5,
              sku: 'sku1',
              fulfillment: {
                selectedType: 'SHIP',
                pickUpStoreId: '',
                isShipEnabled: true,
                isBopisEnabled: true,
              },
            },
          ],
        },
      },
      asyncDispatch,
    });
    (usePageContext as jest.Mock).mockReturnValue({ brandName: 'Brand', market: 'us', abSeg: { xb216: 'a' } });
    (useLocalize as jest.Mock).mockReturnValue({ localize });
    (getStoreInfo as jest.Mock).mockResolvedValue({ storeName: 'Store Name', deliveryType: 'inStorePickup' });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the ActiveBag component', () => {
    const { getByText, getByTestId } = render(
      <BreakpointProvider>
        <ActiveBag />
      </BreakpointProvider>
    );
    expect(getByText('product.myBagHeader')).toBeInTheDocument();
    expect(getByText('Product 1')).toBeInTheDocument();
    expect(getByText('M')).toBeInTheDocument();
    expect(getByText('Red')).toBeInTheDocument();
    expect(getByText('BAGUIUS001')).toBeInTheDocument();
    expect(getByText('product.RETURN_BY_MAIL_TITLE')).toBeInTheDocument();
    expect(getByText('product.NO_RETURNS_TITLE')).toBeInTheDocument();
    expect(getByText('product.NO_RETURNS_DESCRIPTION')).toBeInTheDocument();
    expect(getByText('$100.00')).toBeInTheDocument();
    expect(getByText('$80.00')).toBeInTheDocument();
    expect(getByText('product.savingsText $20.00')).toBeInTheDocument();
    expect(getByText('fulfillment.shipToAddress')).toBeInTheDocument();
    expect(getByText('fulfillment.pickup')).toBeInTheDocument();
    expect(getByTestId('change-store-modal')).toBeInTheDocument();
  });

  describe('Edit Bag', () => {
    const product: Product = {
      brandAbbrName: 'gap',
      id: '1',
      productName: 'Product Name',
      size: 'M',
      productColor: 'Red',
      imageUrl: 'image1.jpg',
      productFlags: [],
      promotions: [],
      regularPrice: 100,
      finalItemPrice: 80,
      totalItemSavings: 20,
      productUrl: 'product-url',
      quantity: 1,
      maxOrderQty: 5,
      sku: 'sku1',
      fulfillment: {
        selectedType: 'SHIP' as FulfillmentOption,
        pickUpStoreId: '',
        isShipEnabled: true,
        isBopisEnabled: true,
      },
      categoryNumber: '',
      finalPrice: 0,
      gwpIndicator: false,
      markdownPrice: 0,
      savingsPrice: 0,
      styleNumber: '',
      vendorId: '',
    };

    const onClose = jest.fn();
    it('should not render Edit button when bag-ui-edit-bag FF and abSeq experiment are OFF', () => {
      useEnabledFeatures.mockReturnValue({ enabledFeatures: { 'bag-ui-edit-bag': false } });
      (usePageContext as jest.Mock).mockReturnValue({ brandName: 'Brand', market: 'us', abSeg: { xb216: 'x' } });

      const { queryByText } = render(
        <BreakpointContext.Provider value={mockBreakpointContext}>
          <ActiveBag />
        </BreakpointContext.Provider>
      );
      expect(queryByText('product.editButton')).not.toBeInTheDocument();
    });

    it('should render Edit button when bag-ui-edit-bag FF and abSeq experiment are ON and item is ship', () => {
      useEnabledFeatures.mockReturnValue({ enabledFeatures: { 'bag-ui-edit-bag': true } });
      const { getByText } = render(
        <BreakpointContext.Provider value={mockBreakpointContext}>
          <ActiveBag />
        </BreakpointContext.Provider>
      );
      expect(getByText('product.editButton')).toBeInTheDocument();
    });

    it('should not render Edit button when bag-ui-edit-bag FF and abSeq experiment are ON and item is pickup', () => {
      useEnabledFeatures.mockReturnValue({ enabledFeatures: { 'bag-ui-edit-bag': true } });
      useActiveBagContext.mockReturnValue({
        bagState: {
          data: {
            currencySymbol: '$',
            bagAttributes: { bagItemsCount: { ship: 1, pickUp: 0 }, userStatus: 'AUTHENTICATED' },
            productList: [
              {
                id: '2',
                productName: 'Product 2',
                size: 'M',
                productColor: 'Blue',
                imageUrl: 'image2.jpg',
                productFlags: [
                  { message: 'RETURN_BY_MAIL', type: 'informational', date: '' },
                  { message: 'NO_RETURNS', type: 'informational', date: '' },
                ],
                promotions: [
                  {
                    code: 'BAGUIUS001',
                    description: 'BAGUIUS001',
                    isAutoApply: false,
                  },
                ],
                regularPrice: '100',
                finalItemPrice: '80',
                totalItemSavings: '20',
                productUrl: 'product-1',
                brandAbbrName: 'Brand1',
                quantity: 2,
                maxOrderQty: 5,
                sku: 'sku1',
                fulfillment: {
                  selectedType: 'PICKUP',
                  pickUpStoreId: '123',
                  isShipEnabled: true,
                  isBopisEnabled: true,
                },
              },
            ],
          },
        },
        asyncDispatch,
      });
      const { queryByText } = render(
        <BreakpointContext.Provider value={mockBreakpointContext}>
          <ActiveBag />
        </BreakpointContext.Provider>
      );
      expect(queryByText('product.editButton')).not.toBeInTheDocument();
    });

    it('should throw error and log in New Relic when capi service call fails', async () => {
      (clientFetch as jest.Mock).mockRejectedValueOnce('error');
      useEnabledFeatures.mockReturnValue({ enabledFeatures: { 'bag-ui-edit-bag': true } });

      const { getByText } = render(
        <BreakpointContext.Provider value={mockBreakpointContext}>
          <ActiveBag />
        </BreakpointContext.Provider>
      );
      fireEvent.click(getByText('product.editButton'));

      await waitFor(() => {
        expect(clientFetch).toHaveBeenCalled();
        expect(logNewRelicError).toHaveBeenCalled();
        expect(logNewRelicError).toHaveBeenCalledWith('error', {
          caller: 'handleEdit()',
          feature: 'EDIT_PRODUCT',
          message: 'Error while calling capi service',
        });
      });
    });

    it('should render Edit button when bag-ui-edit-bag FF and abSeq experiment are ON and item is Gift Card with Ship as fulfillment', () => {
      useEnabledFeatures.mockReturnValue({ enabledFeatures: { 'bag-ui-edit-bag': true } });
      useActiveBagContext.mockReturnValue({
        bagState: {
          data: {
            currencySymbol: '$',
            bagAttributes: { bagItemsCount: { ship: 1, pickUp: 0 }, userStatus: 'AUTHENTICATED' },
            productList: [
              {
                regularPrice: 25,
                markdownPrice: 25,
                finalItemPrice: 22.5,
                finalPrice: 22.5,
                productUrl: 'https://athleta.gap.com/browse/product.do?pid=0001260520025',
                imageUrl: 'https://www1.assets-gap.com/webcontent/0056/318/841/cn56318841.jpg',
                primaryCategoryName: 'All New Arrivals',
                productName: 'Athleta Gift Card',
                productColor: 'Find Your Movement',
                size: '$25.00',
                sku: '0001260520025',
                categoryNumber: '',
                savingsPrice: 2.5,
                totalItemSavings: 2.5,
                quantity: 1,
                maxOrderQty: 5,
                brandId: '10',
                brandAbbrName: 'AT',
                brandFullName: 'Athleta',
                id: 'abf214fea64b4387af9f466be4a55b66',
                gwpIndicator: false,
                styleNumber: '000126',
                productType: 'STORED VALUE CARDS FIXED',
                fulfillment: {
                  selectedType: 'SHIP',
                  pickUpStoreId: '',
                  isShipEnabled: true,
                  isBopisEnabled: true,
                },
                promotions: [
                  {
                    code: 'ID-1070997',
                    isAutoApply: true,
                    description: '10% Off Athleta Gift Cards',
                  },
                ],
                error: {},
                productFlags: [],
              },
            ],
          },
        },
        asyncDispatch,
      });
      const { getByText } = render(
        <BreakpointContext.Provider value={mockBreakpointContext}>
          <ActiveBag />
        </BreakpointContext.Provider>
      );
      expect(getByText('product.editButton')).toBeInTheDocument();
    });

    it('should open the edit modal and call capi service when the edit button is clicked', async () => {
      useEnabledFeatures.mockReturnValue({ enabledFeatures: { 'bag-ui-edit-bag': true } });
      const { getByText } = render(
        <BreakpointContext.Provider value={mockBreakpointContext}>
          <ActiveBag />
        </BreakpointContext.Provider>
      );
      fireEvent.click(getByText('product.editButton'));

      await waitFor(() => {
        expect(clientFetch).toHaveBeenCalled();
        expect(clientFetch).toHaveBeenCalledWith(
          expect.stringContaining('/commerce/catalog/aggregation/v2/products/style/'),
          expect.objectContaining({
            headers: expect.objectContaining({ 'X-Client-Application-Name': 'bag-ui' }),
          })
        );
        expect(mockPublishDataLayerEvent).toHaveBeenCalled();
        expect(mockPublishDataLayerEvent).toHaveBeenCalledWith({
          name: 'edit_item_click',
          extraAttrs: { recognition_status: 'authenticated' },
        });
        expect(getByText('product.editTitle')).toBeInTheDocument();
        expect(productStyleResponseAdapter).toHaveBeenCalled();
      });
    });

    it('should show display error message if capi call fails', async () => {
      useEnabledFeatures.mockReturnValue({ enabledFeatures: { 'bag-ui-edit-bag': true } });
      const capiResponse = {
        data: null,
        isLoading: false,
        hasError: true,
      };

      const { getByText, getByTestId } = render(
        <ProductCard.EditBody capiResponse={capiResponse} brandName='gap' localize={localize} product={product} onClose={onClose} userStatus='AUTHENTICATED' />
      );

      await waitFor(() => {
        expect(getByText('product.editCapiErrorMessage')).toBeInTheDocument();
        expect(getByTestId('edit-modal-capi-error-message')).toBeInTheDocument();
      });
    });

    it('should show Update button in enabled state when selectedSize is available in edit modal', async () => {
      useEnabledFeatures.mockReturnValue({ enabledFeatures: { 'bag-ui-edit-bag': true } });

      const capiData = JSON.parse(JSON.stringify(getMockCapiData));
      const { queryByText, getByTestId } = render(
        <ProductEditDetails capiData={capiData} brandName='gap' localize={localize} onClose={onClose} product={product} userStatus='AUTHENTICATED' />
      );
      expect(queryByText('product.editOOSText')).not.toBeInTheDocument();
      expect(getByTestId('update-button-enabled')).toBeInTheDocument();
      expect(mockPublishDataLayerEvent).toHaveBeenCalled();
      expect(mockPublishDataLayerEvent).toHaveBeenCalledWith({
        name: 'edit_item_view',
        extraAttrs: { recognition_status: 'authenticated' },
      });
    });

    it('should make an update quantity bag call on click of update button', async () => {
      useEnabledFeatures.mockReturnValue({ enabledFeatures: { 'bag-ui-edit-bag': true } });

      const capiData = JSON.parse(JSON.stringify(getMockCapiData));
      const { getByTestId } = render(
        <ProductEditDetails capiData={capiData} brandName='gap' localize={localize} onClose={onClose} product={product} userStatus='AUTHENTICATED' />
      );

      expect(getByTestId('update-button-enabled')).toBeInTheDocument();
      fireEvent.click(getByTestId('update-button-enabled'));
      await waitFor(() => {
        expect(mockPublishDataLayerEvent).toHaveBeenCalled();

        // Note: this test ensures that tealium related to closing the modal is not fired when update is clicked
        expect(mockPublishDataLayerEvent).not.toHaveBeenCalledWith({
          name: 'edit_item_cancel',
          extraAttrs: { recognition_status: 'authenticated' },
        });
        expect(mockPublishDataLayerEvent).toHaveBeenCalledWith({
          name: 'edit_item_update',
          extraAttrs: { recognition_status: 'authenticated' },
        });
        expect(asyncDispatch).toHaveBeenCalled();
        expect(asyncDispatch).toHaveBeenCalledWith({
          action: 'UPDATE_QUANTITY',
          requestPayload: {
            lineItemId: '1',
            sku: '5543540023000',
            op: 'CHANGESKU',
          },
        });
      });
    });
    it('should close the modal on click of close icon without making a call to update quantity endpoint', () => {
      useEnabledFeatures.mockReturnValue({ enabledFeatures: { 'bag-ui-edit-bag': true } });
      const { getByTestId, getByText } = render(
        <BreakpointContext.Provider value={mockBreakpointContext}>
          <ActiveBag />
        </BreakpointContext.Provider>
      );
      fireEvent.click(getByText('product.editButton'));

      const divOnClick = getByTestId('close-icon').parentElement as HTMLElement;
      fireEvent.click(divOnClick);
      expect(asyncDispatch).not.toHaveBeenCalled();
      expect(mockPublishDataLayerEvent).toHaveBeenCalled();
      expect(mockPublishDataLayerEvent).toHaveBeenCalledWith({
        name: 'edit_item_cancel',
        extraAttrs: { recognition_status: 'authenticated' },
      });
    });

    it('should show OOS text when selectedsize is OOS in edit modal', async () => {
      useProductSelectionContext.mockReturnValue({
        selectedSize: {
          inStock: false,
          skuId: '5543540023000',
        },
      });
      useEnabledFeatures.mockReturnValue({ enabledFeatures: { 'bag-ui-edit-bag': true } });
      const capiData = JSON.parse(JSON.stringify(getMockCapiData));

      capiData.selectedVariant.dimensions[0].selectedDimension = '2T'; //2T style is OOS, 3T was the selectedDimension which is inStock
      const { getByText, getByTestId } = render(
        <ProductEditDetails product={product} capiData={capiData} brandName='gap' localize={localize} onClose={onClose} userStatus='AUTHENTICATED' />
      );
      expect(getByText('product.editOOSText')).toBeInTheDocument();
      expect(getByTestId('update-button-disabled')).toBeInTheDocument();
    });
  });

  it('should not render Fulfillment options when FF is OFF', () => {
    useEnabledFeatures.mockReturnValue({ enabledFeatures: { 'bag-ui-bopis-enhacement': false } });
    const { queryByText } = render(<ActiveBag />);
    expect(queryByText('fulfillment.shipToAddress')).not.toBeInTheDocument();
    expect(queryByText('fulfillment.pickup')).not.toBeInTheDocument();
    expect(queryByText('change-store-modal')).not.toBeInTheDocument();
  });

  it('should render store name if Fulfillment options is Pickup', async () => {
    useActiveBagContext.mockReturnValue({
      bagState: {
        data: {
          currencySymbol: '$',
          bagAttributes: { bagItemsCount: { ship: 1, pickUp: 0 }, userStatus: 'AUTHENTICATED', reconition_status: 'authenticated' },
          productList: [
            {
              id: '2',
              productName: 'Product 2',
              size: 'M',
              productColor: 'Blue',
              imageUrl: 'image2.jpg',
              productFlags: [
                { message: 'RETURN_BY_MAIL', type: 'informational', date: '' },
                { message: 'NO_RETURNS', type: 'informational', date: '' },
              ],
              promotions: [
                {
                  code: 'BAGUIUS001',
                  description: 'BAGUIUS001',
                  isAutoApply: false,
                },
              ],
              regularPrice: '100',
              finalItemPrice: '80',
              totalItemSavings: '20',
              productUrl: 'product-1',
              brandAbbrName: 'Brand1',
              quantity: 2,
              maxOrderQty: 5,
              sku: 'sku1',
              fulfillment: {
                selectedType: 'PICKUP',
                pickUpStoreId: '123',
                isShipEnabled: true,
                isBopisEnabled: true,
              },
            },
          ],
        },
      },
      asyncDispatch,
    });

    const { findByText } = render(<ActiveBag />);

    expect(await findByText('fulfillment.shipToAddress')).toBeInTheDocument();
    expect(await findByText('fulfillment.pickup fulfillment.atStore')).toBeInTheDocument();
  });

  it('should render Fulfillment options if getStoreInfo call returns undefined', () => {
    useActiveBagContext.mockReturnValue({
      bagState: {
        data: {
          currencySymbol: '$',
          bagAttributes: { bagItemsCount: { ship: 1, pickUp: 0 }, userStatus: 'AUTHENTICATED', reconition_status: 'authenticated' },
          productList: [
            {
              id: '2',
              productName: 'Product 2',
              size: 'M',
              productColor: 'Blue',
              imageUrl: 'image2.jpg',
              productFlags: [
                { message: 'RETURN_BY_MAIL', type: 'informational', date: '' },
                { message: 'NO_RETURNS', type: 'informational', date: '' },
              ],
              promotions: [
                {
                  code: 'BAGUIUS001',
                  description: 'BAGUIUS001',
                  isAutoApply: false,
                },
              ],
              regularPrice: '100',
              finalItemPrice: '80',
              totalItemSavings: '20',
              productUrl: 'product-1',
              brandAbbrName: 'Brand1',
              quantity: 2,
              maxOrderQty: 5,
              sku: 'sku1',
              fulfillment: {
                selectedType: 'PICKUP',
                pickUpStoreId: '123',
                isShipEnabled: true,
                isBopisEnabled: true,
              },
            },
          ],
        },
      },
      asyncDispatch,
    });

    (getStoreInfo as jest.Mock).mockResolvedValue(undefined);

    const { queryByText } = render(<ActiveBag />);
    expect(queryByText('fulfillment.shipToAddress')).toBeInTheDocument();
    expect(queryByText('fulfillment.pickup')).toBeInTheDocument();
  });

  it('renders the EmptyBag component when productList is empty', () => {
    (useActiveBagContext as jest.Mock).mockReturnValueOnce({
      bagState: {
        data: {
          currencySymbol: '$',
          bagAttributes: { bagItemsCount: { ship: 0, pickUp: 0 }, userStatus: 'GUEST', reconition_status: 'unrecognized' },
          productList: [],
        },
      },
      asyncDispatch,
    });
    render(
      <BreakpointProvider>
        <ActiveBag />
      </BreakpointProvider>
    );
    expect(screen.getByText('emptyBag.activeBagHeader')).toBeInTheDocument();
  });

  it('renders GWP item', () => {
    (useActiveBagContext as jest.Mock).mockReturnValueOnce({
      bagState: {
        data: {
          currencySymbol: '$',
          bagAttributes: { bagItemsCount: { ship: 2, pickUp: 0 }, userStatus: 'GUEST', reconition_status: 'unrecognized' },
          productList: [
            {
              id: '1',
              productName: 'Free Item',
              size: 'M',
              productColor: 'Red',
              imageUrl: 'image1.jpg',
              productFlags: [],
              promotions: [
                {
                  code: 'PNPGWPGD',
                  description: 'PNPGWPGD',
                  isAutoApply: false,
                },
              ],
              regularPrice: '100',
              gwpIndicator: true,
              finalItemPrice: '80',
              totalItemSavings: '100',
              productUrl: 'product-Free',
              brandAbbrName: 'Brand1',
              quantity: 1,
              maxOrderQty: null,
              sku: 'skuFree',
              fulfillment: {
                selectedType: 'SHIP',
                pickUpStoreId: '',
                isShipEnabled: true,
                isBopisEnabled: true,
              },
            },
          ],
        },
      },
      asyncDispatch,
    });
    const { getByText } = render(
      <BreakpointProvider>
        <ActiveBag />
      </BreakpointProvider>
    );
    expect(getByText('product.qty 1')).toBeInTheDocument();
    expect(getByText('Promo')).toBeInTheDocument();
    expect(getByText('PNPGWPGD')).toBeInTheDocument();
    expect(getByText('Free Item')).toBeInTheDocument();
    expect(getByText('$100.00')).toBeInTheDocument();
    expect(getByText('$80.00')).toBeInTheDocument();
    expect(getByText('product.savingsText $100.00')).toBeInTheDocument();
  });

  it('calls asyncDispatch to save an item for later', async () => {
    const { getByTestId, getByText, getByRole } = render(
      <BreakpointProvider>
        <ActiveBag />
      </BreakpointProvider>
    );
    expect(getByTestId('save-for-later')).toBeInTheDocument();

    await act(() => {
      fireEvent.click(getByText('product.pliSaveForLater'));
    });

    expect(asyncDispatch).toHaveBeenCalledWith({
      action: 'SAVE_FOR_LATER',
      requestPayload: {
        lineItemId: '1',
        productName: 'Product 1',
        bagType: 'ACTIVE',
      },
    });

    expect(snackbarModule.Snackbar).toHaveBeenCalled();
    expect(snackbarModule.Snackbar).toHaveBeenCalledWith(
      expect.objectContaining({
        showIcon: false,
        kind: 'information',
        onDismiss: expect.any(Function),
      }),
      {}
    );

    const movedProductLink = getByRole('link', { name: /moved to saved bag/i });
    expect(movedProductLink).toBeInTheDocument();
    expect(movedProductLink).toHaveTextContent('Product 1');
    expect(getByText('product.moveToSavedBag')).toBeInTheDocument();
  });

  it('calls asyncDispatch to delete an item when quantity is 1', async () => {
    (useActiveBagContext as jest.Mock).mockReturnValue({
      bagState: {
        data: {
          currencySymbol: '$',
          bagAttributes: { bagItemsCount: { ship: 1, pickUp: 0 }, userStatus: 'AUTHENTICATED', reconition_status: 'authenticated' },
          productList: [
            {
              id: '1',
              productName: 'Product 1',
              size: 'M',
              productColor: 'Red',
              imageUrl: 'image1.jpg',
              productFlags: [],
              promotions: [],
              regularPrice: '100',
              markdownPrice: '80',
              totalItemSavings: '20',
              productUrl: 'product-1',
              brandAbbrName: 'Brand1',
              quantity: 1,
              maxOrderQty: 5,
              sku: 'sku1',
              fulfillment: {
                selectedType: 'PICKUP',
                pickUpStoreId: '220',
                isShipEnabled: true,
                isBopisEnabled: true,
              },
            },
          ],
        },
      },
      asyncDispatch,
    });
    render(
      <BreakpointProvider>
        <ActiveBag />
      </BreakpointProvider>
    );
    fireEvent.click(screen.getByRole('button', { name: /Trash/i }));

    await waitFor(() => {
      expect(asyncDispatch).toHaveBeenCalledWith({
        action: 'DELETE_BAG_ITEM',
        requestPayload: {
          lineItemId: '1',
          productName: 'Product 1',
          bagType: 'SAVED',
        },
      });
    });
  });

  it('calls asyncDispatch to update item quantity', async () => {
    render(
      <BreakpointProvider>
        <ActiveBag />
      </BreakpointProvider>
    );
    fireEvent.click(screen.getByRole('button', { name: /Minus/i }));

    await waitFor(
      () => {
        expect(asyncDispatch).toHaveBeenCalledWith({
          action: 'UPDATE_QUANTITY',
          requestPayload: {
            lineItemId: '1',
            sku: 'sku1',
            value: 1,
          },
        });
      },
      { timeout: 3000 }
    );

    fireEvent.click(screen.getByRole('button', { name: /Plus/i }));

    await waitFor(
      () => {
        expect(asyncDispatch).toHaveBeenCalledWith({
          action: 'UPDATE_QUANTITY',
          requestPayload: {
            lineItemId: '1',
            sku: 'sku1',
            value: 2,
          },
        });
      },
      { timeout: 3000 }
    );
  });

  it('renders dropship item', () => {
    (useActiveBagContext as jest.Mock).mockReturnValueOnce({
      bagState: {
        data: {
          currencySymbol: '$',
          bagAttributes: { bagItemsCount: { ship: 1, pickUp: 0 }, userStatus: 'ANONYMOUS', reconition_status: 'anonymous' },
          productList: [
            {
              regularPrice: 27,
              vendorId: '3000554',
              markdownPrice: 27,
              finalItemPrice: 27,
              finalPrice: 27,
              productUrl: 'https://www.stage.gaptechol.com/browse/product.do?pid=1129200020000',
              imageUrl: 'https://www1.assets-gap.com/Asset_Archive/webcontent2/0052/716/348/cn52716348.jpg',
              webVendorName: 'STAGE TEST SELLER 2',
              primaryCategoryName: 'Shop All Teen',
              productName: 'Seller 2 Commission Rate 1681818228675 0',
              productColor: 'Red',
              size: 'One Size',
              sku: '1129200020000',
              categoryNumber: '',
              savingsPrice: 0,
              totalItemSavings: 0,
              quantity: 1,
              maxOrderQty: 5,
              brandId: '1',
              brandAbbrName: 'GAP',
              brandFullName: 'Gap',
              id: 'f4960ffb82524a6e805d358f1e4c15f2',
              gwpIndicator: false,
              styleNumber: '112920',
              productType: 'Dropship',
              fulfillment: {
                selectedType: 'SHIP',
                pickUpStoreId: '',
                isShipEnabled: true,
                isBopisEnabled: false,
              },
              promotions: [],
              error: {},
              productFlags: [
                {
                  message: 'MADE_TO_ORDER',
                  type: 'warning',
                },
                {
                  message: 'RETURN_BY_MAIL',
                  type: 'informational',
                },
                {
                  message: 'EXCLUDED_FROM_PROMOTION',
                  type: 'informational',
                },
              ],
            },
          ],
        },
      },
      asyncDispatch,
    });
    const { getByText } = render(
      <BreakpointProvider>
        <ActiveBag />
      </BreakpointProvider>
    );
    expect(getByText('product.myBagHeader')).toBeInTheDocument();
    expect(getByText('fulfillment.shipToAddress')).toBeInTheDocument();
    expect(getByText('fulfillment.disabled')).toBeInTheDocument();
  });
  it('renders dropship vendor text', () => {
    (useActiveBagContext as jest.Mock).mockReturnValueOnce({
      bagState: {
        data: {
          currencySymbol: '$',
          bagAttributes: { bagItemsCount: { ship: 1, pickUp: 0 }, userStatus: 'RECOGNIZED', reconition_status: 'recognized' },
          productList: [
            {
              regularPrice: 27,
              vendorId: '3000554',
              markdownPrice: 27,
              finalItemPrice: 27,
              finalPrice: 27,
              productUrl: 'https://www.stage.gaptechol.com/browse/product.do?pid=1129200020000',
              imageUrl: 'https://www1.assets-gap.com/Asset_Archive/webcontent2/0052/716/348/cn52716348.jpg',
              webVendorName: 'STAGE TEST SELLER 2',
              primaryCategoryName: 'Shop All Teen',
              productName: 'Seller 2 Commission Rate 1681818228675 0',
              productColor: 'Red',
              size: 'One Size',
              sku: '1129200020000',
              categoryNumber: '',
              savingsPrice: 0,
              totalItemSavings: 0,
              quantity: 1,
              maxOrderQty: 5,
              brandId: '1',
              brandAbbrName: 'GAP',
              brandFullName: 'Gap',
              id: 'f4960ffb82524a6e805d358f1e4c15f2',
              gwpIndicator: false,
              styleNumber: '112920',
              productType: 'Dropship',
              fulfillment: {
                selectedType: 'SHIP',
                pickUpStoreId: '',
                isShipEnabled: true,
                isBopisEnabled: false,
              },
              promotions: [],
              error: {},
              productFlags: [
                {
                  message: 'MADE_TO_ORDER',
                  type: 'warning',
                },
                {
                  message: 'RETURN_BY_MAIL',
                  type: 'informational',
                },
                {
                  message: 'EXCLUDED_FROM_PROMOTION',
                  type: 'informational',
                },
              ],
            },
          ],
        },
      },
      asyncDispatch,
    });
    const { getByText, getByTestId } = render(
      <BreakpointProvider>
        <ActiveBag />
      </BreakpointProvider>
    );
    expect(getByText('product.myBagHeader')).toBeInTheDocument();
    expect(getByText('fulfillment.shipToAddress')).toBeInTheDocument();
    expect(getByText('fulfillment.dropShipVendorText')).toBeInTheDocument();
    expect(getByText('STAGE TEST SELLER 2')).toBeInTheDocument();
    expect(getByText('fulfillment.disabled')).toBeInTheDocument();
    expect(getByTestId('active-item-0').className).not.toMatch(/.*max-w-.*/);
  });
});
