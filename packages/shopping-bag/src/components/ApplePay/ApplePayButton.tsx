import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useEffect, useRef, useState } from 'react';
import { Market } from '@ecom-next/utils/server';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { Button } from '@ecom-next/core/migration/button';
import type { ShippingContact, UserState } from '../ApplePay/types';
import { getHeaders } from '../../utils/getHeaders';
import { useDatalayer } from '../../hooks/useDataLayer';
import { fetchTokenInfo } from '../../http-client/fetchTokenInfo';
import { Feature, logNewRelicError } from '../../utils/newrelic-logger';
import { useBagContext } from '../../contexts/ShoppingBagProvider';
import { createApplePaySession } from './createApplePaySession';

export interface RenderApplePayButtonProps {
  currencyCode: string;
  fromStickyCheckout?: boolean;
  market: Market;
  total: number;
}

export const ApplePayButton = ({ total, currencyCode, market, fromStickyCheckout }: RenderApplePayButtonProps) => {
  const { publishDataLayerEvent } = useDatalayer();
  const { setNotification } = useBagContext();
  const pageContext = usePageContext();
  const headers = getHeaders(pageContext);
  headers.set('x-gap-payment-type', 'ApplePay');
  const [resetApplePay, setResetApplePay] = useState(false);

  const shippingContact = useRef<ShippingContact>({
    postalCode: '',
    locality: '',
    administrativeArea: '',
    countryCode: '',
  });

  const setShippingContact = (newShippingContact: ShippingContact) => {
    shippingContact.current = newShippingContact;
  };

  const getShippingContact = () => shippingContact.current;

  const resetShippingContact = () => {
    setShippingContact({
      postalCode: '',
      locality: '',
      administrativeArea: '',
      countryCode: '',
    });
    setResetApplePay(!resetApplePay);
  };

  const { localize } = useLocalize();
  const [userRecognized, setUserRecognized] = useState<UserState | null>(null);
  useEffect(() => {
    if (userRecognized === null) {
      fetchTokenInfo(headers)
        .then(response => {
          const { userState } = response;
          setUserRecognized(userState);
        })
        .catch((err: unknown) => {
          logNewRelicError(err as Error, {
            caller: 'ApplePayButton useEffect() fetchTokenInfo()',
            feature: Feature.APPLE_PAY,
            message: 'Error fetching user state',
          });
        });
    }
  }, []);

  const applePayButtonClicked = () => {
    publishDataLayerEvent({ name: fromStickyCheckout ? 'sticky_applepay_attempt' : 'applepay' });

    const applePaySession = createApplePaySession(
      {
        total,
        currencyCode,
        market,
        userRecognized: userRecognized!,
        localize,
        getShippingContact,
        setShippingContact,
        resetShippingContact,
      },
      headers,
      setNotification
    );

    try {
      applePaySession?.begin();
    } catch (err: unknown) {
      logNewRelicError(err as Error, { caller: 'applePayButtonClicked()', feature: Feature.APPLE_PAY });
    }
  };

  return (
    <Button kind='secondary' onClick={applePayButtonClicked} fullWidth data-testid='apple-pay-button'>
      <div
        style={{
          backgroundImage: '-webkit-named-image(apple-pay-logo-black)',
          height: '1.3rem',
          backgroundSize: 'contain',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
        }}
      ></div>
    </Button>
  );
};
