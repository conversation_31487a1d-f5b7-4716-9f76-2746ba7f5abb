import { GetHeadersResponse } from '../../../utils/getHeaders';
import { parseShippingContactInfo } from '../../ApplePay/validation-schemas/parseShippingContactInfo';
import { fetchPaymentShipping } from '../../../http-client/fetchPaymentShipping';
import { createPaymentShipping } from '../../ApplePay/utils/createPaymentShipping';
import { createLineItems } from '../../ApplePay/utils/createLineItems';
import { PaymentRequestOptions } from '../types';
import { handleShippingContact } from '../handleShippingContact';

jest.mock('../../ApplePay/validation-schemas/parseShippingContactInfo');
jest.mock('../../../http-client/fetchPaymentShipping');
jest.mock('../../ApplePay/utils/createPaymentShipping');
jest.mock('../../ApplePay/utils/createLineItems');

describe('handleShippingContact', () => {
  const evt = {
    shippingContact: {
      // shipping contact details
    },
  } as ApplePayJS.ApplePayShippingContactSelectedEvent;
  const localize = jest.fn();
  const applePaySession = {
    abort: jest.fn(),
    completeShippingContactSelection: jest.fn(),
  } as unknown as ApplePaySession;
  const headers = { get: jest.fn(() => 'en_US') } as unknown as GetHeadersResponse;
  const setShippingContact = jest.fn();
  const paymentInfo = {
    market: 'US',
    total: 100,
  } as PaymentRequestOptions;

  beforeAll(() => {
    // @ts-ignore
    window.ApplePayError = jest.fn();
    // @ts-ignore
    window.ApplePaySession = { STATUS_SUCCESS: 0 };
  });

  beforeEach(() => {
    jest.clearAllMocks();
    sessionStorage.clear();
  });

  afterAll(() => {
    // @ts-ignore
    window.ApplePayError = undefined;
    window.ApplePaySession = undefined;
  });

  it('should set the shipping contact from the event', async () => {
    (parseShippingContactInfo as jest.Mock).mockReturnValue({ success: true });
    await handleShippingContact({ evt, localize, applePaySession, headers, setShippingContact, paymentInfo });

    expect(setShippingContact).toHaveBeenCalledWith(evt.shippingContact);
  });

  it('should handle successful shipping contact selection', async () => {
    (parseShippingContactInfo as jest.Mock).mockReturnValue({ success: true });
    // Mock the fetchPaymentShipping function
    (fetchPaymentShipping as jest.Mock).mockResolvedValue({
      order: {
        shippingMethods: [
          {
            isSelected: true,
            identifier: 1,
          },
          {
            isSelected: false,
            identifier: 2,
          },
        ],
      },
    });
    (createPaymentShipping as jest.Mock).mockReturnValue({ shippingMethods: [], totalPrice: 100 });
    (createLineItems as jest.Mock).mockReturnValue([{}]);

    await handleShippingContact({ evt, localize, applePaySession, headers, setShippingContact, paymentInfo });

    expect(applePaySession.completeShippingContactSelection).toHaveBeenCalled();
    expect(setShippingContact).toHaveBeenCalledWith(evt.shippingContact);
  });

  it('should convert full state name to abbreviation', async () => {
    await handleShippingContact({
      // @ts-ignore
      evt: {
        shippingContact: {
          locality: 'locality',
          administrativeArea: 'California',
          countryCode: 'countryCode',
          postalCode: 'postalCode',
        },
      },
      localize,
      applePaySession,
      headers,
      setShippingContact,
      paymentInfo,
    });

    expect(fetchPaymentShipping).toHaveBeenCalledWith({
      shippingAddress: {
        city: 'locality',
        state: 'CA',
        country: 'countryCode',
        zipCode: 'postalCode',
      },
      headers: expect.any(Object),
    });
  });

  it('should handle shipping contact selection with validation errors', async () => {
    // Mock the parseShippingContactInfo function to return validation errors
    (parseShippingContactInfo as jest.Mock).mockReturnValue({
      success: false,
      error: {
        errors: [
          {
            name: 'Error',
            message: 'Invalid shipping contact',
            path: ['emailAddress'],
          },
        ],
      },
    });

    await handleShippingContact({ evt, localize, applePaySession, headers, setShippingContact, paymentInfo });

    expect(applePaySession.completeShippingContactSelection).toHaveBeenCalled();
  });

  it('should handle errors during shipping contact selection', async () => {
    (parseShippingContactInfo as jest.Mock).mockReturnValue({ success: true });
    // Mock the fetchPaymentShipping function to throw an error
    (fetchPaymentShipping as jest.Mock).mockRejectedValue(new Error('Fetch error'));
    await handleShippingContact({ evt, localize, applePaySession, headers, setShippingContact, paymentInfo });

    expect(applePaySession.abort).toHaveBeenCalled();
  });

  it('should sort payment shipping methods correctly', async () => {
    (parseShippingContactInfo as jest.Mock).mockReturnValue({ success: true });
    const shippingMethods = [
      { identifier: 2, isSelected: false },
      { identifier: 1, isSelected: true },
    ];
    (fetchPaymentShipping as jest.Mock).mockResolvedValue({
      order: { shippingMethods },
    });
    (createPaymentShipping as jest.Mock).mockReturnValue({ shippingMethods, totalPrice: 100 });
    (createLineItems as jest.Mock).mockReturnValue([{}]);

    sessionStorage.setItem('shippingOptionId', '1');

    await handleShippingContact({ evt, localize, applePaySession, headers, setShippingContact, paymentInfo });

    expect(applePaySession.completeShippingContactSelection).toHaveBeenCalledWith(
      ApplePaySession.STATUS_SUCCESS,
      [
        { identifier: 1, isSelected: true },
        { identifier: 2, isSelected: false },
      ],
      expect.any(Object),
      expect.any(Array)
    );
  });
});
