/* eslint-disable @typescript-eslint/no-explicit-any */
import { shouldShowApplePay } from '../shouldShowApplePay';

describe('shouldShowApplePay', () => {
  let originalApplePaySession: any;
  let originalLocation: Location;

  beforeEach(() => {
    originalApplePaySession = window.ApplePaySession;
    originalLocation = window.location;
    delete (window as any).location;
    window.location = { ...originalLocation, protocol: 'https:' } as any;
  });

  afterEach(() => {
    window.ApplePaySession = originalApplePaySession;
    window.location = originalLocation;
  });

  it('returns false if isApplePay is false', () => {
    expect(shouldShowApplePay(false, false, true, true)).toBe(false);
  });

  it('returns false if window is undefined', () => {
    const originalWindow = global.window;
    delete (global as any).window;
    expect(shouldShowApplePay(true, false, true, true)).toBe(false);
    global.window = originalWindow;
  });

  it('returns false if ApplePaySession is not available', () => {
    delete window.ApplePaySession;
    expect(shouldShowApplePay(true, false, true, true)).toBe(false);
  });

  it('returns false if protocol is not https', () => {
    window.location.protocol = 'http:';
    expect(shouldShowApplePay(true, false, true, true)).toBe(false);
  });

  it('returns false if ApplePaySession cannot make payments', () => {
    window.ApplePaySession = { canMakePayments: () => false };
    expect(shouldShowApplePay(true, false, true, true)).toBe(false);
  });

  it('returns false if hasBopis is true', () => {
    window.ApplePaySession = { canMakePayments: () => true };
    expect(shouldShowApplePay(true, true, true, true)).toBe(false);
  });

  it('returns false if isGapBagType is false', () => {
    window.ApplePaySession = { canMakePayments: () => true };
    expect(shouldShowApplePay(true, false, false, true)).toBe(false);
  });

  it('returns true if isGapBagType is true', () => {
    window.ApplePaySession = { canMakePayments: () => true };
    expect(shouldShowApplePay(true, false, true, true)).toBe(true);
  });

  it('returns false if hasActiveItems is false', () => {
    window.ApplePaySession = { canMakePayments: () => true };
    expect(shouldShowApplePay(true, false, false, false)).toBe(false);
  });

  it('returns true if all conditions are met', () => {
    window.ApplePaySession = { canMakePayments: () => true };
    expect(shouldShowApplePay(true, false, true, true)).toBe(true);
  });
});
