import { Market } from '@ecom-next/utils/server';
import { fetchPlaceOrder } from '../../http-client/fetchPlaceOrder';
import { fetchTokenInfo } from '../../http-client/fetchTokenInfo';
import { GetHeadersResponse } from '../../utils/getHeaders';
import { maskShopperId } from '../../utils/maskShopperId';
import { redirectToOrderConfirmationPage } from '../../utils/redirectToOrderConfirmationPage';
import { NotificationContext } from '../../contexts/ShoppingBagProvider';
import { toUpper } from '../../utils/toUpper';
import { logNewRelicError, Feature } from '../../utils/newrelic-logger';
import { mapPaymentErrors } from './utils/mapPaymentErrors';
import { PaymentInfo, parsePaymentInfo } from './validation-schemas/parsePaymentInfo';
import { createOrderBody } from './utils/createOrderBody';

interface HandlePaymentArgs {
  evt: ApplePayJS.ApplePayPaymentAuthorizedEvent;
  headers: GetHeadersResponse;
  localize: (translation: string) => string;
  market: Market | Uppercase<Market>;
  session: ApplePaySession;
  setExpressCheckoutError?: (errorObj: { expressPayment: 'APPLEPAY'; type: 'PAYMENT' }) => void;
  setNotification: NotificationContext['setNotification'];
  shopperId: string;
  publishDataLayerEvent?: ({ name }: { name: string }) => void;
}

type ATErrorDetails = {
  developerMessage: string;
  errorCode: '4121' | '4122';
  moreInfo: string;
  userMessage: string;
};

type ATError = {
  details: {
    '0': ATErrorDetails;
  };
  message: string;
  status: string;
};

export const handlePayment = async ({
  evt,
  shopperId,
  headers,
  market,
  session,
  localize,
  setNotification,
  publishDataLayerEvent,
  setExpressCheckoutError = () => undefined,
}: HandlePaymentArgs) => {
  try {
    const { payment } = evt;
    const { userState } = await fetchTokenInfo(headers);
    const validationResult = parsePaymentInfo(payment as PaymentInfo, {
      userState,
      market: toUpper(market),
    });

    if (!validationResult.success) {
      const errorResult = mapPaymentErrors(validationResult, localize);

      session.completePayment({
        status: ApplePaySession.STATUS_FAILURE,
        errors: errorResult,
      });

      logNewRelicError(errorResult.map(error => error.code).join(', '), {
        caller: 'handlePayment()',
        feature: Feature.APPLE_PAY,
        message: errorResult.map(error => error.message).join(', '),
      });

      return errorResult;
    }

    const shippingOptionId = sessionStorage.getItem('shippingOptionId') || '';
    const timeout = 30000; // Overriding default timeout of 5000ms

    const orderBody = createOrderBody({ payment, shippingOptionId });

    const paymentOrderInfo = await fetchPlaceOrder({ checkoutPayload: orderBody, headers, timeout });

    setNotification(undefined);

    const isSuccessfulResponse = !!paymentOrderInfo?.orderNumber;

    const maskedShopperId = maskShopperId(shopperId);

    if (!isSuccessfulResponse) {
      const error = new Error(`Request failed with status code 200 for shopperId ${maskedShopperId}`);
      logNewRelicError(error, { caller: 'handlePayment()', feature: Feature.APPLE_PAY, message: 'placeOrder 200 failure' });
      setExpressCheckoutError?.({ type: 'PAYMENT', expressPayment: 'APPLEPAY' });
      publishDataLayerEvent?.({ name: 'applepay_error' });

      return session.completePayment(ApplePaySession.STATUS_FAILURE);
    }

    sessionStorage.removeItem('shippingOptionId');
    publishDataLayerEvent?.({ name: 'applepay_success' });
    return redirectToOrderConfirmationPage(paymentOrderInfo.orderNumber);
  } catch (error) {
    const errorCode = (error as ATError)?.details?.['0'].errorCode;
    logNewRelicError(error as Error, { caller: 'handlePayment()', feature: Feature.APPLE_PAY, message: 'Error placing order' });
    if (errorCode === '4121') {
      setNotification({ message: 'PAYMENT_QUEBEC_ATH_ONLY_ERROR_MESSAGE', type: 'error', isDismissible: true, inline: false });
    } else if (errorCode === '4122') {
      setNotification({ message: 'PAYMENT_QUEBEC_ATH_OTHER_BRANDS_ERROR_MESSAGE', type: 'error', isDismissible: true, inline: false });
    }
    setExpressCheckoutError?.({ type: 'PAYMENT', expressPayment: 'APPLEPAY' });
    publishDataLayerEvent?.({ name: 'applepay_error' });
    session.completePayment(ApplePaySession.STATUS_FAILURE);
    return undefined;
  }
};
