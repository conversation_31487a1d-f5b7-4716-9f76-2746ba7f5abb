import { generateStateAbbrWithLocale } from '@ecom-next/my-account/addressUtil';
import { mapApplePayErrors } from '../ApplePay/utils/mapApplePayErrors';
import { parseShippingContactInfo, type ShippingContactInfo } from '../ApplePay/validation-schemas/parseShippingContactInfo';
import type { PaymentRequestOptions } from '../ApplePay/types';
import { createPaymentShipping } from '../ApplePay/utils/createPaymentShipping';
import { fetchPaymentShipping } from '../../http-client/fetchPaymentShipping';
import { createShippingInfo } from '../ApplePay/utils/createShippingInfo';
import { createLineItems } from '../ApplePay/utils/createLineItems';
import type { GetHeadersResponse } from '../../utils/getHeaders';
import type { ShippingMethod } from '../../types/payment-types';
import { toUpper } from '../../utils/toUpper';
import { logNewRelicError, Feature } from '../../utils/newrelic-logger';

export const handleShippingContact = async ({
  applePaySession,
  evt,
  headers,
  localize,
  paymentInfo,
  setShippingContact,
}: {
  applePaySession: ApplePaySession;
  evt: ApplePayJS.ApplePayShippingContactSelectedEvent;
  headers: GetHeadersResponse;
  localize: (translation: string) => string;
  paymentInfo: PaymentRequestOptions;
  setShippingContact: (shippingContact: ShippingContactInfo) => void;
}) => {
  const shippingContactFromEvent = evt.shippingContact;
  setShippingContact(shippingContactFromEvent as ShippingContactInfo);

  const shippingContactInfo = createShippingInfo(shippingContactFromEvent);
  const { locality, administrativeArea, countryCode, postalCode } = shippingContactInfo;
  const market = toUpper(paymentInfo.market);
  const administrativeAreaCode = generateStateAbbrWithLocale(administrativeArea!, countryCode?.toLowerCase(), headers.get('locale')!) || administrativeArea;

  //Validating the shipping contact info
  const shippingContactInfoParsed = parseShippingContactInfo(shippingContactInfo as ShippingContactInfo, market);

  if (!shippingContactInfoParsed?.success) {
    const applePayErrors = mapApplePayErrors(shippingContactInfoParsed.error, localize);
    const applePayShippingContactUpdate = {
      errors: applePayErrors,
      newTotal: {
        label: localize('applePay.freeShippingLabel'),
        amount: paymentInfo.total.toString(),
      },
    };
    applePaySession.completeShippingContactSelection(applePayShippingContactUpdate);
    return;
  }

  try {
    let shippingOptionId = sessionStorage.getItem('shippingOptionId');

    const responsePaymentOrder = await fetchPaymentShipping({
      shippingAddress: {
        zipCode: postalCode!,
        city: locality!,
        state: administrativeAreaCode!,
        country: countryCode!,
      },
      ...(shippingOptionId ? { shippingOptionId } : {}),
      headers: headers,
    });

    if (!shippingOptionId) {
      const selectedShippingMethod = responsePaymentOrder.order.shippingMethods.find(method => method.isSelected) as ShippingMethod;
      shippingOptionId = selectedShippingMethod?.shippingId?.toString();

      if (shippingOptionId) {
        sessionStorage.setItem('shippingOptionId', shippingOptionId);
      }
    }

    const paymentShipping = createPaymentShipping(responsePaymentOrder);
    const sortedPaymentShipping = paymentShipping.shippingMethods.sort(a => (a.identifier === Number(shippingOptionId) ? -1 : 0));
    applePaySession.completeShippingContactSelection(
      ApplePaySession.STATUS_SUCCESS, // eslint-disable-line no-undef
      sortedPaymentShipping as ApplePayJS.ApplePayShippingMethod[],
      {
        label: 'GAP INC.',
        amount: paymentShipping.totalPrice.toString(),
      },
      createLineItems(paymentShipping, localize)
    );
  } catch (error) {
    if ((error as { details: { errorCode: string } })?.details?.errorCode === 'BAGAPI-1100') {
      const applePayShippingContactUpdate = {
        errors: [new ApplePayError('shippingContactInvalid', 'administrativeArea')],
        newTotal: {
          label: localize('applePay.freeShippingLabel'),
          amount: paymentInfo.total.toString(),
        },
      };
      applePaySession.completeShippingContactSelection(applePayShippingContactUpdate);
      return;
    }
    applePaySession.abort();
    logNewRelicError(error as Error, { caller: 'handleShippingContact', feature: Feature.APPLE_PAY, message: 'Error fetching payment shipping' });
  }
};
