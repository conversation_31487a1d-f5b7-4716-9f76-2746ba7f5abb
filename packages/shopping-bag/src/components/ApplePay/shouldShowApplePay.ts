export const shouldShowApplePay = (isApplePay: boolean, hasBopis: boolean, isGapBagType: boolean, hasActiveItems: boolean): boolean => {
  if (isApplePay && typeof window !== 'undefined' && window) {
    return (
      !!window.ApplePaySession &&
      window.location.protocol === 'https:' &&
      window.ApplePaySession.canMakePayments() &&
      !hasBopis &&
      isGapBagType &&
      hasActiveItems
    );
  }
  return false;
};
