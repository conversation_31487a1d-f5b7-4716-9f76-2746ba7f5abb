import { mapAppleApiNameToAppleFormValueName } from '../mapAppleApiNameToAppleFormValueName';

describe('mapAppleApiNameToAppleFormValueName', () => {
  it('should map API name to form value name', () => {
    const apiNames = ['familyName', 'givenName'] as const;
    const formValueNames = apiNames.map(mapAppleApiNameToAppleFormValueName);

    formValueNames.forEach(formValueName => expect(formValueName).toEqual('name'));
  });

  it('should return the original name if no mapping is found', () => {
    const apiName = 'emailAddress';
    const formValueName = mapAppleApiNameToAppleFormValueName(apiName);

    expect(formValueName).toEqual(apiName);
  });
});
