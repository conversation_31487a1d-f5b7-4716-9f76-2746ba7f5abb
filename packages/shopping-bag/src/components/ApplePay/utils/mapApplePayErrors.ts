import { mapAppleApiNameToAppleFormValueName } from './mapAppleApiNameToAppleFormValueName';

export const mapApplePayErrors = (validatedError: Zod.ZodError, localize: (translation: string) => string) => {
  return validatedError.errors.map(
    error =>
      new ApplePayError(
        'shippingContactInvalid',
        mapAppleApiNameToAppleFormValueName(error.path[0] as ApplePayJS.ApplePayErrorContactField),
        localize(error.message)
      )
  );
};
