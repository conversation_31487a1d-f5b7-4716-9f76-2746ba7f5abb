import { PaymentInfo } from '../validation-schemas/parsePaymentInfo';
import { mapAppleApiNameToAppleFormValueName } from './mapAppleApiNameToAppleFormValueName';

export const mapPaymentErrors = (validationErrors: Zod.SafeParseError<PaymentInfo>, localize: (translation: string) => string) => {
  return validationErrors.error.errors.map(({ path, message }) => {
    const inputType = path.includes('addressLines')
      ? 'addressLines'
      : mapAppleApiNameToAppleFormValueName(path[path.length - 1] as unknown as ApplePayJS.ApplePayErrorContactField);

    return new ApplePayError(String(path[0]).concat('Invalid') as unknown as ApplePayJS.ApplePayErrorCode, inputType, localize(message));
  });
};
