import { z } from 'zod';
import { Market } from '@ecom-next/utils/server';
import { UserStatus } from '../../../types/bag-types';
import { mapPostalCodeCompleteRegex } from './matchPostalCode';

const paymentContactInfoParser = z.object({
  givenName: z
    .string({ required_error: 'applePay.firstNameIsRequired' })
    .max(30, { message: 'applePay.firstNameValidationMessage' })
    .regex(/^[a-zA-Z]+(([',. -][a-zA-Z ])?[a-zA-Z]*)*$/, {
      message: 'applePay.firstNameContainsLetters',
    }),
  familyName: z
    .string({ required_error: 'applePay.lastNameIsRequired' })
    .max(30, { message: 'applePay.lastNameValidationMessage' })
    .regex(/^[a-zA-Z]+(([',. -][a-zA-Z ])?[a-zA-Z]*)*$/, {
      message: 'applePay.lastNameContainsLetters',
    }),
  addressLines: z
    .array(z.string({ required_error: 'applePay.addressIsRequired' }).max(30, { message: 'applePay.addressValidationMessage' }))
    .min(1, { message: 'applePay.addressIsRequired' }),
  locality: z.string({ required_error: 'applePay.cityIsRequired' }).max(30, { message: 'applePay.cityValidationMessage' }),
  countryCode: z.union([z.literal('US'), z.literal('CA')], {
    required_error: 'applePay.countryIsRequired',
    invalid_type_error: 'applePay.countryIsInvalid',
  }),
  administrativeArea: z.string({ required_error: 'applePay.stateIsRequired' }).max(30, { message: 'applePay.stateValidationMessage' }),
});

type PaymentOptions = { market: Uppercase<Market>; userState: UserStatus };

const createPaymentInfoParser = ({ userState, market }: PaymentOptions) => {
  const postalCode = z.object({
    postalCode: z
      .string({ required_error: 'applePay.postalCodeIsRequired' })
      .regex(mapPostalCodeCompleteRegex[market], { message: 'applePay.postalCodeIsInvalid' })
      .max(15, { message: 'applePay.postalCodeValidationMessage' }),
  });
  const contactParser = z.intersection(paymentContactInfoParser, postalCode);

  return z.object({
    billingContact: contactParser,
    shippingContact: z.intersection(
      contactParser,
      z
        .object({
          phoneNumber: z.string({ required_error: 'applePay.phoneNumberIsRequired' }).regex(/^(\+\d{1,3}\s?)?(\D?(\d{3})\D?\D?(\d{3})\D?(\d{4}))$/, {
            message: 'applePay.phoneNumberInvalid',
          }),
          emailAddress: z
            .string(userState === 'AUTHENTICATED' ? {} : { required_error: 'applePay.emailIsRequired' })
            .email()
            .max(65),
        })
        .partial({
          ...(userState === 'AUTHENTICATED' && { emailAddress: true }),
        })
    ),
  });
};

type PaymentContactInfo = z.infer<typeof paymentContactInfoParser> & { postalCode: string };
export interface PaymentInfo {
  billingContact: PaymentContactInfo;
  shippingContact: PaymentContactInfo & { emailAddress?: string; phoneNumber: string };
}

export const parsePaymentInfo = (paymentInfo: PaymentInfo, { userState, market }: PaymentOptions) =>
  createPaymentInfoParser({ market, userState }).safeParse(paymentInfo);
