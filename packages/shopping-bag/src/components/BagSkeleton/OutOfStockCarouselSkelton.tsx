import { LoadingPlaceholder } from '@ecom-next/core/migration/loading-placeholder';
import classNames from 'classnames';
import { useBreakpoint } from '../../hooks/useBreakPoint';

export const OutOfStockCarouselSkelton = () => {
  const { isMobile, isMediumDevice, isLargeDevice, isExtraLargeDevice } = useBreakpoint();
  const desktopDevice = isLargeDevice || isExtraLargeDevice;
  const slidesToShowForDesktop = 4;

  return desktopDevice ? (
    // Below is the code to handle skelton for desktop devices.
    <div className='flex w-full items-start'>
      {Array.from({ length: slidesToShowForDesktop }).map((_: unknown, index: number) => (
        <div key={index}>
          <div className='pb-2 pr-4'>
            <LoadingPlaceholder fixedSize={{ width: 120, height: 160 }}></LoadingPlaceholder>
          </div>
          <div className='pb-1'>
            <LoadingPlaceholder fixedSize={{ width: isMobile ? 89 : 89, height: 18 }} />
          </div>
          <div className='pb-1'>
            <LoadingPlaceholder fixedSize={{ width: isMobile ? 56 : 56, height: 18 }} />
          </div>
        </div>
      ))}
    </div>
  ) : (
    <div
      className={classNames({
        'flex h-[346px] w-full': isMediumDevice,
        'flex h-[336px] w-full': isMobile,
      })} // Over all height for image content Mobile and Medium devices.
    >
      <div
        className={classNames({
          'h-[90%] w-[30%] pr-4': isMediumDevice,
          'w-[40%] pr-4': isMobile,
        })}
      >
        <div
          className={classNames({
            'flex h-[274px] w-full': isMediumDevice,
            'flex h-[264px] w-full': isMobile,
          })} // Image height for Mobile and Medium devices to reset the h-full in loadingPlaceholder.
        >
          <LoadingPlaceholder />
        </div>
        <div>
          <LoadingPlaceholder fixedSize={{ width: 89, height: 18 }} className='mt-2' />
          <LoadingPlaceholder fixedSize={{ width: 56, height: 18 }} className='mt-1' />
        </div>
      </div>
      <div
        className={classNames({
          'h-[90%] w-[30%] pr-4': isMediumDevice,
          'w-[40%] pr-4': isMobile,
        })}
      >
        <div
          className={classNames({
            'flex h-[274px] w-full': isMediumDevice,
            'flex h-[264px] w-full': isMobile,
          })}
        >
          <LoadingPlaceholder />
        </div>
        <LoadingPlaceholder fixedSize={{ width: 89, height: 18 }} className='mt-2' />
        <LoadingPlaceholder fixedSize={{ width: 56, height: 18 }} className='mt-1' />
      </div>
      <div
        className={classNames({
          'h-[90%] w-[30%] pr-4': isMediumDevice,
          'w-[20%]': isMobile,
        })}
      >
        <div
          className={classNames({
            'flex h-[274px] w-full': isMediumDevice,
            'flex h-[264px] w-full': isMobile,
          })}
        >
          <LoadingPlaceholder />
        </div>
        <LoadingPlaceholder fixedSize={{ width: isMediumDevice ? 89 : 64, height: 18 }} className='mt-2' />
        <LoadingPlaceholder fixedSize={{ width: 56, height: 18 }} className='mt-1' />
      </div>
      <div
        className={classNames({
          'h-[90%] w-[10%]': isMediumDevice,
          'w-[0%]': isMobile,
        })}
      >
        <div
          className={classNames({
            'flex h-[274px] w-full': isMediumDevice,
            'flex h-[264px] w-full': isMobile,
          })}
        >
          <LoadingPlaceholder />
        </div>
        <LoadingPlaceholder fixedSize={{  width: isMediumDevice ? 64 : 0, height: 18 }} className='mt-2' />
        <LoadingPlaceholder fixedSize={{ width: isMediumDevice ? 54 : 0, height: 18 }} className='mt-1' />
      </div>
    </div>
  );
};
