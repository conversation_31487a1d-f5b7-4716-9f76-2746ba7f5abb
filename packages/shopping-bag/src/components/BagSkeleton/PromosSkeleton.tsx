import { useLocalize } from '@ecom-next/sitewide/localization-provider';

export const PromosSkeleton = () => {
  const { localize } = useLocalize();

  return (
    <div className='mt-4 w-full bg-white px-4 py-4'>
      <h2 className='mb-4 text-base font-crossbrand font-bold'>{localize('promos.header')}</h2>
      <div className='my-4 flex h-[44px] w-full flex-row items-end md:w-full'>
        <div className='mr-2 h-[44px] w-full md:w-44'>
          <input
            type='text'
            disabled={true}
            className='border-gray-30 text-gray-30 h-[44px] w-full rounded border bg-white bg-opacity-50 p-4'
            value={localize('promos.enterPromoCode')}
          />
        </div>
        <div className='w-auto'>
          <input
            type='button'
            disabled={true}
            className='border-gray-30 text-gray-30 h-[44px]  w-full w-full rounded rounded border border bg-opacity-50 bg-none px-4 px-4 text-base font-bold'
            value={localize('promos.apply')}
          />
        </div>
      </div>
    </div>
  )
};
