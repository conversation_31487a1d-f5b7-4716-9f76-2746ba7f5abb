import { render } from '@testing-library/react';
import { OutOfStockCarouselSkelton } from '../OutOfStockCarouselSkelton';
import { useBreakpoint } from '../../../hooks/useBreakPoint';

jest.mock('../../../hooks/useBreakPoint');

describe('OutOfStockCarouselSkelton', () => {
  it('renders correctly for desktop devices', () => {
    (useBreakpoint as jest.Mock).mockReturnValue({
      isMobile: false,
      isMediumDevice: false,
      isLargeDevice: true,
      isExtraLargeDevice: false,
    });

    const { container } = render(<OutOfStockCarouselSkelton />);
    expect(container.querySelectorAll('div.pb-2.pr-4')).toHaveLength(4);
    expect(container.querySelectorAll('div.pb-1')).toHaveLength(8);
  });
});
