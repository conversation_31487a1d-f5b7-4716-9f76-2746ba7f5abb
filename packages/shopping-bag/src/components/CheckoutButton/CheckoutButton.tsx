'use client';
import React from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Button } from '@ecom-next/core/migration/button';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import classNames from 'classnames';
import { useDatalayer } from '../../hooks/useDataLayer';

export const CheckoutButton = ({ fromStickyCheckout }: { fromStickyCheckout?: boolean }) => {
  const { publishDataLayerEvent } = useDatalayer();
  const { localize } = useLocalize();
  const pageContext = usePageContext();
  const { abSeg } = pageContext;

  const buttonColorChangeSegmentEnabled = abSeg?.['xb217'] === 'a';

  const goToCheckout = () => {
    publishDataLayerEvent({ name: fromStickyCheckout ? 'sticky_checkout_click' : 'checkout' });
    const host = window.location.hostname;
    const EASY_SIGN_IN_PATH = `/my-account/sign-in?targetURL=https://${host}/checkout`;
    window.location.href = `https://${host}${EASY_SIGN_IN_PATH}`;
  };

  return (
    <Button
      kind='primary-critical'
      onClick={goToCheckout}
      fullWidth={true}
      data-testid='checkout-button'
      className={classNames('flex w-full items-center justify-center text-center', {
        '!bg-[#f23f1c] hover:!bg-[#f23f1c] hover:!opacity-75': buttonColorChangeSegmentEnabled,
      })}
    >
      {localize('charges.checkout')}
    </Button>
  );
};
