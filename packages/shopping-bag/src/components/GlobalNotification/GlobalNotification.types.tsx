import { OutOfStockProduct } from '../../types/xapi-response-types';

export type Kind = 'error' | 'warning' | 'success' | 'information';

export interface OutOfStockMessageProps {
  items: Array<OutOfStockProduct>;
  message: string;
}

export interface SingleOutOfStockMessageProps {
  items: Array<OutOfStockProduct>;
}

export interface MultiOutOfStockMessageProps {
  items: Array<OutOfStockProduct>;
  locale: string;
}

export interface UserNotificationProps {
  isDismissible: boolean;
  isStatic?: boolean;
  items: Array<OutOfStockProduct>;
  message: string;
  onDismiss: () => void;
  type: Kind;
}
