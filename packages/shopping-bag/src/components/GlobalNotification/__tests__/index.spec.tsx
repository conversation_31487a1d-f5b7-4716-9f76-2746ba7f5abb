import React from 'react';
import { render, screen } from '@testing-library/react';
import { FeatureFlagsContext } from '@ecom-next/core/legacy/feature-flags';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import GlobalNotification from '../index';
import { useBagContext } from '../../../contexts/ShoppingBagProvider';
import { useActiveBagContext } from '../../../contexts/ActiveBagProvider';
import useGlobalNotificationsStore from '../useGlobalNotificationsStore';

jest.mock('@ecom-next/sitewide/localization-provider', () => ({
  useLocalize: jest.fn(),
}));

jest.mock('@ecom-next/sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn(),
}));

jest.mock('../../../contexts/ShoppingBagProvider', () => ({
  useBagContext: jest.fn(),
}));

jest.mock('../../../contexts/ActiveBagProvider', () => ({
  useActiveBagContext: jest.fn(),
}));

jest.mock('../useGlobalNotificationsStore', () => jest.fn());

describe('GlobalNotification', () => {
  const mockLocalize = jest.fn();
  const mockSetNotification = jest.fn();
  const mockRegister = jest.fn();

  beforeEach(() => {
    (useLocalize as jest.Mock).mockReturnValue({ localize: mockLocalize });
    (usePageContext as jest.Mock).mockReturnValue({ brandName: 'TestBrand' });
    (useBagContext as jest.Mock).mockReturnValue({
      notificationObj: { message: 'Test Notification' },
      setNotification: mockSetNotification,
    });
    (useActiveBagContext as jest.Mock).mockReturnValue({
      bagState: { data: { globalNotifications: [], productList: [], promos: { appliedPromotions: [] }, outOfStockItems: [] } },
      outofStockCarouselState: { setIsOOSActive: jest.fn(), isOOSActive: true },
    });
    (useGlobalNotificationsStore as jest.Mock).mockReturnValue({ register: mockRegister });
  });

  it('renders without crashing', () => {
    render(
      <FeatureFlagsContext.Provider value={{ enabledFeatures: { 'bag-ui-popular-items-banner': true } }}>
        <GlobalNotification />
      </FeatureFlagsContext.Provider>
    );
    expect(screen.getByTestId('global_notifications')).toBeInTheDocument();
  });

  it('should show the popular items notification message', () => {
    mockLocalize.mockReturnValue('Some of our most popular items can only be saved in your bag for 30 minutes');
    render(
      <FeatureFlagsContext.Provider value={{ enabledFeatures: { 'bag-ui-popular-items-banner': true } }}>
        <GlobalNotification />
      </FeatureFlagsContext.Provider>
    );
    expect(screen.getByText('Some of our most popular items can only be saved in your bag for 30 minutes')).toBeInTheDocument();
  });

  it('handles out of stock notifications correctly', () => {
    (useActiveBagContext as jest.Mock).mockReturnValue({
      bagState: {
        data: {
          globalNotifications: ['OUT_OF_STOCK'],
          outOfStockItems: [{ productLink: '/browse/product.do?pid=7953460120002', productName: 'Organic Cotton Vintage T-Shirt' }],
        },
      },
      outofStockCarouselState: { setIsOOSActive: jest.fn(), isOOSActive: true },
    });
    render(
      <FeatureFlagsContext.Provider value={{ enabledFeatures: { 'bag-ui-oos': true } }}>
        <GlobalNotification />
      </FeatureFlagsContext.Provider>
    );
    expect(screen.getByText('Organic Cotton Vintage T-Shirt')).toBeInTheDocument();
  });

  it('handles multi out of stock notifications correctly', () => {
    const outOfStockItems = [
      { productLink: '/browse/product.do?pid=7953460120002', productName: 'Organic Cotton Vintage T-Shirt' },
      { productLink: '/browse/product.do?pid=7946000023002', productName: 'High Rise Kick Fit Jeans' },
    ];

    (useActiveBagContext as jest.Mock).mockReturnValue({
      bagState: {
        data: {
          globalNotifications: ['OUT_OF_STOCK'],
          outOfStockItems: outOfStockItems,
        },
      },
      outofStockCarouselState: { setIsOOSActive: jest.fn(), isOOSActive: true },
    });
    render(
      <FeatureFlagsContext.Provider value={{ enabledFeatures: { 'bag-ui-multi-oos': true } }}>
        <GlobalNotification data-testid='global_notifications' />
      </FeatureFlagsContext.Provider>
    );
    expect(screen.getByTestId('global_notifications')).toHaveTextContent(`${outOfStockItems.length}`);
    expect(screen.queryByText('Organic Cotton Vintage T-Shirt')).not.toBeInTheDocument();
    expect(screen.queryByText('High Rise Kick Fit Jeans')).not.toBeInTheDocument();
  });
});
