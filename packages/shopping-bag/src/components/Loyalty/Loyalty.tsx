import { useContext, useEffect } from 'react';
import classNames from 'classnames';
import { FeatureFlagsContext } from '@ecom-next/core/legacy/feature-flags';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Rewards, type RewardsProps } from '../../modules/Loyalty/Rewards';
import { useBreakpoint } from '../../hooks/useBreakPoint';
import { Panel } from '../Panel/Panel';
import { useLoyalty } from './useLoyalty';
import { getLoyaltyDetails } from './apiActions';

export const Loyalty = () => {
  const { enabledFeatures } = useContext(FeatureFlagsContext);
  const { localize } = useLocalize();
  const { isMobile } = useBreakpoint();
  const { data, loyaltyDetails, headers, setLoyaltyDetails, handleApplyRewards, handleRemoveRewards, hasError } = useLoyalty();

  const isLoyaltyFlagEnabled = enabledFeatures['bag-ui-loyalty'];
  const isAuthenticated = data?.bagAttributes.userStatus === 'AUTHENTICATED';
  const isAnonymous = data?.bagAttributes.userStatus === 'ANONYMOUS';
  const minPoints = loyaltyDetails?.details?.minPoints || 100;
  const appliedAmount = data?.loyalty.appliedAmount || 0;

  useEffect(() => {
    if (!isAuthenticated) return;

    (async () => {
      const loyaltyData = await getLoyaltyDetails(headers);
      setLoyaltyDetails(loyaltyData);
    })();
  }, [data?.loyalty.rewardsEligibleTotal]);

  if (!data || isAnonymous || !isLoyaltyFlagEnabled) return null;

  return (
    <div className={classNames({ 'mb-2': isMobile }, { 'mb-4': !isMobile })} id='loyalty'>
      <Panel>
        <Panel.Title>{localize('loyalty.pointsRewards')}</Panel.Title>
        <Panel.Body>
          <Rewards
            applyRewards={({ amount, points }) => handleApplyRewards({ amount, points })}
            removeRewards={handleRemoveRewards}
            details={loyaltyDetails?.details as RewardsProps['details']}
            hasAppliedRewards={data?.loyalty.hasAppliedRewards}
            appliedPoints={appliedAmount * minPoints}
            appliedAmount={appliedAmount}
            isAuthenticated={isAuthenticated}
            isMTLMember={loyaltyDetails?.enrollmentStatus === 'ENROLLED'}
            hasError={hasError}
            merchandiseSubTotal={data.loyalty.rewardsEligibleTotal}
          />
        </Panel.Body>
      </Panel>
    </div>
  );
};
