import React from 'react';
import { BagState, FetchError } from '@shopping-bag/types/bag-types';
import { ShoppingBagResponse } from '@shopping-bag/types/xapi-response-types';
import { ActiveBagProvider } from '../../../contexts/ActiveBagProvider';
import { fireEvent, render, waitFor } from '../../../utils/test-utils';
import { useDatalayer } from '../../../hooks/useDataLayer';
import { Promos } from '../Promos';

jest.mock('../../../hooks/useDataLayer');

const mockPublishDataLayerEvent = jest.fn();

const appliedPromotions = {
  appliedPromotions: [
    {
      code: 'BAGUIUS003',
      displayName: 'BAGUIUS003',
      promoId: '183758',
      isAutoApply: false,
      promoDescription: '$3 off order',
      viewPromoDetails: null,
      plcc: false,
      flags: [
        {
          message: 'BAGPNP-501520',
          type: 'informational',
        },
      ],
    },
  ],
};

const asyncDispatchSpy = jest.fn().mockImplementation(async () => void 0);

afterEach(() => {
  jest.clearAllMocks();
});

describe('Promo Rewards component', () => {
  it('renders correctly', () => {
    const mockBagState = {
      bagAttributes: {
        userStatus: 'AUTHENTICATED',
      },
      actionPayload: { isSuccess: true, action: 'applyShoppingBagPromoAction' },
      promos: { ...appliedPromotions },
    };

    const bagState: BagState = {
      loading: 'SUCCESS',
      data: mockBagState as unknown as ShoppingBagResponse,
      error: false as unknown as FetchError,
    };

    const { getByText } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={asyncDispatchSpy}>
        <Promos />
      </ActiveBagProvider>
    );
    const enterPromoCodeText = getByText('Enter Promo Code');
    const applyText = getByText('Apply');
    expect(enterPromoCodeText).toBeInTheDocument();
    expect(applyText).toBeInTheDocument();
  });
});

describe('Employee Promotions', () => {
  it('renders Employee Promo details correctly', async () => {
    const mockBagState = {
      bagAttributes: {
        userStatus: 'AUTHENTICATED',
      },
      actionPayload: { isSuccess: true, action: 'applyShoppingBagPromoAction' },
      currencySymbol: '$',
      promos: {
        appliedPromotions: [
          {
            code: 'EMPLOYEEDISCOUNT',
            displayName: 'Employee Discount',
            discountTotal: 27.5,
            promoId: '183758',
            isAutoApply: false,
            promoDescription: 'Employee Discount test for online',
            viewPromoDetails: null,
            plcc: false,
            flags: [],
          },
        ],
      },
    };

    const bagState: BagState = {
      loading: 'SUCCESS',
      data: mockBagState as unknown as ShoppingBagResponse,
      error: false as unknown as FetchError,
    };

    const { container, getByText, getByTestId } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={asyncDispatchSpy}>
        <Promos />
      </ActiveBagProvider>
    );
    expect(getByText('Employee Discount')).toBeInTheDocument();
    expect(getByText('$27.50')).toBeInTheDocument();
    expect(getByText('Employee Discount test for online')).toBeInTheDocument();

    const removeButton = container.querySelector('svg') as Element;
    expect(removeButton).toBeInTheDocument();
    fireEvent.click(removeButton);

    expect(getByText('Are you sure?')).toBeInTheDocument();
    expect(getByText("If you remove your employee discount promo, you'll need to sign out and sign back in for it to reapply.")).toBeInTheDocument();
    expect(getByText('Cancel')).toBeInTheDocument();
    expect(getByText('Remove Promo')).toBeInTheDocument();
    expect(getByTestId('remove-promo')).toBeInTheDocument();

    fireEvent.click(getByText('Remove Promo'));

    await waitFor(() => {
      expect(asyncDispatchSpy).toHaveBeenCalledTimes(1);
    });
  });
});

describe('render sign in links for loyalty tier promos', () => {
  it('should display recognized tier exclusive promo error', () => {
    const mockBagState = {
      bagAttributes: {
        userStatus: 'RECOGNIZED',
      },
      actionPayload: { isSuccess: true, action: 'applyShoppingBagPromoAction' },
      promos: { ...appliedPromotions },
    };

    const bagState: BagState = {
      loading: 'SUCCESS',
      data: mockBagState as unknown as ShoppingBagResponse,
      error: false as unknown as FetchError,
    };

    const { getByText } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={asyncDispatchSpy}>
        <Promos />
      </ActiveBagProvider>
    );
    const recognizedPromoError1 = getByText('Please', { exact: false });
    const recognizedPromoError2 = getByText('sign in', { exact: false });
    const recognizedPromoError3 = getByText('apply Rewards Program Savings.', { exact: false });
    expect(recognizedPromoError1).toBeInTheDocument();
    expect(recognizedPromoError2).toBeInTheDocument();
    expect(recognizedPromoError3).toBeInTheDocument();
  });

  it('should display anonymous/guest tier exclusive promo error', () => {
    const mockBagState = {
      bagAttributes: {
        userStatus: 'ANONYMOUS',
      },
      actionPayload: { isSuccess: true, action: 'applyShoppingBagPromoAction' },
      promos: { ...appliedPromotions },
    };

    const bagState: BagState = {
      loading: 'SUCCESS',
      data: mockBagState as unknown as ShoppingBagResponse,
      error: false as unknown as FetchError,
    };

    const { getByText } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={asyncDispatchSpy}>
        <Promos />
      </ActiveBagProvider>
    );
    const recognizedPromoError1 = getByText('Please', { exact: false });
    const recognizedPromoError2 = getByText('sign in', { exact: false });
    const recognizedPromoError3 = getByText('join', { exact: false });
    const recognizedPromoError4 = getByText('Rewards Program to apply savings.', { exact: false });
    expect(recognizedPromoError1).toBeInTheDocument();
    expect(recognizedPromoError2).toBeInTheDocument();
    expect(recognizedPromoError3).toBeInTheDocument();
    expect(recognizedPromoError4).toBeInTheDocument();
  });
});

describe('Datalayer Event', () => {
  (useDatalayer as jest.Mock).mockReturnValue({
    publishDataLayerEvent: mockPublishDataLayerEvent,
  });
  describe('Apply Promo', () => {
    const mockBagState = {
      bagAttributes: {
        userStatus: 'AUTHENTICATED',
      },
      actionPayload: { isSuccess: true, action: 'applyShoppingBagPromoAction' },
      promos: { ...appliedPromotions },
    };

    const bagState: BagState = {
      loading: 'SUCCESS',
      data: mockBagState as unknown as ShoppingBagResponse,
      error: false as unknown as FetchError,
    };

    it('should trigger attemp and failure tealium event on failure', async () => {
      const mockfailedPromoBagState = {
        bagAttributes: {
          userStatus: 'AUTHENTICATED',
        },
        actionPayload: { isSuccess: false, action: 'applyShoppingBagPromoAction' },
        promos: { ...appliedPromotions, notification: 'BAGPNP-5003' },
      };

      const failedPromoBagState: BagState = {
        loading: 'SUCCESS',
        data: mockfailedPromoBagState as unknown as ShoppingBagResponse,
        error: false as unknown as FetchError,
      };

      const { getByLabelText, getByRole } = render(
        <ActiveBagProvider bagState={failedPromoBagState} asyncDispatch={asyncDispatchSpy}>
          <Promos />
        </ActiveBagProvider>
      );

      const promoCode = getByLabelText('Enter Promo Code') as HTMLInputElement;

      fireEvent.change(promoCode, { target: { value: 'AUTOTESTUS' } });
      const applyButton = getByRole('button', {
        name: /Apply/i,
      });
      fireEvent.click(applyButton);
      expect(mockPublishDataLayerEvent).toHaveBeenCalledWith({ name: 'promo-apply-attempt' });

      await waitFor(() => {
        expect(asyncDispatchSpy).toHaveBeenCalledTimes(1);
      });

      expect(mockPublishDataLayerEvent).toHaveBeenCalledWith({ name: 'promo-apply-failure' });
    });
    it('should trigger attemp and success tealium event on success', async () => {
      const { getByLabelText, getByRole } = render(
        <ActiveBagProvider bagState={bagState} asyncDispatch={asyncDispatchSpy}>
          <Promos />
        </ActiveBagProvider>
      );

      const promoCode = getByLabelText('Enter Promo Code') as HTMLInputElement;

      fireEvent.change(promoCode, { target: { value: 'AUTOTESTUS' } });
      const applyButton = getByRole('button', {
        name: /Apply/i,
      });
      fireEvent.click(applyButton);
      expect(mockPublishDataLayerEvent).toHaveBeenCalledWith({ name: 'promo-apply-attempt' });

      await waitFor(() => {
        expect(asyncDispatchSpy).toHaveBeenCalledTimes(1);
      });

      expect(mockPublishDataLayerEvent).toHaveBeenCalledWith({ name: 'promo-apply-success' });
    });
  });

  describe('Remove Promo', () => {
    it('should not trigger publishDataLayerEvent on remove promo', async () => {
      const mockBagState = {
        bagAttributes: {
          userStatus: 'AUTHENTICATED',
        },
        actionPayload: { isSuccess: true, action: 'getShoppingBagPageAction' },
        promos: { ...appliedPromotions },
      };

      const bagState: BagState = {
        loading: 'SUCCESS',
        data: mockBagState as unknown as ShoppingBagResponse,
        error: false as unknown as FetchError,
      };

      render(
        <ActiveBagProvider bagState={bagState} asyncDispatch={asyncDispatchSpy}>
          <Promos />
        </ActiveBagProvider>
      );

      expect(mockPublishDataLayerEvent).not.toHaveBeenCalled();
    });
  });
});
