'use client';
import React, { useContext, useEffect, useState } from 'react';
import { useInView } from 'react-intersection-observer';
import classNames from 'classnames';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { FeatureFlagsContext } from '@ecom-next/core/legacy/feature-flags';
import { useBreakpoint } from '../../hooks/useBreakPoint';
import { ApplePayButton } from '../ApplePay/ApplePayButton';
import { PayPal } from '../../modules/PayPal/PayPal';
import { shouldShowApplePay } from '../ApplePay/shouldShowApplePay';
import { useActiveBagContext } from '../../contexts/ActiveBagProvider';
import { getHeaders } from '../../utils';
import { getStoreInfo } from '../ActiveBag/datalayer/getStoreInfo';
import { StoreAddressInfo } from '../../types/bag-types';
import { CheckoutButton } from '../CheckoutButton/CheckoutButton';
import { StickyCheckout } from './StickyCheckout/StickyCheckout';

export const PaymentOptions = () => {
  const pageContext = usePageContext();
  const { market } = pageContext;
  const headers = getHeaders(pageContext);
  const { enabledFeatures } = useContext(FeatureFlagsContext);
  const { isMobile, isMediumDevice } = useBreakpoint();
  const isApplePayFlagEnabled = enabledFeatures['bag-ui-applepay'];
  const isPayPalFlagEnabled = enabledFeatures['bag-ui-paypal'];
  const { ref, inView } = useInView({
    triggerOnce: false,
    threshold: 0.01,
  });
  const [storeInfo, setStoreInfo] = useState<StoreAddressInfo>();

  const {
    bagState: { data },
  } = useActiveBagContext();

  useEffect(() => {
    (async () => {
      if (data?.bopisStoreId) {
        const response = await getStoreInfo({ bopisStoreId: data?.bopisStoreId, headers });
        setStoreInfo(response);
      }
    })();
  }, [data?.bopisStoreId]);

  if (!data?.productList.length) return null;

  const isPayPalEligible = data.bagAttributes?.flags?.payPalEligible;

  const isGapBagType = data.bagAttributes?.bagType === 'GAP';

  const {
    hasBopisItems,
    productList,
    summaryOfCharges: { currencyCode, myTotal },
  } = data;

  const isApplePayEnabled = shouldShowApplePay(isApplePayFlagEnabled, hasBopisItems, isGapBagType, productList.length > 0);
  const isStickyFooter = (isMobile || isMediumDevice) && !inView;
  const isStickyAndApplePayEnabled = isStickyFooter && isApplePayEnabled;
  const isStickyWithoutApplePay = isStickyFooter && !isApplePayEnabled;
  const isStickyWithoutPayPal = isStickyFooter && (!isPayPalEligible || !isPayPalFlagEnabled);

  return (
    <div ref={ref}>
      <section
        style={{ boxShadow: isStickyFooter ? '0px -2px 4px 0px rgba(0, 0, 0, 0.10)' : 'none' }}
        className={classNames('flex flex-col', { 'bg-cb-coreColor-white fixed bottom-0 left-0 z-[500] w-full p-4': isStickyFooter })}
      >
        {isStickyFooter ? <StickyCheckout /> : null}
        <div
          className={classNames('flex', {
            'flex-col': !isStickyFooter,
            'flex-row': isStickyWithoutApplePay,
            'flex-wrap': isStickyAndApplePayEnabled,
          })}
        >
          <div
            className={classNames(
              { 'w-full': isStickyAndApplePayEnabled || !isStickyFooter || isStickyWithoutPayPal },
              { 'mr-2 w-1/2': isStickyWithoutApplePay && !isStickyWithoutPayPal }
            )}
          >
            <CheckoutButton fromStickyCheckout={!!isStickyFooter} />
          </div>
          {isPayPalFlagEnabled && isPayPalEligible && (
            <div
              className={classNames({
                'mt-2 w-1/2 min-w-0 flex-shrink flex-grow-0': isStickyAndApplePayEnabled,
                'w-1/2': isStickyWithoutApplePay,
                'mt-2': !isStickyFooter,
              })}
            >
              <PayPal fromStickyCheckout={!!isStickyFooter} storeInfo={storeInfo} />
            </div>
          )}
          {isApplePayEnabled && (
            <div
              className={classNames('pt-2', {
                'w-1/2 min-w-0 flex-shrink flex-grow-0 pl-2': isStickyAndApplePayEnabled && isPayPalFlagEnabled,
                'w-full min-w-0 flex-shrink flex-grow-0': isStickyWithoutPayPal,
              })}
            >
              <ApplePayButton total={Number(myTotal)} currencyCode={currencyCode} market={market} fromStickyCheckout={!!isStickyAndApplePayEnabled} />
            </div>
          )}
        </div>
      </section>
    </div>
  );
};
