'use client';
import React, { useContext } from 'react';
import { Meter } from '@ecom-next/core/migration/meter';
import { FeatureFlagsContext } from '@ecom-next/core/legacy/feature-flags';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useActiveBagContext } from '../../contexts/ActiveBagProvider';
import { Price, signInHyperLink } from '../../utils';
import { useDatalayer } from '../../hooks/useDataLayer';
export const ShippingProgress = () => {
  const { enabledFeatures } = useContext(FeatureFlagsContext);
  const { market } = usePageContext();
  const { localize } = useLocalize();
  const {
    bagState: { data },
  } = useActiveBagContext();

  if (!data?.productList.length) return undefined;

  const {
    bagAttributes: { userStatus },
    currencySymbol,
    loyalty: { tier },
    shipping: { isQualifiedForFreeShipping, shippingThreshold },
    summaryOfCharges: { subTotal },
  } = data;

  const generateProgress = () => (isQualifiedForFreeShipping ? 100 : (+subTotal / shippingThreshold) * 100);
  const subTotalPrice = <Price amount={subTotal} currency={currencySymbol} />;
  const amountLeftForFreeShipping = <Price amount={shippingThreshold - Number(subTotal)} currency={currencySymbol} />;
  const isLoyaltyCanada = enabledFeatures['bag-ui-loyalty'] && market === 'ca';

  // Message below the meter for users not signed in
  const SignInOrJoinNow = () => {
    const { publishDataLayerEvent } = useDatalayer();
    const signInDataLayerPayload = {
      name: 'easy_enroll_click',
      extraAttrs: {
        easy_enroll_type: ': orderSummary : Sign In',
      },
    };
    const jonNowDataLayerPayload = {
      name: 'easy_enroll_click',
      extraAttrs: {
        easy_enroll_type: ': orderSummary : Join Now',
      },
    };
    return (
      <>
        <span data-testid='order-summary-sign-in'>{signInHyperLink(localize('charges.signIn'), signInDataLayerPayload, publishDataLayerEvent)}</span>
        {` ${localize('or')} `}
        <span data-testid='order-summary-join-now'>{signInHyperLink(localize('charges.joinNow'), jonNowDataLayerPayload, publishDataLayerEvent)}</span>.
      </>
    );
  };

  // Message above the meter for users not signed in
  const AnonymousTopInfo = () =>
    isQualifiedForFreeShipping ? (
      <>{localize('charges.notLoggedInFree', { shippingThreshold })}</>
    ) : (
      <>
        {localize('charges.notLoggedIn')} {subTotalPrice}
      </>
    );

  // Message above the meter for signed in users
  const LoggedInTopInfo = () =>
    isQualifiedForFreeShipping ? (
      <>{localize('charges.freeShippingQualified')}</>
    ) : (
      <>
        {localize('charges.signedInNotQualifiedA')}
        {amountLeftForFreeShipping}
        {localize('charges.signedInNotQualifiedB')}
      </>
    );

  // Message below the meter for signed in users
  const LoggedInBottomInfo = () => (isQualifiedForFreeShipping ? null : <>{localize('charges.getFreeShipping', { shippingThreshold })}</>);

  const TopInfo = () => {
    if (!shippingThreshold) return undefined;

    return <p>{userStatus === 'AUTHENTICATED' ? <LoggedInTopInfo /> : <AnonymousTopInfo />}</p>;
  };

  const BottomInfo = () => {
    if (!shippingThreshold) return undefined;

    return (
      <p>
        {userStatus === 'AUTHENTICATED' ? (
          <LoggedInBottomInfo />
        ) : (
          <>
            {localize('charges.getFreeShippingAnon', { shippingThreshold })}
            {'. '}
            <SignInOrJoinNow />
          </>
        )}
      </p>
    );
  };

  const ShippingProgressBar = () => {
    if (!shippingThreshold) return undefined;

    return <Meter percentage={generateProgress()} />;
  };

  const NotLoggedInMessageCanada = () => (
    <>
      {userStatus === 'RECOGNIZED' ? localize('charges.forFreeShipping', { shippingThreshold }) : localize('charges.forFreeShippingAnon')}
      <span className='lowercase'>
        {' '}
        <SignInOrJoinNow />
      </span>
    </>
  );

  const FreeShipMessageCanada = () => {
    if (!shippingThreshold) return undefined;

    return <>{userStatus === 'AUTHENTICATED' ? localize('charges.freeShippingAuthenticated', { shippingThreshold }) : <NotLoggedInMessageCanada />}</>;
  };

  if (isLoyaltyCanada) {
    // Below is the return for CA market with bag-ui-loyalty turned on
    return (
      <p className='cb-special-sm pb-4 text-left'>
        {tier !== 'NON_LOYALTY' && <>{localize(`charges.${tier.toLowerCase()}MemberShipping`)} </>}
        <FreeShipMessageCanada />
      </p>
    );
  }

  if (market === 'ca' && !enabledFeatures['bag-ui-loyalty']) return undefined;

  // Below is the default return for US market
  return (
    <div className='cb-special-sm pb-4 text-center'>
      <TopInfo />
      <ShippingProgressBar />
      <BottomInfo />
    </div>
  );
};
