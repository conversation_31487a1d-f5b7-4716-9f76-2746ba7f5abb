import React from 'react';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { ActiveBagProvider } from '@ecom-next/shopping-bag/contexts/ActiveBagProvider';
import { BagState, FetchError } from '../../../types/bag-types';
import { ShoppingBagResponse } from '../../../types/xapi-response-types';
import { render } from '../../../utils/test-utils';
import { Disclaimer } from '../Disclaimer';

jest.mock('@ecom-next/sitewide/hooks/usePageContext');
const mockData = {
  productList: [],
  bagAttributes: {
    flags: {
      payPalEligible: true,
      isAfterPayEligible: true,
      isKlarnaEligible: true,
    },
  },
};

const bagState: BagState = {
  loading: 'SUCCESS',
  data: mockData as unknown as ShoppingBagResponse,
  error: false as unknown as FetchError,
};

const defaultPageContext = {
  brand: 'gap',
  market: 'us',
  abSeg: {},
};

const enabledFeatures = { 'bag-ui-paypal': true, 'bag-ui-afterpay': true, 'buy-ui-klarna': true };

describe('Disclaimer Component - US market', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders disclaimer for gap brand', () => {
    const pageContext = {
      brand: 'gap',
      market: 'us',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures }
    );

    expect(getByText('PayPal, Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', '/customerService/info.do?cid=81265&cs=payment_options');
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer for old navy brand', () => {
    const pageContext = {
      brand: 'on',
      market: 'us',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures }
    );

    expect(getByText('PayPal, Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', '/customerService/info.do?cid=82725&cs=payment_options');
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer for banana repulbic brand', () => {
    const pageContext = {
      brand: 'br',
      market: 'us',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures }
    );

    expect(getByText('PayPal, Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', '/customerService/info.do?cid=80740&cs=payment_options');
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer for athleta brand', () => {
    const pageContext = {
      brand: 'at',
      market: 'us',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures, abSeg: { xb224: 'a' } }
    );

    expect(getByText('PayPal, Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', '/customerService/info.do?cid=79312&cs=payment_options');
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer for gap factory brand', () => {
    const pageContext = {
      brand: 'gapfs',
      market: 'us',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures, abSeg: { xb224: 'a' } }
    );

    expect(getByText('PayPal, Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', '/customerService/info.do?cid=1037495&cs=payment_options');
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer for athleta brand - brfs', () => {
    const pageContext = {
      brand: 'brfs',
      market: 'us',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures, abSeg: { xb224: 'a' } }
    );

    expect(getByText('PayPal, Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', '/customerService/info.do?cid=1037875&cs=payment_options');
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });
});

describe('Disclaimer Component - CA market', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders disclaimer for gap brand', () => {
    const pageContext = {
      brand: 'gap',
      market: 'ca',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures }
    );

    expect(getByText('PayPal, Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', 'https://www.gapcanada.ca/customerService/info.do?cid=40958&mlink=2136%2C27267706%2C9&clink=27267706');
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer for old navy brand', () => {
    const pageContext = {
      brand: 'on',
      market: 'ca',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures }
    );

    expect(getByText('PayPal, Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', 'https://oldnavy.gapcanada.ca/customerService/info.do?cid=3316');
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer for banana repulbic brand', () => {
    const pageContext = {
      brand: 'br',
      market: 'ca',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures }
    );

    expect(getByText('PayPal, Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', 'https://bananarepublic.gapcanada.ca/customerService/info.do?cid=1316');
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer for athleta brand', () => {
    const pageContext = {
      brand: 'at',
      market: 'ca',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures }
    );

    expect(getByText('PayPal, Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', 'https://athleta.gapcanada.ca/customerService/info.do?cid=1180465');
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });
});

describe('Disclaimer Component - Eligible Payments', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders disclaimer without PayPal text when bag-ui-paypal is off', () => {
    const pageContext = {
      brand: 'gap',
      market: 'ca',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole, queryByText } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures: { 'bag-ui-paypal': false, 'bag-ui-afterpay': true, 'buy-ui-klarna': true } }
    );

    expect(queryByText('PayPal')).not.toBeInTheDocument();
    expect(getByText('Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', 'https://www.gapcanada.ca/customerService/info.do?cid=40958&mlink=2136%2C27267706%2C9&clink=27267706');
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer with Apple Pay text when bag-ui-applepay is on', () => {
    const pageContext = {
      brand: 'gap',
      market: 'ca',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={true} />
      </ActiveBagProvider>,
      { enabledFeatures: { 'bag-ui-applepay': true } }
    );

    expect(getByText('Apple Pay', { exact: false })).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', 'https://www.gapcanada.ca/customerService/info.do?cid=40958&mlink=2136%2C27267706%2C9&clink=27267706');
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer without Apple Pay text when bag-ui-applepay is off', () => {
    const pageContext = {
      brand: 'gap',
      market: 'ca',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, getByRole, queryByText } = render(
      <ActiveBagProvider bagState={bagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures: { 'bag-ui-applepay': false, 'bag-ui-paypal': true, 'bag-ui-afterpay': true, 'buy-ui-klarna': true } }
    );

    expect(queryByText('Apple Pay')).not.toBeInTheDocument();
    expect(getByText('PayPal, Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', 'https://www.gapcanada.ca/customerService/info.do?cid=40958&mlink=2136%2C27267706%2C9&clink=27267706');
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer without Afterpay text when bag-ui-afterpay is off', () => {
    const pageContext = {
      brand: 'gap',
      market: 'ca',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const mockBagState = {
      ...bagState,
      data: {
        ...bagState.data,
        bagAttributes: {
          flags: {
            payPalEligible: true,
            isAfterPayEligible: false,
            isKlarnaEligible: true,
          },
        },
      },
    } as unknown as BagState;

    const { getByText, getByRole, queryByText } = render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures: { 'bag-ui-paypal': true, 'bag-ui-afterpay': false, 'buy-ui-klarna': true } }
    );

    expect(queryByText('Afterpay')).not.toBeInTheDocument();
    expect(getByText('PayPal & Klarna:')).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', 'https://www.gapcanada.ca/customerService/info.do?cid=40958&mlink=2136%2C27267706%2C9&clink=27267706');
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer without Klarna text when buy-ui-klarna is off', () => {
    const pageContext = {
      brand: 'gap',
      market: 'ca',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });
    const mockBagState = {
      ...bagState,
      data: {
        ...bagState.data,
        bagAttributes: {
          flags: {
            payPalEligible: true,
            isAfterPayEligible: true,
            isKlarnaEligible: false,
          },
        },
      },
    } as unknown as BagState;

    const { getByText, getByRole, queryByText } = render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures: { 'bag-ui-paypal': true, 'bag-ui-afterpay': true, 'buy-ui-klarna': false } }
    );

    expect(queryByText('Klarna')).not.toBeInTheDocument();
    expect(getByText('PayPal & Afterpay:')).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', 'https://www.gapcanada.ca/customerService/info.do?cid=40958&mlink=2136%2C27267706%2C9&clink=27267706');
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('does not render disclaimer when no payment methods are enabled', () => {
    const pageContext = {
      brand: 'gap',
      market: 'us',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const mockBagState = {
      ...bagState,
      data: {
        ...bagState.data,
        bagAttributes: {
          flags: {
            payPalEligible: false,
            isAfterPayEligible: false,
            isKlarnaEligible: false,
          },
        },
      },
    } as unknown as BagState;

    const { queryByText } = render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures: { 'bag-ui-paypal': false, 'bag-ui-afterpay': false, 'buy-ui-klarna': false, 'bag-ui-applepay': false } }
    );

    expect(queryByText('PayPal')).not.toBeInTheDocument();
    expect(queryByText('Afterpay')).not.toBeInTheDocument();
    expect(queryByText('Klarna')).not.toBeInTheDocument();
    expect(queryByText('Apple Pay')).not.toBeInTheDocument();
    expect(queryByText('Limits Apply')).not.toBeInTheDocument();
    expect(queryByText('to Gap Inc. Credit Card Rewards')).not.toBeInTheDocument();
  });

  it('renders disclaimer with Afterpay and Klarna for dropship items but not PayPal', () => {
    const pageContext = {
      brand: 'gap',
      market: 'us',
      abSeg: { xb224: 'a' },
    };
    const mockBagState = {
      ...bagState,
      data: {
        ...bagState.data,
        productList: [{ productType: 'Dropship' }],
        bagAttributes: {
          flags: {
            payPalEligible: false,
            isAfterPayEligible: true,
            isKlarnaEligible: true,
          },
        },
      },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, queryByText, getByRole } = render(
      <ActiveBagProvider bagState={mockBagState as BagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures: { 'bag-ui-paypal': true, 'bag-ui-afterpay': true, 'buy-ui-klarna': true } }
    );

    expect(queryByText('PayPal')).not.toBeInTheDocument();
    expect(getByText('Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', '/customerService/info.do?cid=81265&cs=payment_options');
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer with only PayPal when a gift card is in the bag', () => {
    const pageContext = {
      brand: 'gap',
      market: 'us',
      abSeg: { xb224: 'a' },
    };
    const mockBagState = {
      ...bagState,
      data: {
        ...bagState.data,
        productList: [{ productType: 'STORED VALUE CARDS FIXED' }],
        bagAttributes: {
          flags: {
            payPalEligible: true,
            isAfterPayEligible: false,
            isKlarnaEligible: false,
          },
        },
      },
    };
    (usePageContext as jest.Mock).mockReturnValue({ ...defaultPageContext, ...pageContext });

    const { getByText, queryByText, getByRole } = render(
      <ActiveBagProvider bagState={mockBagState as BagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={false} />
      </ActiveBagProvider>,
      { enabledFeatures: { 'bag-ui-paypal': true, 'bag-ui-afterpay': true, 'buy-ui-klarna': true } }
    );

    expect(getByText('PayPal:')).toBeInTheDocument();
    expect(queryByText('Afterpay')).not.toBeInTheDocument();
    expect(queryByText('Klarna')).not.toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', '/customerService/info.do?cid=81265&cs=payment_options');
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });

  it('renders disclaimer with multiple payment methods in the correct format', () => {
    const pageContext = {
      brand: 'gap',
      market: 'us',
      abSeg: { xb224: 'a' },
    };
    (usePageContext as jest.Mock).mockReturnValueOnce(pageContext);

    const mockBagState = {
      ...bagState,
      data: {
        ...bagState.data,
        bagAttributes: {
          flags: {
            payPalEligible: true,
            isAfterPayEligible: true,
            isKlarnaEligible: true,
          },
        },
      },
    } as unknown as BagState;

    const { getByText, getByRole } = render(
      <ActiveBagProvider bagState={mockBagState} asyncDispatch={async () => void 0}>
        <Disclaimer isApplePayEnabled={true} />
      </ActiveBagProvider>,
      { enabledFeatures: { 'bag-ui-paypal': true, 'bag-ui-afterpay': true, 'buy-ui-klarna': true, 'bag-ui-applepay': true } }
    );

    expect(getByText('PayPal, Apple Pay, Afterpay & Klarna:')).toBeInTheDocument();
    expect(getByRole('link')).toHaveAttribute('href', '/customerService/info.do?cid=81265&cs=payment_options');
    expect(getByText('Limits Apply', { exact: false })).toBeInTheDocument();
    expect(getByText('to Gap Inc. Credit Card Rewards')).toBeInTheDocument();
  });
});
