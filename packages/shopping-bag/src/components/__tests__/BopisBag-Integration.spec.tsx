import { getBopisOnlyBag } from './get-bag-data.mock';
import { PromoTestCases } from './Promos-Integration';
import { RewardsTestCases } from './Rewards-Integration';
import { ChangeStoreModelTestCases } from './ChangeStoreModelBopis-Integration';
import { setupTests } from './FulfillmentMethodTests/setupTests';
import { BOPISTests } from './FulfillmentMethodTests/BOPIS-Integration-Tests';

describe('Bag page with BOPIS only items', () => {
  setupTests(getBopisOnlyBag);
  BOPISTests(0, false);
  ChangeStoreModelTestCases(getBopisOnlyBag);
  PromoTestCases(getBopisOnlyBag);
  RewardsTestCases(getBopisOnlyBag);
});
