import { screen } from 'test-utils';
import userEvent from '@testing-library/user-event';
import { waitFor } from '@testing-library/react';
import fetchMock from 'fetch-mock-jest';
import { getStoreInfo, storeStatusData, geoStoresData, changeStoreItemActionData } from './get-bag-data.mock';

export const ChangeStoreModelTestCases = getBag => {
  it('Should open the store modal without pre-selected store and select one', async () => {
    fetchMock.get('glob:*/shopping-bag-xapi/get-bag*', getBag, { overwriteRoutes: true });
    fetchMock.get('glob:*/commerce/locations/stores/geo*', geoStoresData, { overwriteRoutes: true });
    fetchMock.get('glob:*/commerce/catalog/inventory/v1/sku/store/status*', storeStatusData, { overwriteRoutes: true });
    fetchMock.post('glob:*/shopping-bag-xapi/change-store-item-action*', changeStoreItemActionData, { overwriteRoutes: true });
    fetchMock.get('glob:*/shopping-bag-xapi/get-store-info*', getStoreInfo, { overwriteRoutes: true });

    const findStoreButton = screen.getByRole('button', { name: /Find a store/i });
    expect(findStoreButton).toBeInTheDocument();
    userEvent.click(findStoreButton);

    await waitFor(() => {
      expect(fetchMock.called('glob:*/commerce/catalog/inventory/v1/sku/store/status*')).toBe(true);
    });

    const valleyFairStoreButton = await screen.findByRole('button', { name: /VALLEY FAIR MALL/i });
    expect(valleyFairStoreButton).toBeInTheDocument();
    userEvent.click(valleyFairStoreButton);

    const doneButton = await screen.findByRole('button', { name: /done/i });
    expect(doneButton).toBeInTheDocument();
    userEvent.click(doneButton);

    await waitFor(() => {
      expect(fetchMock.called('glob:*/shopping-bag-xapi/change-store-item-action*')).toBe(true);
    });
    await waitFor(() => {
      expect(fetchMock.called('glob:*/shopping-bag-xapi/get-store-info*')).toBe(true);
    });
    await waitFor(() => {
      const pickupButton = screen.getByText('Pickup at Valley Fair Mall');
      expect(pickupButton).toBeInTheDocument();
    });
  });
};
