import { act, screen } from 'test-utils';

export const ShiptoHomeTests = (itemIndex: number, isMixedBag: boolean) => {
  const displayText = isMixedBag ? 'hide' : 'show';

  it('should display shipping header with number of shipping items in bag', async () => {
    expect(screen.getByText('Bag')).toBeInTheDocument();
    expect(screen.getByText('Shipping (1 Item)')).toBeInTheDocument();
  });

  it('should render with “Ship to address“ radio option selected and “Pickup” radio option unselected', async () => {
    const shipToRadio = screen.getAllByTestId('ship-to-address')[itemIndex].querySelector('input');
    expect(shipToRadio).toBeChecked();
    const changeStoreRadio = screen.getByTestId('find-store').querySelector('input');
    expect(changeStoreRadio).not.toBeChecked();
  });

  it('should render Selling fast banner for ship to home items', async () => {
    expect(screen.getByText('Selling FAST: these popular items in your bag can only be saved for 30 minutes.')).toBeInTheDocument();
  });

  it(`should ${displayText} Paypal container`, async () => {
    if (isMixedBag) {
      const paypalContainer = screen.queryByTestId('paypal-button');
      expect(paypalContainer).not.toBeInTheDocument();
    } else {
      const paypalContainer = await screen.findByTestId('paypal-button');
      expect(paypalContainer).toBeInTheDocument();
    }
  });

  it('should enable both stepper component increment and delete buttons', async () => {
    const incrementButton = screen.getByTestId('stepper-plus');
    const deleteButton = screen.getAllByTestId('delete-bag-item')[itemIndex];
    expect(incrementButton).toBeInTheDocument();
    expect(deleteButton).toBeInTheDocument();

    await act(async () => {
      incrementButton.click();
    });
    const productQuantity = screen.getAllByTestId('product-quantity')[itemIndex];
    const decrementButton = screen.getByTestId('stepper-minus');
    expect(decrementButton).toBeInTheDocument();
    const spanElement = productQuantity.querySelector('span');
    expect(spanElement).toHaveTextContent('2');
    await act(async () => {
      decrementButton.click();
    });
    expect(spanElement).toHaveTextContent('1');
  });

  it('should enable checkout button and navigate to checkout page', async () => {
    const checkoutButton = await screen.findByTestId('checkout-button');
    expect(checkoutButton).toBeInTheDocument();

    await act(async () => {
      checkoutButton.click();
    });
    expect(window.location.href.includes('/checkout')).not.toBeNull();
  });

  it('should show Shipping progress bar and shipping line item in Order summary', async () => {
    const shippingProgressBar = document.querySelector('#meterWrapper');
    const orderSummaryShippingLable = document.querySelector('#shipping-label');
    const shippingValue = document.querySelector('#shipping-value');
    expect(shippingProgressBar).toBeInTheDocument();
    expect(orderSummaryShippingLable).toBeInTheDocument();
    expect(shippingValue).toHaveTextContent('FREE');
  });
};
