import datalayer from '@mfe/data-layer';
import type { DataLayerAttrs, EventData, GenerateProductListTagReturn, Product } from '../types/datalayer';
import { calcPrice } from '../utils';
import { getInitialState } from './initial-state';

/**
 * Returns the globally available datalayer or the one from `@mfe`.
 */

const getDataLayer = () => {
  return datalayer;
};

const generateProductListTag = (products: Product[]): GenerateProductListTagReturn => {
  const initialProductInfo: GenerateProductListTagReturn = {
    product_id: [],
    product_sku: [],
    product_brand: [],
    product_seller_name: [],
    product_seller_id: [],
    product_dropship: [],
    product_name: [],
    product_category: [],
    product_quantity: [],
    product_markdown_amount: [],
    product_gross_retail: [],
    product_gross_merchandise_bag: [],
  };

  return products.reduce((productInfo: GenerateProductListTagReturn, product: Product): GenerateProductListTagReturn => {
    const isDropShip = product.productType === 'Dropship';
    const sellerNameToAdd = isDropShip ? product.webVendorName : product.brandAbbrName;
    const sellerIDToAdd = isDropShip ? product.vendorId : product.brandId;
    const productGrossRetail = Number(product.regularPrice.toFixed(2));
    const productMarkdownAmount = calcPrice(product.regularPrice, product.markdownPrice);
    const productGrossMerchandise = calcPrice(productGrossRetail, productMarkdownAmount);

    productInfo.product_dropship.push(isDropShip ? 'true' : 'false');
    if (sellerNameToAdd) {
      productInfo.product_seller_name.push(sellerNameToAdd);
    }
    if (sellerIDToAdd) {
      productInfo.product_seller_id.push(sellerIDToAdd);
    }
    productInfo.product_id.push(product.styleNumber);
    productInfo.product_sku.push(product.sku);
    productInfo.product_name.push(product.productName);
    productInfo.product_category.push(product.categoryNumber);
    productInfo.product_brand.push(product.brandAbbrName);
    productInfo.product_gross_retail.push(`${productGrossRetail}`);
    productInfo.product_markdown_amount.push(`${productMarkdownAmount}`);
    productInfo.product_gross_merchandise_bag.push(`${productGrossMerchandise}`);
    productInfo.product_quantity.push(`${product.quantity}`);

    return productInfo;
  }, initialProductInfo);
};

const prepareDataLayerEvent = async (attrs: DataLayerAttrs, globalObj: Window = window, type: string): Promise<EventData> => {
  const { event_name, products, source, easy_enroll_type = '', ...rest } = attrs;
  const state = await getInitialState(globalObj, source);
  const { brandCode, brandId, locale = 'en_US', market, page_type } = state;
  const getEasyEnroll = (easyEnrollType: string, pageType: string) => (easyEnrollType ? { easy_enroll_type: `${pageType} ${easyEnrollType}` } : {});
  const brandShortId = brandId === 'gap' ? 'gp' : brandId;
  const variantGroupId = products.map(product => product.variantGroupId ?? '');

  return {
    event_name,
    data: {
      business_unit_id: brandCode,
      channel: `${brandShortId}:buy`,
      market,
      language_code: locale,
      page_type,
      page_name: `${brandShortId}:buy:${source}`,
      ...(type === 'add' ? { product_mvg_id: variantGroupId } : {}),
      ...getEasyEnroll(easy_enroll_type, page_type),
      ...rest,
      ...generateProductListTag(products),
    },
  };
};

/**
 * Publishes datalayer event.
 *
 * Takes an object of the shape:
 *
 * @example
 * ```
 * {
 *   name: 'cart_remove',
 *   type: 'link',
 *   source: 'ShoppingBag',
 *   products: []
 *   { key1: 'value 1', key2: 'value 2' }
 * }
 * ```
 *
 * Optionally we can create a wrapper to preconfig some properties.
 *
 * @example
 * ```
 * const fireDataLayerEvent = payload => {
 *   return publishDataLayerEvent({
 *     ...payload,
 *     type: 'link',
 *     source: 'MiniBag',
 *   });
 * };
 *
 * fireDataLayerEvent({
 *   name: 'minibag_cta_clicks',
 *   products: bagData.productList,
 *   minibag_cta: 'display:global notification',
 *  });
 * ```
 */

export { getDataLayer, generateProductListTag, prepareDataLayerEvent };
