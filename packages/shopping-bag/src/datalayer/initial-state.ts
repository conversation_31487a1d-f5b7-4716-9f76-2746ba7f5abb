import mfeDataLayer from '@mfe/data-layer';
import { brandData } from '../../config/brand-config';
import { getBrandInfo } from '../utils';
import { InitialState, BrandConfig, BrandConfigDetails, InitialDataLayerValues, BrandType } from '../types/tealium';

import { Source } from '../types/datalayer';

const extractBrandDetailsFromConfig = ({ country, brand }: BrandConfig): BrandConfigDetails | undefined => {
  const targetBrand = brandData[country].find((brandFromConfig: { brandId: string }) => brandFromConfig.brandId === brand);

  if (!targetBrand) return undefined;

  return {
    brandCode: Number(targetBrand.brandCode),
    brandName: targetBrand.brandName,
    brandAbbr: targetBrand.brandAbbr,
    brandId: targetBrand.brandId,
    brandDisplayName: targetBrand.brandDisplayName,
    secureUrl: targetBrand.secureUrl as string,
    brandType: targetBrand.brandType as BrandType,
  };
};

const getDatalayerValues = async (source: Source): Promise<InitialDataLayerValues> => {
  const { channel } = await mfeDataLayer.build();

  const { market } = getBrandInfo(window.location.host);

  return {
    channel: `${channel}`,
    page_name: `${channel}:${source}`,
    page_type: `${source}`,
    market,
  };
};

const getInitialState = async (window: Window, source: Source): Promise<InitialState> => {
  const parsedBrandInfo = getBrandInfo(window.location.host);
  const brandDetailsFromConfig = extractBrandDetailsFromConfig(parsedBrandInfo);

  const dataLayerValues = await getDatalayerValues(source);

  return {
    ...dataLayerValues,
    ...parsedBrandInfo,
    ...(brandDetailsFromConfig || {}),
    pageType: source,
  };
};

export { getInitialState, extractBrandDetailsFromConfig };
