import { clientFetch } from '@ecom-next/utils/clientFetch';
import { GET_PAYMENT_SHIPPING } from '../utils/constants';
import type { GetHeadersResponse } from '../utils/getHeaders';
import type { PaymentShippingAddress, OrderResponse } from '../types/payment-types';
import { Feature, logNewRelicError } from '../utils/newrelic-logger';

export const fetchPaymentShipping = async ({
  shippingAddress,
  shippingOptionId,
  headers,
}: {
  headers: GetHeadersResponse;
  shippingAddress: PaymentShippingAddress;
  shippingOptionId?: string;
}) => {
  try {
    const response = await clientFetch<OrderResponse>(GET_PAYMENT_SHIPPING, {
      method: 'POST',
      body: JSON.stringify({ shippingAddress, shippingOptionId }),
      headers,
    });

    return response;
  } catch (err: unknown) {
    logNewRelicError(err as Error, { caller: 'fetchPaymentShipping()', feature: Feature.PAYMENT, message: 'Error fetching payment shipping data' });

    throw err;
  }
};
