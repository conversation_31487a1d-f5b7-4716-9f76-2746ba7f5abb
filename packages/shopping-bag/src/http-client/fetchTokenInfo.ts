import { clientFetch } from '@ecom-next/utils/clientFetch';
import { GET_TOKEN_INFO } from '../utils/constants';
import { UserStatus } from '../types/bag-types';
import { type GetHeadersResponse } from '../utils/getHeaders';
import { Feature, logNewRelicError } from '../utils/newrelic-logger';

export type GetTokenInfoResponse = {
  userState: UserStatus;
};

export const fetchTokenInfo = async (headers: GetHeadersResponse) => {
  try {
    const response = await clientFetch<GetTokenInfoResponse>(`${GET_TOKEN_INFO}`, {
      headers,
    });

    return response;
  } catch (err: unknown) {
    logNewRelicError(err as Error, { caller: 'fetchTokenInfo()', feature: Feature.APPLE_PAY, message: 'Error fetching Apple Pay token' });
    throw err;
  }
};
