{"2078/marketingbanner": {"contentItems": [{"_meta": {"schema": "https://cms.gap.com/schema/content/v1/json-marketing.json", "name": "2024-10-21_2078_Bag_marketingbanner_US", "deliveryId": "f2e3dd83-74a3-4d38-b7d1-9fd65e0bd642"}, "output": {"instanceName": "shoppingBagBanner", "instanceDesc": "10/21 SC Promo NonCard", "ciid": "f2e3dd83-74a3-4d38-b7d1-9fd65e0bd642", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"defaultHeight": "0px", "mobile": {"shouldDisplay": true, "data": {"style": {"display": "block", "maxWidth": "1440px", "margin": "0 auto", "position": "relative"}, "components": [{"instanceName": "promo1_mobile", "name": "LayoutComponent", "type": "sitewide", "data": {"defaultHeight": "0px", "mobile": {"shouldDisplay": true, "data": {"style": {"display": "block", "width": "100%", "margin": "0 auto", "position": "relative", "padding": "0"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "defaultHeight": "0px", "container": {"style": {"position": "relative", "margin": "0 auto", "pointerEvents": "auto"}}, "background": {"className": "", "desktopStyle": {}, "image": {"alt": "friends & family event 40% off everything + cardmember bonus extra 10% off with a gap inc. credit card. code family excludes brand collaborations. ends 10/30.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16130_FandF_US_SC_MOB", "style": {"aspectRatio": "640/178"}, "desktopStyle": {}}}, "overlay": {"alt": "", "srcUrl": "", "style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "title": "Details", "iframeData": {"src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=1061077,1059097", "title": "Details "}}, "composableButtonData": {"children": " ", "capitalization": "none", "size": "xl", "style": {"position": "absolute", "width": "100%", "height": "100%", "bottom": "0", "right": "0", "backgroundColor": "transparent", "border": "none !important"}}}]}}}, {"type": "builtin", "name": "div", "data": {"style": {"fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "position": "absolute", "background": "transparent", "justifyContent": "right", "fontWeight": "500", "fontSize": "2vw", "color": "#000", "bottom": "15%", "right": "10%", "zIndex": "29"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"textTransform": "", "fontWeight": "500"}, "components": ["Excludes brand collaborations. Ends 10/30."]}}]}}, {"type": "builtin", "name": "div", "data": {"style": {"fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "position": "absolute", "background": "transparent", "justifyContent": "right", "fontWeight": "500", "fontSize": "2vw", "color": "#000", "bottom": "15%", "right": "3%", "zIndex": "29"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"textDecoration": "underline", "textTransform": "none", "fontWeight": "500"}, "components": ["Details"]}}]}}]}}}}, {"instanceName": "promo2_mobile", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"defaultHeight": "0px", "mobile": {"shouldDisplay": true, "data": {"style": {"display": "block", "width": "100%", "margin": "0 auto", "position": "relative", "padding": "0"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"className": "", "desktopStyle": {}, "image": {"alt": "not a cardmember? bonus 20% off your first purchase with your new card. combinable with today’s deals. apply now", "srcUrl": "https://cdn.media.amplience.net/i/gapprod/20241021_GS_P_16129_FandF_US_SC_img_MOB", "style": {"aspectRatio": "640/178"}, "desktopStyle": {}}, "linkData": {"to": "https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSSC&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=2078,SC_GGR_CARD_ACQ", "target": "_blank"}}, "overlay": {"alt": "", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16129_FandF_US_SC_copy_MOB_New", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "title": "Details", "iframeData": {"src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=880925", "title": " "}}, "composableButtonData": {"children": "Excludes select brand collaborations.", "capitalization": "none", "size": "xl", "style": {"backgroundColor": "transparent", "fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "color": "#000", "fontSize": "1.5vw", "fontWeight": "500", "textDecoration": "none", "padding": "0", "position": "absolute", "bottom": "10%", "right": "10%", "border": "none !important", "display": "none"}}, "className": ""}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "title": "Details", "iframeData": {"src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=880925", "title": " "}}, "composableButtonData": {"children": "Details", "capitalization": "none", "size": "xl", "style": {"backgroundColor": "transparent", "fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "color": "#000", "fontSize": "2vw", "fontWeight": "500", "textDecoration": "underline", "textTransform": "none", "padding": "0", "position": "absolute", "bottom": "15%", "right": "3%", "minHeight": "initial !important", "border": "none !important"}}, "className": ""}]}}}]}}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"display": "block", "margin": "0 auto", "position": "relative", "padding": "0", "zIndex": "10"}, "components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {}}, "data": {"carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 100, "autoplaySpeed": 3500, "fade": true, "dots": true, "pauseOnHover": true, "dotsClass": "slick-dots", "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true, "prevArrowUrl": "/Asset_Archive/GPWeb/content/0018/820/333/assets/Blank.svg", "nextArrowUrl": "/Asset_Archive/GPWeb/content/0018/820/333/assets/Blank.svg"}, "buttonSetting": {"nextArrowAlt": "Next Slide", "pauseAltText": "Pause Slideshow", "playAltText": "Play Slideshow", "prevArrowAlt": "Previous Slide", "buttonStyle": {"display": "none"}}, "modalCloseButtonAriaLabel": "Close", "style": {"padding": "20px 0", "position": "relative", ".slick-list": {"overflow": "hidden !important"}}, "components": [{"instanceName": "promo1_desk", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"defaultHeight": "0px", "desktop": {"shouldDisplay": true, "data": {"style": {"display": "block", "maxWidth": "425px", "margin": "0 auto", "position": "relative", "padding": "0", "zIndex": "1"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "defaultHeight": "0px", "container": {"style": {"position": "relative", "margin": "0 auto"}}, "background": {"className": "", "desktopStyle": {}, "image": {"alt": "friends & family event 40% off everything + cardmember bonus extra 10% off with a gap inc. credit card. code family excludes brand collaborations. ends 10/30.", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16128_FandF_US_SC_DESK", "style": {"aspectRatio": "423/366"}, "desktopStyle": {}}}, "overlay": {"alt": "", "srcUrl": "", "style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "title": "Details", "iframeData": {"src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=1061077,1059097", "title": ""}}, "composableButtonData": {"children": " ", "capitalization": "none", "size": "xl", "style": {"position": "absolute", "width": "100%", "height": "100%", "fontWeight": "500", "bottom": "0", "right": "0", "backgroundColor": "transparent", "border": "none !important"}}}]}}}, {"type": "builtin", "name": "div", "data": {"style": {"fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "position": "absolute", "background": "transparent", "justifyContent": "right", "fontWeight": "500", "fontSize": "10px", "color": "#000", "bottom": "5%", "right": "50px", "zIndex": "29", "padding": "0", "@media only screen and (max-width: 1324px)": {"fontSize": "9px"}, "@media only screen and (max-width: 1000px)": {"fontSize": "7.8px"}}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"textTransform": "", "fontWeight": "500"}, "components": ["Excludes brand collaborations. Ends 10/30."]}}]}}, {"type": "builtin", "name": "div", "data": {"style": {"fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "position": "absolute", "background": "transparent", "justifyContent": "right", "fontWeight": "500", "fontSize": "10px", "color": "#000", "bottom": "5%", "right": "11px", "zIndex": "29", "padding": "0", "@media only screen and (max-width: 1324px)": {"fontSize": "9px"}, "@media only screen and (max-width: 1000px)": {"fontSize": "7.8px"}}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"textDecoration": "underline", "textTransform": "none", "fontWeight": "500"}, "components": ["Details"]}}]}}]}}}}, {"instanceName": "promo2_desk", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"defaultHeight": "0px", "desktop": {"shouldDisplay": true, "data": {"style": {"display": "block", "maxWidth": "425px", "margin": "0 auto", "position": "relative", "padding": "0", "zIndex": "1"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"className": "", "desktopStyle": {}, "image": {"alt": "not a cardmember? bonus 20% off your first purchase with your new card. combinable with today’s deals. apply now", "desktopSrcUrl": "https://cdn.media.amplience.net/i/gapprod/20241021_GS_P_16127_FandF_US_SC_img_DESK", "style": {"aspectRatio": "423/366"}, "desktopStyle": {}}, "linkData": {"to": "https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSSC&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=2078,SC_GGR_CARD_ACQ", "target": "_blank"}}, "overlay": {"alt": "", "srcUrl": "", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16127_FandF_US_SC_copy_DESK_New", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=880925", "height": "500px"}}, "composableButtonData": {"children": ["Excludes select brand collaborations."], "capitalization": "none", "style": {"fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "position": "absolute", "bottom": "5%", "right": "50px", "border": "0", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "500", "textDecoration": "none", "padding": "0", "outline": "none", "color": "#000", "display": "none", "@media only screen and (max-width: 1324px)": {"fontSize": "9px"}, "@media only screen and (max-width: 1000px)": {"fontSize": "7.8px"}}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=880925", "height": "500px"}}, "composableButtonData": {"children": ["Details"], "style": {"fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "position": "absolute", "bottom": "5%", "right": "11px", "border": "0", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "500", "textDecoration": "underline", "textTransform": "none", "padding": "0", "outline": "none", "color": "#000", "@media only screen and (max-width: 1324px)": {"fontSize": "9px"}, "@media only screen and (max-width: 1000px)": {"fontSize": "7.8px"}}}}]}}}]}}}}]}}]}}}}}]}, "2078/productrecommendations": {"contentItems": [{"_meta": {"name": "2022-05-01_us-bag-certona", "schema": "https://cms.gap.com/schema/content/v1/json-marketing.json", "deliveryId": "f5b71be9-0441-4576-bc33-bebebbac1965"}, "output": {"name": "Recommendations", "type": "home", "experimentRunning": true, "instanceName": "121222-QuickAddTest-GPUS", "tileStyle": {"mobile": {"boxSizing": "border-box", "margin": "0 auto", "maxWidth": "640px", "paddingLeft": "0.5rem", "paddingRight": "0.5rem", "width": "100%"}, "desktop": {}}, "data": {"source": "c<PERSON>a", "lazy": true, "defaultHeight": {"small": "275px", "large": "330px"}, "layout": "carousel", "numberOfProduct": 20, "priceFlag": false, "customBrand": "GAP", "scheme": "gapcart1_rr", "displayTitle": true, "certonaTitle": {"title": "BEST SELLERS", "style": {"desktop": {"color": "#000000", "display": "block", "fontFamily": "Source Sans Pro, Helvetica, Arial, Roboto, sans-serif", "fontSize": "1rem", "fontWeight": "600", "marginTop": "5rem", "marginBottom": "1.25rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}, "mobile": {"color": "#000000", "display": "block", "fontFamily": "Source Sans Pro, Helvetica, Arial, Roboto, sans-serif", "fontSize": "1rem", "fontWeight": "600", "marginBottom": "1.25rem", "paddingLeft": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}}}, "defaultslidesToShowSlick": 2.5, "defaultslidesToScrollSlick": 1, "resslidesToShowSlick": 2.5, "resslidesToScrollSlick": 1, "useMobileConfig": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0015/410/523/assets/gap-hp-arrow.png", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0015/410/523/assets/gap-hp-arrow.png", "productTextStyles": {"productTitle": {"style": {"textAlign": "left"}}, "productPrice": {"style": {}}, "productSalePrice": {"style": {"color": "red"}}, "size": {"width": "auto"}}, "productCardStyles": {"style": {"width": "auto", "marginBottom": "8px"}}, "gridLayout": {}, "withQuickAddEnabled": false}}}]}, "2078/cardpromo": {"contentItems": [{"_meta": {"schema": "https://cms.gap.com/schema/content/v1/json-marketing.json", "name": "2024-10-21_2078_SavingsCalc_20%", "deliveryId": "8ca963e1-5784-4e78-bdd9-288b40f8f3be"}, "output": {"instanceName": "GapCreditCardOffer", "type": "sitewide", "name": "CreditCardOffer", "experimentRunning": true, "redpointExperimentRunning": true, "description": "GS MCM Version", "lazy": false, "useGreyLoadingEffect": false, "data": {"analytics": {"onClickCsc": {"event_name": "csc-applynow-click"}, "onClickOca": {"event_name": "oca-application-start"}}, "showSavingsDetails": true, "savingsThreshold": 4, "discountPercentage": 20, "offerDescriptionEmphasis": "Save 20%", "offerDescription": " on your first purchase, plus free shipping when you open and shop with a new Gap Good Rewards Credit Card.", "applyLinkURL": "https://secure-www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSSCBAGD&mlink=2078,********,CALC_CARD_GGR_ACQ", "applyLinkURLMobile": "https://secure-www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSSCBAGM&mlink=2078,********,CALC_CARD_GGR_ACQ", "cardImg": "/Asset_Archive/AllBrands/assets/shoppingBag/gp/GAP_card_rCMY_Enthusiast_PLCC_woPersoV3.png", "legalDetails": "*FIRST PURCHASE DISCOUNT: Subject to credit approval. Terms Apply. Your new credit card must be used as the sole payment type. Discount code expires at 11:59 p.m. PT fourteen (14) days from date of Account opening. Open a new Gap Good Rewards Credit Card or Gap Good Rewards Mastercard® Account to receive a 20% discount on your first purchase. Valid only in the U.S. (including Puerto Rico). Only one Account opening discount per Account. The First Purchase and Family of Brands Discounts cannot be combined. Discounts cannot be combined with Brand Ambassador, student, or Gap Inc. employee discount, but can be combined with other eligible offers in a single transaction. Excludes GiftCards, shipping, taxes, and all merchandise sold and shipped by 3rd party sellers. Not valid at other Gap Inc. brands or in our Clearance Center. No adjustments to previous purchases. Offer subject to change. FREE SHIPPING ON FIRST PURCHASE: Offer valid for your first order placed at gap.com or gapfactory.com. This offer is valid on shipping to your order’s first “ship to” address, anywhere in the U.S. (including Puerto Rico). Orders must be shipped to a single address. Select the 3-5 day shipping option. If you choose another shipping option, additional charges will apply. Shipping speed for items sold and shipped by third party sellers may vary. One time use only. Enter discount code at checkout. Not responsible for lost or stolen codes. No adjustments on previous purchases."}}}]}, "ShoppingBag": {"2078": {"marketingBanner": {"instanceName": "shoppingBagBanner", "instanceDesc": "10/21 SC Promo NonCard", "ciid": "f2e3dd83-74a3-4d38-b7d1-9fd65e0bd642", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"defaultHeight": "0px", "mobile": {"shouldDisplay": true, "data": {"style": {"display": "block", "maxWidth": "1440px", "margin": "0 auto", "position": "relative"}, "components": [{"instanceName": "promo1_mobile", "name": "LayoutComponent", "type": "sitewide", "data": {"defaultHeight": "0px", "mobile": {"shouldDisplay": true, "data": {"style": {"display": "block", "width": "100%", "margin": "0 auto", "position": "relative", "padding": "0"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "defaultHeight": "0px", "container": {"style": {"position": "relative", "margin": "0 auto", "pointerEvents": "auto"}}, "background": {"className": "", "desktopStyle": {}, "image": {"alt": "friends & family event 40% off everything + cardmember bonus extra 10% off with a gap inc. credit card. code family excludes brand collaborations. ends 10/30.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16130_FandF_US_SC_MOB", "style": {"aspectRatio": "640/178"}, "desktopStyle": {}}}, "overlay": {"alt": "", "srcUrl": "", "style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "title": "Details", "iframeData": {"src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=1061077,1059097", "title": "Details "}}, "composableButtonData": {"children": " ", "capitalization": "none", "size": "xl", "style": {"position": "absolute", "width": "100%", "height": "100%", "bottom": "0", "right": "0", "backgroundColor": "transparent", "border": "none !important"}}}]}}}, {"type": "builtin", "name": "div", "data": {"style": {"fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "position": "absolute", "background": "transparent", "justifyContent": "right", "fontWeight": "500", "fontSize": "2vw", "color": "#000", "bottom": "15%", "right": "10%", "zIndex": "29"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"textTransform": "", "fontWeight": "500"}, "components": ["Excludes brand collaborations. Ends 10/30."]}}]}}, {"type": "builtin", "name": "div", "data": {"style": {"fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "position": "absolute", "background": "transparent", "justifyContent": "right", "fontWeight": "500", "fontSize": "2vw", "color": "#000", "bottom": "15%", "right": "3%", "zIndex": "29"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"textDecoration": "underline", "textTransform": "none", "fontWeight": "500"}, "components": ["Details"]}}]}}]}}}}, {"instanceName": "promo2_mobile", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"defaultHeight": "0px", "mobile": {"shouldDisplay": true, "data": {"style": {"display": "block", "width": "100%", "margin": "0 auto", "position": "relative", "padding": "0"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"className": "", "desktopStyle": {}, "image": {"alt": "not a cardmember? bonus 20% off your first purchase with your new card. combinable with today’s deals. apply now", "srcUrl": "https://cdn.media.amplience.net/i/gapprod/20241021_GS_P_16129_FandF_US_SC_img_MOB", "style": {"aspectRatio": "640/178"}, "desktopStyle": {}}, "linkData": {"to": "https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSSC&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=2078,SC_GGR_CARD_ACQ", "target": "_blank"}}, "overlay": {"alt": "", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16129_FandF_US_SC_copy_MOB_New", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "title": "Details", "iframeData": {"src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=880925", "title": " "}}, "composableButtonData": {"children": "Excludes select brand collaborations.", "capitalization": "none", "size": "xl", "style": {"backgroundColor": "transparent", "fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "color": "#000", "fontSize": "1.5vw", "fontWeight": "500", "textDecoration": "none", "padding": "0", "position": "absolute", "bottom": "10%", "right": "10%", "border": "none !important", "display": "none"}}, "className": ""}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "title": "Details", "iframeData": {"src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=880925", "title": " "}}, "composableButtonData": {"children": "Details", "capitalization": "none", "size": "xl", "style": {"backgroundColor": "transparent", "fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "color": "#000", "fontSize": "2vw", "fontWeight": "500", "textDecoration": "underline", "textTransform": "none", "padding": "0", "position": "absolute", "bottom": "15%", "right": "3%", "minHeight": "initial !important", "border": "none !important"}}, "className": ""}]}}}]}}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"display": "block", "margin": "0 auto", "position": "relative", "padding": "0", "zIndex": "10"}, "components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {}}, "data": {"carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 100, "autoplaySpeed": 3500, "fade": true, "dots": true, "pauseOnHover": true, "dotsClass": "slick-dots", "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true, "prevArrowUrl": "/Asset_Archive/GPWeb/content/0018/820/333/assets/Blank.svg", "nextArrowUrl": "/Asset_Archive/GPWeb/content/0018/820/333/assets/Blank.svg"}, "buttonSetting": {"nextArrowAlt": "Next Slide", "pauseAltText": "Pause Slideshow", "playAltText": "Play Slideshow", "prevArrowAlt": "Previous Slide", "buttonStyle": {"display": "none"}}, "modalCloseButtonAriaLabel": "Close", "style": {"padding": "20px 0", "position": "relative", ".slick-list": {"overflow": "hidden !important"}}, "components": [{"instanceName": "promo1_desk", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"defaultHeight": "0px", "desktop": {"shouldDisplay": true, "data": {"style": {"display": "block", "maxWidth": "425px", "margin": "0 auto", "position": "relative", "padding": "0", "zIndex": "1"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "defaultHeight": "0px", "container": {"style": {"position": "relative", "margin": "0 auto"}}, "background": {"className": "", "desktopStyle": {}, "image": {"alt": "friends & family event 40% off everything + cardmember bonus extra 10% off with a gap inc. credit card. code family excludes brand collaborations. ends 10/30.", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16128_FandF_US_SC_DESK", "style": {"aspectRatio": "423/366"}, "desktopStyle": {}}}, "overlay": {"alt": "", "srcUrl": "", "style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "title": "Details", "iframeData": {"src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=1061077,1059097", "title": ""}}, "composableButtonData": {"children": " ", "capitalization": "none", "size": "xl", "style": {"position": "absolute", "width": "100%", "height": "100%", "fontWeight": "500", "bottom": "0", "right": "0", "backgroundColor": "transparent", "border": "none !important"}}}]}}}, {"type": "builtin", "name": "div", "data": {"style": {"fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "position": "absolute", "background": "transparent", "justifyContent": "right", "fontWeight": "500", "fontSize": "10px", "color": "#000", "bottom": "5%", "right": "50px", "zIndex": "29", "padding": "0", "@media only screen and (max-width: 1324px)": {"fontSize": "9px"}, "@media only screen and (max-width: 1000px)": {"fontSize": "7.8px"}}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"textTransform": "", "fontWeight": "500"}, "components": ["Excludes brand collaborations. Ends 10/30."]}}]}}, {"type": "builtin", "name": "div", "data": {"style": {"fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "position": "absolute", "background": "transparent", "justifyContent": "right", "fontWeight": "500", "fontSize": "10px", "color": "#000", "bottom": "5%", "right": "11px", "zIndex": "29", "padding": "0", "@media only screen and (max-width: 1324px)": {"fontSize": "9px"}, "@media only screen and (max-width: 1000px)": {"fontSize": "7.8px"}}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"textDecoration": "underline", "textTransform": "none", "fontWeight": "500"}, "components": ["Details"]}}]}}]}}}}, {"instanceName": "promo2_desk", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"defaultHeight": "0px", "desktop": {"shouldDisplay": true, "data": {"style": {"display": "block", "maxWidth": "425px", "margin": "0 auto", "position": "relative", "padding": "0", "zIndex": "1"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"className": "", "desktopStyle": {}, "image": {"alt": "not a cardmember? bonus 20% off your first purchase with your new card. combinable with today’s deals. apply now", "desktopSrcUrl": "https://cdn.media.amplience.net/i/gapprod/20241021_GS_P_16127_FandF_US_SC_img_DESK", "style": {"aspectRatio": "423/366"}, "desktopStyle": {}}, "linkData": {"to": "https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSSC&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=2078,SC_GGR_CARD_ACQ", "target": "_blank"}}, "overlay": {"alt": "", "srcUrl": "", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16127_FandF_US_SC_copy_DESK_New", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=880925", "height": "500px"}}, "composableButtonData": {"children": ["Excludes select brand collaborations."], "capitalization": "none", "style": {"fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "position": "absolute", "bottom": "5%", "right": "50px", "border": "0", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "500", "textDecoration": "none", "padding": "0", "outline": "none", "color": "#000", "display": "none", "@media only screen and (max-width: 1324px)": {"fontSize": "9px"}, "@media only screen and (max-width: 1000px)": {"fontSize": "7.8px"}}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=880925", "height": "500px"}}, "composableButtonData": {"children": ["Details"], "style": {"fontFamily": "Gap Sans,Helvetica,Arial,Roboto,sans-serif", "position": "absolute", "bottom": "5%", "right": "11px", "border": "0", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "500", "textDecoration": "underline", "textTransform": "none", "padding": "0", "outline": "none", "color": "#000", "@media only screen and (max-width: 1324px)": {"fontSize": "9px"}, "@media only screen and (max-width: 1000px)": {"fontSize": "7.8px"}}}}]}}}]}}}}]}}]}}}}, "productRecommendations": {"name": "Recommendations", "type": "home", "experimentRunning": true, "instanceName": "121222-QuickAddTest-GPUS", "tileStyle": {"mobile": {"boxSizing": "border-box", "margin": "0 auto", "maxWidth": "640px", "paddingLeft": "0.5rem", "paddingRight": "0.5rem", "width": "100%"}, "desktop": {}}, "data": {"source": "c<PERSON>a", "lazy": true, "defaultHeight": {"small": "275px", "large": "330px"}, "layout": "carousel", "numberOfProduct": 20, "priceFlag": false, "customBrand": "GAP", "scheme": "gapcart1_rr", "displayTitle": true, "certonaTitle": {"title": "BEST SELLERS", "style": {"desktop": {"color": "#000000", "display": "block", "fontFamily": "Source Sans Pro, Helvetica, Arial, Roboto, sans-serif", "fontSize": "1rem", "fontWeight": "600", "marginTop": "5rem", "marginBottom": "1.25rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}, "mobile": {"color": "#000000", "display": "block", "fontFamily": "Source Sans Pro, Helvetica, Arial, Roboto, sans-serif", "fontSize": "1rem", "fontWeight": "600", "marginBottom": "1.25rem", "paddingLeft": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}}}, "defaultslidesToShowSlick": 2.5, "defaultslidesToScrollSlick": 1, "resslidesToShowSlick": 2.5, "resslidesToScrollSlick": 1, "useMobileConfig": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0015/410/523/assets/gap-hp-arrow.png", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0015/410/523/assets/gap-hp-arrow.png", "productTextStyles": {"productTitle": {"style": {"textAlign": "left"}}, "productPrice": {"style": {}}, "productSalePrice": {"style": {"color": "red"}}, "size": {"width": "auto"}}, "productCardStyles": {"style": {"width": "auto", "marginBottom": "8px"}}, "gridLayout": {}, "withQuickAddEnabled": false}}, "cardPromo": {"instanceName": "GapCreditCardOffer", "type": "sitewide", "name": "CreditCardOffer", "experimentRunning": true, "redpointExperimentRunning": true, "description": "GS MCM Version", "lazy": false, "useGreyLoadingEffect": false, "data": {"analytics": {"onClickCsc": {"event_name": "csc-applynow-click"}, "onClickOca": {"event_name": "oca-application-start"}}, "showSavingsDetails": true, "savingsThreshold": 4, "discountPercentage": 20, "offerDescriptionEmphasis": "Save 20%", "offerDescription": " on your first purchase, plus free shipping when you open and shop with a new Gap Good Rewards Credit Card.", "applyLinkURL": "https://secure-www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSSCBAGD&mlink=2078,********,CALC_CARD_GGR_ACQ", "applyLinkURLMobile": "https://secure-www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSSCBAGM&mlink=2078,********,CALC_CARD_GGR_ACQ", "cardImg": "/Asset_Archive/AllBrands/assets/shoppingBag/gp/GAP_card_rCMY_Enthusiast_PLCC_woPersoV3.png", "legalDetails": "*FIRST PURCHASE DISCOUNT: Subject to credit approval. Terms Apply. Your new credit card must be used as the sole payment type. Discount code expires at 11:59 p.m. PT fourteen (14) days from date of Account opening. Open a new Gap Good Rewards Credit Card or Gap Good Rewards Mastercard® Account to receive a 20% discount on your first purchase. Valid only in the U.S. (including Puerto Rico). Only one Account opening discount per Account. The First Purchase and Family of Brands Discounts cannot be combined. Discounts cannot be combined with Brand Ambassador, student, or Gap Inc. employee discount, but can be combined with other eligible offers in a single transaction. Excludes GiftCards, shipping, taxes, and all merchandise sold and shipped by 3rd party sellers. Not valid at other Gap Inc. brands or in our Clearance Center. No adjustments to previous purchases. Offer subject to change. FREE SHIPPING ON FIRST PURCHASE: Offer valid for your first order placed at gap.com or gapfactory.com. This offer is valid on shipping to your order’s first “ship to” address, anywhere in the U.S. (including Puerto Rico). Orders must be shipped to a single address. Select the 3-5 day shipping option. If you choose another shipping option, additional charges will apply. Shipping speed for items sold and shipped by third party sellers may vary. One time use only. Enter discount code at checkout. Not responsible for lost or stolen codes. No adjustments on previous purchases."}}}}, "brand": "gap", "type": "meta", "sitewide": {"desktopemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "attrition_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "isAsyncExperiment": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"instanceName": "dpg_emergency_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "redpointExperimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "mobileemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "attrition_banner_mob", "type": "builtin", "name": "div", "experimentRunning": true, "isAsyncExperiment": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"instanceName": "dpg_emergency_banner_mob", "type": "builtin", "name": "div", "experimentRunning": true, "redpointExperimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "below-topnav": {"type": "builtin", "name": "div", "data": {"components": [{}]}}, "headline": {"type": "builtin", "name": "div", "data": {"components": []}}, "secondary-headline": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "gpus_secondaryheadline-gsb-DPG_target", "instanceDesc": "10/21/24 GSB US", "ciid": "753e9ee1-fda9-45e1-b194-d5cbf89d0846", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "fixedHeight": {"mobile": "calc(98vw*(40/425))", "desktop": "calc(98vw*(50/1920))"}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"display": "inline"}, "components": [{"instanceDesc": "10/21 GSB- HP", "name": "LayeredContentModule", "type": "sitewide", "meta": {"includePageTypes": ["home"]}, "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "#000", "position": "relative", "lineHeight": "0"}, "desktopStyle": {"width": "100%", "backgroundColor": "#000"}}, "background": {"image": {"alt": "cardmembers: extra 10% off with a gap inc. credit card. code FAMILY", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16123_FandF_US_GSB_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16147_FandF_US_GSB_DESK", "style": {"aspect-ratio": "640/75", "display": "flex"}, "desktopStyle": {"aspect-ratio": "1920/50"}}, "linkData": {"to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=55277,GLOBALBANNER_GGR_CARD_RET_PROMO", "target": "_self", "title": "cardmembers: extra 10% off with a gap inc. credit card. code FAMILY"}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "+ bonus 20% off your first purchase with your new card. apply now exclusions apply. ends 10/30.", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "50%", "height": "100%", "width": "47%"}, "desktopStyle": {"width": "48%", "left": "50.5%", "height": "100%", "top": "0%"}}, "linkData": {"to": "https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSGSB&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=55277,GLOBALBANNER_CARD_GGR_ACQ", "target": "_self", "title": "+ bonus 20% off your first purchase with your new card. apply now exclusions apply. ends 10/30."}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=1059097,880925", "height": "500px"}}, "composableButtonData": {"children": "Exclusions apply. Ends 10/30.", "style": {"position": "absolute", "background": "unset", "textTransform": "unset", "padding": "0", "color": "#ffffff", "fontSize": "1.9vw", "fontWeight": "500", "bottom": "2.4vw", "right": "9vw", "display": "none"}, "desktopStyle": {"fontSize": "0.6vw", "bottom": "0.72vw", "right": "4.5vw", "display": "inline"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=1059097,880925", "height": "500px"}}, "composableButtonData": {"children": "Details", "style": {"position": "absolute", "background": "unset", "textDecoration": "underline", "textTransform": "none", "padding": "0", "color": "#ffffff", "fontSize": "1.65vw", "fontWeight": "500", "bottom": "1.60vw", "right": "1vw"}, "desktopStyle": {"fontSize": "0.65vw", "bottom": "0.72vw", "right": "1.25vw"}}}]}}}, {"name": "Carousel", "type": "sitewide", "meta": {"excludePageTypes": ["home"]}, "data": {"carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 0, "autoplaySpeed": 3000, "fade": false, "displayPlayPauseBtn": false, "arrows": false, "arrowPosition": "0", "prevArrowUrl": "/Asset_Archive/GPWeb/content/static/brand-icons/gp_carousel-carat_spring2021_left--blue.svg", "nextArrowUrl": "/Asset_Archive/GPWeb/content/static/brand-icons/gp_carousel-carat_spring2021_right--blue.svg", "displayArrows": {"mobile": false, "desktop": false}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"instanceDesc": "10/21 GSB- 1", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "#000", "position": "relative", "lineHeight": "0"}, "desktopStyle": {"width": "100%", "backgroundColor": "#000"}}, "background": {"image": {"alt": "friends & family event 40% off everything", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16122_FandF_USEC_GSB_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16121_FandF_USEC_GSB_DESK", "style": {"aspect-ratio": "640/75", "display": "flex"}, "desktopStyle": {"aspect-ratio": "1920/50"}}, "linkData": {"to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=55277,globalbanner", "target": "_self", "title": "friends & family event 40% off everything"}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "+ 50% off all pjs excludes brand collaborations.", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "64%", "height": "100%", "width": "27%"}, "desktopStyle": {"width": "48%", "left": "43.5%", "height": "100%", "top": "0%"}}, "linkData": {"to": "/browse/category.do?cid=1072174#pageId=0&mlink=55277,globalbanner", "target": "_self", "title": "+ 50% off all pjs excludes brand collaborations."}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=1061077,1061117", "height": "500px"}}, "composableButtonData": {"children": "Excludes select collaborations.", "style": {"display": "none", "position": "absolute", "background": "unset", "textTransform": "unset", "padding": "0", "color": "#ffffff", "fontSize": "1.9vw", "fontWeight": "500", "bottom": "2.4vw", "right": "9vw"}, "desktopStyle": {"fontSize": "0.6vw", "bottom": "0.72vw", "right": "4.5vw"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=1061077,1061117", "height": "500px"}}, "composableButtonData": {"children": "Details", "style": {"position": "absolute", "background": "unset", "textDecoration": "underline", "textTransform": "none", "padding": "0", "color": "#ffffff", "fontSize": "1.65vw", "fontWeight": "500", "bottom": "1.60vw", "right": "1vw"}, "desktopStyle": {"fontSize": "0.65vw", "bottom": "0.72vw", "right": "1.25vw"}}}]}}}, {"instanceDesc": "10/21 GSB- 2", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "#000", "position": "relative", "lineHeight": "0"}, "desktopStyle": {"width": "100%", "backgroundColor": "#000"}}, "background": {"image": {"alt": "cardmembers: extra 10% off with a gap inc. credit card. code FAMILY", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16123_FandF_US_GSB_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16147_FandF_US_GSB_DESK", "style": {"aspect-ratio": "640/75", "display": "flex"}, "desktopStyle": {"aspect-ratio": "1920/50"}}, "linkData": {"to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=55277,GLOBALBANNER_GGR_CARD_RET_PROMO", "target": "_self", "title": "cardmembers: extra 10% off with a gap inc. credit card. code FAMILY"}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "+ bonus 20% off your first purchase with your new card. apply now exclusions apply. ends 10/30.", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "50%", "height": "100%", "width": "47%"}, "desktopStyle": {"width": "48%", "left": "50.5%", "height": "100%", "top": "0%"}}, "linkData": {"to": "https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSGSB&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=55277,GLOBALBANNER_CARD_GGR_ACQ", "target": "_self", "title": "+ bonus 20% off your first purchase with your new card. apply now exclusions apply. ends 10/30."}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=1059097,880925", "height": "500px"}}, "composableButtonData": {"children": "Exclusions apply. Ends 10/30.", "style": {"position": "absolute", "background": "unset", "textTransform": "unset", "padding": "0", "color": "#ffffff", "fontSize": "1.9vw", "fontWeight": "500", "bottom": "2.4vw", "right": "9vw", "display": "none"}, "desktopStyle": {"fontSize": "0.6vw", "bottom": "0.72vw", "right": "4.5vw", "display": "inline"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=1059097,880925", "height": "500px"}}, "composableButtonData": {"children": "Details", "style": {"position": "absolute", "background": "unset", "textDecoration": "underline", "textTransform": "none", "padding": "0", "color": "#ffffff", "fontSize": "1.65vw", "fontWeight": "500", "bottom": "1.60vw", "right": "1vw"}, "desktopStyle": {"fontSize": "0.65vw", "bottom": "0.72vw", "right": "1.25vw"}}}]}}}], "modalCloseButtonAriaLabel": "Close", "buttonSetting": {"nextArrowAlt": "next", "pauseAltText": "pause", "playAltText": "play", "prevArrowAlt": "previous"}}}]}}}}]}}, "edfslarge": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-large", "type": "sitewide", "name": "MktEdfsLarge", "tileStyle": {"alignItems": "center", "display": "flex", "height": "40px", "margin-top": "1px", "textTransform": "uppercase"}, "experimentRunning": true, "data": {"shouldWaitForOptimizely": "true", "defaultData": {"text": "FREE SHIPPING ON $50+ FOR REWARDS MEMBERS", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=1194685", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "SIGN IN OR JOIN", "path": "/my-account/sign-in", "style": {"letterSpacing": "0", "fontSize": "1em", "display": "inline", "position": "relative", "fontWeight": "400", "top": "-1.5px", "fontFamily": "'Gap Sans', Helvetica, Arial, Roboto, sans-serif"}}}}]}}, "edfssmall": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-small", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"shouldWaitForOptimizely": true, "lazy": false, "fixedHeight": {"mobile": "40px", "desktop": "0px"}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto"}, "components": [{"instanceDesc": "WCD HP CSS Modifications", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>#sitewide-app > header > div.sitewide-13o7eu2 > div > div:nth-child(2) > div > div > div > button {text-transform:uppercase}</style>"}}, {"type": "sitewide", "name": "MktEdfsSmall", "data": {"lazy": false, "experimentRunning": false, "styles": {"headline": {"fontSize": "10px", "padding": "0 10px", "textTransform": "uppercase"}}, "defaultData": {"text": "Free shipping on $50+ for Rewards Members", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=1194685", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "Sign In or Join", "path": "/my-account/sign-in", "style": {"letterSpacing": "0", "display": "inline", "position": "relative", "top": "-1px", "fontSize": "1em", "fontWeight": "400", "paddingRight": "0.5em", "color": "rgb(153, 153, 153)", "textTransform": "uppercase", "fontFamily": "'Gap Sans', Helvetica, Arial, Roboto, sans-serif"}}}}]}}, "desktop": {"shouldDisplay": false, "data": {"components": []}}}}]}}, "hamburgerNavBanner": {"type": "builtin", "name": "div", "data": {"components": [{}]}}, "hamnavRedesignBanner": {"type": "builtin", "name": "div", "data": {"components": [{}]}}, "popup": {"type": "builtin", "name": "div", "data": {"components": [{"ciid": "d6a62823-24b0-49a2-9984-50d422be109e", "name": "DynamicModal", "type": "sitewide", "instanceName": "general-email-popup-101424", "experimentRunning": true, "data": {"excludePageTypes": ["Information", "ShoppingBag", "Profile"], "shouldWaitForOptimizely": true}}]}}, "promorover": {"type": "builtin", "name": "div", "data": {"components": []}}, "prefooter": {"type": "builtin", "name": "div", "data": {"components": [{}]}}, "countdown": {"type": "builtin", "name": "div", "data": {"components": []}}, "footer": {"footer-ciid": "5cfe02a5-b466-4605-816b-124d46f41128", "footer-desc": "2024-10-15-redesign", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "sitewide", "components": [{"name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "582px", "large": "435px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "width": "100%"}, "components": [{"name": "Footer", "type": "sitewide", "data": {"socialLinks": [{"to": "https://www.facebook.com/gap/", "text": "Follow Gap on Facebook"}], "emailRegistration": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<h3 class=\"wcd_footer_h1\">See It First</h3>"}}, "emailPlaceholderText": "Enter your email address", "submitButtonText": "Join", "submitButtonOptions": {"mobile": {"className": "wcd_footer_cta"}, "desktop": {"className": "wcd_footer_cta"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<p class=\"wcd_footer_copy legal\"><a onclick=\"return contentItemLink(this,'','CS_Footer_PrivacyPolicy');\" href=\"https://corporate.gapinc.com/en-us/consumer-privacy-policy\" target=\"_blank\" class=\"nowrap\">Privacy Policy</a>"}}}, "marketingBannerLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {}, "components": [{"instanceDesc": "Social Icon Container", "type": "builtin", "name": "div", "data": {"style": {"display": "flex", "position": "relative", "width": "fit-content", "left": "16px", "top": "-25%", ">:not(:last-child)": {"marginRight": "3vw"}}, "desktopStyle": {"position": "absolute", "left": "2.5%", "top": "54%", "@media only screen and (max-width:1700px)": {"left": "2.5%", "top": "50%"}, "@media only screen and (max-width:1500px)": {"left": "2.5%", "top": "46%"}, "@media only screen and (max-width:1300px)": {"left": "2.5%", "top": "43%"}, "@media only screen and (max-width:1023px)": {"left": "2.5%", "top": "27%"}, ">:not(:last-child)": {"marginRight": "1vw"}}, "components": [{"instanceDesc": "Social Icon 1", "type": "builtin", "name": "a", "data": {"style": {}, "props": {"href": "http://www.instagram.com/gap", "target": "_blank"}, "components": [{"type": "builtin", "name": "img", "data": {"props": {"src": "https://gapprod.a.bigcontent.io/v1/static/SU247114_IG", "alt": "Instagram"}}}]}}, {"instanceDesc": "Social Icon 2", "type": "builtin", "name": "a", "data": {"style": {}, "props": {"href": "http://www.youtube.com/@gap", "target": "_blank"}, "components": [{"type": "builtin", "name": "img", "data": {"props": {"src": "https://gapprod.a.bigcontent.io/v1/static/SU247114_YT", "alt": "Youtube"}}}]}}, {"instanceDesc": "Social Icon 3", "type": "builtin", "name": "a", "data": {"style": {}, "props": {"href": "https://www.tiktok.com/@gap?is_from_webapp=1&sender_device=pc", "target": "_blank"}, "components": [{"type": "builtin", "name": "img", "data": {"props": {"src": "https://gapprod.a.bigcontent.io/v1/static/SU247114_TT", "alt": "TikTok"}}}]}}]}}, {"instanceName": "footer_overrides", "instanceDesc": "<PERSON><PERSON> Footer Overrides", "name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<style>#sitewide-footer{background-color:#fff;color:#000;}.gap-footer *{box-sizing:border-box;}.gap-footer .nowrap{white-space:nowrap;}.gap-footer sup{font-size:1em;line-height:0;vertical-align:baseline;}.gap-footer .wcd_footer_h1{color:#000;font-size:14px;font-weight:500;line-height:1.125;margin-bottom:1.5em;text-transform:none;}.gap-footer .wcd_footer_copy:not(:last-child){margin-bottom:1.125em;}.gap-footer .wcd_footer_copy.legal{font-size:11px;}.gap-footer .gap-footer .wcd_footer_cta a,.gap-footer .wcd_footer_cta button,.gap-footer a.wcd_footer_cta,.gap-footer button.wcd_footer_cta{-ms-flex-align:center;align-items:center;background-color:#fff;border-width:0;font-size:14px;font-weight:500;height:32px;-ms-flex-pack:center;justify-content:center;letter-spacing:0;padding-left:16px;padding-right:16px;text-transform:none;}.gap-footer .wcd_footer_cta a:hover,.gap-footer .wcd_footer_cta button:hover,.gap-footer a.wcd_footer_cta:hover,.gap-footer button.wcd_footer_cta:hover{background-color:#fff;border-width:0;color:#2b2b2b;}.gap-footer .wcd_footer_cta{display:-ms-flexbox;display:flex;}.gap-footer .wcd_footer_cta.full-width{width:100%;}.gap-footer .wcd_footer_cta.full-width a,.gap-footer .wcd_footer_cta.full-width button{width:100%;}.gap-footer .wcd_footer_cta.full-width a:not(:first-child),.gap-footer .wcd_footer_cta.full-width button:not(:first-child){margin-left:8px;}.gap-footer .wcd_footer_cta.details button{background-color:transparent;color:#fff;display:inline;font-size:11px;height:auto;min-height:16px;min-width:36px;padding:0;text-decoration:underline;}.gap-footer .wcd_footer_cta.details button:hover{color:#fff;}.gap-footer .wcd_footer_cta a,.gap-footer .wcd_footer_cta button{display:-ms-flexbox;display:flex;}.gap-footer .wcd_footer_cta span{font-size:1.125em;padding-bottom:.25em;}.gap-footer .wcd_footer_cta ul{background-color:transparent;box-shadow:none;border:none;padding-bottom:4px;}.gap-footer .wcd_footer_cta li{border:none;padding:0;}.gap-footer .wcd_footer_cta li a{font-weight:500;font-size:14px;padding-left:32px;letter-spacing:0;text-transform:none;}.gap-footer [data-testid=prefooter-row]{margin-bottom:0;}@media only screen and (max-width:1024px){.gap-footer .email-registration__wrapper{margin-bottom:5vw}}@media only screen and (max-width:767px){.gap-footer .email-registration__wrapper{margin-bottom:15vw}}.gap-footer .email-registration__wrapper{-ms-flex-align:start;align-items:flex-start;background-color:transparent;min-height:120px;padding:26px 0;}.gap-footer .email-registration__wrapper>div{margin:0 auto;max-width:640px;width:calc(100% - 32px);}.gap-footer .email-registration__wrapper .email-registration__title{max-width:100%;padding:0;text-align:left;}.gap-footer .email-registration__wrapper .email-registration__inputs{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;max-width:420px;}.gap-footer .email-registration__wrapper .email-registration__disclaimer{padding-left:0;}.gap-footer .email-registration__wrapper .email-registration__form{-ms-flex-align:end;align-items:flex-end;display:-ms-flexbox;display:flex;margin-bottom:24px;}.gap-footer .email-registration__wrapper .email-text-input-wrapper{margin-right:24px;}.gap-footer .email-registration__wrapper .email-registration__form-email{margin:0;padding-bottom:0;}.gap-footer .email-registration__wrapper .email-registration__form-email input[type=email]{margin-top:0;padding:0;}span.sitewide-1sr49e6{display:none;}.gap-footer .email-registration__wrapper .email-registration__form-email span.sitewide-v1qhrf-LabelText-Label{font-size:11px;text-transform:none;top:0;}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container .wcd_footer_cta{min-height:32px;padding-bottom:0;padding-top:0;}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container div[aria-label=loading]{transform:rotate(90deg);}.gap-footer .email-registration__wrapper .notification-after-button:empty,.gap-footer .email-registration__wrapper .notification-before-form:empty{display:none;}.gap-footer .medallia-feedback-wrapper{-ms-flex-order:4;order:4;padding:0 16px;width:100%;}.gap-footer .medallia-feedback-wrapper>button{-ms-flex-align:center;align-items:center;background-color:#fff;border-width:0;color:#000;display:-ms-flexbox;display:flex;font-weight:500;height:36px;-ms-flex-pack:center;justify-content:center;letter-spacing:0;margin:0 auto;max-width:640px;padding:0 16px;width:100%;}.gap-footer .medallia-feedback-wrapper>button img{margin-right:.375rem;}.gap-footer .footer-copyright-section{background-color:#fff;border-top-color:#000;border-width:0;color:#000;-ms-flex-order:5;order:5;padding:24px 0 80px;width:100%;}.gap-footer .footer-copyright-section .footer-legal__wrapper{margin:0 auto;max-width:640px;text-align:left;width:calc(100% - 32px);}.gap-footer .footer-copyright-section .footer_copyright-row{font-size:12px;font-weight:500;line-height:1.5;}.gap-footer .footer-copyright-section .footer_copyright-row:not(:last-child){margin-bottom:1.5em;}.gap-footer .footer-copyright-section a,.gap-footer .footer-copyright-section button{color:inherit;font-size:inherit;}.gap-footer .footer-copyright-section a:hover,.gap-footer .footer-copyright-section button:hover{text-decoration:underline;}.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--divider,.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--span{color:inherit;font-size:inherit;}.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--span,.gap-footer .footer-copyright-section .footer-legal__wrapper a,.gap-footer .footer-copyright-section .footer-legal__wrapper button{display:inline-block;text-transform:none;}.gap-footer .footer-container-wrapper{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;max-width:100%;position:relative;}.gap-footer .footer-container-wrapper .copy-wrapper{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;margin-bottom:12px;}.gap-footer .footer-container-wrapper>div:nth-child(5){-ms-flex-direction:column;flex-direction:column;margin-left:auto;margin-right:auto;max-width:672px;width:100%;}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child{margin-bottom:30px;}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta{background-color:transparent;color:inherit;-ms-flex-direction:column;flex-direction:column;}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta a,.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta button{background-color:transparent;-ms-flex-pack:start;justify-content:flex-start;}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:nth-child(2){margin-bottom:6px;padding:0 16px;}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:nth-child(3){margin-bottom:6px;padding:0 16px;}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:12px;line-height:1.25;}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a:not(:last-child){margin-bottom:.5em;}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a:hover{text-decoration:underline;}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column .wcd_footer_header{margin-bottom:.75em;text-transform:capitalize;}#footer .email-registration__form-submit-button-container button{background-color:#000;color:#fff;}#footer input{border-bottom:1px solid #000;color:#000;}#footer .wcd_footer_copy.legal,#footer label span,.footer_copyright-row{color:#000;text-transform:none;margin-bottom:0!important;}.email-registration__form-email>span.css-eynuxh[value],.email-registration__form-email>span.sitewide-1sr49e6[value]{display:block;}@media only screen and (min-width:768px){.gap-footer .wcd_footer_h1{margin-bottom:1em;color:#000;text-transform:capitalize}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:16px;font-weight:500;line-height:1}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:12px;text-transform:none}.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:12px}.gap-footer .wcd_footer_copy.legal a{text-decoration:underline;color:#000}.gap-footer [data-testid=prefooter-row]{display:block;padding-bottom:48px}.gap-footer .email-registration__wrapper{padding-bottom:0;padding-left:0;padding-top:0}.gap-footer .email-registration__wrapper>div{margin-left:0;max-width:100%;width:100%}.gap-footer .email-registration__wrapper .email-text-input-wrapper{margin-right:10px}.gap-footer .footer-copyright-section{border-top-width:1px;padding-top:44px}.gap-footer .footer-copyright-section .footer-legal__wrapper{margin:0;max-width:1920px;padding-left:2.5%;padding-right:2.5%}.gap-footer .footer-container-wrapper{-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between;margin:0 auto;max-width:1400px;padding-top:30px}.gap-footer .footer-container-wrapper .copy-wrapper{-ms-flex-align:center;align-items:center;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;margin-bottom:0}.gap-footer .footer-container-wrapper .copy-wrapper>div:not(:last-child){margin-right:.75em}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:nth-child(2){display:none}.gap-footer .wcd_footer-links-wrapper{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{-ms-flex-positive:1;flex-grow:1}.gap-footer [data-testid=prefooter-row]{padding-left:2.5%;padding-right:2.5%;width:100%}.gap-footer .footer-container-wrapper>div:nth-child(3){padding-bottom:48px;padding-left:2.5%;padding-right:2.5%;width:100%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:16px}}@media only screen and (min-width:1024px){.gap-footer .wcd_footer-links-wrapper{-ms-flex-pack:start;justify-content:flex-start}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{-ms-flex-positive:initial;flex-grow:initial}.gap-footer [data-testid=prefooter-row]{padding-right:0;width:300px}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:3%;padding-left:0;width:calc(97% - 300px)}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:7%}}@media only screen and (min-width:1280px){.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:14px}.gap-footer [data-testid=prefooter-row]{width:24%}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:6%;padding-bottom:84px;width:70%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:11%}}@media only screen and (min-width:1440px){.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header,.gap-footer .email-registration__wrapper .email-registration__form-email span{font-size:16px}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:12px;font-weight:500}.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:11px}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container .wcd_footer_cta{font-size:16px;height:52px;padding:0 26px}.gap-footer .footer-container-wrapper{padding-top:54px}.gap-footer [data-testid=prefooter-row]{width:29%}.gap-footer .footer-container-wrapper>div:nth-child(3){width:65%}}@media only screen and (min-width:1920px){.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:16px}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:12px}.gap-footer [data-testid=prefooter-row]{width:24%}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:0;width:71%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:18%}}@media only screen and (max-width:767px){.gap-footer .footer-container-wrapper>div:nth-child(2){display:none}.gap-footer .wcd_footer_cta ul[aria-hidden=false]{max-height:1000px}#footer .feedback-link,#footer .wcd_footer_app a{font-size:14px;height:36px;background-color:#000;color:#fff;text-transform:none}#footer label span{font-size:14px;font-weight:500}.gap-footer .medallia-feedback-wrapper>button{font-size:14px;font-weight:500}}</style>"}}]}}}}, "customerSupportLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Mobile Footer Links", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_footer_cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "Customer Support"}, "submenu": [{"text": "Customer Service", "href": "/customerService/info.do?cid=2136&mlink=55278,********,CS_Footer_CustomerService"}, {"text": "Buy Online. Pick Up In-Store.", "href": "/customerService/info.do?cid=1161798&mlink=55278,********,LP_BOPIS_footer_CTA"}, {"text": "Store Locator", "href": "/stores"}, {"text": "GapCash", "href": "/browse/info.do?cid=99996&mlink=55278,********,LP_GapCash_footer_CTA"}, {"text": "GiftCards", "href": "/customerService/info.do?cid=2116&mlink=55278,********,CS_Footer_Giftcards"}]}}, {"buttonDropdownData": {"heading": {"text": "Gap Good Rewards"}, "submenu": [{"text": "Join <PERSON> Good Rewards", "href": "/my-account/sign-in?mlink=55278,********,UNIFOOTER_GGR_ACQ"}, {"text": "Apply for a Credit Card", "href": "/my-account/sign-in?creditOffer=barclays&sitecode=GPSSUNIFTM&mlink=55278,********,UNIFOOTER_GGR_CARD_ACQ"}, {"text": "My Rewards & Benefits", "href": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=55278,********,UNIFOOTER_MTL_RET"}, {"text": "Pay Credit Card Bill", "href": "https://gap.barclaysus.com/servicing/home?redirectAction=/payment", "target": "_blank"}, {"text": "Learn More", "href": "/customerService/info.do?cid=1099008&sitecode=GPSSUNIFTILPM&mlink=55278,UNIFOOTER_GGR_LP_CARD_ACQ", "target": "_blank"}]}}, {"buttonDropdownData": {"heading": {"text": "About Us"}, "submenu": [{"text": "Our Values", "href": "https://www.gapinc.com/en-us/values", "target": "_blank"}, {"text": "Sustainability", "href": "https://www.gap.com/browse/info.do?cid=1086537", "target": "_blank"}, {"text": "Equality and Belonging", "href": "https://www.gap.com/browse/info.do?cid=1179886", "target": "_blank"}, {"text": "Careers", "href": "https://www.gapinc.com/en-us/careers/gap-careers", "target": "_blank"}]}}]}}}, {"instanceDesc": "Download the App", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_app"}, "components": [{"name": "a", "type": "builtin", "data": {"props": {"href": "https://gap.onelink.me/9QBP/12b7d83a", "className": "wcd_footer_cta full-width"}, "components": ["Download the App"]}}]}}, {"instanceDesc": "Sign up for Text Messages", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_app"}, "components": [{"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gap.com/browse/info.do?cid=1195815", "className": "wcd_footer_cta full-width"}, "components": ["Sign up for Text Messages"]}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Desktop Footer Links", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-wrapper"}, "components": [{"instanceDesc": "Desktop Footer Links - Column 1", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["Customer Support"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=2136&mlink=55278,********,CS_Footer_CustomerService"}, "components": ["Customer Service"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1161798&mlink=55278,********,LP_BOPIS_footer_CTA", "target": "_blank"}, "components": ["Buy Online. Pick Up In-Store."]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/stores"}, "components": ["Store Locator"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/browse/info.do?cid=99996&mlink=55278,********,LP_GapCash_footer_CTA"}, "components": ["GapCash"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=2116&mlink=55278,********,CS_Footer_Giftcards"}, "components": ["GiftCards"]}}]}}, {"instanceDesc": "Desktop Footer Links - Column 2", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["Gap Good Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?mlink=55278,********,UNIFOOTER_GGR_ACQ"}, "components": ["Join <PERSON> Good Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?creditOffer=barclays&sitecode=GPSSUNIFTD&mlink=55278,********,UNIFOOTER_GGR_CARD_ACQ"}, "components": ["Apply for a Credit Card"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=55278,********,UNIFOOTER_MTL_RET"}, "components": ["My Rewards & Benefits"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://gap.barclaysus.com/servicing/home?redirectAction=/payment", "target": "_blank"}, "components": ["Pay Credit Card Bill"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1099008&sitecode=GPSSUNIFTILPD&mlink=55278,UNIFOOTER_GGR_LP_CARD_ACQ", "target": "_blank"}, "components": ["Learn More"]}}]}}, {"instanceDesc": "Desktop Footer Links - Column 3", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["About Us"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/values", "target": "_blank"}, "components": ["Our Values"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gap.com/browse/info.do?cid=1086537", "target": "_blank"}, "components": ["Sustainability"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gap.com/browse/info.do?cid=1179886", "target": "_blank"}, "components": ["Equality and Belonging"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/careers/gap-careers", "target": "_blank"}, "components": ["Careers"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/browse/info.do?cid=1174367&shortlink=12b7d83a&c=SiteFooterLink_download_our_app&pid=Mobile%20Footer%20link&source_caller=ui"}, "components": ["Get the App"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gap.com/browse/info.do?cid=1195815"}, "components": ["Sign up for Text Messages"]}}]}}]}}]}}}}}}]}}}}]}, "header": {"CIID": "dea9bab2-7631-4c9f-8114-2745f1ca01ba", "byCid": [{"details": "Setting up rules for specific ciids", "configurationForCids": []}], "byPageType": [{"details": "Rules for homepage", "configurationForPageTypes": ["home"], "stickyBackground": "contrast", "contrast": "dark", "fullBleedOptions": {"fullBleedContrast": "light", "isFullBleedEnabled": false}}, {"details": "Rules for non-hp", "configurationForPageTypes": ["category", "product", "Information", "customlandingpage", "search", "dynamicerror", "division"]}], "default": {"details": "Universal rules", "isUtilityLinksEnabled": false, "headerLayout": "sameRow", "stickyScrollDirection": "up", "isStickyEnabled": true, "stickyBackground": "contrast", "contrast": "dark", "fullBleedOptions": {"fullBleedContrast": "light", "isFullBleedEnabled": false}, "styles": {}}}, "topnav": {"topnav-ciid": "e560d547-2c2e-49ff-a6ef-c05f3e8951e8", "instanceName": "topnav-101524-redesign", "name": "div", "type": "builtin", "experimentRunning": true, "data": {"style": {"width": "100%"}, "tabletStyle": {}, "desktopStyle": {}, "props": {}, "tabletProps": {}, "desktopProps": {}, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>#topNavWrapper>div>div>ul>li:nth-child(10)>div>a{font-weight:500!important;}@media only screen and (min-width:1024px){div[role=search]{box-sizing:border-box;margin:0;padding:10px 10px 18px 20px;padding-right:1rem}.meganav{box-shadow:0 10px 10px -4px rgba(0 0 0 / 15%)}div[data-testid=header-logo-topnav-seach-at-same-row]{margin:0 auto;max-width:1400px!important;justify-content:space-between}.gap-topnav-logo-dt{min-width:7vw;margin-right:1em!important}.gap-topnav-logo-dt>a>img{width:100%;max-inline-size:fit-content}div[data-testid=header-logo-topnav-seach-at-same-row]>div:nth-child(2){width:100%}#topNavWrapper .topnav a.divisionLink{text-transform:none!important;font-weight:500!important;color:#000000!important}#topNavWrapper .topnav .meganav .catnav--header .catnav--item a.sale{color:#e10000}}</style>", "style": {}, "classes": ""}}, {"name": "MegaNav", "type": "sitewide", "data": {"isNavSticky": true, "classStyles": {"topnav li:not(.catnav--item)": "padding: 0;", "topnav a.divisionLink": "box-shadow: none !important; box-sizing: border-box; display: block; font-size: min(max(16px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); font-weight: 500; height: 90px; width: max-content; line-height: 1; min-height: 0vw; padding: 40px 0 0; position: relative; text-transform: none; margin: 0 auto;", "topnav a.divisionLink::before": "border-color: transparent; border-style: solid; border-width: 0 0 1px; content: ''; height: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 16px); left: 50%; min-height: 12px; padding-bottom: 3px; position: absolute; top: 42px; transform: translateX(-50%); width: 100%; display:block", "topnav a.divisionLink._selected": "color: #2b2b2b;", "topnav li:hover a.divisionLink": "background-color: #fff;", "topnav li:hover a.divisionLink::before": "border-color: #2b2b2b;", "topnav a.divisionLink._selected::before": "border-color: #2b2b2b;", "topnav a.divisionLink:hover": "box-shadow: none !important;", "topnav li.catnav--header > span": "border-bottom-color: #2b2b2b;color: #000;font-weight: 500;font-size:16px;", "topnav li.catnav--header > a": "border-bottom-color: #2b2b2b;color: #000;font-weight: 500;font-size:16px;", "topnav a.divisionLink.navlink-pink": "color: #e51937;", "topnav a.divisionLink.navlink-red": "color: #e51937", "topnav a.divisionLink.navlink-gift": "color: #e51937;", "topnav .catnav--item--link": "color: #000; font-weight: 500; font-size: 12px;", "topnav .catnav-links a:catnav--item--link:hover": "border-bottom: 1px solid #767676;", "meganav": "border-top-width: 0"}, "activeDivisions": [{"name": "New", "divisionId": ["1086624"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=3037798&mlink=39813,TOPNAV_NEW_CASHSOFT_1_VISNAV' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/FA247453_img' alt='Shop the Cashsoft Collection image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/FA247454_CTA_v2_1' alt='Shop the Cashsoft Collection'></a></li></ul></li>"], ["1139272", "3037929"], ["3019290"], ["3023115"], ["3019289"], ["<li class='catnav--header'> <ul class='catnav-links'> <li class='catnav--item'> <a href='browse/category.do?cid=3024953&mlink=39813,TOPNAV_NEW_GIFTSHOP_2_VISNAV' class='catnav--item--link' style='position:relative;display:block'> <img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/HOL247556_img' alt='Visit The Giftshop image'> <img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL247556_CTA_2' alt='Visit The Giftshop'> </a> </li> </ul> </li>"]], "numberOfColumns": {}, "exclusionIds": [], "customStyles": {}}, {"name": "Women", "divisionId": ["/browse/division.do?cid=5643&mlink=39813,30012485,Megnav_Women", "5646"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=3022755&mlink=39813,TOPNAV_NEW_WCASHSOFT_1_VISNAV' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/FA247454_img' alt='Shop the Cashsoft Collection image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/FA247454_CTA_v2_1' alt='Shop the Cashsoft Collection'></a></li></ul></li>"], ["1164545", "1131702"], ["1042481"], ["3023110", "3036863"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=1072174&mlink=39813,TOPNAV_W_FAMILYPJS_2_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/FA247476_img' alt='Shop Pajamas for Everyone image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/2024-10-02-PJs_SVG_Update_FA247476' alt='Shop Pajamas for Everyone'></a></li></ul></li>"]], "numberOfColumns": {"1042481": 2}, "exclusionIds": [], "customStyles": {"65179": {"colorScheme": "sale"}}}, {"name": "Men", "divisionId": ["/browse/division.do?cid=5063&mlink=39813,30012485,Megnav_Men&clink=30012485", "5065"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=1165627&mlink=39813,TOPNAV_NEW_CASHSOFT_1_VISNAV' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/FA247455_img' alt='Shop the Cashsoft Collection image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/FA247454_CTA_v2_1' alt='Shop the Cashsoft Collection'></a></li></ul></li>"], ["1164547", "1149531"], ["1042515"], ["1076121"], ["3029287", "3036877"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=1072174&mlink=39813,TOPNAV_M_FAMILYPJS_2_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/FA247476_img' alt='Shop Pajamas for Everyone image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/2024-10-02-PJs_SVG_Update_FA247476' alt='Shop Pajamas for Everyone'></a></li></ul></li>"]], "numberOfColumns": {"1042515": 2}, "exclusionIds": [], "customStyles": {"65289": {"colorScheme": "sale"}}}, {"name": "Girls", "divisionId": ["/browse/division.do?cid=1137865&mlink=39813,30012485,Megnav_Girls&clink=30012485", "6256"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=1135179&mlink=39813,TOPNAV_GIRLS_FAMILYPJS_2_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/FA247456_img' alt='Shop the Cashsoft Collection image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/FA247454_CTA_v2_1' alt='Shop the Cashsoft Collection'></a></li></ul></li>"], ["1161294", "1164548", "1056088"], ["1042516"], ["3037869", "3036894"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=1072174&mlink=39813,TOPNAV_NEW_FAMILYPJS_2_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/FA247476_img' alt='Shop Pajamas for Everyone image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/2024-10-02-PJs_SVG_Update_FA247476' alt='Shop Pajamas for Everyone'></a></li></ul></li>"]], "numberOfColumns": {"1042516": 2}, "exclusionIds": [], "customStyles": {"65194": {"colorScheme": "sale"}}}, {"name": "Boys", "divisionId": ["/browse/division.do?cid=1137867&mlink=39813,30012485,Megnav_Girls&clink=30012485", "6172"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=1133131&mlink=39813,TOPNAV_BOYS_FAMILYPJS_2_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/FA247457_img' alt='Shop the Cashsoft Collection image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/FA247454_CTA_v2_1' alt='Shop the Cashsoft Collection'></a></li></ul></li>"], ["1161295", "1164549", "1056087"], ["1042518"], ["3037872", "3036896"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=1072174&mlink=39813,TOPNAV_NEW_FAMILYPJS_2_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/FA247476_img' alt='Shop Pajamas for Everyone image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/2024-10-02-PJs_SVG_Update_FA247476' alt='Shop Pajamas for Everyone'></a></li></ul></li>"]], "numberOfColumns": {"1042518": 2}, "exclusionIds": [], "customStyles": {"65217": {"colorScheme": "sale"}}}, {"name": "Baby & Toddler", "divisionId": ["/browse/division.do?cid=1137869&mlink=39813,30012485,Megnav_Baby&clink=30012485", "1137869"], "megaNavOrder": [["<li class='catnav--header'> <ul class='catnav-links'> <li class='catnav--item'> <a href='browse/category.do?cid=1138346&style=3037440&mlink=39813,TOPNAV_BT_FAMILYPJS_2_visnav' class='catnav--item--link' style='position:relative;display:block'> <img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/FA247458_img' alt='Shop the Cashsoft Collection image'> <img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/FA247454_CTA_v2_1' alt='Shop the Cashsoft Collection'> </a> </li> </ul> </li>"], ["1164551", "1149847", "3036898", "3018069"], ["3025453"], ["95461"], ["95574"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=1072174&mlink=39813,TOPNAV_NEW_FAMILYPJS_2_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/FA247476_img' alt='Shop Pajamas for Everyone image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/2024-10-02-PJs_SVG_Update_FA247476' alt='Shop Pajamas for Everyone'></a></li></ul></li>"]], "exclusionIds": [], "customStyles": {"65208": {"colorScheme": "sale"}, "65236": {"colorScheme": "sale"}, "65261": {"colorScheme": "sale"}, "65263": {"colorScheme": "sale"}}}, {"name": "Gifts", "divisionId": ["/browse/category.do?cid=3024953", "83061"], "megaNavOrder": [["<li class='catnav--header'> <ul class='catnav-links'> <li class='catnav--item'> <a href='browse/category.do?cid=3024953&mlink=39813,TOPNAV_GIFTS_GIFTSHOP_1_VISNAV' class='catnav--item--link' style='position:relative;display:block'> <img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/HOL247556_img' alt='Visit The Giftshop image'> <img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL247556_CTA_2' alt='Visit The Giftshop'> </a> </li> </ul> </li>"], ["1166400"], ["1142181"], ["3038809"], ["3038810"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='customerService/info.do?cid=2116&mlink=39813,TOPNAV_GIFTS_GIFTCARD_2_VISNAV' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://cdn.media.amplience.net/i/gapprod/HOL247644_img' alt='Shop Gift Cards image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL247644_CTA' alt='Shop Gift Cards'></a></li></ul></li>"]], "exclusionIds": [], "customStyles": {"65208": {"colorScheme": "sale"}, "65236": {"colorScheme": "sale"}, "65261": {"colorScheme": "sale"}, "65263": {"colorScheme": "sale"}}}]}}]}}, "promodrawer": {"name": "PromoDrawerComponentV2", "type": "sitewide", "sitewide-promodrawer-ciid": "2024-10-21_55276_PromoDrawer_US", "instanceName": "promoDrawer-en_US", "experimentRunning": false, "data": {"shouldWaitForOptimizely": false, "buildInfo": ["2024-10-21_55276_PromoDrawer_US", "GP"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "information", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "Profile", "LoyaltyValueCenter", "utility", "Information"], "anchor": "bottom"}, "autoFire": "disabled", "disabledAutoFirePageTypes": ["category"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.promoDrawer__content__item__msg, \n.promoDrawer__content__item__msg > span {\n  text-transform: none !important;\n}\n.pd_four-cta-m29f5g73 {\n  background-color: #000; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_four-cta-m29f5g73 img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_four-cta-m29f5g73 .pd_four-cta--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_four-cta-m29f5g73 .pd_four-cta_button {\n  border: #fff solid 1px;\n  box-sizing: border-box;\n  color: #FFF;\n  font-size: 10px;\n  font-weight: 400;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: none;\n  width: 48.5%;\n}\n.pd_four-cta-m29f5g73 .pd_four-cta_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n\n<div class=\"pd_four-cta-m29f5g73\">\n  <a href=\"/browse/category.do?cid=8792#pageId=0&department=136&mlink=55276,PD_Tile1\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16392_FandF_USEC_PD\" alt=\"friends & family event 40% off everything excludes brand collaborations. applied at checkout.\">\n  </a>\n  <div class=\"pd_four-cta--cta-container\">\n    <a href=\"/browse/category.do?cid=8792#pageId=0&department=136&mlink=55276,PD_Tile1\" class=\"pd_four-cta_button\">Women</a>\n    <a href=\"/browse/category.do?cid=11900#pageId=0&department=75&mlink=55276,PD_Tile1\" class=\"pd_four-cta_button\">Men</a>\n    <a href=\"/browse/category.do?cid=63895#pageId=0&department=48&mlink=55276,PD_Tile1\" class=\"pd_four-cta_button\">Girls</a>\n    <a href=\"/browse/category.do?cid=63896#pageId=0&department=16&mlink=55276,PD_Tile1\" class=\"pd_four-cta_button\">Boys</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "Applied at checkout.", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "Details", "legalOverride": "", "genericCodeId": "1061077", "genericCode": ""}, "promoId": "m29f4opw"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.promoDrawer__content__item__msg, \n.promoDrawer__content__item__msg > span {\n  text-transform: none !important;\n}\n.pd_four-cta-m29f9ubj {\n  background-color: #000; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_four-cta-m29f9ubj img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_four-cta-m29f9ubj .pd_four-cta--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_four-cta-m29f9ubj .pd_four-cta_button {\n  border: #fff solid 1px;\n  box-sizing: border-box;\n  color: #FFF;\n  font-size: 10px;\n  font-weight: 400;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: none;\n  width: 48.5%;\n}\n.pd_four-cta-m29f9ubj .pd_four-cta_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n\n<div class=\"pd_four-cta-m29f9ubj\">\n  <a href=\"/browse/category.do?cid=1072174#pageId=0&department=&mlink=52776,PD_Tile2\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16392_FandF_USEC_PD-1\" alt=\"50% off all pjs\">\n  </a>\n  <div class=\"pd_four-cta--cta-container\">\n    <a href=\"/browse/category.do?cid=29504#pageId=0&department=136&mlink=52776,PD_Tile2\" class=\"pd_four-cta_button\">Women</a>\n    <a href=\"/browse/category.do?cid=5270#pageId=0&department=75&mlink=52776,PD_Tile2\" class=\"pd_four-cta_button\">Men</a>\n    <a href=\"/browse/category.do?cid=6323#pageId=0&department=48&mlink=52776,PD_Tile2\" class=\"pd_four-cta_button\">Girls</a>\n    <a href=\"/browse/category.do?cid=9470#pageId=0&department=16&mlink=52776,PD_Tile2\" class=\"pd_four-cta_button\">Boys</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "Details", "legalOverride": "", "genericCodeId": "1061117", "genericCode": ""}, "promoId": "m29f4pgk"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd__tap-to-apply {text-transform: none;} .promoDrawer__content__item__msg, \n.promoDrawer__content__item__msg > span {\n  text-transform: none !important;\n}\n.pd_one-cta-m29fkeg3 {\n  background-color: #000; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_one-cta-m29fkeg3 img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_one-cta-m29fkeg3 .pd_one-cta--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n  justify-content: center;\n}\n.pd_one-cta-m29fkeg3 .pd_one-cta_button {\n  border: #fff solid 1px;\n  box-sizing: border-box;\n  color: ##FFF;\n  font-size: 10px;\n  font-weight: 400;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: none;\n  width: 48.5%;\n}\n.pd_one-cta-m29fkeg3 .pd_one-cta_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n\n<div class=\"pd_one-cta-m29fkeg3\">\n  <a href=\"/browse/category.do?cid=8792#pageId=0&department=136&mlink=55276,PD_TILE_GGR_CARD_RET_PROMO\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16133_FandF_US_PD\" alt=\"cardmember bonus extra 10% off with a gap inc. credit card. code family excludes brand collaborations. ends 10/30.\">\n  </a>\n  <div class=\"pd_one-cta--cta-container\"><br>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "tap", "overlay": "Applied at checkout", "defaultMessage": "Tap to apply", "isTappedMessage": "Applied at checkout"}, "legalDetails": {"popupTextLink": "Details", "legalOverride": "", "genericCodeId": "1059097", "genericCode": "FAMILY"}, "promoId": "m29f4q5d"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.promoDrawer__content__item__msg, \n.promoDrawer__content__item__msg > span {\n  text-transform: none !important;\n}\n.pd_two-cta-m29fn3h8 {\n  background-color: #cecece; /* default */\n  color: #000; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_two-cta img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two-cta-m29fn3h8 .pd_two-cta--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_two-cta-m29fn3h8 .pd_two-cta_button {\n  border: #000 solid 1px;\n  box-sizing: border-box;\n  color: #000;\n  font-size: 10px;\n  font-weight: 400;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: none;\n  width: 48.5%;\n}\n.pd_two-cta-m29fn3h8 .pd_two-cta_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n\n<div class=\"pd_two-cta-m29fn3h8\">\n  <a href=\"https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSPD&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=55276,PD_TILE_GGR_CARD_ACQ\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"https://gapprod.a.bigcontent.io/v1/static/20241021_GS_P_16135_FandF_US_PD\" alt=\"not a cardmember? bonus 20% off your first purchase with your new card. apply now combinable with today’s deals.\">\n  </a>\n  <div class=\"pd_two-cta--cta-container\">\n    <a href=\"https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSPD&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=55276,PD_TILE_GGR_CARD_ACQ\" class=\"pd_two-cta_button\">Apply now</a>\n    <br>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile4"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "Combinable with today’s deals.", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "Details", "legalOverride": "", "genericCodeId": "880925", "genericCode": ""}, "promoId": "m29f4qw0"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>#promo-drawer-button{background-color:#000 !important}</style>"}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "style": {"desktop": {"fontSize": ".8em !important", "text-transform": "none !important", "letter-spacing": "0px !important"}, "mobile": {"fontSize": ".8em", "text-transform": "none !important", "letter-spacing": "0px !important"}}, "text": "{--! headerText !--}"}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "style": {"desktop": {"fontSize": ".8em !important", "text-transform": "none !important", "letter-spacing": "0px !important"}, "mobile": {"fontSize": ".8em", "text-transform": "none !important", "letter-spacing": "0px !important"}}, "text": "{--! subHeaderText !--}"}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "My offers", "iconAltText": "Open icon", "linkWithModalDisplayStyle": "none", "subHeaderText": "(4 available)"}, "closedState": {"headerText": "Friends & Family Event: 40% off everything + more", "subHeaderText": "", "iconAltText": "Closed icon"}, "aria-label": "Friends & Family Event: 40% off everything + more"}, "pd_id": "pdid_1534541049574"}}, "utilitylinks": {"type": "sitewide", "name": "UtilityLinks", "data": {"style": {"fontSize": "10.5px"}, "brandBarShortcutLinks": [{"link": "/stores?sitecode=GPSSSEARCH&mlink=39813,29693256,SEARCHBAR_STORELOCATOR", "text": "Find a store"}, {"link": "/customerService/info.do?cid=1099008&sitecode=GPSSSEARCH&mlink=39813,29666338,SEARCHBAR_GGR_ACQ", "text": "Gap Good Rewards"}, {"link": "/customerService/info.do?cid=2116&sitecode=GPSSSEARCH&mlink=39813,29693256,SEARCHBAR_GIFTCARD", "text": "Gift Card"}]}}, "hamnav": {"sitewide-topnav-ciid": "aa4e9ffe-c3a3-4bfc-af1a-01745a6ed59c", "sitewide-hamnav-desc": "Hamnav 11-20-2023", "instanceName": "Hamnav-11-20-2023", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"lazy": false, "defaultHeight": {}, "isVisible": {"large": false, "small": true}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {}, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "desc": "saleToRed-CSS", "data": {"html": "<style> @media (max-width: 1023px) { .hamnav-item-list li[data-testid^='category-65'] a { color: red } .hamnav-item-list li[data-testid*='3026711'] button, .hamnav-item-list li[data-testid*='category-3013615'] a, .hamnav-item-list li[data-testid*='category-1187420'] a, .hamnav-item-list li[data-testid*='category-1187426'] a, .hamnav-item-list li[data-testid*='category-1187423'] a, .hamnav-item-list li[data-testid*='category-1187425'] a, .hamnav-item-list li[data-testid*='category-1187428'] a, .hamnav-item-list li[data-testid*='category-1187427'] a, .hamnav-item-list li[data-testid*='category-1187419'] a, .hamnav-item-list li[data-testid*='category-3027311'] a, .hamnav-item-list li[data-testid*='category-1187473'] a, .hamnav-item-list li[data-testid*='category-1168077'] a { color: green } } </style>", "style": {}, "classes": ""}}, {"type": "sitewide", "name": "HamburgerNav", "data": {"activeDivisions": ["1086624", "5643", "5063", "1137865", "1137867", "1137869", "83061", {"cid": "1156863", "name": "Sale", "customStyles": {"button": {"div": {"color": "red"}}}}]}}]}}}}, "search": {"instanceDesc": "Search config code is in the TopNav container", "type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Sweater", "Sweatpants", "<PERSON>igan", "<PERSON><PERSON><PERSON>", "Dresses", "<PERSON><PERSON><PERSON>", "Blazer", "Leggings"]}}, "logo": {"name": "Logo", "type": "sitewide", "altText": "Gap logo", "lightLogoImgPath": "/Asset_Archive/GPWeb/content/0030/015/725/assets/logo/logo_gap--light.svg", "darkLogoImgPath": "/Asset_Archive/GPWeb/content/0030/015/725/assets/logo/logo_gap--dark.svg", "logoImgPath": "/Asset_Archive/GPWeb/content/0028/669/369/assets/logo/Gap_logo_MOB_newV2.svg", "isSquare": true, "className": "gap-topnav-logo-dt"}}}