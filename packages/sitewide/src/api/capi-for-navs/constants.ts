// note that these were taken directly from nav services, and may not be accurate to the final implementation
export const NODE_TYPE_MAP: Record<string, string> = {
  HOMEPAGE_CONTENT: 'home',
  STANDARD_CORE: 'category',
  STANDARD_CONTENT: 'category',
  DIVISION_CONTENT: 'division',
  STANDARD_HEADER: 'header',
  STANDARD_SALE: 'sale',
  STANDARD_TRIMHEADER: 'trimheader',
  DIVISION_SISTER_SITE: 'sisterbrand',
  HEADERLESS_GROUP: 'headerless-group',
  STANDARD_SPACER: 'spacer',
  SUBCATEGORY: 'subcategory',
  SECTION: 'section',
  SUBDIVSION_CONTENT: 'sub-division',
};

export const VALID_HEADER_TYPES = [NODE_TYPE_MAP.STANDARD_HEADER, NODE_TYPE_MAP.HOMEPAGE_CONTENT, NODE_TYPE_MAP.STANDARD_TRIMHEADER];

export const VALID_TEMPLATE_TYPES = {
  SUBCATEGORY: '/browse/category',
  BROWSE_HOME_TEMPLATE: '/browse/home',
  BROWSE_INFO_TEMPLATE: '/browse/info',
  BROWSE_DIVISION_TEMPLATE: '/browse/division',
  BROWSE_CATEGORY_TEMPLATE: '/browse/category',
  CUSTOMER_SERVICE_INFO_TEMPLATE: '/customerService/info',
  BROWSE_CATEGORY_SEARCH_TEMPLATE: '/browse/categorySearch',
  SUBDIVSION_CONTENT: '/browse/subDivision',
};

export const TYPE_TO_TEMPLATE_TYPES: Record<string, string> = {
  SUBCATEGORY: VALID_TEMPLATE_TYPES.SUBCATEGORY,
  STANDARD_HEADER: VALID_TEMPLATE_TYPES.BROWSE_INFO_TEMPLATE,
  STANDARD_SPACER: VALID_TEMPLATE_TYPES.BROWSE_INFO_TEMPLATE,
  HOMEPAGE_CONTENT: VALID_TEMPLATE_TYPES.BROWSE_HOME_TEMPLATE,
  STANDARD_CONTENT: VALID_TEMPLATE_TYPES.BROWSE_INFO_TEMPLATE,
  STANDARD_CORE: VALID_TEMPLATE_TYPES.BROWSE_CATEGORY_TEMPLATE,
  STANDARD_SALE: VALID_TEMPLATE_TYPES.BROWSE_CATEGORY_TEMPLATE,
  STANDARD_MARKETING: VALID_TEMPLATE_TYPES.BROWSE_INFO_TEMPLATE,
  STANDARD_TRIMHEADER: VALID_TEMPLATE_TYPES.BROWSE_INFO_TEMPLATE,
  DIVISION_CONTENT: VALID_TEMPLATE_TYPES.BROWSE_DIVISION_TEMPLATE,
  DIVISION_CLEARANCE: VALID_TEMPLATE_TYPES.BROWSE_DIVISION_TEMPLATE,
  STANDARD_CLEARANCE: VALID_TEMPLATE_TYPES.BROWSE_CATEGORY_TEMPLATE,
  DIVISION_SISTER_SITE: VALID_TEMPLATE_TYPES.BROWSE_CATEGORY_SEARCH_TEMPLATE,
  CUSTOMER_SERVICE_HEADER: VALID_TEMPLATE_TYPES.CUSTOMER_SERVICE_INFO_TEMPLATE,
  CUSTOMER_SERVICE_SPACER: VALID_TEMPLATE_TYPES.CUSTOMER_SERVICE_INFO_TEMPLATE,
  CUSTOMER_SERVICE_CONTENT: VALID_TEMPLATE_TYPES.CUSTOMER_SERVICE_INFO_TEMPLATE,
  CUSTOMER_SERVICE_STANDARD: VALID_TEMPLATE_TYPES.CUSTOMER_SERVICE_INFO_TEMPLATE,
};

export const ITEMS_TO_REMOVE = {
  webCategoryId: ['1118143'],
  webCategoryDescription: ['hillcity.com'],
  webCategoryType: ['CUSTOMER_SERVICE_CONTENT', 'CUSTOMER_SERVICE_SPACER', 'CUSTOMER_SERVICE_HEADER', 'CUSTOMER_SERVICE_STANDARD'],
};
