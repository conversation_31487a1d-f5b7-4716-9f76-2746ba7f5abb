'use client';
import { useContext } from 'react';
import Slider from 'react-slick';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { mapDataToProps } from '@mui/components/legacy/helper';
import { DynamicMarketing } from '@mui/components/json-marketing.client';
import { componentShouldBeVisible } from '@sitewide/components/legacy/utils';
import AnimatedHeadlineContainer from './AnimatedHeadlineContainer';
import { AnimatedHeadlineProps } from './types';
import { usePersonalizedHeadlines } from './usePersonalizedHeadlines';
import { usePlayPauseOnTouch } from './usePlayPauseOnTouch';
import { usePageTypeFilters } from './usePageTypeFilters';

export const DEFAULT_DURATION = 15000;
export const DEFAULT_TRANSITION_SPEED = 1000;
const DEFAULT_BACKGROUND_COLOR = '#FFFFFF';
const DEFAULT_HEIGHT = '30px';

export function AnimatedHeadline({
  components,
  duration = DEFAULT_DURATION,
  options = {},
  style,
  transitionSpeed = DEFAULT_TRANSITION_SPEED,
  useContainerFullHeight = false,
  vertical = true,
}: AnimatedHeadlineProps): JSX.Element | null {
  const { onTouchStart, sliderRef } = usePlayPauseOnTouch();
  const filteredComponents = usePageTypeFilters(components);
  const componentsToShow = usePersonalizedHeadlines(filteredComponents);

  const { minWidth } = useContext(BreakpointContext);
  const isDesktop = minWidth(LARGE);
  const { isDesktopVisible = true, isMobileVisible = true } = options;

  if (!componentShouldBeVisible(isDesktopVisible, isMobileVisible, isDesktop)) {
    return null;
  }

  const settings = {
    vertical,
    arrows: false,
    autoplay: true,
    autoplaySpeed: duration,
    pauseOnHover: isDesktop,
    fade: true,
    infinite: true,
    slidesToScroll: 1,
    slidesToShow: 1,
    speed: transitionSpeed,
    swipe: false,
    centerMode: true,
    centerPadding: useContainerFullHeight ? '0px' : '1px',
  };

  const breakpointStyle = {
    ...style,
    ...style?.[isDesktop ? 'desktop' : 'mobile'],
  };

  const backgroundColor = breakpointStyle?.backgroundColor ?? DEFAULT_BACKGROUND_COLOR;
  const height = breakpointStyle?.height ?? DEFAULT_HEIGHT;

  return componentsToShow.length > 0 ? (
    <AnimatedHeadlineContainer
      backgroundColor={backgroundColor}
      data-testid='animated-headline-container'
      data-hui-rewrite='animated-headline'
      height={height}
      isDesktop={isDesktop}
      onTouchStart={onTouchStart}
      useContainerFullHeight={useContainerFullHeight}
    >
      <Slider ref={sliderRef} {...settings}>
        {componentsToShow?.map(component => {
          const { instanceName } = component;
          return <DynamicMarketing key={instanceName} {...component} />;
        })}
      </Slider>
    </AnimatedHeadlineContainer>
  ) : null;
}

export default mapDataToProps(AnimatedHeadline);
