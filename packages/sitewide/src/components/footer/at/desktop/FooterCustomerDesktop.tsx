import { Link } from '@ecom-next/core/migration/link';
import { AT_LINK_TEXT } from '../utils/ATLinkTextUtil';
import { AT_LINK_HREF } from '../utils/ATLinkHrefUtil';
import { generateSecureUrl } from '../../../../utils/linkUtil';
import { usePageContext } from '../../../../hooks/usePageContext';
import { FooterHeading } from './FooterHeading';

/**
 * @name FooterCustomerDesktop
 * @description FooterCustomerDesktop component for AT US and CA used for Desktop.
 * @returns JSX.Element
 */

export const FooterCustomerDesktop = () => {
  const { host } = window.location;
  const { market, locale } = usePageContext();
  const isCanada = market === 'ca';

  return (
    <div className='text-b1 clear-both mx-auto w-full max-w-[990px] border-b-[1px] border-[#d7d7d7] py-3 pb-[18px] leading-[18px]'>
      <div className='clear-both w-full'>
        {isCanada && <FooterHeading locale={locale} />}
        <div className='grid grid-cols-4 text-[13px]'>
          {/* Column 1 */}
          <div className='mx-8 flex flex-col'>
            <h3 data-testid='customer-support-heading' className='text-b1 mb-1.5 w-full font-bold'>
              {AT_LINK_TEXT[locale].customerSupport}
            </h3>
            <Link
              data-testid='footer-customer-service'
              className='leading-5 text-[#76787b] hover:underline'
              target='_self'
              to={AT_LINK_HREF.customerService[market]}
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].customerService}
            </Link>
            <Link
              data-testid='footer-shipping'
              className='leading-5 text-[#76787b] hover:underline'
              target='_self'
              to={AT_LINK_HREF.shipping[market]}
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].shipping}
            </Link>
            <Link
              data-testid='free-returns'
              className='leading-5 text-[#76787b] hover:underline'
              target='_self'
              to={AT_LINK_HREF.freeReturns[market]}
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].freeReturns}
            </Link>
            <Link
              data-testid='track-order'
              className='leading-5 text-[#76787b] hover:underline'
              target='_self'
              to={generateSecureUrl({ host, path: '/my-account/order-lookup' })}
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].trackYourOrder}
            </Link>
            <Link
              data-testid='gift-cards'
              className='leading-5 text-[#76787b] hover:underline'
              target='_self'
              to='/browse/info.do?cid=52564&mlink=55287,********,Footer_GiftCards&clink=********'
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].giftCards}
            </Link>
            <Link
              data-testid='size-and-fit'
              className='leading-5 text-[#76787b] hover:underline'
              target='_self'
              to={AT_LINK_HREF.sizeAndFit[market]}
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].sizeAndFit}
            </Link>
            {isCanada && (
              <Link data-testid='site-map' className='leading-5 text-[#76787b] hover:underline' target='_self' to='/products/index.jsp' useNextLink={true}>
                {AT_LINK_TEXT[locale].siteMap}
              </Link>
            )}
          </div>

          {/* Column 2 */}
          <div className='mx-8 flex flex-col'>
            <h3 data-testid='about-us-heading' className='text-bk mb-1.5 w-full font-bold'>
              {AT_LINK_TEXT[locale].aboutUs}
            </h3>
            <Link
              data-testid='our-values'
              className='leading-5 text-[#76787b] hover:underline'
              target='_self'
              to='/browse/info.do?cid=1074427&mlink=55287,********,Footer_OurValues&clink=********'
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].ourValues}
            </Link>
            <Link
              data-testid='people-planet'
              className='leading-5 text-[#76787b] hover:underline'
              target='_self'
              to={AT_LINK_HREF.peopleAndPlanet[market]}
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].peopleAndPlanet}
            </Link>
            {!isCanada && (
              <>
                <Link
                  data-testid='power-of-she'
                  className='leading-5 text-[#76787b] hover:underline'
                  target='_self'
                  to='/browse/info.do?cid=1214438&mlink=1,1,Footer_PowerofSheCollective'
                  useNextLink={true}
                >
                  {AT_LINK_TEXT[locale].powerofSheCollective}
                </Link>
                <Link
                  data-testid='wellpro'
                  className='leading-5 text-[#76787b] hover:underline'
                  target='_self'
                  to='/browse/info.do?cid=1214124&mlink=1,1,Footer_WellPro_0228_WellProLP'
                  useNextLink={true}
                >
                  {AT_LINK_TEXT[locale].wellPro}
                </Link>
              </>
            )}

            <Link
              data-testid='work-at-athleta'
              className='leading-5 text-[#76787b] hover:underline'
              target='_self'
              to={AT_LINK_HREF.workAtAthleta[market]}
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].workAtAthleta}
            </Link>
            <Link
              data-testid='gap-sustainability'
              className='leading-5 text-[#76787b] hover:underline'
              target='_self'
              to={AT_LINK_HREF.gapSustainability[market]}
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].gapSustainability}
            </Link>
          </div>

          {/* Column 3 */}
          <div className='mx-8 flex flex-col'>
            <h3 data-testid='find-us-heading' className='text-bk mb-1.5 w-full font-bold'>
              {AT_LINK_TEXT[locale].findUs}
            </h3>
            <div data-testid='at-phone' className='text-base font-bold leading-[18px] text-[#76787b]'>
              ************
            </div>
            <Link
              data-testid='find-store'
              className='leading-5 text-[#76787b] hover:underline'
              target='_self'
              to='/stores?mlink=55287,********,Footer_StoreLocator&clink=********'
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].findAStore}
            </Link>
            <Link
              data-testid='email-sign-up'
              className='leading-5 text-[#76787b] hover:underline'
              target='_self'
              to='/profile/info.do?cid=57480&mlink=55287,********,Footer_Email&clink=********'
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].emailSignup}
            </Link>

            {/* Icons */}
            {!isCanada && (
              <div className='space-between mt-2 flex gap-1'>
                <Link data-testid='instagram-icon' to='//instagram.com/athleta/' target='_blank' className='w-5' useNextLink={true}>
                  <picture>
                    <img alt='Instagram' src='https://athleta.gap.com/Asset_Archive/ATWeb/content/0014/460/498/assets/Instagram.svg'></img>
                  </picture>
                </Link>
                <Link data-testid='pinterest-icon' to='//pinterest.com/athleta/' target='_blank' className='w-5' useNextLink={true}>
                  <picture>
                    <img alt='Pinterest' src='https://athleta.gap.com/Asset_Archive/ATWeb/content/0014/460/498/assets/Pinterest.svg'></img>
                  </picture>
                </Link>
                <Link data-testid='facebook-icon' to='//facebook.com/athleta/' target='_blank' className='w-5' useNextLink={true}>
                  <picture>
                    <img alt='Facebook' src='https://athleta.gap.com/Asset_Archive/ATWeb/content/0014/460/498/assets/Facebook.svg'></img>
                  </picture>
                </Link>
                <Link data-testid='twitter-icon' to='//twitter.com/athleta/' target='_blank' className='w-5' useNextLink={true}>
                  <picture>
                    <img alt='Twitter' src='https://athleta.gap.com/Asset_Archive/ATWeb/content/0014/460/498/assets/Twitter.svg'></img>
                  </picture>
                </Link>
              </div>
            )}
          </div>

          {/* Column 4 */}
          <div className='mx-8 flex flex-col'>
            {isCanada && (
              <div className='mb-2'>
                <span>
                  <picture>
                    <img
                      alt='Athleta Rewards'
                      className='h-auto max-w-full'
                      src={
                        locale === 'fr_CA'
                          ? '/Asset_Archive/ATWeb/content/0020/704/646/assets/French_CDA_AthletaRewards_Logo.svg'
                          : '/Asset_Archive/ATWeb/content/0020/704/646/assets/ATHLETA REWARDS_logo_CDA.svg'
                      }
                    />
                  </picture>
                </span>
              </div>
            )}
            <Link
              data-testid='points-and-rewards'
              className='leading-5 text-[#76787b] hover:underline'
              target='_blank'
              to={generateSecureUrl({
                host,
                path: AT_LINK_HREF.myPointsAndRewards[market],
              })}
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].myPointsAndRewards}
            </Link>
            <Link
              data-testid='explore-benefits'
              className='leading-5 text-[#76787b] hover:underline'
              target='_blank'
              to={AT_LINK_HREF.exploreBenefits[market]}
              useNextLink={true}
            >
              {AT_LINK_TEXT[locale].exploreBenefits}
            </Link>
            {isCanada && (
              <Link
                data-testid='join-rewards'
                className='leading-5 text-[#76787b] hover:underline'
                target='_blank'
                to={generateSecureUrl({
                  host,
                  path: '/my-account/sign-in?&mlink=55288,********,Footer_Join&clink=********',
                })}
                useNextLink={true}
              >
                {AT_LINK_TEXT[locale].joinATRewards}
              </Link>
            )}
            {!isCanada && (
              <>
                <Link
                  data-testid='pay-bill'
                  className='leading-5 text-[#76787b] hover:underline'
                  target='_blank'
                  to='https://athleta.barclaysus.com/payment'
                  useNextLink={true}
                >
                  {AT_LINK_TEXT[locale].payCard}
                </Link>
                <Link
                  data-testid='activate-card'
                  className='leading-5 text-[#76787b] hover:underline'
                  target='_self'
                  to='https://www.athleta.barclaysus.com/activate'
                  useNextLink={true}
                >
                  {AT_LINK_TEXT[locale].activateCard}
                </Link>
                <Link
                  data-testid='join-rewards'
                  className='leading-5 text-[#76787b] hover:underline'
                  target='_blank'
                  to={generateSecureUrl({
                    host,
                    path: '/my-account/sign-in?&mlink=55288,********,Footer_Join&clink=********',
                  })}
                  useNextLink={true}
                >
                  {AT_LINK_TEXT[locale].joinATRewardsComma}
                </Link>
                <Link
                  data-testid='apply-now'
                  className='leading-5 text-[#76787b] hover:underline'
                  target='_blank'
                  to={generateSecureUrl({
                    host,
                    path: '/my-account/sign-in?creditOffer=barclays&sitecode=ATSSUNIFTDE',
                  })}
                  useNextLink={true}
                >
                  {AT_LINK_TEXT[locale].orApplyAtCard}
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
