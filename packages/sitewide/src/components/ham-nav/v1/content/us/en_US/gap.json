{"sitewide": {"utilitylinks": {"type": "sitewide", "name": "UtilityLinks", "data": {"style": {"background": "#eee", "padding": ".5rem .5rem", "&:nth-child(3)": {"color": "#cc0000", "fontWeight": "bold"}}, "brandBarShortcutLinks": [{"link": "/customerService/info.do?cid=35433&mlink=5151,8562444,3&clink=8562444", "text": "Gift Card", "style": {"textTransform": "uppercase", "gridColumnStart": 3}}], "openInNewTab": true}}, "promorover": {"CIID": "28777956", "instanceName": "promorover_022822_noncard", "name": "AcquisitionRover", "type": "sitewide", "experimentRunning": false, "data": {"isVisible": {"small": false, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {}, "mobile": {}}, "style": {"animation": "_EMO_animation-ctnef7_@keyframes animation-ctnef7{\n  from { bottom: -100vh; }\n  to { bottom: 0 }\n}_EMO_ 2s ease", "bottom": "0", "margin": "0 2em 0.5em 0", "right": "0", "display": "inline-grid", "maxWidth": "200px", "position": "fixed", "zIndex": 700}, "dismissButton": {"alt": "dismiss<PERSON><PERSON><PERSON>", "srcUrl": "https://www.wip.prod.gaptecholapps.com/Asset_Archive/static/test/close.jpg"}, "loyaltyLogo": {"alt": "loyalty logo", "srcUrl": "https://www.gap.com/Asset_Archive/GPWeb/content/0028/669/369/assets/logo/Gap_logo_MOB_newV2.svg"}, "userGreeting": {"text": "Hi, ", "style": {"padding": "0.5rem 0", "backgroundColor": "black", "color": "white", "text-align": "center"}}, "offer": {"alt": "Contactless curbside pickup, quick & easy in-store pickup.", "href": "/browse/info.do?cid=1156830&amp;mlink=5151,1,<PERSON>er_CurbsidePlus", "hrefTarget": "_blank", "srcUrl": "https://www.wip.prod.gaptecholapps.com/Asset_Archive/static/test/marketing.jpg"}, "applyButton": {"alt": "apply now", "srcUrl": "https://www.wip.prod.gaptecholapps.com/Asset_Archive/static/test/applyNow.jpg"}}}, "sisterBrandsBar": {"type": "sitewide", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data": {"brandSiteData": {"gidBrandSites": {"1": {"link": "http://www.google.com", "unsecureUrl": "http://www.local.gidgol.com:3000", "secureUrl": "https://secure.www.local.gidgol.com:3000", "displayName": "gap.com", "brandDisplayName": "gap.com", "brandCode": "1", "brandAbbr": "GAP"}, "2": {"link": "http://brol.local.gidgol.com:3000?ssiteID=GAP", "unsecureUrl": "http://brol.local.gidgol.com:3000", "secureUrl": "https://secure.brol.local.gidgol.com:3000", "displayName": "bananarepublic.com", "brandDisplayName": "bananarepublic.com", "brandCode": "2", "brandAbbr": "BR"}, "3": {"link": "http://onol.local.gidgol.com:3000?ssiteID=GAP", "unsecureUrl": "http://onol.local.gidgol.com:3000", "secureUrl": "https://secure.onol.local.gidgol.com:3000", "displayName": "oldnavy.com", "brandDisplayName": "oldnavy.com", "brandCode": "3", "brandAbbr": "ON"}, "10": {"link": "http://atol.local.gidgol.com:3000?ssiteID=GAP", "unsecureUrl": "http://atol.local.gidgol.com:3000", "secureUrl": "https://secure.atol.local.gidgol.com:3000", "displayName": "Athleta.com", "brandDisplayName": "Athleta.com", "brandCode": "10", "brandAbbr": "AT"}, "36": {"link": "http://www.local.gidgol.com:3000?ssiteID=GAP", "unsecureUrl": "http://www.local.gidgol.com:3000", "secureUrl": "https://secure.www.local.gidgol.com:3000", "displayName": "hillcity.com", "brandDisplayName": "hillcity.com", "brandCode": "36", "brandAbbr": "HC"}}, "currentBrandCode": 1, "currentBrandSiteId": "GAP"}}}, "headline": {"instanceName": "headline-03-26", "type": "sitewide", "name": "Headline", "experimentRunning": false, "data": {"placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px", "backgroundColor": "#839CBB"}, "mobile": {"height": "0px"}}, "defaultHeight": "1px", "breakpoints": ["x-large"], "backgroundColor": "red", "text": [{"text": "AAA 50% off everything + extra 10% off AAA", "className": "mkt_bold", "inlineStyle": {"display": "inline", "color": "#000000", "lineHeight": "0", "fontSize": "1rem", "textTransform": "uppercase"}}], "buttons": [], "details": [{"text": "Details", "className": "wcd_headpromo__modalLink", "inlineStyle": {"fontSize": "9pt", "display": "inline", "margin": "0 auto 0 .25em", "textDecoration": "underline", "textTransform": "none", "cursor": "pointer", "textDecorationSkipInk": "auto", "color": "#000000"}, "linkToModal": true}], "accordionContent": [], "modalUrl": "/Asset_Archive/BRWeb/content/0018/703/006/assets/br_legal.html", "modalTitle": "", "modalCloseButtonAriaLabel": "Close", "headlineIntroAriaLabel": "Entering promotional banner", "modalIFrameHeight": "500px"}}, "animatedheadline": {"sitewide-edfs-small-ciid": "18881283", "name": "AnimatedHeadline", "type": "sitewide", "experimentRunning": false, "data": {"duration": 3000, "transitionSpeed": 1000, "useContainerFullHeight": false, "style": {"backgroundColor": "#FFFFFF"}, "components": [{"name": "TextHeadline", "type": "sitewide", "experimentRunning": true, "instanceName": "test01", "data": {"text": "placeholderForTest01", "style": {}}}, {"name": "TextHeadline", "type": "sitewide", "experimentRunning": true, "instanceName": "test02", "data": {"text": "placeholderForTest02", "style": {}}}, {"name": "TextHeadline", "type": "sitewide", "redpointExperimentRunning": true, "instanceName": "test03", "data": {"text": "placeholderForTest03", "style": {}}}, {"name": "TextHeadline", "type": "sitewide", "data": {"lazy": true, "text": "black friday: 10$ discount in all site", "defaultHeight": "1px", "style": {"mobile": {"height": "100%", "whiteSpace": "normal", "textAlign": "center", "fontSize": "1em", "color": "#122344", "textTransform": "uppercase", "width": "100%", "alignItems": "center", "display": "flex", "justifyContent": "center"}, "desktop": {"height": "100%", "whiteSpace": "normal", "textAlign": "center", "fontSize": "1em", "color": "#122344", "textTransform": "uppercase", "width": "100%", "alignItems": "center", "display": "flex", "justifyContent": "center"}}, "className": {"mobile": "", "desktop": ""}}}, {"type": "sitewide", "name": "Countdown", "data": {"style": {"height": "100%", "fontWeight": "700", "width": "100%", "margin": "0", "alignItems": "center", "display": "flex", "justifyContent": "center"}, "ongoingHeadline": {"type": "sitewide", "name": "TextHeadline", "data": {"text": "", "style": {"desktopAndMobile": {"display": "inline-block"}}}}, "endedHeadline": {"type": "sitewide", "name": "TextHeadline", "data": {"text": "", "style": {"desktopAndMobile": {"display": "inline-block"}}}}, "shouldDisplayDays": true, "endDate": "2021-09-18T16:46:59.628-04:00", "timeDisplayText": {"day": {"showAttribute": true, "showAtZero": true, "text": "Days", "textSingular": "Day"}, "hour": {"showAttribute": true, "showAtZero": true, "text": "Hrs", "textSingular": "Hr"}, "minute": {"showAttribute": true, "showAtZero": true, "text": "<PERSON>s", "textSingular": "Min"}, "second": {"showAttribute": true, "showAtZero": true, "text": "Secs", "textSingular": "Sec"}}}}], "options": {"isDesktopVisible": false}}, "placementConfiguration": {"home": {"include": ["secondary-headline"], "exclude": ["mobileemergencybanner"]}}}, "hamnavRedesignBanner": {"sitewide-edfs-large-ciid": "18881281", "type": "builtin", "name": "div", "data": {"props": {"style": {"fontFamily": "Helvetica, sans-serif", "display": "flex", "maxWidth": "480px", "width": "100%"}}, "components": [{"type": "builtin", "name": "a", "data": {"props": {"href": "/browse/info.do?cid=1157924&mlink=5058,18881281,warehouse_hamnav", "style": {"textDecoration": "none", "color": "black", "display": "inherit", "width": "100%"}}, "components": [{"type": "builtin", "name": "img", "data": {"props": {"src": "/Asset_Archive/BRWeb/content/0018/875/764/assets/DealShop_MobileCTAN.svg", "alt": "Deal Shop", "style": {"flex": "0 0 100%", "width": "100%"}}}}]}}]}}, "sitewide-footer-ciid": "18610914", "sitewide-footer-desc": "031620_footerUpdate", "footer": {"type": "sitewide", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components": [{"instanceName": "gap-footer", "name": "Footer", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "1217px", "large": "679px"}, "socialLinks": [{"to": "https://www.facebook.com/gap/", "text": "Follow on Facebook"}], "emailRegistration": {"disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"classes": "legal", "style": {}, "html": "<p><span class=\"asterisk\">*</span>Valid for first-time registrants only &amp; applies to <span>reg. price items only.</span> <a onclick=\"return contentItemLink(this,'','CS_Footer_PrivacyPolicy');\" href=\"https://corporate.gapinc.com/en-us/consumer-privacy-policy\">Privacy Policy</a></p>"}}}, "copyRights": {"rows": [[{"text": "© 2020 The Gap, Inc.", "style": {"fontSize": "11px"}}, {"text": "Privacy Policy", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy?mlink=5058,16926950,CS_Footer_PrivacyPolicy&clink=16926950", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "Do Not Sell My Info", "doNotSell": true, "style": {"fontSize": "11px"}}, {"text": "Interest Based Ads", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy#Interest-Based-Ad", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "Your California Privacy Rights", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy?mlink=5058,16926950,CS_Footer_Returns_CA_Policy&clink=16926950#Your-California-Privacy-Rights", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "California Transparency in Supply Chains Act", "to": "https://www.gapinc.com/content/gapinc/html/sustainability/ca-transparency-insupplychainsact.html", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "Terms of Use", "to": "/customerService/info.do?cid=6754", "style": {"fontSize": "11px"}}, {"text": "Careers", "to": "https://jobs.gapinc.com/gap-home", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "Sustainability", "to": "https://www.gapincsustainability.com/", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}, {"text": "About Gap Inc.", "to": "http://www.gapinc.com/content/gapinc/html/aboutus.html", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}], [{"text": "Americans with Disabilities Act", "to": "/customerService/info.do?cid=1005563", "style": {"fontSize": "11px"}}, {"text": "Gap Inc. Policies", "to": "http://www.gapincsustainability.com/policies", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>", "style": {"fontSize": "11px"}}]]}}}]}, "sitewide-universalBar-ciid": "18211208", "sitewide-universalBar-desc": "031620_edfsFS25", "edfssmall": {"instanceName": "edfs-header-small", "name": "MktEdfsSmall", "type": "sitewide", "experimentRunning": false, "data": {"lazy": false, "defaultData": {"textStrong": "Free Shipping ", "text": "on Orders of $25 or More.", "detailsLink": "Details"}, "modalUrl": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-GP-FS25.html?v=0", "modalTitle": "Everyday Free Shipping", "modalCloseButtonAriaLabel": "Close Pop-Up"}}, "edfslarge": {"instanceName": "edfs-header-large", "name": "MktEdfsLarge", "type": "sitewide", "experimentRunning": false, "data": {"lazy": false, "defaultData": {"text": "Free Shipping on Orders of $25 or More.", "detailsLink": "Details"}, "modalUrl": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-GP-FS25.html?v=0", "modalTitle": "Everyday Free Shipping", "modalCloseButtonAriaLabel": "Close Pop-Up"}}, "sitewide-ciid": "18659411", "logo": {"type": "sitewide", "name": "Logo", "isSquare": true}, "fullbleed": {"type": "sitewide", "name": "FullBleedHeader", "data": {"options": {"isDesktopVisible": true, "isMobileVisible": true}, "desktop": {"contrast": "light", "style": {}}, "mobile": {"contrast": "light", "style": {}}}}, "appsflyer-smart-banner": {"data": {"meta": {"excludePageTypes": ["product"]}}}, "hamnav": {"type": "sitewide", "name": "HamburgerNav", "data": {"activeDivisions": [{"cid": "c48620", "name": "Go to Gap Factory", "link": "gapfactory.com", "icon": {"altText": "O.N.L.Y.", "position": "left", "src": "https://www.gap.com/Asset_Archive/AllBrands/assets/DEC/on-header-only.svg", "styles": {"height": "11px", "width": "40px", "paddingRight": "11px"}}}, {"name": "NEW", "cid": "1086624", "icon": {"altText": "O.N.L.Y.", "position": "top", "src": "https://www.gap.com/Asset_Archive/AllBrands/assets/DEC/on-header-only.svg", "styles": {"height": "11px", "width": "40px", "marginBottom": "2px", "paddingRight": "11px"}}, "customStyles": {"color": "red", "fontWeight": "bold", "font-size": 18, " button": {"backgroundColor": "yellow", "fontSize": 20, " div": {"fontSize": 25}}}, "divisionHeaders": [{"name": "Featured Shops", "cid": "1164542", "isAccordionOpened": false, "icon": {"altText": "O.N.L.Y. division header", "position": "right", "src": "https://www.gap.com/Asset_Archive/AllBrands/assets/DEC/on-header-only.svg", "styles": {"height": "11px", "width": "40px", "marginLeft": "11px"}}, "customStyles": {"backgroundColor": "grey", "color": "purple", "fontSize": 30, " div": {"fontSize": 40}, " a": {}}, "categories": [{"cid": "1173983", "name": "This is Generation not too Good", "link": "/browse/category.do?cid=1173983", "icon": {"altText": "O.N.L.Y.", "position": "right", "src": "https://www.gap.com/Asset_Archive/AllBrands/assets/DEC/on-header-only.svg", "styles": {"height": "11px", "width": "40px", "marginLeft": "11px"}}, "customStyles": {"backgroundColor": "blue", "fontWeight": "bold", "color": "green", "fontSize": 30}}, {"cid": "1173983", "name": "This is Family Logo Shop", "link": "/browse/category.do?cid=1165853"}]}, {"name": "SHOP NEW ARRIVALS", "cid": "1139272", "isAccordionOpened": true, "categories": [{"cid": "1139383", "name": "Toddler Girls", "link": "/browse/category.do?cid=63895#pageId=0&department=48"}, {"cid": "1139380", "name": "Girls", "link": "/browse/category.do?cid=63863#pageId=0&department=165"}, {"cid": "1139384", "name": "Toddler Boys", "link": "/browse/category.do?cid=1016138#pageId=0&department=165"}, {"cid": "1139382", "name": "Boys", "link": "/browse/category.do?cid=63896#pageId=0&department=16"}, {"name": "Women", "cid": "1139377", "link": "/browse/category.do?cid=8792&nav=hamnav%3ANew%20%2B%20Now%3AShop%20New%20Arrivals%3AWomen"}]}]}, "5997", "5646", "1156863", "1188985"], "topBanner": {"altText": "WCD - updated image url top banner", "imgSrc": "WCD - updated image url top banner", "link": "/browse/category.do?cid=1189071&nav=hamnav%3ANew%20%2B%20Now%3AFeatured%20Shops%3AArtist%20Series%20Tee%20Shop"}, "bottomBanner": {"altText": "WCD - updated image url bottom banner", "imgSrc": "WCD - updated image url bottom banner"}, "exclusionIds": ["49708", "49709"]}}, "topnav": {"type": "sitewide", "name": "MegaNav", "data": {"isNavSticky": true, "activeDivisions": [{"name": "New Arrivals", "divisionId": ["1086624"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='8792' href='/browse/info.do?cid=1152274&mlink=18659411,topnav_w_lookbook,visnav&clink=18659411' class='catnav--item--link' style='max-width: 265px;'><img src='/Asset_Archive/GPWeb/content/0018/659/411/assets/WOMEN_topnav_new_arrivals.jpg' alt='shop womens lookbook'><img style='left:0;' src='/Asset_Archive/GPWeb/content/0018/659/411/assets/lookbook_w_topnav_new_arrivals.svg' alt='shop womens lookbook'></a></li></ul></li>"], ["1139272"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='63896' href='/browse/info.do?cid=1152273&mlink=18659411,topnav_m_lookbook,visnav&clink=18659411' class='catnav--item--link' style='max-width: 265px;'><img src='/Asset_Archive/GPWeb/content/0018/659/411/assets/MEN_topnav_new_arrivals.jpg' alt='shop mens lookbook'><img style='left:0;' src='/Asset_Archive/GPWeb/content/0018/659/411/assets/lookbook_m_topnav_new_arrivals.svg' alt='shop mens lookbook'></a></li></ul></li>"]], "numberOfColumns": {"1139272": 2}, "exclusionIds": [], "customStyles": {}}, {"name": "<PERSON><PERSON>", "divisionId": ["1077403"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1137373' href='/browse/category.do?cid=5664#department=136&style=1137550&mlink=18659411,Meganav_W_CheekyStraightJeans&clink=18659411' class='catnav--item--link' style='max-width: 265px;'><img src='/Asset_Archive/GPWeb/content/0018/621/061/assets/G28775_topnav_denim_w.jpg' alt='shop womens denim'><img style='left:0;' src='/Asset_Archive/GPWeb/content/0018/621/061/assets/G28775_topnav_denim_w.svg' alt='shop denim though the decades'></a></li></ul></li>"], ["1135249"], ["1137310"], ["1137320", "1137322"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1137372' href='browse/category.do?cid=6998#department=75&style=96537&mlink=18659411,<PERSON><PERSON>_M_TaperJeans&clink=18659411' class='catnav--item--link' style='max-width: 265px;'><img src='/Asset_Archive/GPWeb/content/0018/621/061/assets/G28775_topnav_denim_m.jpg' alt='shop denim'><img style='left:0;' src='/Asset_Archive/GPWeb/content/0018/621/061/assets/G28775_topnav_denim_m.svg' alt='shop mens denim guide'></a></li></ul></li>"]], "exclusionIds": [], "customStyles": {}}, {"name": "Women", "divisionId": ["/browse/division.do?cid=5643&mlink=39813,17613292,Megnav_Women&clink=17613292", "5646"], "megaNavOrder": [["1131702"], ["1042481"], ["1131696", "5903"], ["1122595", "1131698"]], "numberOfColumns": {"1042481": 2}, "exclusionIds": [], "customStyles": {"65179": {"colorScheme": "sale"}, "1015684": {"colorScheme": "sale"}}}, {"name": "Maternity", "divisionId": ["/browse/division.do?cid=5997&mlink=39813,17613292,<PERSON>av_Maternity&clink=17613292", "5997"], "megaNavOrder": [["1149538"], ["1042513"], ["1014415"], ["1122765"]], "numberOfColumns": {"1042513": 2}, "exclusionIds": [], "customStyles": {"65302": {"colorScheme": "sale"}, "1146678": {"colorScheme": "sale"}}}, {"name": "Men", "divisionId": ["/browse/division.do?cid=5063&mlink=39813,17613292,Megnav_Men&clink=17613292", "5065"], "megaNavOrder": [[], ["1149531"], ["1042515"], ["1122755", "1076121"]], "numberOfColumns": {"1042515": 2}, "exclusionIds": [], "customStyles": {"65289": {"colorScheme": "sale"}, "1008073": {"colorScheme": "sale"}}}, {"name": "Girls", "divisionId": ["/browse/division.do?cid=1137865&mlink=39813,17613292,Megnav_Girls&clink=17613292", "6256"], "megaNavOrder": [[], ["1056088"], ["1042516"], ["1122748", "6258"]], "numberOfColumns": {"1042516": 2}, "exclusionIds": [], "customStyles": {"65194": {"colorScheme": "sale"}, "1137652": {"colorScheme": "sale"}}}, {"name": "Boys", "divisionId": ["/browse/division.do?cid=1137867&mlink=39813,17613292,Megnav_Boys&clink=17613292", "6172"], "megaNavOrder": [[], ["1056087"], ["1042518"], ["1122747", "6174"]], "numberOfColumns": {"1042518": 2}, "exclusionIds": [], "customStyles": {"65217": {"colorScheme": "sale"}, "1137659": {"colorScheme": "sale"}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=1137868&mlink=39813,17613292,<PERSON>nav_<PERSON><PERSON>&clink=17613292", "6413"], "megaNavOrder": [["1149845"], ["1016135"], ["1016083"], ["1048209", "1067853"]], "exclusionIds": [], "customStyles": {"65236": {"colorScheme": "sale"}, "65263": {"inlineStyle": {"color": "#d0011b"}}, "1151403": {"colorScheme": "sale"}}}, {"name": "Baby", "divisionId": ["/browse/division.do?cid=1137869&mlink=39813,17613292,Megnav_Baby&clink=17613292", "6487"], "megaNavOrder": [["1149847"], ["95461"], ["95574"], ["1149848"], ["1048187", "1067854"]], "exclusionIds": [], "customStyles": {"65208": {"inlineStyle": {"color": "#d0011b"}}, "65261": {"inlineStyle": {"color": "#d0011b"}}, "1151388": {"colorScheme": "sale"}}}, {"name": "Gap For Good", "divisionId": ["/browse/info.do?cid=1086537&mlink=18426980,Meganav_GapForGood&clink=18426980", "1147558"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1147558' href='/browse/info.do?cid=1086537&mlink=18426980,Meganav_GapForGood&clink=18426980' class='catnav--item--link' style='max-width: 265px;'><img src='/Asset_Archive/GPWeb/content/0018/426/980/assets/topnav_G4G.jpg' alt='gap for good'><img style='left:0;' src='/Asset_Archive/GPWeb/content/0018/310/953/assets/topnav_G4G.svg' alt='gap for good'></a></li></ul></li>"], ["1150547"], ["1150546"]], "exclusionIds": [], "customStyles": {}}, {"name": "Gap Factory", "divisionId": ["https://www.gapfactory.com/?tid=gfsv000000"]}]}, "abtest": {"isNavSticky": true, "activeDivisions": [{"name": "Gap Factory", "divisionId": ["https://www.gapfactory.com/?tid=gfsv000000"]}, {"name": "New Arrivals", "divisionId": ["1086624"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='8792' href='/browse/info.do?cid=1152274&mlink=18659411,topnav_w_lookbook,visnav&clink=18659411' class='catnav--item--link' style='max-width: 265px;'><img src='/Asset_Archive/GPWeb/content/0018/659/411/assets/WOMEN_topnav_new_arrivals.jpg' alt='shop womens lookbook'><img style='left:0;' src='/Asset_Archive/GPWeb/content/0018/659/411/assets/lookbook_w_topnav_new_arrivals.svg' alt='shop womens lookbook'></a></li></ul></li>"], ["1139272"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='63896' href='/browse/info.do?cid=1152273&mlink=18659411,topnav_m_lookbook,visnav&clink=18659411' class='catnav--item--link' style='max-width: 265px;'><img src='/Asset_Archive/GPWeb/content/0018/659/411/assets/MEN_topnav_new_arrivals.jpg' alt='shop mens lookbook'><img style='left:0;' src='/Asset_Archive/GPWeb/content/0018/659/411/assets/lookbook_m_topnav_new_arrivals.svg' alt='shop mens lookbook'></a></li></ul></li>"]], "numberOfColumns": {"1139272": 2}, "exclusionIds": [], "customStyles": {}}, {"name": "<PERSON><PERSON>", "divisionId": ["1077403"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1137373' href='/browse/category.do?cid=5664#department=136&style=1137550&mlink=18659411,Meganav_W_CheekyStraightJeans&clink=18659411' class='catnav--item--link' style='max-width: 265px;'><img src='/Asset_Archive/GPWeb/content/0018/621/061/assets/G28775_topnav_denim_w.jpg' alt='shop womens denim'><img style='left:0;' src='/Asset_Archive/GPWeb/content/0018/621/061/assets/G28775_topnav_denim_w.svg' alt='shop denim though the decades'></a></li></ul></li>"], ["1135249"], ["1137310"], ["1137320", "1137322"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1137372' href='browse/category.do?cid=6998#department=75&style=96537&mlink=18659411,<PERSON><PERSON>_M_TaperJeans&clink=18659411' class='catnav--item--link' style='max-width: 265px;'><img src='/Asset_Archive/GPWeb/content/0018/621/061/assets/G28775_topnav_denim_m.jpg' alt='shop denim'><img style='left:0;' src='/Asset_Archive/GPWeb/content/0018/621/061/assets/G28775_topnav_denim_m.svg' alt='shop mens denim guide'></a></li></ul></li>"]], "exclusionIds": [], "customStyles": {}}, {"name": "Women", "divisionId": ["/browse/division.do?cid=5643&mlink=39813,17613292,Megnav_Women&clink=17613292", "5646"], "megaNavOrder": [["1131702"], ["1042481"], ["1131696", "5903"], ["1122595", "1131698"]], "numberOfColumns": {"1042481": 2}, "exclusionIds": [], "customStyles": {"65179": {"colorScheme": "sale"}, "1015684": {"colorScheme": "sale"}}}, {"name": "Gap For Good", "divisionId": ["/browse/info.do?cid=1086537&mlink=18426980,Meganav_GapForGood&clink=18426980", "1147558"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1147558' href='/browse/info.do?cid=1086537&mlink=18426980,Meganav_GapForGood&clink=18426980' class='catnav--item--link' style='max-width: 265px;'><img src='/Asset_Archive/GPWeb/content/0018/426/980/assets/topnav_G4G.jpg' alt='gap for good'><img style='left:0;' src='/Asset_Archive/GPWeb/content/0018/310/953/assets/topnav_G4G.svg' alt='gap for good'></a></li></ul></li>"], ["1150547"], ["1150546"]], "exclusionIds": [], "customStyles": {}}]}}, "search": {"type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["Shorts", "Dresses", "Tanks", "Skorts", "Masks"]}}, "desktopemergencybanner": {"type": "builtin", "name": "div", "experimentRunning": true, "instanceName": "dpg_emergency_banner_desk", "useGreyLoadingEffect": false, "desktop": {"height": "0"}, "mobile": {"height": 0}, "data": {"lazy": false, "defaultHeight": {"large": "0px", "small": "0px"}, "isVisible": {"large": true, "small": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"width": 0, "height": 0}}}}, "mobileemergencybanner": {"type": "builtin", "name": "div", "experimentRunning": true, "instanceName": "dpg_emergency_banner_mob", "useGreyLoadingEffect": false, "desktop": {"height": "0"}, "mobile": {"height": 0}, "data": {"lazy": false, "defaultHeight": {"large": "0px", "small": "0px"}, "isVisible": {"large": true, "small": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"width": 0, "height": 0}}}}, "promodrawer": {"name": "PromoDrawerComponentV2", "type": "sitewide", "sitewide-promodrawer-ciid": "********", "sitewide-promodrawer-desc": "US_PD_Update_0930", "instanceName": "promoDrawer-********", "experimentRunning": false, "data": {"shouldWaitForOptimizely": false, "buildInfo": ["********", "GP"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "info", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "Profile"], "anchor": "bottom", "collapseOnScroll": true}, "autoFire": "load", "disabledAutoFirePageTypes": ["category"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1127938#pageId=0&department=136&mlink=5058,PD_Tile1\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GPWeb/content/0019/334/814/assets/093020_PD1_US.png\" alt=\"online only the feel good stuff event 50% off tees 40% off jeans and pants 30% off sweats\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "PRICES AS MARKED. SELECT STYLES.", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "704373", "genericCode": ""}, "promoId": "kfd2vdii"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1127938#pageId=0&department=136&mlink=5058,PD_Tile2\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GPWeb/content/0019/334/814/assets/093020_PD2_US.png\" alt=\"online only 30 to 50% off sitewide\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "PRICES AS MARKED", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "704373", "genericCode": ""}, "promoId": "kfd2yjif"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=65179#pageId=0&department=136&mlink=5058,PD_Tile3\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GPWeb/content/0019/334/814/assets/093020_PD3_US.png\" alt=\"online only extra 40% off sale styles code MORE\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "tap", "overlay": "Code will be applied at bag", "defaultMessage": "tap to apply", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "704353", "genericCode": "MORE"}, "promoId": "kfd30v9j"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "style": {"mobile": {"fontSize": ".75em"}, "desktop": {"fontSize": ".75em"}}, "text": "{--! headerText !--}"}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}"}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "iconAltText": "Open icon", "linkWithModalDisplayStyle": "none", "subHeaderText": "(3 available)"}, "closedState": {"headerText": "30–50% off feel-good stuff + more", "subHeaderText": "", "iconAltText": "Closed icon"}}, "pd_id": "pdid_kfd2uqpe"}}, "popup": {"type": "builtin", "name": "div", "experimentRunning": true, "instanceName": "dpg-brightpopup", "useGreyLoadingEffect": false, "desktop": {"width": 0, "height": 0}, "mobile": {"width": 0, "height": 0}, "data": {"placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"width": 0, "height": 0}, "mobile": {"width": 0, "height": 0}}}}}, "home": {"instanceName": "HomePage_032520", "ciid": "18538892", "type": "home", "name": "HomeMultiSimple", "components": [{"instanceName": "dpg-banner", "instanceDesc": "DPG-placeholder-1", "type": "builtin", "name": "div", "experimentRunning": true, "useGreyLoadingEffect": false, "desktop": {"height": "0"}, "mobile": {"height": 0}, "data": {"lazy": false, "defaultHeight": {"large": "0px", "small": "0px"}, "isVisible": {"large": true, "small": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"width": 0, "height": 0}}}}, {"instanceName": "brand-khaki", "instanceDesc": "032520_brand-khaki", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": false, "defaultHeight": {"small": "500px", "large": "350px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto 3.25rem", "maxWidth": "640px"}, "desktop": {"margin": "10px auto 4.5rem", "maxWidth": "1920px", "width": "100%"}}, "data": {"containerStyle": {"mobile": {}, "desktop": {"maxWidth": "1920px"}}, "background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/brand/G28969_GOL_SPRING_KHAKIS_HP_MOB.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/brand/G28969_GOL_SPRING_KHAKIS_HP_DESK_v1.jpg", "altText": "Relearn the khaki"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/brand/G28969_GOL_SPRING_KHAKIS_HP_MOB.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/brand/G28969_GOL_SPRING_KHAKIS_HP_DESK.svg", "altText": "Re<PERSON>n the khaki", "link": {"url": "/browse/category.do?cid=1153129#pageId=0&mlink=5058,18538892,HP_BRAND_Adult_G28969_IMAGE"}}, "linksContainerStyle": {"mobile": {"paddingTop": "0", "paddingLeft": "0", "paddingRight": "0"}, "desktop": {"left": "50%", "transform": "translateX(-50%)", "padding": "0", "top": "57.36%"}}, "links": {"style": {"mobile": {"color": "#122344", "fontSize": "1rem", "fontWeight": "700", "letterSpacing": "1px", "textAlign": "center", "backgroundColor": "#FFFFFF"}, "desktop": {"backgroundColor": "#FFFFFF", "border": "2px solid #fff", "color": "#122344", "fontSize": "12px", "letterSpacing": "1px", "padding": "0.825rem 1.5rem"}}, "content": [{"text": "Women", "url": "/browse/category.do?cid=1153129#pageId=0&mlink=5058,18538892,HP_BRAND_Adult_G28969_CTA"}, {"text": "Men", "url": "/browse/category.do?cid=1152975#pageId=0&mlink=5058,18538892,HP_BRAND_Adult_G28969_CTA"}, {"text": "Watch the Video", "url": "/"}]}, "detailsLink": {"content": {"modalSize": "max", "linkText": "Watch the Video", "normalTextColor": "transparent", "className": "", "linkTextColor": "transparent", "url": "/Asset_Archive/GPWeb/content/0018/538/892/assets/khaki_video.html?v=14", "modalCloseButtonAriaLabel": "Close Video Pop-up", "closeModalAriaLabel": "Press button to close"}, "style": {"desktop": {"overflow": "hidden", "position": "absolute", "left": "53%", "width": "170px", "fontSize": "1000px", "height": "12%", "color": "black", "transform": "translateX(0%)", "top": "57%", "cursor": "pointer", "display": "block"}, "mobile": {"position": "absolute", "bottom": "0", "left": "0", "width": "100%", "height": "10.5%", "overflow": "hidden", "fontSize": "50px", "color": "transparent"}}, "modalIframeStyle": {"desktop": {"height": "37.5rem"}}}}}]}}}}, {"instanceName": "certona_hp2", "instanceDesc": "041420_RatingsReviewsABtest", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "275px", "large": "260px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 275, "margin": "0 auto 3.25rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 260, "margin": "0 auto 4.5rem", "maxWidth": "1920px", "width": "100%"}}, "desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "Recommendations", "type": "home", "tileStyle": {"mobile": {"boxSizing": "border-box", "margin": "0 auto 2rem", "maxWidth": "640px", "paddingLeft": "0rem", "paddingRight": "0.5rem", "width": "100%"}, "desktop": {"boxSizing": "border-box", "margin": "0 auto 4.5rem", "maxWidth": "1280px", "paddingLeft": "0.5rem", "paddingRight": "0.5rem", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome1_rr", "displayTitle": false, "certonaTitle": {"title": "", "style": {"mobile": {"color": "#122344", "display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "fontSize": "1.5rem", "fontWeight": "400", "marginBottom": "0.5rem", "paddingLeft": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}, "desktop": {"paddingLeft": "0.5rem", "color": "#122344", "display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "fontSize": "1.5rem", "fontWeight": "400", "marginBottom": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}}}, "layout": "carousel", "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "priceFlag": false, "ratings": {"showRating": false, "showReviewCount": false, "minRating": 0, "reviewCountColor": "#ccc"}, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0017/748/073/assets/certona_arrows/gp_certona_gfol-style-arrow3.png", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0017/748/073/assets/certona_arrows/gp_certona_gfol-style_right.png", "nextArrowAlt": "Next", "productTextStyles": {"productTitle": {"style": {"color": "#666666", "textAlign": "left", "fontSize": "0.75rem"}}, "productPrice": {"style": {"color": "#000000", "float": "left", "fontSize": "11px"}}, "productSalePrice": {"style": {"color": "red", "float": "left", "fontSize": "11px"}}, "size": {"width": "90%", "height": "150px"}}, "productCardStyles": {"style": {}}, "gridLayout": {}}}]}}}}, {"instanceName": "optly-placeholder-1", "instanceDesc": "032520_UNREC1", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "500px", "large": "250px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 437, "margin": "0 auto 3.25rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 4.5rem", "maxWidth": "1920px", "width": "100%"}}, "desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto 3.25rem", "maxWidth": "640px"}, "desktop": {"margin": "0rem auto 4.5rem", "maxWidth": "1920px", "width": "100%"}}, "data": {"containerStyle": {"mobile": {}, "desktop": {"maxWidth": "1920px"}}, "background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/unrec_1/G29084_KTB_NewArrivalsSpringFlow3_HPMain_MOB.jpg?v=2", "largeImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/unrec_1/G29084_KTB_NewArrivalsSpringFlow3_HPMain_DESK.jpg?v=2", "altText": "Nice day for new arrivals"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/unrec_1/G29084_KTB_NewArrivalsSpringFlow3_HPMain_MOB.svg?v=1", "largeImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/unrec_1/G29084_KTB_NewArrivalsSpringFlow3_HPMain_DESK.svg?v=1", "altText": "Tees, denim, and color just in.", "link": {"url": "/browse/category.do?cid=63896#pageId=0&mlink=5058,18538892,HP_HERO1_KTB_G29084_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"transform": "translateX(-50%)", "left": "19%", "padding": "0", "top": "81%", "zIndex": "8"}}, "links": {"type": "dropdown", "style": {"mobile": {"padding": "1rem 0"}, "desktop": {"border": "none", "padding": "6px 12px 8px"}}, "content": [{"heading": {"text": "New Arrivals"}, "submenu": [{"text": "Girls", "href": "/browse/category.do?cid=63895#pageId=0&mlink=5058,18538892,HP_HERO1_KTB_G29084_CTA"}, {"text": "Boys", "href": "/browse/category.do?cid=63896#pageId=0&mlink=5058,18538892,HP_HERO1_KTB_G29084_CTA"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=63863#pageId=0&mlink=5058,18538892,HP_HERO1_KTB_G29084_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1016138#pageId=0&mlink=5058,18538892,HP_HERO1_KTB_G29084_CTA"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=14249#pageId=0&mlink=5058,18538892,HP_HERO1_KTB_G29084_CTA"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=95575#pageId=0&mlink=5058,18538892,HP_HERO1_KTB_G29084_CTA"}]}]}}}]}}}}, {"instanceName": "optly-placeholder-2", "instanceDesc": "032520_UNREC2", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "500px", "large": "285px"}, "placeholderSettings": {"useGreyLoadingEffect": false, "mobile": {"backgroundColor": "#ccc", "height": 500, "margin": "0 auto 3.25rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 630, "margin": "0 auto 4.5rem", "maxWidth": "1920px", "width": "100%"}}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 auto", "maxWidth": "1920px"}, "components": [{"instanceDesc": "032520_KTB_sub1", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto 3.25rem", "maxWidth": "640px", "width": "100%"}, "desktop": {"boxSizing": "border-box", "marginBottom": "4.5rem", "padding": "0 1% 0 2%", "width": "50%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "SVGOverlay", "type": "sitewide", "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/unrec_2/sub_1/G28521_KTB_ShortsShop_HPSub_DESK.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/unrec_2/sub_1/G28521_KTB_ShortsShop_HPSub_DESK.jpg", "altText": "New open shorts shop"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/unrec_2/sub_1/G28521_KTB_ShortsShop_HPSub_DESK.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/unrec_2/sub_1/G28521_KTB_ShortsShop_HPSub_DESK.jpg", "altText": "New open shorts shop", "link": {"url": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,18473896,HP_HERO2_KTB_G28521_IMAGE"}}}}, {"name": "SVGOverlay", "type": "sitewide", "data": {"containerStyle": {"desktop": {"width": "100%"}}, "background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/unrec_2/sub_1/G28521_KTB_ShortsShop_HPSub_MOB.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/unrec_2/sub_1/G28521_KTB_ShortsShop_HPSub_DESK.svg", "altText": "Short Sleeve Season"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/unrec_2/sub_1/G28521_KTB_ShortsShop_HPSub_MOB.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/538/892/assets/unrec_2/sub_1/G28521_KTB_ShortsShop_HPSub_DESK.svg", "altText": "Short Sleeve Season", "link": {"url": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,18473896,HP_HERO2_KTB_G28521_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"right": "0", "padding": "0", "top": "4px", "zIndex": "8"}}, "links": {"type": "dropdown", "style": {"mobile": {"color": "#122344", "fontSize": "1rem", "fontWeight": "700", "letterSpacing": "1px", "textAlign": "center", "backgroundColor": "#FFFFFF"}, "desktop": {"border": "none", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "padding": "calc(1px + (14px - 1px) * ((100vw - 768px)/(1600px - 768px)))", "paddingRight": "0", "textAlign": "right"}}, "content": [{"heading": {"text": "See What's New"}, "submenu": [{"text": "Girls", "href": "/browse/category.do?cid=14403#pageId=0&department=48&mlink=5058,18473896,HP_HERO2_KTB_G28521_CTA"}, {"text": "Boys", "href": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,18473896,HP_HERO2_KTB_G28521_CTA"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1121815#pageId=0&department=165&mlink=5058,18473896,HP_HERO2_KTB_G28521_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1121839#pageId=0&department=165&mlink=5058,18473896,HP_HERO2_KTB_G28521_CTA"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1102200#pageId=0&department=166&mlink=5058,18473896,HP_HERO2_KTB_G28521_CTA"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1102201#pageId=0&department=166&mlink=5058,18473896,HP_HERO2_KTB_G28521_CTA"}]}]}}}]}}}}, {"instanceDesc": "032520_MAN_sub2", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"margin": "0 auto 3.25rem", "maxWidth": "640px", "width": "100%"}, "desktop": {"boxSizing": "border-box", "marginBottom": "4.5rem", "padding": "0 2% 0 1%", "width": "50%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative", "width": "100%", "height": "100%"}, "components": [{"name": "SVGOverlay", "type": "sitewide", "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/576/533/assets/PZ/men/2/sub_2/G28572_CR0218_MEN_VintageSoftPolo_HPSub.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/576/533/assets/PZ/men/2/sub_2/G28572_CR0218_MEN_VintageSoftPolo_HPSub.jpg", "altText": "It's Back The Vintage Soft Polo."}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/576/533/assets/PZ/men/2/sub_2/G28572_CR0218_MEN_VintageSoftPolo_HPSub.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/576/533/assets/PZ/men/2/sub_2/G28572_CR0218_MEN_VintageSoftPolo_HPSub.jpg", "altText": "It's Back The Vintage Soft Polo.", "link": {"url": "/browse/category.do?cid=83056#pageId=0&department=75&style=1129514&mlink=5058,18538892,HP_HERO3_M_G28572_IMAGE"}}}}, {"name": "SVGOverlay", "type": "sitewide", "data": {"containerStyle": {"desktop": {"width": "100%"}}, "background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/576/533/assets/PZ/men/2/sub_2/G28572_CR0218_MEN_VintageSoftPolo_HPSub_MOB.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/576/533/assets/PZ/men/2/sub_2/G28572_CR0218_MEN_VintageSoftPolo_HPSub_DESK.svg", "altText": "It's Back The Vintage Soft Polo"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/576/533/assets/PZ/men/2/sub_2/G28572_CR0218_MEN_VintageSoftPolo_HPSub_MOB.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/576/533/assets/PZ/men/2/sub_2/G28572_CR0218_MEN_VintageSoftPolo_HPSub_DESK.svg", "altText": "It's Back The Vintage Soft Polo", "link": {"url": "/browse/category.do?cid=83056#pageId=0&department=75&style=1129514&mlink=5058,18538892,HP_HERO3_M_G28572_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"right": "0px", "padding": "0", "top": "4px", "zIndex": "8"}}, "links": {"style": {"mobile": {"color": "#122344", "fontSize": "1rem", "fontWeight": "700", "letterSpacing": "1px", "textAlign": "center", "backgroundColor": "#FFFFFF"}, "desktop": {"color": "#122344", "fontSize": "12px", "letterSpacing": "1px", "fontWeight": "700", "padding": "calc(1px + (14px - 1px) * ((100vw - 768px)/(1600px - 768px)))"}}, "content": [{"text": "Grab Every Color", "url": "/browse/category.do?cid=83056#pageId=0&department=75&style=1129514&mlink=5058,18576533,HP_HERO3_M_G28572_CTA"}]}}}]}}}}]}}}}, {"instanceName": "optly-vcn", "instanceDesc": "CatNav_012819", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"boxSizing": "border-box", "display": "flex", "flexFlow": "row wrap", "justifyContent": "center", "margin": "0 auto 2rem", "maxWidth": "1185px", "width": "100%"}, "components": [{"instanceDesc": "8grid-item1", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_1.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_1.jpg", "altText": "Women"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_1.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_1.jpg", "altText": "Women", "link": {"url": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,18336122,HP_VCN_1_W_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "8"}}, "links": {"type": "dropdown", "style": {"mobile": {"color": "#122344", "borderColor": "#ccc", "borderTopWidth": "0", "fontSize": "12px", "fontWeight": "700", "lineHeight": "20px", "padding": "6px 0 7px", "textAlign": "center"}, "desktop": {"border": "none", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "paddingTop": "7px", "textAlign": "center"}}, "content": [{"heading": {"text": "Women"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,18336122,HP_VCN_1_W_G28771_CTA"}, {"text": "Body", "href": "/browse/category.do?cid=1140272#pageId=0&department=136&mlink=5058,18336122,HP_VCN_1_Body_G28771_CTA"}, {"text": "GapFit", "href": "/browse/category.do?cid=1117374#pageId=0&department=136&mlink=5058,18336122,HP_VCN_1_Fit_G28771_CTA"}, {"text": "Maternity", "href": "/browse/category.do?cid=11437#pageId=0&department=136&mlink=5058,18336122,HP_VCN_1_Mat_G28771_CTA"}]}]}}}, {"instanceDesc": "8grid-item2", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_2.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_2.jpg", "altText": "Men"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_2.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_2.jpg", "altText": "Men", "link": {"url": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,18336122,HP_VCN_2_M_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "7"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Men", "href": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,18336122,HP_VCN_2_M_G28771_CTA"}]}}}, {"instanceDesc": "8grid-item3", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_3.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_3.jpg", "altText": "Girls"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_3.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_3.jpg", "altText": "Girls", "link": {"url": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,18336122,HP_VCN_3_G_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "6"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Girls", "href": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,18336122,HP_VCN_3_G_G28771_CTA"}]}}}, {"instanceDesc": "8grid-item4", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_4.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_4.jpg", "altText": "Boys"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_4.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_4.jpg", "altText": "Boys", "link": {"url": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,18336122,HP_VCN_4_B_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "5"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Boys", "href": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,18336122,HP_VCN_4_B_G28771_CTA"}]}}}, {"instanceDesc": "8grid-item5", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_5.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_5.jpg", "altText": "<PERSON>ler Girl"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_5.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_5.jpg", "altText": "<PERSON>ler Girl", "link": {"url": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,18336122,HP_VCN_5_TG_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "4"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,18336122,HP_VCN_5_TG_G28771_CTA"}]}}}, {"instanceDesc": "8grid-item6", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_6.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_6.jpg", "altText": "<PERSON><PERSON>"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_6.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_6.jpg", "altText": "<PERSON><PERSON>", "link": {"url": "/browse/category.do?cid=1016138#pageId=0&department=165&mlink=5058,18336122,HP_VCN_6_TB_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "3"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1016138#pageId=0&department=165&mlink=5058,18336122,HP_VCN_6_TB_G28771_CTA"}]}}}, {"instanceDesc": "8grid-item7", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_7.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_7.jpg", "altText": "Baby Girl"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_7.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_7.jpg", "altText": "Baby Girl", "link": {"url": "/browse/category.do?cid=14249#pageId=0&department=166&mlink=5058,18336122,HP_VCN_7_BG_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "2"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Baby Girl", "href": "/browse/category.do?cid=14249#pageId=0&department=166&mlink=5058,18336122,HP_VCN_7_BG_G28771_CTA"}]}}}, {"instanceDesc": "8grid-item8", "name": "SVGOverlay", "type": "sitewide", "tileStyle": {"mobile": {"boxSizing": "border-box", "marginBottom": "1.5rem", "maxWidth": "320px", "padding": "0 0.6rem", "width": "50%"}, "desktop": {"boxSizing": "border-box", "maxWidth": "25%", "padding": "0 1rem 2rem", "width": "25%"}}, "data": {"background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_8.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_8.jpg", "altText": "Baby Boy"}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_8.jpg", "largeImg": "/Asset_Archive/GPWeb/content/0018/336/122/assets/vcn/mom/VCN_8.jpg", "altText": "Baby Boy", "link": {"url": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,18336122,HP_VCN_8_BB_G28771_IMAGE"}}, "linksContainerStyle": {"mobile": {"padding": "0"}, "desktop": {"left": "50%", "padding": "0", "top": "40%", "transform": "translateX(-50%)", "zIndex": "2"}}, "links": {"style": {"mobile": {"padding": "12px", "borderColor": "#ccc", "borderTopWidth": "0", "boxSizing": "border-box", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "2px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Baby Boy", "href": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,18336122,HP_VCN_8_BB_G28771_CTA"}]}}}]}}}}, {"instanceName": "promo2", "instanceDesc": "Promo2_030320_GapCash", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "358px", "large": "189px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"maxWidth": "1920px", "margin": "0 auto"}, "components": [{"name": "SVGOverlay", "type": "sitewide", "data": {"containerStyle": {"mobile": {"maxWidth": "640px", "margin": "0 auto 3.25rem"}, "desktop": {"maxWidth": "1920px", "margin": "0 auto 4.5rem"}}, "background": {"content": {"smallImg": "/Asset_Archive/GPWeb/content/0018/576/608/assets/promos/022520_ALL_GapCashEarnR28_HPbanner_MOB_US.svg", "largeImg": "/Asset_Archive/GPWeb/content/0018/576/608/assets/promos/121019_ALL_GapCashEarnR26_HPbanner_DESK_US.svg", "altText": "GapCash. Earn at Gap & Gap Factory."}}, "svgoverlay": {"smallImg": "/Asset_Archive/GPWeb/content/0018/576/608/assets/promos/022520_ALL_GapCashEarnR28_HPbanner_MOB_US.svg?v=2", "largeImg": "/Asset_Archive/GPWeb/content/0018/576/608/assets/promos/121019_ALL_GapCashEarnR26_HPbanner_DESK_US.svg?v=2", "altText": "1. Start Here: Earn $20 in GapCash for every $50+ you spend. New: Now you can earn every day! 2. Go Big: Earn up to $120 in GapCash per order! 3. Cash Out: Redeem your GapCash & combine with other offers 6/4-6/8.", "link": {"url": "/browse/info.do?cid=99996&mlink=5058,18576608,HP_Banner_GapCashRedeem_LearnMore"}}, "linksContainerStyle": {"mobile": {"position": "absolute", "top": "84.5%", "left": "50%", "padding": "0", "transform": "translateX(-50%)", "zIndex": "2"}, "desktop": {"top": "64%", "left": "72.6%", "padding": "0", "transform": "translateY(-50%)", "zIndex": "2"}}, "links": {"style": {"mobile": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "1rem", "fontWeight": "700", "letterSpacing": "1px", "padding": "0.6rem 1rem", "textAlign": "center"}, "desktop": {"backgroundColor": "#FFFFFF", "borderWidth": "0", "color": "#122344", "fontSize": "12px", "fontWeight": "700", "letterSpacing": "1px", "padding": "0.6rem 1rem", "textAlign": "center"}}, "content": [{"text": "Learn More", "href": "/browse/info.do?cid=99996&mlink=5058,18473896,HP_Banner_GapCashRedeem_LearnMore"}]}}}]}}}}]}, "meta.title.override": "Shop Women, Men, Mat<PERSON><PERSON>, Baby & Kids Clothes Online", "type": "meta", "brand": "gap", "meta.description": "Shop womens, mens, maternity, kids & baby clothes at Gap online and find the perfect pair of jeans, t-shirts, dresses and more for the whole family."}