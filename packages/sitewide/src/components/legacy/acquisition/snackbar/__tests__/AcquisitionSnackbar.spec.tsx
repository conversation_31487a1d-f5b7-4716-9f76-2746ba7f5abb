// @ts-nocheck
import {DataLayer} from "@ecom-next/core/legacy/analytics"
import { screen, render, RenderResult, act } from "@sitewide/components/legacy/setupTests/test-helpers";
import {AcquisitionSnackbar} from "../AcquisitionSnackbar"

const mockedDatalayer: DataLayer = {
  add: jest.fn(),
  build: jest.fn(),
  builderNames: jest.fn(),
  link: jest.fn(),
  linkWithPageProps: jest.fn(),
  view: jest.fn(),
  isTealiumReady: jest.fn(),
  viewWith: jest.fn(),
}

const renderSnackBar = (): RenderResult =>
  render(<AcquisitionSnackbar />, {appState: {datalayer: mockedDatalayer}})

const querySnackBar = () => screen.queryByTestId("test-snackbar")

const getSnackBarWithMessage = (text: string) => screen.getByText(text)

describe("<AcquisitionSnackbar />", () => {
  beforeEach(() => {
    global.IS_SSR = false
    window.utag = {view: () => {}}
  })

  describe("Barclays message", () => {
    it("should be rendered when hash contains 'temp'", () => {
      window.location.assign("https://test.app.stage.somedomain.com/#barclaysOffer=temp");

      renderSnackBar()

      const expectedTemporaryMessage =
        "Success! Your temporary card number has been emailed to you and is ready for use."

      expect(
        getSnackBarWithMessage(expectedTemporaryMessage)
      ).toBeInTheDocument()
    })

    it("should be rendered when hash contains 'success'", () => {
      window.location.assign("https://test.app.stage.somedomain.com/#barclaysOffer=success");

      renderSnackBar()

      const expectedSuccessMessage =
        "Success! Your new credit card has been saved to your Rewards Account and is available for use in checkout."

      expect(getSnackBarWithMessage(expectedSuccessMessage)).toBeInTheDocument()
    })

    it("should be rendered when hash contains 'success' with letters with different cases", () => {
      window.location.assign("https://test.app.stage.somedomain.com/#bArCLAysOfFer=SUCcess");

      renderSnackBar()

      const expectedSuccessMessage =
        "Success! Your new credit card has been saved to your Rewards Account and is available for use in checkout."

      expect(getSnackBarWithMessage(expectedSuccessMessage)).toBeInTheDocument()
    })

    it("should not be rendered when barclays hash is not valid", () => {
      window.location.assign("https://test.app.stage.somedomain.com/#barclaysOffer=invalidValue");

      renderSnackBar()

      expect(querySnackBar()).not.toBeInTheDocument()
    })

    it("should not be rendered when barclays hash is not provided", () => {
      window.location.assign("https://test.app.stage.somedomain.com");

      renderSnackBar()

      expect(querySnackBar()).not.toBeInTheDocument()
    })
  })
})
