// @ts-nocheck
import {XLARGE} from "@ecom-next/core/breakpoint-provider"
import {axe} from "jest-axe"
import * as invertModule from "@ecom-next/core/react-stitch"
import {
  expectNotToBeSticky,
  scrollDown,
  scrollUp,
} from "@sitewide/components/legacy/setupTests/sticky"
import {
  act,
  expectToBeSticky,
  fireEvent,
  mockIntersectionObserver,
  RenderOptions,
  RenderResult,
  screen,
  waitFor,
} from "@sitewide/components/legacy/setupTests/test-helpers"
import {ContentData} from "../../content"
import CompressedHeader from "../CompressedHeader"
import {
  BackgroundOptions,
  ContrastOptions,
  StickyScrollDirections,
} from "../types"
import {expectFullBleedEffect, getHeader} from "../__fixtures__/expectations"
import {compressedHeaderContent} from "../__fixtures__/compressedHeaderContent"
import {renderWithHeaderContext} from "../__fixtures__/renderWithHeaderContext"
import {DummyHeaderComponent} from "../__fixtures__/DummyHeaderComponent"
import {defaultLinks} from "../../brand-bar/__fixtures__"
import {Brand} from "@sitewide/components/legacy/types"
import {
  BR_2023_BACKGROUND_COLOR,
  BR_2023_HEADER_GRADIENT_BLACK,
  BR_2024_HEADER_COLOR,
  features,
} from "@sitewide/components/legacy/sitewide-constants"
import {SCROLL_OFFSET} from "../../sticky-manager/ScrollUpStickyElement"

const spyInvertStyleContext = jest.spyOn(invertModule, "useInverseStyleContext")
const invertSpy = (isEnabled: boolean) => {
  spyInvertStyleContext.mockImplementation(() => {
    return {invert: isEnabled}
  })
}
const getUtilityLinks = (): HTMLElement[] =>
  screen.queryAllByText(/(FIND A STORE)|(CREDIT CARD)|(GIFT CARD)/)

const waitForUpdate = async () => {
  await act(async () => {
    await new Promise((resolve) => setTimeout(resolve))
  })
}

const getSearchBarDesktop = (): HTMLElement => screen.getByRole("search")

const getSearchInput = (): HTMLElement => screen.getByRole("combobox")
mockIntersectionObserver()
const renderDesktopHeader = (
  props: ContentData = compressedHeaderContent(),
  renderOptions: RenderOptions = {}
): RenderResult =>
  renderWithHeaderContext(
    <>
      <CompressedHeader {...props.sitewide.header} data={{}} />
      <DummyHeaderComponent setNeedsHightContrastFn={() => {}} />
    </>,
    {
      contentData: props,
      breakpoint: XLARGE,
      ...renderOptions,
    }
  )

const headerBackground = "pink"

function checkHeaderBackground() {
  const desktopHeader = getHeader()
  expect(desktopHeader).toHaveStyleRule(
    "background",
    BR_2023_HEADER_GRADIENT_BLACK
  )
}

const emulateStickyBehavior = (pageOffset = 400): void => {
  scrollDown(pageOffset)
  scrollUp(SCROLL_OFFSET + 20) // This should be a little more than the SCROLL_OFFSET in ScrollUpStickyElement.tsx
}

describe("DesktopCompressedHeader", () => {
  beforeAll(async () => {
    const header = renderDesktopHeader()
    await waitForUpdate()
    header.unmount()
  })

  xit("does not have accessibility violations", async () => {
    renderDesktopHeader()

    const accessibleHeader = await axe(getHeader())

    expectFullBleedEffect()
    expect(accessibleHeader).toHaveNoViolations()
  })

  it("it should render search bar", async () => {
    renderDesktopHeader(
      compressedHeaderContent({
        default: {
          desktopOverride: {
            contrast: ContrastOptions.LIGHT,
          },
        },
      })
    )

    expect(getSearchBarDesktop()).toBeInTheDocument()
  })

  it("it should render with customized background color on hover", () => {
    renderDesktopHeader(
      compressedHeaderContent({
        byPageType: [
          {
            configurationForPageTypes: ["home"],
            topNavBackground: "#EE4B2B",
            flyoutBackground: "#000000",
          },
        ],
      }),
      {appState: {pageType: "home"}}
    )
    const desktopHeader = getHeader()
    fireEvent.mouseOver(desktopHeader)
    expect(desktopHeader).toHaveStyleRule("background", "#EE4B2B", {
      target: ":hover",
    })
  })

  it("it should render with default background color on hover if pageType is not present", () => {
    renderDesktopHeader(
      compressedHeaderContent({
        default: {
          desktopOverride: {
            topNavBackground: "#F6F4EB",
            flyoutBackground: "#000000",
          },
        },
      })
    )
    const desktopHeader = getHeader()
    fireEvent.mouseOver(desktopHeader)
    expect(desktopHeader).toHaveStyleRule("background", "#F6F4EB", {
      target: ":hover",
    })
  })

  it("it should render with transparent background color on hover if pageType & default is not present", () => {
    renderDesktopHeader(
      compressedHeaderContent({
        default: {
          isStickyEnabled: false,
          fullBleedOptions: {
            isFullBleedEnabled: true,
            hasTransparencyLayer: false,
            fullBleedContrast: ContrastOptions.DARK,
          },
          desktopOverride: {
            fullBleedOptions: {
              fullBleedContrast: ContrastOptions.DARK,
            },
          },
        },
      })
    )
    const desktopHeader = getHeader()
    expect(desktopHeader).toHaveStyleRule("background", "transparent")
  })

  it("should render with style set for position and width, when header is full bleed", () => {
    renderDesktopHeader(
      compressedHeaderContent({
        default: {
          isStickyEnabled: true,
          fullBleedOptions: {
            isFullBleedEnabled: true,
            hasTransparencyLayer: false,
            fullBleedContrast: ContrastOptions.DARK,
          },
        },
      }),
      {enabledFeatures: {"sw-new-sticky-manager": true}}
    )
    const desktopHeader = getHeader()
    expect(desktopHeader).toHaveStyleRule("position", "absolute")
    expect(desktopHeader).toHaveStyleRule("width", "100%")
  })

  it("it should render with dark contrast when the contrast override is set", () => {
    renderDesktopHeader(
      compressedHeaderContent({
        byPageType: [
          {
            configurationForPageTypes: ["product"],
            desktopOverride: {
              contrast: ContrastOptions.DARK,
            },
          },
        ],
      }),
      {appState: {pageType: "product", brandName: Brand.BananaRepublic}}
    )

    const lightLogo = screen.getByAltText("br logo light")
    const darkLogo = screen.getByAltText("br logo dark")
    expect(lightLogo).not.toBeVisible()
    expect(darkLogo).toBeVisible()
  })

  it("it should render with light contrast when the contrast override is set", () => {
    renderDesktopHeader(
      compressedHeaderContent({
        byPageType: [
          {
            configurationForPageTypes: ["product"],
            desktopOverride: {
              contrast: ContrastOptions.LIGHT,
            },
          },
        ],
      }),
      {appState: {pageType: "product", brandName: Brand.BananaRepublic}}
    )

    const lightLogo = screen.getByAltText("br logo light")
    const darkLogo = screen.getByAltText("br logo dark")
    expect(lightLogo).toBeVisible()
    expect(darkLogo).not.toBeVisible()
  })

  it("should render old navy desktop header with white background", () => {
    renderDesktopHeader(
      // @ts-ignore
      {sitewide: {header: {}}},
      {
        appState: {brandName: Brand.OldNavy},
      }
    )

    expect(screen.getByTestId("compressed-sticky-header")).toHaveStyleRule(
      "background-color",
      "#FFFFFF"
    )
  })

  describe("when there a text search input", () => {
    describe("test utility links", () => {
      it("should render utility links if isUtilityLinksEnabled is undefined", () => {
        renderDesktopHeader(
          compressedHeaderContent({
            default: {
              isUtilityLinksEnabled: undefined,
            },
          }),
          {appState: {brandBarShortcutLinks: defaultLinks}}
        )

        const utilityLinks = getUtilityLinks()
        utilityLinks.forEach((utilityLink) => {
          expect(utilityLink).toBeInTheDocument()
        })
      })

      it("should not render utility links if isUtilityLinksEnabled is false", () => {
        renderDesktopHeader(
          compressedHeaderContent({
            default: {
              isUtilityLinksEnabled: false,
            },
          }),
          {appState: {brandBarShortcutLinks: defaultLinks}}
        )

        const utilityLinks = getUtilityLinks()
        expect(utilityLinks.length).toEqual(0)
      })

      it("should render utility links if isUtilityLinksEnabled is true", () => {
        renderDesktopHeader(
          compressedHeaderContent({
            default: {
              isUtilityLinksEnabled: true,
            },
          }),
          {appState: {brandBarShortcutLinks: defaultLinks}}
        )
        const utilityLinks = getUtilityLinks()
        utilityLinks.forEach((utilityLink) => {
          expect(utilityLink).toBeInTheDocument()
        })
      })
    })

    // TODO - search is not using the new compressed header yet
    xdescribe("render with default contentData", () => {
      beforeEach(() => {
        renderDesktopHeader(
          compressedHeaderContent({
            default: {
              contrast: ContrastOptions.DARK,
              desktopOverride: {
                contrast: ContrastOptions.LIGHT,
              },
            },
          })
        )
      })

      it("keeps high contrast even when header is not hovered", async () => {
        fireEvent.change(getSearchInput(), {target: {value: "jeans"}})

        await waitFor(() => {
          expect(getHeader()).toHaveStyleRule("background", "#FFFFFF")
        })
      })

      it("should update backdrop contrast back to original value when text is deleted", async () => {
        fireEvent.change(getSearchInput(), {target: {value: "jeans"}})
        fireEvent.change(getSearchInput(), {target: {value: ""}})

        await waitFor(() => {
          expect(getHeader()).toHaveStyleRule("background", "transparent")
        })
      })
    })
  })

  describe("Sticky Header Background Color", () => {
    beforeEach(() => {
      fireEvent.scroll(window, {target: {pageYOffset: 0}})
    })

    const styles = {
      brBgColor: "#FFFFFF",
      transparent: "transparent",
    }

    it("should render with a background hex value when pageType in not defined, and default is", () => {
      renderDesktopHeader(
        compressedHeaderContent({
          default: {
            desktopOverride: {
              stickyBackground: BackgroundOptions.CONTRAST,
            },
          },
        })
      )
      emulateStickyBehavior()
      expect(getHeader()).toHaveStyleRule("background", styles.brBgColor)
    })

    it("should render with a default background hex value when pageType and default are not defined", () => {
      renderDesktopHeader(compressedHeaderContent({}))
      emulateStickyBehavior()
      expect(getHeader()).toHaveStyleRule("background", styles.brBgColor)
    })

    it("should render with a background value of transparent for corresponding pageType", () => {
      renderDesktopHeader(
        compressedHeaderContent({
          byPageType: [
            {
              configurationForPageTypes: ["home"],
              stickyBackground: BackgroundOptions.TRANSPARENT,
            },
          ],
        }),
        {appState: {pageType: "home"}}
      )
      emulateStickyBehavior()
      expect(getHeader()).toHaveStyleRule("background", styles.transparent)
    })

    it("should render with a background value of transparent for corresponding pageType, overriding default", () => {
      renderDesktopHeader(
        compressedHeaderContent({
          byPageType: [
            {
              configurationForPageTypes: ["home"],
              desktopOverride: {
                stickyBackground: BackgroundOptions.TRANSPARENT,
              },
            },
          ],
          default: {
            desktopOverride: {
              stickyBackground: BackgroundOptions.CONTRAST,
            },
          },
        }),
        {appState: {pageType: "home"}}
      )
      emulateStickyBehavior()
      expect(getHeader()).toHaveStyleRule("background", styles.transparent)
    })

    it("should render with a background value of transparent for corresponding pageType when desktop value is set", () => {
      renderDesktopHeader(
        compressedHeaderContent({
          byPageType: [
            {
              configurationForPageTypes: ["home"],
              desktopOverride: {
                stickyBackground: BackgroundOptions.TRANSPARENT,
              },
            },
          ],
        }),
        {appState: {pageType: "home"}}
      )
      emulateStickyBehavior()
      expect(getHeader()).toHaveStyleRule("background", styles.transparent)
    })

    it("should render with a default mobile background for corresponding pageType when only mobile value is set", () => {
      renderDesktopHeader(
        compressedHeaderContent({
          byPageType: [
            {
              configurationForPageTypes: ["home"],
              stickyBackground: BackgroundOptions.CONTRAST,
            },
          ],
        }),
        {appState: {pageType: "home"}}
      )
      emulateStickyBehavior()
      expect(getHeader()).toHaveStyleRule("background", styles.brBgColor)
    })

    it("should render with default background hex value, when full bleed is disabled", () => {
      renderDesktopHeader(compressedHeaderContent())
      emulateStickyBehavior()
      expect(getHeader()).toHaveStyleRule("background", styles.brBgColor)
    })
  })

  describe("stickiness", () => {
    beforeEach(() => {
      fireEvent.scroll(window, {target: {pageYOffset: 0}})
    })

    const getStickyHeaderContainer = (): HTMLElement =>
      screen.getByTestId("compressed-sticky-header")

    it("is not sticky when configuration explicitly says so", () => {
      renderDesktopHeader(
        compressedHeaderContent({
          default: {
            desktopOverride: {
              isStickyEnabled: false,
            },
          },
        })
      )
      emulateStickyBehavior()
      expectNotToBeSticky(getStickyHeaderContainer())
    })

    describe("near top", () => {
      it("should default not sticky and background be transparent", () => {
        renderDesktopHeader()
        expectNotToBeSticky(getStickyHeaderContainer())
      })

      it("should not be sticky when we scroll down", () => {
        renderDesktopHeader()
        scrollDown(300)
        expectNotToBeSticky(getStickyHeaderContainer())
      })

      it("should not be sticky when back to top", () => {
        renderDesktopHeader()
        scrollDown(300)
        expectNotToBeSticky(getStickyHeaderContainer())
        scrollUp(300)
        expectNotToBeSticky(getStickyHeaderContainer())
      })

      it("should not be sticky when scroll direction is BOTH when back to top", () => {
        renderDesktopHeader(
          compressedHeaderContent({
            default: {
              isStickyEnabled: true,
              desktopOverride: {
                stickyScrollDirection: StickyScrollDirections.BOTH,
              },
            },
          })
        )
        scrollDown(400)
        scrollUp(400)
        expectNotToBeSticky(getStickyHeaderContainer())
      })
    })

    describe("not near top", () => {
      describe("when scroll direction is default or UP", () => {
        it("should be sticky when we scroll up quickly", () => {
          renderDesktopHeader()
          emulateStickyBehavior()
          expectToBeSticky(getStickyHeaderContainer())
        })

        it("should be sticky when scrolling down and scrolling a little up when scroll direction is explicitly set to UP", () => {
          renderDesktopHeader(
            compressedHeaderContent({
              default: {
                isStickyEnabled: true,
                desktopOverride: {
                  stickyScrollDirection: StickyScrollDirections.UP,
                },
              },
            })
          )
          emulateStickyBehavior()
          expectToBeSticky(getStickyHeaderContainer())
        })

        it("should be sticky when scroll up and scroll direction property matches in page type", () => {
          renderDesktopHeader(
            compressedHeaderContent({
              byPageType: [
                {
                  configurationForPageTypes: ["product"],
                  isStickyEnabled: true,
                  desktopOverride: {
                    stickyScrollDirection: StickyScrollDirections.UP,
                  },
                },
              ],
            }),
            {appState: {pageType: "product"}}
          )
          emulateStickyBehavior()
          expectToBeSticky(getStickyHeaderContainer())
        })

        it("should be sticky with scroll direction property UP when set in default", () => {
          renderDesktopHeader(
            compressedHeaderContent({
              default: {
                isStickyEnabled: true,
              },
            })
          )
          emulateStickyBehavior()
          expectToBeSticky(getStickyHeaderContainer())
        })
      })

      describe("when scroll direction set to BOTH", () => {
        it("should be sticky when we scroll down", () => {
          renderDesktopHeader(
            compressedHeaderContent({
              default: {
                isStickyEnabled: true,
                desktopOverride: {
                  stickyScrollDirection: StickyScrollDirections.BOTH,
                },
              },
            })
          )
          scrollDown(600)
          expectToBeSticky(getStickyHeaderContainer())
        })
        it("should be sticky when we scroll down and up", () => {
          renderDesktopHeader(
            compressedHeaderContent({
              default: {
                isStickyEnabled: true,
                desktopOverride: {
                  stickyScrollDirection: StickyScrollDirections.BOTH,
                },
              },
            })
          )
          scrollDown(900)
          expectToBeSticky(getStickyHeaderContainer())
          scrollUp(200)
          expectToBeSticky(getStickyHeaderContainer())
          scrollUp(100)
        })
      })
    })
  })

  describe("Compressed header background color override", () => {
    it("renders with desired color when headerBackground key is present in JSON while rendering to desired pageType", () => {
      renderDesktopHeader(
        compressedHeaderContent({
          default: {
            desktopOverride: {
              headerBackground,
            },
          },
        }),
        {appState: {pageType: "category"}}
      )

      const desktopHeader = screen.getByTestId("brand-header")

      expect(desktopHeader).toHaveStyleRule("background", headerBackground)
    })
  })

  describe("Gradient background for BR", () => {
    afterAll(() => {
      spyInvertStyleContext.mockReset()
    })

    describe("when hasTransparencyLayer is enabled", () => {
      beforeEach(() => {
        invertSpy(true)
        fireEvent.scroll(window, {target: {pageYOffset: 0}})
        renderDesktopHeader(
          compressedHeaderContent({
            default: {
              stickyBackground: BackgroundOptions.TRANSPARENT,
              headerBackground: BR_2023_BACKGROUND_COLOR,
              fullBleedOptions: {
                isFullBleedEnabled: true,
                hasTransparencyLayer: true,
              },
            },
          })
        )
      })

      it("should render with BR gradient background color ", () => {
        checkHeaderBackground()
      })

      it("should render with BR gradient background color when sticky", () => {
        scrollDown(400)
        checkHeaderBackground()
      })
    })

    describe("when hasTransparencyLayer is disabled", () => {
      beforeEach(() => {
        invertSpy(true)
        fireEvent.scroll(window, {target: {pageYOffset: 0}})
        renderDesktopHeader(
          compressedHeaderContent({
            default: {
              isStickyEnabled: false,
              stickyBackground: BackgroundOptions.CONTRAST,
              headerBackground: BR_2023_BACKGROUND_COLOR,
              contrast: ContrastOptions.LIGHT,
              fullBleedOptions: {
                isFullBleedEnabled: false,
                hasTransparencyLayer: true,
                fullBleedContrast: ContrastOptions.DARK,
              },
            },
          })
        )
      })

      it("should render with BR gradient background color without hasTransparencyLayer", () => {
        const desktopHeader = getHeader()
        expect(desktopHeader).toHaveStyleRule(
          "background",
          BR_2023_BACKGROUND_COLOR
        )
      })
    })
  })

  describe("when BR_WHITE_BACKGROUND is active", () => {
    beforeAll(() => {
      invertSpy(true)
    })
    it("should render desktop compressed header with BR 2024 background color", () => {
      renderDesktopHeader(
        // @ts-ignore
        {sitewide: {header: {}}},
        {
          enabledFeatures: {[features.BR_WHITE_BACKGROUND]: true},
          appState: {brandName: Brand.BananaRepublic},
        }
      )

      const desktopHeader = screen.getByTestId("brand-header")

      expect(desktopHeader).toHaveStyleRule("background", BR_2024_HEADER_COLOR)
    })
  })

  describe("Gradient background for AT", () => {
    describe("when hasTransparencyLayer is enabled", () => {
      beforeEach(() => {
        invertSpy(true)
        fireEvent.scroll(window, {target: {pageYOffset: 0}})
        renderDesktopHeader(
          compressedHeaderContent({
            default: {
              stickyBackground: BackgroundOptions.TRANSPARENT,
              headerBackground: BR_2023_BACKGROUND_COLOR,
              fullBleedOptions: {
                isFullBleedEnabled: true,
                hasTransparencyLayer: true,
              },
            },
          }),
          {
            enabledFeatures: {"at-redesign-2023": true},
            appState: {brandName: Brand.Athleta},
          }
        )
      })

      it("should render with gradient background when not sticky ", () => {
        checkHeaderBackground()
      })

      it("should render with gradient background color when sticky", () => {
        scrollDown(400)
        checkHeaderBackground()
      })
    })
  })
})
