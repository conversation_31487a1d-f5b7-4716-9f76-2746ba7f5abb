{"meta.description": "pageDescription defaulted", "meta.title.overide": "pageTitle defaulted", "home": {"type": "home", "name": "HomeMultiSimple", "components": [{"instanceDesc": "2023-05-23 WCD HP CSS Modifications - https://github.gapinc.com/wcd/shared-code-library/tree/main/hp-css/", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>#main-content *{box-sizing:border-box}#main-content img,#main-content video{display:block}.slick-slide>div>div{display:block!important}.wcd_certona-wrapper{margin-left:auto;margin-right:auto;max-width:1920px;width:100%}.wcd_certona-wrapper .wcd_hp-copy.h2,.wcd_certona-wrapper h2{margin-bottom:.875em}.wcd_certona-wrapper h2{padding-left:15px;padding-right:15px}.wcd_certona-wrapper .slick-next{right:0}.wcd_certona-wrapper .slick-prev{left:0}.wcd_certona-wrapper div[data-testid=recommended-product-card]{max-width:100%}.wcd_certona-wrapper div[data-testid=recommended-product-card]>div{padding:0}.wcd_certona-wrapper div[data-testid=recommended-product-card] p{color:#999;font-size:13px;line-height:1.125}.wcd_certona-wrapper div[data-testid=recommended-product-card] a>div>div:first-child{margin-bottom:10px}.wcd_certona-wrapper div[data-testid=recommended-product-card] a>div>div:not(:first-child){padding-left:15px}.wcd_certona-wrapper div[data-testid=recommended-product-card] a>div div[data-testid=price-block]{display:none}.wcd_gutters{padding-left:15px;padding-right:15px}.wcd_sub-copy-spacing{padding-bottom:22.5px;padding-top:22.5px}.wcd_submessage-trio-lockup{-ms-flex-align:start;align-items:flex-start;display:-ms-flexbox;display:flex;-ms-flex-wrap:nowrap;flex-wrap:nowrap;padding-top:22.5px}.wcd_submessage-trio-lockup>div{max-width:50%;width:100%}.wcd_submessage-trio-lockup>div:first-child{padding-right:15px}.wcd_submessage-trio-lockup>div:last-child{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.wcd_certona-wrapper .wcd_submessage-trio-lockup>div:last-child h2,.wcd_submessage-trio-lockup>div:last-child .h2,.wcd_submessage-trio-lockup>div:last-child .wcd_certona-wrapper h2{white-space:nowrap}.wcd_submessage-trio-lockup>div:last-child div.wcd_hp-cta{-ms-flex-pack:start;justify-content:flex-start}.wcd_certona-wrapper h2,.wcd_hp-copy{color:#767676;font-size:17px;font-weight:400;line-height:1.125;min-height:0}.wcd_certona-wrapper h2.dark,.wcd_hp-copy.dark{color:#767676}.wcd_certona-wrapper h2.light,.wcd_hp-copy.light{color:#fff}.wcd_certona-wrapper h2,.wcd_certona-wrapper h2.h1,.wcd_hp-copy.h1,.wcd_hp-copy.h2{color:#999}.wcd_certona-wrapper h2.h1.light,.wcd_certona-wrapper h2.light,.wcd_hp-copy.h1.light,.wcd_hp-copy.h2.light{color:#fff}.wcd_certona-wrapper h2.h1,.wcd_hp-copy.h1{font-size:30px}.wcd_certona-wrapper h2,.wcd_hp-copy.h2{font-size:.875em;font-weight:500;margin-bottom:.625em;text-transform:uppercase}div.wcd_hp-cta{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between}.wcd_hp-cta button,.wcd_hp-cta>a,a .wcd_hp-cta,a.wcd_hp-cta,button.wcd_hp-cta{-ms-flex-align:center;align-items:center;background-color:#fff;border-color:#fff;border-style:solid;border-width:1px;color:#767676;display:-ms-flexbox;display:flex;font-size:13px;font-weight:400;height:Max(2.125em,32px);letter-spacing:0;padding:0 15px;text-transform:uppercase;width:auto}.wcd_hp-cta button:hover,.wcd_hp-cta>a:hover,a .wcd_hp-cta:hover,a.wcd_hp-cta:hover,button.wcd_hp-cta:hover{background-color:#999;border-color:#999;color:#fff}.wcd_hp-cta div[data-testid=button-dropdown-container]:not(:last-child),.wcd_hp-cta>a:not(:last-child){margin-bottom:8px}.wcd_hp-cta button{border-color:#767676;-ms-flex-pack:justify;justify-content:space-between}.wcd_hp-cta button:focus{background-color:#999;border-color:#999;color:#fff;outline:0}.wcd_hp-cta button span{font-size:1.625em;height:.25em;line-height:0;margin-left:.5em;padding:0}.wcd_hp-cta div[data-testid=button-dropdown-container]{position:relative;width:auto}.wcd_hp-cta div[data-testid=button-dropdown-container]:first-child{z-index:40}.wcd_hp-cta div[data-testid=button-dropdown-container]:nth-child(2){z-index:9}.wcd_hp-cta div[data-testid=button-dropdown-container]:nth-child(3){z-index:8}.wcd_hp-cta div[data-testid=button-dropdown-container]:nth-child(4){z-index:7}.wcd_hp-cta div[data-testid=button-dropdown-container]:nth-child(5){z-index:6}.wcd_hp-cta div[data-testid=button-dropdown-container]:nth-child(6){z-index:5}.wcd_hp-cta div[data-testid=button-dropdown-container]:nth-child(7){z-index:4}.wcd_hp-cta div[data-testid=button-dropdown-container]:nth-child(8){z-index:3}.wcd_hp-cta div[data-testid=button-dropdown-container]:nth-child(9){z-index:2}.wcd_hp-cta div[data-testid=button-dropdown-container]:nth-child(10){z-index:1}.wcd_hp-cta div[data-testid=button-dropdown-container] ul{background-color:#fff;border-color:#fff;border-style:solid;border-width:0;box-shadow:rgba(0,0,0,.25) 0 1px 6px 0;padding:0;position:absolute}.wcd_hp-cta div[data-testid=button-dropdown-container] li{border-width:0;padding:0}.wcd_hp-cta div[data-testid=button-dropdown-container] li:first-child{border-top-width:1px}.wcd_hp-cta div[data-testid=button-dropdown-container] a{border-color:#fff;border-style:solid;border-width:0;color:#767676;font-size:13px;padding-left:15px;padding-right:15px;text-align:left}.wcd_hp-cta div[data-testid=button-dropdown-container] a:active,.wcd_hp-cta div[data-testid=button-dropdown-container] a:focus,.wcd_hp-cta div[data-testid=button-dropdown-container] a:hover{background-color:#e9e9e9;border-color:#fff;color:#767676}.wcd_hp-cta.caret button,.wcd_hp-cta.caret>a,a .wcd_hp-cta.caret,a.wcd_hp-cta.caret,button.wcd_hp-cta.caret{background-color:transparent;background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 13px;display:-ms-inline-flexbox;display:inline-flex;-ms-flex-pack:justify;justify-content:space-between}.wcd_hp-cta.caret button:hover,.wcd_hp-cta.caret>a:hover,a .wcd_hp-cta.caret:hover,a.wcd_hp-cta.caret:hover,button.wcd_hp-cta.caret:hover{background-color:transparent;border-color:transparent;color:#767676}.wcd_hp-cta.arrow button,.wcd_hp-cta.arrow>a,a .wcd_hp-cta.arrow,a.wcd_hp-cta.arrow,button.wcd_hp-cta.arrow{background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--black.svg);padding-right:calc(1em + 15px + 6px)}.wcd_hp-cta.arrow button.light,.wcd_hp-cta.arrow>a.light,a .wcd_hp-cta.arrow.light,a.wcd_hp-cta.arrow.light,button.wcd_hp-cta.arrow.light{background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--white.svg)}.wcd_hp-cta.caret button,.wcd_hp-cta.caret>a,a .wcd_hp-cta.caret,a.wcd_hp-cta.caret,button.wcd_hp-cta.caret{background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret.svg);padding-right:calc(.5em + 15px + 6px)}.wcd_hp-cta.caret button.light,.wcd_hp-cta.caret>a.light,a .wcd_hp-cta.caret.light,a.wcd_hp-cta.caret.light,button.wcd_hp-cta.caret.light{background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret_white.svg)}  .wcd_hp-cta.caret-vcn button,.wcd_hp-cta.caret-vcn>a,a .wcd_hp-cta.caret-vcn,a.wcd_hp-cta.caret-vcn,button.wcd_hp-cta.caret-vcn{background-color:transparent;background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 13px;display:-ms-inline-flexbox;display:inline-flex;-ms-flex-pack:justify;justify-content:space-between}.wcd_hp-cta.caret-vcn button:hover,.wcd_hp-cta.caret-vcn>a:hover,a .wcd_hp-cta.caret-vcn:hover,a.wcd_hp-cta.caret-vcn:hover,button.wcd_hp-cta.caret-vcn:hover{background-color:transparent;border-color:transparent;color:#767676}.wcd_hp-cta.arrow button,.wcd_hp-cta.arrow>a,a .wcd_hp-cta.arrow,a.wcd_hp-cta.arrow,button.wcd_hp-cta.arrow{background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--black.svg);padding-right:calc(1em + 15px + 6px)}.wcd_hp-cta.arrow button.light,.wcd_hp-cta.arrow>a.light,a .wcd_hp-cta.arrow.light,a.wcd_hp-cta.arrow.light,button.wcd_hp-cta.arrow.light{background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--white.svg)}.wcd_hp-cta.caret-vcn button,.wcd_hp-cta.caret-vcn>a,a .wcd_hp-cta.caret-vcn,a.wcd_hp-cta.caret-vcn,button.wcd_hp-cta.caret-vcn{background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret.svg);padding-right:calc(.5em + 15px + 6px)}.wcd_hp-cta.caret-vcn button.light,.wcd_hp-cta.caret-vcn>a.light,a .wcd_hp-cta.caret-vcn.light,a.wcd_hp-cta.caret-vcn.light,button.wcd_hp-cta.caret-vcn.light{background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret_white.svg)}.wcd_hp-cta.details>a,a .wcd_hp-cta.details,a.wcd_hp-cta.details,button.wcd_hp-cta.details{background-color:transparent;border-width:0;color:#fff;font-size:10px;min-height:16px;min-width:32px;padding:0;text-decoration:underline}.wcd_hp-cta.details>a:hover,a .wcd_hp-cta.details:hover,a.wcd_hp-cta.details:hover,button.wcd_hp-cta.details:hover{color:#fff}.wcd_hp-cta.details>a.dark,a .wcd_hp-cta.details.dark,a.wcd_hp-cta.details.dark,button.wcd_hp-cta.details.dark{color:#767676}.wcd_hp-cta.details>a.dark:hover,a .wcd_hp-cta.details.dark:hover,a.wcd_hp-cta.details.dark:hover,button.wcd_hp-cta.details.dark:hover{color:#000}.wcd_hp-cta.outline button,.wcd_hp-cta.outline>a,a .wcd_hp-cta.outline,a.wcd_hp-cta.outline,button.wcd_hp-cta.outline{background-color:transparent;border-color:#767676;border-width:1px}.wcd_hp-cta.outline button:active,.wcd_hp-cta.outline button:focus,.wcd_hp-cta.outline button:hover,.wcd_hp-cta.outline>a:active,.wcd_hp-cta.outline>a:focus,.wcd_hp-cta.outline>a:hover,a .wcd_hp-cta.outline:active,a .wcd_hp-cta.outline:focus,a .wcd_hp-cta.outline:hover,a.wcd_hp-cta.outline:active,a.wcd_hp-cta.outline:focus,a.wcd_hp-cta.outline:hover,button.wcd_hp-cta.outline:active,button.wcd_hp-cta.outline:focus,button.wcd_hp-cta.outline:hover{background-color:#999;color:#fff;border-color:#fff}.wcd_hp-cta.full-width,.wcd_hp-cta.full-width a,.wcd_hp-cta.full-width a>div,.wcd_hp-cta.full-width button,.wcd_hp-cta.full-width button>div,.wcd_hp-cta.full-width-at-mob,.wcd_hp-cta.full-width-at-mob a,.wcd_hp-cta.full-width-at-mob a>div,.wcd_hp-cta.full-width-at-mob button,.wcd_hp-cta.full-width-at-mob button>div,.wcd_hp-cta.full-width-at-mob>div,.wcd_hp-cta.full-width>div,a .wcd_hp-cta.full-width,a .wcd_hp-cta.full-width-at-mob,a .wcd_hp-cta.full-width-at-mob>div,a .wcd_hp-cta.full-width>div{width:100%}.wcd_hp-visnav{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between;margin-left:auto;margin-right:auto;max-width:640px}.wcd_hp-visnav>div{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;position:relative;width:50%}.wcd_hp-visnav>div>a{height:auto}.wcd_hp-visnav>div .wcd_hp-cta button,.wcd_hp-visnav>div button.wcd_hp-cta{border-color:transparent}.wcd_hp-visnav>div .wcd_hp-cta{background-color:#fff;color:#767676;padding-bottom:8px}.wcd_hp-visnav>div .wcd_hp-cta>a{padding-left:15px;z-index:1}.wcd_hp-visnav>div .wcd_hp-cta button:hover{background-color:transparent;color:#767676}.wcd_hp-visnav>div:first-child .wcd_hp-cta{z-index:32}.wcd_hp-visnav>div:nth-child(2) .wcd_hp-cta{z-index:31}.wcd_hp-visnav>div:nth-child(3) .wcd_hp-cta{z-index:30}.wcd_hp-visnav>div:nth-child(4) .wcd_hp-cta{z-index:29}.wcd_hp-visnav>div:nth-child(5) .wcd_hp-cta{z-index:28}.wcd_hp-visnav>div:nth-child(6) .wcd_hp-cta{z-index:27}.wcd_hp-visnav>div:nth-child(7) .wcd_hp-cta{z-index:26}.wcd_hp-visnav>div:nth-child(8) .wcd_hp-cta{z-index:25}.wcd_hp-visnav>div:nth-child(9) .wcd_hp-cta{z-index:24}.wcd_hp-visnav>div:nth-child(10) .wcd_hp-cta{z-index:23}.wcd_hp-visnav>div:nth-child(11) .wcd_hp-cta{z-index:22}.wcd_hp-visnav>div:nth-child(12) .wcd_hp-cta{z-index:21}.wcd_hp-visnav>div:nth-child(13) .wcd_hp-cta{z-index:20}.wcd_hp-visnav>div:nth-child(14) .wcd_hp-cta{z-index:19}.wcd_hp-visnav>div:nth-child(15) .wcd_hp-cta{z-index:18}.wcd_hp-visnav>div:nth-child(16) .wcd_hp-cta{z-index:17}@media only screen and (min-width:768px){.wcd_certona-wrapper .slick-next,.wcd_certona-wrapper .slick-prev{display:block}.wcd_certona-wrapper h2,.wcd_hp-copy{font-size:Min(Max(16px, calc(1rem + ((1vw - 7.68px) * .6944))), 24px)}.wcd_certona-wrapper h2,.wcd_hp-copy.h2{font-size:Min(Max(14px, calc(.875rem + ((1vw - 7.68px) * .8681))), 24px)}.wcd_hp-cta button,.wcd_hp-cta>a,a .wcd_hp-cta,a.wcd_hp-cta,button.wcd_hp-cta{font-size:Min(Max(12px, calc(.75rem + ((1vw - 7.68px) * .6944))), 20px)}.wcd_hp-cta div[data-testid=button-dropdown-container]:not(:last-child),.wcd_hp-cta>a:not(:last-child){margin-right:8px}.wcd_hp-cta div[data-testid=button-dropdown-container] a{font-size:Min(Max(12px, calc(.75rem + ((1vw - 7.68px) * .6944))), 20px)}.wcd_hp-cta.exposed-at-desk button{background-color:transparent;border-width:0;color:inherit;margin-bottom:.75em;padding:0;text-align:left}.wcd_hp-cta.exposed-at-desk button span{display:none}.wcd_hp-cta.exposed-at-desk ul{background-color:transparent;box-shadow:none;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:start;justify-content:flex-start;max-height:none;position:relative;visibility:visible;z-index:0}.wcd_hp-cta.exposed-at-desk ul li{width:auto}.wcd_hp-cta.exposed-at-desk ul li:not(:last-child){margin-bottom:8px;margin-right:8px}.wcd_hp-cta.exposed-at-desk ul li a{border-width:1px}.wcd_hp-cta.caret button,.wcd_hp-cta.caret>a,a .wcd_hp-cta.caret,a.wcd_hp-cta.caret,button.wcd_hp-cta.caret{background-size:auto .9em}.wcd_hp-cta.caret-vcn button,.wcd_hp-cta.caret-vcn>a,a .wcd_hp-cta.caret-vcn,a.wcd_hp-cta.caret-vcn,button.wcd_hp-cta.caret-vcn{background-size:auto .9em}.wcd_hp-cta.full-width-at-mob,.wcd_hp-cta.full-width-at-mob a,.wcd_hp-cta.full-width-at-mob a>div,.wcd_hp-cta.full-width-at-mob button,.wcd_hp-cta.full-width-at-mob button>div,.wcd_hp-cta.full-width-at-mob>div,a .wcd_hp-cta.full-width-at-mob,a .wcd_hp-cta.full-width-at-mob>div{width:auto}.wcd_hp-cta.full-width-at-desk,.wcd_hp-cta.full-width-at-desk a,.wcd_hp-cta.full-width-at-desk a>div,.wcd_hp-cta.full-width-at-desk button,.wcd_hp-cta.full-width-at-desk button>div,.wcd_hp-cta.full-width-at-desk>div,a .wcd_hp-cta.full-width-at-desk,a .wcd_hp-cta.full-width-at-desk>div{width:100%}.wcd_hp-visnav{max-width:1920px}.wcd_hp-visnav>div{background-color:#fff;width:25%}.wcd_hp-visnav>div:hover img{opacity:.85}.wcd_hp-visnav>div .wcd_hp-cta{padding-bottom:14px}}@media only screen and (min-width:1024px){.wcd_submessage-trio-lockup>div:last-child{-ms-flex-align:center;align-items:center;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:end;justify-content:flex-end}.wcd_certona-wrapper .wcd_submessage-trio-lockup>div:last-child h2,.wcd_submessage-trio-lockup>div:last-child .h2,.wcd_submessage-trio-lockup>div:last-child .wcd_certona-wrapper h2{margin-bottom:0;padding-right:8px}.wcd_submessage-trio-lockup>div:last-child div.wcd_hp-cta{-ms-flex-pack:end;justify-content:flex-end}}@media only screen and (min-width:1280px){.wcd_certona-wrapper div[data-testid=recommended-product-card] p{font-size:16px}}@media only screen and (max-width:767px){.wcd_hp-cta.two-column-at-mob div[data-testid=button-dropdown-container],.wcd_hp-cta.two-column-at-mob>a{margin-bottom:8px;width:calc(50% - 4px)}.wcd_hp-cta.two-column-at-mob div[data-testid=button-dropdown-container]:nth-last-child(-n+2),.wcd_hp-cta.two-column-at-mob>a:nth-last-child(-n+2){margin-bottom:0}.wcd_hp-cta.two-column-at-mob div[data-testid=button-dropdown-container] button{width:100%}.wcd_hp-cta.two-column-at-mob.odd-number a:first-child,.wcd_hp-cta.two-column-at-mob.odd-number div[data-testid=button-dropdown-container]:first-child{width:100%}}</style>"}}, {"instanceName": "optly-placeholder-7", "instanceDesc": "PZ Placeholder7", "experimentRunning": true, "name": "OptimizelyPlaceholder", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "0", "large": "0"}}}, {"instanceName": "041423_Certona_2", "instanceDesc": "041423_Certona_2", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "2rem", "large": "3rem"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"margin": "0 auto", "flexDirection": "column", "padding": "0", "maxWidth": "1920px"}, "components": [{"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"lazy": true, "style": {"width": "100%", "@media only screen and (max-width:768px)": {"padding": "0px 0 36px", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}, "@media only screen and (min-width:769px)": {"padding": "0px 0 36px", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}}, "props": {"style": {"width": "100%"}, "className": "fullBleedCertona"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome1_rr", "displayTitle": true, "fullWidth": true, "certonaTitle": {"title": "ACTUALISER VOTRE GARDE-ROBE. IMPOSSIBLE DE S’EN PASSER.", "style": {"mobile": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#767676", "lineHeight": "1.6rem", "fontSize": "4.1vw", "textAlign": "left", "padding": "0px 0 0 13px", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}, "desktop": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#767676", "lineHeight": "1.6rem", "fontSize": "1.1vw", "textAlign": "left", "padding": "0px 0 0 1.5%", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}}}, "layout": "carousel", "centerMode": false, "useMobileConfig": true, "defaultslidesToShowSlick": 4, "defaultslidesToScrollSlick": 4, "resslidesToShowSlick": 4, "resslidesToScrollSlick": 4, "displayPlayPauseButton": false, "responsive": [{"breakpoint": 1350, "settings": {"slidesToShow": 4, "slidesToScroll": 4}}, {"breakpoint": 768, "settings": {"slidesToShow": 1.3, "slidesToScroll": 1.3}}], "arrows": true, "autoplay": false, "pauseOnHover": true, "infinite": false, "priceFlag": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0029/669/822/assets/CERTONA/CertonaCarat_Left.svg", "prevArrowAlt": "Image Précédente", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0029/669/822/assets/CERTONA/CertonaCarat_Left.svg", "nextArrowAlt": "Image Suivante", "arrowMaxWidth": "40px", "arrowPosition": "-8px", "productTextStyles": {"productTitle": {"style": {"color": "#767676", "textAlign": "left", "fontSize": "0.75rem", "margin": "10px 13px"}}, "productPrice": {"style": {"display": "none"}}, "productSalePrice": {"style": {"display": "none"}}}, "size": {"width": "100%", "height": "150px"}, "productMarketingFlag": {"style": {"fontWeight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "maxWidth": "unset", "padding": "0px", "width": "auto"}}, "productCardImageStyles": {"width": "19vw", "margin": "0", "maxWidth": "unset", "padding": "0px"}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}}}, {"instanceName": "optly-placeholder-3", "instanceDesc": "2023-05-02 FC VisNav/VDN", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#e8e8e9", "height": 1387, "margin": "0 auto 36px", "maxWidth": "640px", "width": "100%"}, "desktop": {"backgroundColor": "#e8e8e9", "height": 657, "margin": "0 auto 24px", "maxWidth": "1920px", "width": "100%"}}, "style": {}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_hp-visnav"}, "style": {"margin": "0 auto 24px", "maxWidth": "640px", "@media only screen and (min-width:768px)": {"maxWidth": "1920px", "margin": "0 auto 36px"}}, "components": [{"instanceDesc": "vdn_01", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Magasiner les nouveautés", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_NewArrivals_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_NewArrivals_DESK.jpg"}, "linkData": {"title": "Magasiner les nouveautés", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012127,HP_VDN_1_W"}}, "ctaList": {"className": "wcd_hp-cta", "style": {"@media only screen and (max-width: 768px)": {"&.wcd_hp-cta button": {"text-align": "inherit", "white-space": "inherit", "width": "160px"}, "&.wcd_hp-cta button:hover": {"text-align": "inherit", "white-space": "inherit", "width": "160px"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Magasiner les\n nouveautés"}, "submenu": [{"text": "<PERSON>mme", "href": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012127,HP_VDN_1_W"}, {"text": "MATERNITÉ", "href": "/browse/category.do?cid=11437#pageId=0&department=136&mlink=5058,30012127,HP_VDN_1_MAT"}, {"text": "HOMME", "href": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,30012127,HP_VDN_1_M"}, {"text": "FILLE", "href": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,30012127,HP_VDN_1_G"}, {"text": "Garçon", "href": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,30012127,HP_VDN_1_B"}, {"text": "TOUTE-PETITE", "href": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,30012127,HP_VDN_1_TG"}, {"text": "TOUT-PETIT", "href": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,30012127,HP_VDN_1_TB"}, {"text": "BÉBÉ FILLE", "href": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,30012127,HP_VDN_1_BG"}, {"text": "BÉBÉ GARÇON", "href": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,30012127,HP_VDN_1_BB"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}, {"instanceDesc": "vdn_02", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "<PERSON>mme", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_Women_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_Women_DESK.jpg"}, "linkData": {"title": "<PERSON>mme", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012127,HP_VDN_W_Image"}}, "ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "<PERSON>mme"}, "submenu": [{"text": "<PERSON>mme", "href": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012127,HP_VDN_W_CTA"}, {"text": "MATERNITÉ", "href": "/browse/category.do?cid=11437#pageId=0&department=136&mlink=5058,30012127,HP_VDN_Mat_CTA"}, {"text": "GapFit", "href": "/browse/category.do?cid=1117374#pageId=0&department=136&mlink=5058,30012127,HP_VDN_GAPFIT_CTA"}, {"text": "Body", "href": "/browse/category.do?cid=1140272#pageId=0&department=136&mlink=5058,30012127,HP_VDN_BODY_CTA"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}, {"instanceDesc": "vdn_03", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "HOMME", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_Men_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_Men_DESK.jpg"}, "linkData": {"title": "HOMME", "to": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,30012127,HP_VDN_M_Image"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn", "ctas": [{"linkData": {"to": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,30012127,HP_VDN_M_CTA"}, "composableButtonData": {"children": "HOMME"}}]}}}, {"instanceDesc": "vdn_04", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "PROJET GAP", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_ProjectGap_MOB_v2.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_ProjectGap_DESK.jpg"}, "linkData": {"title": "PROJET GAP", "to": "/browse/category.do?cid=3017287#pageId=0&department=136&mlink=5058,30012127,HP_VDN_ProjectGap_W_Image"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn", "ctas": [{"linkData": {"to": "/browse/category.do?cid=3017287#pageId=0&department=136&mlink=5058,30012127,HP_VDN_ProjectGap_W_CTA"}, "composableButtonData": {"children": "PROJET GAP"}}]}}}, {"instanceDesc": "vdn_05", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "<PERSON><PERSON>", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_Girls_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_Girls_DESK.jpg"}, "linkData": {"title": "<PERSON><PERSON>", "to": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,30012127,HP_VDN_G_Image"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn", "ctas": [{"linkData": {"to": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,30012127,HP_VDN_G_CTA"}, "composableButtonData": {"children": "<PERSON><PERSON>"}}]}}}, {"instanceDesc": "vdn_06", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Garçon", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_Boys_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_Boys_DESK.jpg"}, "linkData": {"title": "Garçon", "to": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,30012127,HP_VDN_B_Image"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn", "ctas": [{"linkData": {"to": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,30012127,HP_VDN_B_CTA"}, "composableButtonData": {"children": "Garçon"}}]}}}, {"instanceDesc": "vdn_07", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Toute-petite", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_ToddlerGirl_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_ToddlerGirl_DESK.jpg"}, "linkData": {"title": "Toute-petite", "to": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,30012127,HP_VDN_TG_Image"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn", "ctas": [{"linkData": {"to": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,30012127,HP_VDN_TG_CTA"}, "composableButtonData": {"children": "Toute-petite"}}]}}}, {"instanceDesc": "vdn_08", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Tout-petit", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_ToddlerBoy_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_ToddlerBoy_DESK.jpg"}, "linkData": {"title": "Tout-petit", "to": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,30012127,HP_VDN_TB_Image"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn", "ctas": [{"linkData": {"to": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,30012127,HP_VDN_TB_CTA"}, "composableButtonData": {"children": "Tout-petit"}}]}}}, {"instanceDesc": "vdn_09", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "<PERSON><PERSON><PERSON><PERSON> fille", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_BabyGirl_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_BabyGirl_DESK.jpg"}, "linkData": {"title": "<PERSON><PERSON><PERSON><PERSON> fille", "to": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,30012127,HP_VDN_BG_Image"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn", "ctas": [{"linkData": {"to": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,30012127,HP_VDN_BG_CTA"}, "composableButtonData": {"children": "<PERSON><PERSON><PERSON><PERSON> fille"}}]}}}, {"instanceDesc": "vdn_10", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "<PERSON><PERSON><PERSON><PERSON> gar<PERSON>", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_BabyBoy_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_BabyBoy_DESK.jpg"}, "linkData": {"title": "<PERSON><PERSON><PERSON><PERSON> gar<PERSON>", "to": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,30012127,HP_VDN_BB_Image"}}, "ctaList": {"className": "wcd_hp-cta caret-vcn", "ctas": [{"linkData": {"to": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,30012127,HP_VDN_BB_CTA"}, "composableButtonData": {"children": "<PERSON><PERSON><PERSON><PERSON> gar<PERSON>"}}]}}}, {"instanceDesc": "vdn_11", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Boutique Vacances", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_VacationShop_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_VacationShop_DESK.jpg"}, "linkData": {"title": "Boutique Vacances", "to": "/browse/category.do?cid=1188847#pageId=0&department=136&mlink=5058,30012127,HP_VDN_VACATION_W_IMAGE"}}, "ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "Boutique Vacances"}, "submenu": [{"text": "<PERSON>mme", "href": "/browse/category.do?cid=1188847#pageId=0&department=136&mlink=5058,30012127,HP_VDN_VACATION_W_CTA"}, {"text": "MATERNITÉ", "href": "/browse/category.do?cid=3016031#pageId=0&department=136&mlink=5058,30012127,HP_VDN_VACATION_MAT_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=3015771#pageId=0&department=75&mlink=5058,30012127,HP_VDN_VACATION_M_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1027839#pageId=0&department=48&mlink=5058,30012127,HP_VDN_VACATION_G_CTA"}, {"text": "Garçon", "href": "/browse/category.do?cid=1027210#pageId=0&department=16&mlink=5058,30012127,HP_VDN_VACATION_B_CTA"}, {"text": "Tout<PERSON>Petit", "href": "/browse/category.do?cid=92915#pageId=0&mlink=5058,30012127,HP_VDN_VACATION_TODDLER_CTA"}, {"text": "BÉBÉ", "href": "/browse/category.do?cid=1150300#pageId=0&mlink=5058,30012127,HP_VDN_VACATION_BABY_CTA"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}, {"instanceDesc": "vdn_12", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Boutique Du Lin", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_LinenShop_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_LinenShop_DESK.jpg"}, "linkData": {"title": "Boutique Du Lin", "to": "/browse/category.do?cid=1189708#pageId=0&department=136&mlink=5058,30012127,HP_VDN_LINENSHOP_W_Image"}}, "ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "Boutique Du Lin"}, "submenu": [{"text": "<PERSON>mme", "href": "/browse/category.do?cid=1189708#pageId=0&department=136&mlink=5058,30012127,HP_VDN_LINENSHOP_W_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=3015770#pageId=0&department=75&mlink=5058,30012127,HP_VDN_LINENSHOP_M_CTA"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}]}}, {"instanceName": "dpg-banner1", "instanceDesc": "DPG Placeholder1", "experimentRunning": true, "name": "OptimizelyPlaceholder", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}}}, {"instanceName": "optly-placeholder-1", "instanceDesc": "2023-07-21 Main1 - FC BTS CAMPAIGN | KTB ", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": false, "defaultHeight": {"small": "410px", "large": "306px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 480, "margin": "0 auto", "maxWidth": "640px", "width": "100%"}, "desktop": {"backgroundColor": "#ccc", "height": 506, "margin": "0 auto", "maxWidth": "1920px", "width": "100%"}}, "style": {"margin": "0px auto 30px", "maxWidth": "640px", "position": "relative"}, "desktopStyle": {"maxWidth": "1920px", "margin": "0px auto 36px", "position": "relative", "@media only screen and (max-width:1024px)": {"margin": "0px auto 15px"}}, "components": [{"instanceDesc": "Banner Media - Two Carousel Types", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": true, "slidesToShow": 1, "speed": 300, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause Slideshow", "playAltText": "Play Slideshow", "buttonStyle": {"height": "32px", "left": "4px", "padding": "4px", "position": "absolute", "bottom": "4px", "width": "32px", "zIndex": "1"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_play--white.svg", "pauseBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_pause--white.svg"}}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "ISABELLA PORTE LA VESTE UNIVERSITAIRE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_1/FA235108_img1_MOB.jpg"}, "linkData": {"title": "GAP EN CLASSE", "to": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO1_G_IMAGE"}}, "overlay": {"alt": "GAP EN CLASSE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/933/assets/main1/FA235108_copy1_FC_MOB_1KGjeans.svg"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "OLIVER PORTE LE KAKI D’UNIFORME AMPLE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_1/FA235108_img2_MOB.jpg"}, "linkData": {"title": "GAP EN CLASSE", "to": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO1_G_IMAGE"}}, "overlay": {"alt": "GAP EN CLASSE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/933/assets/main1/FA235108_copy2_FC_MOB_2KBpants.svg"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "RHYS PORTE LA VESTE EN DENIM EMBLÉMATIQUE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_1/FA235108_img3_MOB.jpg"}, "linkData": {"title": "GAP EN CLASSE", "to": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO1_G_IMAGE"}}, "overlay": {"alt": "GAP EN CLASSE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/933/assets/main1/FA235108_copy3_FC_MOB_3KBshorts.svg"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "MAYA PORTE UNE JUPE-SHORT FLEURIE ÉTAGÉE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_1/FA235108_img4_MOB.jpg"}, "linkData": {"title": "GAP EN CLASSE", "to": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO1_G_IMAGE"}}, "overlay": {"alt": "GAP EN CLASSE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/933/assets/main1/FA235108_copy4_FC_MOB_4KGskirt.svg"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_1/FA235108_img5_MOB.jpg"}, "linkData": {"title": "GAP EN CLASSE", "to": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO1_G_IMAGE"}}, "overlay": {"alt": "GAP EN CLASSE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/933/assets/main1/FA235108_copy5_FC_MOB_5closeup.svg"}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": true, "slidesToShow": 1, "speed": 300, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause Slideshow", "playAltText": "Play Slideshow", "buttonStyle": {"height": "32px", "left": "12px", "padding": "2px", "position": "absolute", "bottom": "12px", "width": "32px", "zIndex": "1"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_play--white.svg", "pauseBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_pause--white.svg"}}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "ISABELLA PORTE LA VESTE UNIVERSITAIRE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_1/FA235108_img1_DESK.jpg"}, "linkData": {"title": "GAP EN CLASSE", "to": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO1_G_IMAGE"}}, "overlay": {"alt": "GAP EN CLASSE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/933/assets/main1/FA235108_copy1_FC_DESK_1KGjeans.svg"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "OLIVER PORTE LE KAKI D’UNIFORME AMPLE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_1/FA235108_img2_DESK.jpg"}, "linkData": {"title": "GAP EN CLASSE", "to": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO1_G_IMAGE"}}, "overlay": {"alt": "GAP EN CLASSE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/933/assets/main1/FA235108_copy2_FC_DESK_2KBpants.svg"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "RHYS PORTE LA VESTE EN DENIM EMBLÉMATIQUE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_1/FA235108_img3_DESK.jpg"}, "linkData": {"title": "GAP EN CLASSE", "to": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO1_G_IMAGE"}}, "overlay": {"alt": "GAP EN CLASSE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/933/assets/main1/FA235108_copy3_FC_DESK_3KBshorts.svg"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "MAYA PORTE UNE JUPE-SHORT FLEURIE ÉTAGÉE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_1/FA235108_img4_DESK.jpg"}, "linkData": {"title": "GAP EN CLASSE", "to": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO1_G_IMAGE"}}, "overlay": {"alt": "GAP EN CLASSE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/933/assets/main1/FA235108_copy4_FC_DESK_4KGskirt.svg"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_1/FA235108_img5_DESK.jpg"}, "linkData": {"title": "GAP EN CLASSE", "to": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO1_G_IMAGE"}}, "overlay": {"alt": "GAP EN CLASSE", "srcUrl": "/Asset_Archive/GPWeb/content/0030/014/933/assets/main1/FA235108_copy5_FC_DESK_5closeup.svg"}}}]}}]}}}}, {"instanceDesc": "CTA mob+desk", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "10px 13px 0px", "@media only screen and (min-width:768px)": {"alignItems": "flex-start", "flexDirection": "row", "justifyContent": "space-between", "padding": "12px 12px 0px 12px"}, "@media only screen and (min-width:1280px)": {"padding": "12px 12px 0px 12px"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"color": "#767676", "fontSize": "17px", "fontWeight": "400", "padding": "10px 0px 0px", "@media only screen and (min-width: 768px)": {"display": "flex", "whiteSpace": "pre", "fontSize": "min(max(16px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 40px)", "marginRight": "8px", "padding": "0"}}, "components": [{"type": "builtin", "name": "div", "data": {"components": ["Les favoris rétro. Les tendances actuelles. Des \nnouveautés adaptées à leur style et à leurs \nbesoins."]}}]}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0", "@media only screen and (min-width:768px)": {"alignItems": "end", "flexDirection": "row", "justifyContent": "flex-end", "padding": "0", "width": "75%"}, "@media only screen and (min-width:1280px)": {"paddingTop": "0"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"fontSize": "16px", "fontWeight": "400", "padding": "12px 0 14px", "whiteSpace": "pre", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(12px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 24px)", "marginRight": "8px", "padding": "0"}}, "components": [{"instanceDesc": "CTA Block Headline", "name": "div", "type": "builtin", "data": {"style": {"fontSize": "13px", "fontWeight": "400", "padding": "10px 6px 8px 0px", "textAlign": "left", "textTransform": "uppercase", "color": "#767676", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(10px, calc(0.625rem + ((1vw - 7.68px) * 1.2153))), 18px)", "marginRight": "8px", "padding": "6px 0 0", "textAlign": "right"}}, "components": [""]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta two-column-at-mob caret outline", "style": {"&.wcd_hp-cta.caret a": {"&:last-child": {"width": "100%"}}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO1_G_CTA"}, "composableButtonData": {"children": "<PERSON><PERSON>"}}, {"linkData": {"to": "/browse/category.do?cid=1161642#pageId=0&department=16&mlink=5058,30014932,HP_HERO1_B_CTA"}, "composableButtonData": {"children": "Garçon"}}, {"linkData": {"to": "/browse/category.do?cid=1181268#pageId=0&mlink=5058,30014932,HP_HERO1_TODDLER_CTA"}, "composableButtonData": {"children": "Tout<PERSON>Petit"}}]}}}]}}]}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "10px 13px 0px", "@media only screen and (min-width:768px)": {"flexDirection": "row", "justifyContent": "space-between", "padding": "12px 12px 0px 20px"}, "@media only screen and (max-width:1024px)": {"alignItems": "start"}, "@media only screen and (min-width:1280px)": {"alignItems": "start", "padding": "20px 12px 0px 20px"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"color": "#767676", "fontSize": "17px", "fontWeight": "400", "padding": "0 0 10px", "@media only screen and (min-width: 768px)": {"display": "flex", "whiteSpace": "pre", "fontSize": "min(max(16px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 40px)", "marginRight": "8px", "padding": "0"}}, "components": [{"type": "builtin", "name": "div", "data": {"style": {"whiteSpace": "pre"}, "components": ["Les favoris rétro. Les tendances actuelles. Des \nnouveautés adaptées à leur style et à leurs \nbesoins."]}}]}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0", "@media only screen and (min-width:768px)": {"alignItems": "end", "flexDirection": "row", "justifyContent": "flex-end", "padding": "0", "width": "51%", "flexWrap": "wrap !important"}, "@media only screen and (min-width:1185px)": {"width": "67%", "paddingTop": "0", "flexWrap": "nowrap !important"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"fontSize": "16px", "fontWeight": "400", "padding": "12px 0 14px", "whiteSpace": "pre", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(12px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 24px)", "marginRight": "8px", "padding": "0", "flexWrap": "wrap !important"}, "@media only screen and (min-width:1351px)": {"flexWrap": "nowrap !important"}}, "components": [{"instanceDesc": "CTA Block Headline", "name": "div", "type": "builtin", "data": {"style": {"fontSize": "13px", "fontWeight": "400", "padding": "10px 6px 8px 16px", "textAlign": "left", "textTransform": "uppercase", "color": "#767676", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(10px, calc(0.625rem + ((1vw - 7.68px) * 1.2153))), 18px)", "marginRight": "8px", "padding": "10px 0 10px", "textAlign": "right"}}, "components": [""]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta outline", "style": {"&.wcd_hp-cta": {"justifyContent": "normal"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO1_G_CTA"}, "composableButtonData": {"children": "<PERSON><PERSON>"}}, {"linkData": {"to": "/browse/category.do?cid=1161642#pageId=0&department=16&mlink=5058,30014932,HP_HERO1_B_CTA"}, "composableButtonData": {"children": "Garçon"}}, {"linkData": {"to": "/browse/category.do?cid=1181268#pageId=0&mlink=5058,30014932,HP_HERO1_TODDLER_CTA"}, "composableButtonData": {"children": "Tout<PERSON>Petit"}}]}}}]}}]}}]}}]}}}}]}}, {"instanceName": "optly-placeholder-2", "instanceDesc": "2023-07-21 VCN/Main2 FC BTS Checklist", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#e8e8e9", "height": 1387, "margin": "0 auto 36px", "maxWidth": "640px", "width": "100%"}, "desktop": {"backgroundColor": "#e8e8e9", "height": 657, "margin": "0 auto 24px", "maxWidth": "1920px", "width": "100%"}}, "style": {}, "components": [{"name": "TextHeadline", "type": "siteide", "data": {"text": "LISTE D’ACHATS DE LA RENTRÉE : MODÈLES À PARTIR DE 12 $", "defaultHeight": "34px", "className": {"mobile": {}, "desktop": {}}, "style": {"color": "#767676", "mobile": {"fontSize": "25px", "color": "#767676", "padding": "0 13px 10px"}, "desktop": {"fontSize": "50px", "color": "#767676", "maxWidth": "1920px", "margin": "auto", "@media only screen and (min-width: 768px)": {"padding": "0 12px 10px 20px"}, "@media only screen and (min-width: 1280px)": {"padding": "0 12px 10px 20px"}}}}}, {"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_hp-visnav"}, "style": {"margin": "0 auto 24px", "maxWidth": "640px", "&.wcd_hp-visnav>div:hover img": {"opacity": "100%"}, "@media only screen and (min-width:768px)": {"maxWidth": "1920px", "margin": "0 auto 36px"}}, "components": [{"instanceDesc": "Tile-1", "name": "div", "type": "builtin", "data": {"components": [{"instanceDesc": "Tile-1 Image", "name": "a", "type": "builtin", "data": {"props": {"href": "/browse/category.do?cid=6276#pageId=0&department=48&mlink=5058,30014932,HP_VCN_1_G", "title": "JEANS"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "JEANS", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_<PERSON><PERSON>_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_<PERSON><PERSON>_Default_DESK.jpg"}, "backgroundHover": {"altText": "", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_<PERSON><PERSON>_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_<PERSON><PERSON>_Hover_DESK.jpg"}}}]}}, {"instanceDesc": "Tile-1 CTA", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "JEANS"}, "submenu": [{"text": "FILLE", "href": "/browse/category.do?cid=6276#pageId=0&department=48&mlink=5058,30014932,HP_VCN_1_G"}, {"text": "Garçon", "href": "/browse/category.do?cid=6189#pageId=0&department=16&mlink=5058,30014932,HP_VCN_1_B"}, {"text": "TOUTE-PETITE", "href": "/browse/category.do?cid=6427#pageId=0&department=165&mlink=5058,30014932,HP_VCN_1_TG"}, {"text": "TOUT-PETIT", "href": "/browse/category.do?cid=6359#pageId=0&department=166&mlink=5058,30014932,HP_VCN_1_TB"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}, {"instanceDesc": "Tile-2", "name": "div", "type": "builtin", "data": {"components": [{"instanceDesc": "Tile-2 Image", "name": "a", "type": "builtin", "data": {"props": {"href": "/browse/category.do?cid=1070923#pageId=0&department=16&mlink=5058,30014932,HP_VCN_2_B", "title": "T-SHIRTS"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "T-SHIRTS", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Tees_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Te<PERSON>_Default_DESK.jpg"}, "backgroundHover": {"altText": "", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Tees_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Te<PERSON>_Hover_DESK.jpg"}}}]}}, {"instanceDesc": "Tile-2 CTA", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "T-SHIRTS"}, "submenu": [{"text": "FILLE", "href": "/browse/category.do?cid=14417#pageId=0&department=48&mlink=5058,30014932,HP_VCN_2_G"}, {"text": "Garçon", "href": "/browse/category.do?cid=1070923#pageId=0&department=16&mlink=5058,30014932,HP_VCN_2_B"}, {"text": "TOUTE-PETITE", "href": "/browse/category.do?cid=6444#pageId=0&department=165&mlink=5058,30014932,HP_VCN_2_TG"}, {"text": "TOUT-PETIT", "href": "/browse/category.do?cid=1016096#pageId=0&department=166&mlink=5058,30014932,HP_VCN_2_TB"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}, {"instanceDesc": "Tile-3 Desktop / Tile-4 Mobile", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {}, "components": [{"instanceDesc": "Tile-4 Mobile", "name": "div", "type": "builtin", "data": {"components": [{"instanceDesc": "Tile-4 Image Mobile", "name": "a", "type": "builtin", "data": {"props": {"href": "/browse/category.do?cid=6300#pageId=0&department=48&mlink=5058,30014932,HP_VCN_3_G", "title": "ROBES ET JUPES"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "ROBES ET JUPES", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_DressesSkirts_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_DressesSkirts_Default_DESK.jpg"}, "backgroundHover": {"altText": "", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_DressesSkirts_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_DressesSkirts_Hover_DESK.jpg"}}}]}}, {"instanceDesc": "Tile-4 CTA", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "ROBES ET JUPES"}, "submenu": [{"text": "FILLE", "href": "/browse/category.do?cid=6300#pageId=0&department=48&mlink=5058,30014932,HP_VCN_3_G"}, {"text": "TOUTE-PETITE", "href": "/browse/category.do?cid=6436#pageId=0&department=165&mlink=5058,30014932,HP_VCN_3_B"}]}}]}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Tile-3", "name": "div", "type": "builtin", "data": {"components": [{"instanceDesc": "Tile-3 Image", "name": "a", "type": "builtin", "data": {"props": {"href": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,30014932,HP_VCN_3_B", "title": "SHORTS"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "SHORTS", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Shorts_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Shorts_Default_DESK.jpg"}, "backgroundHover": {"altText": "", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Shorts_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Shorts_Hover_DESK.jpg"}}}]}}, {"instanceDesc": "Tile-3 CTA", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "SHORTS"}, "submenu": [{"text": "FILLE", "href": "/browse/category.do?cid=14403#pageId=0&department=48&mlink=5058,30014932,HP_VCN_3_G"}, {"text": "Garçon", "href": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,30014932,HP_VCN_3_B"}, {"text": "TOUTE-PETITE", "href": "/browse/category.do?cid=1121815#pageId=0&department=165&mlink=5058,30014932,HP_VCN_3_TG"}, {"text": "TOUT-PETIT", "href": "/browse/category.do?cid=1121839#pageId=0&department=166&mlink=5058,30014932,HP_VCN_3_TB"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}]}}}}, {"instanceDesc": "Tile-4 Desktop / Tile-3 Mobile", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {}, "components": [{"instanceDesc": "Tile-3 Mobile", "name": "div", "type": "builtin", "data": {"components": [{"instanceDesc": "Tile-3 Image Mobile", "name": "a", "type": "builtin", "data": {"props": {"href": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,30014932,HP_VCN_4_B", "title": "SHORTS"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "SHORTS", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Shorts_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Shorts_Default_DESK.jpg"}, "backgroundHover": {"altText": "", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Shorts_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Shorts_Hover_DESK.jpg"}}}]}}, {"instanceDesc": "Tile-3 CTA", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "SHORTS"}, "submenu": [{"text": "FILLE", "href": "/browse/category.do?cid=14403#pageId=0&department=48&mlink=5058,30014932,HP_VCN_4_G"}, {"text": "Garçon", "href": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,30014932,HP_VCN_4_B"}, {"text": "TOUTE-PETITE", "href": "/browse/category.do?cid=1121815#pageId=0&department=165&mlink=5058,30014932,HP_VCN_4_TG"}, {"text": "TOUT-PETIT", "href": "/browse/category.do?cid=1121839#pageId=0&department=166&mlink=5058,30014932,HP_VCN_4_TB"}]}}]}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Tile-4", "name": "div", "type": "builtin", "data": {"components": [{"instanceDesc": "Tile-4 Image", "name": "a", "type": "builtin", "data": {"props": {"href": "/browse/category.do?cid=6300#pageId=0&department=48&mlink=5058,30014932,HP_VCN_4_G", "title": "ROBES ET JUPES"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "ROBES ET JUPES", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_DressesSkirts_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_DressesSkirts_Default_DESK.jpg"}, "backgroundHover": {"altText": "", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_DressesSkirts_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_DressesSkirts_Hover_DESK.jpg"}}}]}}, {"instanceDesc": "Tile-4 CTA", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "ROBES ET JUPES"}, "submenu": [{"text": "FILLE", "href": "/browse/category.do?cid=6300#pageId=0&department=48&mlink=5058,30014932,HP_VCN_4_G"}, {"text": "TOUTE-PETITE", "href": "/browse/category.do?cid=6436#pageId=0&department=165&mlink=5058,30014932,HP_VCN_4_B"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}]}}}}, {"instanceDesc": "Tile-5 Desktop / Tile-6 Mobile", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {}, "components": [{"instanceDesc": "Tile-6 Mobile", "name": "div", "type": "builtin", "data": {"components": [{"instanceDesc": "Tile-6 Image Mobile", "name": "a", "type": "builtin", "data": {"props": {"href": "/browse/category.do?cid=6187#pageId=0&department=16&mlink=5058,30014932,HP_VCN_5_B", "title": "LEGGINGS ET PANTALONS"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "LEGGINGS ET PANTALONS", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_LeggingsPants_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_LeggingsPants_Default_DESK.jpg"}, "backgroundHover": {"altText": "", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_LeggingsPants_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_LeggingsPants_Hover_DESK.jpg"}}}]}}, {"instanceDesc": "Tile-6 CTA", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"@media only screen and (max-width: 768px)": {"&.wcd_hp-cta button": {"text-align": "inherit", "white-space": "inherit", "width": "140px"}, "&.wcd_hp-cta button:hover": {"text-align": "inherit", "white-space": "inherit", "width": "140px"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "PANTALONS"}, "submenu": [{"text": "FILLE", "href": "/browse/category.do?cid=13148#pageId=0&department=48&mlink=5058,30014932,HP_VCN_5_G"}, {"text": "Garçon", "href": "/browse/category.do?cid=6187#pageId=0&department=16&mlink=5058,30014932,HP_VCN_5_B"}, {"text": "TOUTE-PETITE ", "href": "/browse/category.do?cid=12378#pageId=0&department=165&mlink=5058,30014932,HP_VCN_5_TG"}, {"text": "TOUT-PETIT", "href": "/browse/category.do?cid=1016106#pageId=0&department=166&mlink=5058,30014932,HP_VCN_5_TB"}]}}]}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Tile-5", "name": "div", "type": "builtin", "data": {"components": [{"instanceDesc": "Tile-5 Image", "name": "a", "type": "builtin", "data": {"props": {"href": "/browse/category.do?cid=1117991#pageId=0&department=16&mlink=5058,30014932,HP_VCN_5_B", "title": "CHANDAILS EN COTON OUATÉ"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "CHANDAILS EN COTON OUATÉ", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Sweatshirts_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Sweatshirts_Default_DESK.jpg"}, "backgroundHover": {"altText": "", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Sweatshirts_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Sweatshirts_Hover_DESK.jpg"}}}]}}, {"instanceDesc": "Tile-5 CTA", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"@media only screen and (max-width: 768px)": {"&.wcd_hp-cta button": {"text-align": "inherit", "white-space": "inherit", "width": "150px"}, "&.wcd_hp-cta button:hover": {"text-align": "inherit", "white-space": "inherit", "width": "150px"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "CHANDAILS EN COTON OUATÉ"}, "submenu": [{"text": "FILLE", "href": "/browse/category.do?cid=1056270#pageId=0&department=48&mlink=5058,30014932,HP_VCN_5_G"}, {"text": "Garçon", "href": "/browse/category.do?cid=1117991#pageId=0&department=16&mlink=5058,30014932,HP_VCN_5_B"}, {"text": "TOUTE-PETITE", "href": "/browse/category.do?cid=17846#pageId=0&department=165&mlink=5058,30014932,HP_VCN_5_TG"}, {"text": "TOUT-PETIT", "href": "/browse/category.do?cid=1016107#pageId=0&department=166&mlink=5058,30014932,HP_VCN_5_TB"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}]}}}}, {"instanceDesc": "Tile-6 Desktop / Tile-5 Mobile", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {}, "components": [{"instanceDesc": "Tile-5 Mobile", "name": "div", "type": "builtin", "data": {"components": [{"instanceDesc": "Tile-5 Image Mobile", "name": "a", "type": "builtin", "data": {"props": {"href": "/browse/category.do?cid=1117991#pageId=0&department=16&mlink=5058,30014932,HP_VCN_6_B", "title": "CHANDAILS EN COTON OUATÉ"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "CHANDAILS EN COTON OUATÉ", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Sweatshirts_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Sweatshirts_Default_DESK.jpg"}, "backgroundHover": {"altText": "", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Sweatshirts_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Sweatshirts_Hover_DESK.jpg"}}}]}}, {"instanceDesc": "Tile-5 CTA", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"@media only screen and (max-width: 768px)": {"&.wcd_hp-cta button": {"text-align": "inherit", "white-space": "inherit", "width": "150px"}, "&.wcd_hp-cta button:hover": {"text-align": "inherit", "white-space": "inherit", "width": "150px"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "CHANDAILS EN COTON OUATÉ"}, "submenu": [{"text": "FILLE", "href": "/browse/category.do?cid=1056270#pageId=0&department=48&mlink=5058,30014932,HP_VCN_6_G"}, {"text": "Garçon", "href": "/browse/category.do?cid=1117991#pageId=0&department=16&mlink=5058,30014932,HP_VCN_6_B"}, {"text": "TOUTE-PETITE", "href": "/browse/category.do?cid=17846#pageId=0&department=165&mlink=5058,30014932,HP_VCN_6_TG"}, {"text": "TOUT-PETIT", "href": "/browse/category.do?cid=1016107#pageId=0&department=166&mlink=5058,30014932,HP_VCN_6_TB"}]}}]}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Tile-6", "name": "div", "type": "builtin", "data": {"components": [{"instanceDesc": "Tile-6 Image", "name": "a", "type": "builtin", "data": {"props": {"href": "/browse/category.do?cid=6187#pageId=0&department=16&mlink=5058,30014932,HP_VCN_6_B", "title": "LEGGINGS ET PANTALONS"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "LEGGINGS ET PANTALONS", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_LeggingsPants_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_LeggingsPants_Default_DESK.jpg"}, "backgroundHover": {"altText": "", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_LeggingsPants_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_LeggingsPants_Hover_DESK.jpg"}}}]}}, {"instanceDesc": "Tile-6 CTA", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"@media only screen and (max-width: 768px)": {"&.wcd_hp-cta button": {"text-align": "inherit", "white-space": "inherit", "width": "140px"}, "&.wcd_hp-cta button:hover": {"text-align": "inherit", "white-space": "inherit", "width": "140px"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "PANTALONS"}, "submenu": [{"text": "FILLE", "href": "/browse/category.do?cid=13148#pageId=0&department=48&mlink=5058,30014932,HP_VCN_6_G"}, {"text": "Garçon", "href": "/browse/category.do?cid=6187#pageId=0&department=16&mlink=5058,30014932,HP_VCN_6_B"}, {"text": "TOUTE-PETITE ", "href": "/browse/category.do?cid=12378#pageId=0&department=165&mlink=5058,30014932,HP_VCN_6_TG"}, {"text": "TOUT-PETIT", "href": "/browse/category.do?cid=1016106#pageId=0&department=166&mlink=5058,30014932,HP_VCN_6_TB"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}]}}}}, {"instanceDesc": "Tile-7", "name": "div", "type": "builtin", "data": {"components": [{"instanceDesc": "Tile-7 Image", "name": "a", "type": "builtin", "data": {"props": {"href": "/browse/category.do?cid=1061822#pageId=0&department=48&mlink=5058,30014932,HP_VCN_7_G", "title": "UNIFORMES"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "UNIFORMES", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Uniforms_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Uniforms_Default_DESK.jpg"}, "backgroundHover": {"altText": "", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Uniforms_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Uniforms_Hover_DESK.jpg"}}}]}}, {"instanceDesc": "Tile-7 CTA", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "UNIFORMES"}, "submenu": [{"text": "FILLE", "href": "/browse/category.do?cid=1061822#pageId=0&department=48&mlink=5058,30014932,HP_VCN_7_G"}, {"text": "Garçon", "href": "/browse/category.do?cid=1060990#pageId=0&department=16&mlink=5058,30014932,HP_VCN_7_B"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}, {"instanceDesc": "Tile-8", "name": "div", "type": "builtin", "data": {"components": [{"instanceDesc": "Tile-8 Image", "name": "a", "type": "builtin", "data": {"props": {"href": "/browse/category.do?cid=56233#pageId=0&department=48&mlink=5058,30014932,HP_VCN_8_G", "title": "SACS À DOS"}, "components": [{"type": "sitewide", "name": "HoverImage", "data": {"background": {"altText": "SACS À DOS", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Backpacks_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Backpacks_Default_DESK.jpg"}, "backgroundHover": {"altText": "", "img": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Backpacks_MOB.jpg", "desktopImg": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_2/FA235110_FA235328_Backpacks_Hover_DESK.jpg"}}}]}}, {"instanceDesc": "Tile-8 CTA", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "SACS À DOS"}, "submenu": [{"text": "FILLE", "href": "/browse/category.do?cid=56233#pageId=0&department=48&mlink=5058,30014932,HP_VCN_8_G"}, {"text": "Garçon", "href": "/browse/category.do?cid=96875#pageId=0&department=16&mlink=5058,30014932,HP_VCN_8_B"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}]}}]}}, {"instanceName": "042123_Certona_1", "instanceDesc": "042123_Certona_1", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "2rem", "large": "3rem"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"margin": "0 auto", "flexDirection": "column", "padding": "0", "maxWidth": "1920px"}, "components": [{"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"lazy": true, "style": {"width": "100%", "@media only screen and (max-width:768px)": {"padding": "0px 0 36px", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}, "@media only screen and (min-width:769px)": {"padding": "0px 0 36px", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}}, "props": {"style": {"width": "100%"}, "className": "fullBleedCertona"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome2_rr", "displayTitle": true, "fullWidth": true, "certonaTitle": {"title": "Nos nouveautés suivent toujours les règles de la mode.", "style": {"mobile": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#767676", "lineHeight": "1.6rem", "fontSize": "4.1vw", "textAlign": "left", "padding": "0px 0 0 13px", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}, "desktop": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#767676", "lineHeight": "1.6rem", "fontSize": "1.1vw", "textAlign": "left", "padding": "0px 0 0 1.5%", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}}}, "layout": "carousel", "centerMode": false, "useMobileConfig": true, "defaultslidesToShowSlick": 4, "defaultslidesToScrollSlick": 4, "resslidesToShowSlick": 4, "resslidesToScrollSlick": 4, "displayPlayPauseButton": false, "responsive": [{"breakpoint": 1350, "settings": {"slidesToShow": 4, "slidesToScroll": 4}}, {"breakpoint": 768, "settings": {"slidesToShow": 1.3, "slidesToScroll": 1.3}}], "arrows": true, "autoplay": false, "pauseOnHover": true, "infinite": false, "priceFlag": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0029/669/822/assets/CERTONA/CertonaCarat_Left.svg", "prevArrowAlt": "Image Précédente", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0029/669/822/assets/CERTONA/CertonaCarat_Left.svg", "nextArrowAlt": "Image Suivante", "arrowMaxWidth": "40px", "arrowPosition": "-8px", "productTextStyles": {"productTitle": {"style": {"color": "#767676", "textAlign": "left", "fontSize": "0.75rem", "margin": "10px 13px"}}, "productPrice": {"style": {"display": "none"}}, "productSalePrice": {"style": {"display": "none"}}}, "size": {"width": "100%", "height": "150px"}, "productMarketingFlag": {"style": {"fontWeight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "maxWidth": "unset", "padding": "0px", "width": "auto"}}, "productCardImageStyles": {"width": "19vw", "margin": "0", "maxWidth": "unset", "padding": "0px"}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}}}, {"instanceName": "optly-placeholder-4", "instanceDesc": "2023-07-21 MAIN3 FC KTB BTS CAMPAIGN", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 480, "margin": "0 auto", "maxWidth": "640px", "width": "100%"}, "desktop": {"backgroundColor": "#ccc", "height": 306, "margin": "0 auto", "maxWidth": "1920px", "width": "100%"}}, "style": {"margin": "0 auto 36px", "maxWidth": "640px", "position": "relative"}, "desktopStyle": {"maxWidth": "1920px", "paddingBottom": "8px"}, "components": [{"instanceDesc": "", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 0", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "VideoComponent", "type": "sitewide", "data": {"isVisible": {"small": false, "large": true}, "posterImage": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_4/FA235109_BTS_Video_Static_Fallback_Mob.jpg", "url": "https://vimeo.com/840120745?loop=1", "fallbackImage": {"alt": "Video Failed to Load", "src": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_4/FA235109_BTS_Video_Static_Fallback_Mob.jpg"}, "controls": true, "width": "100%", "height": "85vw", "light": false, "loop": true, "muted": true, "lazy": true, "playing": true, "playsinline": true, "containerStyle": {"desktop": {"height": "100%", "paddingTop": "0", "position": "relative", "width": "100%"}}, "analytics": {"onPlayer": {"event_name": "video_play_click", "video_name_play": "Back-To-School Styles"}}}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Video", "name": "div", "type": "builtin", "tileStyle": {"desktop": {"position": "relative", "width": "100%"}}, "data": {"props": {"flexDirection": "column", "className": ""}, "components": [{"name": "VideoComponent", "type": "sitewide", "data": {"isVisible": {"small": false, "large": true}, "posterImage": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_4/FA235109_BTS_Video_Static_Fallback_Desk.jpg", "url": "https://vimeo.com/840121004?loop=1", "fallbackImage": {"alt": "Video Failed to Load", "src": "/Asset_Archive/GPWeb/content/0030/014/786/assets/UNREC/UNREC_4/FA235109_BTS_Video_Static_Fallback_Desk.jpg"}, "controls": true, "width": "100%", "height": "auto", "light": false, "loop": true, "muted": true, "lazy": true, "playing": true, "playsinline": true, "playerStyles": {"position": "absolute", "top": "0", "bottom": "0", "width": "100%"}, "containerStyle": {"desktop": {"position": "relative", "paddingTop": "42%"}}, "analytics": {"onPlayer": {"event_name": "video_play_click", "video_name_play": "Back-To-School Styles"}}}}]}}]}}}}, {"instanceName": "CTA Sub Message & CTAs Lockup LayoutComponent", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 auto", "maxWidth": "1920px"}, "components": [{"instanceDesc": "Mobile CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "10px 13px 0", "@media only screen and (min-width:768px)": {"align-items": "center", "flexDirection": "row", "justifyContent": "space-between", "padding": "20px 20px 0px"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"color": "#999", "fontSize": "30px", "fontWeight": "400", "padding": "0 0 0", "whiteSpace": "normal", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(35px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 40px)", "marginRight": "8px", "padding": "0", "whiteSpace": "normal"}}, "components": [""]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta full-width", "ctas": [{"buttonDropdownData": {"heading": {"text": "Magasiner pour la rentrée scolaire"}, "submenu": [{"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO3_G_CTA"}, {"text": "Garçon", "href": "/browse/category.do?cid=1161642#pageId=0&department=16&mlink=5058,30014932,HP_HERO3_B_CTA"}, {"text": "TOUT-PETIT", "href": "/browse/category.do?cid=1181268#pageId=0&mlink=5058,30014932,HP_HERO3_TODDLER_CTA"}]}}]}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "10px 13px 0px", "@media only screen and (min-width:768px)": {"flexDirection": "row", "justifyContent": "space-between", "padding": "12px 12px 0px 20px"}, "@media only screen and (max-width:1024px)": {"alignItems": "baseline"}, "@media only screen and (min-width:1280px)": {"alignItems": "center", "padding": "20px 12px 0px 20px"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"color": "#999", "fontSize": "30px", "fontWeight": "400", "padding": "0 0 10px", "whiteSpace": "pre", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(35px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 40px)", "marginRight": "8px", "padding": "0", "whiteSpace": "normal"}}, "components": [""]}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0", "@media only screen and (min-width:768px)": {"alignItems": "end", "flexDirection": "row", "justifyContent": "flex-end", "padding": "0", "width": "80%", "flexWrap": "wrap !important"}, "@media only screen and (min-width:1240px)": {"width": "50%", "paddingTop": "0", "flexWrap": "nowrap !important"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"fontSize": "16px", "fontWeight": "400", "padding": "12px 0 14px", "whiteSpace": "pre", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(12px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 24px)", "marginRight": "8px", "padding": "0", "flexWrap": "wrap !important"}, "@media only screen and (min-width:1240px)": {"flexWrap": "nowrap !important"}}, "components": [{"instanceDesc": "CTA Block Headline", "name": "div", "type": "builtin", "data": {"style": {"fontSize": "13px", "fontWeight": "400", "padding": "10px 6px 8px 16px", "textAlign": "left", "textTransform": "uppercase", "color": "#767676", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(10px, calc(0.625rem + ((1vw - 7.68px) * 1.2153))), 18px)", "marginRight": "8px", "padding": "10px 0 10px", "textAlign": "right"}}, "components": [""]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta outline", "style": {"&.wcd_hp-cta": {"justifyContent": "normal"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Magasiner pour la rentrée scolaire"}, "submenu": [{"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1151802#pageId=0&department=48&mlink=5058,30014932,HP_HERO3_G_CTA"}, {"text": "Garçon", "href": "/browse/category.do?cid=1161642#pageId=0&department=16&mlink=5058,30014932,HP_HERO3_B_CTA"}, {"text": "TOUT-PETIT", "href": "/browse/category.do?cid=1181268#pageId=0&mlink=5058,30014932,HP_HERO3_TODDLER_CTA"}]}}]}}}]}}]}}]}}]}}}}]}}, {"instanceName": "dpg-banner2", "instanceDesc": "DPG-placeholder-2", "experimentRunning": true, "name": "OptimizelyPlaceholder", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "0", "large": "0"}}}]}, "sitewide": {"edfslarge": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-large-french", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"shouldWaitForOptimizely": "true", "lazy": false, "isVisible": {"large": true, "small": false}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto"}, "components": [{"instanceDesc": "WCD HP CSS Modifications", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>.universal-nav .sitewide-oyuy8v{font-size:.75em}</style>"}}, {"type": "sitewide", "name": "MktEdfsLarge", "tileStyle": {"display": "flex", "height": "40px", "alignItems": "center", "margin-top": "1px", "textTransform": "uppercase"}, "data": {"lazy": false, "experimentRunning": false, "defaultData": {"textStrong": "", "text": "Les membres du programme de récompenses peuvent bénéficier de la livraison gratuite", "detailsLink": "Détails"}, "modalTitle": "EXPÉDITION ET RETOURS", "modalUrl": "/customerService/info.do?cid=2019", "modalCloseButtonAriaLabel": "<PERSON><PERSON><PERSON> Fenêtre Contextuelle", "signInCta": {"text": "Ouvrir une session ou s’inscrire", "path": "/my-account/sign-in", "style": {}}}}]}}}}]}}, "edfssmall": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-small-french", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"shouldWaitForOptimizely": "true", "lazy": false, "defaultHeight": {"large": "80px", "small": "50px"}, "isVisible": {"large": false, "small": true}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto"}, "components": [{"instanceDesc": "WCD HP CSS Modifications", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>#sitewide-app > header > div:nth-child(3) > div > div > div > div > div > button {text-transform:uppercase;font-size:1em}</style>"}}, {"type": "sitewide", "name": "MktEdfsSmall", "data": {"lazy": false, "experimentRunning": false, "styles": {"headline": {}, "detailsButton": {}}, "defaultData": {"textStrong": "", "text": "Les membres du programme de récompenses peuvent bénéficier de la livraison gratuite", "detailsLink": "Détails"}, "modalTitle": "EXPÉDITION ET RETOURS", "modalUrl": "/customerService/info.do?cid=2019", "modalCloseButtonAriaLabel": "<PERSON><PERSON><PERSON> Fenêtre Contextuelle", "signInCta": {"text": "Ouvrir une session ou s’inscrire", "path": "/my-account/sign-in", "style": {}}}}]}}}}]}}, "footer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "sitewide", "components": [{"name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "582px", "large": "435px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "width": "100%"}, "components": [{"name": "Footer", "type": "sitewide", "data": {"socialLinks": [{"to": "https://www.facebook.com/gap/", "text": "<PERSON><PERSON><PERSON>"}], "emailRegistration": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "fr_CA", "data": {"html": "<h3 class=\"wcd_footer_h1 uppercase\">Restez à l’affût</h3>"}}, "emailPlaceholderText": "<PERSON><PERSON> votre courriel", "submitButtonText": "S'inscrire", "submitButtonOptions": {"mobile": {"className": "wcd_footer_cta"}, "desktop": {"className": "wcd_footer_cta"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "fr_CA", "data": {"html": "<p class=\"wcd_footer_copy legal\"><span>*</span> Offre réservée aux nouveaux abonnés. Rabais sur les articles à prix courant seulement.<br><a onclick=\"return contentItemLink(this,'','CS_Footer_PrivacyPolicy');\" href=\"https://www.gapinc.com/fr-ca/consumer-privacy-policy\" target=\"_blank\" class=\"uppercase\">Politique de Confidentialité</a></p>"}}}, "marketingBannerLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"instanceName": "footer_overrides", "instanceDesc": "<PERSON><PERSON> Footer Overrides", "name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "fr_CA", "data": {"html": "<style>#sitewide-footer,#sitewide-footer button,#sitewide-footer input,#sitewide-footer select,#sitewide-footer textarea{font-family:'Gap Sans',Helvetica,Arial,Roboto,sans-serif}#sitewide-footer{background-color:#767676;color:#fff}.gap-footer *{box-sizing:border-box}.gap-footer .nowrap{white-space:nowrap}.gap-footer .uppercase{text-transform:uppercase}.gap-footer sup{font-size:1em;line-height:0;vertical-align:baseline}.gap-footer .wcd_footer_h1{font-size:14px;font-weight:500;line-height:1.125;margin-bottom:.25em}.gap-footer .wcd_footer_copy:not(:last-child){margin-bottom:1.125em}.gap-footer .wcd_footer_copy.legal{font-size:10px}.gap-footer .wcd_footer_copy a{text-decoration:underline}.gap-footer .wcd_footer_cta a,.gap-footer .wcd_footer_cta button,.gap-footer a.wcd_footer_cta,.gap-footer button.wcd_footer_cta{-ms-flex-align:center;align-items:center;background-color:#fff;border-width:0;color:#767676;font-size:14px;font-weight:500;height:32px;-ms-flex-pack:center;justify-content:center;letter-spacing:0;padding-left:16px;padding-right:16px;text-transform:uppercase}.gap-footer .wcd_footer_cta a:hover,.gap-footer .wcd_footer_cta button:hover,.gap-footer a.wcd_footer_cta:hover,.gap-footer button.wcd_footer_cta:hover{background-color:#fff;border-width:0;color:#2b2b2b}.gap-footer .wcd_footer_cta{display:-ms-flexbox;display:flex}.gap-footer .wcd_footer_cta.full-width{width:100%}.gap-footer .wcd_footer_cta.full-width a,.gap-footer .wcd_footer_cta.full-width button{width:100%}.gap-footer .wcd_footer_cta.full-width a:not(:first-child),.gap-footer .wcd_footer_cta.full-width button:not(:first-child){margin-left:8px}.gap-footer .wcd_footer_cta.details button{background-color:transparent;color:#fff;display:inline;font-size:10px;height:auto;min-height:16px;min-width:36px;padding:0;text-decoration:underline}.gap-footer .wcd_footer_cta.details button:hover{color:#fff}.gap-footer .wcd_footer_cta a,.gap-footer .wcd_footer_cta button{display:-ms-flexbox;display:flex}.gap-footer .wcd_footer_cta span{font-size:1.125em;padding-bottom:.25em}.gap-footer .wcd_footer_cta ul{background-color:transparent;box-shadow:none;padding-bottom:4px}.gap-footer .wcd_footer_cta li{border-bottom-width:0;border-color:#fff;padding:0}.gap-footer .wcd_footer_cta li a{font-weight:400;padding-left:32px;text-transform:none}.gap-footer [data-testid=prefooter-row]{margin-bottom:0}.gap-footer .email-registration__wrapper{-ms-flex-align:start;align-items:flex-start;background-color:transparent;min-height:120px;padding:26px 0}.gap-footer .email-registration__wrapper>div{margin:0 auto;max-width:640px;width:calc(100% - 32px)}.gap-footer .email-registration__wrapper .email-registration__title{max-width:100%;padding:0;text-align:left}.gap-footer .email-registration__wrapper .email-registration__inputs{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;max-width:420px}.gap-footer .email-registration__wrapper .email-registration__disclaimer{padding-left:0}.gap-footer .email-registration__wrapper .email-registration__form{-ms-flex-align:end;align-items:flex-end;display:-ms-flexbox;display:flex;margin-bottom:24px}.gap-footer .email-registration__wrapper .email-text-input-wrapper{margin-right:24px}.gap-footer .email-registration__wrapper .email-registration__form-email{margin:0;padding-bottom:0}.gap-footer .email-registration__wrapper .email-registration__form-email input[type=email]{margin-top:0;padding:0}.gap-footer .email-registration__wrapper .email-registration__form-email span{font-size:13px;top:50%;transform:translateY(-50%)}.gap-footer .email-registration__wrapper .email-registration__form-email span.sitewide-v1qhrf-LabelText-Label{font-size:10px;text-transform:none;top:0}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container .wcd_footer_cta{min-height:32px;padding-bottom:0;padding-top:0}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container div[aria-label=loading]{transform:rotate(90deg)}.gap-footer .email-registration__wrapper .notification-after-button:empty,.gap-footer .email-registration__wrapper .notification-before-form:empty{display:none}.gap-footer .medallia-feedback-wrapper{-ms-flex-order:4;order:4;padding:0 16px;width:100%}.gap-footer .medallia-feedback-wrapper>button{-ms-flex-align:center;align-items:center;background-color:#fff;border-width:0;color:#2b2b2b;display:-ms-flexbox;display:flex;font-weight:400;height:36px;-ms-flex-pack:center;justify-content:center;letter-spacing:0;margin:0 auto;max-width:640px;padding:0 16px;width:100%}.gap-footer .medallia-feedback-wrapper>button img{margin-right:.375rem}.gap-footer .footer-copyright-section{background-color:#767676;border-top-color:#fff;border-width:0;color:#fff;-ms-flex-order:5;order:5;padding:24px 0 80px;width:100%}.gap-footer .footer-copyright-section .footer-legal__wrapper{margin:0 auto;max-width:640px;text-align:left;width:calc(100% - 32px)}.gap-footer .footer-copyright-section .footer_copyright-row{font-size:11px;line-height:1.5}.gap-footer .footer-copyright-section .footer_copyright-row:not(:last-child){margin-bottom:1.5em}.gap-footer .footer-copyright-section a,.gap-footer .footer-copyright-section button{color:inherit;font-size:inherit}.gap-footer .footer-copyright-section a:hover,.gap-footer .footer-copyright-section button:hover{text-decoration:underline}.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--divider,.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--span{color:inherit;font-size:inherit}.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--span,.gap-footer .footer-copyright-section .footer-legal__wrapper a,.gap-footer .footer-copyright-section .footer-legal__wrapper button{display:inline-block;text-transform:uppercase}.gap-footer .footer-container-wrapper{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;max-width:100%}.gap-footer .footer-container-wrapper .copy-wrapper{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;margin-bottom:12px}.gap-footer .footer-container-wrapper>div:nth-child(4){-ms-flex-direction:column;flex-direction:column;margin-left:auto;margin-right:auto;max-width:672px;width:100%}.gap-footer .footer-container-wrapper>div:nth-child(4)>div:first-child{margin-bottom:30px}.gap-footer .footer-container-wrapper>div:nth-child(4)>div:first-child .wcd_footer_cta{background-color:transparent;color:inherit;-ms-flex-direction:column;flex-direction:column}.gap-footer .footer-container-wrapper>div:nth-child(4)>div:first-child .wcd_footer_cta a,.gap-footer .footer-container-wrapper>div:nth-child(4)>div:first-child .wcd_footer_cta button{background-color:transparent;color:inherit;height:24px;-ms-flex-pack:start;justify-content:flex-start}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:10px;line-height:1.25}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a:not(:last-child){margin-bottom:.5em}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a:hover{text-decoration:underline}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column .wcd_footer_header{margin-bottom:.75em;text-transform:uppercase}@media only screen and (min-width:768px){.gap-footer .wcd_footer_h1{margin-bottom:1em}.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:16px;font-weight:500;line-height:1}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:14px}.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:12px}.gap-footer [data-testid=prefooter-row]{display:block;padding-bottom:48px}.gap-footer .email-registration__wrapper{padding-bottom:0;padding-left:0;padding-top:0}.gap-footer .email-registration__wrapper>div{margin-left:0;max-width:100%;width:100%}.gap-footer .email-registration__wrapper .email-text-input-wrapper{margin-right:10px}.gap-footer .footer-copyright-section{border-top-width:1px;padding-top:44px}.gap-footer .footer-copyright-section .footer-legal__wrapper{margin:0;max-width:1920px;padding-left:2.5%;padding-right:2.5%}.gap-footer .footer-container-wrapper{-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between;margin:0 auto;max-width:1920px;padding-top:30px}.gap-footer .footer-container-wrapper .copy-wrapper{-ms-flex-align:center;align-items:center;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;margin-bottom:0}.gap-footer .footer-container-wrapper .copy-wrapper>div:not(:last-child){margin-right:.75em}.gap-footer .footer-container-wrapper>div:nth-child(4){max-width:100%}.gap-footer .wcd_footer-links-wrapper{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{-ms-flex-positive:1;flex-grow:1}.gap-footer [data-testid=prefooter-row]{padding-left:2.5%;padding-right:2.5%;width:100%}.gap-footer .footer-container-wrapper>div:nth-child(3){padding-bottom:48px;padding-left:2.5%;padding-right:2.5%;width:100%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:16px}}@media only screen and (min-width:1024px){.gap-footer .wcd_footer-links-wrapper{-ms-flex-pack:start;justify-content:flex-start}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{-ms-flex-positive:initial;flex-grow:initial}.gap-footer [data-testid=prefooter-row]{padding-right:0;width:300px}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:3%;padding-left:0;width:calc(97% - 300px)}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:7%}}@media only screen and (min-width:1280px){.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:14px}.gap-footer [data-testid=prefooter-row]{width:24%}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:6%;padding-bottom:84px;width:70%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:11%}}@media only screen and (min-width:1440px){.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:24px}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:18px}.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:16px}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container .wcd_footer_cta{font-size:20px;height:52px;padding:0 26px}.gap-footer .footer-container-wrapper{padding-top:54px}.gap-footer [data-testid=prefooter-row]{width:29%}.gap-footer .footer-container-wrapper>div:nth-child(3){width:65%}}@media only screen and (min-width:1920px){.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:26px}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:21px}.gap-footer [data-testid=prefooter-row]{width:24%}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:0;width:71%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:18%}}</style>"}}]}}}}, "customerSupportLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Mobile Footer Links", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_footer_cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "Service à la clientèle"}, "submenu": [{"text": "Service à la Clientèle", "href": "/customerService/info.do?cid=2136&mlink=55278,********,CS_Footer_CustomerService"}, {"text": "Commande en ligne et cueillette en magasin.", "href": "/customerService/info.do?cid=1183358&mlink=55278,********,LP_BOPIS_footer_CTA"}, {"text": "Localisateur de magasins", "href": "/stores"}, {"text": "DollarsGap", "href": "/browse/info.do?cid=1000194&mlink=55278,********,LP_GapCash_footer_CTA"}, {"text": "Cartes-cadeaux", "href": "/customerService/info.do?cid=2116&mlink=55278,********,CS_Footer_Giftcards"}]}}, {"buttonDropdownData": {"heading": {"text": "Récompenses Gap Good"}, "submenu": [{"text": "Inscrivez-vous au programme Récompenses Gap", "href": "/my-account/sign-in?mlink=55278,********,UNIFOOTER_GGR_JOIN_CDA"}, {"text": "Mes récompenses et avantages", "href": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=55278,********,UNIFOOTER_GGR_POINTS_CDA"}]}}, {"buttonDropdownData": {"heading": {"text": "À propos de nous"}, "submenu": [{"text": "Nos valeurs", "href": "https://www.gapinc.com/fr-ca/values", "target": "_blank"}, {"text": "Durabilité", "href": "https://www.gapcanada.ca/browse/info.do?cid=1086537&locale=fr_CA", "target": "_blank"}, {"text": "Égalité et appartenance", "href": "https://www.gapcanada.ca/browse/info.do?cid=1179886&locale=fr_CA", "target": "_blank"}, {"text": "Carrières", "href": "https://www.gapinc.com/fr-ca/careers/gap-careers", "target": "_blank"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Desktop Footer Links", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-wrapper"}, "components": [{"instanceDesc": "Desktop Footer Links - Column 1", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["Service à la clientèle"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=2136&mlink=55278,********,CS_Footer_CustomerService"}, "components": ["Service à la Clientèle"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1183358&mlink=55278,********,LP_BOPIS_footer_CTA", "target": "_blank"}, "components": ["Commande en ligne et cueillette en magasin."]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/stores"}, "components": ["Localisateur de magasins"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/browse/info.do?cid=1000194&mlink=55278,********,LP_GapCash_footer_CTA"}, "components": ["DollarsGap"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=2116&mlink=55278,********,CS_Footer_Giftcards"}, "components": ["Cartes-cadeaux"]}}]}}, {"instanceDesc": "Desktop Footer Links - Column 2", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["Récompenses Gap Good"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?mlink=55278,********,UNIFOOTER_GGR_JOIN_CDA"}, "components": ["Inscrivez-vous au programme Récompenses Gap Good"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=55278,********,UNIFOOTER_GGR_POINTS_CDA"}, "components": ["Mes récompenses et avantages"]}}]}}, {"instanceDesc": "Desktop Footer Links - Column 3", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["À propos de nous"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/fr-ca/values", "target": "_blank"}, "components": ["Nos valeurs"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapcanada.ca/browse/info.do?cid=1086537&locale=fr_CA", "target": "_blank"}, "components": ["Durabilité"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapcanada.ca/browse/info.do?cid=1179886&locale=fr_CA", "target": "_blank"}, "components": ["Égalité et appartenance"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/fr-ca/careers/gap-careers", "target": "_blank"}, "components": ["Carrières"]}}]}}]}}]}}}}}}]}}}}]}, "header": {"default": {"isStickyEnabled": true, "headerLayout": "sameRow", "fullBleedOptions": {"isFullBleedEnabled": false, "hasTransparencyLayer": false, "fullBleedContrast": "dark"}, "styles": {"marginLeft": "auto", "marginRight": "auto", "maxWidth": "1920px"}}, "byPageType": [{"configurationForPageTypes": ["home"], "isStickyEnabled": true, "fullBleedOptions": {"isFullBleedEnabled": true, "hasTransparencyLayer": false}}]}, "topnav": {"name": "MegaNav", "type": "sitewide", "data": {"isNavSticky": true, "classStyles": {"topnav li:not(.catnav--item)": "padding: 0;", "topnav img.sds_absolute": "left: 0;", "topnav a.divisionLink": "box-shadow: none !important; box-sizing: border-box; color: #2b2b2b; display: block; font-size: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); font-weight: 400; height: 90px; line-height: 1; min-height: 0vw; padding: 40px 0 0; position: relative; text-transform: uppercase;", "topnav a.divisionLink::before": "border-color: transparent; border-style: solid; border-width: 0 0 1px; content: ''; height: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); left: 50%; min-height: 12px; padding-bottom: 3px; position: absolute; top: 40px; transform: translateX(-50%); width: calc(100% - 2vw);", "topnav a.divisionLink._selected": "color: #2b2b2b;", "topnav li:hover a.divisionLink": "background-color: #fff;", "topnav li:hover a.divisionLink::before": "border-color: #2b2b2b;", "topnav a.divisionLink._selected::before": "border-color: #2b2b2b;", "topnav a.divisionLink:hover": "box-shadow: none !important;", "topnav li.catnav--item.sitewide-1l5zl4x": "color: #2b2b2b;", "topnav a.sitewide-l0i3ri": "color: #2b2b2b;", "topnav span.sitewide-l0i3ri": "color: #2b2b2b;", "topnav li.catnav--header > span": "border-bottom-color: #2b2b2b;", "topnav li.catnav--header > a": "border-bottom-color: #2b2b2b;", "topnav a.divisionLink.navlink-pink": "color: #e51937;", "topnav a.divisionLink.navlink-red": "color: #e51937;", "topnav a.divisionLink.navlink-gift": "color: #e51937;", "topnav .catnav--item.catnav--item-selected": "color: #2b2b2b;", "topnav .catnav--item.catnav--item-selected a": "color: #2b2b2b;", "topnav .catnav--item--link": "max-width: 265px;", "topnav .catnav--item--link:hover": "color: #2b2b2b;", "meganav": "border-top-width: 0"}, "activeDivisions": [{"name": "Nouveautés", "divisionId": ["1086624"], "megaNavOrder": [["1139272"], ["3019289", "3019290"]], "numberOfColumns": {}, "exclusionIds": [], "customStyles": {"3013615": {"inlineStyle": {"color": "#e51937"}}, "3013628": {"inlineStyle": {"color": "#e51937"}}, "3013630": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "<PERSON>mme", "divisionId": ["/browse/division.do?cid=5643&mlink=39813,20188735,Megnav_Women&clink=20188735", "5646"], "megaNavOrder": [["1164545", "1131702"], ["1042481"], ["1065848", "1131698"]], "numberOfColumns": {"1042481": 2}, "exclusionIds": [], "customStyles": {"1065871": {"colorScheme": "sale"}, "1131670": {"colorScheme": "sale"}, "1187420": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=5063&mlink=39813,20188735,Megnav_Men&clink=20188735", "5065"], "megaNavOrder": [[], ["1164547", "1149531"], ["1042515"], ["1065865", "1076121"]], "numberOfColumns": {"1042515": 2}, "exclusionIds": [], "customStyles": {"1065870": {"colorScheme": "sale"}, "1187426": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=1137865&mlink=39813,20188735,Megnav_Girls&clink=20188735", "6256"], "megaNavOrder": [[], ["1164548", "1161294", "1056088"], ["1042516"], ["1065849", "6258"]], "numberOfColumns": {"1042516": 2}, "exclusionIds": [], "customStyles": {"1065850": {"colorScheme": "sale"}, "1187423": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Garçon", "divisionId": ["/browse/division.do?cid=1137867&mlink=39813,20188735,Megnav_Boys&clink=20188735", "6172"], "megaNavOrder": [[], ["1164549", "1161295", "1056087"], ["1042518"], ["1065843", "6174"]], "numberOfColumns": {"1042518": 2}, "exclusionIds": [], "customStyles": {"1065844": {"colorScheme": "sale"}, "1187425": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Tout<PERSON>Petit", "divisionId": ["/browse/division.do?cid=1137868&mlink=39813,20188735,Megnav_<PERSON>ler&clink=20188735", "6413"], "megaNavOrder": [["1164550", "1149845"], ["1016135"], ["1016083"], ["1065866", "1016582"]], "exclusionIds": [], "customStyles": {"1065851": {"colorScheme": "sale"}, "1065853": {"colorScheme": "sale"}, "1187427": {"inlineStyle": {"color": "#e51937"}}, "1187428": {"inlineStyle": {"color": "#e51937"}}, "1193755": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=1137869&mlink=39813,20188735,Megnav_Baby&clink=20188735", "6487"], "megaNavOrder": [["1164551", "1164552", "1149847"], ["95461"], ["95574"], ["1065867", "1014322"]], "exclusionIds": [], "customStyles": {"1065857": {"colorScheme": "sale"}, "1065859": {"colorScheme": "sale"}, "1187419": {"inlineStyle": {"color": "#e51937"}}, "1187422": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "<PERSON><PERSON><PERSON>", "divisionId": ["1195157"], "megaNavOrder": [["1194563"], ["1195043", "1195046"], [], []], "numberOfColumns": {"1042513": 2}, "exclusionIds": [], "customStyles": {"65302": {"colorScheme": "sale"}, "1146678": {"colorScheme": "sale"}, "1187473": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "<PERSON><PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=5997&mlink=39813,20188735,Meganav_Maternity&clink=20188735", "5997"], "megaNavOrder": [["1164546", "3018111", "1149538"], ["1042513"], ["1188906", "1014415"], ["1065847"]], "numberOfColumns": {"1042513": 2}, "exclusionIds": [], "customStyles": {"1029762": {"colorScheme": "sale"}, "1187473": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["1077403"], "megaNavOrder": [[], ["1135249"], ["1137310"], ["1137320", "1137322"]], "exclusionIds": [], "customStyles": {"3013827": {"inlineStyle": {"color": "#e51937"}}, "3013829": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Solde", "divisionId": ["1155669"], "megaNavOrder": [["1155670"]], "numberOfColumns": {"1155670": 2}, "exclusionIds": [], "customStyles": {"1155669": {"className": "navlink-red"}}}]}}, "logo": {"name": "Logo", "type": "sitewide", "altText": "Gap logo", "lightLogoImgPath": "/Asset_Archive/GPWeb/content/0020/452/166/assets/logo/logo_gap--light.svg", "darkLogoImgPath": "/Asset_Archive/GPWeb/content/0028/669/369/assets/logo/Gap_logo_MOB_newV2.svg", "logoImgPath": "/Asset_Archive/GPWeb/content/0028/669/369/assets/logo/Gap_logo_MOB_newV2.svg", "isSquare": true, "className": ""}}, "brand": "gap", "type": "meta", "pmcsEdgeCacheTag": "gap-homepage-fr-ca-stage"}