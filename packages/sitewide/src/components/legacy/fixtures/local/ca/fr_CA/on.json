{"meta.description": "pageDescription defaulted", "meta.title.overide": "pageTitle defaulted", "home": {"type": "home", "name": "HomeMultiSimple", "components": [{"instanceName": "042423-position-01", "name": "div", "type": "builtin", "useGreyLoadingEffect": false, "experimentRunning": true, "meta": {"lazy": false}, "data": {"defaultHeight": {"small": "300px", "large": "593px"}, "components": [{"instanceName": "042423-summer-faves-primary-banner", "name": "div", "type": "builtin", "useGreyLoadingEffect": false, "experimentRunning": false, "meta": {"lazy": false}, "data": {"defaultHeight": {"small": "0px", "large": "0px"}, "style": {"maxWidth": "1440px", "margin": "0 auto", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "ONtopbanner", "style": {"position": "relative"}, "desktopStyle": {"background": "#fff"}}, "background": {"linkData": {"to": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_1_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Banner_HP_SM_US.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Banner_HP_XL_US.svg", "alt": "All dresses on sale and summer faves on sale from $8.", "style": {"display": "block"}}}, "ctaList": {"style": {"width": "90%", "textAlign": "center", "padding": "0", "justifyContent": "space-between", "flexDirection": "row", "flexWrap": "nowrap", "margin": "1rem auto", "& ul": {"zIndex": 200}, "& ul a": {"fontSize": "0.8735rem"}, "& > a": {"textAlign": "center", "width": "calc(50% - 2px)", "background": "#fff", "border": "2px solid #003764", "color": "#003764", "borderRadius": "8px", "margin": "0px 0px 5px 0px", "whiteSpace": "normal", "lineHeight": "1", "letterSpacing": "0.4px", "padding": "1em 0", "fontSize": "0.9rem", "height": "44px"}, "& > a:nth-child(even)": {"marginRight": "2px"}, "& > a:nth-child(odd)": {"marginLeft": "2px"}, "& > a:nth-child(1)": {"marginRight": "0", "marginLeft": "0", "width": "100%"}, "& > div > button": {"width": "100%", "lineHeight": "normal", "margin": "0 auto", "textAlign": "center", "letterSpacing": "0.4px", "padding": "1rem 0", "fontSize": "0.9rem", "height": "44px"}}, "desktopStyle": {"--ctaMargin": "calc(70 / 2)", "@media (min-width: 768px) and (max-width: 1023px)": {"--ctaMargin": "calc(44 / 2)", "& > a": {"margin": "0 calc(100vw / 1440 * var(--ctaMargin))"}}, "position": "relative", "width": "100%", "padding": "0", "margin": "0 auto", "& > a:nth-child(odd)": {"margin": "0 min(calc(100vw / 1440 * var(--ctaMargin)), calc(var(--ctaMargin) * 1px))"}, "& > a:nth-child(even)": {"margin": "0 min(calc(100vw / 1440 * var(--ctaMargin)), calc(var(--ctaMargin) * 1px))"}, "& > a:nth-child(1)": {"width": "initial"}, "& > a": {"transition": "unset", "width": "auto", "flex": "0 1 auto", "backgroundColor": "transparent", "textDecoration": "underline", "borderRadius": "0px", "border": "none", "color": "#000", "textAlign": "center", "padding": "max(15px, min(calc(100vw / 1440 * 27), 27px)) 0", "fontWeight": "bold", "fontSize": "max(11px, min(calc(100vw / 1440 * 14), 14px))", "letterSpacing": "min(calc(100vw / 1440 * 0.4), 0.4px)", "lineHeight": "min(calc(100vw / 1440 * 14), 14px)", "height": "auto", "minHeight": "unset", "margin": "0 min(calc(100vw / 1440 * var(--ctaMargin)), calc(var(--ctaMargin) * 1px))"}, "& > a:nth-child(5), & > a:nth-child(6), & > a:nth-child(7), & > a:nth-child(8), & > a:nth-child(9)": {"display": "inline-flex"}, "& > div": {"display": "none"}}, "ctas": [{"composableButtonData": {"children": "Women"}, "linkData": {"to": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_1_b"}, "className": "women"}, {"composableButtonData": {"children": "Men"}, "linkData": {"to": "/browse/category.do?cid=26061&mlink=5151,1,HP_Prim_1_b"}, "className": "men"}, {"composableButtonData": {"children": "Girls"}, "linkData": {"to": "/browse/category.do?cid=26175&mlink=5151,1,HP_Prim_1_b"}, "className": "girls"}, {"composableButtonData": {"children": "Boys"}, "linkData": {"to": "/browse/category.do?cid=26073&mlink=5151,1,HP_Prim_1_b"}, "className": "boys"}, {"composableButtonData": {"children": "Toddler Girls"}, "linkData": {"to": "/browse/category.do?cid=26785#pageId=0&department=165&mlink=5151,1,HP_Prim_1_b"}, "className": "toddler-girls"}, {"composableButtonData": {"children": "Toddler Boys"}, "linkData": {"to": "/browse/category.do?cid=26619#pageId=0&department=166&mlink=5151,1,HP_Prim_1_b"}, "className": "toddler-boys"}, {"composableButtonData": {"children": "Baby Girls"}, "linkData": {"to": "/browse/category.do?cid=51646#pageId=0&department=165&mlink=5151,1,HP_Prim_1_b"}, "className": "baby-girls"}, {"composableButtonData": {"children": "Baby Boys"}, "linkData": {"to": "/browse/category.do?cid=51666#pageId=0&department=166&mlink=5151,1,HP_Prim_1_b"}, "className": "baby-boys"}, {"composableButtonData": {"children": "Maternity"}, "linkData": {"to": "/browse/category.do?cid=26239&mlink=5151,1,HP_Prim_1_b"}, "className": "maternity"}]}}}]}}, {"instanceName": "042423-summer-primary-banner", "name": "div", "type": "builtin", "useGreyLoadingEffect": false, "experimentRunning": false, "meta": {"lazy": false}, "data": {"defaultHeight": {"small": "0px", "large": "0px"}, "props": {"className": "ONprimary-image"}, "style": {"--desktopCompWidth": "1440", "--mobileCompWidth": "720", "--desktopWidth": "calc(100vw / var(--desktopCompWidth))", "--mobileWidth": "calc(100vw / var(--mobileCompWidth))", "maxWidth": "calc(var(--desktopCompWidth) * 1px)", "margin": "0 auto", "position": "relative"}, "desktopStyle": {"margin": "0 auto min(calc(var(--desktopWidth) * 0), 0px)"}, "components": [{"name": "Carousel", "type": "sitewide", "data": {"carouselOptions": {"slidesToShow": 1, "slidesToScroll": 1, "autoplay": true, "speed": 3000, "autoplaySpeed": 2000, "fade": false, "displayPlayPauseBtn": true, "dots": false, "dotsClass": "slick-dots", "infinite": true, "displayArrows": {"desktop": false, "mobile": false}, "pauseOnHover": false, "prevArrowUrl": "", "nextArrowUrl": ""}, "buttonSetting": {"buttonStyle": {"position": "absolute", "top": "1%", "bottom": "auto", "right": "1%", "left": "auto", "transform": "translate(0,0)", "height": "2em", "width": "2em", "zIndex": "0", "@media (max-width: 767px)": {"top": "1%", "bottom": "auto", "right": "1%", "left": "auto"}}}, "style": {"maxWidth": "1440px", "margin": "0 auto", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "background": {"linkData": {"to": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_2_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero1_HP_SM_US.jpg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero1_HP_XL_US.jpg", "alt": "Models wearing colorful fit and flare dresses, tank tops, jeans and pants.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero1_HP_SM_US.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero1_HP_XL_US.svg", "alt": "Fashion made me do it."}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "background": {"linkData": {"to": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_2_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero2_HP_SM_US.jpg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero2_HP_XL_US.jpg", "alt": "A female model dressed in mini dress and male model wearing solid dark color linen blend cam shirt and slim built-in flex chino short.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero2_HP_SM_US.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero2_HP_XL_US.svg", "alt": "Girls' trip required these camis. Date night craved this dress."}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "background": {"linkData": {"to": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_2_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero3_HP_SM_US.jpg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero3_HP_XL_US.jpg", "alt": "Female models wearing fit & flare floral smocked midi cami dress.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero3_HP_SM_US.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero3_HP_XL_US.svg", "alt": "Scheme-stealing at the wedding in this floral."}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}}, "background": {"linkData": {"to": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_2_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero4_HP_SM_US.jpg", "alt": "Two female models wearing solid color tank tops and light color wide leg pants.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_Hero4_HP_SM_US.svg", "alt": "Serving at brunch in these legs."}}, "hideOnBreakpoint": "desktop"}]}}]}}, {"instanceName": "042423-primary-html-text", "name": "LayoutComponent", "type": "sitewide", "useGreyLoadingEffect": false, "experimentRunning": false, "meta": {"lazy": false}, "data": {"defaultHeight": {"small": "0px", "large": "0px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "maxWidth": "1440px", "margin": "0 auto", "position": "relative", "textAlign": "center"}, "components": [{"name": "a", "type": "builtin", "data": {"style": {"display": "inline-block", "color": "#000000", "background": "transparent", "fontWeight": "900", "textAlign": "center", "whiteSpace": "pre-line", "margin": "calc(100vw / 720 * 34) auto", "padding": "0", "fontSize": "calc(100vw / 720 * 26.5)", "letterSpacing": "calc(100vw / 720 * .4)", "lineHeight": "1.04"}, "desktopStyle": {"display": "inline-block", "fontSize": "min(calc(100vw / 1440 * 26.5), 26.5px)", "letterSpacing": "min(calc(100vw / 1440 * .4), .4px)", "lineHeight": "1.04", "width": "auto", "margin": "min(calc(100vw / 1440 * 16), 16px) auto min(calc(100vw / 1440 * 19), 19px)"}, "props": {"className": "ONprimary-html", "href": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_2_a", "tid": "HP_Prim_1_a"}, "components": ["Buying new looks for everything on my social cal..."], "desktopComponents": ["Buying new looks for everything on my social cal..."]}}]}}}}, {"instanceName": "042423-two-cami-midi-dress-banner", "name": "LayeredContentModule", "type": "sitewide", "pageCid": "5151", "experimentRunning": false, "meta": {"lazy": true}, "data": {"defaultHeight": {"small": "0px", "large": "0px"}, "container": {"className": "ONsecondary-image", "style": {"position": "relative", "margin": "calc(100vw / 720 * 0) auto calc(100vw / 720 * 0)", "& > a": {"display": "block", "marginBottom": "calc(100vw / 720 * 35)"}}, "desktopStyle": {"--imgBottomPadding": "113", "maxWidth": "1440px", "margin": "min(calc(100vw / 1440 * 0), 0px) auto min(calc(100vw / 1440 * 0), 0px)", "& > a": {"marginBottom": "0", "paddingBottom": "min(calc((100vw / 1440 * (var(--imgBottomPadding) - 48)) + 48px), calc(var(--imgBottomPadding) * 1px))"}}}, "background": {"linkData": {"to": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_3_a"}, "image": {"srcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_CamiMidi_HP_SM_US.jpg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_CamiMidi_HP_XL_US.jpg", "alt": "A collage of images show different models wearing brightly colored cami midi dresses.", "style": {"display": "block"}}}, "overlay": {"srcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_CamiMidi_HP_SM_US.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0030/012/108/assets/230424_60_M5003_SummerHouse_CamiMidi_HP_XL_US.svg", "alt": "This week only. <PERSON><PERSON> midi dress $20.", "style": {"display": "block"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"margin": "0 auto calc(100vw / 720 * 35)", "justifyContent": "center", "alignItems": "center", "textAlign": "center", "& ul": {"zIndex": 200}, "& ul a": {"fontSize": "0.8735rem"}, "& > a": {"fontSize": "0.9rem", "height": "44px", "lineHeight": "normal", "letterSpacing": "normal", "backgroundColor": "#fff", "width": "90%", "margin": "0 auto calc(100vw / 720 * 20)", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "@media (min-width: 768px)": {"height": "48px"}}, "& > a:first-child": {"fontSize": "0.9rem", "marginTop": "0"}, "& > div": {"width": "90%", "margin": "0 auto 7.5px"}, "& button": {"height": "44px", "padding": "0.625rem", "fontSize": "0.9rem", "lineHeight": "normal", "width": "100%", "margin": "0"}}, "desktopStyle": {"--ctaTopPosition": "352", "--buttonWidth": "304", "--buttonMargin": "60", "--buttonMarginHalf": "calc(var(--buttonMargin) / 2)", "--buttonCount": 4, "--buttonContainerWidth": "calc((var(--buttonWidth) * var(--buttonCount)) + (var(--buttonMargin) * (var(--buttonCount) - 1)))", "position": "absolute", "top": "min((100vw / 1440 * var(--ctaTopPosition)), calc(var(--ctaTopPosition) * 1px))", "left": "50%", "transform": "translate(-50%, 0)", "display": "flex", "flexDirection": "row", "alignItems": "flex-start", "width": "min(calc(100vw / 1440 * var(--buttonContainerWidth)), calc(var(--buttonContainerWidth) * 1px))", "zIndex": "33", "margin": "0 auto", "& > a": {"backgroundColor": "#fff", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem 0.5rem", "whiteSpace": "normal", "lineHeight": "normal", "width": "calc(100% / var(--buttonCount))", "margin": "0 min(calc(100vw / 1440 * var(--buttonMarginHalf)), calc(var(--buttonMarginHalf) * 1px))"}, "& > a:first-child": {"marginLeft": "0"}, "& > a:last-child": {"marginRight": "0"}, "& > div": {"width": "calc(100% / var(--buttonCount))", "margin": "0 min(calc(100vw / 1440 * var(--buttonMarginHalf)), calc(var(--buttonMarginHalf) * 1px))"}, "& > div:first-child": {"marginLeft": "0"}, "& > div:last-child": {"marginRight": "0"}, "& button": {"height": "48px", "width": "100%"}, "& ul": {"minWidth": "100%", "width": "100%", "left": "50%", "transform": "translate(-50%, 0)"}, "@media (min-width: 768px) and (max-width: 1024px)": {"--buttonMargin": "32", "width": "90%", "& ul li a": {"whiteSpace": "pre-line"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop Sale"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_3_b"}, {"text": "Men", "href": "/browse/category.do?cid=26061&mlink=5151,1,HP_Prim_3_b"}, {"text": "Girls", "href": "/browse/category.do?cid=26175&mlink=5151,1,HP_Prim_3_b"}, {"text": "Boys", "href": "/browse/category.do?cid=26073&mlink=5151,1,HP_Prim_3_b"}, {"text": "Toddler Girls", "href": "/browse/category.do?cid=26785#pageId=0&department=165&mlink=5151,1,HP_Prim_3_b"}, {"text": "Toddler Boys", "href": "/browse/category.do?cid=26619#pageId=0&department=166&mlink=5151,1,HP_Prim_3_b"}, {"text": "Baby Girls", "href": "/browse/category.do?cid=51646#pageId=0&department=165&mlink=5151,1,HP_Prim_3_b"}, {"text": "Baby Boys", "href": "/browse/category.do?cid=51666#pageId=0&department=166&mlink=5151,1,HP_Prim_3_b"}, {"text": "Maternity", "href": "/browse/category.do?cid=26239&mlink=5151,1,HP_Prim_3_b"}], "style": {"mobile": {}}}}, {"buttonDropdownData": {"heading": {"text": "Shop New Arrivals"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=10018&mlink=5151,1,HP_Prim_3_c"}, {"text": "Men", "href": "/browse/category.do?cid=11174&mlink=5151,1,HP_Prim_3_c"}, {"text": "Girls", "href": "/browse/category.do?cid=6036&mlink=5151,1,HP_Prim_3_c"}, {"text": "Boys", "href": "/browse/category.do?cid=5918&mlink=5151,1,HP_Prim_3_c"}, {"text": "Toddler Girls", "href": "/browse/category.do?cid=6825#pageId=0&department=165&mlink=5151,1,HP_Prim_3_c"}, {"text": "Toddler Boys", "href": "/browse/category.do?cid=6157#pageId=0&department=166&mlink=5151,1,HP_Prim_3_c"}, {"text": "Baby Girls", "href": "/browse/category.do?cid=37505#pageId=0&department=165&mlink=5151,1,HP_Prim_3_c"}, {"text": "Baby Boys", "href": "/browse/category.do?cid=37508#pageId=0&department=166&mlink=5151,1,HP_Prim_3_c"}, {"text": "Maternity", "href": "/browse/category.do?cid=8454&mlink=5151,1,HP_Prim_3_c"}], "style": {"mobile": {}}}}, {"buttonDropdownData": {"heading": {"text": "Shop Summer"}, "submenu": [{"text": "Shop for the Fam", "href": "/browse/category.do?cid=1189223#style=3020186&mlink=5151,1,HP_Prim_3_d"}, {"text": "Women", "href": "/browse/category.do?cid=1159705&mlink=5151,1,HP_Prim_3_d"}, {"text": "Men", "href": "/browse/category.do?cid=1140918&mlink=5151,1,HP_Prim_3_d"}, {"text": "Girls", "href": "/browse/category.do?cid=1172235&mlink=5151,1,HP_Prim_3_d"}, {"text": "Boys", "href": "/browse/category.do?cid=1151205&mlink=5151,1,HP_Prim_3_d"}, {"text": "Toddler Girls", "href": "/browse/category.do?cid=1124658#pageId=0&department=165&mlink=5151,1,HP_Prim_3_d"}, {"text": "Toddler Boys", "href": "/browse/category.do?cid=1124659#pageId=0&department=166&mlink=5151,1,HP_Prim_3_d"}, {"text": "Baby Girls", "href": "/browse/category.do?cid=1173061#pageId=0&department=165&mlink=5151,1,HP_Prim_3_d"}, {"text": "Baby Boys", "href": "/browse/category.do?cid=1173068#pageId=0&department=166&mlink=5151,1,HP_Prim_3_d"}], "style": {"mobile": {}}}}, {"buttonDropdownData": {"heading": {"text": "Shop Dresses"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=15292&mlink=5151,1,HP_Prim_3_e"}, {"text": "Girls", "href": "/browse/category.do?cid=39288&mlink=5151,1,HP_Prim_3_e"}, {"text": "Toddler Girls", "href": "/browse/category.do?cid=47926#pageId=0&department=165&mlink=5151,1,HP_Prim_3_e"}, {"text": "Baby Girls", "href": "/browse/category.do?cid=6269#pageId=0&department=165&mlink=5151,1,HP_Prim_3_e"}, {"text": "Maternity", "href": "/browse/category.do?cid=48687&mlink=5151,1,HP_Prim_3_e"}], "style": {"mobile": {}}}}]}}}]}}]}, "sitewide": {"edfslarge": {"type": "builtin", "name": "div", "data": {"components": [{"buildInfo": "amplience", "sitewide-ciid_locale": "fr_CA", "type": "sitewide", "name": "MktEdfsLarge", "instanceName": "091422-FR-LDTO-EDFS-large", "experimentRunning": false, "meta": {"lazy": false}, "data": {"text": "LES MEMBRES DU PROGRAMME DE RÉCOMPENSES PEUVENT BÉNÉFICIER DE LA LIVRAISON GRATUITE", "detailsLink": "DÉTAILS", "styles": {"headline": {}, "container": {"fontSize": "1em", "lineHeight": "1.5"}}, "modalTitle": "EXPÉDITION ET RETOURS", "modalUrl": "/customerService/info.do?cid=3019&locale=fr_CA", "signInCta": {"text": " OUVRIR UNE SESSION OU S’INSCRIRE ", "style": {}}}}]}}, "edfssmall": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-small", "sitewide-ciid_locale": "fr_CA", "type": "sitewide", "name": "MktEdfsSmall", "experimentRunning": false, "meta": {"lazy": false}, "data": {"text": "Les Membres Du Programme De Récompenses Peuvent Bénéficier De La Livraison Gratuite", "detailsLink": "Détails", "styles": {"headline": {}, "detailsButton": {}}, "modalTitle": "EXPÉDITION ET RETOURS", "modalUrl": "/customerService/info.do?cid=3019&locale=fr_CA", "signInCta": {"text": "Ouvrir Une Session Ou S’inscrire", "style": {}}}}]}}, "headline": {"type": "builtin", "name": "div", "data": {"components": []}}, "footer": {"type": "sitewide", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instanceName": "US_Footer", "experimentRunning": false, "components": [{"name": "Footer", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "287px", "showSMS": true, "smsLayout": {"headerSection": {"desktopAndMobile": {"shouldDisplay": true, "data": {"placeholderSettings": {"desktop": {"width": "100%"}}, "style": {"flexDirection": "column", "maxWidth": "600px", "width": "100%", "textAlign": "center", "position": "relative", "padding": "0px 0px 1em 0px", "margin": "0 auto"}, "components": [{"name": "HomeSVGOverlay", "type": "home", "data": {"disableAlternativeImage": true, "isVisible": {"large": true, "small": true}, "containerStyle": {"mobile": {"padding": "0 0", "marginTop": "5%"}}, "background": {"content": {"smallImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/Number/190520_037E_US_SMSAcq_Footer_Mobile_Number.svg", "largeImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/Number/190520_037F_US_SMSAcq_Footer_Desk_Number.svg", "altText": "Great deals let's text!"}}, "svgoverlay": {"smallImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/Number/190520_037E_US_SMSAcq_Footer_Mobile_Number.svg", "largeImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/Number/190520_037F_US_SMSAcq_Footer_Desk_Number.svg", "altText": "Great deals let's text!"}}}]}}}, "legalSection": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "maxWidth": "600px", "width": "88%", "textAlign": "center", "position": "relative", "padding": "0 0 0 0", "margin": "0 auto", "lineHeight": "1.2", "fontSize": "55%", "letterSpacing": "1px"}, "components": [{"name": "TextHeadline", "type": "sitewide", "data": {"text": "Msg & Data Rates May Apply. By entering your phone number, clicking submit, and completing the sign-up instructions, you consent to receive one or more recurring marketing text messages each week at the mobile number provided that may be sent via an automated system, and you also consent to the "}}, {"name": "HTMLInjectionComponent", "type": "sitewide", "wcdNote": "created 2 inline urls", "data": {"lazy": false, "defaultHeight": "0px", "html": "<div style='display: inline'><span><a style='text-decoration: underline; color: rgb(0, 55, 100);' href='https://cs.waterfall.com/gap/terms/'>text terms</a></span> & <span><a style='text-decoration: underline; color: rgb(0, 55, 100);' href='https://www.gapinc.com/en-us/consumer-privacy-policy'>privacy policy</a></span></div>"}}, {"name": "TextHeadline", "type": "sitewide", "data": {"text": "Consent is not a condition of purchasing goods or services. You can opt-out at any time by responding STOP. You can also respond HELP for help."}}]}}}}, "emailRegistration": {"disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "oldnavy", "locale": "en_US", "data": {"style": {}, "html": ""}}}, "carousel": {"prevArrow": "", "nextArrow": "", "slides": [{"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0 2rem", "margin": "0px auto 5% auto", "width": "75%"}, "desktop": {"padding": "0 0", "backgroundColor": "#fff", "marginTop": "5%"}}, "background": {"content": {"smallImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/220500_32-M6325_IM_HP_Footer_USCA.svg", "largeImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/220500_32-M6325_IM_HP_Footer_USCA.svg", "altText": "Imagine a better future. Creating a better future for generations. Learn More."}, "style": {"desktop": {}, "mobile": {"height": "100%", "width": "90%", "margin": "0 auto"}}}, "svgoverlay": {"smallImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/220500_32-M6325_IM_HP_Footer_USCA.svg", "largeImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/220500_32-M6325_IM_HP_Footer_USCA.svg", "altText": "Imagine a better future. Creating a better future for generations. Learn More.", "link": {"url": "/browse/info.do?cid=1160383&mlink=5151,1,footer_imaginemission", "tid": "footer_imaginemission"}}}}, {"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0 2rem", "margin": "0px auto 5% auto", "width": "75%"}, "desktop": {"padding": "0 0", "backgroundColor": "#fff", "marginTop": "5%"}}, "background": {"content": {"smallImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/WE_HP_FootCarousel_US_240x277.svg", "largeImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/WE_HP_FootCarousel_US_240x277.svg", "altText": "We are change"}, "style": {"desktop": {}, "mobile": {"height": "100%", "width": "90%", "margin": "0 auto"}}}, "svgoverlay": {"smallImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/WE_HP_FootCarousel_US_240x277.svg", "largeImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/WE_HP_FootCarousel_US_240x277.svg", "altText": "We are change", "link": {"url": "/customerService/info.do?cid=1160233&mlink=5151,1,footer_wearechange", "tid": "footer_wearechange"}}}}, {"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0", "backgroundColor": "#fff", "height": "0", "display": "none"}, "desktop": {"padding": "0 0", "backgroundColor": "#fff", "marginTop": "2%"}}, "background": {"content": {"smallImg": "/assets/common/clear.gif", "largeImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/081717_US_StoreLocator_hp_carousel_xl_image.jpg?v=2", "altText": "Store Locator"}}, "svgoverlay": {"smallImg": "/assets/common/clear.gif", "largeImg": "/Asset_Archive/ONWeb/content/0029/727/350/assets/081717_US_StoreLocator_hp_carousel_xl_image.jpg?v=2", "altText": "Store Locator", "link": {"url": "/customerService/storeLocator.do?mlink=5151,1,footer_storelocator", "tid": "footer_storelocator"}}}}]}, "socialLinks": [{"to": "https://www.facebook.com/Oldnavy", "text": "Follow on Facebook", "className": "social__facebook", "style": {"position": "absolute"}}, {"to": "https://www.twitter.com/oldnavy", "text": "Follow on Twitter", "className": "social__twitter", "style": {"position": "absolute"}}, {"to": "https://www.pinterest.com/oldnavy/", "text": "Follow on Pinterest", "className": "social__pinterest", "style": {"position": "absolute"}}, {"to": "https://www.instagram.com/oldnavy/", "text": "Follow on Instagram", "className": "social__instagram", "style": {"position": "absolute"}}, {"to": "https://oldnavy.tumblr.com/", "text": "Follow on Tumblr", "className": "social__tumblr", "style": {"position": "absolute"}}, {"to": "https://snapchat.com/add/oldnavy", "text": "Follow on Snap<PERSON><PERSON>", "className": "social__snapchat", "style": {"position": "absolute"}}], "footerCustomerSupport": {"desktop": {"columns": [{"header": {"type": "link", "text": "CUSTOMER SERVICE", "className": "cs-links", "to": "/customerService/info.do?cid=3171&mlink=5151,1,Footer_Customer_Service"}, "links": [{"type": "link", "text": "Customer Service", "to": "/customerService/info.do?cid=3171&mlink=5151,1,Footer_Customer_Service", "className": "body-a onfLinks"}, {"type": "link", "text": "Shipping", "to": "/customerService/info.do?cid=82725&mlink=5151,1,cs=shipping_and_handling", "className": "body-a onfLinks"}, {"type": "link", "text": "Returns", "to": "/customerService/info.do?cid=82724&mlink=5151,1,cs=how_to_return_or_exchange", "className": "body-a onfLinks"}, {"type": "link", "text": "Buy gift cards", "to": "/customerService/info.do?cid=35433&mlink=5151,1,Footer_Gift_Cards", "className": "body-a onfLinks"}, {"type": "link", "text": "Size charts", "to": "/customerService/info.do?cid=83042&mlink=5151,1,Footer_Size_Charts", "className": "body-a onfLinks"}, {"type": "link", "text": "Buy Online. Pick up in-store.", "to": "/customerService/info.do?cid=82725&mlink=5151,1,cs=buy_online_pickup_in-store", "className": "body-a onfLinks"}]}, {"header": {"text": "LEARN MORE", "className": "lm-links", "link": ""}, "links": [{"type": "link", "text": "Our Affiliate program", "to": "/customerService/info.do?cid=3000&mlink=5151,1,Footer_Affiliate_Program", "className": "body-a onfLinks"}, {"type": "link", "text": "Super Cash", "to": "/customerService/info.do?cid=56526&mlink=5151,1,Footer_Super_Cash", "className": "body-a onfLinks"}, {"type": "link", "text": "Careers @ Old Navy", "to": "https://jobs.gapinc.com/old-navy-home?mlink=5151,1,Footer_Careers", "className": "body-a onfLinks"}, {"type": "link", "text": "Sustainability", "to": "/browse/info.do?cid=1130006&mlink=5151,1,Footer_Sustainability", "className": "body-a onfLinks"}]}, {"header": {"type": "link", "text": "on logo", "className": "logo-links", "to": "/customerService/info.do?cid=1095422&mlink=5151,1,FTR_NavyistRewardsLogo", "style": {"color": "transparent", "width": "3.25rem", "background": "url('/Asset_Archive/ONWeb/content/static-marketing/navyist/Navyist_logo.svg') no-repeat;", "backgroundSize": "3.25em"}}, "links": [{"type": "link", "text": "My Points and Rewards", "to": "/my-account/sign-in?targetURL=/loyalty/customer-value", "className": "body-a logoLinks"}, {"type": "link", "text": "Explore Benefits", "to": "/browse/info.do?cid=1095422&mlink=5151,1,FTR_Loyalty_Benefits", "className": "body-a logoLinks"}, {"type": "link", "text": "Pay credit card bill", "to": "https://oldnavy.barclaysus.com/payment", "className": "body-a logoLinks"}, {"type": "link", "text": "Activate credit card", "to": "https://www.oldnavy.barclaysus.com/activate", "className": "body-a logoLinks"}, {"type": "link", "text": "Join <PERSON><PERSON> <PERSON><PERSON> - it’s Free", "to": "/customerService/info.do?cid=1095422", "className": "body-a logoLinks", "style": {"marginTop": "0.5rem"}}, {"type": "link", "text": "or Apply Now for a Credit Card", "to": "/my-account/sign-in?creditOffer=barclays&sitecode=ONUNIFTD", "className": "body-a logoLinks"}]}, {"header": {"type": "link", "text": "FIND US", "className": "find-links", "to": "/customerService/storeLocator.do&mlink=5151,1,Footer_Store_Locator"}, "links": [{"type": "test", "text": "1-800-OLD-NAVY", "className": "body-a onfLinks"}, {"type": "test", "text": "(**************)", "className": "body-a onfLinks"}, {"type": "link", "text": "Email sign-up", "to": "/profile/info.do?cid=82635&mlink=5151,1,<PERSON><PERSON>_Email_Sign_Up", "className": "body-a onfLinks"}, {"type": "link", "text": "Store Locator", "to": "/customerService/storeLocator.do&mlink=5151,1,Footer_Store_Locator", "className": "body-a onfLinks"}]}]}, "mobile": {"links": [{"type": "link", "text": "Store Locator", "to": "/customerService/storeLocator.do&mlink=5151,1,Footer_Store_Locator", "className": "footer-item"}, {"type": "link", "text": "Customer Service", "to": "/customerService/info.do?cid=3171&mlink=5151,1,Footer_Customer_Service", "className": "footer-item"}, {"type": "link", "text": "Orders & Returns", "to": "/my-account/order-lookup?mlink=5151,1,Footer_Order_Returns", "className": "footer-item"}, {"type": "accordion", "text": "Gift Cards", "className": "footer-item", "accordionLinks": [{"type": "link", "text": "Buy eGift Cards", "to": "http://oldnavy-m.cashstar.com/buy/?mlink=5151,1,Buy_eGiftCards", "className": "footer-line", "style": {"color": "red"}}, {"type": "link", "text": "Buy Gift Cards", "to": "/browse/product.do?vid=1&pid=000152&mlink=5151,1,Footer_Gift_Cards", "className": "footer-line"}]}, {"type": "accordion", "text": "Navy<PERSON> <PERSON><PERSON><PERSON>", "className": "footer-item", "accordionLinks": [{"type": "link", "text": "My points and rewards", "to": "/my-account/sign-in?targetURL=/loyalty/customer-value", "className": "footer-line"}, {"type": "link", "text": "Explore benefits", "to": "/browse/info.do?cid=1095422&mlink=5151,1,FTR_Loyalty_Benefits", "className": "footer-line"}, {"type": "link", "text": "Join <PERSON><PERSON><PERSON> - It's Free", "to": "/my-account/sign-in", "className": "footer-line"}]}, {"type": "link", "text": "<PERSON>ail Signup", "to": "/profile/info.do?cid=82635&mlink=5151,1,<PERSON><PERSON>_Email_Sign_Up", "className": "footer-item"}, {"type": "accordion", "text": "Shop our other brands", "className": "footer-item", "accordionLinks": [{"type": "link", "text": "Gap", "to": "https://www.gap.com/?ssiteID=on", "className": "footer-line"}, {"type": "link", "text": "Banana Republic", "to": "https://bananarepublic.gap.com/?ssiteID=on", "className": "footer-line"}, {"type": "link", "text": "Athleta", "to": "https://athleta.gap.com/?ssiteID=on", "className": "footer-line"}]}, {"type": "link", "text": "Sustainability", "to": "/browse/info.do?cid=1130006?mlink=5151,1,Footer_Sustainability", "className": "footer-item"}]}}, "copyRights": {"rows": [[{"text": "© 2022 Old Navy, LLC"}, {"to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy", "text": "Privacy Policy"}, {"text": "Do Not Sell My Info", "doNotSell": true}, {"to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy#Interest-Based-Ad", "text": "Interest Based Ads"}, {"to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy#Your-California-Privacy-Rights", "text": "Your California Privacy Rights"}, {"to": "https://www.gapinc.com/content/gapinc/html/sustainability/ca-transparency-insupplychainsact.html", "text": "California Transparency in Supply Chains Act"}, {"to": "/customerService/info.do?cid=3319&mlink=5151,17385942,Footer_Terms_of_Use&clink=17385942", "text": "Terms of Use"}, {"to": "https://jobs.gapinc.com/old-navy-home", "text": "Careers"}, {"to": "/browse/info.do?cid=1130006", "text": "Sustainability"}, {"to": "/customerService/info.do?cid=1095422", "text": "Navy<PERSON> <PERSON><PERSON><PERSON>"}, {"to": "https://www.gapinc.com/content/gapinc/html/aboutus.html", "text": " About Gap Inc.", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}], [{"to": "/customerService/info.do?cid=1005695&mlink=5151,17385942,Footer_Disabilities_Act&clink=17385942", "text": "Americans with Disabilities Act", "style": {"font-weight": "700"}}, {"to": "https://www.gapinc.com/en-us/values/sustainability", "text": "Gap Inc. Policies"}]]}}}]}, "topnav": {"buildInfo": "amplience", "sitewide-ciid_locale": "fr_QC", "sitewide-topnav-desc": "added html ctas for mktg tiles.", "type": "sitewide", "name": "MegaNav", "instanceName": "meganav-071023-FR", "data": {"isNavSticky": true, "classStyles": {"divisionLink._selected ": "color: #003764;", "meganav:not(.custom) .catnav--header > span ": "margin-top: 12px", "meganav:not(.custom) .catnav--header > a ": "margin-top: 12px", "meganav .wcdNavLimit .meganav-column": "display:inline-block;", "meganav-category-group.MatchingGiftsfortheFamily": "margin-bottom:0px;width:100%;float:left;", "meganav .catnav--header": "float:left;letter-spacing:0px;", "topnav .divisionLink": "font-size:min(max(calc(0.65rem + ((1vw - 10.24px) * 0.6696))), 18px) !important; letter-spacing: .7px !important; text-transform: capitalize !important; font-weight: 400; color: #000 !important; ", "topnav .divisionLink._selected": "font-weight: bold !important; color: #003764 !important", "topnav .divisionLink span": "font-size: 10px !important; display: flex; vertical-align: middle; padding-bottom: 3px; font-weight:400;", "topnav a.divisionLink:hover": "font-weight: bold !important; color: #003764 !important", "topNavLink:nth-child(8) .wcdNavLimit, .topNavLink:nth-child(9) .wcdNavLimit": "position:relative;overflow-x:hidden;", "wcdNavLimit ul.custom": "margin-bottom:40px;", "topNavLink .wcdNavLimit ul.custom .division-header": "position:absolute;background-color:#f7f7f7;width:50%;font-weight:700;color:#000000;font-size:.875rem;padding:.5em 0 .5em .5em;", "topNavLink .wcdNavLimit ul.custom .division-header span": "font-weight:400;", "meganav .catnav--header a": "color: #000000;", "meganav .catnav--header a:hover": "color: #003764 !important;", "meganav.wcd-nouveau .catnav--item a:hover": "color: #003764 !important; border-bottom: 1px solid #003764", "meganav.wcd-nouveau .catnav--item span": "padding-bottom: 2px", "meganav.wcd-nouveau .catnav--header > a::after": "content: ' >'", "meganav.wcd-cadeaux .catnav--header > a::after": "content: ' >'", "meganav.wcd-femme .catnav--header > a::after": "content: ' >'", "meganav.wcd-homme .catnav--header > a::after": "content: ' >'", "meganav.wcd-fille .catnav--header > a::after": "content: ' >'", "meganav.wcd-garon .catnav--header > a::after": "content: ' >'", "meganav.wcd-toutpetit .catnav--header > a::after": "content: ' >'", "meganav.wcd-bb .catnav--header > a::after": "content: ' >'", "meganav.wcd-maternit .catnav--header > a::after": "content: ' >'", "meganav.wcd-jeans .catnav--header > a::after": "content: ' >'", "meganav.wcd-vtementsassortis .catnav--header > a::after": "content: ' >'", "meganav.wcd-solde .catnav--header > a::after": "content: ' >'", "meganav.wcd-masques .catnav--header > a::after": "content: ' >'", "meganav.wcd-larentrescolaire .catnav--header > a::after": "content: ' >'", "meganav.wcd-laubainedujour .catnav--header > a::after": "content: ' >'", "topNavLink .wcd-toutpetit .wcdNavLimit ul.custom .division-header": "width:30.5%; max-width: 442px", "topNavLink .wcd-bb .wcdNavLimit ul.custom .division-header": "width:28.5%; max-width: 442px", "meganav .dp-tile-ctas": "font-weight: bold; text-transform: capitalize; line-height: 1.0; font-size: 14px; letter-spacing: .5px; white-space: pre-line", "meganav a.dp-tile-ctas:hover": "color: #003764; text-decoration: underline;", "meganav .todd.dp-tile-ctas": "font-size: 10px; line-height: 0;", "meganav .baby.dp-tile-ctas": "font-size: 12px; line-height: 0;", "meganav .toddler-baby.catnav--item": "padding: 50px 0 0 0 !important;", "topNavLink a[aria-label=\"l’aubaine du jour!\"]": "color: red!important", "meganav a[aria-label='nouveau et tendance boutique halloween'] span::after": "content: ' 🎃'", "meganav a[aria-label=\"nouveau et tendance la boutique de l’halloween\"] span::after": "content: ' 🎃'", "meganav a[aria-label=\"nouveau et tendance halloween shop\"] span::after": "content: ' 🎃'"}, "activeDivisions": [{"name": "<PERSON>mme", "subtitle": "Tailles 0 à 30 + TP à 4X", "divisionId": ["/browse/division.do?cid=5360&mlink=5151,topNav,visnav", "5360"], "megaNavOrder": [["1016051", "1039280"], ["1036191"], ["55182", "1182446"], ["1019570", "1176447"], ["<ul class='catnav-links'><li class='dp catnav--item'><a data-categoryid='15292' href='/browse/category.do?cid=15292&mlink=5151,w_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='/Asset_Archive/ONWeb/content/0030/012/189/assets/230501_12-M5103_W_MegaNav.jpg' alt='Une femme vêtue d’une robe bain de soleil trapèze courte noire à manches flottantes avec ouverture en trou de serrure en mélange de lin.'></span></a></li></ul><ul><li><a class='dp-tile-ctas' href='/browse/category.do?cid=15292&mlink=5151,w_mnav_tile'>Magasiner les robes ></a></li></ul>"]], "numberOfColumns": {"1036191": 1}, "exclusionIds": [], "customStyles": {"26190": {"colorScheme": "sale"}, "96964": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=5155&mlink=5151,topNav,visnav", "5155"], "megaNavOrder": [["48385", "55197", "1039290"], ["1036209"], ["1031097", "1145656"]], "numberOfColumns": {"1036209": 1}, "exclusionIds": ["1121202"], "customStyles": {"26061": {"colorScheme": "sale", "inlineStyle": {"text-transform": "uppercase"}}, "97035": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=6027&mlink=5151,topNav,visnav", "6027"], "megaNavOrder": [["1119128", "3020904", "48395", "54857", "1092613"], ["1036216"], ["1007069", "1125471"], ["<ul class='catnav-links'><li class='dp catnav--item'><a data-categoryid='39288' href='/browse/category.do?cid=36516&mlink=5151,g_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='/Asset_Archive/ONWeb/content/0030/015/429/assets/230701_40_M5843_Uniform_G_Meganav_USCA.jpeg' alt='Une fille porte un uniforme scolaire Old Navy composé d’un chemisier bleu pâle, d’un pantalon bleu marine et de chaussures de sport.'></span></a></li></ul><ul><li style='padding-bottom: 5px'><a class='todd-baby dp-tile-ctas' href='/browse/category.do?cid=36516&mlink=5151,g_mnav_tile'>Magasiner les uniformes ></a></li><li><a class='todd-baby dp-tile-ctas' href='/browse/category.do?cid=3020921&mlink=5151,g_mnav_tile'>Magasiner les modèles\nde la rentrée scolaire ></a></li></ul>"]], "numberOfColumns": {"1036216": 1}, "exclusionIds": ["1121223"], "customStyles": {"26175": {"colorScheme": "sale"}, "96906": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Garçon", "divisionId": ["/browse/division.do?cid=5910&mlink=5151,topNav,visnav", "5910"], "megaNavOrder": [["1119129", "3021305", "48396", "54865", "1071417"], ["1036210"], ["1006914", "1125295"], ["<ul class='catnav-links'><li class='dp catnav--item'><a data-categoryid='16604' href='/browse/category.do?cid=5924&mlink=5151,b_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='/Asset_Archive/ONWeb/content/0030/015/429/assets/230701_40_M5844_Uniform_B_Meganav_USCA.jpeg' alt='Un garçon porte un uniforme scolaire Old Navy composé d’un polo bourgogne et d’un pantalon kaki.'></span></a></li></ul><ul><li style='padding-bottom: 5px'><a class='todd-baby dp-tile-ctas' href='/browse/category.do?cid=5924&mlink=5151,b_mnav_tile'>Magasiner les uniformes ></a></li><li><a class='todd-baby dp-tile-ctas' href='/browse/category.do?cid=3020928&mlink=5151,b_mnav_tile'>Magasiner les modèles \nde la rentrée scolaire ></a></li></ul>"]], "numberOfColumns": {"1036210": 1}, "exclusionIds": ["1121192"], "customStyles": {"26073": {"colorScheme": "sale"}, "96945": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Tout<PERSON>Petit", "divisionId": ["/browse/division.do?cid=6241&mlink=5151,topNav,visnav", "6241"], "megaNavOrder": [["<div class='toddlergirls-division-header division-header'>Toute-petite <PERSON>lle <span>(12M-6T)</span></div>", "1017563", "54835", "1073646"], ["<div class='division-header-spacer'></div>", "1073647"], ["<div class='toddlerboys-division-header division-header'>Tout-petit G<PERSON> <span>(12M-6T)</span></div>", "1071067", "1040818", "1073756"], ["<div class='division-header-spacer'></div>", "1073744"], ["<ul class='catnav-links'><li class='toddler-baby catnav--item'><a data-categoryid='50123' href='/browse/category.do?cid=50123&mlink=5151,tg_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='/Asset_Archive/ONWeb/content/0030/012/189/assets/230501_12_M5009_Todd_Meganav.jpg' alt='Photo d’un maillot de bain une-pièce en tricot texturé avec garniture à volants de couleur rose pour Toute-petite fille.'></span></a></li></ul><ul><li style='padding-bottom: 5px'><a class='todd dp-tile-ctas' href='/browse/category.do?cid=50123&mlink=5151,tg_mnav_tile'>Magasiner les maillots de bain \npour toute-petite fille ></a></li><li><a class='todd dp-tile-ctas' href='/browse/category.do?cid=1075848&mlink=5151,tb_mnav_tile'>Magasiner les maillots de bain \npour tout-petit garçon ></a></li></ul>"]], "numberOfColumns": {}, "exclusionIds": ["1071074", "1071075", "1121187", "1121188"], "customStyles": {"26619": {"colorScheme": "sale"}, "26785": {"colorScheme": "sale"}, "53699": {"colorScheme": "sale"}, "97017": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=51375&mlink=5151,topNav,visnav", "51375"], "megaNavOrder": [["<div class='toddlerboys-division-header division-header'><PERSON><PERSON><PERSON><PERSON> <span>(0-24M)</span></div>", "1017553", "60396", "1077947"], ["<div class='division-header-spacer'></div>", "1077949"], ["<div class='toddlerboys-division-header division-header'><PERSON><PERSON><PERSON><PERSON> <span>(0-24M)</span></div>", "1077229", "1006541", "1078107"], ["<div class='division-header-spacer'></div>", "1077950"], ["<ul class='catnav-links'><li class='toddler-baby catnav--item'><a data-categoryid='3009618' href='/browse/category.do?cid=3009618&mlink=5151,bb_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='/Asset_Archive/ONWeb/content/0030/012/189/assets/230501_12_M5010_Baby_Meganav.jpg' alt='Photo d’une barboteuse henley unisexe sans manches de couleur orange pour Bébé.'></span></a></li></ul><ul><li><a class='baby dp-tile-ctas' href='/browse/category.do?cid=3009618&mlink=5151,bb_mnav_tile'>Magasiner les vêtements unisexes pour bébé ></a></li></ul>"]], "numberOfColumns": {}, "exclusionIds": ["1077228", "1077230", "1121207", "1121205"], "customStyles": {"51646": {"colorScheme": "sale"}, "51666": {"colorScheme": "sale"}, "96918": {"colorScheme": "sale"}, "96919": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "<PERSON><PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=5758&mlink=5151,topNav,visnav", "5758"], "megaNavOrder": [["55185", "7791", "1065183"], ["1036208"], ["1092435"]], "numberOfColumns": {"1036208": 1}, "exclusionIds": ["1121193"], "customStyles": {"26239": {"colorScheme": "sale", "inlineStyle": {"text-transform": "uppercase"}}, "96921": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "La Rentrée <PERSON>", "divisionId": ["/browse/category.do?cid=3020921&mlink=5151,topNav,visnav", "3021489"], "megaNavOrder": [["3021490", "3021491", "3021492"], ["3021493", "3021494"], ["<ul class='catnav-links'><li class='toddler-baby catnav--item'><a data-categoryid='36516' href='/browse/category.do?cid=36516&mlink=5151,bts_g_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='/Asset_Archive/ONWeb/content/0030/015/467/assets/230701_40_M5595_BTS_Kids_Site_MegaNav_0627_G_MegaNav.jpg' alt='Une fille vêtue d’un uniforme scolaire composé d’un polo anti-moiteur et d’un skort performance taille haute à plis.'></span></a></li></ul><ul><li style='padding-bottom: 5px'><a class='todd dp-tile-ctas' href='/browse/category.do?cid=36516&mlink=5151,bts_g_mnav_tile'>Magasiner les uniformes<br/>pour fille ></a></li><li><a class='todd dp-tile-ctas' href='/browse/category.do?cid=5924&mlink=5151,bts_b_mnav_tile'>Magasiner les uniformes<br/>pour garçon ></a></li></ul>"]], "exclusionIds": [""], "customStyles": {"*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "<PERSON>’aubaine du jour!", "divisionId": ["/browse/category.do?cid=26190&mlink=5151,topNav,visnav", "1183117"], "megaNavOrder": [["1183119", "1183123"], ["1183125"], ["1183128", "1183131"], ["1183143", "1183145"], ["1183146", "1183147"]], "exclusionIds": [""], "customStyles": {"*": {"inlineStyle": {"letter-spacing": "0"}}}}]}}, "promodrawer": {"_meta": {"name": "2024-02-16 PrDrawer VZ", "schema": "https://cms.gap.com/schema/content/v1/promo-drawer.json", "deliveryId": "e3942c03-f0f0-48da-bc53-f73312d70dbb"}, "main": {"handlebarText": "HandleBar"}, "promoCards": [{"background": {"type": "solid", "color": "#F3F573"}, "tapToApply": true, "mainPromoMessage": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\">Main Text</span></p>", "promoDetails": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\">Promo Text</span></p>"}, {"background": {"type": "solid", "color": "#63FFB9"}, "tapToApply": false, "mainPromoMessage": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\">Main Text</span></p>", "promoDetails": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\">Promo Text</span></p>", "cta": [{"cta": {"label": "ONE", "value": "#"}}, {"cta": {"label": "TWO", "value": "#"}}, {"cta": {"label": "FOUR", "value": "#"}}, {"cta": {"label": "FIVE", "value": "#"}}]}, {"background": {"type": "solid", "color": "#E677FC"}, "tapToApply": true}]}, "logo": {"type": "sitewide", "name": "Logo", "lightLogoImgPath": "data:image/svg+xml;charset%3DUS-ASCII,%3Csvg%20id%3D%22mobile%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%221.203in%22%20height%3D%2237.536%22%20viewBox%3D%220%200%2086.649%2028.141%22%3E%20%20%20%20%3Cdefs%3E%20%20%20%20%20%20%20%20%3Cstyle%3E%20%20%20%20%20%20%20%20%20%20%20%20.cls-1%20%7B%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%20%23fff%3B%20%20%20%20%20%20%20%20%20%20%20%20%7D%20%20%20%20%20%20%20%20%20%20%20%20.cls-2%20%7B%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%20%23003874%3B%20%20%20%20%20%20%20%20%20%20%20%20%7D%20%20%20%20%20%20%20%20%20%20%20%20.cls-3%20%7B%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%20%23003764%3B%20%20%20%20%20%20%20%20%20%20%20%20%7D%20%20%20%20%20%20%20%20%3C%2Fstyle%3E%20%20%20%20%3C%2Fdefs%3E%20%20%20%20%3Ctitle%3E122616_HolidayLights_Off_mobile_masthead%3C%2Ftitle%3E%3Cellipse%20class%3D%22cls-1%22%20cx%3D%2243.324%22%20cy%3D%2214.07%22%20rx%3D%2243.325%22%20ry%3D%2214.071%22%2F%3E%3Cpath%20%20%20%20%20%20%20%20class%3D%22cls-2%22%20%20%20%20%20%20%20%20d%3D%22M52.831%2015.355h2.288l-1.156-4.3-1.132%204.3zM31.256%2010.962h-.581v6.252h.574a1.753%201.753%200%200%200%201.711-.773%205.234%205.234%200%200%200%200-4.72%201.738%201.738%200%200%200-1.704-.759zM15.211%2010.764c-1.616%200-1.9%202.242-1.9%203.357s.493%203.222%201.9%203.223%201.905-2.12%201.906-3.222-.29-3.356-1.906-3.358z%22%2F%3E%3Cpath%20%20%20%20%20%20%20%20class%3D%22cls-3%22%20%20%20%20%20%20%20%20d%3D%22M43.323%200C19.397%200%200%206.3%200%2014.069s19.4%2014.072%2043.323%2014.072%2043.326-6.3%2043.326-14.072S67.247%200%2043.323%200zM15.205%2019.334c-2.677%200-4.35-2.212-4.356-5.214v-.023c0-3.164%201.664-5.29%204.364-5.289s4.359%202.132%204.357%205.3c-.007%203.006-1.681%205.226-4.366%205.226zm11.7-.134h-6.04V8.986h2.336v8.227h3.705zm9.13-5.115v.066a5.923%205.923%200%200%201-1.291%203.925c-.956%201.1-1.98%201.125-3.262%201.124h-3.267V8.986h3.274c1.282%200%202.306.005%203.261%201.1a5.938%205.938%200%200%201%201.286%203.947zM48.121%2019.2h-2.346l-3.248-5.844v5.842h-2.352V8.986h2.35l3.256%205.921V8.986h2.344zm8.029%200l-.534-1.987h-3.278l-.523%201.987H49.43l2.745-10.214h3.566L58.49%2019.2zm7.525%200h-3.111L57.941%208.986h2.592l1.589%207.122%201.6-7.122h2.589zm9.066-3.257V19.2h-2.607v-3.258l-3.051-6.956h2.573l1.783%204.465%201.79-4.465h2.572z%22%2F%3E%3C%2Fsvg%3E", "darkLogoImgPath": "data:image/svg+xml;charset%3DUS-ASCII,%3Csvg%20id%3D%22mobile%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%221.203in%22%20height%3D%2237.536%22%20viewBox%3D%220%200%2086.649%2028.141%22%3E%20%20%20%20%3Cdefs%3E%20%20%20%20%20%20%20%20%3Cstyle%3E%20%20%20%20%20%20%20%20%20%20%20%20.cls-1%20%7B%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%20%23fff%3B%20%20%20%20%20%20%20%20%20%20%20%20%7D%20%20%20%20%20%20%20%20%20%20%20%20.cls-2%20%7B%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%20%23003874%3B%20%20%20%20%20%20%20%20%20%20%20%20%7D%20%20%20%20%20%20%20%20%20%20%20%20.cls-3%20%7B%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%20%23003764%3B%20%20%20%20%20%20%20%20%20%20%20%20%7D%20%20%20%20%20%20%20%20%3C%2Fstyle%3E%20%20%20%20%3C%2Fdefs%3E%20%20%20%20%3Ctitle%3E122616_HolidayLights_Off_mobile_masthead%3C%2Ftitle%3E%3Cellipse%20class%3D%22cls-1%22%20cx%3D%2243.324%22%20cy%3D%2214.07%22%20rx%3D%2243.325%22%20ry%3D%2214.071%22%2F%3E%3Cpath%20%20%20%20%20%20%20%20class%3D%22cls-2%22%20%20%20%20%20%20%20%20d%3D%22M52.831%2015.355h2.288l-1.156-4.3-1.132%204.3zM31.256%2010.962h-.581v6.252h.574a1.753%201.753%200%200%200%201.711-.773%205.234%205.234%200%200%200%200-4.72%201.738%201.738%200%200%200-1.704-.759zM15.211%2010.764c-1.616%200-1.9%202.242-1.9%203.357s.493%203.222%201.9%203.223%201.905-2.12%201.906-3.222-.29-3.356-1.906-3.358z%22%2F%3E%3Cpath%20%20%20%20%20%20%20%20class%3D%22cls-3%22%20%20%20%20%20%20%20%20d%3D%22M43.323%200C19.397%200%200%206.3%200%2014.069s19.4%2014.072%2043.323%2014.072%2043.326-6.3%2043.326-14.072S67.247%200%2043.323%200zM15.205%2019.334c-2.677%200-4.35-2.212-4.356-5.214v-.023c0-3.164%201.664-5.29%204.364-5.289s4.359%202.132%204.357%205.3c-.007%203.006-1.681%205.226-4.366%205.226zm11.7-.134h-6.04V8.986h2.336v8.227h3.705zm9.13-5.115v.066a5.923%205.923%200%200%201-1.291%203.925c-.956%201.1-1.98%201.125-3.262%201.124h-3.267V8.986h3.274c1.282%200%202.306.005%203.261%201.1a5.938%205.938%200%200%201%201.286%203.947zM48.121%2019.2h-2.346l-3.248-5.844v5.842h-2.352V8.986h2.35l3.256%205.921V8.986h2.344zm8.029%200l-.534-1.987h-3.278l-.523%201.987H49.43l2.745-10.214h3.566L58.49%2019.2zm7.525%200h-3.111L57.941%208.986h2.592l1.589%207.122%201.6-7.122h2.589zm9.066-3.257V19.2h-2.607v-3.258l-3.051-6.956h2.573l1.783%204.465%201.79-4.465h2.572z%22%2F%3E%3C%2Fsvg%3E", "logoImgPath": "data:image/svg+xml;charset%3DUS-ASCII,%3Csvg%20id%3D%22mobile%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%221.203in%22%20height%3D%2237.536%22%20viewBox%3D%220%200%2086.649%2028.141%22%3E%20%20%20%20%3Cdefs%3E%20%20%20%20%20%20%20%20%3Cstyle%3E%20%20%20%20%20%20%20%20%20%20%20%20.cls-1%20%7B%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%20%23fff%3B%20%20%20%20%20%20%20%20%20%20%20%20%7D%20%20%20%20%20%20%20%20%20%20%20%20.cls-2%20%7B%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%20%23003874%3B%20%20%20%20%20%20%20%20%20%20%20%20%7D%20%20%20%20%20%20%20%20%20%20%20%20.cls-3%20%7B%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%20%23003764%3B%20%20%20%20%20%20%20%20%20%20%20%20%7D%20%20%20%20%20%20%20%20%3C%2Fstyle%3E%20%20%20%20%3C%2Fdefs%3E%20%20%20%20%3Ctitle%3E122616_HolidayLights_Off_mobile_masthead%3C%2Ftitle%3E%3Cellipse%20class%3D%22cls-1%22%20cx%3D%2243.324%22%20cy%3D%2214.07%22%20rx%3D%2243.325%22%20ry%3D%2214.071%22%2F%3E%3Cpath%20%20%20%20%20%20%20%20class%3D%22cls-2%22%20%20%20%20%20%20%20%20d%3D%22M52.831%2015.355h2.288l-1.156-4.3-1.132%204.3zM31.256%2010.962h-.581v6.252h.574a1.753%201.753%200%200%200%201.711-.773%205.234%205.234%200%200%200%200-4.72%201.738%201.738%200%200%200-1.704-.759zM15.211%2010.764c-1.616%200-1.9%202.242-1.9%203.357s.493%203.222%201.9%203.223%201.905-2.12%201.906-3.222-.29-3.356-1.906-3.358z%22%2F%3E%3Cpath%20%20%20%20%20%20%20%20class%3D%22cls-3%22%20%20%20%20%20%20%20%20d%3D%22M43.323%200C19.397%200%200%206.3%200%2014.069s19.4%2014.072%2043.323%2014.072%2043.326-6.3%2043.326-14.072S67.247%200%2043.323%200zM15.205%2019.334c-2.677%200-4.35-2.212-4.356-5.214v-.023c0-3.164%201.664-5.29%204.364-5.289s4.359%202.132%204.357%205.3c-.007%203.006-1.681%205.226-4.366%205.226zm11.7-.134h-6.04V8.986h2.336v8.227h3.705zm9.13-5.115v.066a5.923%205.923%200%200%201-1.291%203.925c-.956%201.1-1.98%201.125-3.262%201.124h-3.267V8.986h3.274c1.282%200%202.306.005%203.261%201.1a5.938%205.938%200%200%201%201.286%203.947zM48.121%2019.2h-2.346l-3.248-5.844v5.842h-2.352V8.986h2.35l3.256%205.921V8.986h2.344zm8.029%200l-.534-1.987h-3.278l-.523%201.987H49.43l2.745-10.214h3.566L58.49%2019.2zm7.525%200h-3.111L57.941%208.986h2.592l1.589%207.122%201.6-7.122h2.589zm9.066-3.257V19.2h-2.607v-3.258l-3.051-6.956h2.573l1.783%204.465%201.79-4.465h2.572z%22%2F%3E%3C%2Fsvg%3E", "isSquare": true}, "header": {"default": {"isUtilityLinksEnabled": true, "headerLayout": "sameRow", "fullBleedOptions": {"isFullBleedEnabled": false, "hasTransparencyLayer": false}}}}, "brand": "on", "type": "meta", "pmcsEdgeCacheTag": "on-homepage-fr-ca-stage"}