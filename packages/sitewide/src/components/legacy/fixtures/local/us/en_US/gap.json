{"meta.description": "pageDescription defaulted", "meta.title.overide": "pageTitle defaulted", "home": {"type": "home", "name": "HomeMultiSimple", "components": [{"instanceDesc": "CMS", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>#main-content *{box-sizing:border-box}#main-content img,#main-content video{display:block}.slick-slide>div>div{display:block!important}.fullBleedCertona div.productCard{max-width:unset}.fullBleedCertona button.slick-arrow.slick-disabled.sitewide-0,.fullBleedCertona button.slick-arrow.slick-next.sitewide-0,.fullBleedCertona button.slick-arrow.slick-prev.sitewide-0{margin-top:0}.fullBleedCertona .mkt-certona-recs{max-width:none}.fullBleedCertona .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon{max-width:none}.fullBleedCertona .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon .mkt-certona-recs__products{max-width:none}div.wcd_hp-cta{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between}.wcd_hp-cta a,.wcd_hp-cta button,a .wcd_hp-cta,a.wcd_hp-cta,button.wcd_hp-cta{-ms-flex-align:center;align-items:center;background-color:#fff;border-color:#fff;border-style:solid;border-width:1px;color:#767676;display:-ms-flexbox;display:flex;font-size:13px;font-weight:400;height:32px;letter-spacing:0;padding:0 15px;text-transform:uppercase;width:auto}.wcd_hp-cta a:active,.wcd_hp-cta a:focus,.wcd_hp-cta a:hover,.wcd_hp-cta button:active,.wcd_hp-cta button:focus,.wcd_hp-cta button:hover,a .wcd_hp-cta:active,a .wcd_hp-cta:focus,a .wcd_hp-cta:hover,a.wcd_hp-cta:active,a.wcd_hp-cta:focus,a.wcd_hp-cta:hover,button.wcd_hp-cta:active,button.wcd_hp-cta:focus,button.wcd_hp-cta:hover{background-color:#999;border-color:#999;color:#fff}.wcd_hp-cta button,button.wcd_hp-cta{-ms-flex-pack:justify;justify-content:space-between;min-width:100px;border-color:#767676}.wcd_hp-cta button:focus,button.wcd_hp-cta:focus{outline:0}.wcd_hp-cta button span,button.wcd_hp-cta span{font-size:1.625em;height:.25em;line-height:0;margin-left:.5em;padding:0}.wcd_hp-cta>div{position:relative;width:auto}.wcd_hp-cta>div:first-child{z-index:10}.wcd_hp-cta>div:nth-child(2){z-index:9}.wcd_hp-cta>div:nth-child(3){z-index:8}.wcd_hp-cta>div:nth-child(4){z-index:7}.wcd_hp-cta>div:nth-child(5){z-index:6}.wcd_hp-cta>div:nth-child(6){z-index:5}.wcd_hp-cta>div:nth-child(7){z-index:4}.wcd_hp-cta>div:nth-child(8){z-index:3}.wcd_hp-cta>div:nth-child(9){z-index:2}.wcd_hp-cta>div:nth-child(10){z-index:1}.wcd_hp-cta>div ul{background-color:#fff;border-color:#fff;border-style:solid;border-width:0 1px 1px;box-shadow:rgba(0,0,0,.3) 0 1px 6px 0;padding:0;position:absolute}.wcd_hp-cta>div li{border-width:0;padding:0}.wcd_hp-cta>div li:first-child a{border-top-width:1px}.wcd_hp-cta>div a{background-color:transparent;border-color:#fff;border-style:solid;border-width:0;color:#767676}.wcd_hp-cta>div a:active,.wcd_hp-cta>div a:focus,.wcd_hp-cta>div a:hover{background-color:#e9e9e9;border-color:#fff;color:#2b2b2b}.wcd_hp-cta.white a,.wcd_hp-cta.white button,a .wcd_hp-cta.white,a.wcd_hp-cta.white,button.wcd_hp-cta.white{background-color:#fff;border-color:#fff;color:#767676}.wcd_hp-cta.white a:active,.wcd_hp-cta.white a:focus,.wcd_hp-cta.white a:hover,.wcd_hp-cta.white button:active,.wcd_hp-cta.white button:focus,.wcd_hp-cta.white button:hover,a .wcd_hp-cta.white:active,a .wcd_hp-cta.white:focus,a .wcd_hp-cta.white:hover,a.wcd_hp-cta.white:active,a.wcd_hp-cta.white:focus,a.wcd_hp-cta.white:hover,button.wcd_hp-cta.white:active,button.wcd_hp-cta.white:focus,button.wcd_hp-cta.white:hover{background-color:#999;border-color:#999;color:#fff}.wcd_hp-cta.arrow a,.wcd_hp-cta.arrow button,a .wcd_hp-cta.arrow,a.wcd_hp-cta.arrow,button.wcd_hp-cta.arrow{background-color:transparent;background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--black.svg);background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 13px;border-color:transparent;display:-ms-inline-flexbox;display:inline-flex;padding-left:0;padding-right:calc(1em + 15px + 6px);text-align:left;transition:background-position .25s ease-out}.wcd_hp-cta.arrow a:hover,.wcd_hp-cta.arrow button:hover,a .wcd_hp-cta.arrow:hover,a.wcd_hp-cta.arrow:hover,button.wcd_hp-cta.arrow:hover{background-color:transparent;background-position-x:calc(100% - 8px);border-color:transparent;color:#767676}.wcd_hp-cta.arrow a.white,.wcd_hp-cta.arrow button.white,a .wcd_hp-cta.arrow.white,a.wcd_hp-cta.arrow.white,button.wcd_hp-cta.arrow.white{background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--white.svg)}.wcd_hp-cta.caret-vcn a,.wcd_hp-cta.caret-vcn button,a .wcd_hp-cta.caret-vcn,a.wcd_hp-cta.caret-vcn,button.wcd_hp-cta.caret-vcn{background-color:transparent;background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret.svg);background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 13px;border-color:transparent;display:-ms-inline-flexbox;display:inline-flex;padding-left:0;padding-right:calc(.5em + 15px + 6px);text-align:left;transition:background-position .25s ease-out}.wcd_hp-cta.caret-vcn a:hover,.wcd_hp-cta.caret-vcn button:hover,a .wcd_hp-cta.caret-vcn:hover,a.wcd_hp-cta.caret-vcn:hover,button.wcd_hp-cta.caret-vcn:hover{background-color:transparent;border-color:transparent;color:#767676}.wcd_hp-cta.caret-vcn a.white,.wcd_hp-cta.caret-vcn button.white,a .wcd_hp-cta.caret-vcn.white,a.wcd_hp-cta.caret-vcn.white,button.wcd_hp-cta.caret-vcn.white{background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret_white.svg)}.wcd_hp-cta.caret a,.wcd_hp-cta.caret button,a .wcd_hp-cta.caret,a.wcd_hp-cta.caret,button.wcd_hp-cta.caret{background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret.svg);background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 13px;color:#767676;display:-ms-inline-flexbox;display:inline-flex;padding-left:0;padding-right:calc(1em + 15px + 6px);text-align:left;transition:background-position .25s ease-out;padding-left:10px;padding-right:24px;-ms-flex-pack:start;justify-content:flex-start}.wcd_hp-cta.caret a:hover,.wcd_hp-cta.caret button:hover,a .wcd_hp-cta.caret:hover,a.wcd_hp-cta.caret:hover,button.wcd_hp-cta.caret:hover{background-color:transparent;background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret_white.svg);border-color:#999;color:#767676}.wcd_hp-cta.caret a.white,.wcd_hp-cta.caret button.white,a .wcd_hp-cta.caret.white,a.wcd_hp-cta.caret.white,button.wcd_hp-cta.caret.white{background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--white.svg)}.wcd_hp-cta.details a,.wcd_hp-cta.details button,a .wcd_hp-cta.details,a.wcd_hp-cta.details,button.wcd_hp-cta.details{background-color:transparent;border-width:0;color:#fff;font-size:10px;min-height:16px;min-width:32px;padding:0;text-decoration:underline}.wcd_hp-cta.details a:hover,.wcd_hp-cta.details button:hover,a .wcd_hp-cta.details:hover,a.wcd_hp-cta.details:hover,button.wcd_hp-cta.details:hover{color:#fff}.wcd_hp-cta.details a.dark,.wcd_hp-cta.details button.dark,a .wcd_hp-cta.details.dark,a.wcd_hp-cta.details.dark,button.wcd_hp-cta.details.dark{color:#2b2b2b}.wcd_hp-cta.details a.dark:hover,.wcd_hp-cta.details button.dark:hover,a .wcd_hp-cta.details.dark:hover,a.wcd_hp-cta.details.dark:hover,button.wcd_hp-cta.details.dark:hover{color:#000}.wcd_hp-cta.outline a,.wcd_hp-cta.outline button,a .wcd_hp-cta.outline,a.wcd_hp-cta.outline,button.wcd_hp-cta.outline{background-color:#fff;border-color:#767676;border-width:1px}.wcd_hp-cta.outline a:active,.wcd_hp-cta.outline a:focus,.wcd_hp-cta.outline a:hover,.wcd_hp-cta.outline button:active,.wcd_hp-cta.outline button:focus,.wcd_hp-cta.outline button:hover,a .wcd_hp-cta.outline:active,a .wcd_hp-cta.outline:focus,a .wcd_hp-cta.outline:hover,a.wcd_hp-cta.outline:active,a.wcd_hp-cta.outline:focus,a.wcd_hp-cta.outline:hover,button.wcd_hp-cta.outline:active,button.wcd_hp-cta.outline:focus,button.wcd_hp-cta.outline:hover{background-color:#999;color:#fff;border-color:#fff}.wcd_hp-cta.full-width,.wcd_hp-cta.full-width a,.wcd_hp-cta.full-width a>div,.wcd_hp-cta.full-width button,.wcd_hp-cta.full-width button>div,.wcd_hp-cta.full-width-at-mob,.wcd_hp-cta.full-width-at-mob a,.wcd_hp-cta.full-width-at-mob a>div,.wcd_hp-cta.full-width-at-mob button,.wcd_hp-cta.full-width-at-mob button>div,.wcd_hp-cta.full-width-at-mob>div,.wcd_hp-cta.full-width>div,a .wcd_hp-cta.full-width,a .wcd_hp-cta.full-width-at-mob,a .wcd_hp-cta.full-width-at-mob>div,a .wcd_hp-cta.full-width>div{width:100%}.wcd_hp-visnav{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between;margin-left:auto;margin-right:auto;max-width:640px}.wcd_hp-visnav>div{position:relative;width:50%}.wcd_hp-visnav>div .wcd_hp-cta button,.wcd_hp-visnav>div button.wcd_hp-cta{border-color:transparent}.wcd_hp-visnav>div .wcd_hp-cta{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);background-color:#fff;color:#767676}.wcd_hp-visnav>div .wcd_hp-cta:hover{background-color:#999;color:#767676;border-color:#999}.wcd_hp-visnav>div:first-child .wcd_hp-cta{z-index:32}.wcd_hp-visnav>div:nth-child(2) .wcd_hp-cta{z-index:31}.wcd_hp-visnav>div:nth-child(3) .wcd_hp-cta{z-index:30}.wcd_hp-visnav>div:nth-child(4) .wcd_hp-cta{z-index:29}.wcd_hp-visnav>div:nth-child(5) .wcd_hp-cta{z-index:28}.wcd_hp-visnav>div:nth-child(6) .wcd_hp-cta{z-index:27}.wcd_hp-visnav>div:nth-child(7) .wcd_hp-cta{z-index:26}.wcd_hp-visnav>div:nth-child(8) .wcd_hp-cta{z-index:25}.wcd_hp-visnav>div:nth-child(9) .wcd_hp-cta{z-index:24}.wcd_hp-visnav>div:nth-child(10) .wcd_hp-cta{z-index:23}.wcd_hp-visnav>div:nth-child(11) .wcd_hp-cta{z-index:22}.wcd_hp-visnav>div:nth-child(12) .wcd_hp-cta{z-index:21}.wcd_hp-visnav>div:nth-child(13) .wcd_hp-cta{z-index:20}.wcd_hp-visnav>div:nth-child(14) .wcd_hp-cta{z-index:19}.wcd_hp-visnav>div:nth-child(15) .wcd_hp-cta{z-index:18}.wcd_hp-visnav>div:nth-child(16) .wcd_hp-cta{z-index:17}.wcd_hp-news{background-color:#fff;color:#2b2b2b;padding:16px 0 12px;width:100%}.wcd_hp-news>div{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;width:100%}.wcd_hp-news .wcd_hp-news_tile{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;margin:0 auto;max-width:672px;padding:10px 16px;width:100%}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_header{font-size:19px;margin-bottom:3px;text-transform:uppercase}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_copy{font-size:13px}@media only screen and (min-width:768px){.wcd_hp-cta a,.wcd_hp-cta button,a .wcd_hp-cta,a.wcd_hp-cta,button.wcd_hp-cta{font-size:calc(.625rem + (1vw - 7.68px) * .6944)}.wcd_hp-cta button,button.wcd_hp-cta{min-width:10.25em}.wcd_hp-cta.exposed-at-desk button{background-color:transparent;border-width:0;color:inherit;margin-bottom:.75em;padding:0;text-align:left}.wcd_hp-cta.exposed-at-desk button span{display:none}.wcd_hp-cta.exposed-at-desk ul{background-color:transparent;box-shadow:none;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:start;justify-content:flex-start;max-height:none;position:relative;visibility:visible;z-index:0}.wcd_hp-cta.exposed-at-desk ul li{width:auto}.wcd_hp-cta.exposed-at-desk ul li:not(:last-child){margin-bottom:8px;margin-right:8px}.wcd_hp-cta.exposed-at-desk ul li a{border-width:1px}.wcd_hp-cta.arrow a,.wcd_hp-cta.arrow button,a .wcd_hp-cta.arrow,a.wcd_hp-cta.arrow,button.wcd_hp-cta.arrow{background-size:auto .9em}.wcd_hp-cta.caret-vcn a,.wcd_hp-cta.caret-vcn button,a .wcd_hp-cta.caret-vcn,a.wcd_hp-cta.caret-vcn,button.wcd_hp-cta.caret-vcn{background-size:auto .9em}.wcd_hp-cta.caret a,.wcd_hp-cta.caret button,a .wcd_hp-cta.caret,a.wcd_hp-cta.caret,button.wcd_hp-cta.caret{background-size:auto .9em}.wcd_hp-cta.full-width-at-mob,.wcd_hp-cta.full-width-at-mob a,.wcd_hp-cta.full-width-at-mob a>div,.wcd_hp-cta.full-width-at-mob button,.wcd_hp-cta.full-width-at-mob button>div,.wcd_hp-cta.full-width-at-mob>div,a .wcd_hp-cta.full-width-at-mob,a .wcd_hp-cta.full-width-at-mob>div{width:auto}.wcd_hp-cta.full-width-at-desk,.wcd_hp-cta.full-width-at-desk a,.wcd_hp-cta.full-width-at-desk a>div,.wcd_hp-cta.full-width-at-desk button,.wcd_hp-cta.full-width-at-desk button>div,.wcd_hp-cta.full-width-at-desk>div,a .wcd_hp-cta.full-width-at-desk,a .wcd_hp-cta.full-width-at-desk>div{width:100%}.wcd_hp-visnav{max-width:1920px}.wcd_hp-visnav>div{background-color:#fff;width:25%}.wcd_hp-visnav>div:hover img{opacity:.85}.wcd_hp-news{padding:0}.wcd_hp-news>div{-ms-flex-direction:row;flex-direction:row;margin:0 auto;max-width:1920px}.wcd_hp-news .wcd_hp-news_tile{-ms-flex-align:center;align-items:center;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:justify;justify-content:space-between;max-width:none;padding:20px 22px 12px;position:relative}.wcd_hp-news .wcd_hp-news_tile:not(:hover) .wcd_hp-cta,.wcd_hp-news .wcd_hp-news_tile:not(:hover) .wcd_hp-news_copy{color:transparent}.wcd_hp-news .wcd_hp-news_tile:not(:hover) .wcd_hp-cta{background-image:none}.wcd_hp-news .wcd_hp-news_tile:hover{background-color:#efefed}.wcd_hp-news .wcd_hp-news_tile:hover .wcd_hp-news_header{background-image:none;color:transparent}.wcd_hp-news .wcd_hp-news_tile:hover .wcd_hp-news_header img{display:none!important}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_header{-ms-flex-align:center;align-items:center;background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--black.svg);background-position:100% 50%;background-repeat:no-repeat;background-size:auto 1em;display:-ms-flexbox;display:flex;font-size:13px;left:50%;padding-right:24px;position:absolute;top:50%;transform:translate(-50%,-50%);white-space:nowrap}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_copy{font-size:10px}}@media only screen and (min-width:1024px){.wcd_hp-cta button,button.wcd_hp-cta{min-width:9.75em}.wcd_hp-news .wcd_hp-news_tile{padding-left:3vw;padding-right:3vw}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_header{font-size:Min(Max(17px, calc(1.0625rem + ((1vw - 10.24px) * 1.6741))), 32px);padding-right:1.5em}.wcd_hp-news .wcd_hp-news_tile:nth-child(2) .wcd_hp-news_header{min-width:17.5em}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_copy{font-size:Min(Max(12px, calc(.75rem + ((1vw - 10.24px) * 1.3393))), 24px)}}@media only screen and (min-width:1280px){.wcd_hp-cta button,button.wcd_hp-cta{min-width:9.375em}}@media only screen and (min-width:1440px){.wcd_hp-cta button,button.wcd_hp-cta{min-width:8.875em}}@media only screen and (max-width:767px){.wcd_hp-cta.two-column-at-mob a,.wcd_hp-cta.two-column-at-mob>div{margin-bottom:8px;width:calc(50% - 4px)}.wcd_hp-cta.two-column-at-mob li a{margin-bottom:0;width:100%}.wcd_hp-cta.two-column-at-mob>div button{width:100%}.wcd_hp-cta.two-column-at-mob.odd-number a:first-child,.wcd_hp-cta.two-column-at-mob.odd-number>div:first-child{width:100%}}</style>"}}, {"instanceName": "dpg-banner1", "instanceDesc": "DPG Placeholder1", "experimentRunning": true, "name": "OptimizelyPlaceholder", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}}}, {"instanceName": "optly-placeholder-TEST-1-a", "instanceDesc": "2023-05-02 TEST-1-a", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": false, "defaultHeight": {"small": "771px", "large": "401px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 771, "margin": "0 auto 16px", "maxWidth": "640px", "width": "100%"}, "desktop": {"backgroundColor": "#ccc", "height": 401, "margin": "0 auto 40px", "maxWidth": "1920px", "width": "100%"}}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 24px", "maxWidth": "1920px"}, "components": [{"instanceDesc": "Banner", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"maxWidth": "640px", "width": "100%", "margin": "0 auto"}, "desktop": {"margin": "0 auto", "maxWidth": "1920px", "width": "100%"}}, "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "marginBottom": "12px", "position": "relative"}, "components": [{"name": "Carousel", "type": "sitewide", "data": {"carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true, "fade": false, "slidesToShow": 1, "speed": 1000}, "buttonSetting": {"pauseAltText": "Pause slideshow", "playAltText": "Play slideshow", "buttonStyle": {"bottom": "7px", "height": "32px", "right": "4px", "padding": "4px", "position": "absolute", "width": "32px", "zIndex": "1"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_play--white.svg", "pauseBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_pause--white.svg"}}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Mob/Part1/SU234701_Control_PT1_img1_MOB.jpg"}, "linkData": {"title": "Portraits of <PERSON>", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012069,HP_HERO1_W_SU234701_IMAGE"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Mob/Part1/SU234701_Control_PT1_img2_MOB.jpg"}, "linkData": {"title": "Portraits of <PERSON>", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012069,HP_HERO1_W_SU234701_IMAGE"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Mob/Part1/SU234701_Control_PT1_img3_MOB.jpg"}, "linkData": {"title": "Portraits of <PERSON>", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012069,HP_HERO1_W_SU234701_IMAGE"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Mob/Part1/SU234701_Control_PT1_img4_MOB.jpg"}, "linkData": {"title": "Portraits of <PERSON>", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012069,HP_HERO1_W_SU234701_IMAGE"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Mob/Part1/SU234701_Control_PT1_img5_MOB.jpg"}, "linkData": {"title": "Portraits of <PERSON>", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012069,HP_HERO1_W_SU234701_IMAGE"}}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "50%"}}, "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Desk/Part%201/SU234701_Control_PT1_img_DESK.jpg"}, "linkData": {"title": "Portraits of <PERSON>", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012069,HP_HERO1_W_SU234701_IMAGE"}}, "overlay": {"alt": "Portraits of <PERSON>", "srcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/Mob/A/SP233946_A_copy1245_MOB.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Desk/Part%201/SU234701_Control_PT1_copy_DESK.svg"}}}, {"instanceDesc": "Video", "name": "div", "type": "builtin", "tileStyle": {"desktop": {"position": "relative", "width": "50%"}}, "data": {"props": {"flexDirection": "column", "className": ""}, "components": [{"name": "VideoComponent", "type": "sitewide", "data": {"isVisible": {"small": false, "large": true}, "posterImage": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Desk/Part%201/SU234701_Control_PT1_FallBackimg_DESK.jpg", "url": "https://vimeo.com/816688555", "fallbackImage": {"alt": "Video Failed to Load", "src": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Desk/Part%201/SU234701_Control_PT1_FallBackimg_DESK.jpg"}, "controls": true, "width": "100%", "height": "41.7vw", "light": false, "loop": false, "muted": false, "lazy": true, "playing": false, "playsinline": true, "containerStyle": {"desktop": {"position": "relative", "width": "100%", "height": "100%", "paddingTop": "0%"}}, "analytics": {"onPlayer": {"event_name": "video_play_click", "video_name_play": "Portraits of <PERSON>"}}}}]}}]}}}}, {"instanceDesc": "CTA mob+desk", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "10px 13px 0px", "@media only screen and (min-width:768px)": {"alignItems": "flex-start", "flexDirection": "row", "justifyContent": "space-between", "padding": "12px 12px 0px 12px"}, "@media only screen and (min-width:1280px)": {"padding": "12px 12px 0px 12px"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"color": "#767676", "fontSize": "17px", "fontWeight": "400", "padding": "10px 0px 0px", "@media only screen and (min-width: 768px)": {"display": "flex", "whiteSpace": "pre", "fontSize": "min(max(16px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 40px)", "marginRight": "8px", "padding": "0"}}, "components": [{"type": "builtin", "name": "div", "data": {"components": ["It’s time to break the rules and make one new one — if it doesn’t feel like summer, we’re not wearing it."]}}]}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0", "@media only screen and (min-width:768px)": {"alignItems": "end", "flexDirection": "row", "justifyContent": "flex-end", "padding": "0", "width": "75%"}, "@media only screen and (min-width:1280px)": {"paddingTop": "0"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"fontSize": "16px", "fontWeight": "400", "padding": "12px 0 14px", "whiteSpace": "pre", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(12px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 24px)", "marginRight": "8px", "padding": "0"}}, "components": [{"instanceDesc": "CTA Block Headline", "name": "div", "type": "builtin", "data": {"style": {"fontSize": "13px", "fontWeight": "400", "padding": "10px 6px 8px 0px", "textAlign": "left", "textTransform": "uppercase", "color": "#767676", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(10px, calc(0.625rem + ((1vw - 7.68px) * 1.2153))), 18px)", "marginRight": "8px", "padding": "6px 0 0", "textAlign": "right"}}, "components": ["Shop New Arrivals"]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta two-column-at-mob caret outline", "style": {"&.wcd_hp-cta.caret a": {"&:first-child": {}}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012069,HP_HERO1_W_SU234701_CTA"}, "composableButtonData": {"children": "Women"}}, {"linkData": {"to": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,30012069,HP_HERO1_M_SU234701_CTA"}, "composableButtonData": {"children": "Men"}}, {"linkData": {"to": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,30012069,HP_HERO1_G_SU234701_CTA"}, "composableButtonData": {"children": "Girls"}}, {"linkData": {"to": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,30012069,HP_HERO1_B_SU234701_CTA"}, "composableButtonData": {"children": "Boys"}}, {"linkData": {"to": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,30012069,HP_HERO1_TG_SU234701_CTA"}, "composableButtonData": {"children": "<PERSON>ler Girl"}}, {"linkData": {"to": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,30012069,HP_HERO1_TB_SU234701_CTA"}, "composableButtonData": {"children": "<PERSON><PERSON>"}}, {"linkData": {"to": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,30012069,HP_HERO1_BG_SU234701_CTA"}, "composableButtonData": {"children": "Baby Girl"}}, {"linkData": {"to": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,30012069,HP_HERO1_BB_SU234701_CTA"}, "composableButtonData": {"children": "Baby Boy"}}]}}}]}}]}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "10px 13px 0px", "@media only screen and (min-width:768px)": {"alignItems": "baseline", "flexDirection": "row", "justifyContent": "space-between", "padding": "12px 12px 0px 20px"}, "@media only screen and (min-width:1280px)": {"padding": "12px 12px 0px 20px"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"color": "#767676", "fontSize": "17px", "fontWeight": "400", "padding": "0 0 10px", "@media only screen and (min-width: 768px)": {"display": "flex", "whiteSpace": "break-spaces", "fontSize": "min(max(16px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 40px)", "marginRight": "8px", "padding": "0"}}, "components": [{"type": "builtin", "name": "div", "data": {"components": ["Roll down the windows, turn everything loose, and make plans just because.\nIt’s time to break the rules and make one new one — if it doesn’t feel like summer,\nwe’re not wearing it."]}}]}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0", "@media only screen and (min-width:768px)": {"alignItems": "end", "flexDirection": "row", "justifyContent": "flex-end", "padding": "0", "width": "81%", "flexWrap": "wrap !important"}, "@media only screen and (min-width:1185px)": {"width": "auto", "paddingTop": "0", "flexWrap": "nowrap !important"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"fontSize": "16px", "fontWeight": "400", "padding": "12px 0 14px", "whiteSpace": "pre", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(12px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 24px)", "padding": "0", "flexWrap": "wrap !important"}, "@media only screen and (min-width:1351px)": {"flexWrap": "nowrap !important"}}, "components": [{"instanceDesc": "CTA Block Headline", "name": "div", "type": "builtin", "data": {"style": {"fontSize": "13px", "fontWeight": "400", "padding": "10px 6px 8px 16px", "textAlign": "left", "textTransform": "uppercase", "color": "#767676", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(10px, calc(0.625rem + ((1vw - 7.68px) * 1.2153))), 18px)", "marginRight": "8px", "padding": "6px 0 0", "textAlign": "right"}}, "components": ["Shop New Arrivals"]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta outline", "style": {}, "desktopStyle": {"flex-wrap": "nowrap !important", "&.wcd_hp-cta a": {"width": "auto", "marginRight": "8px", "marginBottom": "8px"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012069,HP_HERO1_W_SU234701_CTA"}, "composableButtonData": {"children": "Women"}}, {"linkData": {"to": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,30012069,HP_HERO1_M_SU234701_CTA"}, "composableButtonData": {"children": "Men"}}]}}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"@media only screen and (min-width: 768px)": {"&.wcd_hp-cta button": {"minWidth": "3.25rem"}}, "@media only screen and (min-width: 1024px)": {"&.wcd_hp-cta button": {"minWidth": "3.25rem"}}, "@media only screen and (min-width: 1440px)": {"&.wcd_hp-cta button": {"minWidth": "3.25rem"}}}, "desktopStyle": {"marginRight": "8px", "marginBottom": "8px"}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Kids"}, "submenu": [{"text": "Girls", "href": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,30012069,HP_HERO1_G_SU234701_CTA"}, {"text": "Boys", "href": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,30012069,HP_HERO1_B_SU234701_CTA"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"@media only screen and (min-width: 768px)": {"&.wcd_hp-cta button": {"minWidth": "3.25rem"}}, "@media only screen and (min-width: 1024px)": {"&.wcd_hp-cta button": {"minWidth": "3.25rem"}}, "@media only screen and (min-width: 1440px)": {"&.wcd_hp-cta button": {"minWidth": "3.25rem"}}}, "desktopStyle": {"marginRight": "8px", "marginBottom": "8px"}, "ctas": [{"buttonDropdownData": {"heading": {"text": "<PERSON><PERSON>"}, "submenu": [{"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,30012069,HP_HERO1_TG_SU234701_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,30012069,HP_HERO1_TB_SU234701_CTA"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"@media only screen and (min-width: 768px)": {"&.wcd_hp-cta button": {"minWidth": "3.25rem"}}, "@media only screen and (min-width: 1024px)": {"&.wcd_hp-cta button": {"minWidth": "3.25rem"}}, "@media only screen and (min-width: 1440px)": {"&.wcd_hp-cta button": {"minWidth": "3.25rem"}}}, "desktopStyle": {"marginRight": "8px", "marginBottom": "8px"}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Baby"}, "submenu": [{"text": "Baby Girl", "href": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,30012069,HP_HERO1_BG_SU234701_CTA"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,30012069,HP_HERO1_BB_SU234701_CTA"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}]}}]}}]}}}}]}}}}, {"instanceName": "optly-placeholder-TEST-1-b", "instanceDesc": "2023-05-02 TEST-1-b", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "401px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 771, "margin": "0 auto 16px", "maxWidth": "640px", "width": "100%"}, "desktop": {"backgroundColor": "#ccc", "height": 401, "margin": "0 auto 40px", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "marginBottom": "12px", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "VideoComponent", "type": "sitewide", "data": {"isVisible": {"small": false, "large": true}, "posterImage": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Desk/Part%201/SU234701_Control_PT1_FallBackimg_DESK.jpg", "url": "https://vimeo.com/816688494", "fallbackImage": {"alt": "Video Failed to Load", "src": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Desk/Part%201/SU234701_Control_PT1_FallBackimg_DESK.jpg"}, "controls": true, "width": "100%", "height": "112.7vw", "light": false, "loop": false, "muted": false, "lazy": true, "playing": false, "playsinline": true, "containerStyle": {"desktop": {"position": "relative", "width": "100%", "height": "100%", "paddingTop": "0px"}}, "analytics": {"onPlayer": {"event_name": "video_play_click", "video_name_play": "Portraits of <PERSON>"}}}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%", "maxWidth": "1920px", "margin": "0 auto 24px"}}, "data": {"carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": false, "slidesToShow": 1, "speed": 0, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause Slideshow", "playAltText": "Play Slideshow", "buttonStyle": {"height": "32px", "right": "5px", "padding": "4px", "position": "absolute", "bottom": "8px", "width": "32px", "zIndex": "1"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_play--white.svg", "pauseBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_pause--white.svg"}}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Desk/Part2/SU234701_Control_PT2_img1_DESK.jpg"}, "linkData": {"title": "Portraits of <PERSON>", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012069,HP_HERO1_W_SU234701_IMAGE"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Desk/Part2/SU234701_Control_PT2_img2_DESK.jpg"}, "linkData": {"title": "Portraits of <PERSON>", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012069,HP_HERO1_W_SU234701_IMAGE"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Desk/Part2/SU234701_Control_PT2_img3_DESK.jpg"}, "linkData": {"title": "Portraits of <PERSON>", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012069,HP_HERO1_W_SU234701_IMAGE"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/A_VERSION/Desk/Part2/SU234701_Control_PT2_img4_DESK.jpg"}, "linkData": {"title": "Portraits of <PERSON>", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,30012069,HP_HERO1_W_SU234701_IMAGE"}}}}]}}]}}}}, {"instanceName": "optly-placeholder-2", "instanceDesc": "02_08_23_UNREC_BLANK", "experimentRunning": true, "name": "OptimizelyPlaceholder", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "0", "large": "0"}}}, {"instanceName": "013123_Certona_1", "instanceDesc": "013123_Certona_1", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "2rem", "large": "3rem"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"margin": "36px auto 36px", "flexDirection": "column", "padding": "0", "maxWidth": "1920px"}, "components": [{"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"lazy": true, "style": {"width": "100%", "@media only screen and (max-width:768px)": {"padding": "0", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}, "@media only screen and (min-width:769px)": {"padding": "0", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}}, "props": {"style": {"width": "100%"}, "className": "fullBleedCertona"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome2_rr", "displayTitle": true, "fullWidth": true, "certonaTitle": {"title": "Welcome to your new era", "style": {"mobile": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#707070", "lineHeight": "1.6rem", "fontSize": "4.1vw", "textAlign": "left", "padding": "0px 0 0 13px", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}, "desktop": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#707070", "lineHeight": "1.6rem", "fontSize": "1.1vw", "textAlign": "left", "padding": "0px 0 0 1.5%", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}}}, "layout": "carousel", "centerMode": false, "useMobileConfig": true, "defaultslidesToShowSlick": 4, "defaultslidesToScrollSlick": 4, "displayPlayPauseButton": false, "resslidesToShowSlick": 4, "resslidesToScrollSlick": 4, "arrows": true, "autoplay": false, "pauseOnHover": true, "infinite": false, "priceFlag": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0029/669/822/assets/CERTONA/CertonaCarat_Left.svg", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0029/669/822/assets/CERTONA/CertonaCarat_Left.svg", "nextArrowAlt": "Next", "arrowMaxWidth": "40px", "arrowPosition": "-8px", "responsive": [{"breakpoint": 1350, "settings": {"slidesToShow": 4, "slidesToScroll": 4}}, {"breakpoint": 768, "settings": {"slidesToShow": 1.3, "slidesToScroll": 1.3}}], "productTextStyles": {"productTitle": {"style": {"color": "#767676", "textAlign": "left", "fontSize": "0.75rem", "margin": "10px 13px"}}, "productPrice": {"style": {"display": "none"}}, "productSalePrice": {"style": {"display": "none"}}}, "size": {"width": "100%", "height": "150px"}, "productMarketingFlag": {"style": {"fontWeight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "maxWidth": "unset", "padding": "0px", "width": "auto"}}, "productCardImageStyles": {"width": "19vw", "margin": "0", "maxWidth": "unset", "padding": "0px"}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}}}, {"instanceName": "optly-placeholder-4", "instanceDesc": "2023-03-14 UNREC4", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 480, "margin": "0 auto", "maxWidth": "640px", "width": "100%"}, "desktop": {"backgroundColor": "#ccc", "height": 306, "margin": "0 auto", "maxWidth": "1920px", "width": "100%"}}, "style": {"margin": "0 auto 36px", "maxWidth": "640px", "position": "relative"}, "desktopStyle": {"maxWidth": "1920px", "paddingBottom": "8px"}, "components": [{"instanceDesc": "", "name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": false, "slidesToShow": 1, "speed": 0, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause Slideshow", "playAltText": "Play Slideshow", "buttonStyle": {"height": "32px", "right": "4px", "padding": "4px", "position": "absolute", "bottom": "4px", "width": "32px", "zIndex": "1"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_play--white.svg", "pauseBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_pause--white.svg"}}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_4/Mob/SP234791_img1_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_4/Desk/SP234791_img1_DESK.jpg"}, "linkData": {"title": "A little dressed, a little easy, and all good.", "to": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,29759005,HP_HERO2_B"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_4/Mob/SP234791_img2_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_4/Desk/SP234791_img2_DESK.jpg"}, "linkData": {"title": "A little dressed, a little easy, and all good.", "to": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,29759005,HP_HERO2_B"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_4/Mob/SP234791_img3_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_4/Desk/SP234791_img3_DESK.jpg?v=1"}, "linkData": {"title": "A little dressed, a little easy, and all good.", "to": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,29759005,HP_HERO2_B"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_4/Mob/SP234791_img4_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_4/Desk/SP234791_img4_DESK.jpg"}, "linkData": {"title": "A little dressed, a little easy, and all good.", "to": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,29759005,HP_HERO2_B"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_4/Mob/SP234791_img5_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_4/Desk/SP234791_img5_DESK.jpg"}, "linkData": {"title": "A little dressed, a little easy, and all good.", "to": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,29759005,HP_HERO2_B"}}}}]}}]}}}}, {"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "10px 13px 0", "@media only screen and (min-width:768px)": {"align-items": "center", "flexDirection": "row", "justifyContent": "space-between", "padding": "12px 26px 0px 26px"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"color": "#767676", "fontSize": "30px", "fontWeight": "400", "padding": "0 0 10px", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(35px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 40px)", "marginRight": "8px", "padding": "0"}}, "components": ["A little dressed, a little easy, and all good."]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta full-width", "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop Shorts"}, "submenu": [{"text": "Girls", "href": "/browse/category.do?cid=14403#pageId=0&department=48&mlink=5058,29759005,HP_HERO2_G"}, {"text": "Boys", "href": "/browse/category.do?cid=6191#pageId=0&department=16&mlink=5058,29759005,HP_HERO2_B"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1121815#pageId=0&department=165&mlink=5058,29759005,HP_HERO2_TG"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1121839#pageId=0&department=166&mlink=5058,29759005,HP_HERO2_TB"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1102200#pageId=0&department=165&mlink=5058,29759005,HP_HERO2_BG"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1102201#pageId=0&department=166&mlink=5058,29759005,HP_HERO2_BB"}]}}]}}}]}}]}}, {"instanceName": "optly-placeholder-3", "instanceDesc": "2023-03-01 VisNav", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "400px", "large": "306px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 3.25rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 350, "margin": "0 auto 2rem", "maxWidth": "1920px", "width": "100%"}}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 auto 2.5rem", "maxWidth": "1920px"}, "components": [{"instanceDesc": "wrapper1 tile1", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"width": "50%", "position": "relative", "marginBottom": "3px"}, "desktop": {"order": "2", "width": "25%", "position": "relative"}}, "data": {"style": {}, "components": [{"instanceDesc": "vdn_01", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "New Arrivals", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_NewArrivals_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_NewArrivals_DESK.jpg"}, "linkData": {"title": "Shop New Arrivals", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_1_W_SP234114_IMAGE"}}, "ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "New Arrivals"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_1_W_SP234114_CTA"}, {"text": "Maternity", "href": "/browse/category.do?cid=11437#pageId=0&department=136&mlink=5058,29668922,HP_VCN_1_MAT_SP234114_CTA"}, {"text": "Men", "href": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,29668922,HP_VCN_1_M_SP234114_CTA"}, {"text": "Girls", "href": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,29668922,HP_VCN_1_G_SP234114_CTA"}, {"text": "Boys", "href": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,29668922,HP_VCN_1_B_SP234114_CTA"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,29668922,HP_VCN_1_TG_SP234114_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,29668922,HP_VCN_1_TB_SP234114_CTA"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,29668922,HP_VCN_1_BG_SP234114_CTA"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,29668922,HP_VCN_1_BB_SP234114_CTA"}]}}]}}}, {"instanceDesc": "VCN Dropdown CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta white", "style": {"&.wcd_hp-cta.white button": {"color": "#767676"}, "&.wcd_hp-cta button": {"justifyContent": "normal"}, "&.wcd_hp-cta.white button:focus, &.wcd_hp-cta.white button:hover": {"backgroundColor": "transparent", "borderColor": "#fff", "color": "#767676"}, "&.wcd_hp-cta.white a:hover, &.wcd_hp-cta.white a:focus": {"backgroundColor": "#e9e9e9", "borderColor": "#e9e9e9", "color": "#767676"}, "&.wcd_hp-cta>div ul": {"marginLeft": "15px"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "One-Pieces"}, "submenu": [{"text": "Baby Girl", "href": "/browse/category.do?cid=1027203#pageId=0&department=165&mlink=5058,29739196,HP_BABY_VCN_1_BG_CTA"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1027202#pageId=0&department=166&mlink=5058,29739196,HP_BABY_VCN_1_BB_CTA"}]}}]}}}, {"instanceDesc": "Arrow CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta caret-vcn", "mobilePositionAboveContent": false, "style": {"paddingLeft": "15px"}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1027291#pageId=0&department=136&mlink=5058,29710477,HP_W_1_CTA"}, "composableButtonData": {"children": "Swimwear"}}]}}}]}}, {"instanceDesc": "wrapper2 tile2", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"width": "50%", "position": "relative", "marginBottom": "3px"}, "desktop": {"order": "2", "width": "25%", "position": "relative"}}, "data": {"style": {}, "components": [{"instanceDesc": "vdn_02", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Women", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_Women_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_Women_DESK.jpg"}, "linkData": {"title": "Shop Women", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_2_W_SP234114_IMAGE"}}}}, {"instanceDesc": "Arrow CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta caret-vcn", "mobilePositionAboveContent": false, "style": {"paddingLeft": "15px"}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=5664#pageId=0&department=136&mlink=5058,29710477,HP_W_2_CTA"}, "composableButtonData": {"children": "<PERSON><PERSON>"}}]}}}]}}, {"instanceDesc": "wrapper3 tile3", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"order": "4", "width": "50%", "position": "relative", "marginBottom": "3px"}, "desktop": {"order": "3", "width": "25%", "position": "relative"}}, "data": {"style": {}, "components": [{"instanceDesc": "vdn_03", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Women", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_Men_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_Men_DESK.jpg"}, "linkData": {"title": "Shop Women", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_2_W_SP234114_IMAGE"}}}}, {"instanceDesc": "Arrow CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta caret-vcn", "mobilePositionAboveContent": false, "style": {"paddingLeft": "15px"}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=13658#pageId=0&department=136&mlink=5058,29710477,HP_W_3_CTA"}, "composableButtonData": {"children": "Dresses"}}]}}}]}}, {"instanceDesc": "wrapper4 tile4", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"order": "3", "width": "50%", "position": "relative", "marginBottom": "3px"}, "desktop": {"order": "4", "width": "25%", "position": "relative"}}, "data": {"style": {}, "components": [{"instanceDesc": "vdn_04", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Women", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_ProjectGap_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_ProjectGap_DESK.jpg"}, "linkData": {"title": "Shop Women", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_2_W_SP234114_IMAGE"}}}}, {"instanceDesc": "Arrow CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta caret-vcn", "mobilePositionAboveContent": false, "style": {"paddingLeft": "15px"}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1041308#pageId=0&department=136&mlink=5058,29710477,HP_W_4_CTA"}, "composableButtonData": {"children": "Shorts"}}]}}}]}}, {"instanceDesc": "wrapper1 tile5", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"width": "50%", "position": "relative", "marginBottom": "3px"}, "desktop": {"order": "2", "width": "25%", "position": "relative"}}, "data": {"style": {}, "components": [{"instanceDesc": "vdn_05", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "New Arrivals", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_Girls_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_Girls_DESK.jpg"}, "linkData": {"title": "Shop New Arrivals", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_1_W_SP234114_IMAGE"}}}}, {"instanceDesc": "Arrow CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta caret-vcn", "mobilePositionAboveContent": false, "style": {"paddingLeft": "15px"}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1027291#pageId=0&department=136&mlink=5058,29710477,HP_W_1_CTA"}, "composableButtonData": {"children": "Swimwear"}}]}}}]}}, {"instanceDesc": "wrapper2 tile6", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"width": "50%", "position": "relative", "marginBottom": "3px"}, "desktop": {"order": "2", "width": "25%", "position": "relative"}}, "data": {"style": {}, "components": [{"instanceDesc": "vdn_06", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Women", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_Boys_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_Boys_DESK.jpg"}, "linkData": {"title": "Shop Women", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_2_W_SP234114_IMAGE"}}}}, {"instanceDesc": "Arrow CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta caret-vcn", "mobilePositionAboveContent": false, "style": {"paddingLeft": "15px"}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=5664#pageId=0&department=136&mlink=5058,29710477,HP_W_2_CTA"}, "composableButtonData": {"children": "<PERSON><PERSON>"}}]}}}]}}, {"instanceDesc": "wrapper3 tile7", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"order": "4", "width": "50%", "position": "relative", "marginBottom": "3px"}, "desktop": {"order": "3", "width": "25%", "position": "relative"}}, "data": {"style": {}, "components": [{"instanceDesc": "vdn_07", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Women", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_ToddlerGirl_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_ToddlerGirl_DESK.jpg"}, "linkData": {"title": "Shop Women", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_2_W_SP234114_IMAGE"}}}}, {"instanceDesc": "Arrow CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta caret-vcn", "mobilePositionAboveContent": false, "style": {"paddingLeft": "15px"}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=13658#pageId=0&department=136&mlink=5058,29710477,HP_W_3_CTA"}, "composableButtonData": {"children": "Dresses"}}]}}}]}}, {"instanceDesc": "wrapper8 tile8", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"order": "3", "width": "50%", "position": "relative", "marginBottom": "3px"}, "desktop": {"order": "4", "width": "25%", "position": "relative"}}, "data": {"style": {}, "components": [{"instanceDesc": "vdn_08", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Women", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_ToddlerBoy_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_ToddlerBoy_DESK.jpg"}, "linkData": {"title": "Shop Women", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_2_W_SP234114_IMAGE"}}}}, {"instanceDesc": "Arrow CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta caret-vcn", "mobilePositionAboveContent": false, "style": {"paddingLeft": "15px"}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1041308#pageId=0&department=136&mlink=5058,29710477,HP_W_4_CTA"}, "composableButtonData": {"children": "Shorts"}}]}}}]}}, {"instanceDesc": "wrapper9 tile9", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"width": "50%", "position": "relative", "marginBottom": "3px"}, "desktop": {"order": "2", "width": "25%", "position": "relative"}}, "data": {"style": {}, "components": [{"instanceDesc": "vdn_09", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "New Arrivals", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_BabyGirl_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_BabyGirl_DESK.jpg"}, "linkData": {"title": "Shop New Arrivals", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_1_W_SP234114_IMAGE"}}}}, {"instanceDesc": "Arrow CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta caret-vcn", "mobilePositionAboveContent": false, "style": {"paddingLeft": "15px"}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1027291#pageId=0&department=136&mlink=5058,29710477,HP_W_1_CTA"}, "composableButtonData": {"children": "Swimwear"}}]}}}]}}, {"instanceDesc": "wrapper10 tile10", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"width": "50%", "position": "relative", "marginBottom": "3px"}, "desktop": {"order": "2", "width": "25%", "position": "relative"}}, "data": {"style": {}, "components": [{"instanceDesc": "vdn_10", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Women", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_BabyBoy_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_BabyBoy_DESK.jpg"}, "linkData": {"title": "Shop Women", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_2_W_SP234114_IMAGE"}}}}, {"instanceDesc": "Arrow CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta caret-vcn", "mobilePositionAboveContent": false, "style": {"paddingLeft": "15px"}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=5664#pageId=0&department=136&mlink=5058,29710477,HP_W_2_CTA"}, "composableButtonData": {"children": "<PERSON><PERSON>"}}]}}}]}}, {"instanceDesc": "wrapper11 tile11", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"order": "4", "width": "50%", "position": "relative", "marginBottom": "3px"}, "desktop": {"order": "3", "width": "25%", "position": "relative"}}, "data": {"style": {}, "components": [{"instanceDesc": "vdn_11", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Women", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_VacationShop_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_VacationShop_DESK.jpg"}, "linkData": {"title": "Shop Women", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_2_W_SP234114_IMAGE"}}}}, {"instanceDesc": "Arrow CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta caret-vcn", "mobilePositionAboveContent": false, "style": {"paddingLeft": "15px"}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=13658#pageId=0&department=136&mlink=5058,29710477,HP_W_3_CTA"}, "composableButtonData": {"children": "Dresses"}}]}}}]}}, {"instanceDesc": "wrapper12 tile12", "name": "div", "type": "builtin", "tileStyle": {"mobile": {"order": "3", "width": "50%", "position": "relative", "marginBottom": "3px"}, "desktop": {"order": "4", "width": "25%", "position": "relative"}}, "data": {"style": {}, "components": [{"instanceDesc": "vdn_12", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Women", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Mob/SU235074_LinenShop_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_3/Desk/SU235074_LinenShop_DESK.jpg"}, "linkData": {"title": "Shop Women", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,29668922,HP_VCN_2_W_SP234114_IMAGE"}}}}, {"instanceDesc": "Arrow CTA", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"ctaList": {"className": "wcd_hp-cta caret-vcn", "mobilePositionAboveContent": false, "style": {"paddingLeft": "15px"}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1041308#pageId=0&department=136&mlink=5058,29710477,HP_W_4_CTA"}, "composableButtonData": {"children": "Shorts"}}]}}}]}}]}}}}, {"instanceName": "optly-placeholder-5", "instanceDesc": "2023-03-28 UNREC5", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 480, "margin": "0 auto", "maxWidth": "640px", "width": "100%"}, "desktop": {"backgroundColor": "#ccc", "height": 306, "margin": "0 auto", "maxWidth": "1920px", "width": "100%"}}, "style": {"margin": "0 auto 36px", "maxWidth": "640px", "position": "relative"}, "desktopStyle": {"maxWidth": "1920px", "paddingBottom": "8px"}, "components": [{"instanceDesc": "", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"margin": "0px auto"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_5/Mob/SP234541_img_MOB.jpg"}, "linkData": {"to": "/browse/category.do?cid=13658#pageId=0&department=136&mlink=5058,29739196,HP_HERO3_W", "title": "We see you, dress season."}}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"margin": "0px auto"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"tileStyle": {"desktop": {"width": "25%"}}, "background": {"image": {"alt": "Baby", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_5/Desk/SP234541_img1_DESK.jpg"}, "linkData": {"to": "/browse/category.do?cid=6437#pageId=0&department=165&mlink=5058,29739196,HP_HERO3_BG", "title": "We see you, dress season."}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"tileStyle": {"desktop": {"width": "25%"}}, "background": {"image": {"alt": "Women", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_5/Desk/SP234541_img2_DESK.jpg"}, "linkData": {"to": "/browse/category.do?cid=13658#pageId=0&department=136&mlink=5058,29739196,HP_HERO3_W", "title": "We see you, dress season."}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"tileStyle": {"desktop": {"width": "25%"}}, "background": {"image": {"alt": "<PERSON>ler Girl", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_5/Desk/SP234541_img3_DESK.jpg"}, "linkData": {"to": "/browse/category.do?cid=6436#pageId=0&department=165&mlink=5058,29739196,HP_HERO3_TG", "title": "We see you, dress season."}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"tileStyle": {"desktop": {"width": "25%"}}, "background": {"image": {"alt": "Women", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0030/012/069/assets/UNREC_5/Desk/SP234541_img4_DESK.jpg"}, "linkData": {"to": "/browse/category.do?cid=13658#pageId=0&department=136&mlink=5058,29739196,HP_HERO3_W", "title": "We see you, dress season."}}}}]}}}}, {"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "10px 13px 0", "@media only screen and (min-width:768px)": {"align-items": "center", "flexDirection": "row", "justifyContent": "space-between", "padding": "12px 26px 0px 26px"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"color": "#767676", "fontSize": "30px", "fontWeight": "400", "padding": "0 0 10px", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(35px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 40px)", "marginRight": "8px", "padding": "0"}}, "components": ["We see you, dress season."]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta full-width", "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop New Dresses"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=13658#pageId=0&department=136&mlink=5058,29739196,HP_HERO3_W"}, {"text": "Girls", "href": "/browse/category.do?cid=6300#pageId=0&department=48&mlink=5058,29739196,HP_HERO3_G"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=6436#pageId=0&department=165&mlink=5058,29739196,HP_HERO3_TG"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=6437#pageId=0&department=165&mlink=5058,29739196,HP_HERO3_BG"}]}}]}}}]}}]}}, {"instanceName": "optly-placeholder-6", "instanceDesc": "2023-01-31 UNREC6", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": true, "defaultHeight": {"small": "0", "large": "0"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 480, "margin": "0 auto", "maxWidth": "640px", "width": "100%"}, "desktop": {"backgroundColor": "#ccc", "height": 306, "margin": "0 auto", "maxWidth": "1920px", "width": "100%"}}, "style": {"margin": "0 auto 36px", "maxWidth": "640px", "position": "relative"}, "desktopStyle": {"maxWidth": "1920px", "paddingBottom": "8px"}, "components": [{"instanceDesc": "Banner Media - Two Carousel Types", "name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": false, "slidesToShow": 1, "speed": 0, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause Slideshow", "playAltText": "Play Slideshow", "buttonStyle": {"height": "32px", "right": "4px", "padding": "4px", "position": "absolute", "top": "4px", "width": "32px", "zIndex": "1"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_play--white.svg", "pauseBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_pause--white.svg"}}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_6/Mob/SP234107_img1_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_6/Desk/SP234107_img1_DESK.jpg"}, "linkData": {"to": "/browse/category.do?cid=34608#pageId=0&department=136&style=1032848&mlink=5058,29668922,HP_HERO4_W", "title": "Bestseller. The Big Shirt. It truly is the perfect white shirt"}}, "overlay": {"alt": "Bestseller. The Big Shirt. It truly is the perfect white shirt", "srcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_6/Mob/SP234107_copy1_MOB.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_6/Desk/SP234107_copy1_DESK.svg"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_6/Mob/SP234107_img2_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_6/Desk/SP234107_img2_DESK.jpg"}, "linkData": {"to": "/browse/category.do?cid=5664#pageId=0&department=136&style=1137567&mlink=5058,29668922,HP_HERO4_W", "title": "Bestseller. The '70s Flare. These jeans are pure magic."}}, "overlay": {"alt": "Bestseller. The '70s Flare. These jeans are pure magic.", "srcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_6/Mob/SP234107_copy2_MOB.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_6/Desk/SP234107_copy2_DESK.svg"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_6/Mob/SP234107_img3_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_6/Desk/SP234107_img3_DESK.jpg"}, "linkData": {"to": "/browse/category.do?cid=17076#pageId=0&department=136&style=1156279&mlink=5058,29668922,HP_HERO4_W", "title": "Bestseller. The Rib Halter Tank. Obsessed with this tank!"}}, "overlay": {"alt": "Bestseller. The Rib Halter Tank. Obsessed with this tank!", "srcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_6/Mob/SP234107_copy3_MOB.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_6/Desk/SP234107_copy3_DESK.svg"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0029/698/988/assets/UNREC_6/Mob/SP234613_img_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0029/698/988/assets/UNREC_6/Desk/SP234613_img_DESK.jpg"}, "linkData": {"to": "/browse/category.do?cid=1041168#pageId=0&department=136&style=1046140&mlink=5058,29693525,HP_HERO4_W", "title": "Bestseller. The Logo Hoodie. My favorite hoodie of all time!"}}, "overlay": {"alt": "Bestseller. The Logo Hoodie. My favorite hoodie of all time!", "srcUrl": "/Asset_Archive/GPWeb/content/0029/698/988/assets/UNREC_6/Mob/SP234613_copy_MOB.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0029/698/988/assets/UNREC_6/Desk/SP234613_copy_DESK.svg"}}}]}}]}}}}]}}, {"instanceName": "013123_Certona_2", "instanceDesc": "013123_Certona_2", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "2rem", "large": "3rem"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"margin": "0 auto", "flexDirection": "column", "padding": "0", "maxWidth": "1920px"}, "components": [{"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"lazy": true, "style": {"width": "100%", "@media only screen and (max-width:768px)": {"padding": "0px 0 36px", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}, "@media only screen and (min-width:769px)": {"padding": "0px 0 36px", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}}, "props": {"style": {"width": "100%"}, "className": "fullBleedCertona"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome1_rr", "displayTitle": true, "fullWidth": true, "certonaTitle": {"title": "These Look Like You", "style": {"mobile": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#707070", "lineHeight": "1.6rem", "fontSize": "4.1vw", "textAlign": "left", "padding": "0px 0 0 13px", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}, "desktop": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#707070", "lineHeight": "1.6rem", "fontSize": "1.1vw", "textAlign": "left", "padding": "0px 0 0 1.5%", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}}}, "layout": "carousel", "centerMode": false, "useMobileConfig": true, "defaultslidesToShowSlick": 4, "defaultslidesToScrollSlick": 4, "resslidesToShowSlick": 4, "resslidesToScrollSlick": 4, "displayPlayPauseButton": false, "responsive": [{"breakpoint": 1350, "settings": {"slidesToShow": 4, "slidesToScroll": 4}}, {"breakpoint": 768, "settings": {"slidesToShow": 1.3, "slidesToScroll": 1.3}}], "arrows": true, "autoplay": false, "pauseOnHover": true, "infinite": false, "priceFlag": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0029/669/822/assets/CERTONA/CertonaCarat_Left.svg", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0029/669/822/assets/CERTONA/CertonaCarat_Left.svg", "nextArrowAlt": "Next", "arrowMaxWidth": "40px", "arrowPosition": "-8px", "productTextStyles": {"productTitle": {"style": {"color": "#767676", "textAlign": "left", "fontSize": "0.75rem", "margin": "10px 13px"}}, "productPrice": {"style": {"display": "none"}}, "productSalePrice": {"style": {"display": "none"}}}, "size": {"width": "100%", "height": "150px"}, "productMarketingFlag": {"style": {"fontWeight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "maxWidth": "unset", "padding": "0px", "width": "auto"}}, "productCardImageStyles": {"width": "19vw", "margin": "0", "maxWidth": "unset", "padding": "0px"}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}}}, {"instanceName": "optly-placeholder-7", "instanceDesc": "PZ Placeholder7", "experimentRunning": true, "name": "OptimizelyPlaceholder", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "0", "large": "0"}}}]}, "sitewide": {"desktopemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "attrition_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "isAsyncExperiment": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"instanceName": "dpg_emergency_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "redpointExperimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "mobileemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "attrition_banner_mob", "type": "builtin", "name": "div", "experimentRunning": true, "isAsyncExperiment": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"instanceName": "dpg_emergency_banner_mob", "type": "builtin", "name": "div", "experimentRunning": true, "redpointExperimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "headline": {"type": "builtin", "name": "div", "data": {"components": [{"name": "div", "type": "builtin", "data": {"props": {"style": {"position": "relative", "maxWidth": "1920px", "margin": "0 auto", "lineHeight": "0"}}, "components": [{"useGreyLoadingEffect": false, "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "transparent"}, "desktopStyle": {"width": "100%", "backgroundColor": "transparent"}}, "background": {"image": {"alt": "Want delivery by 12/24? Order your gifts TODAY Exclusive Gap Good Rewards Member Get Free Shipping on Order $50 +", "srcUrl": "/Asset_Archive/GPWeb/content/0028/266/047/assets/ldto_phase1/HO21LDTO_US_Phase1_GSB_MOB.svg", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0028/266/047/assets/ldto_phase1/HO21LDTO_US_Phase1_GSB_DESK.svg", "style": {}}, "style": {}, "desktopStyle": {}}, "overlay": {}, "ctaList": {"mobilePositionAboveContent": false, "style": {"height": "auto", "width": "auto"}, "desktopStyle": {}, "className": "", "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/GPWeb/content/0028/266/047/assets/ldto_phase1/details.html", "height": "500px"}}, "composableButtonData": {"children": "details", "capitalization": "uppercase", "style": {"backgroundColor": "transparent", "color": "#fff", "fontSize": "9px", "fontWeight": "normal", "textDecoration": "underline", "padding": "0", "position": "absolute", "bottom": "2px", "right": "1%"}, "desktopStyle": {"fontSize": "calc(0.45rem + ((1vw - 7.68px) * 0.5208))", "bottom": "5%", "right": ".15%"}}}]}}}]}}]}}, "secondary-headline": {"type": "builtin", "name": "div", "data": {"components": [{"name": "div", "type": "builtin", "sitewide-secondaryheadline-ciid": "29618430", "sitewide-secondaryheadline-desc": "update 11/29/22", "data": {"props": {"className": "gol_wcd_globalbanner wcd_sitewide-css-container"}, "components": [{"instanceDesc": "2022-08-17 WCD Sitewide CSS Modifications - https://github.gapinc.com/wcd/shared-code-library/tree/main/sitewide-css", "instanceNotice": "[WCD] ALWAYS CARRY OVER", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>.wcd_nowrap{white-space:nowrap}.wcd_underline{text-decoration:underline}.wcd_uppercase{text-transform:uppercase}.wcd_relative{position:relative}.wcd_absolute{position:absolute}.wcd_visually-hidden{height:1px;left:-10000px;overflow:hidden;position:absolute;top:auto;width:1px}.wcd_framed #sitewide-app>header,.wcd_framed #sitewide-footer,.wcd_framed .embeddedServiceHelpButton{display:none}</style>"}}, {"instanceName": "gpus_secondaryheadline-gsb-0223", "instanceDesc": "11/27 GSB - keep instanceName 0223 and TRUE for dpg", "experimentRunning": true, "name": "div", "type": "builtin", "meta": {"excludePageTypes": []}, "data": {"props": {"style": {"backgroundColor": "#fff", "position": "relative", "width": "100%", "maxWidth": "2560px", "height": "auto", "lineHeight": "0", "margin": "0 auto"}}, "components": [{"instanceDesc": "gsb1", "name": "div", "type": "builtin", "data": {"props": {"style": {"backgroundColor": "#fff", "position": "relative", "width": "100%", "lineHeight": "0", "margin": "0 auto"}}, "components": [{"useGreyLoadingEffect": false, "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "transparent"}, "desktopStyle": {"width": "100%", "backgroundColor": "transparent"}}, "background": {"image": {"alt": "", "srcUrl": "/Asset_Archive/GPWeb/content/0029/618/430/assets/112722_CM_USEC_GMB_img_MOB.jpg?v=2", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0029/618/430/assets/112722_CM_USEC_GMB_img_DESK.jpg"}, "linkData": {"to": "/browse/category.do?cid=1127938#pageId=0&department=136&mlink=55277,29618367,globalbanner_cybermon", "target": "_self", "title": "cyber monday 50% off your purchase"}, "style": {}, "desktopStyle": {}}, "overlay": {"alt": "cyber monday 50% off your purchase featuring just added styles plus free shipping applied at checkout exclusions apply", "srcUrl": "/Asset_Archive/GPWeb/content/0029/618/430/assets/112722_CM_USEC_GMB_copy_MOB_v2.svg?v=2", "desktopSrcUrl": "/Asset_Archive/GPWeb/content/0029/618/430/assets/112722_CM_USEC_GMB_copy_DESK_v2.svg"}, "ctaList": {"mobilePositionAboveContent": false, "style": {"height": "auto", "width": "auto"}, "desktopStyle": {}, "className": "", "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/buy/promo_legal_details.do?promoId=931019", "height": "500px"}}, "composableButtonData": {"children": "Exclusions apply. ", "style": {"backgroundColor": "transparent", "color": "#fff", "fontSize": "10px", "fontWeight": "normal", "textDecoration": "none", "textTransform": "none", "padding": "0", "position": "absolute", "bottom": "1px", "right": "48px", "minHeight": "unset", "border": "none"}, "desktopStyle": {"fontSize": "10px", "bottom": "3px", "right": "48px", "@media only screen and (max-width: 1024px)": {"bottom": "1px"}}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/buy/promo_legal_details.do?promoId=931019", "height": "500px"}}, "composableButtonData": {"children": "details", "capitalization": "uppercase", "style": {"backgroundColor": "transparent", "color": "#fff", "fontSize": "10px", "fontWeight": "normal", "textDecoration": "underline", "padding": "0", "position": "absolute", "bottom": "1px", "right": "2px", "minHeight": "unset", "border": "none"}, "desktopStyle": {"fontSize": "10px", "bottom": "3px", "right": "3px", "@media only screen and (max-width: 1024px)": {"bottom": "1px"}}}}]}}}]}}]}}, {"instanceName": "gpus_secondaryheadline-pzpod", "instanceDesc": "Keep for PZPod GSB", "gpus_secondaryheadline-pzpod-CIID": "29618430", "type": "builtin", "name": "div", "experimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"meta": {"excludePageTypes": []}, "shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "edfslarge": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-large", "type": "sitewide", "name": "MktEdfsLarge", "tileStyle": {"alignItems": "center", "display": "flex", "height": "40px", "margin-top": "1px", "textTransform": "uppercase"}, "experimentRunning": true, "data": {"shouldWaitForOptimizely": "true", "defaultData": {"text": "Free shipping on $50+ for rewards members", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=1194685", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "Sign in or join", "path": "/my-account/sign-in", "style": {}}}}]}}, "edfssmall": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-small", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"shouldWaitForOptimizely": "true", "lazy": false, "defaultHeight": {"small": "50px", "large": "80px"}, "isVisible": {"large": false, "small": true}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto"}, "components": [{"instanceDesc": "WCD HP CSS Modifications", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>#sitewide-app > header > div.sitewide-13o7eu2 > div > div:nth-child(2) > div > div > div > button {text-transform:uppercase}</style>"}}, {"type": "sitewide", "name": "MktEdfsSmall", "data": {"lazy": false, "experimentRunning": false, "styles": {"headline": {}, "detailsButton": {}}, "defaultData": {"textStrong": "", "text": "Free shipping on $50+ for rewards members", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=1194685", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "Sign in or join", "path": "/my-account/sign-in", "style": {}}}}]}}}}]}}, "promorover": {"type": "builtin", "name": "div", "data": {"components": [{"sitewide-promorover-desc": "******** ILP rover", "instanceName": "gpus_promo_sticker_072021", "name": "MktSticker", "type": "sitewide", "experimentRunning": true, "data": {"shouldWaitForOptimizely": true, "placeholderSettings": {"useGreyLoadingEffect": false, "mobile": {"height": 0, "width": 0}, "desktop": {"height": 0, "width": 0}}, "largeImg": "/Asset_Archive/GPWeb/content/0027/281/492/assets/ILP_rover_widget.svg", "altText": "Gap Good Rewards. Earn points on every purchase. 100 points = $1 reward. Free fast shipping on all orders over $50.", "localStorageKey": "wcd_gpRoverStorage_081821", "localStorageVal": "wcd_gpRoverHasBeenClosed_081821", "stickerAriaLabel": "stickerAriaLabel", "stickerCloseButtonAriaLabel": "stickerCloseButtonAriaLabel", "showModal": true, "modalCloseButtonAriaLabel": "Close", "modalTitle": "Gap Good Rewards", "modalUrlDetail": "/buy/promo_legal_details.do?promoId=798745", "modalPopupAriaLabel": "modalPopupAriaLabel", "options": {"includePageTypes": ["category", "division", "home", "product"]}}}]}}, "countdown": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "gpus_secondaryheadline_cdclock", "instanceDesc": "11/29 countdown clock", "type": "sitewide", "name": "LayoutComponent", "experimentRunning": false, "meta": {"excludePageTypes": []}, "data": {"lazy": false, "defaultHeight": {"small": "53px", "large": "53px"}, "mobile": {"shouldDisplay": true, "data": {"style": {"width": "100%", "backgroundColor": "#fff", "boxSizing": "border-box", "margin": "0px auto 0px auto", "padding": "5px 8px 5px", "textAlign": "center", "color": "#2b2b2b", "borderBottom": "1px solid #2b2b2b", "lineHeight": "1"}, "components": [{"type": "builtin", "instanceDesc": "wrapperMain - text/clock", "name": "div", "data": {"props": {"style": {"background": "transparent", "display": "inline", "flexDirection": "row"}}, "components": [{"type": "builtin", "instanceDesc": "wrapper - clock/copy", "name": "div", "data": {"props": {"style": {"background": "transparent", "display": "inline", "flexDirection": "column", "margin": "0 auto", "alignItems": "baseline"}}, "components": [{"type": "builtin", "name": "div", "instanceDesc": "Clock Inner Wrapper", "data": {"props": {"style": {"fontFamily": "'Gap Sans', Helvetica, Arial, Roboto, sans-serif !important", "background": "transparent", "display": "flex", "flexDirection": "row", "alignItems": "baseline", "letterSpacing": "0px", "fontSize": "calc(0.75rem + ((1vw - 3.2px) * 3.8095))", "justifyContent": "center", "flexWrap": "wrap"}}, "components": [{"type": "builtin", "name": "div", "instanceDesc": "FirstHeaderMobile", "data": {"props": {"style": {"display": "inline", "paddingRight": "1px", "alignItems": "baseline", "textTransform": "uppercase"}}, "components": ["Cyber Monday Ends in: "]}}, {"type": "sitewide", "name": "Countdown", "data": {"endDate": "2023-06-20T23:59:59.0-08:00", "ongoingHeadline": {"type": "sitewide", "name": "TextHeadline", "data": {"text": "", "className": {}, "style": {"mobile": {"color": "#2b2b2b", "textTransform": "uppercase", "alignItems": "baseline"}, "desktop": {}, "desktopAndMobile": {}}}}, "endedHeadline": {"type": "sitewide", "name": "TextHeadline", "data": {"text": "", "className": {}, "style": {"mobile": {"display": "none"}, "desktop": {"display": "none"}}}}, "timeDisplayText": {"day": {"showAttribute": false, "showAtZero": false, "text": "D", "textSingular": "D"}, "hour": {"showAttribute": true, "showAtZero": true, "text": "H", "textSingular": "H"}, "minute": {"showAttribute": true, "showAtZero": true, "text": "M", "textSingular": "M"}, "second": {"showAttribute": true, "showAtZero": true, "text": "S", "textSingular": "S"}}, "style": {"display": "inline", "fontWeight": "400", "margin": "0px", "fontSize": "calc(0.75rem + ((1vw - 3.2px) * 3.8095))", "color": "#2b2b2b", "letterSpacing": "0px", "whiteSpace": "nowrap", "paddingLeft": "8px"}}}]}}]}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"display": "block", "width": "100%", "backgroundColor": "#fff", "boxSizing": "border-box", "margin": "0px auto 0px auto", "padding": "10px 8px 7px 8px", "textAlign": "center", "color": "#2b2b2b", "borderTop": "none", "borderBottom": "1px solid #2b2b2b"}, "components": [{"type": "builtin", "instanceDesc": "wrapperMain - text/clock", "name": "div", "data": {"props": {"style": {"background": "transparent", "display": "flex", "flexDirection": "column"}}, "components": [{"type": "builtin", "instanceDesc": "wrapper - clock/copy", "name": "div", "data": {"props": {"style": {"background": "transparent", "display": "flex", "flexDirection": "column", "margin": "0 auto", "alignItems": "baseline"}}, "components": [{"type": "builtin", "name": "div", "instanceDesc": "Clock Inner Wrapper", "data": {"props": {"style": {"fontFamily": "'Gap Sans', Helvetica, Arial, Roboto, sans-serif !important", "background": "transparent", "display": "flex", "justifyContent": "center", "flexDirection": "row", "marginBottom": "2px", "alignItems": "baseline"}}, "components": [{"type": "builtin", "name": "div", "instanceDesc": "FirstHeaderDesk", "data": {"props": {"style": {"display": "flex", "paddingRight": "15px", "alignItems": "baseline", "textTransform": "uppercase", "letterSpacing": "0px", "fontSize": "calc(1rem + ((1vw - 7.68px) * 0.1736))"}}, "components": ["Cyber Monday Ends in: "]}}, {"type": "sitewide", "name": "Countdown", "instanceDesc": "Countdown Ticker Desktop", "data": {"endDate": "2023-06-20T23:59:59.0-08:00", "ongoingHeadline": {"type": "sitewide", "name": "TextHeadline", "data": {"text": "", "className": {}, "style": {"mobile": {"color": "#2b2b2b", "textTransform": "uppercase", "alignItems": "baseline"}, "desktop": {"marginBottom": "0", "marginRight": "5px", "color": "#2b2b2b", "textTransform": "uppercase"}, "desktopAndMobile": {}}}}, "endedHeadline": {"type": "sitewide", "name": "TextHeadline", "data": {"text": "", "className": {}, "style": {"mobile": {"display": "none"}, "desktop": {"display": "none"}}}}, "timeDisplayText": {"day": {"showAttribute": false, "showAtZero": false, "text": "D", "textSingular": "D"}, "hour": {"showAttribute": true, "showAtZero": true, "text": "H", "textSingular": "H"}, "minute": {"showAttribute": true, "showAtZero": true, "text": "M", "textSingular": "M"}, "second": {"showAttribute": true, "showAtZero": true, "text": "S", "textSingular": "S"}}, "style": {"fontWeight": "400", "margin": "0", "fontSize": "calc(1rem + ((1vw - 7.68px) * 0.1736))", "color": "#2b2b2b", "letterSpacing": "0px"}}}]}}]}}]}}]}}}}]}}, "footer": {"name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "582px", "large": "435px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "width": "100%"}, "components": [{"name": "Footer", "type": "sitewide", "data": {"socialLinks": [{"to": "https://www.facebook.com/gap/", "text": "Follow Gap on Facebook"}], "emailRegistration": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<h3 class=\"wcd_footer_h1 uppercase\">See It First</h3>"}}, "emailPlaceholderText": "Enter your email address", "submitButtonText": "Join", "submitButtonOptions": {"mobile": {"className": "wcd_footer_cta"}, "desktop": {"className": "wcd_footer_cta"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<p class=\"wcd_footer_copy legal\"><sup>*</sup>Valid for first-time registrants only & applies to reg. priced items only.<br><a onclick=\"return contentItemLink(this,'','CS_Footer_PrivacyPolicy');\" href=\"https://corporate.gapinc.com/en-us/consumer-privacy-policy\" target=\"_blank\" class=\"uppercase nowrap\">Privacy Policy</a>"}}}, "marketingBannerLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"instanceName": "footer_overrides", "instanceDesc": "<PERSON><PERSON> Footer Overrides", "name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<style>#sitewide-footer,#sitewide-footer button,#sitewide-footer input,#sitewide-footer select,#sitewide-footer textarea{font-family:'Gap Sans',Helvetica,Arial,Roboto,sans-serif}#sitewide-footer{background-color:#767676;color:#fff}.gap-footer *{box-sizing:border-box}.gap-footer .nowrap{white-space:nowrap}.gap-footer .uppercase{text-transform:uppercase}.gap-footer sup{font-size:1em;line-height:0;vertical-align:baseline}.gap-footer .wcd_footer_h1{font-size:14px;font-weight:500;line-height:1.125;margin-bottom:.25em}.gap-footer .wcd_footer_copy:not(:last-child){margin-bottom:1.125em}.gap-footer .wcd_footer_copy.legal{font-size:10px}.gap-footer .wcd_footer_copy a{text-decoration:underline}.gap-footer .wcd_footer_cta a,.gap-footer .wcd_footer_cta button,.gap-footer a.wcd_footer_cta,.gap-footer button.wcd_footer_cta{-ms-flex-align:center;align-items:center;background-color:#fff;border-width:0;color:#767676;font-size:14px;font-weight:500;height:32px;-ms-flex-pack:center;justify-content:center;letter-spacing:0;padding-left:16px;padding-right:16px;text-transform:uppercase}.gap-footer .wcd_footer_cta a:hover,.gap-footer .wcd_footer_cta button:hover,.gap-footer a.wcd_footer_cta:hover,.gap-footer button.wcd_footer_cta:hover{background-color:#fff;border-width:0;color:#2b2b2b}.gap-footer .wcd_footer_cta{display:-ms-flexbox;display:flex}.gap-footer .wcd_footer_cta.full-width{width:100%}.gap-footer .wcd_footer_cta.full-width a,.gap-footer .wcd_footer_cta.full-width button{width:100%}.gap-footer .wcd_footer_cta.full-width a:not(:first-child),.gap-footer .wcd_footer_cta.full-width button:not(:first-child){margin-left:8px}.gap-footer .wcd_footer_cta.details button{background-color:transparent;color:#fff;display:inline;font-size:10px;height:auto;min-height:16px;min-width:36px;padding:0;text-decoration:underline}.gap-footer .wcd_footer_cta.details button:hover{color:#fff}.gap-footer .wcd_footer_cta a,.gap-footer .wcd_footer_cta button{display:-ms-flexbox;display:flex}.gap-footer .wcd_footer_cta span{font-size:1.125em;padding-bottom:.25em}.gap-footer .wcd_footer_cta ul{background-color:transparent;box-shadow:none;padding-bottom:4px}.gap-footer .wcd_footer_cta li{border-bottom-width:0;border-color:#fff;padding:0}.gap-footer .wcd_footer_cta li a{font-weight:400;padding-left:32px;text-transform:none}.gap-footer [data-testid=prefooter-row]{margin-bottom:0}.gap-footer .email-registration__wrapper{-ms-flex-align:start;align-items:flex-start;background-color:transparent;min-height:120px;padding:26px 0}.gap-footer .email-registration__wrapper>div{margin:0 auto;max-width:640px;width:calc(100% - 32px)}.gap-footer .email-registration__wrapper .email-registration__title{max-width:100%;padding:0;text-align:left}.gap-footer .email-registration__wrapper .email-registration__inputs{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;max-width:420px}.gap-footer .email-registration__wrapper .email-registration__disclaimer{padding-left:0}.gap-footer .email-registration__wrapper .email-registration__form{-ms-flex-align:end;align-items:flex-end;display:-ms-flexbox;display:flex;margin-bottom:24px}.gap-footer .email-registration__wrapper .email-text-input-wrapper{margin-right:24px}.gap-footer .email-registration__wrapper .email-registration__form-email{margin:0;padding-bottom:0}.gap-footer .email-registration__wrapper .email-registration__form-email input[type=email]{margin-top:0;padding:0}.gap-footer .email-registration__wrapper .email-registration__form-email span{font-size:13px;top:50%;transform:translateY(-50%)}.gap-footer .email-registration__wrapper .email-registration__form-email span.sitewide-v1qhrf-LabelText-Label{font-size:10px;text-transform:none;top:0}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container .wcd_footer_cta{min-height:32px;padding-bottom:0;padding-top:0}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container div[aria-label=loading]{transform:rotate(90deg)}.gap-footer .email-registration__wrapper .notification-after-button:empty,.gap-footer .email-registration__wrapper .notification-before-form:empty{display:none}.gap-footer .medallia-feedback-wrapper{-ms-flex-order:4;order:4;padding:0 16px;width:100%}.gap-footer .medallia-feedback-wrapper>button{-ms-flex-align:center;align-items:center;background-color:#fff;border-width:0;color:#2b2b2b;display:-ms-flexbox;display:flex;font-weight:400;height:36px;-ms-flex-pack:center;justify-content:center;letter-spacing:0;margin:0 auto;max-width:640px;padding:0 16px;width:100%}.gap-footer .medallia-feedback-wrapper>button img{margin-right:.375rem}.gap-footer .footer-copyright-section{background-color:#767676;border-top-color:#fff;border-width:0;color:#fff;-ms-flex-order:5;order:5;padding:24px 0 80px;width:100%}.gap-footer .footer-copyright-section .footer-legal__wrapper{margin:0 auto;max-width:640px;text-align:left;width:calc(100% - 32px)}.gap-footer .footer-copyright-section .footer_copyright-row{font-size:11px;line-height:1.5}.gap-footer .footer-copyright-section .footer_copyright-row:not(:last-child){margin-bottom:1.5em}.gap-footer .footer-copyright-section a,.gap-footer .footer-copyright-section button{color:inherit;font-size:inherit}.gap-footer .footer-copyright-section a:hover,.gap-footer .footer-copyright-section button:hover{text-decoration:underline}.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--divider,.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--span{color:inherit;font-size:inherit}.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--span,.gap-footer .footer-copyright-section .footer-legal__wrapper a,.gap-footer .footer-copyright-section .footer-legal__wrapper button{display:inline-block;text-transform:uppercase}.gap-footer .footer-container-wrapper{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;max-width:100%}.gap-footer .footer-container-wrapper .copy-wrapper{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;margin-bottom:12px}.gap-footer .footer-container-wrapper>div:nth-child(5){-ms-flex-direction:column;flex-direction:column;margin-left:auto;margin-right:auto;max-width:672px;width:100%}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child{margin-bottom:30px}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta{background-color:transparent;color:inherit;-ms-flex-direction:column;flex-direction:column}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta a,.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta button{background-color:transparent;color:inherit;height:24px;-ms-flex-pack:start;justify-content:flex-start}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:nth-child(2){margin-bottom:6px;padding:0 16px}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:10px;line-height:1.25}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a:not(:last-child){margin-bottom:.5em}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a:hover{text-decoration:underline}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column .wcd_footer_header{margin-bottom:.75em;text-transform:uppercase}@media only screen and (min-width:768px){.gap-footer .wcd_footer_h1{margin-bottom:1em}.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:16px;font-weight:500;line-height:1}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:14px}.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:12px}.gap-footer [data-testid=prefooter-row]{display:block;padding-bottom:48px}.gap-footer .email-registration__wrapper{padding-bottom:0;padding-left:0;padding-top:0}.gap-footer .email-registration__wrapper>div{margin-left:0;max-width:100%;width:100%}.gap-footer .email-registration__wrapper .email-text-input-wrapper{margin-right:10px}.gap-footer .footer-copyright-section{border-top-width:1px;padding-top:44px}.gap-footer .footer-copyright-section .footer-legal__wrapper{margin:0;max-width:1920px;padding-left:2.5%;padding-right:2.5%}.gap-footer .footer-container-wrapper{-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between;margin:0 auto;max-width:1920px;padding-top:30px}.gap-footer .footer-container-wrapper .copy-wrapper{-ms-flex-align:center;align-items:center;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;margin-bottom:0}.gap-footer .footer-container-wrapper .copy-wrapper>div:not(:last-child){margin-right:.75em}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:nth-child(2){display:none}.gap-footer .wcd_footer-links-wrapper{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{-ms-flex-positive:1;flex-grow:1}.gap-footer [data-testid=prefooter-row]{padding-left:2.5%;padding-right:2.5%;width:100%}.gap-footer .footer-container-wrapper>div:nth-child(3){padding-bottom:48px;padding-left:2.5%;padding-right:2.5%;width:100%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:16px}}@media only screen and (min-width:1024px){.gap-footer .wcd_footer-links-wrapper{-ms-flex-pack:start;justify-content:flex-start}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{-ms-flex-positive:initial;flex-grow:initial}.gap-footer [data-testid=prefooter-row]{padding-right:0;width:300px}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:3%;padding-left:0;width:calc(97% - 300px)}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:7%}}@media only screen and (min-width:1280px){.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:14px}.gap-footer [data-testid=prefooter-row]{width:24%}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:6%;padding-bottom:84px;width:70%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:11%}}@media only screen and (min-width:1440px){.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:24px}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:18px}.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:16px}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container .wcd_footer_cta{font-size:20px;height:52px;padding:0 26px}.gap-footer .footer-container-wrapper{padding-top:54px}.gap-footer [data-testid=prefooter-row]{width:29%}.gap-footer .footer-container-wrapper>div:nth-child(3){width:65%}}@media only screen and (min-width:1920px){.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:26px}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:21px}.gap-footer [data-testid=prefooter-row]{width:24%}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:0;width:71%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:18%}}@media only screen and (max-width:767px){.gap-footer .footer-container-wrapper>div:nth-child(2){display:none}}</style>"}}]}}}}, "customerSupportLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Mobile Footer Links", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_footer_cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "Customer Support"}, "submenu": [{"text": "Customer Service", "href": "/customerService/info.do?cid=2136&mlink=55278,********,CS_Footer_CustomerService"}, {"text": "Buy Online. Pick Up In-Store.", "href": "/customerService/info.do?cid=1161798&mlink=55278,********,LP_BOPIS_footer_CTA"}, {"text": "Store Locator", "href": "/stores"}, {"text": "GapCash", "href": "/browse/info.do?cid=99996&mlink=55278,********,LP_GapCash_footer_CTA"}, {"text": "GiftCards", "href": "/customerService/info.do?cid=2116&mlink=55278,********,CS_Footer_Giftcards"}]}}, {"buttonDropdownData": {"heading": {"text": "Gap Good Rewards"}, "submenu": [{"text": "Join <PERSON> Good Rewards", "href": "/my-account/sign-in?mlink=55278,********,UNIFOOTER_GGR_ACQ"}, {"text": "Apply for a Credit Card", "href": "/my-account/sign-in?creditOffer=barclays&sitecode=GPSSUNIFTM&mlink=55278,********,UNIFOOTER_GGR_CARD_ACQ"}, {"text": "My Rewards & Benefits", "href": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=55278,********,UNIFOOTER_MTL_RET"}, {"text": "Pay Credit Card Bill", "href": "https://gap.barclaysus.com/servicing/home?redirectAction=/payment", "target": "_blank"}]}}, {"buttonDropdownData": {"heading": {"text": "About Us"}, "submenu": [{"text": "Our Values", "href": "https://www.gapinc.com/en-us/values", "target": "_blank"}, {"text": "Sustainability", "href": "https://www.gap.com/browse/info.do?cid=1086537", "target": "_blank"}, {"text": "Equality and Belonging", "href": "https://www.gap.com/browse/info.do?cid=1179886", "target": "_blank"}, {"text": "Careers", "href": "https://www.gapinc.com/en-us/careers/gap-careers", "target": "_blank"}]}}]}}}, {"instanceDesc": "Download The App", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_app"}, "components": [{"name": "a", "type": "builtin", "data": {"props": {"href": "https://gap.onelink.me/9QBP/12b7d83a", "className": "wcd_footer_cta full-width"}, "components": ["Download The App"]}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Desktop Footer Links", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-wrapper"}, "components": [{"instanceDesc": "Desktop Footer Links - Column 1", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["Customer Support"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=2136&mlink=55278,********,CS_Footer_CustomerService"}, "components": ["Customer Service"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1161798&mlink=55278,********,LP_BOPIS_footer_CTA", "target": "_blank"}, "components": ["Buy Online. Pick Up In-Store."]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/stores"}, "components": ["Store Locator"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/browse/info.do?cid=99996&mlink=55278,********,LP_GapCash_footer_CTA"}, "components": ["GapCash"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=2116&mlink=55278,********,CS_Footer_Giftcards"}, "components": ["GiftCards"]}}]}}, {"instanceDesc": "Desktop Footer Links - Column 2", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["Gap Good Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?mlink=55278,********,UNIFOOTER_GGR_ACQ"}, "components": ["Join <PERSON> Good Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?creditOffer=barclays&sitecode=GPSSUNIFTD&mlink=55278,********,UNIFOOTER_GGR_CARD_ACQ"}, "components": ["Apply for a Credit Card"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=55278,********,UNIFOOTER_MTL_RET"}, "components": ["My Rewards & Benefits"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://gap.barclaysus.com/servicing/home?redirectAction=/payment", "target": "_blank"}, "components": ["Pay Credit Card Bill"]}}]}}, {"instanceDesc": "Desktop Footer Links - Column 3", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["About Us"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/values", "target": "_blank"}, "components": ["Our Values"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gap.com/browse/info.do?cid=1086537", "target": "_blank"}, "components": ["Sustainability"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gap.com/browse/info.do?cid=1179886", "target": "_blank"}, "components": ["Equality and Belonging"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/careers/gap-careers", "target": "_blank"}, "components": ["Careers"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/browse/info.do?cid=1174367&shortlink=12b7d83a&c=SiteFooterLink_download_our_app&pid=Mobile%20Footer%20link&source_caller=ui"}, "components": ["Get the App"]}}]}}]}}]}}}}}}]}}}}, "header": {"default": {"isStickyEnabled": true, "headerLayout": "sameRow", "fullBleedOptions": {"isFullBleedEnabled": false, "hasTransparencyLayer": false, "fullBleedContrast": "dark"}, "styles": {"marginLeft": "auto", "marginRight": "auto", "maxWidth": "1920px"}}}, "topnav": {"name": "MegaNav", "type": "sitewide", "data": {"isNavSticky": true, "classStyles": {"topnav li:not(.catnav--item)": "padding: 0;", "topnav a.divisionLink": "box-shadow: none !important; box-sizing: border-box; color: #2b2b2b; display: block; font-size: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); font-weight: 400; height: 90px; line-height: 1; min-height: 0vw; padding: 40px 0 0; position: relative; text-transform: uppercase;", "topnav a.divisionLink::before": "border-color: transparent; border-style: solid; border-width: 0 0 1px; content: ''; height: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); left: 50%; min-height: 12px; padding-bottom: 3px; position: absolute; top: 40px; transform: translateX(-50%); width: calc(100% - 2vw);", "topnav a.divisionLink._selected": "color: #2b2b2b;", "topnav li.catnav--item.sitewide-1l5zl4x": "color: #2b2b2b;", "topnav a.sitewide-l0i3ri": "color: #2b2b2b;", "topnav span.sitewide-l0i3ri": "color: #2b2b2b;", "topnav li:hover a.divisionLink": "background-color: #fff;", "topnav li:hover a.divisionLink::before": "border-color: #2b2b2b;", "topnav a.divisionLink._selected::before": "border-color: #2b2b2b;", "topnav a.divisionLink:hover": "box-shadow: none !important;", "topnav li.catnav--header > span": "border-bottom-color: #2b2b2b;", "topnav li.catnav--header > a": "border-bottom-color: #2b2b2b;", "topnav a.divisionLink.navlink-pink": "color: #e51937;", "topnav a.divisionLink.navlink-red": "color: #e51937", "topnav a.divisionLink.navlink-gift": "color: #e51937;", "topnav a.catnav--item--link.sitewide-4l9vad[data-categoryid='1187473']": "color: #e51937;", "topnav .catnav--item.catnav--item-selected": "color: #2b2b2b;", "topnav .catnav--item.catnav--item-selected a": "color: #2b2b2b;", "topnav .catnav--item--link": "max-width: 265px;", "topnav .catnav--item--link:hover": "color: #2b2b2b;", "topnav li.catnav--item.sitewide-1l5zl4xli": "color: #e28743;", "meganav": "border-top-width: 0"}, "activeDivisions": [{"name": "New", "divisionId": ["1086624"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='8792' href='/browse/category.do?cid=8792&mlink=39813,30012485, topnav_NewArrivals_w_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='/Asset_Archive/GPWeb/content/0030/013/507/assets/SU234723_img.jpg' alt='Womens New Arrivals image'><img style='position:absolute;left:0' src='/Asset_Archive/GPWeb/content/0030/013/507/assets/SU234723_CTA.svg' alt='Womens New Arrivals'></a></li></ul></li>"], ["1139272"], ["3019289", "3019290"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='63895' href='/browse/category.do?cid=3018786#pageId=0&mlink=39813,30013131,topnav_NewArrivals_Barbie_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='/Asset_Archive/GPWeb/content/0030/013/507/assets/SU234892_img.jpg?v=2' alt='Girls New Arrivals image'><img style='position:absolute;left:0' src='/Asset_Archive/GPWeb/content/0030/013/507/assets/SU234892_CTA.svg?v=2' alt='Girls New Arrivals'></a></li></ul></li>"]], "numberOfColumns": {}, "exclusionIds": [], "customStyles": {"1164542": {"inlineStyle": {"color": "#e51937"}}, "3013615": {"inlineStyle": {"color": "#e51937"}}, "3013628": {"inlineStyle": {"color": "#e51937"}}, "3013630": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Women", "divisionId": ["/browse/division.do?cid=5643&mlink=39813,30012485,Megnav_Women", "5646"], "megaNavOrder": [["1164545", "1131702"], ["1042481"], ["1122595", "1131698"]], "numberOfColumns": {"1042481": 2}, "exclusionIds": [], "customStyles": {"65179": {"colorScheme": "sale"}, "1015684": {"colorScheme": "sale"}, "1187420": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Men", "divisionId": ["/browse/division.do?cid=5063&mlink=39813,30012485,Megnav_Men&clink=30012485", "5065"], "megaNavOrder": [[], ["1164547", "1149531"], ["1042515"], ["1122755", "1076121"]], "numberOfColumns": {"1042515": 2}, "exclusionIds": [], "customStyles": {"65289": {"colorScheme": "sale"}, "1008073": {"colorScheme": "sale"}, "1187426": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Girls", "divisionId": ["/browse/division.do?cid=1137865&mlink=39813,30012485,Megnav_Girls&clink=30012485", "6256"], "megaNavOrder": [[], ["1164548", "1161294", "1056088"], ["1042516"], ["1122748", "6258"]], "numberOfColumns": {"1042516": 2}, "exclusionIds": [], "customStyles": {"65194": {"colorScheme": "sale"}, "1137652": {"colorScheme": "sale"}, "1187423": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Boys", "divisionId": ["/browse/division.do?cid=1137867&mlink=39813,30012485,Megnav_Girls&clink=30012485", "6172"], "megaNavOrder": [[], ["1164549", "1161295", "1056087"], ["1042518", "6189"], ["1122747", "6174"]], "numberOfColumns": {"1042518": 2}, "exclusionIds": [], "customStyles": {"65217": {"colorScheme": "sale"}, "1137659": {"colorScheme": "sale"}, "1187425": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=1137868&mlink=39813,30012485,Megnav_<PERSON><PERSON>&clink=30012485", "6413"], "megaNavOrder": [["1164550", "1149845"], ["1016135"], ["1016083"], ["1048209", "1067853"]], "exclusionIds": [], "customStyles": {"65236": {"colorScheme": "sale"}, "65263": {"colorScheme": "sale"}, "1185682": {"inlineStyle": {"color": "#e51937"}}, "1187427": {"inlineStyle": {"color": "#e51937"}}, "1187428": {"inlineStyle": {"color": "#e51937"}}, "1193732": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Baby", "divisionId": ["/browse/division.do?cid=1137869&mlink=39813,30012485,Megnav_Baby&clink=30012485", "6487"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='8792' href='/browse/category.do?cid=3010841#pageId=0&mlink=39813,30013507,topnav_Baby_babygapgoods_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='/Asset_Archive/GPWeb/content/0030/013/507/assets/Tile1_SU234886_img.jpg' alt='Womens New Arrivals image'><img style='position:absolute;left:0' src='/Asset_Archive/GPWeb/content/0030/013/507/assets/Tile1_SU234886_CTA.svg' alt='Shop Baby Furniture & More'></a></li></ul></li>"], ["1164551", "3018069", "1164552", "1149847"], ["95461"], ["95574"], ["1048187", "1067854"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='8792' href='/browse/category.do?cid=1055119#pageId=0&mlink=39813,30013507,topnav_baby_giftswelove_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='/Asset_Archive/GPWeb/content/0030/013/507/assets/Tile2_SU234888_img.jpg' alt='Womens New Arrivals image'><img style='position:absolute;left:0' src='/Asset_Archive/GPWeb/content/0030/013/507/assets/Tile2_SU234888_CTA.svg' alt='Shop Baby Gifts We Love'></a></li></ul></li>"]], "exclusionIds": [], "customStyles": {"65208": {"colorScheme": "sale"}, "65261": {"colorScheme": "sale"}, "1187419": {"inlineStyle": {"color": "#e51937"}}, "1187422": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Maternity", "divisionId": ["/browse/division.do?cid=5997&mlink=39813,30012485,Megnav_Maternity&clink=30012485", "5997"], "megaNavOrder": [["1164546", "3018111", "1149538"], ["1042513"], ["1188906", "1014415"], ["1122765"]], "numberOfColumns": {"1042513": 2}, "exclusionIds": [], "customStyles": {"65302": {"colorScheme": "sale"}, "1146678": {"colorScheme": "sale"}, "1187473": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["1077403"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1041308' href='/browse/category.do?cid=1041308#pageId=0&style=1032108&mlink=39813,30012485,Topnav_W_DenimShorts,visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='/Asset_Archive/GPWeb/content/0030/013/507/assets/W_JEAN_Meganavtile1.jpg' alt='womens denim shorts image'><img style='position:absolute;left:0' src='/Asset_Archive/GPWeb/content/0030/013/507/assets/W_JEAN_Meganavtile1.svg' alt='womens denim shorts image'></a></li></ul></li>"], ["1135249"], ["3018007", "3018010"], ["1137322"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='14403' href='/browse/category.do?cid=14403&nav=expmore%3Agirls%3Acategories%3Ashorts#pageId=0&style=1125408&department=48&mlink=39813,30012485, topnav_jeans_girlsshorts,visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='/Asset_Archive/GPWeb/content/0030/013/507/assets/KTB_JEANS_Meganavtile_2.jpg' alt='Girls Denim Shorts image'><img style='position:absolute;left:0' src='/Asset_Archive/GPWeb/content/0030/013/507/assets/KTB_JEANS_Meganavtile_2.svg' alt='Girls Denim Shorts'></a></li></ul></li>"]], "exclusionIds": [], "customStyles": {"3013827": {"inlineStyle": {"color": "#e51937"}}, "3013829": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Sale", "divisionId": ["1156863"], "megaNavOrder": [["1156864"]], "numberOfColumns": {"1156864": 2}, "exclusionIds": [], "customStyles": {}}, {"name": "Factory", "divisionId": ["https://www.gapfactory.com/?tid=gfsv000000"]}]}}, "promodrawer": {"name": "PromoDrawerComponentV2", "type": "sitewide", "instanceName": "promoDrawer-********", "instanceDesc": "6/7 US PD", "experimentRunning": false, "data": {"shouldWaitForOptimizely": false, "buildInfo": ["********", "GP"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "info", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "Profile", "LoyaltyValueCenter", "CustomerService"], "anchor": "bottom"}, "autoFire": "scroll", "disabledAutoFirePageTypes": ["category"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"https://secure-www.gap.com/loyalty/customer-value?target=EarnAndRedeem&tid=PD_Tile\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GPWeb/content/0030/013/648/assets/060723_PD1_US.png\" alt=\"gapcash redeem by 6/12 find out how much you have sign in valid at gap & gap factory\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "", "legalOverride": "", "genericCodeId": "", "genericCode": ""}, "promoId": "li0vtywa"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1127938#pageId=0&department=136&mlink=55276,********,PD_Tile\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GPWeb/content/0030/013/648/assets/060723_PD2_US.png\" alt=\"don't want to use your gapcash extra 30% off your purchase code YOURS exclusions apply\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "tap", "overlay": "Applied at checkout", "defaultMessage": "tap to apply", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "972577", "genericCode": "YOURS"}, "promoId": "li0vvjzq"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1041308#pageId=0&department=136&mlink=55276,********,PD_Tile\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GPWeb/content/0030/013/648/assets/060723_PD3_US.png\" alt=\"all shorts on sale\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "972597", "genericCode": ""}, "promoId": "li0vxg4q"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=8792#pageId=0&department=136&mlink=55276,********,PD_Tile\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GPWeb/content/0030/013/648/assets/060723_PD4_US.png\" alt=\"combinable with your gapcash up to 50% off across the site includes new arrivals\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile4"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "971537", "genericCode": ""}, "promoId": "li0vzke7"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSPD&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=55276,********,PD_TILE_GGR_CARD_ACQ\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GPWeb/content/0030/013/648/assets/060723_PD5_US.png\" alt=\"gap good rewards extra 25% off when you open & shop with a gap good rewards credit card ends 6/12 apply now\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile5"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "970717", "genericCode": ""}, "promoId": "li0w1dnu"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "style": {"mobile": {"fontSize": ".75em !important"}, "desktop": {"fontSize": ".75em !important"}}, "text": "{--! headerText !--}"}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}"}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "iconAltText": "Open icon", "linkWithModalDisplayStyle": "none", "subHeaderText": "(5 available)"}, "closedState": {"headerText": "time to (sun)shine! use your gapcash on today's deals", "subHeaderText": "", "iconAltText": "Closed icon"}, "aria-label": "time to (sun)shine! use your gapcash on today's deals"}, "pd_id": "pdid_li0vr6fy", "analytics": {"onOpen": {"content_id": "promo_drawer_open_content", "link_name": "promo_drawer_open_link", "promo_drawer_autofire_config": "scroll"}}}, "promoCards": [{"background": {"type": "solid", "color": "#FFD3EA"}, "tapToApply": false, "promoDetails": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-3\">For the family of four discount applied</span></p>", "mainPromoMessage": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\">Online Exclusive</span></p><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-1\" style=\"text-transform:uppercase;font-weight:800\">11/8 50% OFF </span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-1\" style=\"font-weight:800\">ALL COLD WEATHER ACCESSORIES </span></p>", "detailsLink": "Details", "pemoleCode": "1025177", "cta": [{"cta": {"label": "Men", "value": "https://www.color-hex.com/"}}, {"cta": {"label": "Women", "value": "https://www.color-hex.com/"}}], "tapToApplyStyle": "primary", "linkWrapperURL": "https://www.google.com", "linkWrapperAltText": "link", "ctaButtonStylingForDesktop": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaButtonStylingForMobile": {"buttonStyle": "border", "buttonColor": "dark"}, "detailsLinkColor": "primary", "legalDetailsLocation": "above"}, {"background": {"type": "solid", "color": "#003764"}, "tapToApply": false, "cta": [{"cta": {"label": "<PERSON><PERSON>", "value": "https://www.color-hex.com/"}}, {"cta": {"label": "Youth", "value": "https://www.color-hex.com/"}}, {"cta": {"label": "<PERSON>", "value": "https://www.color-hex.com/"}}, {"cta": {"label": "Swim", "value": "https://www.color-hex.com/"}}], "mainPromoMessage": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-2\" style=\"color:#FFFFFF\">Online Exclusive, 11/8</span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-3\" style=\"color:#FFFFFF;font-weight:800\">50% OFF ALL COLD</span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-3\" style=\"color:#FFFFFF;font-weight:800\">WEATHER ACCESSORIES</span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--eyebrow-3\" style=\"color:#FFFFFF\">No code needed</span></p>", "promoDetails": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\" style=\"color:#FFFFFF\">Exclusions apply</span></p>", "detailsLink": "Details", "pemoleCode": "1025177", "tapToApplyStyle": "primary", "linkWrapperURL": "https://www.w3schools.com", "linkWrapperAltText": "link", "ctaButtonStylingForDesktop": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaButtonStylingForMobile": {"buttonStyle": "border", "buttonColor": "dark"}, "detailsLinkColor": "primary", "legalDetailsLocation": "above"}, {"background": {"type": "solid", "color": "#003764"}, "tapToApply": false, "mainPromoMessage": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-2\" style=\"color:#FFFFFF\">Online Exclusive, Today Only, 11/8 </span></p><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-1\" style=\"color:#FFFFFF;font-weight:800\">30% OFF YOUR ORDER</span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-1\" style=\"color:#FFFFFF;font-weight:800\">EVEN CLEARANCE</span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-2\" style=\"color:#FFFFFF\">No code needed</span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-2\" style=\"color:#FFFFFF\">Exclusions apply</span></p>", "promoDetails": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-4\" style=\"color:#FFFFFF\">DISCOUNT APPLIED AT CHECKOUT</span></p>", "detailsLink": "Details", "pemoleCode": "566313", "tapToApplyStyle": "primary", "linkWrapperURL": "https://www.gap.com", "linkWrapperAltText": "link", "ctaButtonStylingForDesktop": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaButtonStylingForMobile": {"buttonStyle": "border", "buttonColor": "dark"}, "detailsLinkColor": "primary", "legalDetailsLocation": "above"}, {"background": {"type": "solid", "color": "#9EC881"}, "tapToApply": true, "mainPromoMessage": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-2\" style=\"font-weight:100\">Open &amp; Use a Navyist Rewards Credit Card &amp;</span></p><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-2\" style=\"font-weight:800\">Get an extra</span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-2\" style=\"font-weight:800\">30% off your</span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-2\" style=\"font-weight:800\">first purchase</span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\" style=\"font-weight:100\">CODE: BONUS</span></p>", "detailsLink": "Details", "pemoleCode": "566313", "tapToApplyStyle": "primary", "linkWrapperURL": "https://www.google.com", "linkWrapperAltText": "link", "ctaButtonStylingForDesktop": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaButtonStylingForMobile": {"buttonStyle": "border", "buttonColor": "dark"}, "detailsLinkColor": "primary", "legalDetailsLocation": "above"}, {"background": {"type": "solid", "color": "#E8E1FD"}, "tapToApply": false, "promoDetails": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\">Exclusions Apply</span></p>", "detailsLink": "Details", "pemoleCode": "566313", "tapToApplyStyle": "primary", "linkWrapperURL": "https://www.github.com", "linkWrapperAltText": "link", "ctaButtonStylingForDesktop": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaButtonStylingForMobile": {"buttonStyle": "border", "buttonColor": "dark"}, "detailsLinkColor": "primary", "legalDetailsLocation": "above"}, {"background": {"type": "solid", "color": "#F9ECD6"}, "tapToApply": false, "mainPromoMessage": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\">Main Message</span></p>", "detailsLink": "Details", "pemoleCode": "566313", "tapToApplyStyle": "primary", "linkWrapperURL": "https://www.google.com", "linkWrapperAltText": "link", "ctaButtonStylingForDesktop": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaButtonStylingForMobile": {"buttonStyle": "border", "buttonColor": "dark"}, "detailsLinkColor": "primary", "legalDetailsLocation": "above"}, {"background": {"type": "solid", "color": "#0F4F43"}, "tapToApply": false, "detailsLink": "Detail", "pemoleCode": "566313", "cta": [{"cta": {"label": "Women", "value": "https://www.color-hex.com/"}}], "tapToApplyStyle": "primary", "linkWrapperURL": "https://playwright.dev/", "linkWrapperAltText": "link", "ctaButtonStylingForDesktop": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaButtonStylingForMobile": {"buttonStyle": "border", "buttonColor": "dark"}, "detailsLinkColor": "primary", "legalDetailsLocation": "above"}, {"background": {"type": "solid", "color": "#0F4F43"}, "tapToApply": false, "promoDetails": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\">Secondary text Secondary text Secondary text</span></p>", "tapToApplyStyle": "primary", "linkWrapperURL": "https://www.athleta.gap.com", "linkWrapperAltText": "link", "ctaButtonStylingForDesktop": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaButtonStylingForMobile": {"buttonStyle": "border", "buttonColor": "dark"}, "detailsLinkColor": "primary", "legalDetailsLocation": "above"}, {"background": {"type": "solid", "color": "#F9ECD6"}, "tapToApply": false, "cta": [{"cta": {"label": "CTA1", "value": "https://www.color-hex.com/"}}, {"cta": {"label": "CTA 2", "value": "https://www.color-hex.com/"}}, {"cta": {"label": "CTA 3", "value": "https://www.color-hex.com/"}}], "promoDetails": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-2\">DISCOUNT APPLIED </span></p>", "mainPromoMessage": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\">Online Exclusive, 11/8</span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-1\" style=\"font-weight:700\">50% OFF ALL COLD</span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-1\" style=\"font-weight:700\">WEATHER ACCESSORIES</span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\">No code needed</span></p>", "detailsLink": "Details", "pemoleCode": "123", "tapToApplyStyle": "primary", "linkWrapperURL": "https://www.google.com", "linkWrapperAltText": "link", "ctaButtonStylingForDesktop": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaButtonStylingForMobile": {"buttonStyle": "border", "buttonColor": "dark"}, "detailsLinkColor": "primary", "legalDetailsLocation": "above"}, {"background": {"type": "solid", "color": "#003764"}, "tapToApply": false, "detailsLink": "Details", "pemoleCode": "123", "tapToApplyStyle": "primary", "linkWrapperURL": "https://www.w3schools.com", "linkWrapperAltText": "link", "ctaButtonStylingForDesktop": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaButtonStylingForMobile": {"buttonStyle": "border", "buttonColor": "dark"}, "detailsLinkColor": "primary", "legalDetailsLocation": "above"}]}, "utilitylinks": {"type": "sitewide", "name": "UtilityLinks", "data": {"style": {"fontSize": "10.5px"}, "brandBarShortcutLinks": [{"link": "/stores?sitecode=GPSSSEARCH&mlink=39813,29693256,SEARCHBAR_STORELOCATOR", "text": "Find a store"}, {"link": "/customerService/info.do?cid=1099008&sitecode=GPSSSEARCH&mlink=39813,29666338,SEARCHBAR_GGR_ACQ", "text": "Gap Good Rewards"}, {"link": "/customerService/info.do?cid=2116&sitecode=GPSSSEARCH&mlink=39813,29693256,SEARCHBAR_GIFTCARD", "text": "Gift Card"}]}}, "hamnav": {"type": "sitewide", "name": "HamburgerNav", "data": {"activeDivisions": ["1086624", "1077403", "5643", "5997", "5063", "1159071", "1137865", "1137867", "1137868", "1137869", "1188985", "3016429", "1156863", "c48620"], "exclusionIds": ["1170807"]}}, "search": {"instanceDesc": "Search config code is in the TopNav container", "type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Sweater", "Sweatpants", "<PERSON>igan", "<PERSON><PERSON><PERSON>", "Dresses", "<PERSON><PERSON><PERSON>", "Blazer", "Leggings"]}}, "logo": {"name": "Logo", "type": "sitewide", "altText": "Gap logo", "lightLogoImgPath": "/Asset_Archive/GPWeb/content/0020/452/166/assets/logo/logo_gap--light.svg", "darkLogoImgPath": "/Asset_Archive/GPWeb/content/0028/669/369/assets/logo/Gap_logo_MOB_newV2.svg", "logoImgPath": "/Asset_Archive/GPWeb/content/0028/669/369/assets/logo/Gap_logo_MOB_newV2.svg", "isSquare": true, "className": ""}}, "brand": "gap", "type": "meta", "pmcsEdgeCacheTag": "gap-homepage-en-us-stage"}