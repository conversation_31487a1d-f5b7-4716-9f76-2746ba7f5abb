{"meta.description": "pageDescription defaulted", "meta.title.overide": "pageTitle defaulted", "home": {"type": "home", "name": "HomeMultiSimple", "components": [{"instanceDesc": "WCD HP CSS Modifications - 2023-02-01", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>#main-content *{box-sizing:border-box}#main-content img,#main-content video{display:block}.slick-slide>div>div{display:block!important}.fullBleedCertona div.productCard{max-width:unset}.fullBleedCertona button.slick-arrow.slick-disabled.sitewide-0,.fullBleedCertona button.slick-arrow.slick-next.sitewide-0,.fullBleedCertona button.slick-arrow.slick-prev.sitewide-0{margin-top:0}.fullBleedCertona .mkt-certona-recs{max-width:none}.fullBleedCertona .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon{max-width:none}.fullBleedCertona .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon .mkt-certona-recs__products{max-width:none}div.wcd_hp-cta{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between}.wcd_hp-cta a,.wcd_hp-cta button,a .wcd_hp-cta,a.wcd_hp-cta,button.wcd_hp-cta{-ms-flex-align:center;align-items:center;background-color:#fff;border-color:#fff;border-style:solid;border-width:1px;color:#767676;display:-ms-flexbox;display:flex;font-size:13px;font-weight:400;height:32px;letter-spacing:0;padding:0 15px;text-transform:uppercase;width:auto}.wcd_hp-cta a:active,.wcd_hp-cta a:focus,.wcd_hp-cta a:hover,.wcd_hp-cta button:active,.wcd_hp-cta button:focus,.wcd_hp-cta button:hover,a .wcd_hp-cta:active,a .wcd_hp-cta:focus,a .wcd_hp-cta:hover,a.wcd_hp-cta:active,a.wcd_hp-cta:focus,a.wcd_hp-cta:hover,button.wcd_hp-cta:active,button.wcd_hp-cta:focus,button.wcd_hp-cta:hover{background-color:#999;border-color:#999;color:#fff}.wcd_hp-cta button,button.wcd_hp-cta{-ms-flex-pack:justify;justify-content:space-between;min-width:100px;border-color:#767676}.wcd_hp-cta button:focus,button.wcd_hp-cta:focus{outline:0}.wcd_hp-cta button span,button.wcd_hp-cta span{font-size:1.625em;height:.25em;line-height:0;margin-left:.5em;padding:0}.wcd_hp-cta>div{position:relative;width:auto}.wcd_hp-cta>div:first-child{z-index:10}.wcd_hp-cta>div:nth-child(2){z-index:9}.wcd_hp-cta>div:nth-child(3){z-index:8}.wcd_hp-cta>div:nth-child(4){z-index:7}.wcd_hp-cta>div:nth-child(5){z-index:6}.wcd_hp-cta>div:nth-child(6){z-index:5}.wcd_hp-cta>div:nth-child(7){z-index:4}.wcd_hp-cta>div:nth-child(8){z-index:3}.wcd_hp-cta>div:nth-child(9){z-index:2}.wcd_hp-cta>div:nth-child(10){z-index:1}.wcd_hp-cta>div ul{background-color:#fff;border-color:#fff;border-style:solid;border-width:0 1px 1px;box-shadow:rgba(0,0,0,.3) 0 1px 6px 0;padding:0;position:absolute}.wcd_hp-cta>div li{border-width:0;padding:0}.wcd_hp-cta>div li:first-child a{border-top-width:1px}.wcd_hp-cta>div a{background-color:transparent;border-color:#fff;border-style:solid;border-width:0;color:#767676}.wcd_hp-cta>div a:active,.wcd_hp-cta>div a:focus,.wcd_hp-cta>div a:hover{background-color:#e9e9e9;border-color:#fff;color:#2b2b2b}.wcd_hp-cta.white a,.wcd_hp-cta.white button,a .wcd_hp-cta.white,a.wcd_hp-cta.white,button.wcd_hp-cta.white{background-color:#fff;border-color:#fff;color:#767676}.wcd_hp-cta.white a:active,.wcd_hp-cta.white a:focus,.wcd_hp-cta.white a:hover,.wcd_hp-cta.white button:active,.wcd_hp-cta.white button:focus,.wcd_hp-cta.white button:hover,a .wcd_hp-cta.white:active,a .wcd_hp-cta.white:focus,a .wcd_hp-cta.white:hover,a.wcd_hp-cta.white:active,a.wcd_hp-cta.white:focus,a.wcd_hp-cta.white:hover,button.wcd_hp-cta.white:active,button.wcd_hp-cta.white:focus,button.wcd_hp-cta.white:hover{background-color:#999;border-color:#999;color:#fff}.wcd_hp-cta.arrow a,.wcd_hp-cta.arrow button,a .wcd_hp-cta.arrow,a.wcd_hp-cta.arrow,button.wcd_hp-cta.arrow{background-color:transparent;background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--black.svg);background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 13px;border-color:transparent;display:-ms-inline-flexbox;display:inline-flex;padding-left:0;padding-right:calc(1em + 15px + 6px);text-align:left;transition:background-position .25s ease-out}.wcd_hp-cta.arrow a:hover,.wcd_hp-cta.arrow button:hover,a .wcd_hp-cta.arrow:hover,a.wcd_hp-cta.arrow:hover,button.wcd_hp-cta.arrow:hover{background-color:transparent;background-position-x:calc(100% - 8px);border-color:transparent;color:#767676}.wcd_hp-cta.arrow a.white,.wcd_hp-cta.arrow button.white,a .wcd_hp-cta.arrow.white,a.wcd_hp-cta.arrow.white,button.wcd_hp-cta.arrow.white{background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--white.svg)}.wcd_hp-cta.caret-vcn a,.wcd_hp-cta.caret-vcn button,a .wcd_hp-cta.caret-vcn,a.wcd_hp-cta.caret-vcn,button.wcd_hp-cta.caret-vcn{background-color:transparent;background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret.svg);background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 13px;border-color:transparent;display:-ms-inline-flexbox;display:inline-flex;padding-left:0;padding-right:calc(.5em + 15px + 6px);text-align:left;transition:background-position .25s ease-out}.wcd_hp-cta.caret-vcn a:hover,.wcd_hp-cta.caret-vcn button:hover,a .wcd_hp-cta.caret-vcn:hover,a.wcd_hp-cta.caret-vcn:hover,button.wcd_hp-cta.caret-vcn:hover{background-color:transparent;border-color:transparent;color:#767676}.wcd_hp-cta.caret-vcn a.white,.wcd_hp-cta.caret-vcn button.white,a .wcd_hp-cta.caret-vcn.white,a.wcd_hp-cta.caret-vcn.white,button.wcd_hp-cta.caret-vcn.white{background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret_white.svg)}.wcd_hp-cta.caret a,.wcd_hp-cta.caret button,a .wcd_hp-cta.caret,a.wcd_hp-cta.caret,button.wcd_hp-cta.caret{background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret.svg);background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 13px;color:#767676;display:-ms-inline-flexbox;display:inline-flex;padding-left:0;padding-right:calc(1em + 15px + 6px);text-align:left;transition:background-position .25s ease-out;padding-left:10px;padding-right:24px;-ms-flex-pack:start;justify-content:flex-start}.wcd_hp-cta.caret a:hover,.wcd_hp-cta.caret button:hover,a .wcd_hp-cta.caret:hover,a.wcd_hp-cta.caret:hover,button.wcd_hp-cta.caret:hover{background-color:transparent;background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret_white.svg);border-color:#999;color:#767676}.wcd_hp-cta.caret a.white,.wcd_hp-cta.caret button.white,a .wcd_hp-cta.caret.white,a.wcd_hp-cta.caret.white,button.wcd_hp-cta.caret.white{background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--white.svg)}.wcd_hp-cta.details a,.wcd_hp-cta.details button,a .wcd_hp-cta.details,a.wcd_hp-cta.details,button.wcd_hp-cta.details{background-color:transparent;border-width:0;color:#fff;font-size:10px;min-height:16px;min-width:32px;padding:0;text-decoration:underline}.wcd_hp-cta.details a:hover,.wcd_hp-cta.details button:hover,a .wcd_hp-cta.details:hover,a.wcd_hp-cta.details:hover,button.wcd_hp-cta.details:hover{color:#fff}.wcd_hp-cta.details a.dark,.wcd_hp-cta.details button.dark,a .wcd_hp-cta.details.dark,a.wcd_hp-cta.details.dark,button.wcd_hp-cta.details.dark{color:#2b2b2b}.wcd_hp-cta.details a.dark:hover,.wcd_hp-cta.details button.dark:hover,a .wcd_hp-cta.details.dark:hover,a.wcd_hp-cta.details.dark:hover,button.wcd_hp-cta.details.dark:hover{color:#000}.wcd_hp-cta.outline a,.wcd_hp-cta.outline button,a .wcd_hp-cta.outline,a.wcd_hp-cta.outline,button.wcd_hp-cta.outline{background-color:#fff;border-color:#767676;border-width:1px}.wcd_hp-cta.outline a:active,.wcd_hp-cta.outline a:focus,.wcd_hp-cta.outline a:hover,.wcd_hp-cta.outline button:active,.wcd_hp-cta.outline button:focus,.wcd_hp-cta.outline button:hover,a .wcd_hp-cta.outline:active,a .wcd_hp-cta.outline:focus,a .wcd_hp-cta.outline:hover,a.wcd_hp-cta.outline:active,a.wcd_hp-cta.outline:focus,a.wcd_hp-cta.outline:hover,button.wcd_hp-cta.outline:active,button.wcd_hp-cta.outline:focus,button.wcd_hp-cta.outline:hover{background-color:#999;color:#fff;border-color:#fff}.wcd_hp-cta.full-width,.wcd_hp-cta.full-width a,.wcd_hp-cta.full-width a>div,.wcd_hp-cta.full-width button,.wcd_hp-cta.full-width button>div,.wcd_hp-cta.full-width-at-mob,.wcd_hp-cta.full-width-at-mob a,.wcd_hp-cta.full-width-at-mob a>div,.wcd_hp-cta.full-width-at-mob button,.wcd_hp-cta.full-width-at-mob button>div,.wcd_hp-cta.full-width-at-mob>div,.wcd_hp-cta.full-width>div,a .wcd_hp-cta.full-width,a .wcd_hp-cta.full-width-at-mob,a .wcd_hp-cta.full-width-at-mob>div,a .wcd_hp-cta.full-width>div{width:100%}.wcd_hp-visnav{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between;margin-left:auto;margin-right:auto;max-width:640px}.wcd_hp-visnav>div{position:relative;width:50%}.wcd_hp-visnav>div .wcd_hp-cta button,.wcd_hp-visnav>div button.wcd_hp-cta{border-color:transparent}.wcd_hp-visnav>div .wcd_hp-cta{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);background-color:#fff;color:#767676}.wcd_hp-visnav>div .wcd_hp-cta:hover{background-color:#999;color:#767676;border-color:#999}.wcd_hp-visnav>div:first-child .wcd_hp-cta{z-index:32}.wcd_hp-visnav>div:nth-child(2) .wcd_hp-cta{z-index:31}.wcd_hp-visnav>div:nth-child(3) .wcd_hp-cta{z-index:30}.wcd_hp-visnav>div:nth-child(4) .wcd_hp-cta{z-index:29}.wcd_hp-visnav>div:nth-child(5) .wcd_hp-cta{z-index:28}.wcd_hp-visnav>div:nth-child(6) .wcd_hp-cta{z-index:27}.wcd_hp-visnav>div:nth-child(7) .wcd_hp-cta{z-index:26}.wcd_hp-visnav>div:nth-child(8) .wcd_hp-cta{z-index:25}.wcd_hp-visnav>div:nth-child(9) .wcd_hp-cta{z-index:24}.wcd_hp-visnav>div:nth-child(10) .wcd_hp-cta{z-index:23}.wcd_hp-visnav>div:nth-child(11) .wcd_hp-cta{z-index:22}.wcd_hp-visnav>div:nth-child(12) .wcd_hp-cta{z-index:21}.wcd_hp-visnav>div:nth-child(13) .wcd_hp-cta{z-index:20}.wcd_hp-visnav>div:nth-child(14) .wcd_hp-cta{z-index:19}.wcd_hp-visnav>div:nth-child(15) .wcd_hp-cta{z-index:18}.wcd_hp-visnav>div:nth-child(16) .wcd_hp-cta{z-index:17}.wcd_hp-news{background-color:#fff;color:#2b2b2b;padding:16px 0 12px;width:100%}.wcd_hp-news>div{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;width:100%}.wcd_hp-news .wcd_hp-news_tile{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;margin:0 auto;max-width:672px;padding:10px 16px;width:100%}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_header{font-size:19px;margin-bottom:3px;text-transform:uppercase}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_copy{font-size:13px}@media only screen and (min-width:768px){.wcd_hp-cta a,.wcd_hp-cta button,a .wcd_hp-cta,a.wcd_hp-cta,button.wcd_hp-cta{font-size:calc(.625rem + (1vw - 7.68px) * .6944)}.wcd_hp-cta button,button.wcd_hp-cta{min-width:10.25em}.wcd_hp-cta.exposed-at-desk button{background-color:transparent;border-width:0;color:inherit;margin-bottom:.75em;padding:0;text-align:left}.wcd_hp-cta.exposed-at-desk button span{display:none}.wcd_hp-cta.exposed-at-desk ul{background-color:transparent;box-shadow:none;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:start;justify-content:flex-start;max-height:none;position:relative;visibility:visible;z-index:0}.wcd_hp-cta.exposed-at-desk ul li{width:auto}.wcd_hp-cta.exposed-at-desk ul li:not(:last-child){margin-bottom:8px;margin-right:8px}.wcd_hp-cta.exposed-at-desk ul li a{border-width:1px}.wcd_hp-cta.arrow a,.wcd_hp-cta.arrow button,a .wcd_hp-cta.arrow,a.wcd_hp-cta.arrow,button.wcd_hp-cta.arrow{background-size:auto .9em}.wcd_hp-cta.caret-vcn a,.wcd_hp-cta.caret-vcn button,a .wcd_hp-cta.caret-vcn,a.wcd_hp-cta.caret-vcn,button.wcd_hp-cta.caret-vcn{background-size:auto .9em}.wcd_hp-cta.caret a,.wcd_hp-cta.caret button,a .wcd_hp-cta.caret,a.wcd_hp-cta.caret,button.wcd_hp-cta.caret{background-size:auto .9em}.wcd_hp-cta.full-width-at-mob,.wcd_hp-cta.full-width-at-mob a,.wcd_hp-cta.full-width-at-mob a>div,.wcd_hp-cta.full-width-at-mob button,.wcd_hp-cta.full-width-at-mob button>div,.wcd_hp-cta.full-width-at-mob>div,a .wcd_hp-cta.full-width-at-mob,a .wcd_hp-cta.full-width-at-mob>div{width:auto}.wcd_hp-cta.full-width-at-desk,.wcd_hp-cta.full-width-at-desk a,.wcd_hp-cta.full-width-at-desk a>div,.wcd_hp-cta.full-width-at-desk button,.wcd_hp-cta.full-width-at-desk button>div,.wcd_hp-cta.full-width-at-desk>div,a .wcd_hp-cta.full-width-at-desk,a .wcd_hp-cta.full-width-at-desk>div{width:100%}.wcd_hp-visnav{max-width:1920px}.wcd_hp-visnav>div{background-color:#fff;width:25%}.wcd_hp-visnav>div:hover img{opacity:.85}.wcd_hp-news{padding:0}.wcd_hp-news>div{-ms-flex-direction:row;flex-direction:row;margin:0 auto;max-width:1920px}.wcd_hp-news .wcd_hp-news_tile{-ms-flex-align:center;align-items:center;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:justify;justify-content:space-between;max-width:none;padding:20px 22px 12px;position:relative}.wcd_hp-news .wcd_hp-news_tile:not(:hover) .wcd_hp-cta,.wcd_hp-news .wcd_hp-news_tile:not(:hover) .wcd_hp-news_copy{color:transparent}.wcd_hp-news .wcd_hp-news_tile:not(:hover) .wcd_hp-cta{background-image:none}.wcd_hp-news .wcd_hp-news_tile:hover{background-color:#efefed}.wcd_hp-news .wcd_hp-news_tile:hover .wcd_hp-news_header{background-image:none;color:transparent}.wcd_hp-news .wcd_hp-news_tile:hover .wcd_hp-news_header img{display:none!important}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_header{-ms-flex-align:center;align-items:center;background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--black.svg);background-position:100% 50%;background-repeat:no-repeat;background-size:auto 1em;display:-ms-flexbox;display:flex;font-size:13px;left:50%;padding-right:24px;position:absolute;top:50%;transform:translate(-50%,-50%);white-space:nowrap}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_copy{font-size:10px}}@media only screen and (min-width:1024px){.wcd_hp-cta button,button.wcd_hp-cta{min-width:9.75em}.wcd_hp-news .wcd_hp-news_tile{padding-left:3vw;padding-right:3vw}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_header{font-size:Min(Max(17px, calc(1.0625rem + ((1vw - 10.24px) * 1.6741))), 32px);padding-right:1.5em}.wcd_hp-news .wcd_hp-news_tile:nth-child(2) .wcd_hp-news_header{min-width:17.5em}.wcd_hp-news .wcd_hp-news_tile .wcd_hp-news_copy{font-size:Min(Max(12px, calc(.75rem + ((1vw - 10.24px) * 1.3393))), 24px)}}@media only screen and (min-width:1280px){.wcd_hp-cta button,button.wcd_hp-cta{min-width:9.375em}}@media only screen and (min-width:1440px){.wcd_hp-cta button,button.wcd_hp-cta{min-width:8.875em}}@media only screen and (max-width:767px){.wcd_hp-cta.two-column-at-mob a,.wcd_hp-cta.two-column-at-mob>div{margin-bottom:8px;width:calc(50% - 4px)}.wcd_hp-cta.two-column-at-mob li a{margin-bottom:0;width:100%}.wcd_hp-cta.two-column-at-mob>div button{width:100%}.wcd_hp-cta.two-column-at-mob.odd-number a:first-child,.wcd_hp-cta.two-column-at-mob.odd-number>div:first-child{width:100%}}@media only screen and (min-width:768px){#main-content > div:nth-child(4) > div > div > div > div > div.sitewide-x6zmea > div > div > div:nth-child(1) > button{border-right:0}}@media only screen and (max-width:767px){#main-content > div:nth-child(4) > div > div > div > div > div.sitewide-j4a4fw > div > div > div:nth-child(1) > button{border-bottom:0}.wcd_hp-cta.two-column-at-mob.caret a{background-image:url(/Asset_Archive/GFWeb/content/0030/014/454/assets/MOBCTA_Caret_2224db.svg?v=2);color:#de0032;border-color:#de0032}#main-content > div.sitewide-6gpkta > div > div > div > div > div:nth-child(2) > div > div > div.sitewide-1avyp1d > div > a:nth-child(1){display:none}}</style>"}}, {"instanceName": "dpg-banner1", "instanceDesc": "DPG Placeholder1", "name": "OptimizelyPlaceholder", "type": "sitewide", "experimentRunning": true, "data": {"defaultHeight": {"small": "0", "large": "0"}}}, {"instanceName": "HP_PromoTB1", "instanceDesc": "HP_PromoTB1", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "100%", "large": "71px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1038092,30014454,HP_PromoTB1"}, "image": {"alt": "Great Gap Sale Up to 75% + Extra 50% Off", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_PromoTB1/070623_GreatGapSaleUpto75_SiteHPPromoTB1_1_7226_MOB.svg", "style": {"display": "block"}}}}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0", "@media only screen and (min-width:768px)": {"alignItems": "end", "flexDirection": "row", "justifyContent": "flex-end", "padding": "0", "width": "75%"}, "@media only screen and (min-width:1280px)": {"paddingTop": "0"}}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"fontSize": "16px", "fontWeight": "400", "padding": "1rem 1rem 0", "whiteSpace": "pre", "@media only screen and (min-width: 768px)": {"display": "flex", "fontSize": "min(max(12px, calc(0.75rem + ((1vw - 7.68px) * 1.0417))), 24px)", "marginRight": "8px", "padding": "0"}}, "components": [{"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta two-column-at-mob caret outline", "style": {"&.wcd_hp-cta.caret a": {"&:first-child": {"width": "100%"}}}, "ctas": [{"linkData": {"to": ""}, "composableButtonData": {"children": "None"}}, {"linkData": {"to": "/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1038092,30014454,HP_PromoTB1"}, "composableButtonData": {"children": "Women"}}, {"linkData": {"to": "/browse/category.do?cid=1092773#pageId=0&department=75&mlink=1038092,30014454,HP_PromoTB1"}, "composableButtonData": {"children": "Men "}}, {"linkData": {"to": "/browse/category.do?cid=1092850#pageId=0&department=48&mlink=1038092,30014454,HP_PromoTB1"}, "composableButtonData": {"children": "Girls"}}, {"linkData": {"to": "/browse/category.do?cid=1092793#pageId=0&department=16&mlink=1038092,30014454,HP_PromoTB1"}, "composableButtonData": {"children": "Boys"}}, {"linkData": {"to": "/browse/category.do?cid=1092869#pageId=0&department=165&mlink=1038092,30014454,HP_PromoTB1"}, "composableButtonData": {"children": "<PERSON>ler Girl"}}, {"linkData": {"to": "/browse/category.do?cid=1092872#pageId=0&department=166&mlink=1038092,30014454,HP_PromoTB1"}, "composableButtonData": {"children": "<PERSON><PERSON>"}}, {"linkData": {"to": "/browse/category.do?cid=1092873#pageId=0&department=165&mlink=1038092,30014454,HP_PromoTB1"}, "composableButtonData": {"children": "Baby Girl"}}, {"linkData": {"to": "/browse/category.do?cid=1092875#pageId=0&department=166&mlink=1038092,30014454,HP_PromoTB1"}, "composableButtonData": {"children": "Baby Boy"}}]}}}]}}]}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"mobile": {"textAlign": "center", "whiteSpace": "nowrap", "height": "10px", "width": "100%"}}, "data": {"ctaList": {"mobilePositionAboveContent": false, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=975177,975137", "height": "500px"}}, "composableButtonData": {"children": ["Extra 50% off clearance discount applied at checkout. Exclusions apply. "], "style": {"position": "relative", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#9a9a9a", "zIndex": "1!important", "marginTop": "0"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=975177,975137", "height": "500px"}}, "composableButtonData": {"children": ["DETAILS"], "style": {"position": "relative", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "padding": "0", "outline": "none", "color": "#9a9a9a", "zIndex": "1!important", "marginTop": "0"}}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 5rem", "position": "relative", "maxWidth": "1920px"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1038092,30014454,HP_PromoTB1"}, "image": {"alt": "Great Gap Sale Up to 75% + Extra 50% Off", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_PromoTB1/070623_GreatGapSaleUpto75_SiteHPPromoTB1_1_7226_DESK.svg", "style": {"display": "block"}}}}}, {"instanceDesc": "CTA Lockup", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"mobilePositionAboveContent": false, "style": {"padding": "0", "position": "absolute", "top": "100%", "right": "0", "transform": "translate(0, 50%)", "width": "100%", "button, ul li a": {"color": "#de0032", "backgroundColor": "transparent", "borderColor": "#de0032", "fontWeight": "400", "letterSpacing": "0", "whiteSpace": "nowrap", "marginTop": "0rem"}, "button": {"display": "none", "borderWidth": "1px 0", "fontSize": "24px", "&:focus": {"borderColor": "#de0032 !important", "backgroundColor": "#de0032", "color": "#FFF", "outline": "0"}, "span": {"display": "none"}}, "ul": {"backgroundColor": "transparent", "borderStyle": "solid", "borderWidth": "0", "boxShadow": "none", "display": "flex", "flexDirection": "row", "flexWrap": "wrap", "maxHeight": "none", "padding": "0", "visibility": "visible", "zIndex": "397", "li": {"backgroundColor": "#FFF", "borderColor": "#de0032", "borderStyle": "solid", "borderWidth": "0 0 1px", "boxSizing": "border-box", "padding": "0", "width": "50%", "&:last-child": {"borderBottom": "1px solid #de0032", "borderLeft": "1px solid #de0032"}, "&:nth-of-type(even)": {"borderLeftWidth": "1px"}, "&:nth-of-type(odd):last-of-type": {"width": "100%"}, "a": {"backgroundColor": "transparent", "borderWidth": "0", "fontSize": "15px", "height": "32px", "line-height": "32px", "transitionDuration": "0.2s", "&:hover": {"borderColor": "#de0032", "backgroundColor": "#de0032", "color": "#FFF"}}}}}, "desktopStyle": {"padding": "0", "div[data-testid='button-dropdown-container']": {"borderWidth": "0", "display": "flex", "flexDirection": "row wrap", "flexWrap": "wrap", "justifyContent": "center"}, "button, ul li a": {"fontSize": "15px", "minHeight": "0vw", "padding": "0 15px", "height": "32px", "lineHeight": "32px"}, "button": {"backgroundColor": "#FFF", "borderColor": "#ff7a7a", "borderWidth": "0", "cursor": "default", "letterSpacing": "0", "marginBottom": "0px", "pointerEvents": "none", "width": "100%", "span": {"display": "none"}}, "ul": {"borderWidth": "0", "display": "flex", "flexWrap": "wrap", "justifyContent": "center", "marginLeft": "auto", "marginRight": "auto", "maxHeight": "none", "minWidth": "0", "visibility": "visible", "&:nth-of-type(odd):last-of-type": {"width": "auto"}, "li": {"borderWidth": "0", "marginRight": "8px", "marginTop": "0px", "width": "auto", "&:nth-of-type(even)": {"borderLeftWidth": "0"}, "&:last-child": {"borderBottom": "0", "borderLeft": "0", "marginRight": "0"}, "&:nth-of-type(odd):last-of-type": {"width": "auto"}, "a": {"borderWidth": "1px", "borderStyle": "solid"}}}, "@media only screen and (min-width: 768px)": {"button": {"width": "auto"}, "button, ul li a": {"fontSize": "7px"}, "ul": {"marginLeft": "1rem", "marginRight": "1rem"}}, "@media only screen and (min-width: 949px)": {"button": {"marginBottom": "0", "width": "auto"}, "button, ul li a": {"fontSize": "11px"}, "ul": {"marginLeft": "0", "marginRight": "0"}}, "@media only screen and (min-width: 1200px)": {"button, ul li a": {"fontSize": "14px"}}}, "ctas": [{"buttonDropdownData": {"submenu": [{"text": "Women", "href": "/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1038092,30014454,HP_PromoTB1"}, {"text": "Men", "href": "/browse/category.do?cid=1092773#pageId=0&department=75&mlink=1038092,30014454,HP_PromoTB1"}, {"text": "Girls", "href": "/browse/category.do?cid=1092850#pageId=0&department=48&mlink=1038092,30014454,HP_PromoTB1"}, {"text": "Boys", "href": "/browse/category.do?cid=1092793#pageId=0&department=16&mlink=1038092,30014454,HP_PromoTB1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1092869#pageId=0&department=165&mlink=1038092,30014454,HP_PromoTB1"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1092872#pageId=0&department=166&mlink=1038092,30014454,HP_PromoTB1"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1092873#pageId=0&department=165&mlink=1038092,30014454,HP_PromoTB1"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1092875#pageId=0&department=166&mlink=1038092,30014454,HP_PromoTB1"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"mobile": {"textAlign": "center", "whiteSpace": "nowrap", "height": "10px", "width": "100%"}, "desktop": {"whiteSpace": "nowrap", "position": "absolute", "right": "1%", "bottom": "12%", "zIndex": "1", "height": "10px", "textAlign": "center", "transform": "translate(0, 0)"}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {}, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=975177,975137", "height": "500px"}}, "composableButtonData": {"children": ["Extra 50% off clearance discount applied at checkout. Exclusions apply. "], "style": {"fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#FFF"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=975177,975137", "height": "500px"}}, "composableButtonData": {"children": ["DETAILS"], "style": {"fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "padding": "0", "outline": "none", "color": "#FFF"}}}]}}}]}}}}, {"instanceName": "HP_PromoTB2", "instanceDesc": "HP_PromoTB2", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "134px", "large": "71px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 7rem", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "Carousel", "type": "sitewide", "instanceName": "carousel-arrows", "data": {"modalCloseButtonAriaLabel": "Close", "carouselOptions": {"autoplay": true, "autoplaySpeed": 1000, "fade": true, "slidesToShow": 1, "speed": 300, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause slideshow", "playAltText": "Play slideshow", "buttonStyle": {"left": "2%", "position": "absolute", "bottom": "12%", "transform": "translate(0,0)", "height": "1.5em", "width": "1.5em", "zIndex": "14"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GFWeb/content/0030/014/454/assets/Icon_Play_767676.svg", "pauseBtnSrc": "/Asset_Archive/GFWeb/content/0030/014/454/assets/Icon_Pause_767676.svg"}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_PromoTB2/070623_PromoMerchIntXXoff_SiteHPPromoBanner_V1_7004_SA1381_MOB.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1041618#pageId=0&department=136&mlink=1038092,30014454,HP_PromoTB2"}, "image": {"alt": "50% Off Dresses & Shorts", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_PromoTB2/070623_PromoMerchIntXXoff_SiteHPPromoBanner_V1_7004_SA1381_ANIM_1_MOB.jpg", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_PromoTB2/070623_PromoMerchIntXXoff_SiteHPPromoBanner_V1_7004_SA1381_MOB.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1192445#pageId=0&department=166&mlink=1038092,30014454,HP_PromoTB2"}, "image": {"alt": "50% Off Dresses & Shorts", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_PromoTB2/070623_PromoMerchIntXXoff_SiteHPPromoBanner_V1_7004_SA1381_ANIM_2_MOB.jpg", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_PromoTB2/070623_PromoMerchIntXXoff_SiteHPPromoBanner_V1_7004_SA1381_MOB.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1077052#pageId=0&department=75&mlink=1038092,30014454,HP_PromoTB2"}, "image": {"alt": "50% Off Dresses & Shorts", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_PromoTB2/070623_PromoMerchIntXXoff_SiteHPPromoBanner_V1_7004_SA1381_ANIM_2_MOB.jpg", "style": {"display": "block"}}}}}]}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"height": "0"}}, "data": {"container": {"desktopStyle": {"height": "100%", "position": "relative", "width": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "className": "wcd_hp-cta full-width", "style": {"width": "100%", "zIndex": "31", "padding": "0 1rem", "button": {"color": "#767676!important", "borderColor": "#767676!important", "&:hover": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#767676!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop dresses"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1041618#pageId=0&department=136&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Girls", "href": "/browse/category.do?cid=1041723#pageId=0&department=48&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041922#pageId=0&department=165&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064646#pageId=0&department=165&mlink=1038092,30014454,HP_PromoTB2"}]}}, {"buttonDropdownData": {"heading": {"text": "shop shorts"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1079858#pageId=0&department=136&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Men", "href": "/browse/category.do?cid=1077052#pageId=0&department=75&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Girls", "href": "/browse/category.do?cid=1108148#pageId=0&department=48&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Boys", "href": "/browse/category.do?cid=1041823#pageId=0&department=16&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1192444#pageId=0&department=165&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1192445#pageId=0&department=166&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=3016490#pageId=0&department=165&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=3016495#pageId=0&department=166&mlink=1038092,30014454,HP_PromoTB2"}]}}]}}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"mobile": {"textAlign": "center", "whiteSpace": "nowrap", "height": "10px", "width": "100%"}}, "data": {"ctaList": {"mobilePositionAboveContent": false, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=975157", "height": "500px"}}, "composableButtonData": {"children": ["Exclusions apply. "], "style": {"position": "relative", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#9a9a9a", "zIndex": "1!important", "marginTop": "18%"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=975157", "height": "500px"}}, "composableButtonData": {"children": ["DETAILS"], "style": {"position": "relative", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "padding": "0", "outline": "none", "color": "#9a9a9a", "zIndex": "1!important", "marginTop": "18%"}}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "position": "relative", "maxWidth": "1920px"}, "components": [{"instanceDesc": "3column_30", "name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "margin": "0 0 0", "maxWidth": "1920px"}, "components": [{"instanceDesc": "sub1", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"boxSizing": "border-box", "maxWidth": "none", "width": "33.33%", "padding": "0"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_PromoTB2/070623_PromoMerchIntXXoff_SiteHPPromoBanner_V1_7004_SA1381_DESK.svg", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1041618#pageId=0&department=136&mlink=1038092,30014454,HP_PromoTB2"}, "image": {"alt": "50% Off Dresses & Shorts", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_PromoTB2/070623_PromoMerchIntXXoff_SiteHPPromoBanner_V1_7004_SA1381_A_DESK.jpg", "style": {"display": "block"}}}}}]}}}}, {"instanceDesc": "sub2", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"boxSizing": "border-box", "maxWidth": "none", "width": "33.33%", "padding": "0"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1192445#pageId=0&department=166&mlink=1038092,30014454,HP_PromoTB2"}, "image": {"alt": "50% Off Dresses & Shorts", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_PromoTB2/070623_PromoMerchIntXXoff_SiteHPPromoBanner_V1_7004_SA1381_B_DESK.jpg", "style": {"display": "block"}}}}}]}}}}, {"instanceDesc": "sub3", "name": "LayoutComponent", "type": "sitewide", "tileStyle": {"desktop": {"boxSizing": "border-box", "maxWidth": "none", "width": "33.33%", "padding": "0"}}, "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"linkData": {"to": "/browse/category.do?cid=1077052#pageId=0&department=75&mlink=1038092,30014454,HP_PromoTB2"}, "image": {"alt": "50% Off Dresses & Shorts", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_PromoTB2/070623_PromoMerchIntXXoff_SiteHPPromoBanner_V1_7004_SA1381_C_DESK.jpg", "style": {"display": "block"}}}}}]}}}}]}}}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "80%", "left": "3.5%", "transform": "translate(-10%,0)", "zIndex": "2"}}, "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"button": {"backgroundColor": "transparent!important", "borderColor": "#767676!important", "color": "#767676!important", "&:hover": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#767676!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop dresses"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1041618#pageId=0&department=136&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Girls", "href": "/browse/category.do?cid=1041723#pageId=0&department=48&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041922#pageId=0&department=165&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064646#pageId=0&department=165&mlink=1038092,30014454,HP_PromoTB2"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}, {"buttonDropdownData": {"heading": {"text": "shop shorts"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1079858#pageId=0&department=136&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Men", "href": "/browse/category.do?cid=1077052#pageId=0&department=75&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Girls", "href": "/browse/category.do?cid=1108148#pageId=0&department=48&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Boys", "href": "/browse/category.do?cid=1041823#pageId=0&department=16&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1192444#pageId=0&department=165&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1192445#pageId=0&department=166&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=3016490#pageId=0&department=165&mlink=1038092,30014454,HP_PromoTB2"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=3016495#pageId=0&department=166&mlink=1038092,30014454,HP_PromoTB2"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"mobile": {"textAlign": "center", "whiteSpace": "nowrap", "height": "10px", "width": "100%"}, "desktop": {"whiteSpace": "nowrap", "position": "absolute", "right": "1%", "bottom": "2%", "zIndex": "1", "height": "10px", "textAlign": "center", "transform": "translate(0, 0)"}}, "data": {"container": {"className": "", "style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {}, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=975157", "height": "500px"}}, "composableButtonData": {"children": ["Exclusions apply. "], "style": {"fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#9a9a9a"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=975157", "height": "500px"}}, "composableButtonData": {"children": ["DETAILS"], "style": {"fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "padding": "0", "outline": "none", "color": "#9a9a9a"}}}]}}}]}}}}, {"instanceName": "HP_A", "instanceDesc": "HP_A", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "134px", "large": "71px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 5rem", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "Carousel", "type": "sitewide", "instanceName": "carousel-arrows", "data": {"modalCloseButtonAriaLabel": "Close", "carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": true, "slidesToShow": 1, "speed": 300, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause slideshow", "playAltText": "Play slideshow", "buttonStyle": {"left": "2%", "position": "absolute", "bottom": "24%", "transform": "translate(0,0)", "height": "1.5em", "width": "1.5em", "zIndex": "14"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GFWeb/content/0030/014/454/assets/Icon_Play_767676.svg", "pauseBtnSrc": "/Asset_Archive/GFWeb/content/0030/014/454/assets/Icon_Pause_767676.svg"}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_A/062923_NewArrivalsFashionWov_SiteHPA_V1_7018_SA1471_MOB.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1042791#pageId=0&department=136&mlink=1038092,30014134,HP_A"}, "image": {"alt": "New Arrivals: Fashion Woven Outfitting", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_A/062923_NewArrivalsFashionWov_SiteHPA_V1_7018_SA1471_ANIM_1_MOB.jpg", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_A/062923_NewArrivalsFashionWov_SiteHPA_V1_7018_SA1471_MOB.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1052118#pageId=0&department=75&mlink=1038092,30014134,HP_A"}, "image": {"alt": "New Arrivals: Fashion Woven Outfitting", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_A/062923_NewArrivalsFashionWov_SiteHPA_V1_7018_SA1471_ANIM_2_MOB.jpg", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_A/062923_NewArrivalsFashionWov_SiteHPA_V1_7018_SA1471_MOB.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1064872#pageId=0&department=48&mlink=1038092,30014134,HP_A"}, "image": {"alt": "New Arrivals: Fashion Woven Outfitting", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_A/062923_NewArrivalsFashionWov_SiteHPA_V1_7018_SA1471_ANIM_3_MOB.jpg", "style": {"display": "block"}}}}}]}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"height": "0"}}, "data": {"container": {"desktopStyle": {"height": "100%", "position": "relative", "width": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "className": "wcd_hp-cta full-width", "style": {"width": "100%", "zIndex": "31", "padding": "0 1rem", "button": {"color": "#767676!important", "borderColor": "#767676!important", "&:hover": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#767676!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop now"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1042791#pageId=0&department=136&mlink=1038092,30013505,HP_A"}, {"text": "Men", "href": "/browse/category.do?cid=1052118#pageId=0&department=75&mlink=1038092,30013505,HP_A"}, {"text": "Girls", "href": "/browse/category.do?cid=1064872#pageId=0&department=48&mlink=1038092,30013505,HP_A"}, {"text": "Boys", "href": "/browse/category.do?cid=1064875#pageId=0&department=16&mlink=1038092,30013505,HP_A"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1064881#pageId=0&department=165&mlink=1038092,30013505,HP_A"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1064880#pageId=0&department=166&mlink=1038092,30013505,HP_A"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1063532#pageId=0&department=165&mlink=1038092,30013505,HP_A"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1063552#pageId=0&department=166&mlink=1038092,30013505,HP_A"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "position": "relative", "maxWidth": "1920px"}, "components": [{"name": "Carousel", "type": "sitewide", "instanceName": "carousel-arrows", "data": {"modalCloseButtonAriaLabel": "Close", "carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": true, "slidesToShow": 1, "speed": 300, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause slideshow", "playAltText": "Play slideshow", "buttonStyle": {"position": "absolute", "left": "10px", "bottom": "2%", "transform": "translate(0,0)", "height": "1.5em", "width": "1.5em", "zIndex": "14"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GFWeb/content/0030/014/454/assets/Icon_Play_767676.svg", "pauseBtnSrc": "/Asset_Archive/GFWeb/content/0030/014/454/assets/Icon_Pause_767676.svg"}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_A/062923_NewArrivalsFashionWov_SiteHPA_V1_7018_SA1471_DESK.svg", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1042791#pageId=0&department=136&mlink=1038092,30014134,HP_A"}, "image": {"alt": "New Arrivals: Fashion Woven Outfitting", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_A/062923_NewArrivalsFashionWov_SiteHPA_V1_7018_SA1471_ANIM_1_DESK.jpg", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_A/062923_NewArrivalsFashionWov_SiteHPA_V1_7018_SA1471_DESK.svg", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1052118#pageId=0&department=75&mlink=1038092,30014134,HP_A"}, "image": {"alt": "New Arrivals: Fashion Woven Outfitting", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_A/062923_NewArrivalsFashionWov_SiteHPA_V1_7018_SA1471_ANIM_2_DESK.jpg", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_A/062923_NewArrivalsFashionWov_SiteHPA_V1_7018_SA1471_DESK.svg", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1064872#pageId=0&department=48&mlink=1038092,30014134,HP_A"}, "image": {"alt": "New Arrivals: Fashion Woven Outfitting", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_A/062923_NewArrivalsFashionWov_SiteHPA_V1_7018_SA1471_ANIM_3_DESK.jpg", "style": {"display": "block"}}}}}]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "59%", "left": "52%", "transform": "translate(-10%,0)", "zIndex": "2"}}, "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"button": {"backgroundColor": "transparent!important", "borderColor": "#767676!important", "color": "#767676!important", "&:hover": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#767676!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "shop now"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1042791#pageId=0&department=136&mlink=1038092,30014134,HP_A"}, {"text": "Men", "href": "/browse/category.do?cid=1052118#pageId=0&department=75&mlink=1038092,30014134,HP_A"}, {"text": "Girls", "href": "/browse/category.do?cid=1064872#pageId=0&department=48&mlink=1038092,30014134,HP_A"}, {"text": "Boys", "href": "/browse/category.do?cid=1064875#pageId=0&department=16&mlink=1038092,30014134,HP_A"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1064881#pageId=0&department=165&mlink=1038092,30014134,HP_A"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1064880#pageId=0&department=166&mlink=1038092,30014134,HP_A"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1063532#pageId=0&department=165&mlink=1038092,30014134,HP_A"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1063552#pageId=0&department=166&mlink=1038092,30014134,HP_A"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}}}, {"instanceName": "013123_Certona_1", "instanceDesc": "013123_Certona_1", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "2rem", "large": "3rem"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"margin": "0 auto", "flexDirection": "column", "padding": "0", "maxWidth": "1920px"}, "components": [{"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"lazy": true, "style": {"width": "100%", "@media only screen and (max-width:768px)": {"padding": "0px 0 36px", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}, "@media only screen and (min-width:769px)": {"padding": "0px 0 36px", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}}, "props": {"style": {"width": "100%"}, "className": "fullBleedCertona"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome2_rr", "displayTitle": true, "fullWidth": true, "certonaTitle": {"title": "Shop All New Everything", "style": {"mobile": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#707070", "lineHeight": "1.6rem", "fontSize": "4.1vw", "textAlign": "left", "padding": "0px 0 0 13px", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}, "desktop": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#707070", "lineHeight": "1.6rem", "fontSize": "1.1vw", "textAlign": "left", "padding": "0px 0 0 1.5%", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}}}, "layout": "carousel", "centerMode": false, "useMobileConfig": true, "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "displayPlayPauseButton": false, "responsive": [{"breakpoint": 1350, "settings": {"slidesToShow": 5, "slidesToScroll": 5}}, {"breakpoint": 768, "settings": {"slidesToShow": 1.3, "slidesToScroll": 2}}], "arrows": true, "autoplay": false, "pauseOnHover": true, "infinite": false, "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "priceOffText": "off", "showMarketingFlag": false, "showPercentage": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0029/669/822/assets/CERTONA/CertonaCarat_Left.svg", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0029/669/822/assets/CERTONA/CertonaCarat_Left.svg", "nextArrowAlt": "Next", "arrowMaxWidth": "40px", "arrowPosition": "-8px", "productTextStyles": {"productTitle": {"style": {"color": "#767676", "textAlign": "left", "fontSize": "0.75rem", "margin": "10px 13px"}}, "productPrice": {"style": {"color": "#2b2b2b", "fontSize": "0.75rem", "float": "left", "display": "block", "marginLeft": "13px", "marginTop": "-2px"}}, "productPercentage": {"style": {"color": "#CD2026", "fontSize": "0.75rem", "float": "left", "marginTop": "-2px", "display": "block", "fontWeight": "600"}}, "productSalePrice": {"style": {"color": "#CD2026", "fontSize": "0.75rem", "display": "block", "marginLeft": "13px", "marginTop": "1rem", "fontWeight": "600", "width": "fit-content"}}}, "size": {"width": "100%", "height": "150px"}, "productMarketingFlag": {"style": {"fontWeight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "maxWidth": "unset", "padding": "0px", "width": "auto"}}, "productCardImageStyles": {"width": "19vw", "margin": "0", "maxWidth": "unset", "padding": "0px"}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}}}, {"instanceName": "HP_B1", "instanceDesc": "HP_B1", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "134px", "large": "71px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 7rem", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_B1/062923_BacktoSchoolShopIsOp_SiteHPB1_V1_7024_SA1387_MOB.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1160139#pageId=0&department=48&mlink=1038092,30014134,HP_B1"}, "image": {"alt": "Back To School Shop - Now Open!", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_B1/062923_BacktoSchoolShopIsOp_SiteHPB1_V1_7024_SA1387_MOB.jpg", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"height": "0"}}, "data": {"container": {"style": {"zIndex": "99"}, "desktopStyle": {"height": "100%", "position": "relative", "width": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "className": "wcd_hp-cta full-width", "style": {"width": "100%", "zIndex": "99", "padding": "0 1rem", "button": {"color": "#767676!important", "borderColor": "#767676!important", "&:hover": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#767676!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "check off your list"}, "submenu": [{"text": "Girls", "href": "/browse/category.do?cid=1160139#pageId=0&department=48&mlink=1038092,30014134,HP_B1"}, {"text": "Boys", "href": "/browse/category.do?cid=1160140#pageId=0&department=16&mlink=1038092,30014134,HP_B1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1064881#pageId=0&department=165&mlink=1038092,30014134,HP_B1"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1064880#pageId=0&department=166&mlink=1038092,30014134,HP_B1"}, {"text": "Women", "href": "/browse/category.do?cid=1160129#pageId=0&department=136&mlink=1038092,30014134,HP_B1"}, {"text": "Men", "href": "/browse/category.do?cid=1160136#pageId=0&department=75&mlink=1038092,30014134,HP_B1"}]}}, {"buttonDropdownData": {"heading": {"text": "shop school uniforms"}, "submenu": [{"text": "Girls", "href": "/browse/category.do?cid=1120881#pageId=0&department=48&mlink=1038092,30014134,HP_B1"}, {"text": "Boys", "href": "/browse/category.do?cid=1120882#pageId=0&department=16&mlink=1038092,30014134,HP_B1"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "position": "relative", "maxWidth": "1920px"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_B1/062923_BacktoSchoolShopIsOp_SiteHPB1_V1_7024_SA1387_DESK.svg", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1160139#pageId=0&department=48&mlink=1038092,30014134,HP_B1"}, "image": {"alt": "Back To School Shop - Now Open!", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_B1/062923_BacktoSchoolShopIsOp_SiteHPB1_V1_7024_SA1387_DESK.jpg", "style": {"display": "block"}}}}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "59%", "left": "10.75%", "transform": "translate(-10%,0)", "zIndex": "2"}}, "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"button": {"backgroundColor": "transparent!important", "borderColor": "#767676!important", "color": "#767676!important", "&:hover": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#767676!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "check off your list"}, "submenu": [{"text": "Girls", "href": "/browse/category.do?cid=1160139#pageId=0&department=48&mlink=1038092,30014134,HP_B1"}, {"text": "Boys", "href": "/browse/category.do?cid=1160140#pageId=0&department=16&mlink=1038092,30014134,HP_B1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1064881#pageId=0&department=165&mlink=1038092,30014134,HP_B1"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1064880#pageId=0&department=166&mlink=1038092,30014134,HP_B1"}, {"text": "Women", "href": "/browse/category.do?cid=1160129#pageId=0&department=136&mlink=1038092,30014134,HP_B1"}, {"text": "Men", "href": "/browse/category.do?cid=1160136#pageId=0&department=75&mlink=1038092,30014134,HP_B1"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}, {"buttonDropdownData": {"heading": {"text": "shop school uniforms"}, "submenu": [{"text": "Girls", "href": "/browse/category.do?cid=1120881#pageId=0&department=48&mlink=1038092,30014134,HP_B1"}, {"text": "Boys", "href": "/browse/category.do?cid=1120882#pageId=0&department=16&mlink=1038092,30014134,HP_B1"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}}}, {"instanceName": "HP_B2", "instanceDesc": "HP_B2", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "134px", "large": "71px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 5rem", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_B2/062923_VacationShopCollection_SiteHPB2_V1_6988_SA1385_MOB.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1175088#pageId=0&department=136&mlink=1038092,30014134,HP_B2"}, "image": {"alt": "Vacation Shop", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_B2/062923_VacationShopCollection_SiteHPB2_V1_6988_SA1385_MOB.jpg", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"height": "0"}}, "data": {"container": {"desktopStyle": {"height": "100%", "position": "relative", "width": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "className": "wcd_hp-cta full-width", "style": {"width": "100%", "zIndex": "31", "padding": "0 1rem", "button": {"color": "#767676!important", "borderColor": "#767676!important", "&:hover": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#767676!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "pack your bags"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1175088#pageId=0&department=136&mlink=1038092,30014134,HP_B2"}, {"text": "Men", "href": "/browse/category.do?cid=1175078#pageId=0&department=75&mlink=1038092,30014134,HP_B2"}, {"text": "Girls", "href": "/browse/category.do?cid=1175077#pageId=0&department=48&mlink=1038092,30014134,HP_B2"}, {"text": "Boys", "href": "/browse/category.do?cid=1175079#pageId=0&department=16&mlink=1038092,30014134,HP_B2"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1175098#department=165&style=1175099&mlink=1038092,30014134,HP_B2"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1175098#department=166&style=1175100&mlink=1038092,30014134,HP_B2"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1175101#department=165&style=1175102&mlink=1038092,30014134,HP_B2"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1175101#department=166&style=1175103&mlink=1038092,30014134,HP_B2"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "position": "relative", "maxWidth": "1920px"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_B2/062923_VacationShopCollection_SiteHPB2_V1_6988_SA1385_DESK.svg", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1175088#pageId=0&department=136&mlink=1038092,30014134,HP_B2"}, "image": {"alt": "Vacation Shop", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_B2/062923_VacationShopCollection_SiteHPB2_V1_6988_SA1385_DESK.jpg", "style": {"display": "block"}}}}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "62%", "left": "9%", "transform": "translate(-10%,0)", "zIndex": "2"}}, "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"button": {"backgroundColor": "transparent!important", "borderColor": "#767676!important", "color": "#767676!important", "&:hover": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#767676!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "pack your bags"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1175088#pageId=0&department=136&mlink=1038092,30014134,HP_B2"}, {"text": "Men", "href": "/browse/category.do?cid=1175078#pageId=0&department=75&mlink=1038092,30014134,HP_B2"}, {"text": "Girls", "href": "/browse/category.do?cid=1175077#pageId=0&department=48&mlink=1038092,30014134,HP_B2"}, {"text": "Boys", "href": "/browse/category.do?cid=1175079#pageId=0&department=16&mlink=1038092,30014134,HP_B2"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1175098#department=165&style=1175099&mlink=1038092,30014134,HP_B2"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1175098#department=166&style=1175100&mlink=1038092,30014134,HP_B2"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1175101#department=165&style=1175102&mlink=1038092,30014134,HP_B2"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1175101#department=166&style=1175103&mlink=1038092,30014134,HP_B2"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}}}, {"instanceName": "HP_C1", "instanceDesc": "HP_C1", "experimentRunning": true, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "134px", "large": "71px"}, "placeholderSettings": {"useGreyLoadingEffect": true, "mobile": {"backgroundColor": "#ccc", "height": 370, "margin": "0 auto 0rem", "maxWidth": "100%", "width": 640}, "desktop": {"backgroundColor": "#ccc", "height": 450, "margin": "0 auto 0", "maxWidth": "1920px", "width": "100%"}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 5rem", "maxWidth": "640px", "position": "relative"}, "components": [{"name": "Carousel", "type": "sitewide", "instanceName": "carousel-arrows", "data": {"modalCloseButtonAriaLabel": "Close", "carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": true, "slidesToShow": 1, "speed": 300, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause slideshow", "playAltText": "Play slideshow", "buttonStyle": {"left": "2%", "position": "absolute", "bottom": "23%", "transform": "translate(0,0)", "height": "1.5em", "width": "1.5em", "zIndex": "14"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GFWeb/content/0030/014/454/assets/Icon_Play_767676.svg", "pauseBtnSrc": "/Asset_Archive/GFWeb/content/0030/014/454/assets/Icon_Pause_767676.svg"}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_C1/062923_NEWFashionWovenDresses_SiteHPC1_V1_7014_SA1383_MOB.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1041618#pageId=0&department=136&mlink=1038092,30014134,HP_C1"}, "image": {"alt": "NEW Fashion Woven Dresses & Rompers", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_C1/062923_NEWFashionWovenDresses_SiteHPC1_V1_7014_SA1383_ANIM_1_MOB.jpg", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_C1/062923_NEWFashionWovenDresses_SiteHPC1_V1_7014_SA1383_MOB.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1041618#pageId=0&department=136&mlink=1038092,30014134,HP_C1"}, "image": {"alt": "NEW Fashion Woven Dresses & Rompers", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_C1/062923_NEWFashionWovenDresses_SiteHPC1_V1_7014_SA1383_ANIM_2_MOB.jpg", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_C1/062923_NEWFashionWovenDresses_SiteHPC1_V1_7014_SA1383_MOB.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1041922#pageId=0&department=165&mlink=1038092,30014134,HP_C1"}, "image": {"alt": "NEW Fashion Woven Dresses & Rompers", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_C1/062923_NEWFashionWovenDresses_SiteHPC1_V1_7014_SA1383_ANIM_3_MOB.jpg", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_C1/062923_NEWFashionWovenDresses_SiteHPC1_V1_7014_SA1383_MOB.svg"}, "background": {"linkData": {"to": "/browse/category.do?cid=1041922#pageId=0&department=165&mlink=1038092,30014134,HP_C1"}, "image": {"alt": "NEW Fashion Woven Dresses & Rompers", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_C1/062923_NEWFashionWovenDresses_SiteHPC1_V1_7014_SA1383_ANIM_4_MOB.jpg", "style": {"display": "block"}}}}}]}}, {"name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"height": "0"}}, "data": {"container": {"desktopStyle": {"height": "100%", "position": "relative", "width": "100%"}}, "ctaList": {"mobilePositionAboveContent": false, "className": "wcd_hp-cta full-width", "style": {"width": "100%", "zIndex": "31", "padding": "0 1rem", "button": {"color": "#767676!important", "borderColor": "#767676!important", "&:hover": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#767676!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "check them out"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1041618#pageId=0&department=136&mlink=1038092,30013505,HP_C1"}, {"text": "Girls", "href": "/browse/category.do?cid=1041723#pageId=0&department=48&mlink=1038092,30013505,HP_C1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041922#pageId=0&department=165&mlink=1038092,30013505,HP_C1"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064646#pageId=0&department=165&mlink=1038092,30013505,HP_C1"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto 2.5rem", "position": "relative", "maxWidth": "1920px"}, "components": [{"name": "Carousel", "type": "sitewide", "instanceName": "carousel-arrows", "data": {"modalCloseButtonAriaLabel": "Close", "carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": true, "slidesToShow": 1, "speed": 300, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": true}, "buttonSetting": {"pauseAltText": "Pause slideshow", "playAltText": "Play slideshow", "buttonStyle": {"position": "absolute", "left": "10px", "bottom": "2%", "transform": "translate(0,0)", "height": "1.5em", "width": "1.5em", "zIndex": "14"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_play--white.svg", "pauseBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_pause--white.svg"}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_C1/062923_NEWFashionWovenDresses_SiteHPC1_V1_7014_SA1383_DESK.svg", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1041618#pageId=0&department=136&mlink=1038092,30014134,HP_C1"}, "image": {"alt": "NEW Fashion Woven Dresses & Rompers", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_C1/062923_NEWFashionWovenDresses_SiteHPC1_V1_7014_SA1383_ANIM_1_DESK.jpg", "style": {"display": "block"}}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"overlay": {"alt": "", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_C1/062923_NEWFashionWovenDresses_SiteHPC1_V1_7014_SA1383_DESK.svg", "style": {"display": "block"}}, "background": {"linkData": {"to": "/browse/category.do?cid=1041922#pageId=0&department=165&mlink=1038092,30014134,HP_C1"}, "image": {"alt": "NEW Fashion Woven Dresses & Rompers", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/HP_C1/062923_NEWFashionWovenDresses_SiteHPC1_V1_7014_SA1383_ANIM_2_DESK.jpg", "style": {"display": "block"}}}}}]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"desktop": {"position": "absolute", "top": "65%", "left": "53%", "transform": "translate(-10%,0)", "zIndex": "2"}}, "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"button": {"backgroundColor": "transparent!important", "borderColor": "#767676!important", "color": "#767676!important", "&:hover": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:active": {"backgroundColor": "#767676!important", "color": "#FFF!important"}, "&:focus": {"backgroundColor": "#767676!important", "color": "#FFF!important"}}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "check them out"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1041618#pageId=0&department=136&mlink=1038092,30014134,HP_C1"}, {"text": "Girls", "href": "/browse/category.do?cid=1041723#pageId=0&department=48&mlink=1038092,30014134,HP_C1"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1041922#pageId=0&department=165&mlink=1038092,30014134,HP_C1"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1064646#pageId=0&department=165&mlink=1038092,30014134,HP_C1"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}}}, {"instanceName": "013123_Certona_2", "instanceDesc": "013123_Certona_2", "experimentRunning": false, "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "2rem", "large": "3rem"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"margin": "0 auto 36px", "flexDirection": "column", "padding": "0", "maxWidth": "1920px"}, "components": [{"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"lazy": true, "style": {"width": "100%", "@media only screen and (max-width:768px)": {"padding": "0", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}, "@media only screen and (min-width:769px)": {"padding": "0", "div[data-testid='recommended-product-card'] > div": {"padding": "0"}}}, "props": {"style": {"width": "100%"}, "className": "fullBleedCertona"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome1_rr", "displayTitle": true, "fullWidth": true, "certonaTitle": {"title": "These look like you", "style": {"mobile": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#707070", "lineHeight": "1.6rem", "fontSize": "4.1vw", "textAlign": "left", "padding": "0px 0 0 13px", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}, "desktop": {"display": "block", "fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "marginBottom": "0.5rem", "WebkitFontSmoothing": "antialiased", "color": "#707070", "lineHeight": "1.6rem", "fontSize": "1.1vw", "textAlign": "left", "padding": "0px 0 0 1.5%", "fontWeight": "400", "letterSpacing": "normal", "textTransform": "uppercase"}}}, "layout": "carousel", "centerMode": false, "useMobileConfig": true, "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "displayPlayPauseButton": false, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "arrows": true, "autoplay": false, "pauseOnHover": true, "infinite": false, "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "priceOffText": "off", "showMarketingFlag": false, "showPercentage": true, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0029/669/822/assets/CERTONA/CertonaCarat_Left.svg", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0029/669/822/assets/CERTONA/CertonaCarat_Left.svg", "nextArrowAlt": "Next", "arrowMaxWidth": "40px", "arrowPosition": "-8px", "responsive": [{"breakpoint": 1350, "settings": {"slidesToShow": 5, "slidesToScroll": 5}}, {"breakpoint": 768, "settings": {"slidesToShow": 1.3, "slidesToScroll": 2}}], "productTextStyles": {"productTitle": {"style": {"color": "#767676", "textAlign": "left", "fontSize": "0.75rem", "margin": "10px 13px"}}, "productPrice": {"style": {"color": "#2b2b2b", "fontSize": "0.75rem", "float": "left", "display": "block", "marginLeft": "13px", "marginTop": "-2px"}}, "productPercentage": {"style": {"color": "#CD2026", "fontSize": "0.75rem", "float": "left", "marginTop": "-2px", "display": "block", "fontWeight": "600"}}, "productSalePrice": {"style": {"color": "#CD2026", "fontSize": "0.75rem", "display": "block", "marginLeft": "13px", "marginTop": "1rem", "fontWeight": "600", "width": "fit-content"}}}, "size": {"width": "100%", "height": "150px"}, "productMarketingFlag": {"style": {"fontWeight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "maxWidth": "unset", "padding": "0px", "width": "auto"}}, "productCardImageStyles": {"width": "19vw", "margin": "0", "maxWidth": "unset", "padding": "0px"}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}}}, {"instanceName": "VCN/VDN", "instanceDesc": "VCN/VDN_050421", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"lazy": true, "defaultHeight": {"small": "134px", "large": "71px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "justifyContent": "space-between", "maxWidth": "1920px", "backgroundColor": "#FFF", "padding": "0 0 2.7rem", "margin": "0 auto 0"}, "components": [{"instanceDesc": "vdn_01", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box"}, "desktop": {"width": "25%", "boxSizing": "border-box"}}, "data": {"container": {"style": {"position": "relative"}, "desktopStyle": {"backgroundColor": "#fff", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Women", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_1_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_1_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1150781#pageId=0&department=136&mlink=1038092,30012285,HP_VCN_1_W", "title": "Women"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "zIndex": "1", "padding": "0", "position": "absolute", "top": "45%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "#FFF", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "11px", "fontWeight": "400", "letterSpacing": "0", "minHeight": "24px", "padding": "8px 16px", "width": "100%", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}, "button": {"&:focus": {"outline": "0"}}, "ul": {"borderColor": "#FFF", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}}}}, "desktopStyle": {"top": "45%", "zIndex": "1", "a, button": {"fontSize": "15px", "minHeight": "24px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1150781#pageId=0&department=136&mlink=1038092,30012285,HP_VCN_1_W"}, "composableButtonData": {"children": "Women", "font": "primary"}}]}}}, {"instanceDesc": "vdn_02", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box"}, "desktop": {"width": "25%", "boxSizing": "border-box"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#fff", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Men", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_2_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_2_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1151154#pageId=0&department=75&mlink=1038092,30012285,HP_VCN_2_M", "title": "Men"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "zIndex": "1", "padding": "0", "position": "absolute", "top": "45%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "#FFF", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "11px", "fontWeight": "400", "letterSpacing": "0", "minHeight": "24px", "padding": "8px 16px", "width": "100%", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}, "button": {"&:focus": {"outline": "0"}}, "ul": {"borderColor": "#FFF", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}}}}, "desktopStyle": {"top": "45%", "zIndex": "1", "a, button": {"fontSize": "15px", "minHeight": "24px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151154#pageId=0&department=75&mlink=1038092,30012285,HP_VCN_2_M"}, "composableButtonData": {"children": "Men", "font": "primary"}}]}}}, {"instanceDesc": "vdn_03", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box"}, "desktop": {"width": "25%", "boxSizing": "border-box"}}, "data": {"container": {"style": {"position": "relative"}, "desktopStyle": {"backgroundColor": "#fff", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Girls", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_3_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_3_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1151275#pageId=0&department=48&mlink=1038092,30012285,HP_VCN_3_G", "title": "Girls"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "45%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "#FFF", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "11px", "fontWeight": "400", "letterSpacing": "0", "minHeight": "24px", "padding": "8px 16px", "width": "100%", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}, "button": {"&:focus": {"outline": "0"}}, "ul": {"borderColor": "#FFF", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}}}}, "desktopStyle": {"top": "45%", "zIndex": "1", "a, button": {"fontSize": "15px", "minHeight": "24px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151275#pageId=0&department=48&mlink=1038092,30012285,HP_VCN_3_G"}, "composableButtonData": {"children": "Girls", "font": "primary"}}]}}}, {"instanceDesc": "vdn_04", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box"}, "desktop": {"width": "25%", "boxSizing": "border-box"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#fff", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Boys", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_4_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_4_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1151296#pageId=0&department=16&mlink=1038092,30012285,HP_VCN_4_B", "title": "Boys"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "45%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "#FFF", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "11px", "fontWeight": "400", "letterSpacing": "0", "minHeight": "24px", "padding": "8px 16px", "width": "100%", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}, "button": {"&:focus": {"outline": "0"}}, "ul": {"borderColor": "#FFF", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}}}}, "desktopStyle": {"top": "45%", "a, button": {"fontSize": "15px", "minHeight": "24px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151296#pageId=0&department=16&mlink=1038092,30012285,HP_VCN_4_B"}, "composableButtonData": {"children": "Boys", "font": "primary"}}]}}}, {"instanceDesc": "vdn_05", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box"}, "desktop": {"width": "25%", "boxSizing": "border-box"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#fff", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Toddler Girl", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_5_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_5_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1151336#pageId=0&department=165&mlink=1038092,30012285,HP_VCN_5_TG", "title": "<PERSON>ler Girl"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "45%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "#FFF", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "11px", "fontWeight": "400", "letterSpacing": "0", "minHeight": "24px", "padding": "8px 16px", "width": "100%", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}, "button": {"&:focus": {"outline": "0"}}, "ul": {"borderColor": "#FFF", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}}}}, "desktopStyle": {"top": "45%", "a, button": {"fontSize": "15px", "minHeight": "24px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151336#pageId=0&department=165&mlink=1038092,30012285,HP_VCN_5_TG"}, "composableButtonData": {"children": "<PERSON>ler Girl", "font": "primary"}}]}}}, {"instanceDesc": "vdn_06", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box"}, "desktop": {"width": "25%", "boxSizing": "border-box"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#fff", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Toddler Boy", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_6_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_6_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1151463#pageId=0&department=166&mlink=1038092,30012285,HP_VCN_6_TB", "title": "<PERSON><PERSON>"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "45%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "#FFF", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "11px", "fontWeight": "400", "letterSpacing": "0", "minHeight": "24px", "padding": "8px 16px", "width": "100%", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}, "button": {"&:focus": {"outline": "0"}}, "ul": {"borderColor": "#FFF", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}}}}, "desktopStyle": {"top": "45%", "a, button": {"fontSize": "15px", "minHeight": "24px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151463#pageId=0&department=166&mlink=1038092,30012285,HP_VCN_6_TB"}, "composableButtonData": {"children": "<PERSON><PERSON>", "font": "primary"}}]}}}, {"instanceDesc": "vdn_07", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box"}, "desktop": {"width": "25%", "boxSizing": "border-box"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#fff", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Baby Girl", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_7_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_7_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1151862#pageId=0&department=165&mlink=1038092,30012285,HP_VCN_7_BG", "title": "Baby Girl"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "45%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "#FFF", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "11px", "fontWeight": "400", "letterSpacing": "0", "minHeight": "24px", "padding": "8px 16px", "width": "100%", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}, "button": {"&:focus": {"outline": "0"}}, "ul": {"borderColor": "#FFF", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}}}}, "desktopStyle": {"top": "45%", "a, button": {"fontSize": "15px", "minHeight": "24px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151862#pageId=0&department=165&mlink=1038092,30012285,HP_VCN_7_BG"}, "composableButtonData": {"children": "Baby Girl", "font": "primary"}}]}}}, {"instanceDesc": "vdn_08", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%", "boxSizing": "border-box"}, "desktop": {"width": "25%", "boxSizing": "border-box"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#fff", "&:hover img": {"opacity": "0.85"}}}, "background": {"image": {"alt": "Shop Baby Boy", "srcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_8_MOB.jpg", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/014/454/assets/VDN/050123_SUMHPVDNRefreshSite_SiteHPVDN_V1_6575_SA1350_8_DESK.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1151906#pageId=0&department=166&mlink=1038092,30012285,HP_VCN_8_BB", "title": "Baby Boy"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "45%", "transform": "translateX(-50%)", "a, button": {"backgroundColor": "#FFF", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "11px", "fontWeight": "400", "letterSpacing": "0", "minHeight": "24px", "padding": "8px 16px", "width": "100%", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}, "button": {"&:focus": {"outline": "0"}}, "ul": {"borderColor": "#FFF", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "padding": "0", "li": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #2b2b2b"}, "a": {"boxSizing": "border-box", "color": "#2b2b2b", "padding": "10px 12px", "&:hover": {"backgroundColor": "#2b2b2b", "color": "#FFF"}}}}}, "desktopStyle": {"top": "45%", "a, button": {"fontSize": "15px", "minHeight": "24px"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "0px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1151906#pageId=0&department=166&mlink=1038092,30012285,HP_VCN_8_BB"}, "composableButtonData": {"children": "Baby Boy", "font": "primary"}}]}}}]}}}}, {"instanceName": "dpg-banner2", "instanceDesc": "DPG-placeholder-2", "name": "OptimizelyPlaceholder", "type": "sitewide", "experimentRunning": true, "data": {"defaultHeight": {"small": "0", "large": "0"}}}]}, "sitewide": {"desktopemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "attrition_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "isAsyncExperiment": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"instanceName": "dpg_emergency_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "mobileemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "attrition_banner_mob", "type": "builtin", "name": "div", "experimentRunning": true, "isAsyncExperiment": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"instanceName": "dpg_emergency_banner_mob", "type": "builtin", "name": "div", "experimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "headline": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "get_your_gifts_on_time_phase4", "instanceDesc": "Global Skinny Banner", "type": "builtin", "name": "div", "data": {"props": {"style": {"backgroundColor": "#ffffff", "position": "relative", "width": "100%", "maxWidth": "1920px", "height": "auto", "lineHeight": "0", "margin": "0 auto"}}, "components": [{"instanceName": "card_acquisition_drive", "instanceDesc": "12/21", "name": "div", "type": "builtin", "gsb-ciid": "28299514", "experimentRunning": true, "data": {"props": {"style": {"backgroundColor": "#ffffff", "position": "relative", "width": "100%", "lineHeight": "0", "maxWidth": "1920px", "margin": "0 auto"}}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"style": {"position": "relative", "maxWidth": "1920px", "margin": "0 auto", "lineHeight": "0"}}, "components": [{"useGreyLoadingEffect": false, "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "transparent"}, "desktopStyle": {"width": "100%", "backgroundColor": "transparent"}}, "background": {"image": {"alt": "Card acquisition drive 12/25 - 1/3: 20% + 2022 Bonus Points ($20 reward)", "srcUrl": "/Asset_Archive/GFWeb/content/0028/406/929/assets/122621_GREAT_GAP_SALE_PHASE1_HP2_GLOBAL_MOB.png", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0028/406/929/assets/122621_GREAT_GAP_SALE_PHASE1_HP2_GLOBAL_DESK.png", "style": {}}, "linkData": {"to": "https://apply.syf.com/eapply/eapply.action?clientCode=GAP&sitecode=gfbchbad0"}, "style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"height": "auto", "width": "auto"}, "desktopStyle": {}, "className": "", "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/buy/promo_legal_details.do?promoId=840645", "height": "500px"}}, "composableButtonData": {"children": ["Details"], "style": {"position": "absolute", "bottom": "3%", "right": "4%", "fontSize": "10px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "padding": "0", "outline": "none", "color": "#fff"}, "desktopStyle": {"bottom": "3%", "right": "1%"}}}]}}}]}}]}}]}}]}}, "secondary-headline": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "GSB_052423", "instanceDesc": "Global Skinny Banner", "type": "builtin", "name": "div", "data": {"props": {"style": {"backgroundColor": "#ffffff", "position": "relative", "width": "100%", "maxWidth": "1920px", "height": "auto", "lineHeight": "0", "margin": "0 auto"}}, "components": [{"instanceName": "GSB_05242023_sitewide", "instanceDesc": "05/24", "name": "div", "type": "builtin", "gsb-ciid": "29203575", "experimentRunning": true, "meta": {"excludePageTypes": []}, "data": {"props": {"style": {"backgroundColor": "#ffffff", "position": "relative", "width": "100%", "lineHeight": "0", "maxWidth": "1920px", "margin": "0 auto 2.5rem"}}, "components": [{"useGreyLoadingEffect": false, "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "transparent"}, "desktopStyle": {"width": "100%", "backgroundColor": "transparent"}}, "background": {"image": {"alt": "50-70% off + Extra 15% Off", "srcUrl": "/Asset_Archive/GFWeb/content/0030/013/194/assets/052423_MemorialDayAlmostEv5_GlobalSkinnyBannerRestofSite_1_6923_MOB.svg?v=2", "desktopSrcUrl": "/Asset_Archive/GFWeb/content/0030/013/194/assets/052423_MemorialDayAlmostEv5_GlobalSkinnyBannerRestofSite_1_6923_DESK.svg?v=2", "style": {}}, "linkData": {"to": "/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1037460,30013194,GSB_PROMO"}, "style": {}, "desktopStyle": {}}}}, {"name": "LayeredContentModule", "type": "sitewide", "description": "DETAILS_CTA", "tileStyle": {"desktop": {}, "mobile": {}}, "data": {"container": {"className": "", "style": {"position": "absolute", "top": "66%", "right": "50%", "transform": "translateX(50%)", "whiteSpace": "nowrap"}, "desktopStyle": {"position": "absolute", "top": "50%", "right": "1%", "transform": "translateY(-50%)", "whiteSpace": "nowrap"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {}, "desktopStyle": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=968417,968497", "height": "500px"}}, "composableButtonData": {"children": ["Exclusions apply. "], "style": {"fontSize": "8px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "none", "textTransform": "none", "padding": "0", "outline": "none", "color": "#FFF", "position": "relative"}, "desktopStyle": {"fontSize": "10px", "color": "#FFF", "left": "0"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "iframeData": {"title": "", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=968417,968497", "height": "500px"}}, "composableButtonData": {"children": ["details"], "style": {"fontSize": "8px", "backgroundColor": "transparent", "fontWeight": "400", "textDecoration": "underline", "textTransform": "uppercase", "padding": "0", "outline": "none", "color": "#FFF", "position": "relative"}, "desktopStyle": {"fontSize": "10px", "color": "#FFF", "left": "0"}}}]}}}]}}]}}]}}, "edfslarge": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-large", "type": "sitewide", "name": "MktEdfsLarge", "experimentRunning": true, "tileStyle": {}, "data": {"lazy": false, "experimentRunning": false, "defaultData": {"text": "Free shipping on $50+ for rewards members", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=1194686", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "Sign in or join", "path": "/my-account/sign-in", "style": {}}}}]}}, "edfssmall": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-small", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"lazy": false, "defaultHeight": {"large": "80px", "small": "50px"}, "isVisible": {"large": false, "small": true}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto"}, "components": [{"instanceDesc": "WCD HP CSS Modifications", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>#sitewide-app > header > div.sitewide-13o7eu2 > div > div:nth-child(2) > div > div > div > button {text-transform:uppercase}</style>"}}, {"type": "sitewide", "name": "MktEdfsSmall", "data": {"lazy": false, "experimentRunning": false, "styles": {"headline": {}, "detailsButton": {}}, "defaultData": {"textStrong": "", "text": "Free shipping on $50+ for rewards members", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=1194686", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "Sign in or join", "path": "/my-account/sign-in", "style": {}}}}]}}}}]}}, "promorover": {"type": "builtin", "name": "div", "data": {"components": [{"sitewide-promorover-desc": "******** ILP rover", "instanceName": "gpus_promo_sticker_072021", "name": "MktSticker", "type": "sitewide", "experimentRunning": true, "data": {"shouldWaitForOptimizely": true, "placeholderSettings": {"useGreyLoadingEffect": false, "mobile": {"height": 0, "width": 0}, "desktop": {"height": 0, "width": 0}}, "largeImg": "/Asset_Archive/GFWeb/content/0027/281/481/assets/ILP_rover_widget.svg", "altText": "Gap Good Rewards. Earn points on every purchase. 100 points = $1 reward. Free fast shipping on all orders over $50.", "localStorageKey": "wcd_gpRoverStorage_081821", "localStorageVal": "wcd_gpRoverHasBeenClosed_081821", "stickerAriaLabel": "stickerAriaLabel", "stickerCloseButtonAriaLabel": "stickerCloseButtonAriaLabel", "showModal": true, "modalCloseButtonAriaLabel": "Close", "modalTitle": "Gap Good Rewards", "modalUrlDetail": "/buy/promo_legal_details.do?promoId=798745", "modalPopupAriaLabel": "modalPopupAriaLabel", "options": {"includePageTypes": ["category", "division", "home", "product"]}}}]}}, "countdown": {"type": "builtin", "name": "div", "data": {"components": []}}, "footer": {"name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "544px", "large": "435px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "width": "100%"}, "components": [{"name": "Footer", "type": "sitewide", "data": {"socialLinks": [{"to": "https://www.facebook.com/gap/", "text": "Follow Gap on Facebook"}], "emailRegistration": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<h3 class=\"wcd_footer_h1 uppercase\">See It First</h3>"}}, "emailPlaceholderText": "Enter your email address", "submitButtonText": "Join", "submitButtonOptions": {"mobile": {"className": "wcd_footer_cta"}, "desktop": {"className": "wcd_footer_cta"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<p class=\"wcd_footer_copy legal\"><sup>*</sup>Valid for first-time registrants only & applies to reg. priced items only.<br><a onclick=\"return contentItemLink(this,'','CS_Footer_PrivacyPolicy');\" href=\"https://corporate.gapinc.com/en-us/consumer-privacy-policy\" target=\"_blank\" class=\"uppercase nowrap\">Privacy Policy</a>"}}}, "marketingBannerLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"instanceName": "footer_overrides", "instanceDesc": "<PERSON><PERSON> Footer Overrides", "name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<style>#sitewide-footer,#sitewide-footer button,#sitewide-footer input,#sitewide-footer select,#sitewide-footer textarea{font-family:'Gap Sans',Helvetica,Arial,Roboto,sans-serif}#sitewide-footer{background-color:#767676;color:#fff}.gap-footer *{box-sizing:border-box}.gap-footer .nowrap{white-space:nowrap}.gap-footer .uppercase{text-transform:uppercase}.gap-footer sup{font-size:1em;line-height:0;vertical-align:baseline}.gap-footer .wcd_footer_h1{font-size:14px;font-weight:500;line-height:1.125;margin-bottom:.25em}.gap-footer .wcd_footer_copy:not(:last-child){margin-bottom:1.125em}.gap-footer .wcd_footer_copy.legal{font-size:10px}.gap-footer .wcd_footer_copy a{text-decoration:underline}.gap-footer .wcd_footer_cta a,.gap-footer .wcd_footer_cta button,.gap-footer a.wcd_footer_cta,.gap-footer button.wcd_footer_cta{-ms-flex-align:center;align-items:center;background-color:#fff;border-width:0;color:#767676;font-size:14px;font-weight:500;height:32px;-ms-flex-pack:center;justify-content:center;letter-spacing:0;padding-left:16px;padding-right:16px;text-transform:uppercase}.gap-footer .wcd_footer_cta a:hover,.gap-footer .wcd_footer_cta button:hover,.gap-footer a.wcd_footer_cta:hover,.gap-footer button.wcd_footer_cta:hover{background-color:#fff;border-width:0;color:#2b2b2b}.gap-footer .wcd_footer_cta{display:-ms-flexbox;display:flex}.gap-footer .wcd_footer_cta.full-width{width:100%}.gap-footer .wcd_footer_cta.full-width a,.gap-footer .wcd_footer_cta.full-width button{width:100%}.gap-footer .wcd_footer_cta.full-width a:not(:first-child),.gap-footer .wcd_footer_cta.full-width button:not(:first-child){margin-left:8px}.gap-footer .wcd_footer_cta.details button{background-color:transparent;color:#fff;display:inline;font-size:10px;height:auto;min-height:16px;min-width:36px;padding:0;text-decoration:underline}.gap-footer .wcd_footer_cta.details button:hover{color:#fff}.gap-footer .wcd_footer_cta a,.gap-footer .wcd_footer_cta button{display:-ms-flexbox;display:flex}.gap-footer .wcd_footer_cta span{font-size:1.125em;padding-bottom:.25em}.gap-footer .wcd_footer_cta ul{background-color:transparent;box-shadow:none;padding-bottom:4px}.gap-footer .wcd_footer_cta li{border-bottom-width:0;border-color:#fff;padding:0}.gap-footer .wcd_footer_cta li a{font-weight:400;padding-left:32px;text-transform:none}.gap-footer [data-testid=prefooter-row]{margin-bottom:0}.gap-footer .email-registration__wrapper{-ms-flex-align:start;align-items:flex-start;background-color:transparent;min-height:120px;padding:26px 0}.gap-footer .email-registration__wrapper>div{margin:0 auto;max-width:640px;width:calc(100% - 32px)}.gap-footer .email-registration__wrapper .email-registration__title{max-width:100%;padding:0;text-align:left}.gap-footer .email-registration__wrapper .email-registration__inputs{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;max-width:420px}.gap-footer .email-registration__wrapper .email-registration__disclaimer{padding-left:0}.gap-footer .email-registration__wrapper .email-registration__form{-ms-flex-align:end;align-items:flex-end;display:-ms-flexbox;display:flex;margin-bottom:24px}.gap-footer .email-registration__wrapper .email-text-input-wrapper{margin-right:24px}.gap-footer .email-registration__wrapper .email-registration__form-email{margin:0;padding-bottom:0}.gap-footer .email-registration__wrapper .email-registration__form-email input[type=email]{margin-top:0;padding:0}.gap-footer .email-registration__wrapper .email-registration__form-email span{font-size:13px;top:50%;transform:translateY(-50%)}.gap-footer .email-registration__wrapper .email-registration__form-email span.sitewide-v1qhrf-LabelText-Label{font-size:10px;text-transform:none;top:0}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container .wcd_footer_cta{min-height:32px;padding-bottom:0;padding-top:0}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container div[aria-label=loading]{transform:rotate(90deg)}.gap-footer .email-registration__wrapper .notification-after-button:empty,.gap-footer .email-registration__wrapper .notification-before-form:empty{display:none}.gap-footer .medallia-feedback-wrapper{-ms-flex-order:4;order:4;padding:0 16px;width:100%}.gap-footer .medallia-feedback-wrapper>button{-ms-flex-align:center;align-items:center;background-color:#fff;border-width:0;color:#2b2b2b;display:-ms-flexbox;display:flex;font-weight:400;height:36px;-ms-flex-pack:center;justify-content:center;letter-spacing:0;margin:0 auto;max-width:640px;padding:0 16px;width:100%}.gap-footer .medallia-feedback-wrapper>button img{margin-right:.375rem}.gap-footer .footer-copyright-section{background-color:#767676;border-top-color:#fff;border-width:0;color:#fff;-ms-flex-order:5;order:5;padding:24px 0 80px;width:100%}.gap-footer .footer-copyright-section .footer-legal__wrapper{margin:0 auto;max-width:640px;text-align:left;width:calc(100% - 32px)}.gap-footer .footer-copyright-section .footer_copyright-row{font-size:11px;line-height:1.5}.gap-footer .footer-copyright-section .footer_copyright-row:not(:last-child){margin-bottom:1.5em}.gap-footer .footer-copyright-section a,.gap-footer .footer-copyright-section button{color:inherit;font-size:inherit}.gap-footer .footer-copyright-section a:hover,.gap-footer .footer-copyright-section button:hover{text-decoration:underline}.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--divider,.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--span{color:inherit;font-size:inherit}.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--span,.gap-footer .footer-copyright-section .footer-legal__wrapper a,.gap-footer .footer-copyright-section .footer-legal__wrapper button{display:inline-block;text-transform:uppercase}.gap-footer .footer-container-wrapper{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;max-width:100%}.gap-footer .footer-container-wrapper .copy-wrapper{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;margin-bottom:12px}.gap-footer .footer-container-wrapper>div:nth-child(5){-ms-flex-direction:column;flex-direction:column;margin-left:auto;margin-right:auto;max-width:672px;width:100%}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child{margin-bottom:30px}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta{background-color:transparent;color:inherit;-ms-flex-direction:column;flex-direction:column}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta a,.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta button{background-color:transparent;color:inherit;height:24px;-ms-flex-pack:start;justify-content:flex-start}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:nth-child(2){margin-bottom:6px;padding:0 16px}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:10px;line-height:1.25}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a:not(:last-child){margin-bottom:.5em}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a:hover{text-decoration:underline}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column .wcd_footer_header{margin-bottom:.75em;text-transform:uppercase}@media only screen and (min-width:768px){.gap-footer .wcd_footer_h1{margin-bottom:1em}.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:16px;font-weight:500;line-height:1}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:14px}.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:12px}.gap-footer [data-testid=prefooter-row]{display:block;padding-bottom:48px}.gap-footer .email-registration__wrapper{padding-bottom:0;padding-left:0;padding-top:0}.gap-footer .email-registration__wrapper>div{margin-left:0;max-width:100%;width:100%}.gap-footer .email-registration__wrapper .email-text-input-wrapper{margin-right:10px}.gap-footer .footer-copyright-section{border-top-width:1px;padding-top:44px}.gap-footer .footer-copyright-section .footer-legal__wrapper{margin:0;max-width:1920px;padding-left:2.5%;padding-right:2.5%}.gap-footer .footer-container-wrapper{-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between;margin:0 auto;max-width:1920px;padding-top:30px}.gap-footer .footer-container-wrapper .copy-wrapper{-ms-flex-align:center;align-items:center;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;margin-bottom:0}.gap-footer .footer-container-wrapper .copy-wrapper>div:not(:last-child){margin-right:.75em}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:nth-child(2){display:none}.gap-footer .wcd_footer-links-wrapper{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{-ms-flex-positive:1;flex-grow:1}.gap-footer [data-testid=prefooter-row]{padding-left:2.5%;padding-right:2.5%;width:100%}.gap-footer .footer-container-wrapper>div:nth-child(3){padding-bottom:48px;padding-left:2.5%;padding-right:2.5%;width:100%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:16px}}@media only screen and (min-width:1024px){.gap-footer .wcd_footer-links-wrapper{-ms-flex-pack:start;justify-content:flex-start}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{-ms-flex-positive:initial;flex-grow:initial}.gap-footer [data-testid=prefooter-row]{padding-right:0;width:300px}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:3%;padding-left:0;width:calc(97% - 300px)}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:7%}}@media only screen and (min-width:1280px){.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:14px}.gap-footer [data-testid=prefooter-row]{width:24%}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:6%;padding-bottom:84px;width:70%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:11%}}@media only screen and (min-width:1440px){.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:24px}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:18px}.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:16px}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container .wcd_footer_cta{font-size:20px;height:52px;padding:0 26px}.gap-footer .footer-container-wrapper{padding-top:54px}.gap-footer [data-testid=prefooter-row]{width:29%}.gap-footer .footer-container-wrapper>div:nth-child(3){width:65%}}@media only screen and (min-width:1920px){.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:26px}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:21px}.gap-footer [data-testid=prefooter-row]{width:24%}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:0;width:71%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:18%}}@media only screen and (max-width:767px){.gap-footer .footer-container-wrapper>div:nth-child(2){display:none}}</style>"}}]}}}}, "customerSupportLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Mobile Footer Links", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_footer_cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "Customer Support"}, "submenu": [{"text": "Customer Service", "href": "/customerService/info.do?cid=1037175&mlink=1037267,********,CS_Footer_CustomerService"}, {"text": "Buy Online. Pick Up In-Store.", "href": "/browse/info.do?cid=1178485&mlink=1037267,********,Footer_BOPIS"}, {"text": "Store Locator", "href": "/stores?mlink=1037267,********,cs_footer_storelocator"}, {"text": "GapCash", "href": "/browse/info.do?cid=1077281&mlink=1037267,********,Footer_GapCash"}, {"text": "GiftCards", "href": "/customerService/info.do?cid=1067353&mlink=1037267,********,Footer_GiftCards"}]}}, {"buttonDropdownData": {"heading": {"text": "Gap Good Rewards"}, "submenu": [{"text": "Join <PERSON> Good Rewards", "href": "/my-account/sign-in?mlink=1037267,********,UNIFOOTER_GGR_ACQ"}, {"text": "Apply for a Credit Card", "href": "/my-account/sign-in?creditOffer=barclays&sitecode=GPFSUNIFTM&mlink=1037267,********,UNIFOOTER_GGR_CARD_ACQ"}, {"text": "My Rewards & Benefits", "href": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=1037267,********,UNIFOOTER_MTL_RET"}, {"text": "Pay Credit Card Bill", "href": "https://gap.barclaysus.com/servicing/home?redirectAction=/payment", "target": "_blank"}]}}, {"buttonDropdownData": {"heading": {"text": "About Us"}, "submenu": [{"text": "Our Values", "href": "https://www.gapinc.com/en-us/values", "target": "_blank"}, {"text": "Sustainability", "href": "https://www.gapinc.com/en-us/values/sustainability", "target": "_blank"}, {"text": "Equality and Belonging", "href": "https://www.gapinc.com/en-us/values/equality-belonging", "target": "_blank"}, {"text": "Careers", "href": "https://www.gapinc.com/en-us/careers/gap-careers", "target": "_blank"}]}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Desktop Footer Links", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-wrapper"}, "components": [{"instanceDesc": "Desktop Footer Links - Column 1", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["Customer Support"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1037175&mlink=1037267,********,CS_Footer_CustomerService"}, "components": ["Customer Service"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/browse/info.do?cid=1178485&mlink=1037267,********,Footer_BOPIS", "target": "_blank"}, "components": ["Buy Online. Pick Up In-Store."]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/stores?mlink=1037267,********,cs_footer_storelocator"}, "components": ["Store Locator"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/browse/info.do?cid=1077281&mlink=1037267,********,Footer_GapCash"}, "components": ["GapCash"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1067353&mlink=1037267,********,Footer_GiftCards"}, "components": ["GiftCards"]}}]}}, {"instanceDesc": "Desktop Footer Links - Column 2", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["Gap Good Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?mlink=1037267,********,UNIFOOTER_GGR_ACQ"}, "components": ["Join <PERSON> Good Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?creditOffer=barclays&sitecode=GPFSUNIFTD&mlink=1037267,********,UNIFOOTER_GGR_CARD_ACQ"}, "components": ["Apply for a Credit Card"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=1037267,********,UNIFOOTER_MTL_RET"}, "components": ["My Rewards & Benefits"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://gap.barclaysus.com/servicing/home?redirectAction=/payment", "target": "_blank"}, "components": ["Pay Credit Card Bill"]}}]}}, {"instanceDesc": "Desktop Footer Links - Column 3", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["About Us"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/values", "target": "_blank"}, "components": ["Our Values"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/values/sustainability", "target": "_blank"}, "components": ["Sustainability"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/values/equality-belonging", "target": "_blank"}, "components": ["Equality and Belonging"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/careers/gap-careers", "target": "_blank"}, "components": ["Careers"]}}]}}]}}]}}}}}}]}}}}, "header": {"default": {"isStickyEnabled": true, "headerLayout": "sameRow", "stickyScrollDirection": "both", "contrast": "dark", "fullBleedOptions": {"isFullBleedEnabled": false, "hasTransparencyLayer": false, "fullBleedContrast": "dark"}, "styles": {"marginLeft": "auto", "marginRight": "auto", "maxWidth": "1920px"}, "desktopOverride": {"isStickyEnabled": true}}}, "topnav": {"name": "MegaNav", "type": "sitewide", "data": {"isNavSticky": false, "classStyles": {"topnav li:not(.catnav--item)": "padding: 0;", "topnav img.sds_absolute": "left: 0;", "topnav a.divisionLink": "box-shadow: none !important; box-sizing: border-box; color: #2b2b2b; display: block; font-size: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); font-weight: 400; height: 90px; line-height: 1; min-height: 0vw; padding: 40px 0 0; position: relative; text-transform: uppercase;", "topnav a.divisionLink::before": "border-color: transparent; border-style: solid; border-width: 0 0 1px; content: ''; height: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); left: 50%; min-height: 12px; padding-bottom: 3px; position: absolute; top: 40px; transform: translateX(-50%); width: calc(100% - 2vw);", "topnav a.divisionLink._selected": "color: #2b2b2b;", "topnav li:hover a.divisionLink": "background-color: #fff;", "topnav li:hover a.divisionLink::before": "border-color: #2b2b2b;", "topnav a.divisionLink._selected::before": "border-color: #2b2b2b;", "topnav a.divisionLink:hover": "box-shadow: none !important;", "topnav li.catnav--header > span": "border-bottom-color: #2b2b2b; color:#2b2b2b;", "topnav li.catnav--header > a": "border-bottom-color: #2b2b2b;color:#2b2b2b;", "topnav .catnav--item.catnav--item-selected": "color: #2b2b2b;", "topnav .catnav--item.catnav--item-selected a": "color: #2b2b2b;", "topnav .catnav--item--link": "max-width: 265px;", "topnav .catnav--item": "color:#2b2b2b;", "topnav .catnav--item--link:hover": "color: #2b2b2b;", "topnav a.divisionLink.navlink-red": "color: #D00000;", "topnav a.divisionLink.navlink-pink": "color: #D62498;", "topnav a.divisionLink.navlink-gift": "color: #006646;", "sitewide-l0i3ri[aria-label='holiday essentials']": "color: #D62498", "topnav li.catnav--header > span[aria-label='deals']": "color: #e51937", "topnav li.catnav--header > span[aria-label='clearance']": "color: #e51937", "topnav li.catnav--header > span[aria-label='more deals']": "color: #e51937", "topnav li.catnav--header>a[data-categoryid='1150513']": "color: #e51937", "topnav li.catnav--header>span[data-categoryid='1184866']": "color: #e51937", "topnav li.catnav--header>a[data-categoryid='1184866']": "color: #e51937", "topnav li.catnav--header>span[data-categoryid='1144536']": "color: #006646", "topnav li.catnav--header>span[data-categoryid='1144613']": "color: #006646", "topnav li.catnav--header>span[data-categoryid='1144578']": "color: #006646", "topnav li.catnav--header>span[data-categoryid='1144579']": "color: #006646", "topnav li.catnav--header>span[data-categoryid='1144266']": "color: #006646", "topnav li.catnav--header>span[data-categoryid='1144584']": "color: #006646", "meganav": "border-top-width: 0"}, "activeDivisions": [{"name": "New Arrivals", "divisionId": ["1137421"], "megaNavOrder": [["1137422"], ["3019886"]], "numberOfColumns": {"1137422": 2}, "exclusionIds": [], "customStyles": {}}, {"name": "Women", "subtitle": "New! 0-30 & XS-4X", "divisionId": ["/browse/division.do?cid=1040941&mlink=1037059,20188717,Megnav_Women&clink=20188717", "1040941"], "megaNavOrder": [["1075602", "1144615", "1092843", "1042792"], ["1083398"], ["1099767", "1089399"]], "numberOfColumns": {"1083398": 2}, "exclusionIds": [], "customStyles": {"1042792": {"inlineStyle": {"color": "#D00000"}}, "1092843": {"inlineStyle": {"color": "#D00000"}}, "1144615": {"inlineStyle": {"color": "#D00000"}}}}, {"name": "Men", "divisionId": ["/browse/division.do?cid=1040942&mlink=1037059,29624941,Megnav_Men&clink=29624941", "1040942"], "megaNavOrder": [["1084557", "1144614", "1092773"], ["1083400"], ["1144612", "1162911"]], "numberOfColumns": {"1083400": 2}, "exclusionIds": [], "customStyles": {"1042764": {"colorScheme": "sale"}, "1092773": {"inlineStyle": {"color": "#D00000"}}, "1144613": {"inlineStyle": {"color": "#006646"}}, "1144614": {"inlineStyle": {"color": "#D00000"}}}}, {"name": "Girls", "divisionId": ["/browse/division.do?cid=1040943&mlink=1037059,20188717,Megnav_Girls&clink=20188717", "1040943"], "megaNavOrder": [["1084367", "1144611", "1092850", "1042785"], ["1084370"], ["1093865", "1144609"]], "numberOfColumns": {"1084370": 2}, "exclusionIds": [], "customStyles": {"1042785": {"inlineStyle": {"color": "#D00000"}}, "1092850": {"inlineStyle": {"color": "#D00000"}}, "1144578": {"inlineStyle": {"color": "#006646"}}, "1144611": {"inlineStyle": {"color": "#D00000"}}}}, {"name": "Boys", "divisionId": ["/browse/division.do?cid=1079661&mlink=1037059,20188717,Megnav_Boys&clink=20188717", "1079661"], "megaNavOrder": [["1083130", "1144606", "1092793", "1042766"], ["1083133"], ["1093866", "1144607"]], "numberOfColumns": {"1083133": 2}, "exclusionIds": [], "customStyles": {"1042766": {"inlineStyle": {"color": "#D00000"}}, "1083131": {"colorScheme": "sale"}, "1092793": {"inlineStyle": {"color": "#D00000"}}, "1144579": {"inlineStyle": {"color": "#006646"}}, "1144606": {"inlineStyle": {"color": "#D00000"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=1091867&mlink=1037059,29624941,Megnav_<PERSON><PERSON>&clink=29624941", "1091867"], "megaNavOrder": [["1093607", "1144054", "1092869", "1092872", "1042786", "1042787"], ["1049875"], ["1049876"], ["1093549", "1144264"]], "exclusionIds": [], "customStyles": {"1042786": {"inlineStyle": {"color": "#D00000"}}, "1042787": {"inlineStyle": {"color": "#D00000"}}, "1092869": {"inlineStyle": {"color": "#D00000"}}, "1092872": {"inlineStyle": {"color": "#D00000"}}, "1144054": {"inlineStyle": {"color": "#D00000"}}, "1144266": {"inlineStyle": {"color": "#006646"}}}}, {"name": "Baby", "divisionId": ["/browse/division.do?cid=1040944&mlink=1037059,29624941,Megnav_Boys&clink=29624941", "1040944"], "megaNavOrder": [["1093606", "1144585", "1092873", "1092875", "1091767", "1091779"], ["1064166"], ["1064167"], ["1075607", "1144583"]], "exclusionIds": [], "customStyles": {"1091767": {"inlineStyle": {"color": "#D00000"}}, "1091779": {"inlineStyle": {"color": "#D00000"}}, "1092873": {"inlineStyle": {"color": "#D00000"}}, "1092875": {"inlineStyle": {"color": "#D00000"}}, "1144584": {"inlineStyle": {"color": "#006646"}}, "1144585": {"inlineStyle": {"color": "#D00000"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["1137431"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a data-categoryid='1143678' href='/browse/info.do?cid=1151006&mlink=1037059,29624941,<PERSON><PERSON>_Gapforgood' class='catnav--item--link' target='_blank' style='position:relative;display:block;'><img style='position:relative;' src='/Asset_Archive/GFWeb/content/0030/013/416/assets/110821_G4G_MEGA_NAV_TILE_IMG.jpg' alt='Gap For Good'><img style='position:absolute; left:0;' src='/Asset_Archive/GFWeb/content/0030/013/416/assets/110821_G4G_MEGA_NAV_TILE_COPY_NEW.svg' alt='Gap For Good'></a></li></ul></li>"], ["1183311"], ["1183320"]], "numberOfColumns": {}, "exclusionIds": [], "customStyles": {}}, {"name": "Logo", "divisionId": ["1164903"], "megaNavOrder": [["1183285"]], "numberOfColumns": {"1183285": 2}, "exclusionIds": [], "customStyles": {}}, {"name": "Deals & Clearance", "divisionId": ["/browse/division.do?cid=1150511", "1150511"], "megaNavOrder": [["1184866"], ["1150513"]], "numberOfColumns": {"1150513": 2, "1184866": 2}, "exclusionIds": [], "customStyles": {"1150511": {"className": "clearance navlink-red"}, "1150513": {"colorScheme": "sale"}, "1184866": {"colorScheme": "sale"}}}, {"name": "Gap.com", "divisionId": ["http://www.gap.com/browse/home.do?cid=5058&mlink=1037059,18446873,GFOL_MainNav_GOLHP&clink=18446873", "5058"], "megaNavOrder": [], "exclusionIds": [], "customStyles": {"5058": {"className": "gapTextLink"}}}]}}, "promodrawer": {"name": "PromoDrawerComponentV2", "type": "sitewide", "instanceName": "promoDrawer-********", "experimentRunning": false, "data": {"buildInfo": ["********", "GF"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "info", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "Profile", "LoyaltyValueCenter"], "anchor": "bottom"}, "autoFire": "scroll", "disabledAutoFirePageTypes": ["category"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1037345,********,PD_TILE1\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GFWeb/content/0030/014/009/assets/061723_SummerCyberOLAE60O_PromoDrawer_1_6647_.png\" alt=\"free shipping\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "tap", "overlay": "Applied at checkout", "defaultMessage": "tap to apply", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "973279", "genericCode": "GFSHIP"}, "promoId": "li3gk5r3"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n  .pd_four-cta-lijdklaw {\n    background-color: #122344; /* default */\n    color: #fff; /* default */\n    height: 100%;\n    position: relative;\n    width: 100%;\n  }\n  .pd_four-cta-lijdklaw img {\n    margin: 0 auto;\n    max-width: 100%;\n  }\n  .pd_four-cta-lijdklaw .pd_four-cta--cta-container {\n    bottom: 4%;\n    box-sizing: border-box;\n    display: flex;\n    flex-flow: row nowrap;\n    padding: 0 3%;\n    position: absolute;\n    width: 100%;\n    flex-direction: column;\n  }\n  \n  .pd_four-cta-lijdklaw .pd_four-cta--cta-subcontainer {\n    display: flex;\n    width: 100%;\n  }\n  \n  .pd_four-cta-lijdklaw .pd_four-cta--cta-subcontainer:not(:first-child) {\n    margin-top: 3%;\n  }\n  \n  .pd_four-cta-lijdklaw .pd_four-cta_button {\n    border: #fff solid 1px;\n    box-sizing: border-box;\n    color: #fff;\n    font-size: 12px;\n    font-weight: 600;\n    min-height: 24px;\n    padding: 6px 8px;\n    text-align: center;\n    text-transform: uppercase;\n    width: 48.5%;\n  }\n  .pd_four-cta-lijdklaw .pd_four-cta_button:not(:first-child) {\n    margin-left: 3%;\n  }\n  </style>\n  \n  \n  <div class=\"pd_four-cta-lijdklaw\">\n    <!-- <a href=\"\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\"> -->\n      <img id=\"PDImageTag\" src=\"/Asset_Archive/GFWeb/content/0030/014/009/assets/061723_SummerCyberOLAE60O_PromoDrawer_2_7116_.png\" alt=\"60 off\">\n<!-- </a> -->\n    <div class=\"pd_four-cta--cta-container\">\n      <div class=\"pd_four-cta--cta-subcontainer\">\n        <a href=\"/browse/category.do?cid=1092843#pageId=0&department=136&mlink=1037345,********,PD_TILE2\" class=\"pd_four-cta_button\">Women</a>\n        <a href=\"/browse/category.do?cid=1092773#pageId=0&department=75&mlink=1037345,********,PD_TILE2\" class=\"pd_four-cta_button\">Men</a>\n      </div>\n      <div class=\"pd_four-cta--cta-subcontainer\">\n        <a href=\"/browse/category.do?cid=1092850#pageId=0&department=48&mlink=1037345,********,PD_TILE2\" class=\"pd_four-cta_button\">GIRLS</a>\n        <a href=\"/browse/category.do?cid=1092793#pageId=0&department=16&mlink=1037345,********,PD_TILE2\" class=\"pd_four-cta_button\">BOYS</a>\n      </div>\n    </div>\n  </div>\n  ", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "excludes licensed and special edition styles", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "973277", "genericCode": ""}, "promoId": "li3gl54w"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1135847&mlink=1037345,********,PD_TILE3\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GFWeb/content/0030/014/009/assets/061723_SummerCyberOLAE60O_PromoDrawer_4_7165_.png\" alt=\"extra 60 off clearance\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "discount automatically applied at checkout", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "973357", "genericCode": ""}, "promoId": "li3gqapu"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/my-account/sign-in?creditOffer=barclays&sitecode=GPFSPD&mlink=1037345,********,PD_TILE_GGR_CARD_ACQ\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/GFWeb/content/0030/014/009/assets/061723_SummerCyberOLAE60O_PromoDrawer_3_7117_.png\" alt=\"extra 20 off acquisition\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile4"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "combinable with other offers", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "880925", "genericCode": ""}, "promoId": "li3god3l"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "style": {"mobile": {"fontSize": ".8em"}}, "text": "{--! headerText !--}"}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}"}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "iconUrl": "/Asset_Archive/GPWeb/content/promo_drawer/assets/minus-white.svg", "iconAltText": "Open icon", "linkWithModalDisplayStyle": "none", "subHeaderText": "(4 available)"}, "closedState": {"headerText": "summer cyber sale: 60% off sitewide", "subHeaderText": "+ extra 60% off clearance", "iconUrl": "/Asset_Archive/GPWeb/content/promo_drawer/assets/plus-white.svg", "iconAltText": "Closed icon"}, "aria-label": "summer cyber sale: 60% off sitewide"}, "pd_id": "pdid_1534541049574", "analytics": {"onOpen": {"content_id": "promo_drawer_open_content", "link_name": "promo_drawer_open_link", "promo_drawer_autofire_config": "scroll"}}}}, "utilitylinks": {"type": "sitewide", "name": "UtilityLinks", "data": {"style": {"fontSize": "10.5px"}, "brandBarShortcutLinks": [{"link": "/stores?sitecode=GPFSSEARCH&mlink=1037059,29683115,SEARCHBAR_STORELOCATOR", "text": "Find a store"}, {"link": "/customerService/info.do?cid=1099133&sitecode=GPFSSEARCH&mlink=1037059,29683115,SEARCHBAR_GGR_ACQ", "text": "Gap Good Rewards"}, {"link": "/customerService/info.do?cid=1067353&sitecode=GPFSSEARCH&mlink=1037059,29683115,SEARCHBAR_GIFTCARD", "text": "Gift Card"}]}}, "hamnav": {"type": "sitewide", "name": "HamburgerNav", "data": {"activeDivisions": ["1137421", "1040941", "1040942", "1040943", "1079661", "1091867", "1040944", "cGap5058", {"cid": "1150511", "customStyles": {"button": {"div": {"color": "#D00000"}}}}], "exclusionIds": ["1170807"]}}, "search": {"type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sweaters", "<PERSON><PERSON>", "Gap Logo Hoodie", "Dresses"]}}, "logo": {"name": "Logo", "type": "sitewide", "altText": "Gap Factory Store logo", "lightLogoImgPath": "/Asset_Archive/GFWeb/content/0029/695/174/assets/GF_LOGO_WHITE.svg", "darkLogoImgPath": "/Asset_Archive/GFWeb/content/0029/695/174/assets/GF_LOGO_BLK.svg", "logoImgPath": "/Asset_Archive/GFWeb/content/0029/695/174/assets/GF_LOGO_BLK.svg", "isSquare": true, "className": ""}}, "brand": "gapfs", "type": "meta", "pmcsEdgeCacheTag": "gapfs-homepage-en-us-stage"}