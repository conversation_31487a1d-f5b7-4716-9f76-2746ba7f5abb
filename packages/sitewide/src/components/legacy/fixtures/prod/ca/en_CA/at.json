{"meta.description": "pageDescription defaulted", "meta.title.overide": "pageTitle defaulted", "home": {"type": "home", "name": "HomeMultiSimple", "components": [{"name": "LayoutComponent", "type": "sitewide", "desc": "2023_HOL1_MobilePills_US", "style": {"backgroundColor": ""}, "data": {"defaultHeight": "0px", "lazy": true, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "100px"}, "mobile": {"height": "100px"}}, "style": {"backgroundColor": ""}, "mobile": {"shouldDisplay": true, "data": {"lazy": true, "style": {"display": "block", "overflow-x": "scroll", "overflow-y": "hidden"}, "tileStyle": {"mobile": {"position": "relative"}}, "components": [{"type": "sitewide", "name": "LayeredContentModule", "desc": "Mobile Pills", "style": {"overflow-x": "scroll", "overflow-y": "hidden"}, "tileStyle": {"mobile": {"position": "relative"}}, "data": {"container": {"style": {"width": "100%"}}, "background": {"className": "", "style": {"width": "100%"}, "desktopStyle": {}, "image": {"alt": "", "srcUrl": "", "desktopSrcUrl": ""}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"position": "relative", "top": "0%", "margin-left": "7px", "margin-right": "12px", "height": "100%", "width": "100%", "display": "flex", "overflow": "auto-hidden", "flex-wrap": "nowrap", "& a": {"color": "rgb(51, 51, 51)", "background": "rgb(242, 242, 242)", "userSelect": "none", "padding": "0.625rem", "height": "33px", "border-radius": "1rem", "margin": "0px 5px 0.75rem 5px", "font-family": "Avenir Next", "font-weight": "400", "line-height": "0.75rem", "font-size": "0.8125rem", "outline": "none", "white-space": "nowrap"}}, "desktopStyle": {}, "className": "", "ctas": [{"composableButtonData": {"children": "New Arrivals", "alt": "New Arrivals", "style": {}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1006482&tlink=pill_home_AllNewArrivals"}}, {"composableButtonData": {"children": "Bottoms", "alt": "Bottoms", "style": {}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1025878&tlink=pill_home_AllBottoms"}}, {"composableButtonData": {"children": "Tops", "alt": "Tops", "style": {}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1032080&tlink=pill_home_AllTops"}}, {"composableButtonData": {"children": "Tights & Leggings", "alt": "Tights & Leggings", "style": {}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1059481&tlink=pill_home_TightsLeggings"}}, {"composableButtonData": {"children": "Girl", "alt": "Girl", "style": {}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1067955&tlink=pill_home_Girl"}}, {"composableButtonData": {"children": "Jackets", "alt": "Jackets", "style": {}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1017102&tlink=pill_home_AllJackets"}}, {"composableButtonData": {"children": "Sweatshirts", "alt": "Sweatshirts", "style": {}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1005761&tlink=pill_home_Sweatshirts"}}, {"composableButtonData": {"children": "Gift Shop", "alt": "Gift Shop", "style": {}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1144086&tlink=pill_home_GiftShop"}}, {"composableButtonData": {"children": "Cozy Shop", "alt": "Cozy Shop", "style": {}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1165622&tlink=pill_home_CozyShop"}}, {"composableButtonData": {"children": "<PERSON><PERSON><PERSON>", "alt": "<PERSON><PERSON><PERSON>", "style": {}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1046322&tlink=pill_home_Joggers"}}, {"composableButtonData": {"children": "Matching Sets", "alt": "Matching Sets", "style": {}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1134973&tlink=pill_home_MatchingSets"}}, {"composableButtonData": {"children": "Sale", "alt": "Sale", "style": {}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1023728&tlink=pill_home_AllSale"}}]}}}]}}}}, {"_meta": {"name": "2023_HOL1_F2_HP_Spotlight_CWT_S", "schema": "https://cms.gap.com/schema/content/v1/spotlight-video.json", "deliveryId": "e9333135-4d68-4cdf-928a-7101d36cdb21"}, "general": {"layout": "inset", "background": {"type": "image", "images": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "3c815339-c83d-4b30-9424-20531cb24a59", "name": "2023_Hol1_Flip1_HP_Secondary_Elements_Background_NoCTA@2x_1", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}, "showHideBasedOnScreenSize": "hideOnDesktop"}, "content": {"contentJustification": "center", "verticalAlignment": "middle", "icon": {"iconSize": "24px"}, "mobileContentJustification": "center", "mobileVerticalAlignment": "middle", "mobileIcon": {"iconSize": "14px"}, "spotlightText": {"useGradientBackfill": false, "defaultText": "<hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" />"}, "ctaButtons": [{"buttonStyle": {"buttonStyle": "underline", "buttonColor": "light"}, "cta": {"label": "Shop New Arrivals", "value": "https://athleta.gap.com/browse/category.do?cid=1006482&mlink=1,1,HP_Spotlight_CTA1"}}]}, "video": {"vimeoVideo": {"desktop": {"fallbackImage": [{"altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "url": "https://player.vimeo.com/video/871989047?badge=0&amp;autopause=0&amp;player_id=0&amp;app_id=58479"}, "mobile": {"url": "https://player.vimeo.com/video/871989047?h=2a2b728578&amp;badge=0&amp&controls=1&autoplay=1&loop=1&autopause=0&muted=1&playsinline=1", "fallbackImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "7c6a5ac7-3ed1-4d5e-bf34-5e69448e3139", "name": "2023_Hol1_Flip1_HP_Secondary_Elements_Video_Fallback_Image_S@2x", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}}}, "videoOverlays": {"useGradientBackfill": false}}, {"_meta": {"name": "2023_HOL1_F2_HP_Spotlight_CWT_XL", "schema": "https://cms.gap.com/schema/content/v1/spotlight-video.json", "deliveryId": "41d2686e-5111-4895-a3fb-5f4f99d74090"}, "general": {"layout": "fullBleed", "background": {"type": "solid", "color": "#FFFFFF"}, "showHideBasedOnScreenSize": "hideOnMobile"}, "content": {"contentJustification": "left", "verticalAlignment": "bottom", "icon": {"iconSize": "24px"}, "mobileContentJustification": "center", "mobileVerticalAlignment": "middle", "mobileIcon": {"iconSize": "14px"}, "spotlightText": {"useGradientBackfill": false}, "ctaButtons": [{"buttonStyle": {"buttonStyle": "underline", "buttonColor": "light"}, "cta": {"label": "Shop New Arrivals", "value": "/browse/category.do?cid=1006482&mlink=1,1,HP_Spotlight_CTA1"}}]}, "video": {"vimeoVideo": {"desktop": {"fallbackImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "994dd08b-5fc4-4513-8705-bfe92cfd7f1b", "name": "2023_Hol1_Flip1_HP_Secondary_Elements_Video_Fallback_Image_XL@2x", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "url": "https://player.vimeo.com/video/872640139?h=42b38d486c&amp;badge=0&amp&controls=1&autoplay=1&loop=1&autopause=0&muted=1&playsinline=1"}, "mobile": {"url": "https://player.vimeo.com/video/861363668?h=2a2b728578&amp;badge=0&amp&controls=1&autoplay=1&loop=1&autopause=0&muted=1&playsinline=1"}}}, "videoOverlays": {"useGradientBackfill": false}}, {"_meta": {"name": "2023_HOL1_F2_HP_TopCats", "schema": "https://cms.gap.com/schema/content/v1/wayfinding-and-product-cards.json", "deliveryId": "f6176a49-f3b6-49d6-97ea-e16e3598bf00"}, "general": {"background": {"type": "solid", "color": "#FFFFFF"}, "mobileLayout": "exposed", "showHideBasedOnScreenSize": "alwaysShow", "categoryButtonColor": "dark"}, "content": {"categories": [{"image": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "7e93de11-682c-44b3-8ee6-ca27f66e8884", "name": "2023_Hol1_Flip1_HP_Wayfinding_Bottoms@2x", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Bottoms", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "cta": {"label": "Bottoms", "value": "/browse/category.do?cid=1025878&mlink=1,1,HP_Top_Cats_1"}, "url": {"value": "/browse/category.do?cid=1025878&mlink=1,1,HP_Top_Cats_1", "label": "Shop women's bottoms"}}, {"image": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "c0e26b14-3c63-4fbd-bfa4-eb5c5bae3447", "name": "2023_Hol1_Flip1_HP_Wayfinding_Tops@2x", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Tops", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "cta": {"label": "Tops", "value": "/browse/category.do?cid=1032080&mlink=1,1,HP_Top_Cats_2"}, "url": {"value": "/browse/category.do?cid=1032080&mlink=1,1,HP_Top_Cats_2", "label": "Shop women's tops"}}, {"image": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "86a88fd6-c19b-44f1-bd1c-c306a54bd8d0", "name": "2023_Hol1_Flip1_HP_Wayfinding_Jackets@2x", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Jackets", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "cta": {"value": "/browse/category.do?cid=1017102&mlink=1,1,HP_Top_Cats_3", "label": "Jackets"}, "url": {"label": "Shop women's jackets", "value": "/browse/category.do?cid=1017102&mlink=1,1,HP_Top_Cats_3"}}, {"image": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "01788475-9d23-485d-9f3d-1d0dfdf199b8", "name": "2023_Hol1_Flip1_HP_Wayfinding_Gifts@2x", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Girl", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "cta": {"label": "The Gift Shop", "value": "/browse/category.do?cid=1144086&mlink=1,1,HP_Top_Cats_4"}, "url": {"value": "/browse/category.do?cid=1144086&mlink=1,1,HP_Top_Cats_4", "label": "The Gift Shop"}}], "carouselSettings": {"transition": "slide", "type": "clickThrough", "continuousLoop": false, "autoplay": {"delay": 3000, "pauseOnHover": false}, "animation": {"speed": 500, "ease": false}, "styling": {"controlsIconsColor": "primary", "pagination": "hide", "hideChevrons": false}}}}, {"_meta": {"name": "2023_HOL1_F2_HP_Secondary_RainierJogger", "schema": "https://cms.gap.com/schema/content/v1/spotlight.json", "deliveryId": "dcb50295-49fa-4aaa-b308-2d34d92e68be"}, "general": {"layout": "fullBleed", "background": {"type": "image", "images": [{"variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}, "showHideBasedOnScreenSize": "alwaysShow"}, "content": {"contentJustification": "left", "verticalAlignment": "middle", "icon": {"iconSize": "24px"}, "mobileContentJustification": "center", "mobileVerticalAlignment": "top", "mobileIcon": {"iconSize": "14px"}, "spotlightText": {"useGradientBackfill": false, "defaultText": "<hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" />", "mobileOverride": "<hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" />"}, "ctaButtons": [{"buttonStyle": {"buttonStyle": "underline", "buttonColor": "light"}, "cta": {"label": "Shop Cold Weather Train", "value": "/browse/category.do?cid=46711&mlink=1,1,HP_Secondary_CTA1"}}]}, "image": {"mainImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "d5cd2fde-9d70-4532-8979-8d0e62b67480", "name": "2023_Hol1_Flip2_HP_Secondary_CWT_XL@2x_1", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "mobileImageOverride": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "29ed6da8-0771-4813-a7a4-e3e8b3bc8fb9", "name": "2023_Hol1_Flip2_HP_Secondary_CWT_S@2x_1", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}, "imageOverlays": {"handle": {"placement": "left"}, "useGradientBackfill": false}}, {"name": "LayoutComponent", "type": "sitewide", "desc": "certona feed Desktop and mobile full bleed", "experimentRunning": true, "instanceName": "ATUS_POD_1EF6_HP", "data": {"lazy": true, "defaultHeight": {"large": "300px", "small": "300px"}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0px auto", "marginTop": "-25px"}, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>\n.fullBleedCertona1_rr .slick-list img {\n width:100%; padding: 0 5px; \n}\n .fullBleedCertona1_rr .mkt-certona-recs {\n  max-width: none; \n} .fullBleedCertona1_rr .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon {\n  max-width: none; \n} .fullBleedCertona1_rr .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon .mkt-certona-recs__products {\n  max-width: none; \n} .fullBleedCertona1_rr .slick-next, .fullBleedCertona1_rr .slick-prev {\n padding:0;  width:50px; max-width: 50px!important;transform:none!important;margin-top:auto; padding-bottom:5%; \n}  \n .fullBleedCertona1_rr {\n width:100%;background-image:url('https://athletaprod.a.bigcontent.io/v1/static/FA23_DROP1_FLIP1_HP__Certona_XL'); background-size:cover; padding: 2em 0;\n} \n</style>", "style": {}, "classes": ""}}, {"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"lazy": true, "props": {"style": {"width": "100%"}, "className": "fullBleedCertona1_rr"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%", "paddingLeft": "20px"}}, "data": {"customBrand": "AT", "source": "c<PERSON>a", "scheme": "athome1_rr", "displayTitle": true, "fullWidth": true, "certonaTitle": {"title": "", "style": {"desktop": {"display": "flex", "padding": "1em 0.2em", "justifyContent": "flex-start", "alignItems": "flex-start", "fontSize": "50px", "fontWeight": "500", "letterSpacing": "1px", "fontFamily": "'Phantom',Avenir Next,Gap Sans,Helvetica,Arial,Roboto,sans-serif", "textTransform": "uppercase", "-webkitFontSmoothing": "antialiased", "color": "#000"}, "mobile": {}}}, "layout": "carousel", "centerMode": false, "defaultslidesToShowSlick": 4, "defaultslidesToScrollSlick": 1, "displayPlayPauseButton": false, "resslidesToShowSlick": 4, "resslidesToScrollSlick": 1, "arrows": true, "autoplay": false, "autoplaySpeed": 2000, "speed": 500, "pauseOnHover": true, "infinite": true, "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "showMarketingFlag": true, "prevArrowSlick": "/Asset_Archive/ATWeb/content/0029/000/374/assets/Carousel_Arrow_Black_Left.svg", "nextArrowSlick": "/Asset_Archive/ATWeb/content/0029/000/374/assets/Carousel_Arrow_Black_Right.svg", "prevArrowAlt": "previous recommendation", "nextArrowAlt": "next recommendation", "productTextStyles": {"productTitle": {"style": {"text-align": "left", "fontWeight": "600", "marginTop": "20px", "color": "#000000", "lineHeight": "1.35", "paddingLeft": "15px"}}, "productPrice": {"style": {"display": "flex", "color": "#000000", "justifyContent": "flex-start", "textAlign": "left", "paddingLeft": "15px"}}, "productSalePrice": {"style": {"textAlign": "left", "display": "flex", "lineHeight": "2", "justifyContent": "flex-start", "color": "red", "paddingLeft": "15px"}}, "productMarketingFlag": {"style": {"color": "#000000", "textAlign": "left", "paddingLeft": "15px"}}}, "productCardStyles": {"style": {"boxSizing": "border-box", "width": "100%!important"}}, "productCardImageStyles": {}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "paddingTop": "0rem", "marginBottom": "0"}, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>\n.fullBleedCertona1_rr .slick-list img {\n width:100%; padding: 0 5px; \n} \n .fullBleedCertona1_rr {\n width:100%; background-image:url('https://athletaprod.a.bigcontent.io/v1/static/FA23_DROP1_FLIP1_HP__Certona_S'); padding:2em 0; background-size: cover; margin-top:-1vh; \n} \n .fullBleedCertona1_rr .productCard br{\n display:none;\n}  \n .fullBleedCertona1_rr .slick-next {\n display: block; right: 0; width: 100%; top: -5%; \n} \n .fullBleedCertona1_rr .slick-prev {\n padding:0;display: block; left: 0; width: 100%; top: -5%; \n} \n .fullBleedCertona1_rr .slick-next, .fullBleedCertona1_rr .slick-prev {\n padding:0; width:50px; max-width: 50px!important;transform:none!important;margin-top:auto; \n} \n .fullBleedCertona1_rr .slick-list {\n overflow:hidden;\n}</style>", "style": {}, "classes": ""}}, {"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"lazy": true, "props": {"style": {"width": "100%"}, "className": "fullBleedCertona1_rr"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px"}}, "data": {"customBrand": "AT", "source": "c<PERSON>a", "scheme": "athome1_rr", "displayTitle": true, "certonaTitle": {"title": "", "style": {"mobile": {"display": "flex", "padding": "0.5em 0.3em", "justifyContent": "flex-start", "alignItems": "flex-start", "fontSize": "34px", "maxWidth": "1400px", "fontFamily": "'Phantom',Avenir Next,Gap Sans,Helvetica,Arial,Roboto,sans-serif", "fontWeight": "500", "letterSpacing": "1px", "textTransform": "uppercase", "-webkitFontSmoothing": "antialiased", "paddingBottom": "1em", "color": "#000"}}}, "layout": "carousel", "useMobileConfig": true, "arrows": false, "defaultslidesToShowSlick": 1.33, "defaultslidesToScrollSlick": 1, "displayPlayPauseButton": false, "resslidesToShowSlick": 1, "resslidesToScrollSlick": 1, "autoplay": false, "autoplaySpeed": 2000, "speed": 500, "infinite": false, "showMarketingFlag": true, "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "prevArrowSlick": "/Asset_Archive/ATWeb/content/0029/000/374/assets/Carousel_Arrow_Black_Left.svg", "nextArrowSlick": "/Asset_Archive/ATWeb/content/0029/000/374/assets/Carousel_Arrow_Black_Right.svg", "productTextStyles": {"productTitle": {"style": {"fontSize": "1rem", "text-align": "flex-start", "fontWeight": "600", "marginTop": "20px", "color": "#000000", "lineHeight": "1.35"}}, "productMarketingFlag": {"style": {"color": "#000000", "fontSize": "1rem", "textAlign": "flex-start"}}, "productPrice": {"style": {"color": "#000000", "display": "flex", "justifyContent": "flex-start", "textAlign": "flex-start", "fontSize": "1rem"}}, "productSalePrice": {"style": {"textAlign": "flex-start", "display": "flex", "lineHeight": "2", "justifyContent": "flex-start", "fontSize": "1rem", "color": "red"}}}, "productCardStyles": {"style": {"boxSizing": "border-box", "width": "100%!important"}}, "gridLayout": {"style": {"desktop": {"display": "block"}, "mobile": {"display": "block"}}, "productsPerRow": {"desktop": 4, "mobile": 1}}}}]}}]}}}}, {"_meta": {"name": "2023_HOL1_F2_HP_Tertiary_TheWarmUp", "schema": "https://cms.gap.com/schema/content/v1/spotlight.json", "deliveryId": "a87f7a52-8b8f-45dc-a2cf-edeba3a7c769"}, "general": {"layout": "fullBleed", "background": {"type": "image", "images": [{"variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}, "showHideBasedOnScreenSize": "alwaysShow"}, "content": {"contentJustification": "left", "verticalAlignment": "bottom", "icon": {"iconSize": "24px"}, "mobileContentJustification": "center", "mobileVerticalAlignment": "bottom", "mobileIcon": {"iconSize": "14px"}, "spotlightText": {"useGradientBackfill": false, "mobileOverride": "<hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" />"}, "ctaButtons": [{"buttonStyle": {"buttonStyle": "underline", "buttonColor": "light"}, "cta": {"label": "Shop Outerwear", "value": "/browse/category.do?cid=1017102&mlink=1,1,HP_Tertiary_CTA1"}}]}, "image": {"mainImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "b5016869-7af3-402b-94cd-9b0371891752", "name": "2023_Hol1_Flip1_HP_Tertiary_WarmUp_XL@2x_1", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "mobileImageOverride": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "45d79068-c9d7-4e2b-80bc-a7a8208ded49", "name": "2023_Hol1_Flip1_HP_Tertiary_WarmUp_S@2x_1", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}, "imageOverlays": {"handle": {"placement": "left"}, "useGradientBackfill": false}}, {"_meta": {"name": "2023_HOL1_F2_HP_Spacer_1", "schema": "https://cms.gap.com/schema/content/v1/vertical-spacer.json", "deliveryId": "363f90c1-9340-49be-983a-2121974ab282"}, "background": {"backgroundColor": "#ffffff"}, "webAppearance": {"showHideBasedOnScreenSize": "alwaysShow", "size": "custom", "customSize": 30, "mobileOverrideCustomSize": 20, "mobileOverrideSize": "custom"}}, {"_meta": {"name": "2023_HOL1_F2_ATG_Spotlight", "schema": "https://cms.gap.com/schema/content/v1/spotlight.json", "deliveryId": "f7017ab4-76df-431e-8a19-f668c9426a70"}, "general": {"layout": "fullBleed", "background": {"type": "image", "images": [{"variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}, "mobileBackground": [{"mobileBackground": {"type": "image", "images": [{"variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}}], "showHideBasedOnScreenSize": "alwaysShow"}, "content": {"contentJustification": "left", "verticalAlignment": "bottom", "icon": {"iconSize": "24px"}, "mobileContentJustification": "left", "mobileVerticalAlignment": "middle", "mobileIcon": {"iconSize": "14px"}, "spotlightText": {"useGradientBackfill": false, "mobileOverride": "<hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" /><hr style=\"display:block;border:0;height:8px;margin:0;background:transparent;\" aria-hidden=\"true\" />"}, "ctaButtons": [{"buttonStyle": {"buttonStyle": "underline", "buttonColor": "light"}, "cta": {"label": "Shop Gifts For Girl", "value": "/browse/category.do?cid=1080259&mlink=1,1,HP_AG_Spotlight_CTA1"}}]}, "image": {"mainImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "1fe84fd7-231b-4cef-9aa2-0a606d879a88", "name": "2023_Hol1_Flip1_ATG_Spotlight_XL_rev@2x", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "mobileImageOverride": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "248a7675-b9a4-464b-be16-7bef233bad88", "name": "2023_Hol1_Flip1_ATG_Spotlight_S_rev@2x", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}, "imageOverlays": {"handle": {"placement": "left"}, "useGradientBackfill": false}, "instanceName": "2C2_HPG_Removal"}, {"_meta": {"name": "2023_HOL1_F2_ATG_TopCats", "schema": "https://cms.gap.com/schema/content/v1/wayfinding-and-product-cards.json", "deliveryId": "73063fc5-6d78-4c84-8a33-73dd780f4843"}, "general": {"background": {"type": "solid"}, "mobileLayout": "exposed", "showHideBasedOnScreenSize": "alwaysShow", "categoryButtonColor": "dark"}, "content": {"categories": [{"image": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "7bad3be0-fa27-4702-86e4-479493f7ed25", "name": "2023_Hol1_Flip1_ATG_Wayfinding_NA@2x", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "cta": {"label": "New Arrivals", "value": "/browse/category.do?cid=1054844&mlink=1,1,G_HP_Wayfinding_1"}, "url": {"label": "New Arrivals", "value": "/browse/category.do?cid=1054844&mlink=1,1,G_HP_Wayfinding_1"}}, {"image": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "32ec54fe-c481-4f8f-b9c6-a3bad71146df", "name": "2023_Hol1_Flip1_ATG_Wayfinding_Tights@2x", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "cta": {"label": "Tights & Leggings", "value": "/browse/category.do?cid=1054835&mlink1,1,G_HP_Wayfinding_2"}, "url": {"value": "/browse/category.do?cid=1054835&mlink1,1,G_HP_Wayfinding_2", "label": "Tights & Leggings"}}, {"image": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "889a0425-3ab7-4293-9995-8c6965f95c68", "name": "2023_Hol1_Flip1_ATG_Wayfinding_Tops2@2x", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "cta": {"label": "Tops", "value": "/browse/category.do?cid=1055327&mlink=1,1,G_HP_Wayfinding_3"}, "url": {"value": "/browse/category.do?cid=1055327&mlink=1,1,G_HP_Wayfinding_3", "label": "Tops"}}, {"image": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "1b71b4a3-d1cf-4042-bc4b-4ac96c9bc58c", "name": "2023_Hol1_Flip1_ATG_Wayfinding_Jackets@2x", "endpoint": "athletaprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}], "cta": {"label": "Jackets", "value": "/browse/category.do?cid=1145147&mlink=1,1,G_HP_Wayfinding_4"}, "url": {"label": "Jackets", "value": "/browse/category.do?cid=1145147&mlink=1,1,G_HP_Wayfinding_4"}}], "carouselSettings": {"transition": "slide", "type": "clickThrough", "continuousLoop": false, "autoplay": {"delay": 3000, "pauseOnHover": false}, "animation": {"speed": 500, "ease": false}, "styling": {"controlsIconsColor": "primary", "pagination": "hide", "hideChevrons": false}}}}]}, "sitewide": {"desktopemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "sitewide", "name": "OptimizelyPlaceholder", "instanceName": "dpg_emergency_banner_desk_en", "experimentRunning": true, "data": {"shouldWaitForOptimizely": true, "defaultHeight": {"large": "0", "small": "0"}}}]}}, "mobileemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "sitewide", "name": "OptimizelyPlaceholder", "instanceName": "dpg_emergency_banner_mob_en", "experimentRunning": true, "data": {"shouldWaitForOptimizely": true, "defaultHeight": {"large": "0", "small": "0"}}}]}}, "below-topnav": {"type": "builtin", "name": "div", "data": {"components": [{"type": "sitewide", "name": "OptimizelyPlaceholder", "instanceName": "below-topnav", "experimentRunning": true, "data": {"meta": {"includePageTypes": ["product"]}, "defaultHeight": {"large": "0", "small": "0"}}}]}}, "edfslarge": {"type": "builtin", "name": "div", "data": {"components": [{"_meta": {"name": "EDFS", "schema": "https://cms.gap.com/schema/content/v1/edfs.json", "deliveryId": "10444d6e-3756-45e2-8e53-5fdc537a063c"}, "showSignInLink": true, "mobileFrames": [{"rte": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--eyebrow-3\" style=\"font-weight:400\">REWARDS MEMBERS GET </span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--eyebrow-3\" style=\"font-weight:400\">ACCESS TO FREE SHIPPING</span></p>", "htmlModalUrl": "/customerService/info.do?cid=1197295", "detailsLink": "DETAILS"}], "mobileBackground": "#FFFFFF", "experimentRunning": false, "redpointExperimentRunning": false, "desktopRte": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-4\" style=\"font-weight:400\">REWARDS MEMBERS GET ACCESS TO FREE SHIPPING</span></p>", "desktopDetailsLink": "DETAILS", "desktopHtmlModalUrl": "/customerService/info.do?cid=1197295"}]}}, "edfssmall": {"type": "builtin", "name": "div", "data": {"components": [{"_meta": {"name": "EDFS", "schema": "https://cms.gap.com/schema/content/v1/edfs.json", "deliveryId": "10444d6e-3756-45e2-8e53-5fdc537a063c"}, "showSignInLink": true, "mobileFrames": [{"rte": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--eyebrow-3\" style=\"font-weight:400\">REWARDS MEMBERS GET </span></p><p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--eyebrow-3\" style=\"font-weight:400\">ACCESS TO FREE SHIPPING</span></p>", "htmlModalUrl": "/customerService/info.do?cid=1197295", "detailsLink": "DETAILS"}], "mobileBackground": "#FFFFFF", "experimentRunning": false, "redpointExperimentRunning": false, "desktopRte": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-4\" style=\"font-weight:400\">REWARDS MEMBERS GET ACCESS TO FREE SHIPPING</span></p>", "desktopDetailsLink": "DETAILS", "desktopHtmlModalUrl": "/customerService/info.do?cid=1197295"}]}}, "hamburgerNavBanner": {"type": "builtin", "name": "div", "data": {"components": [{"CIID": "28355864", "type": "builtin", "name": "div", "desc": "amplience CA EN hamnav top banner", "data": {"props": {"class": "hamNavMaskShop", "style": {"fontFamily": "Avenir Next,Gap Sans,Helvetica,Arial,Roboto,sans-serif", "display": "block", "background": "#0466CA", "fontSize": 0, "width": "100%"}}, "components": [{"type": "builtin", "name": "a", "data": {"props": {"href": "https://community.athletawell.com?utm_campaign=phase1&utm_medium=hamnav&utm_source=atol", "target": "_blank", "style": {"textDecoration": "none", "color": "transparent", "width": "100%"}}, "components": [{"type": "builtin", "name": "img", "data": {"props": {"style": {"display": "block"}, "src": "/Asset_Archive/ATWeb/content/0027/137/400/assets/AW_PHASE1_HAMNAV.jpeg", "alt": "Introducing Athl<PERSON>well. Join the conversation"}}}]}}]}}]}}, "hamnavRedesignBanner": {"type": "builtin", "name": "div", "data": {"components": []}}, "popup": {"type": "builtin", "name": "div", "data": {"components": [{"name": "DynamicModal", "type": "sitewide", "instanceName": "ATUS-email-popup", "experimentRunning": false, "data": {"meta": {"excludePageTypes": ["profile", "ShoppingBag", "Information", "LoyaltyValueCenter", "profile-ui"]}, "defaultHeight": {"small": "0px", "large": "0px"}, "shouldWaitForOptimizely": false, "closeButtonAriaLabel": "close email sign up modal", "localStorageKey": "emailPopup", "modalSize": "max", "title": "", "style": {"padding": "1rem"}, "breakpoints": ["large"], "layoutData": {"desktop": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0 1rem 2rem 1rem"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "row", "justifyContent": "space-between"}, "components": [{"name": "EmailRegistrationForm", "type": "sitewide", "tileStyle": {"desktop": {"width": "50%"}}, "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {"padding": "3rem 0 0 0", "margin": "0 1rem 0 0"}, "mobile": {"padding": "20px", "margin": "1rem"}}, "targetURL": "/profile/info.do?cid=82634&mlink=412523,18003840,emailconfirmationTest&clink=18003840", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:NA;ON:NA;PL:NA;AT:emailconfirmationTest;BRFS:NA;GPO:NA"]}, "customText": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "optly-header-container", "style": {}, "html": "<h1 class=\"optly-main-header optly-main-header\" style=\"font-size: 2.5rem;color: black;line-height: 1;font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;font-weight: 600;padding-right:40px;\">IT’S YOUR LUCKY DAY — 20% OFF*</h1>"}}, "subtitle": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "", "style": {}, "html": "<p class=\"optly-lighter\" style=\"font-weight:400;\">Here’s access to exclusive offers, new arrivals, private sales & free in-store fitness classes.</p>"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "", "style": {"marginTop": "2em"}, "html": "<div class=\"sds_color--g2\"><p class=\"sds_line-height--1-4 optly-lighter\" style=\"font-size:0.8em;font-weight:400;color:#666;\">Yes! I would like to receive style news and exclusive offers from Gap Inc.'s Canadian brands and related companies including Gap, Old Navy, Banana Republic, and Athleta.  <a href=\"/customerService/info.do?cid=1096615\" style=\"font-size:0.8em;text-decoration:underline;\"> Details</a> You can withdraw consent at any time. FOR MORE DETAILS SEE OUR <a href=\"https://www.gapinc.com/en-ca/consumer-privacy-policy\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: inline-block; line-height: 1.3; text-decoration: underline; font-size:0.8em;\">PRIVACY POLICY</a> OR  <a href=\"/customerService/info.do?cid=44959\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: inline-block; line-height: 1.3; text-decoration: underline; font-size:0.8em\">CONTACT US</a></p></div>"}}}, "textInputOptions": {"label": "Enter Your Email Address", "errorMessage": "Please enter a valid email address", "desktop": {"className": "", "crossBrand": false, "inverse": false}, "mobile": {"className": "", "crossBrand": false, "inverse": false}}, "submitButtonOptions": {"text": "Claim your unique code", "desktop": {"className": "", "variant": "solid", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "black", "style": {"marginTop": "20px"}}, "mobile": {"className": "", "variant": "solid", "size": "small", "fullWidth": true, "crossBrand": false, "color": "primary"}}, "errorNotificationAriaLabel": "Error"}}, {"type": "builtin", "name": "img", "data": {"props": {"src": "/Asset_Archive/ATWeb/content/0018/870/596/assets/EmailPopUp_FINAL.jpg", "alt": "Woman hiking in Athleta gear"}}}]}}}}]}}, "mobile": {"shouldDisplay": false, "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0 1rem 2rem 1rem"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "row", "justifyContent": "space-between"}, "components": [{"name": "EmailRegistrationForm", "type": "sitewide", "tileStyle": {"desktop": {"width": "50%"}}, "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {}, "mobile": {}}, "targetURL": "/profile/info.do?cid=82634&mlink=412523,18003840,emailconfirmationTest&clink=18003840", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:NA;ON:NA;PL:NA;AT:emailconfirmationTest;BRFS:NA;GPO:NA"]}, "customText": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "optly-header-container", "style": {}, "html": "<h1 class=\"optly-main-header optly-main-header\" style=\"font-size: 2.4rem;color: black;line-height: 1;font-family: sofia-webfont, Helvetica, Arial, Roboto, sans-serif;font-weight: 600;\">IT’S YOUR LUCKY DAY — 20% OFF*</h1>"}}, "subtitle": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "", "style": {}, "html": "<h3 class=\"optly-lighter\" style=\"font-weight:400;\">Here’s access to exclusive offers, new arrivals, private sales & free in-store fitness classes.</h3>"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"classes": "", "style": {"marginTop": "8em"}, "html": "<a href=\"https://oldnavy.gap.com/customerService/info.do?cid=3318\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: block; line-height: 1.3; text-decoration: underline; font-size: 0.8em;\">*TERMS AND CONDITIONS</a>"}}}, "textInputOptions": {"label": "Enter Your Email Address", "errorMessage": "Please enter a valid email address", "desktop": {"className": "", "crossBrand": false, "inverse": false}, "mobile": {"className": "", "crossBrand": false, "inverse": false}}, "submitButtonOptions": {"text": "Claim your unique code", "desktop": {"className": "", "variant": "solid", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "black", "style": {"marginTop": "20px"}}, "mobile": {"className": "", "variant": "solid", "size": "small", "fullWidth": true, "crossBrand": false, "color": "black"}}, "errorNotificationAriaLabel": "Error"}}]}}}}]}}}, "analytics": {"on_close": {"tracking_enabled": true, "content_id": "email_popup_close", "link_name": "email_popup_close"}, "on_submit": {"tracking_enabled": true, "content_id": "email_popup_submit", "link_name": "email_popup_submit"}}, "displayOptions": {"pageVisit": 2, "localStorageKey": "ATOL_hasSeenEmailAcquisitionNew"}}}]}}, "countdown": {"type": "builtin", "name": "div", "data": {"components": []}}, "footer": {"type": "sitewide", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "footer-ciid": "28893472", "desc": "20231024_Footer_CDA-EN", "components": [{"name": "Footer", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "287px", "emailRegistration": {"detailsModal": {"closeTermsAndConditionsButtonAriaLabel": "Close"}, "title": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "athleta", "locale": "en_US", "data": {"classes": "", "style": {"paddingBottom": "0"}, "html": "<style>.card_links{display:none;}.email-registration__input-wrapper{visibility: unset;}#sitewide-footer{position:relative;}.email-registration__form-email, .email-registration__form-submit-button-container button#submit-button, .email-registration__form-submit-button-container button.email-registration__form-submit-button {display:none} .atol-footer .email-registration__subtitle{padding-bottom:0}</style>Get 20% Off"}}, "subtitle": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "athleta", "locale": "en_US", "data": {"classes": "", "style": {"paddingBottom": "0"}, "html": "when you sign up for our emails<br><a href='/profile/info.do?cid=54069&mlink=55287,18801841,email_v&clink=18801841' target='_blank'><button class='email-registration__form-submit-button' style='background: black;COLOR: white;margin: 1em 0;padding: .3rem 2rem;font-size: .9375rem;text-transform: uppercase;font-family: Phantom,Gap Sans,Helvetica,Arial,Roboto,sans-serif;font-weight: 600;border:none;'>Join Here</button></a>"}}, "submitButtonText": "Join Here", "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "athleta", "locale": "en_US", "data": {"classes": "", "style": {}, "html": "<a style='text-decoration: underline;' href='https://www.gapinc.com/en-ca/consumer-privacy-policy' target='_blank'>Privacy Policy </a>"}}}, "carousel": {"slides": [{"type": "sitewide", "name": "LayoutComponent", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"position": "relative", "display": "block", "margin": "0px 1vw", "marginBottom": "1vw"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "container": {"style": {"position": "relative"}}, "background": {"image": {"alt": "Athleta Rewards. Earn points on every purchase", "desktopSrcUrl": "https://athletaprod.a.bigcontent.io/v1/static/XL_Global_Footer_(Card+MTL)_Retention_2"}, "style": {}, "desktopStyle": {}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"position": "absolute", "bottom": 0, "height": "60%", "width": "100%", "display": "flex", "alignItems": "center", "flex-direction": "row"}, "desktopStyle": {"height": "40%", "alignItems": "flex-start"}, "ctas": [{"composableButtonData": {"children": "Learn More", "style": {"color": "transparent", "height": "100%", "backgroundColor": "transparent", "width": "50%!important", "padding": 0}, "desktopStyle": {"position": "relative", "height": "100%", "width": "50%!important"}}, "linkData": {"target": "_self", "to": "/browse/info.do?cid=1184799&mlink=1,1,GBFooter_LearnMore,1", "title": "apply now"}}, {"composableButtonData": {"children": "Shop Now", "style": {"color": "transparent", "backgroundColor": "transparent", "width": "50%!important", "height": "100%", "padding": 0}, "desktopStyle": {"position": "relative", "width": "50%!important", "height": "100%", "padding": 0}}, "linkData": {"target": "_self", "to": "/browse/category.do?cid=1006482&mlink=1,1,GBFooter_ShopNow,15", "title": "learn more"}}]}}}]}}}}]}, "marketingBannerLayout": {"name": "LayoutComponent", "type": "sitewide", "tileStyle": {"mobile": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "2px 0", "margin": "0 auto", "maxWidth": "640px", "width": "100%"}, "desktop": {"borderColor": "#2b2b2b", "borderStyle": "solid", "borderWidth": "0 0 2px", "margin": "0 auto", "maxWidth": "1920px", "width": "100%"}}, "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "div", "type": "builtin", "tileStyle": {"mobile": {"maxWidth": "100%"}, "desktop": {"maxWidth": "100%"}}, "data": {"style": {"alignItems": "center", "color": "#2b2b2b", "display": "none", "flexWrap": "wrap", "fontSize": "min(max(18px, calc(1.125rem + ((1vw - 3.75px) * 0.7653))), 21px)", "lineHeight": "1", "min-height": "0vw", "padding": "16px"}, "desktopStyle": {"display": "flex", "fontSize": "min(max(16px, calc(1rem + ((1vw - 7.68px) * 0.6076))), 23px)", "justifyContent": "space-between"}, "components": [{"instanceDesc": "Left Side", "name": "div", "type": "builtin", "data": {"style": {"marginBottom": "4px", "textTransform": "uppercase", "fontWeight": "700"}, "desktopStyle": {"lineHeight": "1.125", "marginBottom": "0", "min-width": "240px"}, "components": ["4 Brands 1 Easy Checkout"]}}, {"instanceDesc": "Right Side", "name": "div", "type": "builtin", "data": {"style": {"display": "contents", "flexWrap": "wrap", "lineHeight": "1.125", "marginTop": "10px"}, "desktopStyle": {"justifyContent": "flex-end", "marginTop": "0", "maxWidth": "70%"}, "components": [{"name": "p", "type": "builtin", "data": {"style": {"whiteSpace": "nowrap", "color": "#2b2b2b", "textTransform": "uppercase", "fontWeight": "600", "fontSize": "16px"}, "components": [{"name": "p", "type": "builtin", "data": {"style": {"color": "#2b2b2b", "textTransform": "uppercase"}, "components": ["Rewards Members Get Access To Free Shipping "]}}]}}, {"name": "p", "type": "builtin", "data": {"style": {"whiteSpace": "nowrap", "fontSize": "16px", "color": "#2b2b2b"}, "desktopStyle": {"marginLeft": "5px", "fontSize": "16px", "color": "#2b2b2b", "display": "contents"}, "components": [{"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in"}, "style": {"textDecoration": "underline !important", "textTransform": "uppercase", "fontWeight": "700"}, "components": ["Sign In"]}}, {"name": "p", "type": "builtin", "data": {"style": {"textDecoration": "none", "fontWeight": "600"}, "components": [" OR "]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in"}, "style": {"textDecoration": "underline !important", "textTransform": "uppercase", "fontWeight": "700"}, "components": ["Join"]}}]}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"marginLeft": "auto"}, "desktopStyle": {"marginLeft": "10px"}}, "ctaList": {"mobilePositionAboveContent": false, "ctas": [{"composableButtonData": {"children": "Details", "font": "primary", "style": {"backgroundColor": "transparent", "borderWidth": "0", "color": "#2b2b2b", "fontSize": "10px", "fontWeight": "700", "lineHeight": "1", "letterSpacing": "0", "padding": "4px", "textDecoration": "underline", "textTransform": "uppercase", "&:hover": {"color": "#333"}}, "desktopStyle": {"paddingRight": "0"}}, "modalData": {"closeButtonAriaLabel": "Close Pop-up", "modalSize": "max", "iframeData": {"title": "SHIPPING & RETURNS", "src": "/browse/info.do?cid=1197295", "tid": "FooterEDFS_Details", "height": "500px"}}}]}}}]}}]}}]}}}}, "footerCustomerSupport": {"desktop": {"columns": [{"header": {"text": "Customer Support", "className": "", "link": "optional"}, "links": [{"type": "link", "text": "Customer Service", "to": "/customerService/info.do?cid=44959", "className": "atfLinks", "style": {}}, {"type": "link", "text": "Shipping", "to": "/customerService/info.do?cid=1180468", "className": "atfLinks", "style": {}}, {"type": "link", "text": "Free Returns", "to": "/customerService/info.do?cid=1180471", "className": "atfLinks", "style": {}}, {"type": "link", "text": "Track Your Order", "to": "/my-account/order-lookup", "className": "atfLinks", "style": {}}, {"type": "link", "text": "Gift Cards", "to": "/browse/info.do?cid=52564&mlink=55287,********,Footer_GiftCards&clink=********", "className": "atfLinks", "style": {}}, {"type": "link", "text": "Size & Fit Guides", "to": "/customerService/info.do?cid=79315", "className": "atfLinks", "style": {}}, {"type": "link", "text": "Site Map", "to": "/products/index.jsp", "className": "atfLinks", "style": {}}]}, {"header": {"text": "About Us", "className": "", "link": "optional"}, "links": [{"type": "link", "text": "Our Values", "to": "/browse/info.do?cid=1074427&mlink=55287,********,Footer_OurValues&clink=********", "className": "atfLinks", "style": {}}, {"type": "link", "text": "People & Planet", "to": "/browse/info.do?cid=1191658&mlink=55287,********,Footer_0405_Sustainability&clink=********#BCorp", "className": "atfLinks", "style": {}}, {"type": "link", "text": "Work at Athleta", "to": "https://www.gapinc.com/en-ca/careers/job-search?brand=Athleta&currentPage=1", "className": "atfLinks", "style": {}}, {"type": "link", "text": "Gap Inc. Sustainability", "to": "https://www.gapinc.com/en-ca/values/sustainability", "className": "atfLinks", "style": {}}]}, {"header": {"text": "Find Us", "className": ""}, "links": [{"type": "text", "text": "877-328-4538", "className": "", "style": {"font-weight": "700", "color": "#76787b"}}, {"type": "link", "text": "Find a Store", "to": "/stores?mlink=55287,********,Footer_StoreLocator&clink=********", "className": "atfLinks", "style": {}}, {"type": "link", "text": "Email Sign-Up", "to": "/profile/info.do?cid=57480&mlink=55287,********,Footer_Email&clink=********", "className": "atfLinks", "style": {}}]}, {"header": {"img": "/Asset_Archive/ATWeb/content/0020/704/646/assets/ATHLETA REWARDS_logo_CDA.svg", "alt": "Athleta Rewards", "target": "_blank", "style": {}}, "links": [{"type": "link", "img": "/Asset_Archive/ATWeb/content/0020/704/646/assets/ATHLETA REWARDS_logo_CDA.svg", "alt": "Athleta Rewards", "target": "_blank", "style": {"pointerEvents": "none"}}, {"type": "link", "text": "My Points and Rewards", "to": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=1,1,TextFooter_MyPointsandRewards,1", "className": "atfLinks", "target": "_blank", "style": {}}, {"type": "link", "text": "Explore Benefits", "to": "/browse/info.do?cid=1184799&mlink=1,1,TextFooter_ExploreBenefits,1", "className": "atfLinks", "target": "_blank", "style": {}}, {"type": "link", "text": "Join <PERSON><PERSON><PERSON>-it's Free", "to": "/my-account/sign-in?mlink=1,1,<PERSON><PERSON><PERSON>er_Join,1", "className": "atfLinks", "target": "_blank", "style": {}}]}]}, "mobile": {"links": [{"type": "link", "text": "Store Locator", "to": "/stores?mlink=55287,********,Footer_StoreLocator&clink=********", "className": "footer-item", "target": ""}, {"type": "link", "text": "CUSTOMER SERVICE", "to": "/customerService/info.do?cid=44959", "className": "footer-item"}, {"type": "link", "text": "ORDERS & RETURNS", "to": "/customerService/info.do?cid=1180471", "className": "footer-item"}, {"type": "link", "text": "SHIPPING & HANDLING", "to": "/customerService/info.do?cid=1180468", "className": "footer-item"}, {"type": "accordion", "text": "GIFT CARDS", "accordionLinks": [{"text": "BUY EGIFT CARDS", "to": "https://www.buyatab.com/custom/athleta/"}], "className": "footer-item"}, {"type": "accordion", "text": "ATHLETA REWARDS", "accordionLinks": [{"type": "link", "text": "My Points and Rewards", "to": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=1,1,TextFooter_MyPointsandRewards,1", "className": "footer-item", "target": "_blank", "style": {}}, {"type": "link", "text": "Explore Benefits", "to": "/browse/info.do?cid=1184799&mlink=1,1,TextFooter_ExploreBenefits,1", "className": "footer-item", "target": "_blank", "style": {}}, {"type": "link", "text": "Join <PERSON><PERSON><PERSON>-it's Free", "to": "/my-account/sign-in?mlink=1,1,<PERSON><PERSON><PERSON>er_Join,1", "className": "footer-item", "target": "_blank", "style": {}}], "className": "footer-item"}, {"type": "link", "text": "EMAIL SIGN-UP", "to": "/profile/info.do?cid=57480&mlink=55287,********,Footer_Email&clink=********", "className": "footer-item"}, {"type": "accordion", "text": "SHOP OUR OTHER BRANDS", "accordionLinks": [{"text": "GAP", "to": "//www.gapcanada.ca/?ssiteID=AT&locale=en_CA", "target": "_blank"}, {"text": "OLD NAVY", "to": "//oldnavy.gapcanada.ca?ssiteID=AT&locale=en_CA", "target": "_blank"}, {"text": "BANANA REPUBLIC", "to": "//bananarepublic.gapcanada.ca/?ssiteID=AT&locale=en_CA", "target": "_blank"}]}]}}, "copyRights": {"rows": [[{"text": "© 2022 Athleta LLC"}, {"to": "https://www.gapinc.com/en-ca/consumer-privacy-policy", "text": "Privacy Policy", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}, {"to": "/customerService/info.do?cid=44990", "text": "Terms of Use", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}, {"to": "https://www.gapinc.com/en-ca/values/sustainability", "text": "Sustainability", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}, {"to": "/browse/info.do?cid=1183672", "text": "Accessibility for Ontarians with Disabilities Act", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}, {"to": "/browse/info.do?cid=1193540", "text": "The Accessibility for Manitobans Act", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}]]}}}]}, "topnav": {"type": "sitewide", "name": "MegaNav", "desc": "20231024_ENCDA_Site Wide MegaNav", "experimentRunning": true, "instanceName": "atus-topnav-a1b7", "data": {"placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "41px"}, "mobile": {"height": "0px"}}, "lazy": false, "isNavSticky": true, "classStyles": {"divisionLink": "outline: none;  font-weight:bold!important; text-transform:none; color: #111111;", "topNavLink:last-child a": "cursor:pointer;", "topNavLink:nth-child(13) a": "cursor:pointer;", "wcdhovercontainer": "position:relative", "hoverimage": "opacity:1;display:block;width:100%;height:auto;transition:0.5s ease;backface-visibility:hidden", "wcdmiddle": "transition:0.5s ease;opacity:0;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);-ms-transform:translate(-50%, -50%);text-align:center", "wcdhovercontainer:hover .hoverimage": "opacity:1", "wcdhovercontainer:hover .wcdmiddle": "opacity:1", "wcdhovertext": "color:white;font-size:14px;font-weight:bold", "hoverbg": "position:absolute;transition:0.5s ease;width:100%;height:100%;top:0;left:0;background:rgba(0, 0, 0, 0.7);opacity:0", "wcdhovercontainer:hover .hoverbg": "opacity:0.7", "wcdsubtext": "text-transform:none;margin-top:15px;", "wcdsubtext strong": "text-transform:uppercase;", "topNavLink a[aria-label='stores & events']": "cursor:pointer;border-left: solid 1px grey;padding-left: 15px;", "topNavLink a[aria-label='sale']": "color: #111111", "meganav .catnav--header": "text-transform: lowercase;", "meganav .catnav--header:first-letter": "text-transform: capitalize;", "meganav .catnav--header>[data-categoryid='1150961']": "margin-top: 25px;", "meganav .catnav--header>[data-categoryid='1150963']": "margin-top: 25px;", "meganav .catnav--header>[data-categoryid='1150774']": "margin-top: 25px;", "meganav .customFlyout span": "-webkit-font-smoothing: antialiased;font-weight: 700;display: block;padding: 0px 0px 0.5rem;border-bottom: 0px;color: black;margin: 0px;text-transform:none", "meganav .customFlyout ul li": "-webkit-font-smoothing: antialiased;line-height: 15px;padding: 5px 0px;color: rgb(117, 117, 117);text-transform:none;", "topNavLink a[aria-label='gifts']": "color:#DE124D", "meganav.wcd-bottoms": "text-align: left; padding-left: 60px;", "meganav.wcd-gifts": "text-align: left; padding-left: 60px;", "meganav.wcd-tops": "text-align: left; padding-left: 60px;", "meganav.wcd-bras": "text-align: left; padding-left: 60px;", "meganav.wcd-dresses": "text-align: left; padding-left: 60px;", "meganav.wcd-jackets": "text-align: left; padding-left: 60px;", "meganav.wcd-swim": "text-align: left; padding-left: 60px;", "meganav.wcd-sale": "text-align: left; padding-left: 60px;"}, "activeDivisions": [{"name": "New", "divisionId": ["/browse/category.do?cid=1006482&mlink=1,1,Meganav_1"], "megaNavOrder": [["1126243"], ["1090431"], ["1184389"], ["3021556"]], "exclusionIds": ["1107873", "1093212", "1094397", "1094903", "1111161"], "customStyles": {}, "numberOfColumns": {"1090431": 1, "1126243": 1, "1184389": 1}}, {"name": "Gifts", "divisionId": ["/browse/category.do?cid=1144086&mlink=1,1,Meganav_2"], "megaNavOrder": [["1168115"]], "exclusionIds": [], "customStyles": {}, "numberOfColumns": {}}, {"name": "Bottoms", "divisionId": ["/browse/category.do?cid=1025878&mlink=1,1,Meganav_3"], "megaNavOrder": [["1060184"]], "exclusionIds": [], "customStyles": {}, "numberOfColumns": {"1060184": 1}}, {"name": "Tops", "divisionId": ["/browse/category.do?cid=1032080&mlink=1,1,Meganav_4"], "megaNavOrder": [["1060187"]], "exclusionIds": [], "customStyles": {}, "numberOfColumns": {"1060187": 1}}, {"name": "Jackets", "divisionId": ["/browse/category.do?cid=1017102&mlink=1,1,Meganav_5"], "megaNavOrder": [["1063663"], ["1155988"], []], "exclusionIds": [], "customStyles": {}, "numberOfColumns": {"1063663": 1, "1063664": 1}}, {"name": "Bras", "divisionId": ["/browse/category.do?cid=1038916&mlink=1,1,Meganav_6"], "megaNavOrder": [["1111335"]], "exclusionIds": [], "customStyles": {}, "numberOfColumns": {"1111335": 1}}, {"name": "Dresses", "divisionId": ["/browse/category.do?cid=89745&mlink=1,1,Meganav_7"], "megaNavOrder": [["1060173"], ["1158085"]], "exclusionIds": [], "customStyles": {}, "numberOfColumns": {"1060173": 1}}, {"name": "Swim", "divisionId": ["/browse/category.do?cid=1031353&mlink=1,1,Meganav_8"], "megaNavOrder": [["1130421"]], "exclusionIds": [], "customStyles": {}, "numberOfColumns": {"1130421": 1}}, {"name": "Accessories", "divisionId": ["/browse/category.do?cid=1032096&mlink=1,1,Meganav_9"], "megaNavOrder": [["1060101"], [], []], "exclusionIds": ["1061220"], "customStyles": {}, "numberOfColumns": {"1060098": 1, "1060101": 2}}, {"name": "Athleta Girl", "divisionId": ["/browse/division.do?cid=1054832&mlink=1,1,Meganav_10"], "megaNavOrder": [["1060198"], ["1060197"]], "exclusionIds": [], "customStyles": {}, "numberOfColumns": {"1060197": 1, "1060198": 2}}, {"name": "Sale", "divisionId": ["/browse/category.do?cid=1023728&mlink=1,1,Meganav_11", "49936"], "megaNavOrder": [["1060189"], []], "exclusionIds": ["1062800"], "customStyles": {}, "numberOfColumns": {"1060189": 1}}, {"name": "Experiences", "divisionId": ["/browse/info.do?cid=1183694&nav=meganav"], "megaNavOrder": [], "exclusionIds": [], "customStyles": {}, "numberOfColumns": {}}]}}, "utilitylinks": {"type": "sitewide", "name": "UtilityLinks", "desc": "amplience CA EN utility links", "data": {"style": {"color": "#000", "textTransform": "capitalize", "fontSize": "12px", "fontWeight": "normal!important"}, "brandBarShortcutLinks": [{"link": "/stores", "text": "Find A Store"}, {"link": "/browse/info.do?cid=1184799&mlink=1,1,UtilityLink,1", "text": "Athleta Rewards"}, {"link": "/browse/info.do?cid=52564", "text": "Gift Card"}]}}, "search": {"type": "sitewide", "name": "SearchSuggestions", "desc": "20231024_Search_ENCA", "data": {"search-suggestions": ["Leggings", "Sports Bras", "<PERSON><PERSON><PERSON>", "Sweatshirts", "<PERSON>ts", "Jackets"]}}, "logo": {"type": "sitewide", "name": "Logo", "logoImgPath": "/Asset_Archive/ATWeb/content/0028/760/166/assets/Athleta Logo_111111.svg"}}, "brand": "at", "type": "meta", "pmcsEdgeCacheTag": "at-homepage-en-ca-prod"}