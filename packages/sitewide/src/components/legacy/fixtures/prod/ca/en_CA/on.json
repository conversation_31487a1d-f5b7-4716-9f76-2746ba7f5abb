{"meta.description": "pageDescription defaulted", "meta.title.overide": "pageTitle defaulted", "home": {"type": "home", "name": "HomeMultiSimple", "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "wcdNote": "Rendering the new mobile CTA primary styles (4 half-width buttons with 1 full-width flyout)", "data": {"lazy": false, "defaultHeight": "0px", "html": "<link rel=\"stylesheet\" href=\"/Asset_Archive/ONWeb/content/0030/015/209/css/mobile-cta.css?v=6\"><style type=\"text/css\">#main-content .slick-slide > div > div { display: block !important; } .slick-list{ overflow: hidden !important} .slick-dots li button::before {width: 10px !important; height: 10px !important;} div[data-testid='compressed-sticky-header'] { z-index: 10000; }</style>"}}, {"_meta": {"name": "COPY_HP_SVH_Primary_Topper_NonCard_Mobile_US_102023", "schema": "https://cms.gap.com/schema/content/v1/spotlight-variable-height.json", "deliveryId": "6abf382a-b458-41c8-a47e-26e922d321cc"}, "image": {"heroImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "b082f2bb-0dd7-4e48-84a8-6c29b8b748ab", "name": "231114_81-M9051_HolidayParty_BI_OfferBanner_HP_XL_CA_1114_svg", "endpoint": "oldnavyprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Early black friday deals. Tops from $12, dresses from $25, pants from $30.", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "svgPath": "https://oldnavyprod.a.bigcontent.io/v1/static/231114_81-M9051_HolidayParty_BI_OfferBanner_HP_XL_CA_1114_svg", "enableChroma": true, "chromaQuality": 80}], "imageSize": "xsmall", "link": {"value": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_1_a", "label": "Shop women's sale"}}, "imageOverlay": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "useGradientBackfill": false, "contentPlacement": {"horizontal": "middle", "vertical": "middle"}, "contentJustification": "middle", "handle": {"placement": "left"}, "useGradientBackfillFooter": false}, "contentBlocks": {"aboveImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "background": {"type": "solid"}}, "belowImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "background": {"type": "solid"}}}, "webAppearance": {"showHideBasedOnScreenSize": "hideOnMobile"}}, {"_meta": {"name": "COPY_HP_SVH_Primary_Topper_NonCard_Mobile_US_102023", "schema": "https://cms.gap.com/schema/content/v1/spotlight-variable-height.json", "deliveryId": "70d2d18f-1c32-4453-a721-870c0414cf5d"}, "image": {"mobileHeroImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "df6156a3-2e07-4fef-a08a-f9492a43d0e1", "name": "231114_81-M9051_HolidayParty_BI_OfferBanner_HP_SM_CA_1114_svg", "endpoint": "oldnavyprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Early black friday deals. Tops from $12, dresses from $25, pants from $30.", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "svgPath": "https://oldnavyprod.a.bigcontent.io/v1/static/231114_81-M9051_HolidayParty_BI_OfferBanner_HP_SM_CA_1114_svg", "enableChroma": true, "chromaQuality": 80}], "imageSize": "small", "link": {"value": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_1_a", "label": "Shop women's sale"}, "heroImage": [{"altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}, "imageOverlay": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "useGradientBackfill": false, "contentPlacement": {"horizontal": "middle", "vertical": "middle"}, "contentJustification": "middle", "handle": {"placement": "left"}, "useGradientBackfillFooter": false}, "contentBlocks": {"aboveImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "background": {"type": "solid"}}, "belowImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "background": {"type": "solid"}}}, "webAppearance": {"showHideBasedOnScreenSize": "hideOnDesktop"}}, {"_meta": {"name": "COPY_HP_Text Navigation Exposed_NonCard_Desktop_US_102023", "schema": "https://cms.gap.com/schema/content/v1/text-navigation-exposed.json", "deliveryId": "f258aa23-5896-425e-b42d-2548c0534311"}, "ctaList": [{"label": "Women", "value": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_1_b"}, {"label": "Men", "value": "/browse/category.do?cid=26061&mlink=5151,1,HP_Prim_1_b"}, {"label": "Girls", "value": "/browse/category.do?cid=26175&mlink=5151,1,HP_Prim_1_b"}, {"label": "Boys", "value": "/browse/category.do?cid=26073&mlink=5151,1,HP_Prim_1_b"}, {"label": "Toddler Girls", "value": "/browse/category.do?cid=26785&department=165&mlink=5151,1,HP_Prim_1_b"}, {"label": "Toddler Boys", "value": "/browse/category.do?cid=26619&department=166&mlink=5151,1,HP_Prim_1_b"}, {"label": "Baby Girls", "value": "/browse/category.do?cid=51646&department=165&mlink=5151,1,HP_Prim_1_b"}, {"label": "Baby Boys", "value": "/browse/category.do?cid=51666&department=166&mlink=5151,1,HP_Prim_1_b"}, {"label": "Maternity", "value": "/browse/category.do?cid=26239&mlink=5151,1,HP_Prim_1_b"}], "webAppearance": {"mobileDropdown": "list", "showHideBasedOnScreenSize": "hideOnMobile", "fontColor": "#000"}}, {"_meta": {"name": "COPY_HP_CTA/Dropdown_AllExposed_Mobile_NonCard_US_102023", "schema": "https://cms.gap.com/schema/content/v1/cta-or-dropdown.json", "deliveryId": "34af8f9b-d6df-429b-943d-421fbec76de7"}, "ctaDropdownList": [{"ctaDropdown": [{"label": "women", "value": "/browse/category.do?cid=26190&mlink=5151,1,HP_Prim_1_b"}], "label": "women"}, {"ctaDropdown": [{"label": "men", "value": "/browse/category.do?cid=26061&mlink=5151,1,HP_Prim_1_b"}], "label": "men"}, {"ctaDropdown": [{"label": "girls", "value": "/browse/category.do?cid=26175&mlink=5151,1,HP_Prim_1_b"}], "label": "girls"}, {"ctaDropdown": [{"label": "boys", "value": "/browse/category.do?cid=26073&mlink=5151,1,HP_Prim_1_b"}], "label": "boys"}, {"ctaDropdown": [{"label": "Toddler Girls", "value": "/browse/category.do?cid=26785&department=165&mlink=5151,1,HP_Prim_1_b"}, {"label": "Toddler Boys", "value": "/browse/category.do?cid=26619&department=166&mlink=5151,1,HP_Prim_1_b"}, {"label": "Baby Girls", "value": "/browse/category.do?cid=51646&department=165&mlink=5151,1,HP_Prim_1_b"}, {"label": "Baby Boys", "value": "/browse/category.do?cid=51666&department=166&mlink=5151,1,HP_Prim_1_b"}, {"label": "Maternity", "value": "/browse/category.do?cid=26239&mlink=5151,1,HP_Prim_1_b"}], "label": "shop for the fam"}], "webAppearance": {"ctaButtonStyling": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "hideOnDesktop"}, "mobileGridLayout": "2/2/1"}, {"_meta": {"name": "COPY_HP_SVH_Primary_Hero_Banner_NonCard_Desktop_US_102023", "schema": "https://cms.gap.com/schema/content/v1/spotlight-variable-height.json", "deliveryId": "7108bf10-f2a8-433e-89e2-d0132b9ea873"}, "image": {"heroImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "37cf4b50-8735-473d-bc14-ca7c960d131f", "name": "231114_81-<PERSON>9051_HolidayP<PERSON>y_BI_Hero_HP_XL_CA_1114", "endpoint": "oldnavyprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Party me, styles from $12.", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": true, "chromaQuality": 80}], "mobileHeroImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "f5d7e28f-edec-4e46-ad77-176d4aeafe52", "name": "231114_81-<PERSON>9051_HolidayP<PERSON>y_BI_Hero_HP_SM_US_1114", "endpoint": "oldnavyprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Party me, styles from $10.", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": true, "chromaQuality": 80}], "imageSize": "large", "link": {"value": "/browse/category.do?cid=3024692&mlink=5151,1,HP_Prim_2_a", "label": "Shop Family Holiday Outfits"}}, "imageOverlay": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "useGradientBackfill": false, "contentPlacement": {"horizontal": "middle", "vertical": "middle"}, "contentJustification": "middle", "handle": {"placement": "right"}, "useGradientBackfillFooter": false, "detailsLink": {"fontColor": "#FFF"}}, "contentBlocks": {"aboveImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "background": {"type": "solid"}}, "belowImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaDropdownList": [{"ctaDropdown": [{"label": "Women", "value": "/browse/category.do?cid=72091&mlink=5151,1,HP_Prim_2_b"}, {"label": "Men", "value": "/browse/category.do?cid=1126985&mlink=5151,1,HP_Prim_2_b"}, {"label": "Girls", "value": "/browse/category.do?cid=97071&mlink=5151,1,HP_Prim_2_b"}, {"label": "Boys", "value": "/browse/category.do?cid=13469&mlink=5151,1,HP_Prim_2_b"}, {"label": "Toddler Girls", "value": "/browse/category.do?cid=1026838&department=165&mlink=5151,1,HP_Prim_2_b"}, {"label": "Toddler Boys", "value": "/browse/category.do?cid=41785&department=166&mlink=5151,1,HP_Prim_2_b"}, {"label": "Baby Girls", "value": "/browse/category.do?cid=23270&department=165&mlink=5151,1,HP_Prim_2_b"}, {"label": "Baby Boys", "value": "/browse/category.do?cid=23276&department=166&mlink=5151,1,HP_Prim_2_b"}, {"label": "Maternity", "value": "/browse/category.do?cid=1090999&mlink=5151,1,HP_Prim_2_b"}], "label": "Shop Tops"}, {"ctaDropdown": [{"label": "Women", "value": "/browse/category.do?cid=15292&mlink=5151,1,HP_Prim_2_c"}, {"label": "Girls", "value": "/browse/category.do?cid=39288&mlink=5151,1,HP_Prim_2_c"}, {"label": "Toddler Girls", "value": "/browse/category.do?cid=47926&department=165&mlink=5151,1,HP_Prim_2_c"}, {"label": "Baby Girls", "value": "/browse/category.do?cid=6269&department=165&mlink=5151,1,HP_Prim_2_c"}, {"label": "Maternity", "value": "/browse/category.do?cid=48687&mlink=5151,1,HP_Prim_2_c"}], "label": "Shop Dresses"}, {"ctaDropdown": [{"label": "Women", "value": "/browse/category.do?cid=5475&mlink=5151,1,HP_Prim_2_d"}, {"label": "Men", "value": "/browse/category.do?cid=5211&mlink=5151,1,HP_Prim_2_d"}, {"label": "Girls", "value": "/browse/category.do?cid=45013&mlink=5151,1,HP_Prim_2_d"}, {"label": "Boys", "value": "/browse/category.do?cid=5934&mlink=5151,1,HP_Prim_2_d"}, {"label": "Toddler Girls", "value": "/browse/category.do?cid=41941&department=165&mlink=5151,1,HP_Prim_2_d"}, {"label": "Toddler Boys", "value": "/browse/category.do?cid=37257&department=166&mlink=5151,1,HP_Prim_2_d"}, {"label": "Baby Girls", "value": "/browse/category.do?cid=1127079&department=165&mlink=5151,1,HP_Prim_2_d"}, {"label": "Baby Boys", "value": "/browse/category.do?cid=1127083&department=166&mlink=5151,1,HP_Prim_2_d"}, {"label": "Maternity", "value": "/browse/category.do?cid=5856&mlink=5151,1,HP_Prim_2_d"}], "label": "Shop Pants"}], "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "rte": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-1\" style=\"color:#003764;font-weight:500\">Looks for scene-stealer me, wallflower me, dance floor me, eggnog hog me...</span></p>", "background": {"type": "solid"}}}, "webAppearance": {"showHideBasedOnScreenSize": "hideOnMobile"}}, {"_meta": {"name": "COPY_HP_SVH_Primary_Hero_Banner_NonCard_Desktop_US_102023", "schema": "https://cms.gap.com/schema/content/v1/spotlight-variable-height.json", "deliveryId": "c3f4443e-aaa6-40bb-8443-a23450cededd"}, "image": {"mobileHeroImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "f7fc012d-31a3-43b4-9f31-3993e89798d1", "name": "231114_81-<PERSON>9051_HolidayP<PERSON>y_BI_Hero_HP_SM_CA_1114", "endpoint": "oldnavyprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Party me, styles from $12.", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": true, "chromaQuality": 80}], "imageSize": "large", "link": {"value": "/browse/category.do?cid=3024692&mlink=5151,1,HP_Prim_2_a", "label": "Shop Family Holiday Outfits"}, "heroImage": [{"altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}, "imageOverlay": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "useGradientBackfill": false, "contentPlacement": {"horizontal": "middle", "vertical": "middle"}, "contentJustification": "middle", "handle": {"placement": "right"}, "useGradientBackfillFooter": false, "detailsLink": {"fontColor": "#FFF"}}, "contentBlocks": {"aboveImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "background": {"type": "solid"}}, "belowImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaDropdownList": [{"ctaDropdown": [{"label": "Women", "value": "/browse/category.do?cid=72091&mlink=5151,1,HP_Prim_2_b"}, {"label": "Men", "value": "/browse/category.do?cid=1126985&mlink=5151,1,HP_Prim_2_b"}, {"label": "Girls", "value": "/browse/category.do?cid=97071&mlink=5151,1,HP_Prim_2_b"}, {"label": "Boys", "value": "/browse/category.do?cid=13469&mlink=5151,1,HP_Prim_2_b"}, {"label": "Toddler Girls", "value": "/browse/category.do?cid=1026838&department=165&mlink=5151,1,HP_Prim_2_b"}, {"label": "Toddler Boys", "value": "/browse/category.do?cid=41785&department=166&mlink=5151,1,HP_Prim_2_b"}, {"label": "Baby Girls", "value": "/browse/category.do?cid=23270&department=165&mlink=5151,1,HP_Prim_2_b"}, {"label": "Baby Boys", "value": "/browse/category.do?cid=23276&department=166&mlink=5151,1,HP_Prim_2_b"}, {"label": "Maternity", "value": "/browse/category.do?cid=1090999&mlink=5151,1,HP_Prim_2_b"}], "label": "Shop Tops"}, {"ctaDropdown": [{"label": "Women", "value": "/browse/category.do?cid=15292&mlink=5151,1,HP_Prim_2_c"}, {"label": "Girls", "value": "/browse/category.do?cid=39288&mlink=5151,1,HP_Prim_2_c"}, {"label": "Toddler Girls", "value": "/browse/category.do?cid=47926&department=165&mlink=5151,1,HP_Prim_2_c"}, {"label": "Baby Girls", "value": "/browse/category.do?cid=6269&department=165&mlink=5151,1,HP_Prim_2_c"}, {"label": "Maternity", "value": "/browse/category.do?cid=48687&mlink=5151,1,HP_Prim_2_c"}], "label": "Shop Dresses"}, {"ctaDropdown": [{"label": "Women", "value": "/browse/category.do?cid=5475&mlink=5151,1,HP_Prim_2_d"}, {"label": "Men", "value": "/browse/category.do?cid=5211&mlink=5151,1,HP_Prim_2_d"}, {"label": "Girls", "value": "/browse/category.do?cid=45013&mlink=5151,1,HP_Prim_2_d"}, {"label": "Boys", "value": "/browse/category.do?cid=5934&mlink=5151,1,HP_Prim_2_d"}, {"label": "Toddler Girls", "value": "/browse/category.do?cid=41941&department=165&mlink=5151,1,HP_Prim_2_d"}, {"label": "Toddler Boys", "value": "/browse/category.do?cid=37257&department=166&mlink=5151,1,HP_Prim_2_d"}, {"label": "Baby Girls", "value": "/browse/category.do?cid=1127079&department=165&mlink=5151,1,HP_Prim_2_d"}, {"label": "Baby Boys", "value": "/browse/category.do?cid=1127083&department=166&mlink=5151,1,HP_Prim_2_d"}, {"label": "Maternity", "value": "/browse/category.do?cid=5856&mlink=5151,1,HP_Prim_2_d"}], "label": "Shop Pants"}], "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "rte": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\" style=\"color:#003764\">Looks for scene-stealer me, wallflower me, dance floor me, eggnog hog me...</span></p>", "background": {"type": "solid"}}}, "webAppearance": {"showHideBasedOnScreenSize": "hideOnDesktop"}}, {"_meta": {"name": "COPY_HP_SVH_Primary_Hero_Banner_NonCard_Desktop_US_102023", "schema": "https://cms.gap.com/schema/content/v1/spotlight-variable-height.json", "deliveryId": "b8acf96b-f19e-4458-a0e2-622ed78e7fcc"}, "image": {"heroImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "769bea5c-976a-4144-9065-b998350936f3", "name": "231114_81-<PERSON>9051_HolidayParty_BI_Hero2_HP_XL_CA_1114", "endpoint": "oldnavyprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Velvet me, sequins me. <PERSON> trouser $35 this week only*.", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": true, "chromaQuality": 80}], "imageSize": "large", "link": {"value": "/browse/category.do?cid=1185965&mlink=5151,1,HP_Prim_3_a", "label": "Shop Women's Holiday Outfits"}}, "imageOverlay": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "useGradientBackfill": false, "contentPlacement": {"horizontal": "middle", "vertical": "middle"}, "contentJustification": "middle", "handle": {"placement": "right"}, "useGradientBackfillFooter": false, "detailsLink": {"fontColor": "#FFF"}}, "contentBlocks": {"aboveImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "background": {"type": "solid"}}, "belowImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaDropdownList": [{"ctaDropdown": [{"label": "Shop Taylor Trouser", "value": "/browse/category.do?cid=5475#collection=106557715&mlink=5151,1,HP_Prim_3_b"}]}, {"ctaDropdown": [{"label": "Women", "value": "/browse/category.do?cid=1185965&mlink=5151,1,HP_Prim_3_c"}, {"label": "Men", "value": "/browse/category.do?cid=3012629&mlink=5151,1,HP_Prim_3_c"}, {"label": "Girls", "value": "/browse/category.do?cid=3012594&mlink=5151,1,HP_Prim_3_c"}, {"label": "Boys", "value": "/browse/category.do?cid=3012589&mlink=5151,1,HP_Prim_3_c"}, {"label": "Toddler Girls", "value": "/browse/category.do?cid=3012611&department=165&mlink=5151,1,HP_Prim_3_c"}, {"label": "Toddler Boys", "value": "/browse/category.do?cid=3012608&department=166&mlink=5151,1,HP_Prim_3_c"}, {"label": "Baby Girls", "value": "/browse/category.do?cid=3012616&department=165&mlink=5151,1,HP_Prim_3_c"}, {"label": "Baby Boys", "value": "/browse/category.do?cid=3012619&department=166&mlink=5151,1,HP_Prim_3_c"}, {"label": "Maternity", "value": "/browse/category.do?cid=3024578&mlink=5151,1,HP_Prim_3_c"}, {"label": "Shop for the Fam", "value": "/browse/category.do?cid=3024692&mlink=5151,1,HP_Prim_3_c"}], "label": "Shop Party Looks"}], "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "rte": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-1\" style=\"color:#003764;font-weight:500\">As seen on <PERSON> and... <PERSON>.</span></p>", "background": {"type": "solid"}}}, "webAppearance": {"showHideBasedOnScreenSize": "hideOnMobile"}}, {"_meta": {"name": "COPY_HP_SVH_Primary_Hero_Banner_NonCard_Desktop_US_102023", "schema": "https://cms.gap.com/schema/content/v1/spotlight-variable-height.json", "deliveryId": "462b6260-9143-47e3-a111-d75e00139967"}, "image": {"mobileHeroImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "4c13092c-3c3f-4f27-919a-28e5a7579201", "name": "231114_81-<PERSON>9051_HolidayParty_BI_Hero2_HP_SM_CA_1114", "endpoint": "oldnavyprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Velvet me, sequins me. <PERSON> trouser $35 this week only*.", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": true, "chromaQuality": 80}], "imageSize": "large", "link": {"value": "/browse/category.do?cid=1185965&mlink=5151,1,HP_Prim_3_a", "label": "Shop Women's Holiday Outfits"}, "heroImage": [{"altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}, "imageOverlay": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "useGradientBackfill": false, "contentPlacement": {"horizontal": "middle", "vertical": "middle"}, "contentJustification": "middle", "handle": {"placement": "right"}, "useGradientBackfillFooter": false, "detailsLink": {"fontColor": "#FFF"}}, "contentBlocks": {"aboveImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "background": {"type": "solid"}}, "belowImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaDropdownList": [{"ctaDropdown": [{"label": "Shop Taylor Trouser", "value": "/browse/category.do?cid=5475#collection=106557715&mlink=5151,1,HP_Prim_3_b"}]}, {"ctaDropdown": [{"label": "Women", "value": "/browse/category.do?cid=1185965&mlink=5151,1,HP_Prim_3_c"}, {"label": "Men", "value": "/browse/category.do?cid=3012629&mlink=5151,1,HP_Prim_3_c"}, {"label": "Girls", "value": "/browse/category.do?cid=3012594&mlink=5151,1,HP_Prim_3_c"}, {"label": "Boys", "value": "/browse/category.do?cid=3012589&mlink=5151,1,HP_Prim_3_c"}, {"label": "Toddler Girls", "value": "/browse/category.do?cid=3012611&department=165&mlink=5151,1,HP_Prim_3_c"}, {"label": "Toddler Boys", "value": "/browse/category.do?cid=3012608&department=166&mlink=5151,1,HP_Prim_3_c"}, {"label": "Baby Girls", "value": "/browse/category.do?cid=3012616&department=165&mlink=5151,1,HP_Prim_3_c"}, {"label": "Baby Boys", "value": "/browse/category.do?cid=3012619&department=166&mlink=5151,1,HP_Prim_3_c"}, {"label": "Maternity", "value": "/browse/category.do?cid=3024578&mlink=5151,1,HP_Prim_3_c"}, {"label": "Shop for the Fam", "value": "/browse/category.do?cid=3024692&mlink=5151,1,HP_Prim_3_c"}], "label": "Shop Party Looks"}], "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "rte": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\" style=\"color:#003764;font-weight:500\">As seen on the iconic <PERSON>, and, soon, you!</span></p>", "background": {"type": "solid"}}}, "webAppearance": {"showHideBasedOnScreenSize": "hideOnDesktop"}}, {"instanceName": "hierarchy-placeholder-02", "name": "div", "type": "builtin", "useGreyLoadingEffect": false, "experimentRunning": true, "meta": {"lazy": false}, "data": {"defaultHeight": {"small": "300px", "large": "513px"}, "components": [{"instanceName": "040522-certona-rows", "name": "LayoutComponent", "type": "sitewide", "useGreyLoadingEffect": false, "experimentRunning": false, "meta": {"lazy": true}, "data": {"defaultHeight": {"small": "645px", "large": "654px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"display": "flex", "margin": " 0 auto", "maxWidth": "1440px", "flexDirection": "column", "flexWrap": "nowrap", "justifyContent": "flex-start", "height": "100%"}, "components": [{"name": "Recommendations", "type": "home", "meta": {"lazy": true}, "tileStyle": {"mobile": {"margin": "0 auto", "width": "99%"}, "desktop": {"width": "85%", "boxSizing": "border-box", "margin": "1% auto", "paddingLeft": "50px", "paddingRight": "50px", "textAlign": "center"}}, "data": {"source": "c<PERSON>a", "placeholderStyles": {"width": "100%", "height": "350px", "overflow": "hidden"}, "layout": "carousel", "numberOfProduct": 20, "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "showMarketingFlag": true, "customBrand": "ON", "scheme": "onhome1_rr", "displayTitle": true, "certonaTitle": {"title": "We picked these just for you...", "style": {"desktop": {"display": "flex", "color": "#000", "paddingBottom": "16px", "fontSize": "min(calc(14px + 28 * ((100vw - 768px) / 2232)), 22px)", "fontWeight": "700", "textAlign": "center", "textTransform": "uppercase", "justifyContent": "center", "letterSpacing": "1.2px"}, "mobile": {"display": "flex", "paddingBottom": "10px", "fontSize": "4.5vw", "fontWeight": "700", "color": "#000", "textAlign": "center", "textTransform": "uppercase", "justifyContent": "center", "letterSpacing": "1.2px", "whiteSpace": "pre-line"}}}, "useMobileConfig": true, "infinite": true, "defaultslidesToShowSlick": 4, "defaultslidesToScrollSlick": 4, "resslidesToShowSlick": 4, "resslidesToScrollSlick": 4, "responsive": [{"breakpoint": 767, "settings": {"infinite": false, "slidesToShow": 2.5, "slidesToScroll": 1}}], "mobileSmoothScroll": true, "prevArrowSlick": "/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png", "nextArrowSlick": "/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png", "prevArrowAlt": "previous", "nextArrowAlt": "next", "arrowPosition": "1.5%", "arrowVerticalPosition": "-10%", "productTextStyles": {"productTitle": {"style": {"textAlign": "left", "color": "auto", "fontSize": ".75rem", "paddingRight": "1rem"}}, "productMarketingFlag": {"style": {"fontWeight": "bold", "fontSize": ".75rem", "float": "left", "textAlign": "left"}}, "productPrice": {"style": {"fontSize": ".75rem", "color": "auto", "float": "left"}}, "productSalePrice": {"style": {"fontSize": ".75rem", "color": "red", "float": "left"}}, "size": {"width": "auto"}}, "productCardStyles": {"style": {"width": "auto", "marginBottom": "8px", "textAlign": "center"}}, "gridLayout": {"style": {"desktop": {"display": "flex", "flex-flow": "row wrap"}, "mobile": {}}, "productsPerRow": {"desktop": 4, "mobile": 2}}}}, {"name": "Recommendations", "type": "home", "meta": {"lazy": true}, "tileStyle": {"mobile": {"margin": "0 auto", "width": "99%"}, "desktop": {"width": "85%", "boxSizing": "border-box", "margin": "0 auto 1%", "paddingLeft": "50px", "paddingRight": "50px", "textAlign": "center"}}, "data": {"source": "c<PERSON>a", "placeholderStyles": {"width": "100%", "height": "350px", "overflow": "hidden"}, "layout": "carousel", "numberOfProduct": 20, "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "showMarketingFlag": true, "customBrand": "ON", "scheme": "onhome2_rr", "displayTitle": false, "certonaTitle": {"title": "We picked these just for you...", "style": {"desktop": {"display": "flex", "color": "#000", "paddingBottom": "16px", "fontSize": "min(calc(14px + 28 * ((100vw - 768px) / 2232)), 22px)", "fontWeight": "700", "textAlign": "center", "textTransform": "uppercase", "justifyContent": "center", "letterSpacing": "1.2px"}, "mobile": {"display": "flex", "paddingBottom": "10px", "fontSize": "4.5vw", "fontWeight": "700", "color": "#000", "textAlign": "center", "textTransform": "uppercase", "justifyContent": "center", "letterSpacing": "1.2px", "whiteSpace": "pre-line"}}}, "useMobileConfig": true, "infinite": true, "defaultslidesToShowSlick": 4, "defaultslidesToScrollSlick": 4, "resslidesToShowSlick": 4, "resslidesToScrollSlick": 4, "responsive": [{"breakpoint": 767, "settings": {"infinite": false, "slidesToShow": 2.5, "slidesToScroll": 1}}], "mobileSmoothScroll": true, "prevArrowSlick": "/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png", "nextArrowSlick": "/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png", "prevArrowAlt": "previous", "nextArrowAlt": "next", "arrowPosition": "1.5%", "arrowVerticalPosition": "-10%", "productTextStyles": {"productTitle": {"style": {"textAlign": "left", "color": "auto", "fontSize": ".75rem", "paddingRight": "1rem"}}, "productMarketingFlag": {"style": {"fontWeight": "bold", "fontSize": ".75rem", "float": "left", "textAlign": "left"}}, "productPrice": {"style": {"fontSize": ".75rem", "color": "auto", "float": "left"}}, "productSalePrice": {"style": {"fontSize": ".75rem", "color": "red", "float": "left"}}, "size": {"width": "auto"}}, "productCardStyles": {"style": {"width": "auto", "marginBottom": "8px", "textAlign": "center"}}, "gridLayout": {"style": {"desktop": {"display": "flex", "flex-flow": "row wrap"}, "mobile": {}}, "productsPerRow": {"desktop": 4, "mobile": 2}}}}, {"name": "ComposableButton", "meta": {"lazy": true}, "tileStyle": {"mobile": {"display": "block", "width": "90%", "margin": "calc(100vw / 720 * 20) auto calc(100vw / 720 * 40)", "textAlign": "center", "& a": {"backgroundColor": "#fff", "width": "100%", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.6875em 0.8em", "fontSize": "0.9rem", "letterSpacing": "normal"}}, "desktop": {"display": "none"}}, "data": {"linkProps": {"href": "/browse/info.do?cid=1113546&mlink=5151,1,<PERSON>_<PERSON><PERSON><PERSON>_More"}, "borderThickness": "medium", "bright": false, "capitalization": "uppercase", "color": "primary", "crossBrand": false, "font": "secondary", "fullWidth": false, "variant": "border", "roundedCorner": "true", "buttonText": "Shop More Picks"}}]}}}}]}}, {"_meta": {"name": "COPY_HP_SVH_CP_Banner_NonCard_Mobile_US_102023", "schema": "https://cms.gap.com/schema/content/v1/spotlight-variable-height.json", "deliveryId": "9b32adb4-e4c5-4a55-8ea7-d990f1dcf0a9"}, "image": {"heroImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "c77d4920-1694-4e18-ab73-c89d8e7621e1", "name": "231031_80_M9595_Gifting_BI_HP_Secondary_XL_CA_1031", "endpoint": "oldnavyprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Gifts from $8.", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": true, "chromaQuality": 80}], "imageSize": "medium", "link": {"value": "/browse/category.do?cid=1184818&mlink=5151,1,HP_Prim_5_a", "label": "Shop Gift Guide"}}, "imageOverlay": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "useGradientBackfill": false, "contentPlacement": {"horizontal": "middle", "vertical": "middle"}, "contentJustification": "middle", "handle": {"placement": "left"}, "useGradientBackfillFooter": false, "detailsLink": {"fontColor": "#FFF"}}, "contentBlocks": {"aboveImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "background": {"type": "solid"}}, "belowImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaDropdownList": [{"ctaDropdown": [{"label": "Shop Gift Guide", "value": "/browse/category.do?cid=1184818&mlink=5151,1,HP_Prim_5_b"}], "label": "Shop Gift Guide"}], "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "background": {"type": "solid"}, "rte": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-1\" style=\"color:#003764;font-weight:500\">Deal-obsessed me 🤝 pro gifter me.</span></p>"}}, "webAppearance": {"showHideBasedOnScreenSize": "hideOnMobile"}}, {"_meta": {"name": "COPY_HP_SVH_CP_Banner_NonCard_Mobile_US_102023", "schema": "https://cms.gap.com/schema/content/v1/spotlight-variable-height.json", "deliveryId": "d7701b88-6b63-4014-b667-8b6b18ce3ced"}, "image": {"imageSize": "large", "link": {"value": "/browse/category.do?cid=1184818&mlink=5151,1,HP_Prim_5_a", "label": "Shop Gift Guide"}, "mobileHeroImage": [{"image": {"_meta": {"schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"}, "id": "7fe42967-bf5a-42e7-86c4-1fe9633dbd38", "name": "231031_80_M9595_Gifting_BI_HP_Secondary_SM_CA_1031", "endpoint": "oldnavyprod", "defaultHost": "cdn.media.amplience.net"}, "altText": "Gifts from $8.", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": true, "chromaQuality": 80}], "heroImage": [{"altText": "", "variations": [{"variation": "desktop"}, {"variation": "mobile"}], "fliph": false, "flipv": false, "enableChroma": false, "chromaQuality": 80}]}, "imageOverlay": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "useGradientBackfill": false, "contentPlacement": {"horizontal": "middle", "vertical": "middle"}, "contentJustification": "middle", "handle": {"placement": "left"}, "useGradientBackfillFooter": false, "detailsLink": {"fontColor": "#FFF"}}, "contentBlocks": {"aboveImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "background": {"type": "solid"}}, "belowImage": {"cta": {"buttonStyle": {"buttonStyle": "border", "buttonColor": "dark"}, "ctaDropdownList": [{"ctaDropdown": [{"label": "Shop Gift Guide", "value": "/browse/category.do?cid=1184818&mlink=5151,1,HP_Prim_5_b"}], "label": "Shop Gift Guide"}], "showHideBasedOnScreenSize": "alwaysShow"}, "contentPlacement": "middle", "contentJustification": "middle", "background": {"type": "solid"}, "rte": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\" style=\"color:#003764;font-weight:500\">Deal-obsessed me 🤝 pro gifter me.</span></p>"}}, "webAppearance": {"showHideBasedOnScreenSize": "hideOnDesktop"}}, {"name": "div", "type": "builtin", "instanceName": "new-arrivals-ppof-LCM-new-amplience-en", "experimentRunning": true, "meta": {"lazy": true}, "data": {"defaultHeight": {"small": "300px", "large": "396px"}, "style": {"margin": "0 auto 1rem", "position": "relative", "& > div:nth-child(1)": {"boxSizing": "border-box", "margin": "0 auto", "paddingLeft": "0.5rem", "paddingRight": "0.5rem", "maxWidth": "100%", "width": "100%", "minHeight": "355px"}, "& > div:nth-child(1) h2": {"display": "flex", "paddingBottom": "20px", "fontSize": "4.5vw", "fontWeight": "700", "textAlign": "center", "textTransform": "uppercase", "justifyContent": "center", "letterSpacing": "1.5px"}}, "desktopStyle": {"maxWidth": "1440px", "marginBottom": "1%", "& > div:nth-child(1)": {"width": "85%", "boxSizing": "border-box", "margin": "0 auto", "paddingLeft": "50px", "paddingRight": "50px", "textAlign": "center", "minHeight": "300px"}, "& > div:nth-child(1) h2": {"fontSize": "min(calc(14px + 28 * ((100vw - 768px) / 2232)), 22px)", "display": "flex", "fontWeight": "700", "textAlign": "center", "textTransform": "uppercase", "justifyContent": "center", "letterSpacing": "1.5px", "margin": "0 0 6%", "@media (min-width: 768px) and (max-width: 1023px)": {"margin": "0 auto 8%"}, "padding": "0"}}, "components": [{"instanceName": "na-ppof", "name": "Recommendations", "type": "home", "data": {"source": "productCategory", "requestUrl": "https://api.gapcanada.ca/commerce/search/products/v2/cc?brand=on&market=ca&locale=en_CA&pageSize=20&cid=", "useDivPref": true, "cid": "10018", "layout": "carousel", "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "showMarketingFlag": true, "customBrand": "ON", "scheme": "", "displayTitle": true, "certonaTitle": {"title": "Even more new arrivals!", "style": {"desktop": {}, "mobile": {}}}, "defaultslidesToShowSlick": 4, "defaultslidesToScrollSlick": 4, "resslidesToShowSlick": 4, "resslidesToScrollSlick": 4, "responsive": [{"breakpoint": 320, "settings": {"slidesToScroll": 1}}], "mobileSmoothScroll": true, "prevArrowSlick": "/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png", "nextArrowSlick": "/Asset_Archive/ONWeb/content/0013/268/652/assets/020416_US_DenimWovenSweaters_site_hp_v2_arrowL.png", "prevArrowAlt": "previous", "nextArrowAlt": "next", "arrowPosition": "1.5%", "arrowVerticalPosition": "-10%", "productTextStyles": {"productTitle": {"style": {"fontSize": ".75rem", "textAlign": "left", "paddingRight": "0rem"}}, "productMarketingFlag": {"style": {"fontWeight": "bold", "fontSize": ".75rem", "float": "left", "textAlign": "left"}}, "productPrice": {"style": {"float": "left", "fontSize": ".75rem"}}, "productSalePrice": {"style": {"color": "red", "float": "left", "fontSize": ".75rem"}}, "size": {"width": "auto"}}, "productCardStyles": {"style": {"width": "auto", "marginBottom": "8px", "textAlign": "center"}}, "gridLayout": {"style": {"desktop": {"display": "flex", "flex-flow": "row wrap"}, "mobile": {}}, "productsPerRow": {"desktop": 4, "mobile": 2}}, "productsPerRow": {"desktop": 4, "mobile": 2}}}, {"instanceName": "exposed-ctas-desk", "name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"desktopStyle": {"position": "absolute", "left": "50%", "transform": "translate(-50%, -50%)", "width": "98%", "maxWidth": "1372px", "top": "11%", "@media (min-width: 768px) and (max-width: 1279px)": {"top": "9.5%"}}}, "ctaList": {"style": {"display": "none", "width": "90%", "textAlign": "center", "padding": "0", "justifyContent": "space-between", "flexDirection": "row", "flexWrap": "nowrap", "margin": "1rem auto", "& ul": {"zIndex": "200"}, "& ul a": {"fontSize": "0.8735rem"}, "& > a": {"textAlign": "center", "width": "calc(50% - 2px)", "background": "#fff", "border": "2px solid #003764", "color": "#003764", "borderRadius": "8px", "margin": "0px 0px 5px 0px", "whiteSpace": "normal", "lineHeight": "1", "letterSpacing": "0.4px", "padding": "1em 0", "fontSize": "0.9rem", "height": "44px"}, "& > a:nth-child(odd)": {"marginRight": "2px"}, "& > a:nth-child(even)": {"marginLeft": "2px"}, "& > a:nth-child(5), & > a:nth-child(6), & > a:nth-child(7), & > a:nth-child(8), & > a:nth-child(9)": {"display": "none"}, "& > div > button": {"width": "100%", "lineHeight": "normal", "margin": "0 auto", "textAlign": "center", "letterSpacing": "0.4px", "padding": "1rem 0", "fontSize": "0.9rem", "height": "44px"}}, "desktopStyle": {"--ctaMargin": "calc(70 / 2)", "@media (min-width: 768px) and (max-width: 1023px)": {"--ctaMargin": "calc(44 / 2)", "& > a": {"margin": "0 calc(100vw / 1440 * var(--ctaMargin))"}}, "display": "block", "background": "#fff", "position": "relative", "whiteSpace": "nowrap", "width": "100%", "padding": "0", "margin": "0 auto", "& > a:nth-child(odd)": {"margin": "0 min(calc(100vw / 1440 * var(--ctaMargin)), calc(var(--ctaMargin) * 1px))"}, "& > a:nth-child(even)": {"margin": "0 min(calc(100vw / 1440 * var(--ctaMargin)), calc(var(--ctaMargin) * 1px))"}, "& > a": {"transition": "unset", "width": "auto", "flex": "0 1 auto", "backgroundColor": "transparent", "textDecoration": "underline", "borderRadius": "0px", "border": "none", "color": "#000", "textAlign": "center", "padding": "max(15px, min(calc(100vw / 1440 * 15.5), 15.5px)) 0", "fontWeight": "bold", "fontSize": "max(11px, min(calc(100vw / 1440 * 14), 14px))", "letterSpacing": "min(calc(100vw / 1440 * 0.4), 0.4px)", "lineHeight": "min(calc(100vw / 1440 * 14), 14px)", "height": "auto", "minHeight": "unset", "margin": "0 min(calc(100vw / 1440 * var(--ctaMargin)), calc(var(--ctaMargin) * 1px))"}, "& > a:nth-child(5), & > a:nth-child(6), & > a:nth-child(7), & > a:nth-child(8), & > a:nth-child(9)": {"display": "inline-flex"}, "& > div": {"display": "none"}}, "container": {"style": {"position": "relative"}}, "ctas": [{"composableButtonData": {"children": "Women"}, "linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,HP_NA", "title": "Women"}}, {"composableButtonData": {"children": "Men"}, "linkData": {"to": "/browse/category.do?cid=11174&mlink=5151,1,HP_NA", "title": "Men"}}, {"composableButtonData": {"children": "Girls"}, "linkData": {"to": "/browse/category.do?cid=6036&mlink=5151,1,HP_NA", "title": "Girls"}}, {"composableButtonData": {"children": "Boys"}, "linkData": {"to": "/browse/category.do?cid=5918&mlink=5151,1,HP_NA", "title": "Boys"}}, {"composableButtonData": {"children": "Toddler Girls"}, "linkData": {"to": "/browse/category.do?cid=6825#pageId=0&department=165&mlink=5151,1,HP_NA", "title": "Toddler Girls"}}, {"composableButtonData": {"children": "Toddler Boys"}, "linkData": {"to": "/browse/category.do?cid=6157#pageId=0&department=166&mlink=5151,1,HP_NA", "title": "Toddler Boys"}}, {"composableButtonData": {"children": "Baby Girls"}, "linkData": {"to": "/browse/category.do?cid=37505#pageId=0&department=165&mlink=5151,1,HP_NA", "title": "Baby Girls"}}, {"composableButtonData": {"children": "Baby Boys"}, "linkData": {"to": "/browse/category.do?cid=37508#pageId=0&department=166&mlink=5151,1,HP_NA", "title": "Baby Boys"}}, {"composableButtonData": {"children": "Maternity"}, "linkData": {"to": "/browse/category.do?cid=8454&mlink=5151,1,HP_NA", "title": "Maternity"}}]}}}, {"instanceName": "shop-now-cta-mobile", "name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"style": {"position": "relative"}, "desktopStyle": {"display": "none"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"margin": "0 auto", "justifyContent": "center", "alignItems": "center", "textAlign": "center", "& ul": {"zIndex": "200"}, "& > a": {"fontSize": "0.9rem", "height": "44px", "letterSpacing": "normal", "backgroundColor": "#fff", "width": "90%", "margin": "0 auto 7.5px", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "@media (min-width: 768px)": {"height": "48px"}}, "& ul a": {"fontSize": "0.8735rem"}, "& > a:first-child": {"fontSize": "0.9rem", "marginTop": "0"}, "& > div": {"width": "90%", "margin": "0 auto 7.5px"}, "& button": {"padding": "0.625rem", "fontSize": "0.9rem", "width": "100%", "margin": "0"}}, "desktopStyle": {"position": "absolute", "top": "0", "maxWidth": "1440px", "left": "50%", "transform": "translate(-50%, 0)", "zIndex": "33", "margin": "0 auto", "& > a": {"backgroundColor": "#fff", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem 0.5rem", "whiteSpace": "normal", "lineHeight": "1", "width": "378px", "height": "48px", "margin": "0"}, "& > div": {"width": "100%", "margin": "0"}, "& button": {"width": "378px", "height": "48px"}}, "className": "", "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop Now"}, "submenu": [{"href": "/browse/category.do?cid=10018&mlink=5151,1,HP_NA", "trackingId": "HP_NA_W", "text": "Women"}, {"href": "/browse/category.do?cid=11174&mlink=5151,1,HP_NA", "trackingId": "HP_NA_M", "text": "Men"}, {"href": "/browse/category.do?cid=6036&mlink=5151,1,HP_NA", "trackingId": "HP_NA_G", "text": "Girls"}, {"href": "/browse/category.do?cid=5918&mlink=5151,1,HP_NA", "trackingId": "HP_NA_B", "text": "Boys"}, {"href": "/browse/category.do?cid=6825&#pageId=0&department=165&mlink=5151,1,HP_NA", "trackingId": "HP_NA_G", "text": "Toddler Girls"}, {"href": "/browse/category.do?cid=6157&#pageId=0&department=166&mlink=5151,1,HP_NA", "trackingId": "HP_NA_TB", "text": "Toddler Boys"}, {"href": "/browse/category.do?cid=37505&#pageId=0&department=165&mlink=5151,1,HP_NA", "trackingId": "HP_NA_G", "text": "Baby Girls"}, {"href": "/browse/category.do?cid=37508&#pageId=0&department=166&mlink=5151,1,HP_NA", "trackingId": "HP_NA_BB", "text": "Baby Boys"}, {"href": "/browse/category.do?cid=8454&mlink=5151,1,HP_NA", "trackingId": "HP_NA_W", "text": "Maternity"}], "style": {"desktop": {"backgroundColor": "#fff", "margin": "0 auto", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem", "whiteSpace": "normal", "lineHeight": "1", "width": "95%"}, "mobile": {"backgroundColor": "#fff", "width": "90%", "margin": "0 auto", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764"}}}}]}}}]}}, {"instanceName": "amplience-secondary-banner-en", "experimentRunning": true, "name": "LayeredContentModule", "type": "sitewide", "data": {"defaultHeight": {"small": "58px", "large": "62px"}, "background": {"linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,HP_Convenience_a"}, "image": {"alt": "Get it today with these fast and worry free options. Quick and easy in-store pickup.  Free returns and exchanges.", "srcUrl": "/Asset_Archive/ONWeb/content/0029/695/178/assets/221226_NS_N3298_Convenience2023_HP_2nd_CA_SM.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0029/695/178/assets/221226_NS_N3298_Convenience2023_HP_2nd_CA_XL.svg", "style": {"display": "block"}}}, "container": {"style": {"position": "relative", "maxWidth": "1440px", "margin": "0 auto"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"margin": "0 auto", "justifyContent": "center", "alignItems": "center", "textAlign": "center", "& > a": {"fontSize": "0.9rem", "height": "44px", "letterSpacing": "normal", "backgroundColor": "#fff", "width": "90%", "margin": "0 auto 7.5px", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764"}, "& > a:first-child": {"marginTop": "1rem"}, "& button": {"padding": "0.625rem 0", "fontSize": "0.9rem", "marginBottom": "2%"}}, "desktopStyle": {"position": "relative", "width": "60%", "maxWidth": "1440px", "zIndex": "33", "margin": "1rem auto 0", "& > a": {"backgroundColor": "#fff", "border": "solid 2px #003764", "borderRadius": "8px", "color": "#003764", "padding": "0.5rem 0.5rem", "whiteSpace": "normal", "lineHeight": "1", "height": "48px", "width": "45%", "margin": "0"}, "& > a:first-child": {"marginTop": "0"}, "& > div": {"width": "45%", "margin": "0"}, "& button": {"height": "48px", "width": "100%", "marginBottom": "0"}, "& ul": {"minWidth": "100%", "width": "100%", "left": "50%", "transform": "translate(-50%, 0)"}, "@media (min-width: 768px) and (max-width: 1200px)": {"width": "80%"}}, "ctas": [{"composableButtonData": {"children": "Start Shopping", "style": {}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=10018&mlink=5151,1,HP_Convenience_b"}}]}}}, {"instanceName": "amplience-on-hp-legal-en", "experimentRunning": true, "name": "TextHeadline", "type": "sitewide", "data": {"text": "Select styles only. While supplies last. For a limited time.\nOnline & in-store prices and exclusions may vary.", "defaultHeight": "34px", "style": {"mobile": {"whiteSpace": "inherit", "margin": "2em 1em", "fontSize": ".75rem", "textAlign": "center", "fontWeight": "400", "color": "#666", "lineHeight": "1.38"}, "desktop": {"marginTop": "2rem", "fontSize": ".75rem", "textAlign": "center", "fontWeight": "400", "color": "#666", "lineHeight": "1.38"}}, "className": {"mobile": "", "desktop": ""}}}]}, "sitewide": {"desktopemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"sitewide-desktopemergencybanner-ciid": "28004566", "sitewide-ciid_locale": "en_CA", "instanceName": "dpg_emergency_banner_desk_en", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "useGreyLoadingEffect": false, "defaultHeight": "0px", "desktop": {"height": 0, "width": "100%"}, "mobile": {"height": 0, "width": "100%"}, "data": {"placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"backgroundColor": "transparent", "width": 0, "height": 0, "margin": "0 auto"}, "mobile": {"width": 0, "height": 0}}, "excludePageTypes": [], "lazy": false, "defaultHeight": {"large": "0px", "small": "0px"}, "isVisible": {"large": true, "small": false}, "desktop": {"shouldDisplay": true, "data": {"style": {"height": "auto", "maxWidth": "1400px", "width": "100%", "display": "flex", "margin": "0 auto", "justifyContent": "center"}, "components": []}}}}]}}, "mobileemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"sitewide-mobileemergencybanner-ciid": "19024173", "sitewide-ciid_locale": "en_CA", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "instanceName": "dpg_emergency_banner_mob_en", "useGreyLoadingEffect": false, "desktop": {"height": 0, "width": "100%"}, "mobile": {"height": 0, "width": "100%"}, "data": {"placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"backgroundColor": "transparent", "width": 0, "height": 0, "margin": "0 auto"}, "mobile": {"width": 0, "height": 0}}, "excludePageTypes": [], "lazy": false, "defaultHeight": {"large": "0px", "small": "0px"}, "isVisible": {"large": false, "small": true}, "mobile": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "column", "flexWrap": "nowrap", "alignItems": "center", "width": "100%"}, "components": []}}}}]}}, "below-topnav": {"type": "builtin", "name": "div", "data": {"components": [{"name": "div", "type": "builtin", "instanceName": "below-topnav", "experimentRunning": true, "data": {"components": []}}]}}, "headline": {"type": "builtin", "name": "div", "data": {"components": [{"sitewide-ciid_locale": "en_CA", "experimentRunning": true, "useGreyLoadingEffect": false, "name": "LayeredContentModule", "type": "sitewide", "instanceName": "global-banner-022723-EN", "data": {"placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"backgroundColor": "#f5f1e3", "width": 0, "height": 50, "margin": "0 auto"}, "mobile": {"backgroundColor": "#f5f1e3", "width": 0, "height": 50, "margin": "0 auto"}}, "defaultHeight": {"large": "50px", "small": "50px"}, "excludePageTypes": ["ShoppingBag", "CustomerService", "store-service"], "lazy": false, "container": {"className": "", "style": {"backgroundColor": "#f5f1e3"}, "desktopStyle": {"width": "100%", "backgroundColor": "#f5f1e3"}}, "background": {"image": {"alt": "The Old Navy Navyist <PERSON><PERSON><PERSON> logo. Want access to Free Shipping?* Join Navyist <PERSON><PERSON><PERSON>. Details.", "srcUrl": "/Asset_Archive/ONWeb/content/0029/690/397/assets/220914_NS_N3207_ILP_Promo_GlobalBanner_SM_CA.svg", "desktopSrcUrl": "/Asset_Archive/ONWeb/content/0029/690/397/assets/220914_NS_N3207_ILP_Promo_GlobalBanner_XL_CA.svg", "style": {"display": "block"}}, "linkData": {"title": "Join Now", "to": "/browse/info.do?cid=1095422&locale=en_CA&mlink=5151,GlobalBanner_CanadaILP", "target": "_self"}, "style": {"width": "100%", "display": "block", "padding": "0", "margin": "0 auto"}, "desktopStyle": {"display": "block", "maxWidth": "1440px"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"position": "relative", "alignItems": "center", "maxWidth": "1440px", "margin": "0 auto", "padding": "0"}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "", "src": "/Asset_Archive/ONWeb/content/0029/690/397/assets/legal.html?v=2", "height": "500px"}}, "composableButtonData": {"children": "", "style": {"margin": "0 auto", "color": "#ffffff", "backgroundColor": "transparent", "position": "absolute", "bottom": "2vw", "right": "41%", "width": "11%", "textAlign": "center", "fontSize": "1.8vw", "outline": "none", "fontWeight": "normal", "textTransform": "none", "textDecoration": "underline", "height": "20px"}, "desktopStyle": {"font-size": "11px", "right": "7%", "bottom": "1.2vw", "width": "5%", "padding": "1em"}}}]}}}]}}, "edfslarge": {"type": "builtin", "name": "div", "data": {"components": [{"buildInfo": "amplience", "sitewide-ciid_locale": "en_CA", "type": "sitewide", "name": "MktEdfsLarge", "instanceName": "091422-EN-LDTO-EDFS-large", "experimentRunning": false, "meta": {"lazy": false}, "data": {"text": "REWARDS MEMBERS GET ACCESS TO FREE SHIPPING", "detailsLink": "DETAILS", "modalCloseButtonAriaLabel": "Close Pop-Up", "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=3019&locale=en_CA", "signInCta": {"text": "SIGN IN OR JOIN", "style": {}}}}]}}, "edfssmall": {"type": "builtin", "name": "div", "data": {"components": [{"sitewide-ciid_locale": "en_CA", "type": "sitewide", "name": "MktEdfsSmall", "instanceName": "edfs-small-amplience-en", "experimentRunning": true, "meta": {"lazy": false}, "data": {"text": "REWARDS MEMBERS GET ACCESS TO FREE SHIPPING ", "detailsLink": "DETAILS", "styles": {"headline": {"fontSize": "9px"}, "container": {"backgroundColor": "#fff"}}, "modalCloseButtonAriaLabel": "Close Pop-Up", "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=3019&locale=en_CA", "signInCta": {"text": " SIGN IN OR JOIN", "style": {"fontSize": "9px", "letterSpacing": "0", "padding": "0 0 2px 4px", "@media (min-width: 768px) and (max-width: 1024px)": {"padding": "3px 0 2px 4px", "color": "#999999"}, "@media (max-width: 767px)": {"display": "inline"}}}}}]}}, "hamburgerNavBanner": {}, "popup": {"type": "builtin", "name": "div", "data": {"components": [{"type": "sitewide", "name": "OptimizelyPlaceholder", "instanceName": "021823-email-popup", "experimentRunning": true, "data": {"defaultHeight": {"large": "0", "small": "0"}}}]}}, "promorover": {"type": "builtin", "name": "div", "data": {"components": [{"buildInfo": "amplience", "sitewide-ciid_locale": "en_CA", "name": "MktSticker", "type": "sitewide", "experimentRunning": false, "instanceName": "promo_sticker_08-12", "data": {"shouldWaitForOptimizely": false, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": 0, "width": 0}, "mobile": {"height": 0, "width": 0}}, "href": "https://oldnavy.gapcanada.ca/profile/info.do?cid=1152078&mlink=1,1,<PERSON><PERSON>_<PERSON>ailAcq", "hrefTarget": "_blank", "largeImg": "/Asset_Archive/ONWeb/content/0019/024/183/assets/200803_NS_N0478_EmailAcqSticker_CA_close.svg", "altText": "Join our email fam to get 30% off your purchase! Join now", "isVisible": {"small": false, "large": true}, "modalCloseButtonAriaLabel": true, "localStorageKey": "wcd_onRoverStorage_081220", "localStorageVal": "ONroverHasBeenClosed_081220", "options": {"excludePageTypes": ["home", "division", "product", "ShoppingBag", "Information", "info", "CustomerService", "store-service", "profile", "dynamicerror", ""]}}}]}}, "prefooter": {"type": "builtin", "name": "div", "data": {"components": [{"buildInfo": "amplience", "type": "sitewide", "name": "LayoutComponent", "instanceName": "CA_fsrewardbanner", "experimentRunning": false, "data": {"desktopAndMobile": {"shouldDisplay": true, "style": {"position": "relative", "display": "content"}, "data": {"style": {"position": "relative", "display": "content"}, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"lazy": false, "defaultHeight": "0px", "html": "<style>@media screen and (min-width: 768px) { .footerCol .body-a, .footerCol .body-a span { font-size: 16px !important; } .footer-legal__wrapper .footer_copyright-row { font-size: 0.8rem !important; } .cda-on-logo { color: transparent !important; } .on-ca-support-links .footerCol:nth-child(3) .columnContainer .header .onfLinks span { font-style: italic !important; } .on-ca-support-links .footerCol .columnContainer .header { height: 2em !important; } .desktop-footer-container { margin-bottom: 0 !important; } }</style> <style> .checkout-container {display: flex; flex-direction: row; max-width: 1400px; align-items: center; margin: 2em auto} .width100atXL {width: 100%} .width50atXL {width: 50%} .four-brands {text-align: right} @media screen and (min-width: 768px) and (max-width: 1022px) { .logos-details { bottom: 3.25em !important}} @media screen and (min-width: 1023px) { .logos-details { bottom: 42px !important}} @media screen and (min-width: 768px) { .pl50 {padding-left: 50%} .pl40 {padding-left: 40%} } @media screen and (min-width: 768px) and (max-width: 1250px) { .pl20 {padding-left: 20%} .pl10 {padding-left: 10%} .brand-logos-wrapper img { padding-left: 20px; width: 85%}} @media screen and (max-width: 767px) { .brand-logos-wrapper {text-align: center; margin-top: 1em} .brand-logos {margin-top: 1em; width: 70%; margin: auto} .four-brands {text-align: center} .checkout-container {flex-direction: column} .width100atSM {width: 100%} .width50atSM {width: 50%}} .on-footer a.sitewide-0:hover { text-decoration: underline !important; }</style> <div class='checkout-container' style='position: relative; display: flex;'><div class='width50atXL width100atSM four-brands' style='position: relative;'><div class='pl40 pl10'><div 'class='width100atXL width100atSM' style='text-align: center; font-size: 1.25em; font-weight: bold; margin-bottom: 10px'>4 BRANDS 1 EASY CHECKOUT</div><div 'class='width100atXL width100atSM' style='text-align: center; font-weight: bold; font-size: .9em;'>REWARDS MEMBERS GET ACCESS TO FREE SHIPPING<br/><span><a style='text-decoration: underline; font-weight: bold; font-size: .9em; z-index: 2; position: relative' target='blank' href='/my-account/sign-in?mlink=5151,1,FTR_BannerSignIn&locale=en_CA'>Sign in</a></span> or <span><a style='text-decoration: underline; font-weight: bold; font-size: .9em; z-index: 2; position: relative' target='blank' href='/my-account/sign-in?mlink=5151,1,FTR_BannerJoin&targetURL=/browse/home.do&locale=en_CA'>Join</a></span> <span><a style='text-decoration: underline; color: gray; font-size: .75em; padding-left: 3px'>DETAILS</a></span></div></div></div><div class='width50atXL width100atSM brand-logos-wrapper' style='position: relative; max-width: 400px; padding-left: 0px'><img class='brand-logos' src='/Asset_Archive/ONWeb/content/static-marketing/footer-assets/EDFS_Footer.svg' /></div></div></div>"}}, {"name": "LayeredContentModule", "type": "sitewide", "instanceName": "brand-logos-popup", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"position": "relative"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"position": "absolute", "top": 0, "height": "100%", "width": "100%", "display": "flex", "justifyContent": "center", "alignItems": "center"}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "SHIPPING & RETURNS", "src": "/customerService/info.do?cid=3019&locale=en_CA", "height": "500px"}}, "composableButtonData": {"children": "details", "className": "logos-details", "style": {"fontSize": "3vw", "color": "transparent", "backgroundColor": "transparent", "position": "absolute", "margin-top": "-43%", "right": "30%", "width": "10%"}, "desktopStyle": {"margin-top": "0", "right": "0", "width": "50%"}}}]}}}, {"name": "LayeredContentModule", "type": "sitewide", "instanceName": "4brands-details-popup", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"position": "relative", "zIndex": "1"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"position": "absolute", "top": 0, "height": "100%", "width": "50%", "display": "flex", "justifyContent": "center", "alignItems": "center", "zIndex": "1"}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "SHIPPING & RETURNS", "src": "/customerService/info.do?cid=3019&locale=en_CA", "height": "500px"}}, "composableButtonData": {"children": "DETAILS", "className": "details-link", "style": {"fontSize": "4vw", "color": "transparent", "backgroundColor": "transparent", "margin-top": "-50%", "width": "150%", "left": "25%", "right": "auto", "position": "absolute"}, "desktopStyle": {"fontSize": ".75em", "margin-top": "-83px", "left": "auto", "height": "50px", "right": "0", "width": "100%", "zIndex": "1"}}}]}}}]}}}}]}}, "appsflyer-smart-banner": {"type": "builtin", "name": "div", "data": {"components": []}}, "footer": {"buildInfo": "amplience", "sitewide-footer-ciid_locale": "en_CA", "type": "sitewide", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instanceName": "<PERSON><PERSON>_<PERSON><PERSON>", "experimentRunning": false, "components": [{"name": "Footer", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "287px", "emailRegistration": {"disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "oldnavycanada", "locale": "en_CA", "data": {"style": {}, "html": "Yes! I would like to receive style news and exclusive offers from Gap Inc. and related companies and brands including Gap (Canada) Inc., Old Navy (Canada) Inc., Banana Republic and Athleta. You can withdraw consent at any time. <br><br>FOR MORE DETAILS SEE OUR <a href=\"https://www.gapinc.com/en-ca/consumer-privacy-policy\" target=\"_blank\"><u>PRIVACY POLICY</u></a> OR <a href=\"/customerService/info.do?cid=3332&locale=en_CA&mlink=5151,1,Footer_Contact_Us\" target=\"_blank\" onclick=\"return mkt_contentClickTracking( this, 'Footer_Email_Contact_Us' );\"><u>CONTACT US</u></a>."}}}, "carousel": {"prevArrow": "", "nextArrow": "", "slides": [{"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0 2rem", "margin": "0px auto 5% auto", "width": "75%"}, "desktop": {"padding": "0 0", "backgroundColor": "#fff", "marginTop": "5%"}}, "background": {"content": {"smallImg": "/Asset_Archive/ONWeb/content/0029/325/573/assets/220500_32-M6325_IM_HP_Footer_USCA.svg", "largeImg": "/Asset_Archive/ONWeb/content/0029/325/573/assets/220500_32-M6325_IM_HP_Footer_USCA.svg", "altText": "Imagine a better future. Creating a better future for generations. Learn More."}, "style": {"desktop": {}, "mobile": {"height": "100%", "width": "90%", "margin": "0 auto"}}}, "svgoverlay": {"smallImg": "/Asset_Archive/ONWeb/content/0029/325/573/assets/220500_32-M6325_IM_HP_Footer_USCA.svg", "largeImg": "/Asset_Archive/ONWeb/content/0029/325/573/assets/220500_32-M6325_IM_HP_Footer_USCA.svg", "altText": "Imagine a better future. Creating a better future for generations. Learn More.", "link": {"url": "/browse/info.do?cid=1160383&mlink=5151,1,footer_imaginemission", "tid": "footer_imaginemission"}}}}, {"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0 2rem", "margin": "0px auto 5% auto", "width": "75%"}, "desktop": {"padding": "0 0", "backgroundColor": "#fff", "marginTop": "5%"}}, "background": {"content": {"smallImg": "/Asset_Archive/ONWeb/content/0019/909/140/assets/WE_HP_FootCarousel_US_240x277.svg", "largeImg": "/Asset_Archive/ONWeb/content/0019/909/140/assets/WE_HP_FootCarousel_US_240x277.svg", "altText": "We are change"}, "style": {"desktop": {}, "mobile": {"height": "100%", "width": "90%", "margin": "0 auto"}}}, "svgoverlay": {"smallImg": "/Asset_Archive/ONWeb/content/0019/909/140/assets/WE_HP_FootCarousel_US_240x277.svg", "largeImg": "/Asset_Archive/ONWeb/content/0019/909/140/assets/WE_HP_FootCarousel_US_240x277.svg", "altText": "We are change", "link": {"url": "/customerService/info.do?cid=1160233&mlink=5151,1,footer_wearechange", "tid": "footer_wearechange"}}}}]}, "footerCustomerSupport": {"desktop": {"columns": [{"header": {"text": "CUSTOMER SERVICE", "className": "", "link": ""}, "links": [{"type": "link", "text": "Customer Service", "to": "/customerService/info.do?cid=3171&mlink=5151,1,Footer_Customer_Service", "className": "body-a onfLinks"}, {"type": "link", "text": "Easy Returns", "to": "/customerService/info.do?cid=3325&mlink=5151,1,Footer_Easy_Returns", "className": "body-a onfLinks"}, {"type": "link", "text": "Gift cards", "to": "/browse/info.do?cid=35433&mlink=5151,1,Footer_Gift_Cards", "className": "body-a onfLinks"}, {"type": "link", "text": "Privacy Policy", "to": "https://www.gapinc.com/en-ca/consumer-privacy-policy?mlink=5151,1,Footer_Privacy_Policy", "className": "body-a onfLinks", "target": "_blank"}, {"type": "link", "text": "Terms of Use", "to": "/customerService/info.do?cid=3319&mlink=5151,1,Footer_Terms_of_Use", "className": "body-a onfLinks", "style": {}}]}, {"header": {"text": "LEARN MORE", "className": "", "link": ""}, "links": [{"type": "link", "text": "Super Cash", "to": "/customerService/info.do?cid=56526&mlink=5151,1,Footer_Super_Cash", "className": "body-a onfLinks"}, {"type": "link", "text": "Sustainability", "to": "/browse/info.do?cid=1130006&mlink=5151,1,Footer_Sustainability", "className": "body-a onfLinks", "style": {}}, {"type": "link", "text": "Site index", "to": "/products/index.jsp?mlink=5151,1,Footer_Site_Index", "className": "body-a onfLinks", "style": {}}]}, {"header": {"text": "on logo", "className": "cda-on-logo", "link": "", "style": {"color": "transparent", "background": "url('/Asset_Archive/ONWeb/content/0029/564/552/assets/Navyist_logo.svg') no-repeat;", "backgroundSize": "3.25em"}}, "links": [{"type": "link", "text": "My Points and Rewards", "to": "/my-account/sign-in?targetURL=/loyalty/customer-value", "className": "body-a onfLinks", "style": {}}, {"type": "link", "text": "Explore Benefits", "to": "/browse/info.do?cid=1095422&mlink=5151,1,FTR_Loyalty_Benefits", "className": "body-a onfLinks", "style": {}}, {"type": "link", "text": "Join <PERSON><PERSON> <PERSON><PERSON> - it’s Free", "to": "/my-account/sign-in", "className": "body-a onfLinks", "style": {}}]}, {"header": {"text": "FIND US", "className": "", "link": ""}, "links": [{"type": "link", "text": "Email sign-up", "to": "/profile/info.do?cid=82635&src=Site_TextFooter&mlink=5151,1,Footer_Email_Sign_Up", "className": "body-a onfLinks"}, {"type": "link", "text": "Store Locator", "to": "/stores?locale=en_CA&mlink=5151,1,Footer_Store_Locator", "className": "body-a onfLinks"}, {"type": "link", "text": "Contact Us", "to": "/customerService/info.do?cid=3332&locale=en_CA&mlink=5151,1,Footer_Contact_Us", "className": "body-a onfLinks"}]}]}, "mobile": {"links": [{"type": "link", "text": "Store Locator", "to": "/stores?locale=en_CA&mlink=5151,1,Footer_Store_Locator", "className": "footer-item"}, {"type": "link", "text": "Customer Service", "to": "/customerService/info.do?cid=3171&mlink=5151,1,Footer_Customer_Service", "className": "footer-item"}, {"type": "link", "text": "Orders & Returns", "to": "/customerService/info.do?cid=3325&mlink=5151,1,Footer_Order_Returns", "className": "footer-item"}, {"type": "link", "text": "Gift Cards", "to": "/browse/info.do?cid=35433&mlink=5151,1,Footer_Gift_Cards", "className": "footer-item"}, {"type": "accordion", "text": "Navy<PERSON> <PERSON><PERSON><PERSON>", "className": "footer-item", "accordionLinks": [{"type": "link", "text": "My points and rewards", "to": "/my-account/sign-in?targetURL=/loyalty/customer-value", "className": "footer-line"}, {"type": "link", "text": "Explore benefits", "to": "/browse/info.do?cid=1095422&mlink=5151,1,FTR_Loyalty_Benefits", "className": "footer-line"}, {"type": "link", "text": "Join <PERSON><PERSON><PERSON> - It's Free", "to": "/my-account/sign-in", "className": "footer-line"}]}, {"type": "accordion", "text": "Shop our other brands", "className": "footer-item", "accordionLinks": [{"type": "link", "text": "Gap", "to": "https://www.gapcanada.ca/?ssiteID=on", "className": "footer-line"}, {"type": "link", "text": "Banana Republic", "to": "https://bananarepublic.gapcanada.ca/?ssiteID=on", "className": "footer-line"}, {"type": "link", "text": "Athleta", "to": "https://athleta.gapcanada.ca/?ssiteID=on", "className": "footer-line"}]}]}}, "copyRights": {"rows": [[{"text": "© 2022 The Gap, Inc."}, {"to": "https://www.gapinc.com/en-ca/consumer-privacy-policy", "text": "Privacy Policy"}, {"text": "1-800-OLD-NAVY (1.800.653.6289)"}, {"to": "https://jobs.gapinc.com/old-navy-home", "text": "Careers"}], [{"to": "/customerService/info.do?cid=3331&mlink=5151,********,Footer_Disabilities_Act&clink=********", "text": "Accessibility for Ontarians with Disabilities Act", "style": {"font-weight": "700"}}, {"to": "/customerService/info.do?cid=3008234&mlink=5151,********,Footer_Disabilities_Act&clink=********", "text": "The Accessibility for Manitobans Act", "style": {"font-weight": "700"}}]]}, "socialLinks": []}}]}, "topnav": {"buildInfo": "amplience", "sitewide-ciid_locale": "en_CA", "sitewide-topnav-desc": "08/25 updates", "type": "sitewide", "name": "MegaNav", "instanceName": "meganav-071023-EN", "data": {"isNavSticky": true, "classStyles": {"divisionLink._selected ": "color: #003764;", "meganav:not(.custom) .catnav--header > span ": "margin-top: 12px", "meganav:not(.custom) .catnav--header > a ": "margin-top: 12px", "meganav .wcdNavLimit .meganav-column": "display:inline-block;", "meganav-category-group.MatchingGiftsfortheFamily": "margin-bottom:0px;width:100%;float:left;", "meganav .catnav--header": "float:left;letter-spacing:0px;", "topnav .divisionLink": "font-size:min(max(calc(0.7rem + ((1vw - 10.24px) * 0.6696))), 18px) !important; letter-spacing: .7px !important; text-transform: capitalize !important; font-weight: 400; color: #000 !important", "topnav .divisionLink._selected": "font-weight: bold !important; color: #003764 !important", "topnav .divisionLink span": "font-size: 11px !important; display: flex; vertical-align: middle; padding-bottom: 3px; font-weight:400;", "topnav a.divisionLink:hover": "font-weight: bold !important; color: #003764 !important", "topNavLink:nth-child(8) .wcdNavLimit, .topNavLink:nth-child(9) .wcdNavLimit": "position:relative;overflow-x:hidden;", "wcdNavLimit ul.custom": "margin-bottom:40px;", "topNavLink .wcdNavLimit ul.custom .division-header": "position:absolute;background-color:#f7f7f7;font-weight:700;color:#000000;font-size:.875rem;padding:.5em 0 .5em .5em;", "topNavLink .wcdNavLimit ul.custom .division-header span": "font-weight:400;", "meganav .catnav--header a": "color: #000000;", "meganav .catnav--header a:hover": "color: #003764 !important;", "meganav.wcd-new .catnav--item a:hover": "color: #003764 !important; border-bottom: 1px solid #003764", "meganav.wcd-new .catnav--item span": "padding-bottom: 2px", "meganav.wcd-new .catnav--header > a::after": "content: ' >'", "meganav.wcd-gifts .catnav--header > a::after": "content: ' >'", "meganav.wcd-women .catnav--header > a::after": "content: ' >'", "meganav.wcd-men .catnav--header > a::after": "content: ' >'", "meganav.wcd-girls .catnav--header > a::after": "content: ' >'", "meganav.wcd-boys .catnav--header > a::after": "content: ' >'", "meganav.wcd-toddler .catnav--header > a::after": "content: ' >'", "meganav.wcd-baby .catnav--header > a::after": "content: ' >'", "meganav.wcd-maternity .catnav--header > a::after": "content: ' >'", "meganav.wcd-jeans .catnav--header > a::after": "content: ' >'", "meganav.wcd-familyoutfits .catnav--header > a::after": "content: ' >'", "meganav.wcd-sale .catnav--header > a::after": "content: ' >'", "meganav.wcd-masks .catnav--header > a::after": "content: ' >'", "meganav.wcd-familymatching .catnav--header > a::after": "content: ' >'", "meganav.wcd-familypajamas .catnav--header > a::after": "content: ' >'", "meganav.wcd-todaysdeals .catnav--header > a::after": "content: ' >'", "meganav.wcd-women .wcdNavLimit .meganav-column:nth-child(3)": "width: 235px", "meganav.wcd-toddler .meganav-column": "width: 230px", "meganav.wcd-baby .meganav-column": "width: 215px", "meganav.wcd-jeans .meganav-column": "width: 200px", "topNavLink .wcd-toddler .wcdNavLimit ul.custom .division-header": "width:30.5%; max-width: 485px", "topNavLink .wcd-baby .wcdNavLimit ul.custom .division-header": "width:28.5%; max-width: 460px", "meganav .dp-tile-ctas": "font-weight: bold; text-transform: capitalize; line-height: 1.5; font-size: 16px; letter-spacing: .5px", "meganav a.dp-tile-ctas:hover": "color: #003764; text-decoration: underline;", "meganav .todd-baby.dp-tile-ctas": "font-size: 14px; white-space: pre-line; line-height: 0.5;", "meganav .toddler-baby.catnav--item": "padding: 50px 0 0 0 !important;", "topNavLink a[aria-label=\"today's deals!\"]": "color: red!important", "meganav a[aria-label=\"new & now family pajamas\"]": "color: red!important", "meganav a[aria-label=\"new & now  family pajamas\"]": "color: red!important", "meganav a[aria-label$=\"holiday outfits\"]": "color: red!important", "meganav a[aria-label*='stocking stuffers'] span::after": "content: ' 🎁'", "meganav a[aria-label*='stocking stuffers'] span": "color: red!important", "meganav a[aria-label*='gifts stocking stuffers'] span": "color: #000000!important", "meganav a[aria-label*='gifts holiday outfits'] span": "color: #000000!important"}, "activeDivisions": [{"name": "Women", "subtitle": "Sizes 0-30 & XS-4X", "divisionId": ["/browse/division.do?cid=5360&mlink=5151,topNav,visnav", "5360"], "megaNavOrder": [["1016051", "1039280"], ["1036191"], ["55182", "1182446"], ["1019570", "1176447"], ["<ul class='catnav-links'><li class='dp catnav--item'><a data-categoryid='1184818' href='/browse/category.do?cid=1184818&mlink=5151,w_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='https://oldnavyprod.a.bigcontent.io/v1/static/231001_51-M9167_W_MegaNav' alt='Two models wearing fair isle cardigan sweater, beanies, jeans and holding lots of presents.'></span></a></li></ul><ul><li><a class='dp-tile-ctas' href='/browse/category.do?cid=1184818&mlink=5151,w_mnav_tile'>Shop Gifts ></a></li></ul>"]], "numberOfColumns": {"1036191": 1}, "exclusionIds": [], "customStyles": {"26190": {"colorScheme": "sale"}, "96964": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Men", "divisionId": ["/browse/division.do?cid=5155&mlink=5151,topNav,visnav", "5155"], "megaNavOrder": [["48385", "55197", "1039290"], ["1036209"], ["1031097", "1145656"], ["<ul class='catnav-links'><li class='dp catnav--item'><a data-categoryid='38529' href='/browse/category.do?cid=38529&mlink=5151,m_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='https://oldnavyprod.a.bigcontent.io/v1/static/231001_51-M9168_M_MegaNav' alt='A men model wearing red tartan pajama pants, sweater, soft brushed topcoat and winter accessories.'></span></a></li></ul><ul><li><a class='dp-tile-ctas' href='/browse/category.do?cid=38529&mlink=5151,m_mnav_tile'>Shop Pajamas ></a></li></ul>"]], "numberOfColumns": {"1036209": 1}, "exclusionIds": ["1121202"], "customStyles": {"26061": {"colorScheme": "sale", "inlineStyle": {"text-transform": "uppercase"}}, "97035": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Girls", "divisionId": ["/browse/division.do?cid=6027&mlink=5151,topNav,visnav", "6027"], "megaNavOrder": [["1119128", "48395", "54857", "1092613"], ["1036216"], ["1007069", "1125471", "1015504"], ["<ul class='catnav-links'><li class='dp catnav--item'><a data-categoryid='6100' href='/browse/category.do?cid=6100&mlink=5151,g_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='https://oldnavyprod.a.bigcontent.io/v1/static/231001_51-M9169_G_Meganav_2X' alt='Two young girls wearing gender-neutral printed pajama sets.'></span></a></li></ul><ul><li><a class='dp-tile-ctas' href='/browse/category.do?cid=6100&mlink=5151,g_mnav_tile'>Shop Pajamas ></a></li></ul>"]], "numberOfColumns": {"1036216": 1}, "exclusionIds": ["1121223"], "customStyles": {"26175": {"colorScheme": "sale"}, "96906": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Boys", "divisionId": ["/browse/division.do?cid=5910&mlink=5151,topNav,visnav", "5910"], "megaNavOrder": [["1119129", "48396", "54865", "1071417"], ["1036210"], ["1006914", "1125295", "1015530"], ["<ul class='catnav-links'><li class='dp catnav--item'><a data-categoryid='36319' href='/browse/category.do?cid=36319&mlink=5151,b_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='https://oldnavyprod.a.bigcontent.io/v1/static/231001_51-M9170_B_Meganav_2X' alt='Two young models wearing gender-neutral printed pajama sets.'></span></a></li></ul><ul><li><a class='dp-tile-ctas' href='/browse/category.do?cid=36319&mlink=5151,b_mnav_tile'>Shop Pajamas ></a></li></ul>"]], "numberOfColumns": {"1036210": 1}, "exclusionIds": ["1121192"], "customStyles": {"26073": {"colorScheme": "sale"}, "96945": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=6241&mlink=5151,topNav,visnav", "6241"], "megaNavOrder": [["<div class='toddlergirls-division-header division-header'>TODDLER GIRLS <span>(12M-6T)</span></div>", "1017563", "54835", "1073646"], ["<div class='division-header-spacer'></div>", "1073647"], ["<div class='toddlerboys-division-header division-header'>TODDLER BOYS <span>(12M-6T)</span></div>", "1071067", "1040818", "1073756"], ["<div class='division-header-spacer'></div>", "1073744"], ["<ul class='catnav-links'><li class='dp catnav--item'><a data-categoryid='6297' href='/browse/category.do?cid=6297&mlink=5151,tg_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='https://oldnavyprod.a.bigcontent.io/v1/static/231001_51-M9175_Todd_Meganav_2X' alt='Toddler girl and boy wearing unisex snug-fit printed pajama set.'></span></a></li></ul><ul><li><a class='dp-tile-ctas' href='/browse/category.do?cid=6297&mlink=5151,tg_mnav_tile'>Shop Toddler Girls Pajamas ></a></li><li><a class='dp-tile-ctas' href='/browse/category.do?cid=34722&mlink=5151,tb_mnav_tile'>Shop Toddler Boys Pajamas ></a></li></ul>"]], "exclusionIds": ["1071074", "1071075", "1121187", "1121188"], "customStyles": {"26619": {"colorScheme": "sale"}, "26785": {"colorScheme": "sale"}, "53699": {"colorScheme": "sale"}, "97017": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Baby", "divisionId": ["/browse/division.do?cid=51375&mlink=5151,topNav,visnav", "51375"], "megaNavOrder": [["<div class='toddlerboys-division-header division-header'>BABY GIRLS <span>(0-24M)</span></div>", "1017553", "60396", "1077947"], ["<div class='division-header-spacer'></div>", "1077949"], ["<div class='toddlerboys-division-header division-header'>BABY BOYS <span>(0-24M)</span></div>", "1077229", "1006541", "1078107"], ["<div class='division-header-spacer'></div>", "1077950"], ["<ul class='catnav-links'><li class='dp catnav--item'><a data-categoryid='51737' href='/browse/category.do?cid=51737&mlink=5151,bg_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='https://oldnavyprod.a.bigcontent.io/v1/static/231001_51-M9180_Baby_Meganav_2X' alt='A mother and baby wearing family matching pajamas.'></span></a></li></ul><ul><li><a class='dp-tile-ctas' href='/browse/category.do?cid=51737&mlink=5151,bg_mnav_tile'>Shop Baby Girls <br/>Pajamas ></a></li><li><a class='dp-tile-ctas' href='/browse/category.do?cid=51738&mlink=5151,bb_mnav_tile'>Shop Baby Boys <br/>Pajamas ></a></li></ul>"]], "exclusionIds": ["1077228", "1077230", "1121207", "1121205"], "customStyles": {"51646": {"colorScheme": "sale"}, "51666": {"colorScheme": "sale"}, "96918": {"colorScheme": "sale"}, "96919": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Maternity", "divisionId": ["/browse/division.do?cid=5758&mlink=5151,topNav,visnav", "5758"], "megaNavOrder": [["55185", "7791", "1065183"], ["1036208"], ["1092435"], ["<ul class='catnav-links'><li class='dp catnav--item'><a data-categoryid='5848' href='/browse/category.do?cid=5848&mlink=5151,mat_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='https://oldnavyprod.a.bigcontent.io/v1/static/231001_51-M9773_Mat_MegaNav' alt='A model wearing maternity full panel wide-leg jeans, basic top and double breasted blazer.'></span></a></li></ul><ul><li><a class='dp-tile-ctas' href='/browse/category.do?cid=5848&mlink=5151,mat_mnav_tile'>Shop Jeans ></a></li></ul>"]], "numberOfColumns": {"1036208": 1}, "exclusionIds": ["1121193"], "customStyles": {"26239": {"colorScheme": "sale", "inlineStyle": {"text-transform": "uppercase"}}, "96921": {"colorScheme": "sale"}, "*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Family Pajamas", "divisionId": ["/browse/category.do?cid=1189220&&mlink=5151,topNav,visnav", "1183013"], "megaNavOrder": [["1189249"], ["<ul class='catnav-links'><li class='dp catnav--item'><a data-categoryid='1189220' href='/browse/category.do?cid=1189220&mlink=5151,fam_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='https://oldnavyprod.a.bigcontent.io/v1/static/230901_19-M6098_HolidayJJ_FAM_MegaNav' alt='Image features family wearing matching red tartan plaid Old Navy pajamas.'></span></a></li></ul><ul><li><a class='dp-tile-ctas' href='/browse/category.do?cid=1189220&mlink=5151,fam_mnav_tile'>Shop Family Pajamas ></a></li></ul>"]], "exclusionIds": [""], "customStyles": {"*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Gifts 🎁", "divisionId": ["/browse/category.do?cid=1184818&&mlink=5151,topNav,visnav", "1184813"], "megaNavOrder": [["3024580"], ["<ul class='catnav-links'><li class='dp catnav--item'><a data-categoryid='1184818' href='/browse/category.do?cid=1184818&mlink=5151,gifts_mnav_tile' class='' style='position: relative; display: block; max-width: 183px;'><span><img src='https://oldnavyprod.a.bigcontent.io/v1/static/231001_51-M9283_Gifting_FAM_MegaNav' alt='Two female models wearing Fair Isle sweaters holding stack of red wrapped gifts.'></span></a></li></ul><ul><li><a class='dp-tile-ctas' href='/browse/category.do?cid=1184818&mlink=5151,gifts_mnav_tile'>Shop Gifts For<br/>The Fam ></a></li></ul>"]], "exclusionIds": [""], "customStyles": {"*": {"inlineStyle": {"letter-spacing": "0"}}}}, {"name": "Today's Deals!", "divisionId": ["/browse/category.do?cid=26190&mlink=5151,topNav,visnav", "1183117"], "megaNavOrder": [["1183119", "1183123"], ["1183125"], ["1183128", "1183131"], ["1183143", "1183145"], ["1183146", "1183147"]], "exclusionIds": [""], "customStyles": {"*": {"inlineStyle": {"letter-spacing": "0"}}}}]}}, "promodrawer": {"name": "PromoDrawerComponentV2", "type": "sitewide", "sitewide-promodrawer-ciid": "********", "instanceName": "promoDrawer-en_CA", "experimentRunning": false, "data": {"shouldWaitForOptimizely": false, "buildInfo": ["********", "ON"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "search", "info", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "LoyaltyValueCenter"]}, "autoFire": "scroll", "disabledAutoFirePageTypes": ["category", "product", "Profile"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_four-cta-lop0z7dh {\n  background-color: #fff; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_four-cta-lop0z7dh img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_four-cta-lop0z7dh .pd_four-cta--cta-container {\n  bottom: 10px;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row wrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n  width: 100%;\n  gap: 5px;\n  justify-content: space-around;\n}\n.pd_four-cta-lop0z7dh .pd_four-cta_button {\n  background-color: transparent;\n  border: 1px solid #fff;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 0.625rem;\n  font-weight: 400;\n  min-height: 24px;\n  height: 24px;\n  padding: 5px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 100%;\n  max-width: 142px;\n}\n\n@media only screen and (max-width: 767px) {\n  .pd_four-cta-lop0z7dh .pd_four-cta--cta-container {\n    gap: 3px;\n    bottom: 10px;\n  }\n  .pd_four-cta-lop0z7dh .pd_four-cta_button {\n    min-height: inherit;\n    height: inherit;\n    padding: 2px;\n    max-width: 110px;\n  }\n}\n\n</style>\n\n\n<div class=\"pd_four-cta-lop0z7dh\">\n  <a href=\"/browse/category.do?cid=20408&mlink=5151,1,PD_1\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"https://oldnavyprod.a.bigcontent.io/v1/static/231101_70P-M9739_2DWXC50OffALLSweaters_TOD_Site_PD_2x\" alt=\"2 days only! In-store and online, ends 11/18. 50% off all sweaters for the fam. \">\n  </a>\n  <div class=\"pd_four-cta--cta-container\">\n    <a href=\"/browse/category.do?cid=20408&mlink=5151,1,PD_1_a\" class=\"pd_four-cta_button\">Women</a>\n    <a href=\"/browse/category.do?cid=63315&mlink=5151,1,PD_1_b\" class=\"pd_four-cta_button\">Men</a>\n    <a href=\"/browse/category.do?cid=41748&mlink=5151,1,PD_1_c\" class=\"pd_four-cta_button\">Girls</a>\n    <a href=\"/browse/category.do?cid=41893&mlink=5151,1,PD_1_d\" class=\"pd_four-cta_button\">Boys</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "50% Off All Sweaters for the Fam: Offer valid 11/17/23 - 11/18/23 at Old Navy stores in Canada and at Old Navy online. Excludes Clearance (merchandise ending in $.47, $.96 and $.97) and Maternity. While supplies last. No adjustments on previous purchases. Cannot be combined with other offers or discounts, including Gap Inc. employee discount.\t", "genericCodeId": "", "genericCode": ""}, "promoId": "lop0yi1a"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style type=\"text/css\">\n\t.pd__xxOff-tile-lo35zw14 {\n\t\twidth:100%;\n\t\theight:100%;\n\t\tposition:relative;\n\t\ttext-transform: inherit;\n\t\ttext-align:left;\n\t\tletter-spacing: 0.48px;\n\t\tfont-size: 7px;\n\t}\n\t.pd__xxOff-tile-lo35zw14 .pd__xxOff-tile-content-wrapper {\n\t\twidth:100%;\n\t\tposition:absolute;\n\t\tbox-sizing: border-box;\n\t\tpadding: 8% 3%;\n\t}\n\t.pd__xxOff-tile-lo35zw14 .pd__xxOff-tile-super-title {\n\t\tfont-size:1.71428em;\n\t\tfont-weight:300;\n    margin-bottom: 0.5em;\n\t}\n\t.pd__xxOff-tile-lo35zw14 .pd__xxOff-tile-title {\n\t\tfont-size:3.14285em;\n\t\tline-height:1.2em;\n\t\tletter-spacing: 0.88px;\n\t\tfont-weight: 700;\n\t\ttext-transform: uppercase;\n\t}\n\t.pd__xxOff-tile-lo35zw14 .pd__xxOff-tile-code-text {\n\t\tfont-size: 1.71428em;\n\t}\n\t.pd__xxOff-tile-lo35zw14 .pd__xxOff-tile-exclusion-text {\n\t\tfont-size: 1.71428em;\n\t}\n\t.pd__xxOff-tile-lo35zw14 .pd__xxOff-tile-code-text span {\n\t\tfont-weight:700;\n\t}\n\n\t@media only screen and (max-width: 768px) {\n    .pd__xxOff-tile-lo35zw14 {\n\t\t\tfont-size: 5px;\n    }\n  }\n\t\n</style>\n\n<!-- <a href=\"\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\"> -->\n\t<div class=\"pd__xxOff-tile-lo35zw14\" style=\"background-color:#015a4c; color: #fff8e3\">\n\t\t<div class=\"pd__xxOff-tile-content-wrapper\">\n\t\t\t<div class=\"pd__xxOff-tile-super-title\">Online Exclusive.&nbsp;<span style=\"font-size: 12px; letter-spacing: 0.48px; text-transform: inherit;\">Ends 11/22</span></div>\n\t\t\t<div class=\"pd__xxOff-tile-title\">30% off</div>\n\t\t\t<div class=\"pd__xxOff-tile-title\">your order</div>\n\t\t\t<div class=\"pd__xxOff-tile-title\">even clearance</div>\n\t\t\t<div class=\"pd__xxOff-tile-code-text\">No code needed</div>\n\t\t\t<div class=\"pd__xxOff-tile-exclusion-text\">Exclusions apply</div>\n\t\t</div>\n\t</div>\n<!-- </a> -->", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "discount automatically applied at checkout", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "1001077", "genericCode": ""}, "promoId": "lo35y6xi"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #fff; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=1185965&mlink=5151,1,PD_3\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"https://oldnavyprod.a.bigcontent.io/v1/static/231101_69-M9674_TaylorTrouser_Blazer_TWO_Site_PD_CA_2x\" alt=\"This week only! In-store and online, ends 11/22. $35 Taylor Trouser & $35 Taylor Blazer. Women.\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "$35 Taylor Trouser & $35 Taylor Blazer: Offer valid through 11/22/23 at Old Navy stores in Canada and at Old Navy online. Women’s styles only. Excludes Maternity. Select styles only. While supplies last. No adjustments on previous purchases. Cannot be combined with other offers or discounts, including Gap Inc. employee discount.", "genericCodeId": "", "genericCode": ""}, "promoId": "lo35ywxy"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #fff; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<!-- <a href=\"\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\"> -->\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"https://oldnavyprod.a.bigcontent.io/v1/static/231101_NS-N3846_SMSACQ_Evergreen_Site_PD_CA_2x\" alt=\"Ends 12/1. Sign up for text alerts & get $10 in Super Cash. Text REWARD to 653-681*. Exclusions apply. $10 Super Cash Coupon is redeemable 12/2/23-12/10/23 on purchases of $25 or more. *By signing up via text, you consent to receive recurring automated personalized (e.g. cart reminders) and marketing text messages from Old Navy at the cell phone number used when signing up. Terms & Conditions apply, www.oldnavy.ca/text. Learn more about our privacy practices at www.oldnavy.com/privacy. Consent is not a condition of purchase. Reply HELP for help and STOP to opt out. Msg frequency varies. Msg and data rates apply. You may also contact us by postal mail at Old Navy Canada Customer Service, 9500. McLaughlin, Road North, Brampton, ON, L6X 0B8, Canada.\">\n  </div>\n<!-- </a> -->\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile4"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "*DETAILS", "legalOverride": "*By signing up via text, you consent to receive recurring automated personalized (e.g. cart reminders) and marketing text messages from Old Navy at the cell phone number used when signing up. Terms & Conditions apply, www.oldnavy.ca/text. Learn more about our privacy practices at www.oldnavy.com/privacy. Consent is not a condition of purchase. Reply HELP for help and STOP to opt out. Msg frequency varies. Msg and data rates apply. You may also contact us by postal mail at Old Navy Canada Customer Service, 9500. McLaughlin, Road North, Brampton, ON, L6X 0B8, Canada. SUPER CASH: Offer valid on Old Navy merchandise only from 12/2/23 at 12:00 am PT through 12/10/23 at 11:59 pm PT in Canada at Old Navy stores and online at oldnavy.gapcanada.ca. Valid for one time use only. Not valid on international purchases. Qualifying amount applies to merchandise only, not value of gift cards purchased, packaging, applicable taxes or shipping & handling charges. No adjustments on previous purchases. Not valid for cash or cash equivalent. Cannot be combined with other offers or discounts including Gap Inc. employee discount. Gap Inc. is not responsible for lost or stolen coupons.", "genericCodeId": "", "genericCode": ""}, "promoId": "lmqpjoun"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "text": "{--! headerText !--}", "style": {"mobile": {"fontSize": "0.6875em !important"}, "desktop": {"fontSize": "0.75em !important"}}}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}", "style": {"mobile": {"fontSize": "0.6875em !important"}}}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "subHeaderText": "(4 available)", "iconAltText": "Open icon"}, "closedState": {"headerText": "2 days only! 50% off all sweaters", "subHeaderText": "click for more ways to save", "iconAltText": "Closed icon"}, "aria-label": "2 days only! 50% off all sweaters"}, "pd_id": "pdid_1534541049574"}}, "utilitylinks": {"buildInfo": "amplience", "sitewide-ciid_locale": "en_CA", "type": "sitewide", "name": "UtilityLinks", "meta": {"lazy": false}, "data": {"style": {"color": "#000", "textTransform": "uppercase", "fontSize": "11px", "fontWeight": "bold"}, "brandBarShortcutLinks": [{"link": "/stores", "text": "Find a store"}, {"link": "/browse/info.do?cid=1095422", "text": "Navy<PERSON> <PERSON><PERSON><PERSON>"}, {"link": "/customerService/info.do?cid=35433&mlink=5151,1,utilityLinks_giftcard", "text": "Gift Card"}]}}, "hamnav": {"buildInfo": "amplience", "name": "HamburgerNav", "type": "sitewide", "instanceName": "hamnav-122622", "experimentRunning": false, "data": {"exclusionIds": ["49708", "49709", "1090014", "1091221", "3009759", "3008578", "3009445"]}}, "search": {"buildInfo": "amplience", "desc": "contains search terms for animated search", "type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["Family Pajamas", "Gifts", "Holiday Party Looks", "<PERSON><PERSON>", "Active", "Jackets"]}}, "logo": {"buildInfo": "amplience", "type": "sitewide", "name": "Logo", "lightLogoImgPath": "https://oldnavyprod.a.bigcontent.io/v1/static/221000_13_M4195_<PERSON><PERSON><PERSON><PERSON><PERSON>_HPMasthead_Logo_SM", "darkLogoImgPath": "https://oldnavyprod.a.bigcontent.io/v1/static/221000_13_M4195_<PERSON><PERSON><PERSON><PERSON><PERSON>_HPMasthead_Logo_SM", "logoImgPath": "https://oldnavyprod.a.bigcontent.io/v1/static/221000_13_M4195_<PERSON><PERSON><PERSON><PERSON><PERSON>_HPMasthead_Logo_SM", "isSquare": true}, "header": {"buildInfo": "amplience", "default": {"isUtilityLinksEnabled": true, "headerLayout": "sameRow", "fullBleedOptions": {"isFullBleedEnabled": false, "hasTransparencyLayer": false}}}, "onflyoutbanner": {"type": "builtin", "name": "div", "data": {"components": []}}}, "brand": "on", "type": "meta", "pmcsEdgeCacheTag": "on-homepage-en-ca-prod"}