{"meta.description": "pageDescription defaulted", "meta.title.overide": "pageTitle defaulted", "home": {"type": "home", "name": "HomeMultiSimple", "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": "0", "style": {"display": "none"}, "html": "<style> div[name='VideoComponent'] > video { object-fit: cover; } </style>"}}, {"instanceName": "html5_video-occasions-spotlight", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": false, "defaultHeight": {"small": "calc(98vw*(1194/768))", "large": "calc(98vw*(1110/1920))"}, "mobile": {"shouldDisplay": true, "data": {"style": {"backgroundColor": "#000", "flexDirection": "column", "position": "relative", "margin": "0 auto"}, "components": [{"instanceName": "html5-video-component", "name": "VideoComponent", "type": "sitewide", "defaultHeight": "calc(98vw*(1080/768))", "data": {"playerStyles": {"margin": "0 auto"}, "containerStyle": {"desktop": {"display": "none", "margin": "0", "height": "100%"}, "mobile": {"position": "relative", "margin": "0 auto", "top": "0%", "left": "0%", "height": "100%", "width": "100%"}}, "url": "/Asset_Archive/videocampaign/BR_BRF_Occasions_HERO_768x1194_Mobile_v2.mp4", "fallbackImage": {"src": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_01_IMG_SM", "alt": ""}, "controls": true, "muted": true, "playing": true, "loop": true, "width": "100%", "height": "calc(100vw*(1195/768))", "customControls": true, "customControlStyles": {"position": "absolute", "bottom": "0px", "right": "0px", "zIndex": "100", "button[aria-label='Play'], button[aria-label='Pause']": {"right": "56px"}, "button[aria-label='Mute'], button[aria-label='Unmute']": {"right": "16px"}, "button[aria-label='Mute'] ~ div, button[aria-label='Unmute'] ~ div": {"right": "16px"}}}}, {"type": "sitewide", "name": "LayeredContentModule", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"display": "block", "zIndex": "11", "position": "absolute", "top": 0, "left": 0, "margin": "0rem auto 0", "pointerEvents": "auto"}}, "background": {"className": "", "desktopStyle": {"display": "block"}, "image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_01_TXT_SM", "alt": "Campaign: New Arrivals", "style": {"display": "block"}, "desktopStyle": {}}, "linkData": {"to": "/browse/info.do?cid=3018119&mlink=5001,30016968,hp_occasion_bg", "target": ""}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "shop occasion", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "69%", "left": "28%", "height": "7%", "width": "44%"}, "desktopStyle": {"top": "80%", "left": "15%", "height": "9%", "width": "22%"}}, "linkData": {"to": "/browse/info.do?cid=3018119&mlink=5001,30016968,hp_occasion_cta"}}, {"composableButtonData": {"children": "shop dresses", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "79%", "left": "28%", "height": "7%", "width": "44%"}, "desktopStyle": {"top": "80%", "left": "39%", "height": "9%", "width": "22%"}}, "linkData": {"to": "/browse/category.do?cid=69883&mlink=5001,30016968,hp_occasion_wcta"}}, {"composableButtonData": {"children": "shop men's suits", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "89%", "left": "28%", "height": "7%", "width": "44%"}, "desktopStyle": {"top": "80%", "left": "63%", "height": "9%", "width": "22%"}}, "linkData": {"to": "/browse/category.do?cid=75310&mlink=5001,30016968,hp_occasion_mcta"}}]}}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"backgroundColor": "#000", "flexDirection": "column", "position": "relative", "margin": "0 auto"}, "components": [{"instanceName": "html5-video-component", "name": "VideoComponent", "type": "sitewide", "data": {"playerStyles": {"margin": "0 auto"}, "containerStyle": {"mobile": {"display": "none", "margin": "0", "height": "100%"}, "desktop": {"position": "relative", "margin": "0 auto", "top": "0%", "left": "0%", "height": "calc(98vw*(1080/1920))", "width": "100%"}}, "url": "/Asset_Archive/videocampaign/BR_BRF_Occasions_HERO_1920x1110_Desktop_audio.mp4", "fallbackImage": {"alt": "", "src": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_01_IMG_XL"}, "controls": true, "muted": true, "playing": true, "loop": true, "width": "100%", "height": "100%", "customControls": true, "customControlStyles": {"position": "absolute", "bottom": "5px", "right": "0px", "zIndex": "100", "button[aria-label='Play'], button[aria-label='Pause']": {"right": "56px"}, "button[aria-label='Mute'], button[aria-label='Unmute']": {"right": "16px"}, "button[aria-label='Mute'] ~ div, button[aria-label='Unmute'] ~ div": {"right": "16px"}}}}, {"type": "sitewide", "name": "LayeredContentModule", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"display": "block", "zIndex": "11", "position": "absolute", "top": 0, "left": 0, "width": "100vw", "margin": "0rem auto 0", "pointerEvents": "auto"}}, "background": {"className": "", "desktopStyle": {"display": "block"}, "image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_01_TXT_XL", "alt": "Campaign: New Arrivals", "style": {"display": "block"}, "desktopStyle": {}}, "linkData": {"to": "/browse/info.do?cid=3018119&mlink=5001,30016968,hp_occasion_bg", "target": ""}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "shop occasion", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "69%", "left": "28%", "height": "7%", "width": "44%"}, "desktopStyle": {"top": "80%", "left": "15%", "height": "9%", "width": "22%"}}, "linkData": {"to": "/browse/info.do?cid=3018119&mlink=5001,30016968,hp_occasion_cta"}}, {"composableButtonData": {"children": "shop dresses", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "79%", "left": "28%", "height": "7%", "width": "44%"}, "desktopStyle": {"top": "80%", "left": "39%", "height": "9%", "width": "22%"}}, "linkData": {"to": "/browse/category.do?cid=69883&mlink=5001,30016968,hp_occasion_wcta"}}, {"composableButtonData": {"children": "shop men's suits", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "89%", "left": "28%", "height": "7%", "width": "44%"}, "desktopStyle": {"top": "80%", "left": "63%", "height": "9%", "width": "22%"}}, "linkData": {"to": "/browse/category.do?cid=75310&mlink=5001,30016968,hp_occasion_mcta"}}]}}}]}}}}, {"instanceName": "widget_02_certona_personal", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "calc(98vw*(1151/768))", "large": "calc(98vw*(1080/1920))"}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "desktop": {"shouldDisplay": true, "data": {"style": {"position": "relative", "margin": "0 auto 0rem", "flexDirection": "column", "backgroundColor": "#ffffff", "borderTop": "0px solid #ffffff", "borderBottom": "0px solid #ffffff", "paddingBottom": "0rem"}, "components": [{"instanceName": "widget_certonaTitle", "name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/R00_CertonaBG_IMG_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/R00_CertonaBG_IMG_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_00_Certona_TXT_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_00_Certona_TXT_XL", "alt": "New Styles For You", "style": {}, "desktopStyle": {}}}}, {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>\n .slick-list {\n overflow: hidden !important; \n} div[data-testid='recommended-placeholder-card'] > div > div {\n padding-bottom: 150%; \n}  </style>", "style": {}, "classes": ""}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"position": "absolute", "bottom": "6%", "left": "0%", "width": "100%"}}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "BR", "source": "c<PERSON>a", "scheme": "brhome1_rr", "displayTitle": false, "fullWidth": true, "certonaTitle": {"title": "Just-In Picks For You", "style": {"mobile": {"color": "#fff", "display": "block", "fontSize": "1.5rem", "fontWeight": "400", "marginBottom": "0.5rem", "paddingLeft": "1rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}}}, "layout": "carousel", "centerMode": false, "defaultslidesToShowSlick": 3.5, "defaultslidesToScrollSlick": 1, "displayPlayPauseButton": true, "buttonSetting": {"buttonStyle": {"zIndex": "2"}, "playAltText": "Play carousel", "pauseAltText": "Pause carousel"}, "resslidesToShowSlick": 3.5, "resslidesToScrollSlick": 1, "arrows": true, "arrowVerticalPosition": "-10%", "autoplay": true, "autoplaySpeed": 2000, "speed": 500, "pauseOnHover": true, "infinite": true, "priceFlag": true, "prevArrowSlick": "/Asset_Archive/BRWeb/content/0028/925/519/assets/certona/left_arrow_v1.svg", "nextArrowSlick": "/Asset_Archive/BRWeb/content/0028/925/519/assets/certona/left_arrow_v1.svg", "prevArrowAlt": "previous recommendation", "nextArrowAlt": "next recommendation", "strikeThroughOriginalPriceFlag": true, "productTextStyles": {"productTitle": {"style": {"color": "black", "text-align": "center", "fontSize": "1vw", "font-weight": "700"}}, "productPrice": {"style": {"color": "black", "textAlign": "center", "fontSize": "1vw", "font-weight": "700"}}, "productSalePrice": {"style": {"color": "black", "textAlign": "center", "fontSize": "1vw", "fontWeight": "700"}}}, "productMarketingFlag": {"style": {"color": "black", "font-weight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "padding": "0px"}}, "productCardImageStyles": {}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"position": "relative", "flexDirection": "column", "backgroundColor": "#ffffff", "borderTop": "0px solid #ffffff", "borderBottom": "0px solid #ffffff", "paddingBottom": "0rem", "paddingTop": "0rem"}, "components": [{"instanceName": "widget_certonaTitle", "name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_00_Certona_TXT_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_00_Certona_TXT_XL", "alt": "New Styles For You", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}}}, {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>\n .slick-list {\n overflow: hidden !important; \n} div[data-testid='recommended-placeholder-card'] > div > div {\n padding-bottom: 150%; \n}  </style>", "style": {}, "classes": ""}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"position": "absolute", "bottom": "10%", "left": "0%", "width": "100%", "paddingBottom": "0"}}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px"}}, "data": {"customBrand": "BR", "source": "c<PERSON>a", "scheme": "brhome1_rr", "displayTitle": false, "certonaTitle": {"title": "Just-In Picks For You", "style": {"mobile": {"color": "#e2e0dd", "display": "block", "fontWeight": "400", "marginBottom": "0.5rem", "paddingLeft": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}}}, "layout": "carousel", "useMobileConfig": true, "arrows": false, "defaultslidesToShowSlick": 1.5, "defaultslidesToScrollSlick": 1, "displayPlayPauseButton": true, "resslidesToShowSlick": 1, "resslidesToScrollSlick": 1, "autoplay": true, "autoplaySpeed": 2000, "speed": 500, "infinite": true, "priceFlag": true, "prevArrowSlick": "/Asset_Archive/BRWeb/content/0028/925/519/assets/certona/left_arrow_v1.svg", "nextArrowSlick": "/Asset_Archive/BRWeb/content/0028/925/519/assets/certona/left_arrow_v1.svg", "strikeThroughOriginalPriceFlag": true, "productTextStyles": {"productTitle": {"style": {"color": "black", "fontSize": ".8125rem", "fontWeight": "700", "textAlign": "center"}}, "productMarketingFlag": {"style": {"color": "black", "font-weight": "700", "fontSize": ".8125rem", "textAlign": "center"}}, "productPrice": {"style": {"color": "black", "textAlign": "center", "fontWeight": "700", "fontSize": ".8125rem"}}, "productSalePrice": {"style": {"color": "black", "textAlign": "center", "fontWeight": "700", "fontSize": ".8125rem"}}}, "productCardStyles": {"style": {"flex-grow": "1"}}, "gridLayout": {"style": {"desktop": {"display": "block"}, "mobile": {"display": "block"}}, "productsPerRow": {"desktop": 4, "mobile": 1}}}}]}}]}}}}, {"instanceName": "widget_03_newarrivals_static", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(1080/1920))", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_02_IMG_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_02_IMG_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=48422&mlink=5001,30016968,hp_newarrivals_bg", "target": ""}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_02_TXT_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_02_TXT_XL", "alt": "", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Women's New Arrivals", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "89%", "left": "5%", "height": "9%", "width": "44%"}, "desktopStyle": {"top": "82%", "left": "26%", "height": "9%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=48422&mlink=5001,30016968,hp_newarrivals_women_cta"}}, {"composableButtonData": {"children": "Men's New Arrivals", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "89%", "left": "51%", "height": "9%", "width": "44%"}, "desktopStyle": {"top": "82%", "left": "51%", "height": "9%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=13846&mlink=5001,30016968,hp_newarrivals_men_cta"}}]}}}, {"instanceName": "widget_04_carousel_CatNav", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"lazy": true, "defaultHeight": "calc(98vw*(800/1920))", "mobile": {"shouldDisplay": true, "data": {"style": {"backgroundColor": "#fff", "flexDirection": "column", "position": "relative", "margin": "0 auto"}, "components": [{"instanceName": "widget_categories_Header", "name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_03_TXT_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_03_TXT_XL", "alt": "Lived in Luxury. Juxtaposing the stark landscape of a Norwegian mountain range, the soft and rich textures of silk, cashmere, and shearling create a warm and inviting collection designed for the infinite possibilities the season brings.", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=48422", "target": ""}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}}}, {"instanceName": "widgets_categories_carousel_mobile", "name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%", "padding": "0", "margin": "0 auto", "backgroundColor": "#fff", "fontSize": "0"}, "mobile": {}}, "data": {"lazy": true, "carouselOptions": {"slidesToShow": 1, "centerMode": true, "autoplay": false, "speed": 500, "autoplaySpeed": 3250, "fade": false, "displayPlayPauseBtn": false, "infinite": true, "dots": true, "dotsClass": "slick-dots", "arrowPosition": "0px", "prevArrowUrl": "/Asset_Archive/AllBrands/assets/components/carousel/desktop-prev-arrow_wht.svg", "nextArrowUrl": "/Asset_Archive/AllBrands/assets/components/carousel/desktop-next-arrow_wht.svg", "css": {"& .slick-slide > div:first-of-type": {"display": "flex"}, "& .slick-dots": {"bottom": "4.5%"}, "& .slick-dots > li": {"margin": "0 1%", "width": "5%", "backgroundColor": "gray", "height": "2px", "overflow": "hidden"}, "& .slick-dots > li > button ": {"margin": "0 auto"}, "& .slick-dots > li.slick-active > button::before": {"opacity": "1", "backgroundColor": "#000", "width": "100%", "left": "0%", "top": "0%", "transform": "none"}, "& .slick-dots > li > button::before ": {"font-size": "12px", "margin": "0px auto", "height": "100%", "backgroundColor": "transparent", "borderRadius": "0", "width": "100%", "opacity": "0.3"}}, "pauseOnHover": true, "pauseOnFocus": false}, "buttonSetting": {"buttonStyle": {"position": "absolute", "height": "auto", "left": "23%", "bottom": "2.5%", "width": "25px", "padding": "0rem", "zIndex": "2", "maxWidth": "8%"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/BRWeb/content/0030/015/211/assets/evergreen/BRSP230801_SITE_USCA_HP_00_Play_Icon_XL.svg", "pauseBtnSrc": "/Asset_Archive/BRWeb/content/0030/015/211/assets/evergreen/BRSP230801_SITE_USCA_HP_00_Pause_Icon_XL.svg"}, "playAltText": "Play carousel", "pauseAltText": "Pause carousel", "prevArrowAlt": "Previous Slide", "nextArrowAlt": "Next Slide"}, "style": {"position": "relative", ".slick-list": {"overflow": "hidden !important"}}, "carouselStyle": {"padding": "0px"}, "components": [{"instanceName": "widgets_slide_A", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#F6F4EB"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04A_IMG_SM", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/info.do?cid=3018119&mlink=5001,30015211,hp_catnav_occasion_bg", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04A_TXT_SM", "alt": "Campaign: Occasion", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Dresses", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "19%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=69883&mlink=5001,30015211,hp_catnav_occasion_dresses_cta"}}, {"composableButtonData": {"children": "Shop Men's Suits", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "46%", "height": "6%", "width": "27%"}}, "linkData": {"to": "/browse/category.do?cid=75310&mlink=5001,30015211,hp_catnav_occasion_suits_cta"}}]}}}, {"instanceName": "widgets_slide_B", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#F6F4EB"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04B_IMG_SM", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=28660&mlink=5001,30015211,hp_catnav_sweaters_bg", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04B_TXT_SM", "alt": "Campaign: Sweaters", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Women's Sweaters", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "24%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=5032&mlink=5001,30015211,hp_catnav_sweaters_women_cta"}}, {"composableButtonData": {"children": "Shop Men's Sweaters", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "50%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=28660&mlink=5001,30015211,hp_catnav_sweaters_men_cta"}}]}}}, {"instanceName": "widgets_slide_C", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#F6F4EB"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04C_IMG_SM", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=99915&mlink=5001,30015211,hp_catnav_coatsjackets_bg", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04C_TXT_SM", "alt": "Campaign: Jackets", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Women's Jackets", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "24%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=99915&mlink=5001,30015211,hp_catnav_coatsjackets_women_cta"}}, {"composableButtonData": {"children": "Shop Men's Jackets", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "50%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=1016720&mlink=5001,30015211,hp_catnav_coatsjackets_men_cta"}}]}}}, {"instanceName": "widgets_slide_D", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#F6F4EB"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04D_IMG_SM", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1183416&mlink=5001,30015211,hp_catnav_luxfabrics_bg", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04D_TXT_SM", "alt": "Campaign: Luxurious Fabrics", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Cashmere", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "22%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=1183416#style=1183420&mlink=5001,30015211,hp_catnav_luxfabrics_cashmere_cta"}}, {"composableButtonData": {"children": "Shop Luxurious Fabrics", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "48%", "height": "6%", "width": "27%"}}, "linkData": {"to": "/browse/category.do?cid=1183416&mlink=5001,30015211,hp_catnav_luxfabrics_cta"}}]}}}, {"instanceName": "widgets_slide_E", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#F6F4EB"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04E_IMG_SM", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1147303&mlink=5001,30015211,hp_catnav_accesories_bg", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04E_TXT_SM", "alt": "Campaign: Accesories", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Women's Accesories", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "25%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=1147303&mlink=5001,30015211,hp_catnav_accesories_women_cta"}}, {"composableButtonData": {"children": "Shop Men's Accesories", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "50%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=1147354&mlink=5001,30015211,hp_catnav_accesories_men_cta"}}]}}}, {"instanceName": "widgets_slide_F", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#F6F4EB"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04F_IMG_SM", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=3012158&mlink=5001,30015211,hp_catnav_gifting_bg", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04F_TXT_SM", "alt": "Campaign: Gifting Shop", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Women's Gifts", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "25%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=3012158&mlink=5001,30015211,hp_catnav_gifting_women_cta"}}, {"composableButtonData": {"children": "Shop Men's Gifts", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "50%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=3024831&mlink=5001,30015211,hp_catnav_gifting_men_cta"}}]}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"defaultHeight": "calc(98vw*(1000/1920))", "style": {"width": "100%", "backgroundColor": "#fff", "flexDirection": "column", "position": "relative", "margin": "0 auto", "padding": "0"}, "components": [{"instanceName": "widget_categories_Header", "name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_03_TXT_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_03_TXT_XL", "alt": "Lived in Luxury. Juxtaposing the stark landscape of a Norwegian mountain range, the soft and rich textures of silk, cashmere, and shearling create a warm and inviting collection designed for the infinite possibilities the season brings.", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=48422", "target": ""}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}}}, {"instanceName": "widgets_categories_carousel_desktop", "name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%", "padding": "0", "margin": "0 auto", "backgroundColor": "#fff", "fontSize": "0"}, "mobile": {}}, "data": {"lazy": true, "carouselOptions": {"slidesToShow": 4, "autoplay": false, "speed": 500, "autoplaySpeed": 3250, "fade": false, "displayPlayPauseBtn": false, "infinite": false, "dots": true, "dotsClass": "slick-dots", "arrowPosition": "0px", "prevArrowUrl": "/Asset_Archive/AllBrands/assets/components/carousel/desktop-prev-arrow_wht.svg", "nextArrowUrl": "/Asset_Archive/AllBrands/assets/components/carousel/desktop-next-arrow_wht.svg", "css": {"& .slick-slide > div:first-of-type": {"display": "flex"}, "& .slick-dots": {"bottom": "-8%"}, "& .slick-dots > li": {"margin": "0 0.25%", "width": "2.5%", "backgroundColor": "gray", "height": "2px", "overflow": "hidden"}, "& .slick-dots > li > button ": {"margin": "0 auto"}, "& .slick-dots > li.slick-active > button::before": {"opacity": "1", "backgroundColor": "#000", "width": "100%", "height": "100%", "left": "0%", "top": "0%", "transform": "none"}, "& .slick-dots > li > button::before ": {"font-size": "12px", "margin": "0px auto", "height": "100%", "backgroundColor": "transparent", "borderRadius": "0", "width": "100%", "opacity": "0.8"}, "& .slick-arrow.slick-prev:hover": {"transform": "scale(1.15)", "left": "0.75%"}, "& .slick-arrow.slick-next:hover": {"transform": "scale(1.15)", "right": "0.75%"}, "& .slick-arrow.slick-prev": {"transition": "all .2s ease-in-out", "left": "1%"}, "& .slick-arrow.slick-next": {"transition": "all .2s ease-in-out", "right": "1%"}}, "pauseOnHover": true, "pauseOnFocus": false}, "buttonSetting": {"buttonStyle": {"position": "absolute", "height": "auto", "left": "44%", "bottom": "5.5%", "width": "25px", "padding": "0rem", "zIndex": "20", "maxWidth": "8%"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/BRWeb/content/0030/015/211/assets/evergreen/BRSP230801_SITE_USCA_HP_00_Play_Icon_XL.svg", "pauseBtnSrc": "/Asset_Archive/BRWeb/content/0030/015/211/assets/evergreen/BRSP230801_SITE_USCA_HP_00_Pause_Icon_XL.svg"}, "playAltText": "Play carousel", "pauseAltText": "Pause carousel", "prevArrowAlt": "Previous Slide", "nextArrowAlt": "Next Slide"}, "style": {"position": "relative", "padding": "0 0 6%", ".slick-list": {"overflow": "hidden !important"}}, "carouselStyle": {"padding": "0px"}, "components": [{"instanceName": "widgets_slide_A", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#F6F4EB"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04A_IMG_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/info.do?cid=3018119&mlink=5001,30015211,hp_catnav_occasion_bg", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04A_TXT_XL", "alt": "Campaign: Occasion", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Dresses", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "91%", "left": "17%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=69883&mlink=5001,30015211,hp_catnav_occasion_dresses_cta"}}, {"composableButtonData": {"children": "Shop Men's Suits", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "91%", "left": "46%", "height": "6%", "width": "25%"}}, "linkData": {"to": "/browse/category.do?cid=75310&mlink=5001,30015211,hp_catnav_occasion_suits_cta"}}]}}}, {"instanceName": "widgets_slide_B", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#F6F4EB"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04B_IMG_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=28660&mlink=5001,30015211,hp_catnav_sweaters_bg", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04B_TXT_XL", "alt": "Campaign: Sweaters", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Women's Sweaters", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "91%", "left": "26%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=5032&mlink=5001,30015211,hp_catnav_sweaters_women_cta"}}, {"composableButtonData": {"children": "Shop Men's Sweaters", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "91%", "left": "49%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=28660&mlink=5001,30015211,hp_catnav_sweaters_men_cta"}}]}}}, {"instanceName": "widgets_slide_C", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#F6F4EB"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04C_IMG_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=99915&mlink=5001,30015211,hp_catnav_coatsjackets_bg", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04C_TXT_XL", "alt": "Campaign: Jackets", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Women's Jackets", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "91%", "left": "26%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=99915&mlink=5001,30015211,hp_catnav_coatsjackets_women_cta"}}, {"composableButtonData": {"children": "Shop Men's Jackets", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "91%", "left": "49%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=1016720&mlink=5001,30015211,hp_catnav_coatsjackets_men_cta"}}]}}}, {"instanceName": "widgets_slide_D", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#F6F4EB"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04D_IMG_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1183416&mlink=5001,30015211,hp_catnav_luxfabrics_bg", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04D_TXT_XL", "alt": "Campaign: Luxurious Fabrics", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Cashmere", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "91%", "left": "23%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=1183416#style=1183420&mlink=5001,30015211,hp_catnav_luxfabrics_cashmere_cta"}}, {"composableButtonData": {"children": "Shop Luxurious Fabrics", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "91%", "left": "49%", "height": "6%", "width": "26%"}}, "linkData": {"to": "/browse/category.do?cid=1183416&mlink=5001,30015211,hp_catnav_luxfabrics_cta"}}]}}}, {"instanceName": "widgets_slide_E", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#F6F4EB"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04E_IMG_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1147303&mlink=5001,30015211,hp_catnav_accesories_bg", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04E_TXT_XL", "alt": "Campaign: Accesories", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Women's Accesories", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "91%", "left": "26%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=1147303&mlink=5001,30015211,hp_catnav_accesories_women_cta"}}, {"composableButtonData": {"children": "Shop Men's Accesories", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "91%", "left": "50%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=1147354&mlink=5001,30015211,hp_catnav_accesories_men_cta"}}]}}}, {"instanceName": "widgets_slide_F", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#F6F4EB"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04F_IMG_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=3012158&mlink=5001,30015211,hp_catnav_gifting_bg", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_04F_TXT_XL", "alt": "Campaign: Gifting Shop", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Women's Gifts", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "91%", "left": "26%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=3012158&mlink=5001,30015211,hp_catnav_gifting_women_cta"}}, {"composableButtonData": {"children": "Shop Men's Gifts", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "91%", "left": "50%", "height": "6%", "width": "23%"}}, "linkData": {"to": "/browse/category.do?cid=3024831&mlink=5001,30015211,hp_catnav_gifting_men_cta"}}]}}}]}}]}}}}, {"instanceName": "widget_05_gifting", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(1080/1920))", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_05_IMG_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_05_IMG_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=3012158&mlink=5001,30016968,hp_holidaygiftguide_bg", "target": ""}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_05_TXT_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_05_TXT_XL", "alt": "Holiday Gift Guide. Handmade details. The finest materials. Expertly crafted designs. Explore an elevated guide on holiday gifting.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Women's Gifts", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "69%", "left": "28%", "height": "9%", "width": "44%"}, "desktopStyle": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "82%", "left": "14%", "height": "10%", "width": "24%"}}, "linkData": {"to": "/browse/category.do?cid=3012158&mlink=5001,30015211,hp_holidaygiftguide_women_cta"}}, {"composableButtonData": {"children": "Shop Men's Gifts", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "78%", "left": "28%", "height": "9%", "width": "44%"}, "desktopStyle": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "82%", "left": "38%", "height": "10%", "width": "24%"}}, "linkData": {"to": "/browse/category.do?cid=3024831&mlink=5001,30015211,hp_holidaygiftguide_men_cta"}}, {"composableButtonData": {"children": "Shop Baby's Gifts", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "89%", "left": "28%", "height": "9%", "width": "44%"}, "desktopStyle": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "82%", "left": "62%", "height": "10%", "width": "24%"}}, "linkData": {"to": "/browse/category.do?cid=3024832&mlink=5001,30015211,hp_holidaygiftguide_baby_ct"}}]}}}, {"instanceName": "widget_06_certona_NA", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "calc(98vw*(1151/768))", "large": "calc(98vw*(1080/1920))"}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "desktop": {"shouldDisplay": true, "data": {"style": {"position": "relative", "margin": "0 auto 0rem", "flexDirection": "column", "backgroundColor": "#ffffff", "borderTop": "0px solid #ffffff", "borderBottom": "0px solid #ffffff", "paddingBottom": "0rem"}, "components": [{"instanceName": "widget_certonaTitle", "name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/R00_CertonaBG_IMG_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/R00_CertonaBG_IMG_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_00_Certona2_TXT_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_00_Certona2_TXT_XL", "alt": "New Styles For You", "style": {}, "desktopStyle": {}}}}, {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>\n .slick-list {\n overflow: hidden !important; \n} div[data-testid='recommended-placeholder-card'] > div > div {\n padding-bottom: 150%; \n}  </style>", "style": {}, "classes": ""}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"position": "absolute", "bottom": "6%", "left": "0%", "width": "100%"}}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "BR", "source": "c<PERSON>a", "scheme": "brhome2_rr", "displayTitle": false, "fullWidth": true, "certonaTitle": {"title": "Just-In Picks For You", "style": {"mobile": {"color": "#fff", "display": "block", "fontSize": "1.5rem", "fontWeight": "400", "marginBottom": "0.5rem", "paddingLeft": "1rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}}}, "layout": "carousel", "centerMode": false, "defaultslidesToShowSlick": 3.5, "defaultslidesToScrollSlick": 1, "displayPlayPauseButton": true, "buttonSetting": {"buttonStyle": {"zIndex": "2"}, "playAltText": "Play carousel", "pauseAltText": "Pause carousel"}, "resslidesToShowSlick": 3.5, "resslidesToScrollSlick": 1, "arrows": true, "arrowVerticalPosition": "-10%", "autoplay": true, "autoplaySpeed": 2000, "speed": 500, "pauseOnHover": true, "infinite": true, "priceFlag": true, "prevArrowSlick": "/Asset_Archive/BRWeb/content/0028/925/519/assets/certona/left_arrow_v1.svg", "nextArrowSlick": "/Asset_Archive/BRWeb/content/0028/925/519/assets/certona/left_arrow_v1.svg", "prevArrowAlt": "previous recommendation", "nextArrowAlt": "next recommendation", "strikeThroughOriginalPriceFlag": true, "productTextStyles": {"productTitle": {"style": {"color": "black", "text-align": "center", "fontSize": "1vw", "font-weight": "700"}}, "productPrice": {"style": {"color": "black", "textAlign": "center", "fontSize": "1vw", "font-weight": "700"}}, "productSalePrice": {"style": {"color": "black", "textAlign": "center", "fontSize": "1vw", "fontWeight": "700"}}}, "productMarketingFlag": {"style": {"color": "black", "font-weight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "padding": "0px"}}, "productCardImageStyles": {}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"position": "relative", "flexDirection": "column", "backgroundColor": "#ffffff", "borderTop": "0px solid #ffffff", "borderBottom": "0px solid #ffffff", "paddingBottom": "0rem", "paddingTop": "0rem"}, "components": [{"instanceName": "widget_certonaTitle", "name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_00_Certona2_TXT_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_00_Certona2_TXT_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}}}, {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>\n .slick-list {\n overflow: hidden !important; \n} div[data-testid='recommended-placeholder-card'] > div > div {\n padding-bottom: 150%; \n}  </style>", "style": {}, "classes": ""}}, {"type": "builtin", "name": "div", "data": {"props": {"style": {"position": "absolute", "bottom": "10%", "left": "0%", "width": "100%", "paddingBottom": "0"}}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px"}}, "data": {"customBrand": "BR", "source": "c<PERSON>a", "scheme": "brhome2_rr", "displayTitle": false, "certonaTitle": {"title": "Just-In Picks For You", "style": {"mobile": {"color": "#e2e0dd", "display": "block", "fontWeight": "400", "marginBottom": "0.5rem", "paddingLeft": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}}}, "layout": "carousel", "useMobileConfig": true, "arrows": false, "defaultslidesToShowSlick": 1.5, "defaultslidesToScrollSlick": 1, "displayPlayPauseButton": true, "resslidesToShowSlick": 1, "resslidesToScrollSlick": 1, "autoplay": true, "autoplaySpeed": 2000, "speed": 500, "infinite": true, "priceFlag": true, "prevArrowSlick": "/Asset_Archive/BRWeb/content/0028/925/519/assets/certona/left_arrow_v1.svg", "nextArrowSlick": "/Asset_Archive/BRWeb/content/0028/925/519/assets/certona/left_arrow_v1.svg", "strikeThroughOriginalPriceFlag": true, "productTextStyles": {"productTitle": {"style": {"color": "black", "fontSize": ".8125rem", "fontWeight": "700", "textAlign": "center"}}, "productMarketingFlag": {"style": {"color": "black", "font-weight": "700", "fontSize": ".8125rem", "textAlign": "center"}}, "productPrice": {"style": {"color": "black", "textAlign": "center", "fontWeight": "700", "fontSize": ".8125rem"}}, "productSalePrice": {"style": {"color": "black", "textAlign": "center", "fontWeight": "700", "fontSize": ".8125rem"}}}, "productCardStyles": {"style": {"flex-grow": "1"}}, "gridLayout": {"style": {"desktop": {"display": "block"}, "mobile": {"display": "block"}}, "productsPerRow": {"desktop": 4, "mobile": 1}}}}]}}]}}}}, {"instanceName": "widgets_BRhome", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(1080/1920))", "container": {"className": "", "desktopStyle": {"paddingBottom": 0}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "paddingBottom": "4vw", "backgroundColor": "#fff"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_06_IMG_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_06_IMG_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "https://brhome.com//?tid=brsv000001&mlink=5001,********,hp_brhome_bg", "target": "_blank"}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_06_TXT_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231114_SITE_USCA_HP_06_TXT_XL", "alt": "Come Home to the World", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop the Collection", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "84%", "left": "20%", "height": "13%", "width": "59%"}, "desktopStyle": {"top": "79%", "left": "38%", "height": "10%", "width": "24%"}}, "linkData": {"to": "https://brhome.com/?tid=brsv000001&mlink=5001,********,hp_brhome_cta"}}]}}}, {"instanceName": "widgets_stores", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "mobile": {"shouldDisplay": true, "data": {"className": "BR_mobile_carousel", "style": {"backgroundColor": "#000", "flexDirection": "column", "position": "relative", "maxWidth": "1680px", "margin": "0 auto"}, "components": [{"instanceName": "widgets_carousel_stores", "name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "data": {"className": "BR_carousel", "carouselOptions": {"slidesToShow": 1, "displayArrows": "false", "arrowPosition": "0px", "prevArrowUrl": "/Asset_Archive/AllBrands/assets/components/carousel/desktop-prev-arrow_wht.svg", "nextArrowUrl": "/Asset_Archive/AllBrands/assets/components/carousel/desktop-next-arrow_wht.svg", "carouselStyle": {}, "infinite": false, "css": {"& .slick-slide > div:first-of-type": {"display": "flex"}}}, "style": {"position": "relative", ".slick-list": {"overflow": "hidden !important"}}, "components": [{"instanceName": "widgets_slide_A", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BRWeb/content/0030/017/345/assets/widgets/SM/BRSP231003_SITE_USCA_HP_07A_IMG_SM.jpg", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "/Asset_Archive/BRWeb/content/0030/017/345/assets/widgets/SM/BRSP231003_SITE_USCA_HP_07A_TXT_SM.svg?v=2", "alt": "Campaign: Melrose Store", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "noHeader": true, "title": " ", "iframeData": {"src": "https://player.vimeo.com/video/871712889?badge=0&&autoplay=1&loop=1&muted=1&player_id=0&app_id=58479", "title": " "}}, "composableButtonData": {"children": "soho modal", "color": "primary", "capitalization": "uppercase", "size": "xl", "style": {"position": "absolute", "overflow": "hidden", "opacity": "0", "top": "0", "left": "0", "height": "100%", "width": "100%"}}, "className": ""}]}}}, {"instanceName": "widgets_slide_B", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BRWeb/content/0030/017/345/assets/widgets/SM/BRSP231003_SITE_USCA_HP_07B_IMG_SM.jpg", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "/Asset_Archive/BRWeb/content/0030/017/345/assets/widgets/SM/BRSP231003_SITE_USCA_HP_07B_TXT_SM.svg?v=2", "alt": "Campaign: Geary store", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Geary LP", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "0%", "height": "100%", "width": "100%"}}, "linkData": {"to": "/browse/info.do?cid=3025167&mlink=5001,********,HP_stores_geary_bg"}}]}}}, {"instanceName": "widgets_slide_C", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BRWeb/content/0030/017/345/assets/widgets/SM/BRSP231003_SITE_USCA_HP_07C_IMG_SM.jpg", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "/Asset_Archive/BRWeb/content/0030/017/345/assets/widgets/SM/BRSP231003_SITE_USCA_HP_07C_TXT_SM.svg?v=2", "alt": "Campaign: Soho store", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "noHeader": true, "title": " ", "iframeData": {"src": "https://player.vimeo.com/video/870389643?badge=0&&autoplay=1&loop=1&muted=1&player_id=0&app_id=58479", "title": " "}}, "composableButtonData": {"children": "soho modal", "color": "primary", "capitalization": "uppercase", "size": "xl", "style": {"position": "absolute", "overflow": "hidden", "opacity": "0", "top": "0", "left": "0", "height": "100%", "width": "100%"}}, "className": ""}]}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "margin": "0 auto"}, "components": [{"instanceName": "widget-stores", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(1080/960))", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BRWeb/content/0030/017/345/assets/widgets/XL/BRSP231003_SITE_USCA_HP_07_IMG_XL_101323.jpg", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/info.do?cid=3025167&mlink=5001,********,HP_stores_melrose_bg", "target": ""}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "overlay": {"srcUrl": "/Asset_Archive/BRWeb/content/0030/017/345/assets/widgets/XL/BRSP231003_SITE_USCA_HP_07_TXT_XL_101323.svg", "alt": "Campaign: Stores", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "noHeader": true, "title": " ", "iframeData": {"src": "https://player.vimeo.com/video/871710867?badge=0&&autoplay=1&loop=1&muted=1&player_id=0&app_id=58479", "title": " "}}, "composableButtonData": {"children": "melrose", "color": "primary", "capitalization": "uppercase", "size": "xl", "style": {"position": "absolute", "overflow": "hidden", "opacity": "0", "top": "87%", "left": "14%", "height": "9%", "width": "23%"}}, "className": ""}, {"composableButtonData": {"children": "Geary LP", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "87%", "left": "39%", "height": "9%", "width": "23%"}}, "linkData": {"to": "/browse/info.do?cid=3025167&mlink=5001,********,HP_stores_geary_bg"}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "max", "noHeader": true, "title": " ", "iframeData": {"src": "https://player.vimeo.com/video/869736463?badge=0&&autoplay=1&loop=1&muted=1&player_id=0&app_id=58479", "title": " "}}, "composableButtonData": {"children": "soho modal", "color": "primary", "capitalization": "uppercase", "size": "xl", "style": {"position": "absolute", "overflow": "hidden", "opacity": "0", "top": "87%", "left": "62%", "height": "9%", "width": "23%"}}, "className": ""}]}}}]}}}}, {"instanceName": "widgets_catalogs", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "mobile": {"shouldDisplay": true, "data": {"style": {"backgroundColor": "#000", "flexDirection": "column", "position": "relative", "maxWidth": "1680px", "margin": "0 auto"}, "components": [{"instanceName": "catalogs_header", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(311/768))", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "backgroundColor": "#ffffff"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_HP_07_TXT_SM", "alt": "An Invitation to Explore. Discover our latest issues for an in-depth look at our new collection — and the places that inspired us this season. ", "style": {"display": "flex"}, "desktopStyle": {}}, "style": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": []}}}, {"instanceName": "widgets_carousel__catalog", "name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {}, "mobile": {"width": "100%", "padding": "0", "margin": "0 auto", "backgroundColor": "#a49f8c", "fontSize": "0"}}, "data": {"carouselOptions": {"slidesToShow": 1, "displayArrows": "false", "arrowPosition": "0px", "prevArrowUrl": "/Asset_Archive/AllBrands/assets/components/carousel/desktop-prev-arrow.svg", "nextArrowUrl": "/Asset_Archive/AllBrands/assets/components/carousel/desktop-next-arrow.svg", "displayPlayPauseBtn": false, "infinite": false, "dots": false, "css": {"& .slick-slide > div:first-of-type": {"display": "flex"}}}, "style": {"position": "relative", ".slick-list": {"overflow": "hidden !important"}}, "components": [{"instanceName": "widgets_slide_A", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#FED501", "padding": "0"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_HP_08A_IMG_SM", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/info.do?cid=3018186&mlink=5001,********,hp_catalog_holiday", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_HP_08A_TXT_SM", "alt": "Campaign: Holiday", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": []}}}, {"instanceName": "widgets_slide_B", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#FED501"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_HP_08B_IMG_SM", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/info.do?cid=3017731&mlink=5001,********,hp_catalog_brclassics", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_HP_08B_TXT_SM", "alt": "Campaign: BR Classics", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": []}}}, {"instanceName": "widgets_slide_C", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#FED501"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_HP_08C_IMG_SM", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/info.do?cid=3017729&mlink=5001,********,hp_catalog_brhome", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_HP_08C_TXT_SM", "alt": "Campaign: BR Classics", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": []}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "margin": "0 auto"}, "components": [{"instanceName": "hp-catalogs-header", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(1080/960))", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_USCA_HP_08_IMG_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_USCA_HP_08_TXT_XL", "alt": "An Invitation to Explore. Discover our latest issues for an in-depth look at our new collection — and the places that inspired us this season. ", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "View Holiday", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "29%", "left": "7%", "height": "65%", "width": "27%"}}, "linkData": {"to": "/browse/info.do?cid=3018186&mlink=5001,********,hp_catalog_holiday"}}, {"composableButtonData": {"children": "View BR Classics", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "29%", "left": "39%", "height": "65%", "width": "25%"}}, "linkData": {"to": "/browse/info.do?cid=3017731&mlink=5001,********,hp_catalog_brclassics"}}, {"composableButtonData": {"children": "View BR Home", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "29%", "left": "69%", "height": "65%", "width": "25%"}}, "linkData": {"to": "/browse/info.do?cid=3017729&mlink=5001,********,hp_catalog_brhome"}}]}}}]}}}}, {"instanceName": "hp-card_acquisition", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(600/1920))", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "backgroundColor": "#cccccc"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_USCA_HP_09_IMG_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_USCA_HP_09_IMG_XL", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=48422&mlink=5001,********,hp_widget_Card_Acq_BG", "target": "_self", "title": ""}, "style": {}}, "overlay": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_USCA_HP_09_TXT_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_USCA_HP_09_TXT_XL", "alt": "Cardmember Exclusive Pass. 20% off* every purchase for 6 months when you open and use your Banana Republic Rewards Credit Card.  Apply Now. Details", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Apply Now", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "79%", "left": "26%", "height": "10%", "width": "48%"}, "desktopStyle": {"top": "60%", "left": "59%", "height": "16%", "width": "24%"}}, "linkData": {"to": "https://bananarepublic.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=BRSSHMPGBNR&mlink=5001,********,hp_widget_Card_Acq", "target": "_self"}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "title": " ", "iframeData": {"src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=969777", "title": " "}}, "composableButtonData": {"children": "Details", "color": "primary", "capitalization": "uppercase", "size": "xl", "style": {"position": "absolute", "overflow": "hidden", "opacity": "0", "top": "92%", "left": "41%", "height": "4%", "width": "18%"}, "desktopStyle": {"top": "80%", "left": "67%", "height": "5%", "width": "7%"}}, "className": ""}]}}}, {"instanceName": "widgets_HolidayServices", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(400/1920))", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_USCA_HP_00_Holiday_TXT_SM", "desktopSrcUrl": "https://bananarepprod.a.bigcontent.io/v1/static/BRSP231101_SITE_USCA_HP_00_Holiday_TXT_XL", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "E-gift Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "21%", "left": "0", "height": "33%", "width": "50%"}, "desktopStyle": {"top": "31%", "left": "11%", "height": "69%", "width": "18%"}}, "linkData": {"to": "/customerService/info.do?cid=61830&mlink=5001,********,holiday_giftcards_cta"}}, {"composableButtonData": {"children": "BR Atelier", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "21%", "left": "50%", "height": "33%", "width": "50%"}, "desktopStyle": {"top": "31%", "left": "29%", "height": "69%", "width": "22.5%"}}, "linkData": {"to": "/browse/info.do?cid=1194368&mlink=5001,********,holiday_atelier_cta"}}, {"composableButtonData": {"children": "Download App", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "54%", "left": "0", "height": "33%", "width": "50%"}, "desktopStyle": {"top": "31%", "left": "52%", "height": "69%", "width": "21%"}}, "linkData": {"to": "/browse/info.do?cid=1154071&mlink=5001,********,holiday_app_cta"}}, {"composableButtonData": {"children": "Gifting options", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "54%", "left": "50%", "height": "33%", "width": "50%"}, "desktopStyle": {"top": "31%", "left": "73%", "height": "69%", "width": "21%"}}, "linkData": {"to": "/browse/category.do?cid=3012158&mlink=5001,********,holiday_paymentoptions_cta"}}]}}}]}, "sitewide": {"desktopemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "dpg_emergency_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": false, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "mobileemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "dpg_emergency_banner_mob", "type": "builtin", "name": "div", "experimentRunning": false, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"type": "builtin", "name": "div", "data": {"props": {"id": "emailMarketingBanner"}, "components": [{"instanceName": "email_signup_banner_mobile_072021", "name": "LayoutComponent", "type": "sitewide", "meta": {"excludePageTypes": ["ShoppingBag"]}, "experimentRunning": true, "useGreyLoadingEffect": false, "data": {"shouldWaitForOptimizely": true, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "mobile": {"shouldDisplay": true, "data": {}}}}]}}]}}]}}, "below-topnav": {"type": "builtin", "name": "div", "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "HTMLInjectionComponent", "description": "CSS to support WCD-owned styles on Category pages", "type": "sitewide", "data": {"includePageTypes": ["category", "division"], "defaultHeight": "0px", "classes": "", "style": {"display": "none"}, "html": "<style>#category-page .limit-width h1 {text-transform:uppercase;}#category-page .wcd_seo-copy-block{color:#000;margin:0 auto 1.5rem;padding-left:1rem;padding-bottom:4rem}#category-page .wcd_seo-copy-block div,#utility-page .wcd_seo-copy-block div{padding-top:.7rem;text-transform:uppercase}#category-page .wcd_seo-copy-block a,#utility-page .wcd_seo-copy-block a{color:#00e}#utility-page .wcd_seo-copy-block{line-height:1.25;font-family:EuclidCircularB,Hiragino <PERSON>,Helvetica,Arial,sans-serif;font-size:12px;white-space:pre-line;max-width:835px;color:#000;margin:0 auto;padding:4rem 1rem}#category-page .wcd_jumplinks{flex-direction:row;margin-bottom:45px;background-color:#e9e8e3;pointer-events:auto;display:flex;justify-content:space-evenly;align-items:flex-start;width:100%}#category-page div:has(.wcd_jumplinks){margin-bottom:0}#category-page #main-content:has(.wcd_jumplinks)~.product-grid .filter_button,#category-page #main-content:has(.wcd_jumplinks)~.product-grid [aria-haspopup=listbox]{color:#000}#category-page #main-content:has(.wcd_jumplinks)~.product-grid .filter_button:hover,#category-page #main-content:has(.wcd_jumplinks)~.product-grid [aria-haspopup=listbox]:hover{color:#fff}@media(max-width:767px){#category-page #main-content:has(.wcd_jumplinks)~.product-grid select#sortBySelect,#category-page #main-content:has(video)~.product-grid .filter_button,#category-page #main-content:has(video)~.product-grid select#sortBySelect{color:#000}#category-page #main-content:has(.wcd_jumplinks)~.product-grid select#sortBySelect:hover,#category-page #main-content:has(video)~.product-grid .filter_button:hover,#category-page #main-content:has(video)~.product-grid select#sortBySelect:hover{color:#fff}#category-page #main-content:has(.wcd_jumplinks)~.product-grid select#sortBySelect,#category-page #main-content:has(video)~.product-grid select#sortBySelect{background-image:url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000;fill-rule:evenodd' /%3E%3C/svg%3E\")}#category-page #main-content:has(.wcd_jumplinks)~.product-grid select#sortBySelect:hover,#category-page #main-content:has(video)~.product-grid select#sortBySelect:hover{background-image:url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23fff;fill-rule:evenodd' /%3E%3C/svg%3E\")}}#category-page #main-content:has(.wcd_jumplinks)~.product-grid [aria-haspopup=listbox]::before{background:url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000;fill-rule:evenodd' /%3E%3C/svg%3E\") 2px 18px no-repeat}#category-page #main-content:has(.wcd_jumplinks)~.product-grid [aria-haspopup=listbox]:hover::before{background:url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23fff;fill-rule:evenodd' /%3E%3C/svg%3E\") 2px 18px no-repeat}#category-page .wcd_jumplinks.wcd_scrollable-jumplinks{justify-content:flex-start;overflow-x:auto}#category-page .wcd_jumplinks>a{padding:1.25rem .8rem;margin:0;background:0 0;color:#000;border:none;font-weight:300;font-family:'Linotype Didot BR',BananaSerif,'Times New Roman',serif;flex:0 0 auto;font-size:1rem}#category-page .wcd_jumplinks div[data-testid=button-dropdown-container] button{padding:1.25rem 0;background-color:transparent;color:#000;font-family:'Linotype Didot BR',BananaSerif,'Times New Roman',serif;font-weight:300;font-size:1rem}#category-page .wcd_jumplinks div[data-testid=button-dropdown-container] ul{box-shadow:none}#category-page .wcd_jumplinks div[data-testid=button-dropdown-container] ul li{margin:0;border:0}#category-page .wcd_jumplinks div[data-testid=button-dropdown-container] ul li a{font-weight:400;background-color:#f6f4eb;font-size:.9rem;text-transform:none;color:#000}#category-page .wcd_jumplinks div[data-testid=button-dropdown-container] ul li a:hover{background-color:#ccc;color:#000}@media(min-width:768px){#category-page .wcd_jumplinks.wcd_scrollable-jumplinks{justify-content:space-evenly}#category-page .wcd_jumplinks>a{overflow-x:inherit;z-index:99}#category-page .wcd_jumplinks>a:hover{text-decoration:underline}}</style>"}}, {"type": "sitewide", "name": "OptimizelyPlaceholder", "instanceName": "below-topnav", "experimentRunning": true, "data": {"meta": {"includePageTypes": ["product"]}, "defaultHeight": {"large": "5px", "small": "5px"}}}]}}}}]}}, "headline": {"type": "builtin", "name": "div", "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "instanceName": "headline-11-14-non<PERSON>f", "experimentRunning": true, "data": {"components": [""]}}]}}, "edfslarge": {"type": "builtin", "name": "div", "data": {"components": [{"type": "sitewide", "instanceName": "edfs-header-large", "name": "MktEdfsLarge", "experimentRunning": true, "tileStyle": {"display": "flex", "height": "40px", "alignItems": "center", "margin-top": "1px"}, "data": {"lazy": false, "experimentRunning": true, "styles": {"detailsButton": {"color": "#000"}}, "defaultData": {"text": [{"text": "FREE SHIPPING ON $50+ FOR REWARDS MEMBERS", "inlineStyle": {"color": "#000"}}], "detailsLink": "Details", "styles": {"detailsButton": {"color": "#000"}}}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=1301", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "SIGN IN OR JOIN", "path": "/my-account/sign-in", "style": {"letterSpacing": "0", "fontSize": "1em", "display": "inline", "fontWeight": "400", "position": "relative", "top": "-1px", "fontFamily": "EuclidCircularB, '<PERSON><PERSON><PERSON>', Helvetica, Arial, sans-serif", "color": "#000"}}}}]}}, "edfssmall": {"type": "builtin", "name": "div", "data": {"components": [{"ciid": "2378cdfe-f11d-4fbd-b4bc-344d783212ca", "instanceName": "edfs-header-small", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"lazy": false, "defaultHeight": {"large": "80px", "small": "50px"}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto", "fontSize": "10px", "background-color": "#f6f4eb"}, "components": [{"type": "sitewide", "name": "MktEdfsSmall", "data": {"lazy": false, "experimentRunning": false, "styles": {"headline": {"padding": "0", "fontSize": "10px"}, "detailsButton": {"paddingRight": "0px"}}, "defaultData": {"textStrong": "Free Shipping", "text": "ON $50+ FOR REWARDS MEMBERS", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=1301", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "SIGN IN OR JOIN", "path": "/my-account/sign-in", "style": {"letterSpacing": "0", "display": "inline", "position": "relative", "top": "-1px", "fontSize": "10px", "fontWeight": "400", "paddingRight": "0", "fontFamily": "EuclidCircularB, '<PERSON><PERSON><PERSON>', Helvetica, Arial, sans-serif"}}}}, {"name": "HTMLInjectionComponent", "type": "sitewide", "desc": "override-pdp-header-color", "data": {"html": "<style> .page-product .sitewide-949mjn-StyledMobileHeader:not(:hover) .ewg61dq1, .page-product .sitewide-949mjn-StyledMobileHeader:not(:hover) .e123k0v90, .page-product .sitewide-949mjn-StyledMobileHeader:not(:hover) .e123k0v91 { stroke: black; } .page-product .sitewide-949mjn-StyledMobileHeader:not(:hover) .e1m8srwu0 { fill: black; } .page-product .sitewide-949mjn-StyledMobileHeader:not(:hover) .e943t160 { display: none; } .page-product .sitewide-949mjn-StyledMobileHeader:not(:hover) .e943t161 { display: block; } </style>", "style": {}, "classes": ""}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "desc": "override-pdp-header-color", "data": {"html": "<style> .page-product .sitewide-949mjn-StyledMobileHeader:not(:hover) .ewg61dq1, .page-product .sitewide-949mjn-StyledMobileHeader:not(:hover) .e123k0v90, .page-product .sitewide-949mjn-StyledMobileHeader:not(:hover) .e123k0v91 { stroke: black; } .page-product .sitewide-949mjn-StyledMobileHeader:not(:hover) .e1m8srwu0 { fill: black; } .page-product .sitewide-949mjn-StyledMobileHeader:not(:hover) .e943t160 { display: none; } .page-product .sitewide-949mjn-StyledMobileHeader:not(:hover) .e943t161 { display: block; } </style>", "style": {}, "classes": ""}}]}}}}]}}, "popup": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "DynamicModal_11222022", "noteForWCD": "DO NOT CHANGE INSTANCE NAME (DynamicModal_11222022)", "type": "builtin", "name": "div", "experimentRunning": true, "data": {"components": [], "shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}, "promorover": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "promorover_053122-segmentation", "name": "MktSticker", "type": "sitewide", "redpointExperimentRunning": true, "experimentRunning": true, "meta": {"includePageTypes": ["division", "product", "category", "search", "info"]}, "data": {"shouldWaitForOptimizely": true, "isVisible": {"small": false, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {}, "mobile": {}}, "defaultHeight": "0px", "largeImg": "/Asset_Archive/BRWeb/content/0030/014/406/assets/BRSP230515_SITE_US_ILP_CardAcq_6M_Pass_Rover1.svg", "altText": "Banana Republic Rewards. Get 20% off. Your First purchase when you open and use a Banana Republic Rewards Credit Card. Exclusions Apply. Apply Now", "localStorageKey": "wcd_brRoverStorage_051523", "localStorageVal": "BRroverHasBeenClosed_051523", "stickerAriaLabel": "stickerAriaLabel", "stickerCloseButtonAriaLabel": "stickerCloseButtonAriaLabel", "href": "https://bananarepublic.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=BRSPROV&mlink=55297,********,rover_Card_Acq", "hrefTarget": "_blank"}}]}}, "footer": {"type": "sitewide", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components": [{"type": "builtin", "name": "div", "data": {"props": {"className": "optly-hpEmailAndIcons-target", "style": {"backgroundColor": "#F6F4EB", "borderTop": "1px solid #000"}}, "components": [{"instanceName": "hpEmailAndIcons", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "189px"}, "desktop": {"shouldDisplay": true, "data": {"style": {"maxWidth": "1400px", "margin": "0 auto", "paddingTop": "1.875rem", "backgroundColor": "transparent"}, "components": [{"type": "builtin", "name": "div", "tileStyle": {"desktop": {"font-family": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "display": "flex", "alignItems": "center", "maxWidth": "66.667%"}}, "data": {"style": {"width": "100%"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"fontSize": "3rem", "fontWeight": "100", "paddingBottom": "unset", "letterSpacing": "-0.02em", "line-height": "1.25", "text-transform": "uppercase", "-moz-osx-font-smoothing": "grayscale", "maxWidth": "87.5%", "margin-left": "6.25%"}, "components": ["Keep "]}}, {"type": "builtin", "name": "span", "data": {"style": {"fontSize": "3rem", "fontWeight": "100", "paddingBottom": "unset", "letterSpacing": "-0.02em", "line-height": "1.25", "text-transform": "lowercase", "-moz-osx-font-smoothing": "grayscale", "font-style": "italic"}, "components": ["in "]}}, {"type": "builtin", "name": "span", "data": {"style": {"fontSize": "3rem", "fontWeight": "100", "paddingBottom": "unset", "letterSpacing": "-0.02em", "line-height": "1.25", "text-transform": "uppercase", "-moz-osx-font-smoothing": "grayscale"}, "components": ["Touch"]}}, {"type": "builtin", "name": "div", "tileStyle": {"desktop": {"display": "flex", "alignItems": "center", "maxWidth": "50%"}}, "data": {"style": {"display": "flex", "width": "100%"}, "components": [{"type": "builtin", "name": "div", "tileStyle": {"desktop": {"display": "flex", "alignItems": "center", "maxWidth": "25%"}}, "data": {"style": {"width": "100%"}, "components": [{"name": "EmailRegistrationForm", "type": "sitewide", "data": {"defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {"padding": "0", "margin": "0 auto", "maxWidth": "75%"}, "mobile": {"padding": "0", "margin": "0"}}, "targetURL": "/profile/info.do?cid=53444&mlink=5001,9495925,site_footer_02252015&clink=9495925", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:site_footer_02252015;ON:NA;PL:NA;AT:NA;BRFS:NA;GPO:NA"]}, "textInputOptions": {"label": "Enter Your Email Address", "errorMessage": "please enter a valid email address", "desktop": {"className": "", "crossBrand": false, "inverse": false}, "mobile": {"className": "", "crossBrand": false, "inverse": false}}, "submitButtonOptions": {"text": "Sign Up for Emails", "desktop": {"className": "emailSubmitBtn", "variant": "border", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "primary", "style": {"border-radius": "10px", "min-width": "50%", "fontSize": "0.75rem", "fontFamily": "Lynstone", "margin-top": "25px", "padding": "0px 25px", "height": "2.75rem", "letter-spacing": "2px", "backgroundColor": "transparent"}}, "mobile": {"className": "signButton label-a", "variant": "outline", "size": "small", "fullWidth": true, "crossBrand": false, "color": "primary", "style": {"border-radius": "10px", "min-width": "50%", "fontSize": "0.75rem", "fontFamily": "Lynstone", "height": "2.75rem", "letter-spacing": "2px", "backgroundColor": "transparent"}}}, "errorNotificationAriaLabel": "Error"}}]}}, {"type": "builtin", "name": "div", "tileStyle": {"desktop": {"alignItems": "center", "maxWidth": "25%"}}, "data": {"style": {"width": "100%"}, "components": [{"type": "sitewide", "name": "SMSForm", "data": {"phoneInputData": {"aria-label": "", "label": "Enter Your Phone Number"}, "apiParams": {"campaign_id": "8mp2I6WJ", "acquisition_id": "pbapRSuN", "src_gnrc_sms": "WEBSITE_SIGNUP", "src_spfc_sms": "BRUS_FOOTERSIGNUP"}, "buttonText": "Sign up for Texts", "styles": {"formStyles": {"align-items": "flex-end", "justify-content": "space-evenly", "width": "75%", "margin": "0 auto"}, "buttonStyles": {"border": "2px solid rgb(0, 0, 0)", "border-radius": "10px", "padding": "0px 25px", "background-color": "transparent", "color": "rgb(0, 0, 0)", "min-width": "50%;", "font-size": "0.75rem;", "margin-top": "25px;", "height": "2.75rem", "letter-spacing": "2px"}, "phoneInputStyles": {"width": "90%", "min-height": "0px"}}}}]}}]}}, {"type": "builtin", "name": "div", "tileStyle": {"desktop": {"display": "flex", "alignItems": "center", "maxWidth": "50%"}}, "data": {"style": {"margin": "0 auto", "padding": "25px 0px 25px 55px"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"fontFamily": "Lynstone", "background": "transparent", "color": "#000", "display": "inline", "maxWidth": "87.5%", "margin": "auto", "font-size": "12px", "line-height": "0.9rem"}, "components": ["*Msg & Data Rates May Apply. By entering your phone number, clicking submit, and completing the sign-up instructions, you consent to receive one or more recurring marketing text messages each week at the mobile number provided that may be sent via an automated system, and you also consent to the "]}}, {"type": "builtin", "name": "a", "data": {"props": {"style": {"fontFamily": "Lynstone", "background": "transparent", "color": "000", "display": "inline", "maxWidth": "87.5%", "margin": "auto", "font-size": "12px", "line-height": "0.9rem", "textDecoration": "underline"}, "href": "https://attnl.tv/t/8b8", "target": "_blank"}, "components": ["text terms & privacy policy"]}}, {"type": "builtin", "name": "span", "data": {"style": {"fontFamily": "Lynstone", "background": "transparent", "color": "#000", "display": "inline", "maxWidth": "87.5%", "margin": "auto", "font-size": "12px", "line-height": "0.9rem"}, "components": [". Consent is not a condition of purchasing goods or services. You can opt-out at any time by responding STOP. You can also respond HELP for help."]}}]}}]}}, {"instanceName": "footer_icons", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "tileStyle": {"desktop": {"maxWidth": "50%", "flex-direction": "column", "alignSelf": "center"}}, "data": {"shouldWaitForOptimizely": false, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "lazy": false, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BRWeb/content/0028/661/552/assets/footer/FooterSlice1_updated.svg", "desktopSrcUrl": "/Asset_Archive/BRWeb/content/0028/661/552/assets/footer/FooterSlice1_updated.svg", "alt": "Find a store.", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "ctaList": {"className": "", "mobilePositionAboveContent": false, "style": {"textAlign": "center"}, "desktopStyle": {}, "ctas": [{"composableButtonData": {"children": "Find a store", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "81.5%", "left": "23.5%", "height": "6%", "width": "53.5%"}, "desktopStyle": {"top": "19%", "left": "33%", "height": "15%", "width": "47%"}}, "linkData": {"to": "/stores?mlink=hp_widget_FAS"}}, {"composableButtonData": {"children": "Gift Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "81.5%", "left": "23.5%", "height": "6%", "width": "53.5%"}, "desktopStyle": {"top": "41%", "left": "33%", "height": "15%", "width": "47%"}}, "linkData": {"to": "/customerService/info.do?cid=61830&mlink=hp_widget_GC"}}, {"composableButtonData": {"children": "Credit Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "81.5%", "left": "23.5%", "height": "6%", "width": "53.5%"}, "desktopStyle": {"top": "62%", "left": "33%", "height": "15%", "width": "47%"}}, "linkData": {"to": "https://bananarepublic.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=BRSSUNIFTD"}}]}}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"margin": "0 auto", "paddingTop": "32px", "backgroundColor": "#F6F4EB"}, "components": [{"type": "builtin", "name": "div", "tileStyle": {"mobile": {"font-family": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "display": "flex", "alignItems": "center"}}, "data": {"style": {"width": "100%"}, "components": [{"type": "builtin", "name": "div", "tileStyle": {"mobile": {"display": "flex", "alignItems": "center"}}, "data": {"style": {"display": "block", "width": "90%", "margin": "0 auto", "textAlign": "center"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"fontSize": "8vw", "fontWeight": "400", "paddingBottom": "unset", "letterSpacing": "-0.02em", "line-height": "1.25", "text-transform": "uppercase", "-moz-osx-font-smoothing": "grayscale"}, "components": ["Keep "]}}, {"type": "builtin", "name": "span", "data": {"style": {"fontSize": "8vw", "fontWeight": "400", "paddingBottom": "unset", "letterSpacing": "-0.02em", "line-height": "1.25", "text-transform": "lowercase", "-moz-osx-font-smoothing": "grayscale", "font-style": "italic"}, "components": ["in "]}}, {"type": "builtin", "name": "span", "data": {"style": {"fontSize": "8vw", "fontWeight": "400", "paddingBottom": "unset", "letterSpacing": "-0.02em", "line-height": "1.25", "text-transform": "uppercase", "-moz-osx-font-smoothing": "grayscale"}, "components": ["Touch"]}}]}}, {"type": "builtin", "name": "div", "tileStyle": {"mobile": {"display": "flex", "alignItems": "center"}}, "data": {"style": {"display": "flex", "width": "100%", "flex-direction": "column"}, "components": [{"type": "builtin", "name": "div", "tileStyle": {"mobile": {"display": "flex", "alignItems": "center"}}, "data": {"style": {"width": "100%"}, "components": [{"name": "EmailRegistrationForm", "type": "sitewide", "data": {"defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {}, "mobile": {"padding": "0", "margin": "0 auto", "maxWidth": "65%"}}, "targetURL": "/profile/info.do?cid=53444&mlink=48608,2804525,email_v&clink=2804525&Footer=true", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:site_footer_02252015;ON:NA;PL:NA;AT:NA;BRFS:NA;GPO:NA"]}, "textInputOptions": {"label": "Enter Your Email Address", "errorMessage": "please enter a valid email address", "desktop": {"className": "", "crossBrand": false, "inverse": false}, "mobile": {"className": "", "crossBrand": false, "inverse": false}}, "submitButtonOptions": {"text": "Sign Up for Emails", "mobile": {"className": "emailSubmitBtn", "variant": "border", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "primary", "style": {"min-width": "50%", "fontSize": "0.75rem", "margin": "1rem auto", "height": "2.75rem", "letter-spacing": "2px", "display": "block", "backgroundColor": "transparent"}}}, "errorNotificationAriaLabel": "Error"}}]}}, {"type": "builtin", "name": "div", "tileStyle": {"mobile": {"alignItems": "center"}}, "data": {"style": {"width": "100%"}, "components": [{"type": "sitewide", "name": "SMSForm", "data": {"phoneInputData": {"aria-label": "", "label": "Enter Your Phone Number"}, "apiParams": {"campaign_id": "8mp2I6WJ", "acquisition_id": "pbapRSuN", "src_gnrc_sms": "WEBSITE_SIGNUP", "src_spfc_sms": "BRUS_FOOTERSIGNUP"}, "buttonText": "Sign up for Texts", "styles": {"formStyles": {"align-items": "flex-end", "justify-content": "space-evenly", "width": "65%", "margin": "0 auto"}, "buttonStyles": {"border": "2px solid rgb(0, 0, 0)", "padding": "0.5em 0.8em", "background-color": "transparent", "color": "rgb(0, 0, 0)", "min-width": "50%;", "font-size": "0.75rem;", "margin": "1rem auto", "height": "2.75rem", "letter-spacing": "2px", "display": "block"}, "phoneInputStyles": {"width": "100%", "min-height": "0px"}}}}]}}]}}, {"type": "builtin", "name": "div", "tileStyle": {"mobile": {"display": "flex", "alignItems": "center"}}, "data": {"style": {"width": "90%", "margin": "0 auto", "padding": "20px", "textAlign": "center", "line-height": "1"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"background": "transparent", "color": "#000", "display": "inline", "maxWidth": "87.5%", "margin": "auto", "font-size": "10px"}, "components": ["*Msg & Data Rates May Apply. By entering your phone number, clicking submit, and completing the sign-up instructions, you consent to receive one or more recurring marketing text messages each week at the mobile number provided that may be sent via an automated system, and you also consent to the "]}}, {"type": "builtin", "name": "a", "data": {"props": {"style": {"background": "transparent", "color": "#000", "display": "inline", "maxWidth": "87.5%", "margin": "auto", "font-size": "10px", "textDecoration": "underline"}, "href": "https://attnl.tv/t/8b8", "target": "_blank"}, "components": ["text terms & privacy policy"]}}, {"type": "builtin", "name": "span", "data": {"style": {"background": "transparent", "color": "#000", "display": "inline", "maxWidth": "87.5%", "margin": "auto", "font-size": "10px"}, "components": [". Consent is not a condition of purchasing goods or services. You can opt-out at any time by responding STOP. You can also respond HELP for help."]}}]}}]}}]}}}}]}}, {"name": "LayoutComponent", "type": "sitewide", "instanceName": "bottomBanner", "meta": {}, "data": {"defaultHeight": "0px", "desktop": {"shouldDisplay": true, "data": {"style": {"backgroundColor": "#F6F4EB"}, "components": [{"type": "builtin", "name": "div", "data": {"props": {"className": "", "style": {"maxWidth": "1400px", "margin": "0 auto"}}, "components": [{"type": "builtin", "name": "div", "experimentRunning": true, "instanceName": "LDTO_footer_bottom_banner_desktop", "data": {"style": {"display": "block", "backgroundColor": "transparent", "color": "#000", "lineHeight": "1.25em", "margin": "25px 0px 25px 55px", "fontWeight": "normal", "letterSpacing": "0.04em", "font-size": "1rem", "fontFamily": "Lynstone"}, "components": [{"type": "builtin", "name": "span", "data": {"props": {"style": {"textTransform": "uppercase", "display": "inline"}}, "components": ["Rewards Members "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": ["get "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"textTransform": "uppercase", "display": "inline"}}, "components": ["free shipping "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": ["on all orders $50+ "]}}, {"type": "builtin", "name": "a", "data": {"props": {"style": {"display": "inline", "textDecoration": "underline"}, "href": "https://secure-bananarepublic.gap.com/my-account/sign-in?mlink=55299,********,sitewidefooter_ILP_signin"}, "components": ["Sign In"]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": [" or "]}}, {"type": "builtin", "name": "a", "data": {"props": {"style": {"display": "inline", "textDecoration": "underline"}, "href": "https://secure-bananarepublic.gap.com/my-account/sign-in?&mlink=55299,********,sitewidefooter_ILP_join"}, "components": ["Join"]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": [" "]}}, {"name": "ComposableButton", "data": {"modalProps": {"title": "SHIPPING & RETURNS", "src": "/customerService/info.do?cid=1301", "height": "650px", "width": "100%", "color": "#000", "font-family": "<PERSON><PERSON><PERSON>, Arial, Sans-serif", "font-size": "10px", "line-height": "12px", "closeButtonAriaLabel": "close legal details modal"}, "borderThickness": "thin", "bright": false, "capitalization": "uppercase", "className": "wcd_headpromo__modalLink", "crossBrand": false, "font": "secondary", "fullWidth": false, "variant": "underline", "buttonText": "DETAILS", "style": {"color": "#000", "fontSize": ".75rem", "font-weight": "normal", "textTransform": "none", "marginLeft": "0.3rem", "padding": "0", "margin-bottom": "5px"}}}]}}]}}, {"instanceName": "footer_icons", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "tileStyle": {"desktop": {"display": "none"}}, "data": {"shouldWaitForOptimizely": false, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "lazy": false, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BRWeb/content/0029/555/919/assets/footer/BRSP220202_SITE_US_UpdatedFooter_Icons_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BRWeb/content/0029/555/919/assets/footer/BRSP220202_SITE_US_UpdatedFooter_Icons_TXT_SM.svg", "alt": "Find a store.", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "ctaList": {"className": "", "mobilePositionAboveContent": false, "style": {"textAlign": "center"}, "desktopStyle": {}, "ctas": [{"composableButtonData": {"children": "Find a store", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "0%", "height": "100%", "width": "33.33%"}}, "linkData": {"to": "/stores?mlink=hp_widget_FAS"}}, {"composableButtonData": {"children": "Gift Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "33.33%", "height": "100%", "width": "33.34%"}}, "linkData": {"to": "/customerService/info.do?cid=61830&mlink=hp_widget_GC"}}, {"composableButtonData": {"children": "Credit Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "66.67%", "height": "100%", "width": "33.33%"}}, "linkData": {"to": "https://bananarepublic.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=BRSSUNIFTD"}}]}}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"display": "block", "background": "#F6F4EB", "color": "#000", "textAlign": "center", "justifyContent": "center", "lineHeight": "1.25em", "padding": "0.5em 1em 0.25em", "fontWeight": "normal", "letterSpacing": "0.04em", "font-size": "1rem", "fontFamily": "Lynstone", "borderBottom": "1px solid #000"}, "components": [{"type": "builtin", "name": "div", "data": {"props": {"className": "optly-bottomBanner-rewards-target"}, "components": [{"type": "builtin", "name": "div", "experimentRunning": true, "instanceName": "LDTO_footer_bottom_banner_mobile", "data": {"style": {"display": "block", "background": "transparent", "color": "#000", "textAlign": "center", "justifyContent": "center", "lineHeight": "1.25em", "padding": "0.5em 1em 0.25em", "fontWeight": "normal", "letterSpacing": "0.04em", "font-size": "1rem", "fontFamily": "Lynstone"}, "components": [{"type": "builtin", "name": "span", "data": {"props": {"style": {"textTransform": "uppercase", "display": "inline"}}, "components": ["Rewards Members "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": ["get "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"textTransform": "uppercase", "display": "inline"}}, "components": ["free shipping "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": ["on all orders $50+ "]}}, {"type": "builtin", "name": "a", "data": {"props": {"style": {"display": "inline", "textDecoration": "underline"}, "href": "https://secure-bananarepublic.gap.com/my-account/sign-in?mlink=55299,********,sitewidefooter_ILP_signin"}, "components": ["Sign In"]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": [" or "]}}, {"type": "builtin", "name": "a", "data": {"props": {"style": {"display": "inline", "textDecoration": "underline"}, "href": "https://secure-bananarepublic.gap.com/my-account/sign-in?&mlink=55299,********,sitewidefooter_ILP_join"}, "components": ["Join"]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": [" "]}}, {"name": "ComposableButton", "data": {"modalProps": {"title": "SHIPPING & RETURNS", "src": "/customerService/info.do?cid=1301", "height": "650px", "width": "100%", "color": "#000", "font-family": "<PERSON><PERSON><PERSON>, Arial, Sans-serif", "font-size": "10px", "line-height": "12px", "closeButtonAriaLabel": "close legal details modal"}, "borderThickness": "thin", "bright": false, "capitalization": "uppercase", "className": "wcd_headpromo__modalLink", "crossBrand": false, "font": "secondary", "fullWidth": false, "variant": "underline", "buttonText": "DETAILS", "style": {"color": "#000", "fontSize": ".75rem", "font-weight": "normal", "textTransform": "none", "marginLeft": "0.3rem", "padding": "0", "margin-bottom": "5px"}}}]}}, {"instanceName": "footer_icons", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "tileStyle": {"desktop": {"display": "none"}}, "data": {"shouldWaitForOptimizely": false, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "lazy": false, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BRWeb/content/0029/555/919/assets/footer/BRSP220202_SITE_US_UpdatedFooter_Icons_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BRWeb/content/0029/555/919/assets/footer/BRSP220202_SITE_US_UpdatedFooter_Icons_TXT_SM.svg", "alt": "Find a store.", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "ctaList": {"className": "", "mobilePositionAboveContent": false, "style": {"textAlign": "center"}, "desktopStyle": {}, "ctas": [{"composableButtonData": {"children": "Find a store", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "0%", "height": "100%", "width": "33.33%"}}, "linkData": {"to": "/stores?mlink=hp_widget_FAS"}}, {"composableButtonData": {"children": "Gift Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "33.33%", "height": "100%", "width": "33.34%"}}, "linkData": {"to": "/customerService/info.do?cid=61830&mlink=hp_widget_GC"}}, {"composableButtonData": {"children": "Credit Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "66.67%", "height": "100%", "width": "33.33%"}}, "linkData": {"to": "https://bananarepublic.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=BRSSUNIFTD"}}]}}}]}}]}}}}, {"name": "Footer", "type": "sitewide", "data": {"hideFirstRow": true, "lazy": true, "showSMS": true, "smsLayout": {"headerSection": {"desktopAndMobile": {"shouldDisplay": true, "data": {"placeholderSettings": {"desktop": {"width": "100%"}}, "style": {"flexDirection": "column", "maxWidth": "600px", "width": "98%", "textAlign": "center", "position": "relative", "padding": "0px 0px 1em 0px", "margin": "0 auto"}, "components": [{"name": "HomeSVGOverlay", "type": "home", "data": {"disableAlternativeImage": true, "isVisible": {"large": true, "small": true}, "containerStyle": {"mobile": {"padding": "0 0", "marginTop": "5%"}}, "background": {"content": {"smallImg": "/Asset_Archive/BRWeb/content/0028/312/249/assets/BRfriends_didot.svg", "largeImg": "/Asset_Archive/BRWeb/content/0028/312/249/assets/BRfriends_didot.svg", "altText": "BR friends let's text. enter mobile number for early accsss to new arrivals, style tips and insider offers."}}, "svgoverlay": {"smallImg": "/Asset_Archive/BRWeb/content/0028/312/249/assets/BRfriends_didot.svg", "largeImg": "/Asset_Archive/BRWeb/content/0028/312/249/assets/BRfriends_didot.svg", "altText": "BR friends let's text. enter mobile number for early accsss to new arrivals, style tips and insider offers."}}}]}}}, "legalSection": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "maxWidth": "600px", "width": "88%", "textAlign": "center", "position": "relative", "padding": "0 0 0 0", "margin": "0 auto", "lineHeight": "1.5", "fontSize": "72%", "& span": {"backgroundColor": "transparent"}, "& a": {"backgroundColor": "transparent"}}, "components": [{"name": "TextHeadline", "type": "sitewide", "data": {"text": "*Msg & Data Rates May Apply. By entering your phone number, clicking submit, and completing the sign-up instructions, you consent to receive one or more recurring marketing text messages each week at the mobile number provided that may be sent via an automated system, and you also consent to the ", "className": {}}}, {"name": "LinkWithModal", "type": "sitewide", "data": {"type": "link", "children": "text terms & privacy policy.", "to": "https://attnl.tv/t/8b8", "style": {"textDecoration": "underline"}, "className": ""}}, {"name": "TextHeadline", "type": "sitewide", "data": {"text": "Consent is not a condition of purchasing goods or services. You can opt-out at any time by responding STOP. You can also respond HELP for help.", "className": {}}}]}}}}, "carousel": {"slides": [{"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0"}, "desktop": {"padding": "0 0", "marginBottom": "4rem"}}, "background": {"content": {"smallImg": "/assets/common/clear.gif", "largeImg": "/Asset_Archive/BRWeb/content/0015/556/416/assets/carousel/FindAStore_IMG.jpg", "altText": "Find A Store"}, "style": {"desktop": {"width": "auto"}}}, "svgoverlay": {"smallImg": "/assets/common/clear.gif", "largeImg": "/Asset_Archive/BRWeb/content/0015/556/416/assets/carousel/FindAStore_IMG.jpg", "altText": "Find A Store", "link": {"url": "/stores?mlink=1095711,17322994,findAStore&clink=17322994", "tid": "findAStore"}}, "linksContainerStyle": {"desktop": {"top": "88%", "left": "0", "width": "100%", "boxSizing": "border-box"}}, "links": {"verticalStacking": false, "buttonClasses": "carousel__cta", "style": {"desktop": {}}, "content": [{"tid": "findAStore", "url": "/stores?mlink=1095711,17322994,findAStore&clink=17322994", "text": "FIND A STORE"}]}}}, {"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0"}, "desktop": {"padding": "0 0", "marginBottom": "4rem"}}, "background": {"content": {"smallImg": "/assets/common/clear.gif", "largeImg": "/Asset_Archive/BRWeb/content/0014/917/015/assets/carousel/GiftCards2.svg", "altText": "Shop Gift Cards"}}, "svgoverlay": {"smallImg": "/assets/common/clear.gif", "largeImg": "/Asset_Archive/BRWeb/content/0014/917/015/assets/carousel/GiftCards2.svg", "altText": "Shop Gift Cards", "link": {"url": "/customerService/info.do?cid=61830&mlink=5001,17277801,quickgift&clink=17277801", "tid": "quickgift"}}, "linksContainerStyle": {"desktop": {"top": "88%", "left": "0", "width": "100%", "boxSizing": "border-box"}}, "links": {"verticalStacking": false, "buttonClasses": "carousel__cta", "style": {"desktop": {}}, "content": [{"tid": "quickgift", "url": "/customerService/info.do?cid=61830&mlink=5001,17277801,quickgift&clink=17277801", "text": "BUY NOW"}]}}}, {"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0"}, "desktop": {"padding": "0 0", "marginBottom": "4rem"}}, "background": {"content": {"smallImg": "/assets/common/clear.gif", "largeImg": "/Asset_Archive/BRWeb/content/0028/312/249/assets/footer/BRSP200630_SITE_US_StylePassport_HP_Footer.svg", "altText": "Style Passport Rental Service. Free trial on your first rental month."}, "style": {"desktop": {"width": "auto"}}}, "svgoverlay": {"smallImg": "/assets/common/clear.gif", "largeImg": "/Asset_Archive/BRWeb/content/0028/312/249/assets/footer/BRSP200630_SITE_US_StylePassport_HP_Footer.svg", "altText": "Style Passport Rental Service. Free trial on your first rental month.", "link": {"url": "https://www.brstylepassport.com/?tc=T-310082&utm_source=footer&utm_medium=ecomm&utm_content=20200925&utm_campaign=06.19.20_09.24.20_footer_carousel_FT-offer_BR-30df-june20-bau", "tid": "stylepassport"}}, "linksContainerStyle": {"desktop": {"top": "88%", "left": "0", "width": "100%", "boxSizing": "border-box"}}, "links": {"verticalStacking": false, "buttonClasses": "carousel__cta", "style": {"desktop": {"textTransform": "uppercase"}}, "content": [{"tid": "stylepassport", "url": "https://www.brstylepassport.com/?tc=T-310083&utm_source=footer&utm_medium=ecomm&utm_content=20200925&utm_campaign=06.19.20_09.24.20_footer_text-only_FT-offer_BR-30df-june20-bau", "text": "Let's Go"}]}}}, {"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0"}, "desktop": {"padding": "0 0", "marginBottom": "4rem"}}, "background": {"content": {"smallImg": "/assets/common/clear.gif", "largeImg": "/Asset_Archive/BRWeb/content/0018/834/500/assets/footer/BRSP_200218_SITE_HP_ThredUP_Footer_XL.jpg", "altText": "ThreadUp sustainable shop"}, "style": {"desktop": {"width": "auto"}}}, "svgoverlay": {"smallImg": "/assets/common/clear.gif", "largeImg": "/Asset_Archive/BRWeb/content/0018/834/500/assets/footer/BRSP_200218_SITE_HP_ThredUP_Footer_XL.jpg", "altText": "ThreadUp sustainable shop", "link": {"url": "https://www.thredup.com/bananarepublic", "tid": "threadup"}}, "linksContainerStyle": {"desktop": {"top": "88%", "left": "0", "width": "100%", "boxSizing": "border-box"}}, "links": {"verticalStacking": false, "buttonClasses": "carousel__cta", "style": {"desktop": {"textTransform": "uppercase"}}, "content": [{"tid": "stylepassport", "url": "https://www.thredup.com/bananarepublic", "text": "Learn More"}]}}}]}, "defaultHeight": {"large": "300px"}, "footerCustomerSupport": {"desktop": {"columns": [{"header": {}, "links": []}, {"header": {"text": "Customer Support", "className": "brfLinksHeader"}, "links": [{"type": "link", "text": "Stores & Services", "to": "/browse/info.do?cid=1194369", "className": "brfLinks"}, {"type": "link", "text": "Shipping and handling", "to": "/customerService/info.do?cid=80740&cs=shipping_and_handling", "className": "brfLinks"}, {"type": "link", "text": "Returns/Exchanges", "to": "/customerService/info.do?cid=80735", "className": "brfLinks"}, {"type": "link", "text": "Gift Cards", "to": "/customerService/info.do?cid=61830", "className": "brfLinks"}, {"type": "link", "text": "Size Charts", "to": "/customerService/info.do?cid=80743&cs=size_charts", "className": "brfLinks"}, {"type": "link", "text": "Buy Online, Pick Up in Store", "to": "/customerService/info.do?cid=80740&cs=buy_online_pickup_in-store", "target": "blank", "className": "brfLinks"}]}, {"header": {"text": "Styling Service / Appointments", "className": "brfLinksHeader"}, "links": [{"type": "link", "text": "BR Atelier Styling Service", "to": "/browse/info.do?cid=1194368", "className": "brfLinks"}, {"type": "link", "text": "BR Atelier Store Locations", "to": "/stores", "className": "brfLinks"}]}, {"header": {"text": "BR Rewards", "className": "brfLinksHeader"}, "links": [{"type": "link", "text": "My Points and Rewards", "to": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=55298,********,footer_ILP_points_rewards", "rel": "noopener", "className": "brfLinks"}, {"type": "link", "text": "Explore Benefits", "to": "/customerService/info.do?cid=1098875&sitecode=BRSSUNIFTD&mlink=55298,********,footer_ILP_explore_benefits", "className": "brfLinks"}, {"type": "link", "text": "Pay Credit Card Bill", "to": "https://www.bananarepublic.barclaysus.com/payment", "className": "brfLinks"}, {"type": "link", "text": "Activate Credit Card", "to": "https://www.bananarepublic.barclaysus.com/activate", "target": "blank", "rel": "noopener", "className": "brfLinks"}, {"type": "link", "text": "Join <PERSON> Rewards—it’s Free", "to": "/my-account/sign-in?mlink=55298,********,footer_ILP_join", "target": "blank", "rel": "noopener", "className": "brfLinks", "style": {"marginTop": "0.5rem", "fontSize": "12px", "lineHeight": "12px", "padding": "4px 4px 1px"}}, {"type": "link", "text": "or Apply Now for a Credit Card", "to": "https://bananarepublic.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=BRSSUNIFTD&mlink=55298,********,footer_ILP_Card_Apply", "target": "blank", "rel": "noopener", "className": "brfLinks", "style": {"fontSize": "12px", "lineHeight": "12px", "padding": "1px 4px 4px"}}]}, {"header": {"text": "FIND US", "className": "brfLinksHeader"}, "links": [{"type": "link", "text": "1-888-<PERSON><PERSON><PERSON><PERSON> (**************)", "to": "tel:************", "className": "brfLinks"}, {"type": "link", "text": "Store Locator", "to": "/stores", "className": "brfLinks"}, {"type": "link", "text": "BR Home Stores", "to": "/browse/info.do?cid=3023473", "className": "brfLinks"}, {"type": "link", "text": "Banana Republic Factory Store", "to": "https://bananarepublicfactory.gapfactory.com/", "className": "brfLinks"}]}, {"header": {}, "links": []}]}, "mobile": {"links": [{"type": "link", "text": "Store Locator", "to": "/stores", "className": "footer-item"}, {"type": "link", "text": "Customer Service", "to": "/customerService/info.do?cid=6740", "className": "footer-item"}, {"type": "link", "text": "Orders & Returns", "to": "/my-account/order-lookup", "className": "footer-item"}, {"type": "link", "text": "Shipping & Delivery", "to": "/customerService/info.do?cid=80740&cs=shipping_and_delivery", "className": "footer-item"}, {"type": "accordion", "text": "Gift Cards", "accordionLinks": [{"type": "link", "text": "Buy eGift Cards", "to": "https://bananarepublic-m.cashstar.com/buy/", "target": "_blank", "className": "footer-item"}, {"type": "link", "text": "Buy Gift Cards", "to": "/customerService/info.do?cid=61830", "target": "_blank", "className": "footer-item"}]}, {"type": "accordion", "text": "Banana Republic Rewards", "accordionLinks": [{"type": "link", "text": "My Points and Rewards", "to": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=55298,********,footer_ILP_points_rewards", "target": "_blank", "className": "footer-item", "style": {"fontSize": "12px", "padding": "2px 4px"}}, {"type": "link", "text": "Explore Benefits", "to": "/customerService/info.do?cid=1098875&sitecode=BRSSUNIFTM&mlink=55298,********,footer_ILP_explore_benefits", "target": "_blank", "className": "footer-item", "style": {"fontSize": "12px", "padding": "2px 4px"}}, {"type": "link", "text": "Pay Credit Card Bill", "to": "https://www.bananarepublic.barclaysus.com/payment", "target": "_blank", "className": "footer-item", "style": {"fontSize": "12px", "padding": "2px 4px"}}, {"type": "link", "text": "Activate Credit Card", "to": "https://www.bananarepublic.barclaysus.com/activate", "target": "_blank", "className": "footer-item", "style": {"fontSize": "12px", "padding": "2px 4px"}}, {"type": "link", "text": "Join <PERSON> Rewards—it’s Free", "to": "/my-account/sign-in?mlink=55298,********,footer_ILP_join", "target": "_blank", "className": "footer-item", "style": {"fontSize": "12px", "padding": "2px 4px"}}, {"type": "link", "text": "Apply Now for a Credit Card", "to": "https://bananarepublic.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=BRSSUNIFTM&mlink=55298,********,footer_ILP_Card_Apply", "target": "blank", "rel": "noopener", "style": {"fontSize": "12px", "lineHeight": "12px", "padding": "1px 4px 4px"}}]}, {"type": "link", "text": "Email sign up", "to": "/profile/info.do?cid=53443", "target": "_blank", "rel": "noopener", "className": "footer-item"}, {"type": "accordion", "text": "Shop Our Other Brands", "accordionLinks": [{"type": "link", "text": "Gap", "to": "https://www.gap.com/?ssiteID=br", "target": "_blank", "className": "footer-item"}, {"type": "link", "text": "Old Navy", "to": "https://oldnavy.gap.com/?ssiteID=br", "target": "_blank", "className": "footer-item"}, {"type": "link", "text": "Athleta", "to": "https://athleta.gap.com/?ssiteID=br", "target": "_blank", "className": "footer-item"}]}]}}, "copyRights": {"rows": [[{"text": "Download our App", "to": "https://bananarepublic.onelink.me/6L9o/e907381d", "className": ""}], [{"text": "© 2022 Banana Republic, LLC"}, {"text": "Privacy Policy", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy"}, {"text": "Do Not Sell My Info", "doNotSell": true}, {"text": "Interest Based Ads", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy#Interest-Based-Ad"}, {"text": "Your California Privacy Rights", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy#Your-California-Privacy-Rights"}, {"text": "Terms of Use", "to": "/customerService/info.do?cid=1317"}, {"text": "Careers", "to": "https://jobs.gapinc.com/banana-republic-home"}, {"text": "Social Responsibility", "to": "https://www.gapinc.com/en-us/values/sustainability"}, {"text": "About Gap Inc.", "to": "http://www.gapinc.com/content/gapinc/html/aboutus.html", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}], [{"text": "Americans with Disabilities Act", "to": "/customerService/info.do?cid=1005458"}, {"text": "California Transparency in Supply Chains Act", "to": "https://www.gapinc.com/content/gapinc/html/sustainability/ca-transparency-insupplychainsact.html"}, {"text": "Gap Inc. Policies", "to": "http://www.gapincsustainability.com/policies", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}]]}}}]}, "topnav": {"name": "div", "type": "builtin", "instanceName": "MegaNav", "data": {"style": {"width": "100%"}, "tabletStyle": {}, "desktopStyle": {}, "props": {}, "tabletProps": {}, "desktopProps": {}, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style> .page-product .sitewide-ljziyx-StyledDesktopHeader:not(:hover) .divisionLink, .page-product .sitewide-ljziyx-StyledDesktopHeader:not(:hover) .search--input::placeholder { color: black; } .page-product .sitewide-ljziyx-StyledDesktopHeader:not(:hover) button[aria-label='search'] path { fill: black; } .page-product .sitewide-ljziyx-StyledDesktopHeader:not(:hover) .divisionLink._selected { color: #929292; } .page-product .sitewide-ljziyx-StyledDesktopHeader:not(:hover) .e943t160 { display: none; } .page-product .sitewide-ljziyx-StyledDesktopHeader:not(:hover) .e943t161 { display: block; } @media(min-width: 1024px) and (max-width: 1200px){ #topNavWrapper .topNavLink > div > a.divisionLink { font-size: 16px; } }@media(min-width: 1024px) { div[data-testid='header-only-logo-at-top'] {max-width: min(98vw - 32px, 1400px);} .page-product ~ div[aria-label='stickerAriaLabel'] ~ .embeddedServiceHelpButton .helpButton { bottom: 12rem; } .meganav { box-shadow: 0px 10px 10px -4px rgba(0 0 0 / 15%);}}</style>", "style": {}, "classes": ""}}, {"instanceName": "MegaNav", "type": "sitewide", "name": "MegaNav", "data": {"isNavSticky": true, "classStyles": {"topnav-container > ul.topnav": "margin: 0 auto; max-width: 900px;", "topnav > li.topNavLink": "padding: 35px 0px 30px 0px;", "topNavLink > div > a.divisionLink": "font-family: BananaSerif; font-weight: 400; font-size: 16px; letter-spacing: 0.12em", "topnav-container .flyout-svg, .topnav-container .flyout-svg": "top:0; left:0", "meganav .catnav--item .flyoutnav-hover": "max-width: 208px; display: block", "flyoutnav-hover .flyout-svg": "visibility: hidden", "flyoutnav-hover:hover .flyout-svg": "visibility: visible", "topnav-container .gifts-flyout-svg, .topnav-container .flyout-svg": "top:0; left:0", "meganav-category-group.custom .catnav--header > span": "font-weight: 700; -webkit-font-smoothing: antialiased; color: rgb(0, 0, 0); display: block; margin-bottom: 8px; padding: 0px 0px 0.5rem; border-bottom: 0.5px solid rgb(146, 146, 146);", "wcdNavLimit .meganav-column[class*='sitewide-']:last-child": "margin-left: 32px", "wcd_full": "width: 100%;"}, "activeDivisions": [{"name": "Shop by", "divisionId": ["New"], "megaNavOrder": [["1176968", "1176971"], ["1176970", "1176972"], ["1191480"], ["1191550", "3014288"]], "exclusionIds": ["56247"], "customStyles": {}}, {"name": "Women", "divisionId": ["5002"], "megaNavOrder": [["5003"], ["1036334", "1008603"], ["1033063", "3013366"], ["1165862", "1058885", "1045596", "1122625", "3026526"]], "exclusionIds": ["56247"], "customStyles": {}}, {"name": "Men", "divisionId": ["5343"], "megaNavOrder": [["1016384"], ["1036307", "1129138"], ["5487", "3013371"], ["38675", "1029753", "1045595", "1122624", "3026538"]], "exclusionIds": ["1063043", "1102425", "1101787"], "customStyles": {"1139745": {"inlineStyle": {"font-weight": "600"}}}}, {"name": "Home", "divisionId": ["https://www.brhome.com"], "divisionLinkProps": {"target": "_blank"}, "megaNavOrder": [], "exclusionIds": [], "customStyles": {}}, {"name": "Gifts", "divisionId": ["/browse/category.do?cid=3012158"], "megaNavOrder": [["1185727"], ["1185732"], ["3013152"], ["1185730"], ["3013153"]], "exclusionIds": [], "customStyles": {}}, {"name": "Occasion", "divisionId": ["Occasion"], "megaNavOrder": [["3026270"], ["3026271"]], "exclusionIds": [], "customStyles": {}}, {"name": "BR Classics", "divisionId": ["/browse/info.do?cid=3017521"], "megaNavOrder": [["3021920"]], "exclusionIds": [], "customStyles": {}}, {"name": "Cashmere", "divisionId": ["/browse/category.do?cid=3011914"], "megaNavOrder": [["3011928"]], "exclusionIds": [], "customStyles": {}}, {"name": "Sale", "divisionId": ["/browse/division.do?cid=1014329"], "megaNavOrder": [["3019400", "3019402"], ["3019403", "3019412"], ["3019401"]], "exclusionIds": [], "customStyles": {}}]}}]}}, "promodrawer": {"name": "LayoutComponent", "type": "sitewide", "instanceName": "promoDrawer-102423", "CIID": "30017525", "experimentRunning": true, "meta": {"excludePageTypes": ["LoyaltyValueCenter"]}, "data": {"shouldWaitForOptimizely": false, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "PromoDrawerComponentV2", "type": "sitewide", "sitewide-promodrawer-ciid": "********", "instanceName": "promoDrawer-********", "experimentRunning": false, "data": {"buildInfo": ["********", "BR"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": true, "includePageTypes": ["product"]}, "autoFire": "disabled", "disabledAutoFirePageTypes": ["category"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two_cta-wh {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n font-family: BananaSerif;\n}\n.pd_two_cta-wh img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two_cta-wh .pd_two_cta-wh--cta-container {\n  bottom: 8%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\njustify-content:center;\n}\n.pd_two_cta-wh .pd_two_cta-wh_button {\n    border-bottom: 1px solid #fff;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 12px;\n  text-align: center;\n  text-transform: uppercase;\n \n  letter-spacing: .1em;\n  font-weight: 700;\n}\n.pd_two_cta-wh_button:hover{\n  cursor:pointer;\n  text-decoration:none;\n}\n.pd_two_cta-wh .pd_two_cta-wh_button:not(:first-child) {\n  margin-left: 12%;\n}\n</style>\n\n\n<div class=\"pd_two_cta-wh\">\n  <a href=\"/browse/division.do?cid=1014329&amp;mlink=5151,********,MOB_DR_BG_1&amp;clink=********\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BRWeb/content/0030/017/525/assets/BRSP230713_PRM_USCA_EvergreenRefresh_SITE_PD_40offSale.jpg\" alt=\"Up to 40% Off Sale Styles.  Shop now.\">\n  </a>\n  <div class=\"pd_two_cta-wh--cta-container\">\n    <a href=\"/browse/category.do?cid=26495&amp;mlink=5151,********,MOB_DR_W_1&amp;clink=********\" class=\"pd_two_cta-wh_button\">Shop Women</a>\n    <a href=\"/browse/category.do?cid=26219&amp;mlink=5151,********,MOB_DR_M_1&amp;clink=********\" class=\"pd_two_cta-wh_button\">Shop Men</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "no code needed", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "983937", "genericCode": ""}, "promoId": "l1p7rrtg"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"https://bananarepublic.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=BRSSPD&amp;mlink=5151,********,PD_CARD_ACQ&amp;clink=********\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" target=\"_blank\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BRWeb/content/0030/017/525/assets/BRSP230515_SITE_US_ILP_CardAcq_6M_Pass_PD.jpg\" alt=\"cardmember exclusive pass 20% off every purchase for 6 months when you open and use your Banana Republic Rewards credit card. Apply Now. \">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "969777", "genericCode": ""}, "promoId": "l3j922pc"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"https://bananarepublic.onelink.me/6L9o/9faefc80\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" target=\"_blank\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BRWeb/content/0030/017/525/assets/BRSP230713_PRM_USCA_EvergreenRefresh_APP_PD.jpg\" alt=\"Get the BR App.  Download today for exclusive offers and more.  Download on the App Store.  Get it on Google Play.\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "", "legalOverride": "", "genericCodeId": "", "genericCode": ""}, "promoId": "l25ccek0"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_one_cta-wh-lkczpdmx {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n font-family: BananaSerif;\n}\n.pd_one_cta-wh-lkczpdmx img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_one_cta-wh-lkczpdmx .pd_one_cta-wh--cta-container {\n  bottom: 8%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n  justify-content: center;\n}\n.pd_one_cta-wh-lkczpdmx .pd_one_cta-wh_button {\n  border-bottom: 1px solid #fff;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 12px;\n  text-align: center;\n  text-transform: uppercase;\n  letter-spacing: .1em;\n  font-weight: 700;\n}\n.pd_one_cta-wh_button:hover{\n  cursor:pointer;\n  text-decoration:none;\n}\n</style>\n\n\n<div class=\"pd_one_cta-wh-lkczpdmx\">\n  <a href=\"/profile/info.do?cid=53443&amp;mlink=48422,,MOB_DR_EMAIL_REG_BG\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BRWeb/content/0030/017/525/assets/BRSP230713_PRM_USCA_EvergreenRefresh_SITE_PD_OPT1.jpg\" alt=\"Join the list.  Sign up for emails and get 15% off your first purchase.  \">\n  </a>\n  <div class=\"pd_one_cta-wh--cta-container\">\n    <a href=\"/profile/info.do?cid=53443&amp;mlink=48422,,MOB_DR_EMAIL_REG_CTA\" class=\"pd_one_cta-wh_button\">sign up now</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile4"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "672633", "genericCode": ""}, "promoId": "l1p7rs2z"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "text": "{--! headerText !--}", "style": {"mobile": {"fontSize": ".8em"}}}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}", "style": {"mobile": {"fontSize": ".75em"}}}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "subHeaderText": "(4 available)", "iconAltText": "Open icon"}, "closedState": {"headerText": "up to 40% off sale", "subHeaderText": "tap for more offers", "iconAltText": "Closed icon"}, "aria-label": "up to 40% off sale"}, "pd_id": "pdid_1534541049574"}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "PromoDrawerComponentV2", "type": "sitewide", "sitewide-promodrawer-ciid": "********", "instanceName": "promoDrawer-********", "experimentRunning": false, "data": {"buildInfo": ["********", "BR"], "style": {"height": "293px"}, "options": {"desktopVisible": false, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "search", "info", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "profile", "LoyaltyValueCenter", "CustomerService"]}, "autoFire": "disabled", "disabledAutoFirePageTypes": ["category"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two_cta-wh {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n font-family: BananaSerif;\n}\n.pd_two_cta-wh img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two_cta-wh .pd_two_cta-wh--cta-container {\n  bottom: 8%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\njustify-content:center;\n}\n.pd_two_cta-wh .pd_two_cta-wh_button {\n    border-bottom: 1px solid #fff;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 12px;\n  text-align: center;\n  text-transform: uppercase;\n \n  letter-spacing: .1em;\n  font-weight: 700;\n}\n.pd_two_cta-wh_button:hover{\n  cursor:pointer;\n  text-decoration:none;\n}\n.pd_two_cta-wh .pd_two_cta-wh_button:not(:first-child) {\n  margin-left: 12%;\n}\n</style>\n\n\n<div class=\"pd_two_cta-wh\">\n  <a href=\"/browse/division.do?cid=1014329&amp;mlink=5151,********,MOB_DR_BG_1&amp;clink=********\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BRWeb/content/0030/017/525/assets/BRSP230713_PRM_USCA_EvergreenRefresh_SITE_PD_40offSale.jpg\" alt=\"Up to 40% Off Sale Styles.  Shop now.\">\n  </a>\n  <div class=\"pd_two_cta-wh--cta-container\">\n    <a href=\"/browse/category.do?cid=26495&amp;mlink=5151,********,MOB_DR_W_1&amp;clink=********\" class=\"pd_two_cta-wh_button\">Shop Women</a>\n    <a href=\"/browse/category.do?cid=26219&amp;mlink=5151,********,MOB_DR_M_1&amp;clink=********\" class=\"pd_two_cta-wh_button\">Shop Men</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "no code needed", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "983937", "genericCode": ""}, "promoId": "l1p7rrtg"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"https://bananarepublic.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=BRSSPD&amp;mlink=5151,********,PD_CARD_ACQ&amp;clink=********\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" target=\"_blank\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BRWeb/content/0030/017/525/assets/BRSP230515_SITE_US_ILP_CardAcq_6M_Pass_PD.jpg\" alt=\"cardmember exclusive pass 20% off every purchase for 6 months when you open and use your Banana Republic Rewards credit card. Apply Now. \">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "969777", "genericCode": ""}, "promoId": "l3j922pc"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"https://bananarepublic.onelink.me/6L9o/9faefc80\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" target=\"_blank\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BRWeb/content/0030/017/525/assets/BRSP230713_PRM_USCA_EvergreenRefresh_APP_PD.jpg\" alt=\"Get the BR App.  Download today for exclusive offers and more.  Download on the App Store.  Get it on Google Play.\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "", "legalOverride": "", "genericCodeId": "", "genericCode": ""}, "promoId": "l25ccek0"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_one_cta-wh-lkczpdmx {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n font-family: BananaSerif;\n}\n.pd_one_cta-wh-lkczpdmx img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_one_cta-wh-lkczpdmx .pd_one_cta-wh--cta-container {\n  bottom: 8%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n  justify-content: center;\n}\n.pd_one_cta-wh-lkczpdmx .pd_one_cta-wh_button {\n  border-bottom: 1px solid #fff;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 12px;\n  text-align: center;\n  text-transform: uppercase;\n  letter-spacing: .1em;\n  font-weight: 700;\n}\n.pd_one_cta-wh_button:hover{\n  cursor:pointer;\n  text-decoration:none;\n}\n</style>\n\n\n<div class=\"pd_one_cta-wh-lkczpdmx\">\n  <a href=\"/profile/info.do?cid=53443&amp;mlink=48422,,MOB_DR_EMAIL_REG_BG\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BRWeb/content/0030/017/525/assets/BRSP230713_PRM_USCA_EvergreenRefresh_SITE_PD_OPT1.jpg\" alt=\"Join the list.  Sign up for emails and get 15% off your first purchase.  \">\n  </a>\n  <div class=\"pd_one_cta-wh--cta-container\">\n    <a href=\"/profile/info.do?cid=53443&amp;mlink=48422,,MOB_DR_EMAIL_REG_CTA\" class=\"pd_one_cta-wh_button\">sign up now</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile4"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "672633", "genericCode": ""}, "promoId": "l1p7rs2z"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "text": "{--! headerText !--}", "style": {"mobile": {"fontSize": ".8em"}}}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}", "style": {"mobile": {"fontSize": ".75em"}}}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "subHeaderText": "(4 available)", "iconAltText": "Open icon"}, "closedState": {"headerText": "up to 40% off sale", "subHeaderText": "tap for more offers", "iconAltText": "Closed icon"}, "aria-label": "up to 40% off sale"}, "pd_id": "pdid_1534541049574"}}]}}}}, "search": {"type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["Sweaters", "Shirts", "Dresses", "Outerwear", "<PERSON>ts", "Gifts", "Baby", "Home"]}}, "logo": {"type": "sitewide", "name": "Logo", "darkLogoImgPath": "/Asset_Archive/BRWeb/content/0016/264/099/assets/BR_Logo_black.svg", "lightLogoImgPath": "/Asset_Archive/BRWeb/content/0016/264/099/assets/BR_Logo_white.svg", "logoImgPath": "/Asset_Archive/BRWeb/content/0016/264/099/assets/BR_Logo_black.svg", "altText": "Banana Republic logo"}, "header": {"byCid": [{"details": "Pages that want transparent nav to overlay content", "configurationForCids": ["3018119", "3011914", "1183416", "1016720", "28660", "75310", "99915", "69883", "5032", "3025049", "1194368", "3017900", "3016950", "1189711", "3023473", "1192597", "1140707", "3017521", "3021855", "3018259", "3016016", "1141785", "3015539", "1188156", "13846", "48422", "1192385", "1188148", "1184521", "1191449", "1188885", "1189473", "1192145", "1014740", "1080679", "1027224", "1014756", "1014757", "1014739", "26399", "3009783", "3010568", "3010447", "1196034", "1191481", "29818", "1167227", "3010820", "3011631", "1167228", "1131057", "1186806", "3012018", "1185898", "3013253", "3011144", "3011143", "3011145", "3011147", "3011150", "3011149", "3011148", "3011142", "3012751", "3013763", "1193554", "3014498", "3019481", "3017868", "3017854", "5122", "3018752", "3017944"], "stickyBackground": "transparent", "contrast": "light", "fullBleedOptions": {"isFullBleedEnabled": true, "hasTransparencyLayer": true, "flyoutBackground": "#F6F4EB", "fullBleedContrast": "light"}}, {"details": "Pages that originally had transparent nav overlay content, but now need to have cream background. These are the pages that need a shorter first asset but design cannot support.", "configurationForCids": ["61830", "1193373", "3018622"], "stickyBackground": "transparent", "contrast": "dark", "headerBackground": "#F6F4EB", "fullBleedOptions": {"isFullBleedEnabled": false, "hasTransparencyLayer": false, "flyoutBackground": "#F6F4EB", "fullBleedContrast": "dark"}}], "byPageType": [{"configurationForPageTypes": ["home"], "stickyBackground": "transparent", "contrast": "light", "fullBleedOptions": {"isFullBleedEnabled": true, "hasTransparencyLayer": true, "flyoutBackground": "#F6F4EB", "fullBleedContrast": "light"}}, {"configurationForPageTypes": ["category", "product", "Information", "customlandingpage", "search", "dynamicerror", "division"], "stickyBackground": "transparent", "headerBackground": "#F6F4EB", "flyoutBackground": "#F6F4EB", "contrast": "dark"}], "default": {"isUtilityLinksEnabled": false, "headerLayout": "onlyLogoAtTop", "stickyScrollDirection": "both", "isStickyEnabled": true}}}, "brand": "br", "type": "meta", "pmcsEdgeCacheTag": "br-homepage-en-us-prod"}