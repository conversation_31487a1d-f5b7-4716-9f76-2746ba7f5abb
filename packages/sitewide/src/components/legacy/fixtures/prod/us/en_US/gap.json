{"meta.description": "pageDescription defaulted", "meta.title.overide": "pageTitle defaulted", "home": {"type": "home", "name": "HomeMultiSimple", "components": [{"instanceDesc": "2023-08-29 WCD HP CSS Modifications - https://github.gapinc.com/wcd/shared-code-library/tree/main/hp-css/", "ciid": "bcd77708-2a79-496a-8f39-57456de24221", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>#main-content *{box-sizing:border-box}#main-content img,#main-content video{display:block}.fullBleedCertona div.productCard{max-width:unset}.fullBleedCertona button.slick-arrow.slick-disabled.sitewide-0,.fullBleedCertona button.slick-arrow.slick-next.sitewide-0,.fullBleedCertona button.slick-arrow.slick-prev.sitewide-0{margin-top:0}.fullBleedCertona .mkt-certona-recs{max-width:none}.fullBleedCertona .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon{max-width:none}.fullBleedCertona .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon .mkt-certona-recs__products{max-width:none}div.wcd_hp-cta{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between}.wcd_hp-cta,.wcd_hp-cta a,.wcd_hp-cta button,a .wcd_hp-cta,a.wcd_hp-cta,button.wcd_hp-cta{-ms-flex-align:center;align-items:center;text-transform:uppercase;border:none;font-weight:400;letter-spacing:0;line-height:normal;padding:0 0;color:#000;font-size:3vw;background-color:transparent;text-decoration:underline;text-underline-offset:15%}.wcd_hp-cta a button,.wcd_hp-cta button,.wcd_hp-cta button button,a .wcd_hp-cta button,a.wcd_hp-cta button,button.wcd_hp-cta button{padding:1vw}.wcd_hp-cta a>div,.wcd_hp-cta button>div,.wcd_hp-cta>div,a .wcd_hp-cta>div,a.wcd_hp-cta>div,button.wcd_hp-cta>div{position:relative;width:auto;left:-1vw}.wcd_hp-cta a ul,.wcd_hp-cta button ul,.wcd_hp-cta ul,a .wcd_hp-cta ul,a.wcd_hp-cta ul,button.wcd_hp-cta ul{position:absolute;border-style:solid;background-color:#fff;padding:2vw 2vw 2.5vw;min-width:-webkit-fill-available;left:-1vw;box-shadow:none}.wcd_hp-cta a ul li,.wcd_hp-cta button ul li,.wcd_hp-cta ul li,a .wcd_hp-cta ul li,a.wcd_hp-cta ul li,button.wcd_hp-cta ul li{line-height:4.5vw;border-width:0;padding:0}.wcd_hp-cta a ul li a,.wcd_hp-cta button ul li a,.wcd_hp-cta ul li a,a .wcd_hp-cta ul li a,a.wcd_hp-cta ul li a,button.wcd_hp-cta ul li a{display:inline}.wcd_hp-cta button span,button.wcd_hp-cta span{font-size:3.5vw;height:.25em;line-height:0;margin-left:1vw;padding:0;width:1vw}.wcd_hp-cta>div:first-child{z-index:10}.wcd_hp-cta>div:nth-child(2){z-index:9}.wcd_hp-cta>div:nth-child(3){z-index:8}.wcd_hp-cta>div:nth-child(4){z-index:7}.wcd_hp-cta>div:nth-child(5){z-index:6}.wcd_hp-cta>div:nth-child(6){z-index:5}.wcd_hp-cta>div:nth-child(7){z-index:4}.wcd_hp-cta>div:nth-child(8){z-index:3}.wcd_hp-cta>div:nth-child(9){z-index:2}.wcd_hp-cta>div:nth-child(10){z-index:1}.wcd_hp-cta.white-cta,.wcd_hp-cta.white-cta a,.wcd_hp-cta.white-cta button,a .wcd_hp-cta.white-cta,a.wcd_hp-cta.white-cta,button.wcd_hp-cta.white-cta{background-color:transparent;color:#fff}.wcd_hp-cta.white-cta ul a{color:#000}.wcd_hp-cta.arrow a,.wcd_hp-cta.arrow button,a .wcd_hp-cta.arrow,a.wcd_hp-cta.arrow,button.wcd_hp-cta.arrow{background-color:transparent;background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--black.svg);background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 3vw;border-color:transparent;display:-ms-inline-flexbox;display:inline-flex;padding-left:0;padding-right:calc(1em + 0px + 6px);text-align:left}.wcd_hp-cta.arrow a:hover,.wcd_hp-cta.arrow button:hover,a .wcd_hp-cta.arrow:hover,a.wcd_hp-cta.arrow:hover,button.wcd_hp-cta.arrow:hover{background-color:transparent;border-color:transparent;color:#000}.wcd_hp-cta.arrow a.white,.wcd_hp-cta.arrow button.white,a .wcd_hp-cta.arrow.white,a.wcd_hp-cta.arrow.white,button.wcd_hp-cta.arrow.white{background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--white.svg)}.wcd_hp-cta.caret-vcn a,.wcd_hp-cta.caret-vcn button,a .wcd_hp-cta.caret-vcn,a.wcd_hp-cta.caret-vcn,button.wcd_hp-cta.caret-vcn{height:17px}.wcd_hp-cta.caret-vcn a:hover,.wcd_hp-cta.caret-vcn button:hover,a .wcd_hp-cta.caret-vcn:hover,a.wcd_hp-cta.caret-vcn:hover,button.wcd_hp-cta.caret-vcn:hover{background-color:transparent;border-color:transparent;color:#000}.wcd_hp-cta.caret-vcn a.white,.wcd_hp-cta.caret-vcn button.white,a .wcd_hp-cta.caret-vcn.white,a.wcd_hp-cta.caret-vcn.white,button.wcd_hp-cta.caret-vcn.white{background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret_white.svg)}.wcd_hp-cta.caret a,.wcd_hp-cta.caret button,a .wcd_hp-cta.caret,a.wcd_hp-cta.caret,button.wcd_hp-cta.caret{background-position:calc(100% - 12px) 50%;background-repeat:no-repeat;background-size:auto 3vw;color:#000;display:-ms-inline-flexbox;display:inline-flex;padding-left:0;padding-right:calc(1em + 0px + 6px);text-align:left;transition:background-position .25s ease-out;padding-right:24px;-ms-flex-pack:start;justify-content:flex-start}.wcd_hp-cta.caret a:hover,.wcd_hp-cta.caret button:hover,a .wcd_hp-cta.caret:hover,a.wcd_hp-cta.caret:hover,button.wcd_hp-cta.caret:hover{background-color:transparent;background-image:url(/Asset_Archive/GPWeb/content/0029/669/822/assets/UNREC_1/MOBCTA_Caret_white.svg);color:#000}.wcd_hp-cta.caret a.white,.wcd_hp-cta.caret button.white,a .wcd_hp-cta.caret.white,a.wcd_hp-cta.caret.white,button.wcd_hp-cta.caret.white{background-image:url(/Asset_Archive/GPWeb/content/0029/568/895/assets/arrow-right--white.svg)}.wcd_hp-cta.details a,.wcd_hp-cta.details button,a .wcd_hp-cta.details,a.wcd_hp-cta.details,button.wcd_hp-cta.details{background-color:transparent;border-width:0;font-size:10px;min-height:16px;min-width:32px;padding:0;text-decoration:underline}.wcd_hp-cta.details a:hover,.wcd_hp-cta.details button:hover,a .wcd_hp-cta.details:hover,a.wcd_hp-cta.details:hover,button.wcd_hp-cta.details:hover{color:#fff}.wcd_hp-cta.details a.dark,.wcd_hp-cta.details button.dark,a .wcd_hp-cta.details.dark,a.wcd_hp-cta.details.dark,button.wcd_hp-cta.details.dark{color:#2b2b2b}.wcd_hp-cta.details a.dark:hover,.wcd_hp-cta.details button.dark:hover,a .wcd_hp-cta.details.dark:hover,a.wcd_hp-cta.details.dark:hover,button.wcd_hp-cta.details.dark:hover{color:#000}.wcd_hp-cta.full-width,.wcd_hp-cta.full-width a,.wcd_hp-cta.full-width a>div,.wcd_hp-cta.full-width button,.wcd_hp-cta.full-width button>div,.wcd_hp-cta.full-width-at-mob,.wcd_hp-cta.full-width-at-mob a,.wcd_hp-cta.full-width-at-mob a>div,.wcd_hp-cta.full-width-at-mob button,.wcd_hp-cta.full-width-at-mob button>div,.wcd_hp-cta.full-width-at-mob>div,.wcd_hp-cta.full-width>div,a .wcd_hp-cta.full-width,a .wcd_hp-cta.full-width-at-mob,a .wcd_hp-cta.full-width-at-mob>div,a .wcd_hp-cta.full-width>div{width:100%}.wcd_hp-visnav{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between}.wcd_hp-visnav .wcd_hp-cta>div{left:-1vw}.wcd_hp-visnav .wcd_hp-cta>div ul{left:-1vw}.wcd_hp-visnav .wcd_hp-cta>a{position:relative;padding:1vw;left:-1vw}.wcd_hp-visnav button{padding:1vw}.wcd_hp-visnav>div{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;position:relative;width:50%}.wcd_hp-visnav>div>a{height:auto}.wcd_hp-visnav>div:first-child .wcd_hp-cta{z-index:32}.wcd_hp-visnav>div:nth-child(2) .wcd_hp-cta{z-index:31}.wcd_hp-visnav>div:nth-child(3) .wcd_hp-cta{z-index:30}.wcd_hp-visnav>div:nth-child(4) .wcd_hp-cta{z-index:29}.wcd_hp-visnav>div:nth-child(5) .wcd_hp-cta{z-index:28}.wcd_hp-visnav>div:nth-child(6) .wcd_hp-cta{z-index:27}.wcd_hp-visnav>div:nth-child(7) .wcd_hp-cta{z-index:26}.wcd_hp-visnav>div:nth-child(8) .wcd_hp-cta{z-index:25}.wcd_hp-visnav>div:nth-child(9) .wcd_hp-cta{z-index:24}.wcd_hp-visnav>div:nth-child(10) .wcd_hp-cta{z-index:23}.wcd_hp-visnav>div:nth-child(11) .wcd_hp-cta{z-index:22}.wcd_hp-visnav>div:nth-child(12) .wcd_hp-cta{z-index:21}.wcd_hp-visnav>div:nth-child(13) .wcd_hp-cta{z-index:20}.wcd_hp-visnav>div:nth-child(14) .wcd_hp-cta{z-index:19}.wcd_hp-visnav>div:nth-child(15) .wcd_hp-cta{z-index:18}.wcd_hp-visnav>div:nth-child(16) .wcd_hp-cta{z-index:17}@media only screen and (min-width:768px){.wcd_hp-cta a button,.wcd_hp-cta button,.wcd_hp-cta button button,a .wcd_hp-cta button,a.wcd_hp-cta button,button.wcd_hp-cta button{padding:unset}.wcd_hp-cta a>div,.wcd_hp-cta button>div,.wcd_hp-cta>div,a .wcd_hp-cta>div,a.wcd_hp-cta>div,button.wcd_hp-cta>div{left:unset}.wcd_hp-cta a ul,.wcd_hp-cta button ul,.wcd_hp-cta ul,a .wcd_hp-cta ul,a.wcd_hp-cta ul,button.wcd_hp-cta ul{padding:1vw 1vw 1.25vw;margin-top:.2vw}.wcd_hp-cta a ul li,.wcd_hp-cta button ul li,.wcd_hp-cta ul li,a .wcd_hp-cta ul li,a.wcd_hp-cta ul li,button.wcd_hp-cta ul li{line-height:1.5vw}.wcd_hp-cta,.wcd_hp-cta a,.wcd_hp-cta button,a .wcd_hp-cta,a.wcd_hp-cta,button.wcd_hp-cta{font-size:1vw}.wcd_hp-cta a:active,.wcd_hp-cta a:focus,.wcd_hp-cta a:hover,.wcd_hp-cta button:active,.wcd_hp-cta button:focus,.wcd_hp-cta button:hover,.wcd_hp-cta:active,.wcd_hp-cta:focus,.wcd_hp-cta:hover,a .wcd_hp-cta:active,a .wcd_hp-cta:focus,a .wcd_hp-cta:hover,a.wcd_hp-cta:active,a.wcd_hp-cta:focus,a.wcd_hp-cta:hover,button.wcd_hp-cta:active,button.wcd_hp-cta:focus,button.wcd_hp-cta:hover{color:#367948}.wcd_hp-cta button span,button.wcd_hp-cta span{font-size:1.25vw;margin-left:.25vw;width:-moz-fit-content;width:fit-content}.wcd_hp-cta.exposed-at-desk button{background-color:transparent;border-width:0;color:inherit;margin-bottom:.75em;padding:0;text-align:left}.wcd_hp-cta.exposed-at-desk button span{display:none}.wcd_hp-cta.exposed-at-desk ul{background-color:transparent;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:start;justify-content:flex-start;max-height:none;position:relative;visibility:visible;z-index:0}.wcd_hp-cta.exposed-at-desk ul li{width:auto}.wcd_hp-cta.exposed-at-desk ul li:not(:last-child){margin-bottom:8px;margin-right:8px}.wcd_hp-cta.arrow a,.wcd_hp-cta.arrow button,a .wcd_hp-cta.arrow,a.wcd_hp-cta.arrow,button.wcd_hp-cta.arrow{background-size:auto .9em}.wcd_hp-cta.caret-vcn a,.wcd_hp-cta.caret-vcn button,a .wcd_hp-cta.caret-vcn,a.wcd_hp-cta.caret-vcn,button.wcd_hp-cta.caret-vcn{margin-left:0;-ms-flex-pack:left;justify-content:left;text-align:left;padding-left:0;height:25px;margin:0}.wcd_hp-cta.caret a,.wcd_hp-cta.caret button,a .wcd_hp-cta.caret,a.wcd_hp-cta.caret,button.wcd_hp-cta.caret{background-size:auto .9em}.wcd_hp-cta.full-width-at-mob,.wcd_hp-cta.full-width-at-mob a,.wcd_hp-cta.full-width-at-mob a>div,.wcd_hp-cta.full-width-at-mob button,.wcd_hp-cta.full-width-at-mob button>div,.wcd_hp-cta.full-width-at-mob>div,a .wcd_hp-cta.full-width-at-mob,a .wcd_hp-cta.full-width-at-mob>div{width:auto}.wcd_hp-cta.full-width-at-desk,.wcd_hp-cta.full-width-at-desk a,.wcd_hp-cta.full-width-at-desk a>div,.wcd_hp-cta.full-width-at-desk button,.wcd_hp-cta.full-width-at-desk button>div,.wcd_hp-cta.full-width-at-desk>div,a .wcd_hp-cta.full-width-at-desk,a .wcd_hp-cta.full-width-at-desk>div{width:100%}.wcd_hp-visnav .wcd_hp-cta>div{left:unset}.wcd_hp-visnav .wcd_hp-cta>div ul{left:-1vw}.wcd_hp-visnav .wcd_hp-cta>a{padding:0;left:unset}.wcd_hp-visnav button{padding:unset}.wcd_hp-visnav>div{width:25%}}@media only screen and (max-width:767px){.wcd_hp-cta.two-column-at-mob{position:absolute;left:0;bottom:20%;display:flow;padding-left:15px}.wcd_hp-cta.two-column-at-mob li a{margin-bottom:0;width:100%}.wcd_hp-cta.two-column-at-mob>div button{color:#fff;width:100%}.wcd_hp-cta.two-column-at-mob.odd-number a:first-child,.wcd_hp-cta.two-column-at-mob.odd-number>div:first-child{width:100%}}</style>"}}, {"instanceName": "dpg-banner1", "instanceDesc": "DPG Placeholder1", "experimentRunning": true, "name": "OptimizelyPlaceholder", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}}}, {"instanceName": "HP Promo 1", "instanceDesc": "11-16-23 US HP Promo 1", "ciid": "", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": false, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "calc(98vw*(180/640))", "large": "calc(98vw*(131/1440))"}, "style": {"position": "relative", "maxWidth": "767px", "margin": "0 auto 0"}, "desktopStyle": {"position": "relative", "maxWidth": "2560px", "margin": "0 auto 0"}, "components": [{"instanceDesc": "HP Banner", "name": "div", "type": "builtin", "data": {"style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Pre-Black Friday Sale 40% Off Your Purchase Applied at checkout 50% Off Jeans Select styles.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/20231116_FriendsgivingSale_USEC_HPMain_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/20231116_FriendsgivingSale_USEC_HPMain_DESK"}, "linkData": {"title": "Pre-Black Friday Sale 40% Off Your Purchase Applied at checkout 50% Off Jeans Select styles.", "to": "/browse/category.do?cid=1127938#pageId=0&department=136&mlink=5058,HP_Promo_Main"}}, "overlay": {"alt": "", "srcUrl": "", "desktopSrcUrl": ""}, "ctaList": {"mobilePositionAboveContent": false, "style": {"height": "auto", "width": "auto"}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998939,998957", "height": "500px"}}, "composableButtonData": {"children": "details", "style": {"position": "absolute", "background": "unset", "textDecoration": "underline", "padding": "0", "color": "#fff", "fontSize": "1.9vw", "bottom": "4.5vw", "right": "2.5vw"}, "desktopStyle": {"fontSize": "0.75vw", "bottom": "2.5vw", "right": "1.25vw"}}}]}}}]}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": "true", "data": {"style": {}, "components": [{"instanceDesc": "CTA Mob", "name": "div", "type": "builtin", "data": {"style": {"display": "flex"}, "components": [{"instanceDesc": "CTA Block Headline", "name": "div", "type": "builtin", "data": {"style": {}, "components": [""]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta white-cta", "style": {"position": "absolute", "bottom": "4vw", "left": "4vw", ">div:nth-child(1)": {"marginRight": "20vw"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Get This Deal"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1127938#pageId=0&department=136&mlink=5058,HP_Promo_W"}, {"text": "Maternity", "href": "/browse/category.do?cid=1127956#pageId=0&department=136&mlink=5058,HP_Promo_Mat"}, {"text": "Men", "href": "/browse/category.do?cid=1127944#pageId=0&department=75&mlink=5058,HP_Promo_M"}, {"text": "Girls", "href": "/browse/category.do?cid=1127946#pageId=0&department=48&mlink=5058,HP_Promo_G"}, {"text": "Boys", "href": "/browse/category.do?cid=1127945#pageId=0&department=16&mlink=5058,HP_Promo_B"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=1127952#pageId=0&department=165&mlink=5058,HP_Promo_TG"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1127955#pageId=0&department=166&mlink=5058,HP_Promo_TB"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=1127947#pageId=0&department=165&mlink=5058,HP_Promo_BG"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=1127948#pageId=0&department=166&mlink=5058,HP_Promo_BB"}, {}]}}, {"buttonDropdownData": {"heading": {"text": "Shop Jeans"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=5664#pageId=0&department=136&mlink=5058,HP_Promo_W"}, {"text": "Maternity", "href": "/browse/category.do?cid=6013#pageId=0&department=136&mlink=5058,HP_Promo_Mat"}, {"text": "Men", "href": "/browse/category.do?cid=6998#pageId=0&department=75&mlink=5058,HP_Promo_M"}, {"text": "Girls", "href": "/browse/category.do?cid=6276#pageId=0&department=48&mlink=5058,HP_Promo_G"}, {"text": "Boys", "href": "/browse/category.do?cid=6189#pageId=0&department=16&mlink=5058,HP_Promo_B"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=6427#pageId=0&department=165&mlink=5058,HP_Promo_TG"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=6359#pageId=0&department=166&mlink=5058,HP_Promo_TB"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=7191#pageId=0&department=165&mlink=5058,HP_Promo_BG"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=95684#pageId=0&department=166&mlink=5058,HP_Promo_BB"}]}}]}}}]}}]}}, "desktop": {"shouldDisplay": "true", "data": {"style": {}, "components": [{"instanceDesc": "CTA Desk", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "padding": "0"}, "components": [{"instanceDesc": "CTA Block Headline", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "fontSize": "1vw", "marginRight": "0"}, "components": [""]}}, {"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta white-cta", "style": {"position": "absolute", "bottom": "3vw", "left": "2.5vw", "> div, > a": {"marginRight": "2vw"}, ">div:nth-child(5)": {"zIndex": "7 !important"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Women"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1127938#pageId=0&department=136&mlink=5058,HP_Promo_W"}, {"text": "Maternity", "href": "/browse/category.do?cid=1127956#pageId=0&department=136&mlink=5058,HP_Promo_Mat"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}, {"composableButtonData": {"children": "Men"}, "linkData": {"to": "/browse/category.do?cid=1127944#pageId=0&department=75&mlink=5058,HP_Promo_M"}}, {"composableButtonData": {"children": "Girls"}, "linkData": {"to": "/browse/category.do?cid=1127946#pageId=0&department=48&mlink=5058,HP_Promo_G"}}, {"composableButtonData": {"children": "Boys"}, "linkData": {"to": "/browse/category.do?cid=1127945#pageId=0&department=16&mlink=5058,HP_Promo_B"}}, {"composableButtonData": {"children": "<PERSON>ler Girl"}, "linkData": {"to": "/browse/category.do?cid=1127952#pageId=0&department=165&mlink=5058,HP_Promo_TG"}}, {"composableButtonData": {"children": "<PERSON><PERSON>"}, "linkData": {"to": "/browse/category.do?cid=1127955#pageId=0&department=166&mlink=5058,HP_Promo_TB"}}, {"composableButtonData": {"children": "Baby Girl"}, "linkData": {"to": "/browse/category.do?cid=1127947#pageId=0&department=165&mlink=5058,HP_Promo_BG"}}, {"composableButtonData": {"children": "Baby Boy"}, "linkData": {"to": "/browse/category.do?cid=1127948#pageId=0&department=166&mlink=5058,HP_Promo_BB"}}, {"buttonDropdownData": {"heading": {"text": "SHOP JEANS"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=5664#pageId=0&department=136&mlink=5058,HP_Promo_W"}, {"text": "Maternity", "href": "/browse/category.do?cid=6013#pageId=0&department=136&mlink=5058,HP_Promo_Mat"}, {"text": "Men", "href": "/browse/category.do?cid=6998#pageId=0&department=75&mlink=5058,HP_Promo_M"}, {"text": "Girls", "href": "/browse/category.do?cid=6276#pageId=0&department=48&mlink=5058,HP_Promo_G"}, {"text": "Boys", "href": "/browse/category.do?cid=6189#pageId=0&department=16&mlink=5058,HP_Promo_B"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=6427#pageId=0&department=165&mlink=5058,HP_Promo_TG"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=6359#pageId=0&department=166&mlink=5058,HP_Promo_TB"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=7191#pageId=0&department=165&mlink=5058,HP_Promo_BG"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=95684&#pageId=0&department=166&mlink=5058,HP_Promo_BB"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}]}}}}]}}, {"instanceName": "optly-placeholder-1", "instanceDesc": "11-07-23 US - Gifted: Cashsoft", "ciid": "", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": false, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "calc(98vw*(870/640))", "large": "calc(98vw*(754/1440))"}, "style": {"position": "relative", "maxWidth": "767px", "margin": "0px auto 15vw"}, "desktopStyle": {"position": "relative", "maxWidth": "2560px", "margin": "0px auto 7vw"}, "components": [{"instanceDesc": "Banner Media - Mobile Carousel 5 / Desktop Carousel 3", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"minHeight": "calc(98vw*(873/640))"}, "components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": true, "slidesToShow": 1, "speed": 1500, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": false, "dots": true}, "buttonSetting": {"pauseAltText": "Pause Slideshow", "playAltText": "Play Slideshow", "buttonStyle": {}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_play--white.svg", "pauseBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_pause--white.svg"}}, "style": {".slick-slider .slick-dots": {"width": "fit-content", "height": "fit-content", "bottom": "5vw", "left": "unset", "right": "5vw", "padding-bottom": "1vw", "lineHeight": "0"}, ".slick-slider .slick-dots li": {"width": "0", "height": "0", "margin": "0 max(0.8vw, 4px) !important", "padding": "max(0.8vw, 4px)"}, ".slick-slider .slick-dots li button": {"borderRadius": "100%", "border": "1px solid #fff", "padding": "max(0.625vw, 3px)"}, ".slick-slider .slick-dots li.slick-active button:before": {"opacity": "1"}, ".slick-slider .slick-dots li button::before": {"opacity": "0", "backgroundColor": "#fff !important", "width": "max(1.6vw, 6px) !important", "height": "max(1.6vw, 6px) !important"}, "@media only screen and (min-width:768px)": {".slick-slider .slick-dots": {"bottom": "2.5vw", "right": "2.5vw", "padding-bottom": "unset"}, ".slick-slider .slick-dots li": {"margin": "0 max(0.25vw, 3px) !important", "padding": "max(0.25vw, 3px)"}, ".slick-slider .slick-dots li button": {"padding": "max(0.21vw, 2px)"}, ".slick-slider .slick-dots li button::before": {"width": "max(0.5vw, 6px) !important", "height": "max(0.5vw, 6px) !important"}}}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_img1_MOB"}, "linkData": {"title": "Our Softest Sweaters. Ever.", "to": "/browse/category.do?cid=3022957#pageId=0&mlink=5058,HP_HERO1_ALL_HOL235875_IMAGE"}}, "overlay": {"alt": "Our Softest Sweaters. Ever.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_copy_MOB"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_img2_MOB"}, "linkData": {"title": "Our Softest Sweaters. Ever.", "to": "/browse/category.do?cid=3022957#pageId=0&mlink=5058,HP_HERO1_ALL_HOL235875_IMAGE"}}, "overlay": {"alt": "Our Softest Sweaters. Ever.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_copy_MOB"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_img3_MOB"}, "linkData": {"title": "Our Softest Sweaters. Ever.", "to": "/browse/category.do?cid=3022957#pageId=0&mlink=5058,HP_HERO1_ALL_HOL235875_IMAGE"}}, "overlay": {"alt": "Our Softest Sweaters. Ever.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_copy_MOB"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_img4_MOB"}, "linkData": {"title": "Our Softest Sweaters. Ever.", "to": "/browse/category.do?cid=3022957#pageId=0&mlink=5058,HP_HERO1_ALL_HOL235875_IMAGE"}}, "overlay": {"alt": "Our Softest Sweaters. Ever.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_copy_MOB"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_img5_MOB"}, "linkData": {"title": "Our Softest Sweaters. Ever.", "to": "/browse/category.do?cid=3022957#pageId=0&mlink=5058,HP_HERO1_ALL_HOL235875_IMAGE"}}, "overlay": {"alt": "Our Softest Sweaters. Ever.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_copy_MOB"}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"minHeight": "calc(98vw*(715/1440))"}, "components": [{"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": true, "slidesToShow": 1, "speed": 1500, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": false, "dots": true}, "buttonSetting": {"pauseAltText": "Pause Slideshow", "playAltText": "Play Slideshow", "buttonStyle": {}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_play--white.svg", "pauseBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_pause--white.svg"}}, "style": {".slick-slider .slick-dots": {"width": "fit-content", "height": "fit-content", "bottom": "5vw", "left": "unset", "right": "5vw", "padding-bottom": "1vw", "lineHeight": "0"}, ".slick-slider .slick-dots li": {"width": "0", "height": "0", "margin": "0 max(0.8vw, 4px) !important", "padding": "max(0.8vw, 4px)"}, ".slick-slider .slick-dots li button": {"borderRadius": "100%", "border": "1px solid #fff", "padding": "max(0.625vw, 3px)"}, ".slick-slider .slick-dots li.slick-active button:before": {"opacity": "1"}, ".slick-slider .slick-dots li button::before": {"opacity": "0", "backgroundColor": "#fff !important", "width": "max(1.6vw, 6px) !important", "height": "max(1.6vw, 6px) !important"}, "@media only screen and (min-width:768px)": {".slick-slider .slick-dots": {"bottom": "2.5vw", "right": "2.5vw", "padding-bottom": "unset"}, ".slick-slider .slick-dots li": {"margin": "0 max(0.25vw, 3px) !important", "padding": "max(0.25vw, 3px)"}, ".slick-slider .slick-dots li button": {"padding": "max(0.21vw, 2px)"}, ".slick-slider .slick-dots li button::before": {"width": "max(0.5vw, 6px) !important", "height": "max(0.5vw, 6px) !important"}}}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_img1_DESK"}, "linkData": {"title": "Our Softest Sweaters. Ever.", "to": "/browse/category.do?cid=3022957#pageId=0&mlink=5058,HP_HERO1_ALL_HOL235875_IMAGE"}}, "overlay": {"alt": "Our Softest Sweaters. Ever.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_copy_DESK"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_img2_DESK"}, "linkData": {"title": "Our Softest Sweaters. Ever.", "to": "/browse/category.do?cid=3022957#pageId=0&mlink=5058,HP_HERO1_ALL_HOL235875_IMAGE"}}, "overlay": {"alt": "Our Softest Sweaters. Ever.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_copy_DESK"}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_img3_DESK"}, "linkData": {"title": "Our Softest Sweaters. Ever.", "to": "/browse/category.do?cid=3022957#pageId=0&mlink=5058,HP_HERO1_ALL_HOL235875_IMAGE"}}, "overlay": {"alt": "Our Softest Sweaters. Ever.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235875_copy_DESK"}}}]}}]}}}}, {"instanceDesc": "CTA mob+desk", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0 0 0"}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"position": "absolute", "fontSize": "3vw", "bottom": "50vw", "left": "5vw", "color": "#fff"}, "components": [{"type": "builtin", "name": "div", "data": {"style": {"whiteSpace": "pre"}, "components": ["CashSoft. Our bestselling, signature knit that’s cashmere soft. \nNo dry-cleaning. Always luxe. Forever easy."]}}]}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"padding": "0 0 0"}, "components": [{"instanceDesc": "CTAs 1", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta white-cta", "style": {"position": "absolute", "bottom": "calc(5vw + 2px)", "left": "5vw", "flexDirection": "column", "alignItems": "baseline !important", ">:first-child": {"marginBottom": "1vw"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop New Arrivals"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,HP_HERO1_W_HOL235875_CTA"}, {"text": "Men", "href": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,HP_HERO1_M_HOL235875_CTA"}, {"text": "Maternity", "href": "/browse/category.do?cid=11437#pageId=0&department=136&mlink=5058,HP_HERO1_MAT_HOL235875_CTA"}, {"text": "Girls", "href": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,HP_HERO1_G_HOL235875_CTA"}, {"text": "Boys", "href": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,HP_HERO1_B_HOL235875_CTA"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,HP_HERO1_TG_HOL235875_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,HP_HERO1_TB_HOL235875_CTA"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,HP_HERO1_BG_HOL235875_CTA"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,HP_HERO1_BB_HOL235875_CTA"}]}}, {"buttonDropdownData": {"heading": {"text": "Shop Cashsoft For All"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=3022755#pageId=0&department=136&mlink=5058,HP_HERO1_W_HOL235875_CTA"}, {"text": "Men", "href": "/browse/category.do?cid=1165627#pageId=0&mlink=5058,HP_HERO1_M_HOL235875_CTA"}, {"text": "Girls", "href": "/browse/category.do?cid=1135179#pageId=0&department=48&mlink=5058,HP_HERO1_G_HOL235875_CTA"}, {"text": "Boys", "href": "/browse/category.do?cid=1133131#pageId=0&department=16&mlink=5058,HP_HERO1_B_HOL235875_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1135069#pageId=0&mlink=5058,HP_HERO1_Toddler_HOL235875_CTA"}, {"text": "Baby", "href": "/browse/category.do?cid=1138346#pageId=0&mlink=5058,HP_HERO1_Baby_HOL235875_CTA"}]}}]}}}]}}]}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "baseline", "padding": "1.5vw 2.5vw 0 2.5vw"}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"display": "flow", "fontSize": "1vw"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"whiteSpace": "pre"}, "components": ["CashSoft. Our bestselling, signature knit that’s cashmere soft. No dry-cleaning. Always luxe. Forever easy."]}}]}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexWrap": "wrap"}, "components": [{"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "style": {">:first-child": {"marginRight": "1vw"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop New Arrivals"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,HP_HERO1_W_HOL235875_CTA"}, {"text": "Men", "href": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,HP_HERO1_M_HOL235875_CTA"}, {"text": "Maternity", "href": "/browse/category.do?cid=11437#pageId=0&department=136&mlink=5058,HP_HERO1_MAT_HOL235875_CTA"}, {"text": "Girls", "href": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,HP_HERO1_G_HOL235875_CTA"}, {"text": "Boys", "href": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,HP_HERO1_B_HOL235875_CTA"}, {"text": "<PERSON>ler Girl", "href": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,HP_HERO1_TG_HOL235875_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,HP_HERO1_TB_HOL235875_CTA"}, {"text": "Baby Girl", "href": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,HP_HERO1_BG_HOL235875_CTA"}, {"text": "Baby Boy", "href": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,HP_HERO1_BB_HOL235875_CTA"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}, {"buttonDropdownData": {"heading": {"text": "Shop Cashsoft For All"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=3022755#pageId=0&department=136&mlink=5058,HP_HERO1_W_HOL235875_CTA"}, {"text": "Men", "href": "/browse/category.do?cid=1165627#pageId=0&mlink=5058,HP_HERO1_M_HOL235875_CTA"}, {"text": "Girls", "href": "/browse/category.do?cid=1135179#pageId=0&department=48&mlink=5058,HP_HERO1_G_HOL235875_CTA"}, {"text": "Boys", "href": "/browse/category.do?cid=1133131#pageId=0&department=16&mlink=5058,HP_HERO1_B_HOL235875_CTA"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1135069#pageId=0&mlink=5058,HP_HERO1_Toddler_HOL235875_CTA"}, {"text": "Baby", "href": "/browse/category.do?cid=1138346#pageId=0&mlink=5058,HP_HERO1_Baby_HOL235875_CTA"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}]}}]}}]}}}}]}}, {"instanceName": "Certona_1", "instanceDesc": "10-17-23 Certona_1 US", "ciid": "416468b1-0e6e-4a8a-a663-dbec5bea314b", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": false, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "calc(98vw*(436/640))", "large": "calc(98vw*(503/1440))"}, "style": {"position": "relative", "maxWidth": "767px", "padding": "0 0 15vw 3vw"}, "desktopStyle": {"position": "relative", "maxWidth": "2560px", "padding": "0 8vw 7vw"}, "components": [{"type": "builtin", "name": "div", "data": {"style": {"div[data-testid='recommended-product-card'] > div": {"padding": "0"}, "p": {"marginTop": "2.5vw", "@media only screen and (min-width:768px)": {"fontSize": "1vw", "marginTop": "1vw"}}, "#mui-certona-recs-container": {"maxWidth": "unset"}}, "components": [{"name": "Recommendations", "type": "home", "data": {"customBrand": "GAP", "source": "c<PERSON>a", "scheme": "gaphome2_rr", "displayTitle": true, "fullWidth": false, "layout": "carousel", "centerMode": false, "useMobileConfig": true, "defaultslidesToShowSlick": 5, "defaultslidesToScrollSlick": 5, "displayPlayPauseButton": false, "resslidesToShowSlick": 5, "resslidesToScrollSlick": 5, "arrows": true, "autoplay": false, "pauseOnHover": true, "infinite": false, "priceFlag": false, "prevArrowSlick": "/Asset_Archive/GPWeb/content/0030/016/106/assets/certona/CertonaArrow_Black_Left.svg", "prevArrowAlt": "Previous", "nextArrowSlick": "/Asset_Archive/GPWeb/content/0030/016/106/assets/certona/CertonaArrow_Black_Left.svg", "nextArrowAlt": "Next", "arrowMaxWidth": "40px", "arrowVerticalPosition": "-50px", "arrowPosition": "30px", "certonaTitle": {"title": "More New Arrivals to Give and Get", "style": {"mobile": {"fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "padding": "0px 0 0 2vw", "lineHeight": "1.6rem", "WebkitFontSmoothing": "antialiased", "marginBottom": "0.5rem", "textTransform": "normal", "fontSize": "3.9vw"}, "desktop": {"fontFamily": "Gap Sans, Helvetica, Arial, Roboto, sans-serif", "padding": "0 0 0 0.5vw", "lineHeight": "1.6rem", "WebkitFontSmoothing": "antialiased", "marginBottom": "0.5rem", "textTransform": "normal", "fontSize": "1.3vw"}}}, "responsive": [{"breakpoint": 768, "settings": {"slidesToShow": 2.4, "slidesToScroll": 1}}], "productsPerRow": {"desktop": 5}, "gridLayout": {"style": {"desktop": {"display": "block", "height": "500px", "overflow-y": "scroll"}}}, "productTextStyles": {"productTitle": {"style": {"color": "#000", "textAlign": "left", "fontSize": "2.5vw", "margin": "0"}}, "productPrice": {"style": {"display": "none"}}, "productSalePrice": {"style": {"display": "none"}}}, "productMarketingFlag": {"style": {"fontWeight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"margin": "0% auto 0% auto", "maxWidth": "unset", "padding": "2vw", "@media only screen and (min-width:768px)": {"padding": "0.5vw"}}}, "productCardImageStyles": {"margin": "0", "maxWidth": "unset", "padding": "0", "width": "19vw"}}}]}}]}}, {"instanceName": "inno-banner-HP", "instanceDesc": "Innotech Content Banner", "experimentRunning": true, "name": "OptimizelyPlaceholder", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}}}, {"instanceName": "optly-placeholder-2", "instanceDesc": "11-07-23 US - Dressed Up Outfitting:The Lookbook", "ciid": "", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": false, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "calc(98vw*(944/640))", "large": "calc(98vw*(752/1440))"}, "style": {"position": "relative", "maxWidth": "767px", "margin": "0px auto 15vw"}, "desktopStyle": {"position": "relative", "maxWidth": "2560px", "margin": "0px 2.5vw 7vw"}, "components": [{"instanceDesc": "Banner Media - Mobile Carousel 2 / Desktop Static", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"style": {"minHeight": "calc(98vw*(873/640))", "flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "The Party Shop", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235467_copy_MOB"}, "linkData": {"title": "The Party Shop", "to": "/browse/category.do?cid=1119499#pageId=0&department=136&mlink=5058,HP_HERO2_W"}}, "overlay": {"alt": "", "srcUrl": "", "desktopSrcUrl": ""}}}, {"name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%"}, "mobile": {"width": "100%"}}, "data": {"carouselOptions": {"autoplay": true, "autoplaySpeed": 2000, "fade": true, "slidesToShow": 1, "speed": 1500, "displayArrows": {"mobile": false, "desktop": false}, "displayPlayPauseBtn": false, "dots": true}, "buttonSetting": {"pauseAltText": "Pause Slideshow", "playAltText": "Play Slideshow", "buttonStyle": {}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_play--white.svg", "pauseBtnSrc": "/Asset_Archive/GPWeb/content/0029/568/895/assets/icon_pause--white.svg"}}, "style": {".slick-slider .slick-dots": {"width": "fit-content", "height": "fit-content", "bottom": "5vw", "left": "unset", "right": "5vw", "padding-bottom": "1vw", "lineHeight": "0"}, ".slick-slider .slick-dots li": {"width": "0", "height": "0", "margin": "0 max(0.8vw, 4px) !important", "padding": "max(0.8vw, 4px)"}, ".slick-slider .slick-dots li button": {"borderRadius": "100%", "border": "1px solid #fff", "padding": "max(0.625vw, 3px)"}, ".slick-slider .slick-dots li.slick-active button:before": {"opacity": "1"}, ".slick-slider .slick-dots li button::before": {"opacity": "0", "backgroundColor": "#fff !important", "width": "max(1.6vw, 6px) !important", "height": "max(1.6vw, 6px) !important"}, "@media only screen and (min-width:768px)": {".slick-slider .slick-dots": {"bottom": "2.5vw", "right": "2.5vw", "padding-bottom": "unset"}, ".slick-slider .slick-dots li": {"margin": "0 max(0.25vw, 3px) !important", "padding": "max(0.25vw, 3px)"}, ".slick-slider .slick-dots li button": {"padding": "max(0.21vw, 2px)"}, ".slick-slider .slick-dots li button::before": {"width": "max(0.5vw, 6px) !important", "height": "max(0.5vw, 6px) !important"}}}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "The Party Shop", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235467_img1_MOB"}, "linkData": {"title": "The Party Shop", "to": "/browse/category.do?cid=1119499#pageId=0&department=136&mlink=5058,HP_HERO2_W"}}, "overlay": {"alt": "", "srcUrl": "", "desktopSrcUrl": ""}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "The Party Shop", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235467_img2_MOB"}, "linkData": {"title": "The Party Shop", "to": "/browse/category.do?cid=1119499#pageId=0&department=136&mlink=5058,HP_HERO2_W"}}, "overlay": {"alt": "", "srcUrl": "", "desktopSrcUrl": ""}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"style": {"minHeight": "calc(98vw*(715/1440))", "flexDirection": "column"}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "The Party Shop", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235467_copy_DESK"}, "linkData": {"title": "The Party Shop", "to": "/browse/category.do?cid=1119499#pageId=0&department=136&mlink=5058,HP_HERO2_W"}}, "overlay": {"alt": "", "srcUrl": "", "desktopSrcUrl": ""}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "The Party Shop", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235467_img_DESK"}, "linkData": {"title": "The Party Shop", "to": "/browse/category.do?cid=1119499#pageId=0&department=136&mlink=5058,HP_HERO2_W"}}, "overlay": {"alt": "", "srcUrl": "", "desktopSrcUrl": ""}}}]}}}}, {"instanceDesc": "CTA mob+desk", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "5vw 5vw 0"}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"display": "unset", "fontSize": "3vw"}, "components": [{"type": "builtin", "name": "span", "data": {"components": ["Made plans just to wear these outfits."]}}]}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"paddingTop": "2vw"}, "components": [{"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"position": "relative"}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Visit The Party Shop"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1119499#pageId=0&department=136&mlink=5058,HP_HERO2_W"}, {"text": "Men", "href": "/browse/category.do?cid=1128169#pageId=0&department=75&mlink=5058,HP_HERO2_M"}, {"text": "Girls", "href": "/browse/category.do?cid=1112193#pageId=0&department=48&mlink=5058,HP_HERO2_G"}, {"text": "Boys", "href": "/browse/category.do?cid=1112687#pageId=0&department=16&mlink=5058,HP_HERO2_B"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1099317#pageId=0&mlink=5058,HP_HERO2_TODDLER"}, {"text": "Baby", "href": "/browse/category.do?cid=1119370#pageId=0&mlink=5058,HP_HERO2_BABY"}]}}]}}}]}}]}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "baseline", "padding": "1.5vw 0 0"}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"display": "flow", "whiteSpace": "normal", "fontSize": "1vw"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"whiteSpace": "pre"}, "components": ["Made plans just to wear these outfits."]}}]}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexWrap": "wrap"}, "components": [{"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"> div": {"zIndex": "31 !important"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Visit The Party Shop"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1119499#pageId=0&department=136&mlink=5058,HP_HERO2_W"}, {"text": "Men", "href": "/browse/category.do?cid=1128169#pageId=0&department=75&mlink=5058,HP_HERO2_M"}, {"text": "Girls", "href": "/browse/category.do?cid=1112193#pageId=0&department=48&mlink=5058,HP_HERO2_G"}, {"text": "Boys", "href": "/browse/category.do?cid=1112687#pageId=0&department=16&mlink=5058,HP_HERO2_B"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1099317#pageId=0&mlink=5058,HP_HERO2_TODDLER"}, {"text": "Baby", "href": "/browse/category.do?cid=1119370#pageId=0&mlink=5058,HP_HERO2_BABY"}], "style": {"mobile": {"whiteSpace": "nowrap"}}}}]}}}]}}]}}]}}]}}}}]}}, {"instanceName": "optly-placeholder-3", "instanceDesc": "UNREC_BLANK", "experimentRunning": true, "name": "OptimizelyPlaceholder", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "0", "large": "0"}}}, {"instanceName": "optly-placeholder-4", "instanceDesc": "10-23-23 US - Gifted. The Shop.", "ciid": "7658ebf7-bcf2-4f2e-942a-84e924dfc0fd", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "calc(98vw*(944/640))", "large": "calc(98vw*(752/1440))"}, "style": {"position": "relative", "maxWidth": "767px", "margin": "0px auto 15vw"}, "desktopStyle": {"position": "relative", "maxWidth": "2560px", "margin": "0px 2.5vw 7vw"}, "components": [{"instanceDesc": "Banner Image", "name": "div", "type": "builtin", "data": {"style": {}, "components": [{"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Gifted. The Shop.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235894_copy_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235894_copy_DESK"}, "linkData": {"title": "Gifted. The Shop.", "to": "/browse/category.do?cid=3024953#pageId=0&mlink=5058,HP_HERO3_IMAGE_ALL"}}}}, {"name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Gifted. The Shop.", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235894_img_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235894_img_DESK"}, "linkData": {"title": "Gifted. The Shop.", "to": "/browse/category.do?cid=3024953#pageId=0&mlink=5058,HP_HERO3_IMAGE_ALL"}}, "overlay": {"alt": "", "srcUrl": "", "desktopSrcUrl": ""}}}]}}, {"instanceDesc": "CTA mob+desk", "name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "5vw 5vw 0"}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "fontSize": "3vw"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"whiteSpace": "pre"}, "components": [" "]}}]}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"paddingTop": "0"}, "components": [{"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"position": "relative"}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Start Gifting"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1119498#pageId=0&department=136&mlink=5058,HP_HERO3_CTA_W"}, {"text": "Men", "href": "/browse/category.do?cid=1119476#pageId=0&mlink=5058,HP_HERO3_CTA_M"}, {"text": "Girls", "href": "/browse/category.do?cid=1109055#pageId=0&department=48&mlink=5058,HP_HERO3_CTA_G"}, {"text": "Boys", "href": "/browse/category.do?cid=1112189#pageId=0&department=16&mlink=5058,HP_HERO3_CTA_B"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1101805#pageId=0&mlink=5058,HP_HERO3_CTA_Toddler"}, {"text": "Baby", "href": "/browse/category.do?cid=1119368#pageId=0&mlink=5058,HP_HERO3_CTA_Baby"}]}}]}}}]}}]}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "CTA Sub Message + CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "baseline", "padding": "1.5vw 0 0"}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"display": "flow", "whiteSpace": "normal", "fontSize": "1vw"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"whiteSpace": "pre"}, "components": [" "]}}]}}, {"instanceDesc": "CTAs Lockup", "name": "div", "type": "builtin", "data": {"style": {}, "components": [{"instanceDesc": "Sub Message", "name": "div", "type": "builtin", "data": {"style": {"display": "flex", "flexWrap": "wrap"}, "components": [{"instanceDesc": "CTAs", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_hp-cta", "style": {"> div": {"zIndex": "30 !important"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Start Gifting"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1119498#pageId=0&department=136&mlink=5058,HP_HERO3_CTA_W"}, {"text": "Men", "href": "/browse/category.do?cid=1119476#pageId=0&mlink=5058,HP_HERO3_CTA_M"}, {"text": "Girls", "href": "/browse/category.do?cid=1109055#pageId=0&department=48&mlink=5058,HP_HERO3_CTA_G"}, {"text": "Boys", "href": "/browse/category.do?cid=1112189#pageId=0&department=16&mlink=5058,HP_HERO3_CTA_B"}, {"text": "<PERSON><PERSON>", "href": "/browse/category.do?cid=1101805#pageId=0&mlink=5058,HP_HERO3_CTA_Toddler"}, {"text": "Baby", "href": "/browse/category.do?cid=1119368#pageId=0&mlink=5058,HP_HERO3_CTA_Baby"}]}}]}}}]}}]}}]}}]}}}}]}}, {"instanceName": "optly-placeholder-5", "instanceDesc": "10-23-23 US - VisNav", "ciid": "83d7e26e-0082-4d1d-a183-e499000bc361", "experimentRunning": true, "name": "div", "type": "builtin", "data": {"lazy": true, "shouldWaitForOptimizely": true, "defaultHeight": {"small": "calc(98vw*(2003/640))", "large": "calc(98vw*(968/1440))"}, "style": {"maxWidth": "767px", "position": "relative", "margin": "0px 3vw 15vw"}, "desktopStyle": {"maxWidth": "2560px", "position": "relative", "margin": "0px 8vw 7vw"}, "components": [{"instanceDesc": "Headline", "type": "builtin", "name": "h2", "data": {"style": {"fontSize": "4vw", "padding": "0 2vw 3vw"}, "desktopStyle": {"fontSize": "1.3vw", "padding": "0 0.8vw 1vw"}, "components": ["Shop by Division"]}}, {"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_hp-visnav"}, "style": {"> div": {"padding": "2vw 2vw 6vw"}, ".wcd_hp-cta": {"marginTop": "3vw"}, "@media only screen and (min-width:768px)": {"> div": {"padding": "0.8vw 0.8vw 2vw"}, ".wcd_hp-cta": {"marginTop": "1.75vw"}}}, "components": [{"instanceDesc": "vdn_01", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Women", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_Women_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_Women_DESK"}, "linkData": {"title": "Women", "to": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,HP_VDN_1_W_HOL235662_IMAGE"}}, "ctaList": {"className": "wcd_hp-cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "Women"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=8792#pageId=0&department=136&mlink=5058,HP_VDN_1_W_HOL235662_CTA"}, {"text": "Maternity", "href": "/browse/category.do?cid=11437#pageId=0&department=136&mlink=5058,HP_VDN_1_MAT_HOL235662_CTA"}, {"text": "GapBody", "href": "/browse/category.do?cid=1140272#pageId=0&department=136&mlink=5058,HP_VDN_1_Body_HOL235662_CTA"}, {"text": "GapFit", "href": "/browse/category.do?cid=1117374#pageId=0&department=136&mlink=5058,HP_VDN_1_Fit_HOL235662_CTA"}]}}]}}}, {"instanceDesc": "vdn_02", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Men", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_Men_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_Men_DESK"}, "linkData": {"title": "Men", "to": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,HP_VDN_2_M_HOL235662_IMAGE"}}, "ctaList": {"className": "wcd_hp-cta", "ctas": [{"linkData": {"to": "/browse/category.do?cid=11900#pageId=0&department=75&mlink=5058,30016106,HP_VDN_2_M_HOL235662_CTA"}, "composableButtonData": {"children": "Men"}}]}}}, {"instanceDesc": "vdn_03", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Girls", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_Girls_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_Girls_DESK"}, "linkData": {"title": "Girls", "to": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,30016106,HP_VDN_3_G_HOL235662_IMAGE"}}, "ctaList": {"className": "wcd_hp-cta", "ctas": [{"linkData": {"to": "/browse/category.do?cid=63895#pageId=0&department=48&mlink=5058,30016106,HP_VDN_3_G_HOL235662_CTA"}, "composableButtonData": {"children": "Girls"}}]}}}, {"instanceDesc": "vdn_04", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Boys", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_Boys_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_Boys_DESK"}, "linkData": {"title": "Boys", "to": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,30016106,HP_VDN_4_B_HOL235662_IMAGE"}}, "ctaList": {"className": "wcd_hp-cta", "ctas": [{"linkData": {"to": "/browse/category.do?cid=63896#pageId=0&department=16&mlink=5058,30016106,HP_VDN_4_B_HOL235662_CTA"}, "composableButtonData": {"children": "Boys"}}]}}}, {"instanceDesc": "vdn_05", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "<PERSON>ler Girl", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_ToddlerGirl_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_ToddlerGirl_DESK"}, "linkData": {"title": "<PERSON>ler Girl", "to": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,30016106,HP_VDN_5_TG_HOL235662_IMAGE"}}, "ctaList": {"className": "wcd_hp-cta", "ctas": [{"linkData": {"to": "/browse/category.do?cid=63863#pageId=0&department=165&mlink=5058,30016106,HP_VDN_5_TG_HOL235662_CTA"}, "composableButtonData": {"children": "<PERSON>ler Girl"}}]}}}, {"instanceDesc": "vdn_06", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "<PERSON><PERSON>", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_ToddlerBoy_MOB?v=1", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_ToddlerBoy_DESK"}, "linkData": {"title": "<PERSON><PERSON>", "to": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,30016106,HP_VDN_6_TB_HOL235662_IMAGE"}}, "ctaList": {"className": "wcd_hp-cta", "ctas": [{"linkData": {"to": "/browse/category.do?cid=1016138#pageId=0&department=166&mlink=5058,30016106,HP_VDN_6_TB_HOL235662_CTA"}, "composableButtonData": {"children": "<PERSON><PERSON>"}}]}}}, {"instanceDesc": "vdn_07", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Baby Girl", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_BabyGirl_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_BabyGirl_DESK"}, "linkData": {"title": "Baby Girl", "to": "/browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,30016106,HP_VDN_7_BG_HOL235662_IMAGE"}}, "ctaList": {"className": "wcd_hp-cta", "ctas": [{"linkData": {"to": "browse/category.do?cid=14249#pageId=0&department=165&mlink=5058,30016106,HP_VDN_7_BG_HOL235662_CTA"}, "composableButtonData": {"children": "Baby Girl"}}]}}}, {"instanceDesc": "vdn_08", "name": "LayeredContentModule", "type": "sitewide", "data": {"background": {"image": {"alt": "Baby Boy", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_BabyBoy_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/HOL235662_BabyBoy_DESK"}, "linkData": {"title": "Baby Boy", "to": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,30016106,HP_VDN_8_BB_HOL235662_IMAGE"}}, "ctaList": {"className": "wcd_hp-cta", "ctas": [{"linkData": {"to": "/browse/category.do?cid=95575#pageId=0&department=166&mlink=5058,30016106,HP_VDN_8_BB_HOL235662_CTA"}, "composableButtonData": {"children": "Baby Boy"}}]}}}]}}]}}, {}, {}, {}, {}]}, "sitewide": {"desktopemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "attrition_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "isAsyncExperiment": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"instanceName": "dpg_emergency_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "redpointExperimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "mobileemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "attrition_banner_mob", "type": "builtin", "name": "div", "experimentRunning": true, "isAsyncExperiment": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}, {"instanceName": "dpg_emergency_banner_mob", "type": "builtin", "name": "div", "experimentRunning": true, "redpointExperimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "below-topnav": {"type": "builtin", "name": "div", "data": {"components": [{}]}}, "headline": {"type": "builtin", "name": "div", "data": {"components": []}}, "secondary-headline": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "gpec_secondaryheadline-gsb-102323", "instanceDesc": "11/16 GSB - keep instanceName 111623", "ciid": "d170669b-7e90-449a-98c0-a1dc7799de49", "experimentRunning": false, "name": "div", "type": "builtin", "data": {"props": {"style": {"backgroundColor": "#000", "position": "relative", "width": "100%", "maxWidth": "2560px", "height": "auto", "lineHeight": "0", "margin": "0 auto", "display": "flex"}}, "components": [{"useGreyLoadingEffect": false, "name": "LayeredContentModule", "type": "sitewide", "meta": {"includePageTypes": ["home"]}, "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "transparent"}, "desktopStyle": {"width": "100%", "backgroundColor": "transparent"}}, "background": {"image": {"alt": "Cardmember Exclusive Extra 10% off code GOOD Online &in stores. Ends 11/19. Not a member? Apply Now", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/20231116_FriendsgivingSale_US_Card_GMB_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/20231116_FriendsgivingSale_US_Card_GMB_DESK"}, "linkData": {"to": "https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSGSB&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=55277,GLOBALBANNER_CARD_GGR_ACQ", "target": "_self", "title": "Cardmember Exclusive Extra 10% off code GOOD Online & in stores. Ends 11/19. Not a member? Apply Now"}}, "overlay": {}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998937", "height": "500px"}}, "composableButtonData": {"children": "Exclusions apply.", "style": {"position": "absolute", "background": "unset", "textTransform": "unset", "padding": "0", "color": "#ffffff", "fontSize": "1.9vw", "bottom": "2.5vw", "right": "9vw"}, "desktopStyle": {"fontSize": "0.75vw", "bottom": ".7vw", "right": "4.5vw"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998937", "height": "500px"}}, "composableButtonData": {"children": "details", "style": {"position": "absolute", "background": "unset", "textDecoration": "underline", "padding": "0", "color": "#ffffff", "fontSize": "1.9vw", "bottom": "2.5vw", "right": "1vw"}, "desktopStyle": {"fontSize": "0.75vw", "bottom": "0.7vw", "right": "1.25vw"}}}]}}}, {"name": "Carousel", "type": "sitewide", "meta": {"excludePageTypes": ["home"]}, "data": {"carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 0, "autoplaySpeed": 2000, "fade": false, "displayPlayPauseBtn": false, "arrows": false, "arrowPosition": "0", "prevArrowUrl": "/Asset_Archive/GPWeb/content/static/brand-icons/gp_carousel-carat_spring2021_left--blue.svg", "nextArrowUrl": "/Asset_Archive/GPWeb/content/static/brand-icons/gp_carousel-carat_spring2021_right--blue.svg", "displayArrows": {"mobile": false, "desktop": false}}, "carouselStyle": {"padding": "0px"}, "style": {}, "components": [{"instanceDesc": "GSB_10_23_2023_1", "name": "div", "type": "builtin", "data": {"props": {"style": {"backgroundColor": "#000", "position": "relative", "width": "100%", "lineHeight": "0", "margin": "0 auto"}}, "components": [{"useGreyLoadingEffect": false, "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "transparent"}, "desktopStyle": {"width": "100%", "backgroundColor": "transparent"}}, "background": {"image": {"alt": "Pre-Black Friday Sale 40% Off Your Purchase 50% off <PERSON><PERSON>", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/20231116_FriendsgivingSale_USEC_GMB_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/20231116_FriendsgivingSale_USEC_GMB_DESK"}, "linkData": {"to": "/browse/category.do?cid=1127938#pageId=0&department=136&mlink=55277,globalbanner", "target": "_self", "title": "Pre-Black Friday Sale 40% Off Your Purchase 50% off <PERSON><PERSON>"}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998939,998957", "height": "500px"}}, "composableButtonData": {"children": "Applied at checkout.", "style": {"position": "absolute", "background": "unset", "textTransform": "unset", "padding": "0", "color": "#ffffff", "fontSize": "1.9vw", "bottom": "2.5vw", "right": "9vw", "display": "none"}, "desktopStyle": {"fontSize": "0.75vw", "bottom": "0.7vw", "right": "4.5vw", "display": "none"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998939,998957", "height": "500px"}}, "composableButtonData": {"children": "details", "style": {"position": "absolute", "background": "unset", "textDecoration": "underline", "padding": "0", "color": "#ffffff", "fontSize": "1.9vw", "bottom": "2.5vw", "right": "1vw"}, "desktopStyle": {"fontSize": "0.75vw", "bottom": "0.7vw", "right": "1.25vw"}}}]}}}]}}, {"instanceDesc": "GSB_10_23_2023_2", "name": "div", "type": "builtin", "data": {"props": {"style": {"backgroundColor": "#000", "position": "relative", "width": "100%", "lineHeight": "0", "margin": "0 auto"}}, "components": [{"useGreyLoadingEffect": false, "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": false, "container": {"className": "", "style": {"width": "100%", "backgroundColor": "transparent"}, "desktopStyle": {"width": "100%", "backgroundColor": "transparent"}}, "background": {"image": {"alt": "Cardmember Exclusive Extra 10% off code GOOD Online &in stores. Ends 11/19. Not a member? Apply Now", "srcUrl": "https://gapprod.a.bigcontent.io/v1/static/20231116_FriendsgivingSale_US_Card_GMB_MOB", "desktopSrcUrl": "https://gapprod.a.bigcontent.io/v1/static/20231116_FriendsgivingSale_US_Card_GMB_DESK"}, "linkData": {"to": "https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSGSB&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=55277,GLOBALBANNER_CARD_GGR_ACQ", "target": "_self", "title": "Cardmember Exclusive Extra 10% off code GOOD Online &in stores. Ends 11/19. Not a member? Apply Now"}}, "overlay": {}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998937", "height": "500px"}}, "composableButtonData": {"children": "Exclusions apply.", "style": {"position": "absolute", "background": "unset", "textTransform": "unset", "padding": "0", "color": "#ffffff", "fontSize": "1.9vw", "bottom": "2.5vw", "right": "9vw"}, "desktopStyle": {"fontSize": "0.75vw", "bottom": "0.7vw", "right": "4.5vw"}}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "iframeData": {"title": "Details", "src": "/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=998937", "height": "500px"}}, "composableButtonData": {"children": "details", "style": {"position": "absolute", "background": "unset", "textDecoration": "underline", "padding": "0", "color": "#ffffff", "fontSize": "1.9vw", "bottom": "2.5vw", "right": "1vw"}, "desktopStyle": {"fontSize": "0.75vw", "bottom": "0.7vw", "right": "1.25vw"}}}]}}}]}}], "modalCloseButtonAriaLabel": "Close", "buttonSetting": {"nextArrowAlt": "next", "pauseAltText": "pause", "playAltText": "play", "prevArrowAlt": "previous"}}}]}}]}}, "edfslarge": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-large", "type": "sitewide", "name": "MktEdfsLarge", "tileStyle": {"alignItems": "center", "display": "flex", "height": "40px", "margin-top": "1px", "textTransform": "uppercase"}, "experimentRunning": true, "data": {"shouldWaitForOptimizely": "true", "defaultData": {"text": "Free Shipping on $50+ for Rewards Members", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=1194685", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "SIGN IN OR JOIN", "path": "/my-account/sign-in", "style": {"letterSpacing": "0", "fontSize": "1em", "display": "inline", "position": "relative", "fontWeight": "400", "top": "-1px", "fontFamily": "'Gap Sans', Helvetica, Arial, Roboto, sans-serif"}}}}]}}, "edfssmall": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-small", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"shouldWaitForOptimizely": "true", "lazy": false, "defaultHeight": {"small": "50px", "large": "80px"}, "isVisible": {"large": false, "small": true}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto"}, "components": [{"instanceDesc": "WCD HP CSS Modifications", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>#sitewide-app > header > div.sitewide-13o7eu2 > div > div:nth-child(2) > div > div > div > button {text-transform:uppercase}</style>"}}, {"type": "sitewide", "name": "MktEdfsSmall", "data": {"lazy": false, "experimentRunning": false, "styles": {"headline": {"fontSize": "10px", "padding": "0 10px", "textTransform": "uppercase"}}, "defaultData": {"text": "Free Shipping on $50+ for Rewards Members", "detailsLink": "Details"}, "modalTitle": "SHIPPING & RETURNS", "modalUrl": "/customerService/info.do?cid=1194685", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "SIGN IN OR JOIN", "path": "/my-account/sign-in", "style": {"letterSpacing": "0", "display": "inline", "position": "relative", "top": "-2px", "fontSize": "1em", "fontWeight": "400", "paddingRight": "0", "fontFamily": "'Gap Sans', Helvetica, Arial, Roboto, sans-serif"}}}}]}}}}]}}, "hamburgerNavBanner": {"type": "builtin", "name": "div", "data": {"components": [{}]}}, "hamnavRedesignBanner": {"type": "builtin", "name": "div", "data": {"components": [{}]}}, "popup": {"type": "builtin", "name": "div", "data": {"components": [{}]}}, "promorover": {"type": "builtin", "name": "div", "data": {"components": []}}, "prefooter": {"type": "builtin", "name": "div", "data": {"components": [{}]}}, "countdown": {"type": "builtin", "name": "div", "data": {"components": []}}, "footer": {"footer-ciid": "9fda533e-ec17-4adc-bcf9-3cd3f3cff9a1", "footer-desc": "2023-08-29 Fall Style Update", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "sitewide", "components": [{"name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "582px", "large": "435px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "width": "100%"}, "components": [{"name": "Footer", "type": "sitewide", "data": {"socialLinks": [{"to": "https://www.facebook.com/gap/", "text": "Follow Gap on Facebook"}], "emailRegistration": {"title": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<h3 class=\"wcd_footer_h1 uppercase\">See It First</h3>"}}, "emailPlaceholderText": "Enter your email address", "submitButtonText": "Join", "submitButtonOptions": {"mobile": {"className": "wcd_footer_cta"}, "desktop": {"className": "wcd_footer_cta"}}, "disclaimerText": {"name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<p class=\"wcd_footer_copy legal\"><a onclick=\"return contentItemLink(this,'','CS_Footer_PrivacyPolicy');\" href=\"https://corporate.gapinc.com/en-us/consumer-privacy-policy\" target=\"_blank\" class=\"uppercase nowrap\">Privacy Policy</a>"}}}, "marketingBannerLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"instanceName": "footer_overrides", "instanceDesc": "<PERSON><PERSON> Footer Overrides", "name": "HTMLInjectionComponent", "type": "sitewide", "brand": "gap", "locale": "en_US", "data": {"html": "<style>#sitewide-footer,#sitewide-footer button,#sitewide-footer input,#sitewide-footer select,#sitewide-footer textarea{font-family:'Gap Sans',Helvetica,Arial,Roboto,sans-serif}#sitewide-footer{background-color:#000;color:#fff}.gap-footer *{box-sizing:border-box}.gap-footer .nowrap{white-space:nowrap}.gap-footer .uppercase{text-transform:uppercase}.gap-footer sup{font-size:1em;line-height:0;vertical-align:baseline}.gap-footer .wcd_footer_h1{font-size:14px;font-weight:500;line-height:1.125;margin-bottom:.25em}.gap-footer .wcd_footer_copy:not(:last-child){margin-bottom:1.125em}.gap-footer .wcd_footer_copy.legal{font-size:10px}.gap-footer .wcd_footer_copy a{text-decoration:underline}.gap-footer .wcd_footer_cta a,.gap-footer .wcd_footer_cta button,.gap-footer a.wcd_footer_cta,.gap-footer button.wcd_footer_cta{-ms-flex-align:center;align-items:center;background-color:#fff;border-width:0;color:#767676;font-size:14px;font-weight:500;height:32px;-ms-flex-pack:center;justify-content:center;letter-spacing:0;padding-left:16px;padding-right:16px;text-transform:uppercase}.gap-footer .wcd_footer_cta a:hover,.gap-footer .wcd_footer_cta button:hover,.gap-footer a.wcd_footer_cta:hover,.gap-footer button.wcd_footer_cta:hover{background-color:#fff;border-width:0;color:#2b2b2b}.gap-footer .wcd_footer_cta{display:-ms-flexbox;display:flex}.gap-footer .wcd_footer_cta.full-width{width:100%}.gap-footer .wcd_footer_cta.full-width a,.gap-footer .wcd_footer_cta.full-width button{width:100%}.gap-footer .wcd_footer_cta.full-width a:not(:first-child),.gap-footer .wcd_footer_cta.full-width button:not(:first-child){margin-left:8px}.gap-footer .wcd_footer_cta.details button{background-color:transparent;color:#fff;display:inline;font-size:10px;height:auto;min-height:16px;min-width:36px;padding:0;text-decoration:underline}.gap-footer .wcd_footer_cta.details button:hover{color:#fff}.gap-footer .wcd_footer_cta a,.gap-footer .wcd_footer_cta button{display:-ms-flexbox;display:flex}.gap-footer .wcd_footer_cta span{font-size:1.125em;padding-bottom:.25em}.gap-footer .wcd_footer_cta ul{background-color:transparent;box-shadow:none;padding-bottom:4px}.gap-footer .wcd_footer_cta li{border-bottom-width:0;border-color:#fff;padding:0}.gap-footer .wcd_footer_cta li a{font-weight:400;padding-left:32px;text-transform:none}.gap-footer [data-testid=prefooter-row]{margin-bottom:0}.gap-footer .email-registration__wrapper{-ms-flex-align:start;align-items:flex-start;background-color:transparent;min-height:120px;padding:26px 0}.gap-footer .email-registration__wrapper>div{margin:0 auto;max-width:640px;width:calc(100% - 32px)}.gap-footer .email-registration__wrapper .email-registration__title{max-width:100%;padding:0;text-align:left}.gap-footer .email-registration__wrapper .email-registration__inputs{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;max-width:420px}.gap-footer .email-registration__wrapper .email-registration__disclaimer{padding-left:0}.gap-footer .email-registration__wrapper .email-registration__form{-ms-flex-align:end;align-items:flex-end;display:-ms-flexbox;display:flex;margin-bottom:24px}.gap-footer .email-registration__wrapper .email-text-input-wrapper{margin-right:24px}.gap-footer .email-registration__wrapper .email-registration__form-email{margin:0;padding-bottom:0}.gap-footer .email-registration__wrapper .email-registration__form-email input[type=email]{margin-top:0;padding:0}span.sitewide-1sr49e6{display:none}.gap-footer .email-registration__wrapper .email-registration__form-email span.sitewide-v1qhrf-LabelText-Label{font-size:10px;text-transform:none;top:0}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container .wcd_footer_cta{min-height:32px;padding-bottom:0;padding-top:0}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container div[aria-label=loading]{transform:rotate(90deg)}.gap-footer .email-registration__wrapper .notification-after-button:empty,.gap-footer .email-registration__wrapper .notification-before-form:empty{display:none}.gap-footer .medallia-feedback-wrapper{-ms-flex-order:4;order:4;padding:0 16px;width:100%}.gap-footer .medallia-feedback-wrapper>button{-ms-flex-align:center;align-items:center;background-color:#fff;border-width:0;color:#2b2b2b;display:-ms-flexbox;display:flex;font-weight:400;height:36px;-ms-flex-pack:center;justify-content:center;letter-spacing:0;margin:0 auto;max-width:640px;padding:0 16px;width:100%}.gap-footer .medallia-feedback-wrapper>button img{margin-right:.375rem}.gap-footer .footer-copyright-section{background-color:#000;border-top-color:#fff;border-width:0;color:#fff;-ms-flex-order:5;order:5;padding:24px 0 80px;width:100%}.gap-footer .footer-copyright-section .footer-legal__wrapper{margin:0 auto;max-width:640px;text-align:left;width:calc(100% - 32px)}.gap-footer .footer-copyright-section .footer_copyright-row{font-size:11px;line-height:1.5}.gap-footer .footer-copyright-section .footer_copyright-row:not(:last-child){margin-bottom:1.5em}.gap-footer .footer-copyright-section a,.gap-footer .footer-copyright-section button{color:inherit;font-size:inherit}.gap-footer .footer-copyright-section a:hover,.gap-footer .footer-copyright-section button:hover{text-decoration:underline}.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--divider,.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--span{color:inherit;font-size:inherit}.gap-footer .footer-copyright-section .footer-legal__wrapper .site-footer_sublinks--span,.gap-footer .footer-copyright-section .footer-legal__wrapper a,.gap-footer .footer-copyright-section .footer-legal__wrapper button{display:inline-block;text-transform:uppercase}.gap-footer .footer-container-wrapper{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;max-width:100%}.gap-footer .footer-container-wrapper .copy-wrapper{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;margin-bottom:12px}.gap-footer .footer-container-wrapper>div:nth-child(5){-ms-flex-direction:column;flex-direction:column;margin-left:auto;margin-right:auto;max-width:672px;width:100%}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child{margin-bottom:30px}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta{background-color:transparent;color:inherit;-ms-flex-direction:column;flex-direction:column}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta a,.gap-footer .footer-container-wrapper>div:nth-child(5)>div:first-child .wcd_footer_cta button{background-color:transparent;color:inherit;height:24px;-ms-flex-pack:start;justify-content:flex-start}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:nth-child(2){margin-bottom:6px;padding:0 16px}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:10px;line-height:1.25}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a:not(:last-child){margin-bottom:.5em}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a:hover{text-decoration:underline}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column .wcd_footer_header{margin-bottom:.75em;text-transform:uppercase}@media only screen and (min-width:768px){.gap-footer .wcd_footer_h1{margin-bottom:1em}.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:16px;font-weight:500;line-height:1}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:14px}.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:12px}.gap-footer [data-testid=prefooter-row]{display:block;padding-bottom:48px}.gap-footer .email-registration__wrapper{padding-bottom:0;padding-left:0;padding-top:0}.gap-footer .email-registration__wrapper>div{margin-left:0;max-width:100%;width:100%}.gap-footer .email-registration__wrapper .email-text-input-wrapper{margin-right:10px}.gap-footer .footer-copyright-section{border-top-width:1px;padding-top:44px}.gap-footer .footer-copyright-section .footer-legal__wrapper{margin:0;max-width:1920px;padding-left:2.5%;padding-right:2.5%}.gap-footer .footer-container-wrapper{-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:justify;justify-content:space-between;margin:0 auto;max-width:1920px;padding-top:30px}.gap-footer .footer-container-wrapper .copy-wrapper{-ms-flex-align:center;align-items:center;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:wrap;flex-wrap:wrap;margin-bottom:0}.gap-footer .footer-container-wrapper .copy-wrapper>div:not(:last-child){margin-right:.75em}.gap-footer .footer-container-wrapper>div:nth-child(5)>div:nth-child(2){display:none}.gap-footer .wcd_footer-links-wrapper{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{-ms-flex-positive:1;flex-grow:1}.gap-footer [data-testid=prefooter-row]{padding-left:2.5%;padding-right:2.5%;width:100%}.gap-footer .footer-container-wrapper>div:nth-child(3){padding-bottom:48px;padding-left:2.5%;padding-right:2.5%;width:100%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:16px}}@media only screen and (min-width:1024px){.gap-footer .wcd_footer-links-wrapper{-ms-flex-pack:start;justify-content:flex-start}.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column{-ms-flex-positive:initial;flex-grow:initial}.gap-footer [data-testid=prefooter-row]{padding-right:0;width:300px}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:3%;padding-left:0;width:calc(97% - 300px)}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:7%}}@media only screen and (min-width:1280px){.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:14px}.gap-footer [data-testid=prefooter-row]{width:24%}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:6%;padding-bottom:84px;width:70%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:11%}}@media only screen and (min-width:1440px){.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:24px}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:18px}.gap-footer .footer-copyright-section .footer_copyright-row,.gap-footer .wcd_footer_copy.legal{font-size:16px}.gap-footer .email-registration__wrapper .email-registration__form-submit-button-container .wcd_footer_cta{font-size:20px;height:52px;padding:0 26px}.gap-footer .footer-container-wrapper{padding-top:54px}.gap-footer [data-testid=prefooter-row]{width:29%}.gap-footer .footer-container-wrapper>div:nth-child(3){width:65%}}@media only screen and (min-width:1920px){.gap-footer .wcd_footer_h1,.gap-footer .wcd_footer_header{font-size:26px}.gap-footer .email-registration__wrapper .email-registration__form-email span,.gap-footer .wcd_footer-links-wrapper .wcd_footer-links-column a{font-size:21px}.gap-footer [data-testid=prefooter-row]{width:24%}.gap-footer .footer-container-wrapper>div:nth-child(3){margin-left:0;width:71%}.gap-footer .wcd_footer-links-column:not(:last-child){padding-right:18%}}@media only screen and (max-width:767px){.gap-footer .footer-container-wrapper>div:nth-child(2){display:none}}</style>"}}]}}}}, "customerSupportLayout": {"name": "LayoutComponent", "type": "sitewide", "data": {"mobile": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Mobile Footer Links", "name": "LayeredContentModule", "type": "sitewide", "data": {"ctaList": {"className": "wcd_footer_cta", "ctas": [{"buttonDropdownData": {"heading": {"text": "Customer Support"}, "submenu": [{"text": "Customer Service", "href": "/customerService/info.do?cid=2136&mlink=55278,********,CS_Footer_CustomerService"}, {"text": "Buy Online. Pick Up In-Store.", "href": "/customerService/info.do?cid=1161798&mlink=55278,********,LP_BOPIS_footer_CTA"}, {"text": "Store Locator", "href": "/stores"}, {"text": "GapCash", "href": "/browse/info.do?cid=99996&mlink=55278,********,LP_GapCash_footer_CTA"}, {"text": "GiftCards", "href": "/customerService/info.do?cid=2116&mlink=55278,********,CS_Footer_Giftcards"}]}}, {"buttonDropdownData": {"heading": {"text": "Gap Good Rewards"}, "submenu": [{"text": "Join <PERSON> Good Rewards", "href": "/my-account/sign-in?mlink=55278,********,UNIFOOTER_GGR_ACQ"}, {"text": "Apply for a Credit Card", "href": "/my-account/sign-in?creditOffer=barclays&sitecode=GPSSUNIFTM&mlink=55278,********,UNIFOOTER_GGR_CARD_ACQ"}, {"text": "My Rewards & Benefits", "href": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=55278,********,UNIFOOTER_MTL_RET"}, {"text": "Pay Credit Card Bill", "href": "https://gap.barclaysus.com/servicing/home?redirectAction=/payment", "target": "_blank"}, {"text": "Learn More", "href": "/customerService/info.do?cid=1099008&sitecode=GPSSUNIFTILPM&mlink=55278,UNIFOOTER_GGR_LP_CARD_ACQ", "target": "_blank"}]}}, {"buttonDropdownData": {"heading": {"text": "About Us"}, "submenu": [{"text": "Our Values", "href": "https://www.gapinc.com/en-us/values", "target": "_blank"}, {"text": "Sustainability", "href": "https://www.gap.com/browse/info.do?cid=1086537", "target": "_blank"}, {"text": "Equality and Belonging", "href": "https://www.gap.com/browse/info.do?cid=1179886", "target": "_blank"}, {"text": "Careers", "href": "https://www.gapinc.com/en-us/careers/gap-careers", "target": "_blank"}]}}]}}}, {"instanceDesc": "Download The App", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_app"}, "components": [{"name": "a", "type": "builtin", "data": {"props": {"href": "https://gap.onelink.me/9QBP/12b7d83a", "className": "wcd_footer_cta full-width"}, "components": ["Download The App"]}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"components": [{"instanceDesc": "Desktop Footer Links", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-wrapper"}, "components": [{"instanceDesc": "Desktop Footer Links - Column 1", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["Customer Support"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=2136&mlink=55278,********,CS_Footer_CustomerService"}, "components": ["Customer Service"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1161798&mlink=55278,********,LP_BOPIS_footer_CTA", "target": "_blank"}, "components": ["Buy Online. Pick Up In-Store."]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/stores"}, "components": ["Store Locator"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/browse/info.do?cid=99996&mlink=55278,********,LP_GapCash_footer_CTA"}, "components": ["GapCash"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=2116&mlink=55278,********,CS_Footer_Giftcards"}, "components": ["GiftCards"]}}]}}, {"instanceDesc": "Desktop Footer Links - Column 2", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["Gap Good Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?mlink=55278,********,UNIFOOTER_GGR_ACQ"}, "components": ["Join <PERSON> Good Rewards"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?creditOffer=barclays&sitecode=GPSSUNIFTD&mlink=55278,********,UNIFOOTER_GGR_CARD_ACQ"}, "components": ["Apply for a Credit Card"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=55278,********,UNIFOOTER_MTL_RET"}, "components": ["My Rewards & Benefits"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://gap.barclaysus.com/servicing/home?redirectAction=/payment", "target": "_blank"}, "components": ["Pay Credit Card Bill"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/customerService/info.do?cid=1099008&sitecode=GPSSUNIFTILPD&mlink=55278,UNIFOOTER_GGR_LP_CARD_ACQ", "target": "_blank"}, "components": ["Learn More"]}}]}}, {"instanceDesc": "Desktop Footer Links - Column 3", "name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer-links-column"}, "components": [{"name": "div", "type": "builtin", "data": {"props": {"className": "wcd_footer_header"}, "components": ["About Us"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/values", "target": "_blank"}, "components": ["Our Values"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gap.com/browse/info.do?cid=1086537", "target": "_blank"}, "components": ["Sustainability"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gap.com/browse/info.do?cid=1179886", "target": "_blank"}, "components": ["Equality and Belonging"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "https://www.gapinc.com/en-us/careers/gap-careers", "target": "_blank"}, "components": ["Careers"]}}, {"name": "a", "type": "builtin", "data": {"props": {"href": "/browse/info.do?cid=1174367&shortlink=12b7d83a&c=SiteFooterLink_download_our_app&pid=Mobile%20Footer%20link&source_caller=ui"}, "components": ["Get the App"]}}]}}]}}]}}}}}}]}}}}]}, "header": {"CIID": "dea9bab2-7631-4c9f-8114-2745f1ca01ba", "byCid": [{"details": "Setting up rules for specific ciids", "configurationForCids": []}], "byPageType": [{"details": "Rules for homepage", "configurationForPageTypes": ["home"]}, {"details": "Rules for non-hp", "configurationForPageTypes": ["category", "product", "Information", "customlandingpage", "search", "dynamicerror", "division"]}], "default": {"details": "Universal rules", "isUtilityLinksEnabled": false, "headerLayout": "sameRow", "stickyScrollDirection": "up", "isStickyEnabled": true, "stickyBackground": "contrast", "contrast": "dark", "fullBleedOptions": {"fullBleedContrast": "light", "isFullBleedEnabled": false}, "styles": {}}}, "topnav": {"topnav-ciid": "d0e904a7-385d-42e7-a2d9-4b62ce8487d9", "instanceName": "topnav-230829", "name": "div", "type": "builtin", "experimentRunning": true, "data": {"style": {"width": "100%"}, "tabletStyle": {}, "desktopStyle": {}, "props": {}, "tabletProps": {}, "desktopProps": {}, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>#topNavWrapper > div > div > ul > li:nth-child(10) > div:nth-child(1) > a{font-weight:bold!important} @media only screen and (min-width: 1024px) { .sitewide-ila3p9-StyledDesktopHeaderOnlyLogoAtTop [data-wrapper-name='search-wrapper'] [role='search'] { padding-right: 0px; } div[role='search'] { box-sizing: border-box; margin: 0; padding: 10px 10px 18px 20px; padding-right: 0; font-size: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); } .meganav { box-shadow: 0px 10px 10px -4px rgba(0 0 0 / 15%); } .sitewide-s0ze5g-StyledDesktopHeaderLogoTopnavAndSearchAtSameRow { max-width: none; width: 94.3vw; justify-content: space-between; } .gap-topnav-logo-dt { min-width: 5.7vw; } div[data-testid='brand-header'] > div:first-child > div:first-child a { display: block; padding: 0; padding-left: 0; } .gap-topnav-logo-dt > a > img { width: 100%; } div[data-testid='header-logo-topnav-seach-at-same-row'] > div:nth-child(2) { width: 100% } #topNavWrapper > div:first-child { width: fit-content; } }</style>", "style": {}, "classes": ""}}, {"name": "MegaNav", "type": "sitewide", "data": {"isNavSticky": true, "classStyles": {"topnav li:not(.catnav--item)": "padding: 0;", "topnav a.divisionLink": "box-shadow: none !important; box-sizing: border-box; /*color: #2b2b2b;*/ display: block; font-size: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); font-weight: 400; height: 90px; line-height: 1; min-height: 0vw; padding: 40px 0 0; position: relative; text-transform: uppercase;", "topnav a.divisionLink::before": "border-color: transparent; border-style: solid; border-width: 0 0 1px; content: ''; height: min(max(12px, calc(0.75rem + ((1vw - 10.24px) * 0.6696))), 18px); left: 50%; min-height: 12px; padding-bottom: 3px; position: absolute; top: 40px; transform: translateX(-50%); width: calc(100% - 2.75vw);", "topnav a.divisionLink._selected": "color: #2b2b2b;", "topnav li:hover a.divisionLink": "background-color: #fff;", "topnav li:hover a.divisionLink::before": "border-color: #2b2b2b;", "topnav a.divisionLink._selected::before": "border-color: #2b2b2b;", "topnav a.divisionLink:hover": "box-shadow: none !important;", "topnav li.catnav--header > span": "border-bottom-color: #2b2b2b;", "topnav li.catnav--header > a": "border-bottom-color: #2b2b2b;", "topnav a.divisionLink.navlink-pink": "color: #e51937;", "topnav a.divisionLink.navlink-red": "color: #e51937", "topnav a.divisionLink.navlink-gift": "color: #e51937;", "topnav a.catnav--item--link.sitewide-4l9vad[data-categoryid='1187473']": "color: #e51937;", "topnav .catnav--item--link": "max-width: 265px;", "topnav .catnav-links a:hover": "border: 0;", "topnav .catnav-links a.catnav--item--link.sitewide-eja54e:hover": "border-bottom: 1px solid #767676;", "topnav li.catnav--item.sitewide-1l5zl4xli": "color: #e28743;", "meganav": "border-top-width: 0", "topnav li": "/*margin-left: auto; margin-right: 10%;*/", "topnav": "/*max-width: 100%; min-width: fit-content;*/", "topnav-container": "min-width: 61vw;"}, "activeDivisions": [{"name": "New", "divisionId": ["1086624"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=8792&mlink=39813,topnav_new_wna_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapprod.a.bigcontent.io/v1/static/HOL235456_img' alt='Womens New Arrivals image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL235456_CTA' alt='Womens New Arrivals'></a></li></ul></li>"], ["1139272"], ["3023115", "3019290", "3019289"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=1072174&mlink=39813,topnav_new_matchingPJs_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapprod.a.bigcontent.io/v1/static/HOL235457_img' alt='Matching PJs image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL235457_CTA' alt='Matching PJs'></a></li></ul></li>"]], "numberOfColumns": {}, "exclusionIds": [], "customStyles": {"1164542": {"inlineStyle": {"color": "#e51937"}}, "3013615": {"inlineStyle": {"color": "#e51937"}}, "3013628": {"inlineStyle": {"color": "#e51937"}}, "3013630": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Women", "divisionId": ["/browse/division.do?cid=5643&mlink=39813,30012485,Megnav_Women", "5646"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=3024953&mlink=39813,topnav_w_giftshop_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_img' alt='The Gift Shop image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_CTA' alt='The Gift Shop'></a></li></ul></li>"], ["1164545", "1131702"], ["1042481"], ["1131696"], ["3023110", "1131698"]], "numberOfColumns": {"1042481": 2}, "exclusionIds": [], "customStyles": {"65179": {"colorScheme": "sale"}}}, {"name": "Men", "divisionId": ["/browse/division.do?cid=5063&mlink=39813,30012485,Megnav_Men&clink=30012485", "5065"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=3024953&mlink=39813,topnav_m_giftshop_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_img' alt='The Gift Shop image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_CTA' alt='The Gift Shop'></a></li></ul></li>"], ["1164547", "1149531"], ["1042515"], ["1076121"]], "numberOfColumns": {"1042515": 2}, "exclusionIds": [], "customStyles": {"65289": {"colorScheme": "sale"}, "1008073": {"colorScheme": "sale"}, "1187426": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Girls", "divisionId": ["/browse/division.do?cid=1137865&mlink=39813,30012485,Megnav_Girls&clink=30012485", "6256"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=3024953&mlink=39813,topnav_g_giftshop_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_img' alt='The Gift Shop image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_CTA' alt='The Gift Shop'></a></li></ul></li>"], ["1161294", "1164548", "1056088"], ["1042516"], ["6258"]], "numberOfColumns": {"1042516": 2}, "exclusionIds": [], "customStyles": {"65194": {"colorScheme": "sale"}, "1137652": {"colorScheme": "sale"}, "1187423": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Boys", "divisionId": ["/browse/division.do?cid=1137867&mlink=39813,30012485,Megnav_Girls&clink=30012485", "6172"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=3024953&mlink=39813,topnav_b_giftshop_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_img' alt='The Gift Shop image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_CTA' alt='The Gift Shop'></a></li></ul></li>"], ["1161295", "1164549", "1056087"], ["1042518", "6189"], ["6174"]], "numberOfColumns": {"1042518": 2}, "exclusionIds": [], "customStyles": {"65217": {"colorScheme": "sale"}, "1137659": {"colorScheme": "sale"}, "1187425": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "<PERSON><PERSON>", "divisionId": ["/browse/division.do?cid=1137868&mlink=39813,30012485,Megnav_<PERSON><PERSON>&clink=30012485", "6413"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=3024953&mlink=39813,topnav_toddler_giftshop_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_img' alt='The Gift Shop image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_CTA' alt='The Gift Shop'></a></li></ul></li>"], ["1164550", "1149845"], ["1016135"], ["1016083"], ["1067853"]], "exclusionIds": [], "customStyles": {"65236": {"colorScheme": "sale"}, "65263": {"colorScheme": "sale"}, "1185682": {"inlineStyle": {"color": "#e51937"}}, "1187427": {"inlineStyle": {"color": "#e51937"}}, "1187428": {"inlineStyle": {"color": "#e51937"}}, "1193732": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Baby", "divisionId": ["/browse/division.do?cid=1137869&mlink=39813,30012485,Megnav_Baby&clink=30012485", "6487"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=3024953&mlink=39813,topnav_baby_giftshop_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_img' alt='The Gift Shop image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_CTA' alt='The Gift Shop'></a></li></ul></li>"], ["1164551", "3018069", "1164552", "1149847"], ["95461"], ["95574"], ["1067854"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=1119368&mlink=39813,topnav_baby_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapprod.a.bigcontent.io/v1/static/HOL235937_img' alt='Shop Baby Gifts image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL235937_CTA' alt='Shop Baby Gifts'></a></li></ul></li>"]], "exclusionIds": [], "customStyles": {"65208": {"colorScheme": "sale"}, "65261": {"colorScheme": "sale"}, "1187419": {"inlineStyle": {"color": "#e51937"}}, "1187422": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Maternity", "divisionId": ["/browse/division.do?cid=5997&mlink=39813,30012485,Megnav_Maternity&clink=30012485", "5997"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=3024953&mlink=39813,topnav_giftshop_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_img' alt='The Gift Shop image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_CTA' alt='The Gift Shop'></a></li></ul></li>"], ["1164546", "3018111", "1149538"], ["1042513"], ["1188906", "1014415"]], "numberOfColumns": {"1042513": 2}, "exclusionIds": [], "customStyles": {"65302": {"colorScheme": "sale"}, "1146678": {"colorScheme": "sale"}, "1187473": {"inlineStyle": {"color": "#e51937"}}}}, {"name": "Sale", "divisionId": ["1156863"], "megaNavOrder": [["1156864"], ["3024915"]], "numberOfColumns": {"1156864": 2}, "exclusionIds": [], "customStyles": {}}, {"name": "The Gift Shop", "divisionId": ["83061"], "megaNavOrder": [["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/category.do?cid=3024953&mlink=39813,topnav_giftshop_visnav' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_img' alt='The Gift Shop image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL235458_Updated_CTA' alt='The Gift Shop'></a></li></ul></li>"], ["1166400"], ["1142181"], ["<li class='catnav--header'><ul class='catnav-links'><li class='catnav--item'><a href='/browse/info.do?cid=2116' class='catnav--item--link' style='position:relative;display:block'><img style='position:relative' src='https://gapprod.a.bigcontent.io/v1/static/HOL235459_img' alt='Giftcard image'><img style='position:absolute;left:0;top:0;' src='https://gapprod.a.bigcontent.io/v1/static/HOL235459_CTA' alt='Giftcard'></a></li></ul></li>"]], "exclusionIds": [], "customStyles": {}}]}}]}}, "promodrawer": {"name": "PromoDrawerComponentV2", "type": "sitewide", "sitewide-promodrawer-ciid": "2023-11-16_55276_PromoDrawer_US", "instanceName": "promoDrawer-en_US", "experimentRunning": false, "data": {"shouldWaitForOptimizely": false, "buildInfo": ["2023-11-16_55276_PromoDrawer_US", "GP"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "info", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "Profile", "LoyaltyValueCenter"], "anchor": "bottom"}, "autoFire": "disabled", "disabledAutoFirePageTypes": ["category"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n  .pd_four-cta-lo7fevej {\n    background-color: #122344; /* default */\n    color: #fff; /* default */\n    height: 100%;\n    position: relative;\n    width: 100%;\n  }\n  .pd_four-cta-lo7fevej img {\n    margin: 0 auto;\n    max-width: 100%;\n  }\n  .pd_four-cta-lo7fevej .pd_four-cta--cta-container {\n    bottom: 4%;\n    box-sizing: border-box;\n    display: flex;\n    flex-flow: row nowrap;\n    padding: 0 3%;\n    position: absolute;\n    width: 100%;\n  }\n  .pd_four-cta-lo7fevej .pd_four-cta_button {\n    border: #fff solid 1px;\n    box-sizing: border-box;\n    color: #fff;\n    font-size: 10px;\n    font-weight: 600;\n    min-height: 24px;\n    padding: 6px 8px;\n    text-align: center;\n    text-transform: uppercase;\n    width: 48.5%;\n  }\n  .pd_four-cta-lo7fevej .pd_four-cta_button:not(:first-child) {\n    margin-left: 3%;\n  }\n  </style>\n  \n  \n  <div class=\"pd_four-cta-lo7fevej\">\n    <a href=\"/browse/category.do?cid=1127938#pageId=0&department=136&mlink=55276,PD_Tile\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n      <img id=\"PDImageTag\" src=\"https://gapprod.a.bigcontent.io/v1/static/20231116_FriendsgivingSale_USEC_40OffPurch_PD_1\" alt=\"Pre-Black Friday Sale 40% off Your Purchase Exclusions apply. Applied at checkout.\">\n    </a>\n    <div class=\"pd_four-cta--cta-container\">\n      <a href=\"/browse/category.do?cid=1127938#pageId=0&department=136&mlink=55276,PD_Tile\" class=\"pd_four-cta_button\">Women</a>\n      <a href=\"/browse/category.do?cid=1127944#pageId=0&department=75&mlink=55276,PD_Tile\" class=\"pd_four-cta_button\">Men</a>\n      <a href=\"/browse/category.do?cid=1127946#pageId=0&department=48&mlink=55276,PD_Tile\" class=\"pd_four-cta_button\">GIRLS</a>\n      <a href=\"/browse/category.do?cid=1127945#pageId=0&department=16&mlink=55276,PD_Tile\" class=\"pd_four-cta_button\">BOYS</a>\n    </div>\n  </div>\n  ", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "applied at checkout.", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "998939", "genericCode": ""}, "promoId": "lo7feql7"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n  .pd_four-cta-lo7fhrb3 {\n    background-color: #122344; /* default */\n    color: #fff; /* default */\n    height: 100%;\n    position: relative;\n    width: 100%;\n  }\n  .pd_four-cta-lo7fhrb3 img {\n    margin: 0 auto;\n    max-width: 100%;\n  }\n  .pd_four-cta-lo7fhrb3 .pd_four-cta--cta-container {\n    bottom: 4%;\n    box-sizing: border-box;\n    display: flex;\n    flex-flow: row nowrap;\n    padding: 0 3%;\n    position: absolute;\n    width: 100%;\n  }\n  .pd_four-cta-lo7fhrb3 .pd_four-cta_button {\n    border: #fff solid 1px;\n    box-sizing: border-box;\n    color: #fff;\n    font-size: 10px;\n    font-weight: 600;\n    min-height: 24px;\n    padding: 6px 8px;\n    text-align: center;\n    text-transform: uppercase;\n    width: 48.5%;\n  }\n  .pd_four-cta-lo7fhrb3 .pd_four-cta_button:not(:first-child) {\n    margin-left: 3%;\n  }\n  </style>\n  \n  \n  <div class=\"pd_four-cta-lo7fhrb3\">\n    <a href=\"/browse/category.do?cid=5664#pageId=0&department=136&mlink=55276,PD_Tile\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n      <img id=\"PDImageTag\" src=\"https://gapprod.a.bigcontent.io/v1/static/111623_PD2_USEC\" alt=\"50% Off Jeans Select Styles\">\n    </a>\n    <div class=\"pd_four-cta--cta-container\">\n      <a href=\"/browse/category.do?cid=5664#pageId=0&department=136&mlink=55276,PD_Tile\" class=\"pd_four-cta_button\">Women</a>\n      <a href=\"/browse/category.do?cid=6998#pageId=0&department=75&mlink=55276,PD_Tile\" class=\"pd_four-cta_button\">Men</a>\n      <a href=\"/browse/category.do?cid=6276#pageId=0&department=48&mlink=55276,PD_Tile\" class=\"pd_four-cta_button\">GIRLS</a>\n      <a href=\"/browse/category.do?cid=6189#pageId=0&department=16&mlink=55276,PD_Tile\" class=\"pd_four-cta_button\">BOYS</a>\n    </div>\n  </div>\n  ", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "998957", "genericCode": ""}, "promoId": "lo7fhlj2"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/browse/category.do?cid=8792#pageId=0&department=136&mlink=55276,PD_TILE_GGR_CARD_RET_PROMO\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" class=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"https://gapprod.a.bigcontent.io/v1/static/111623_PD03_US\" alt=\"Cardmember Exclusive Extra 10% Off With a Gap Inc Credit Card. Code GOOD Online & in stores. Exclusions apply. Ends 11/19.\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "tap", "overlay": "Applied at checkout", "defaultMessage": "tap to apply", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "998937", "genericCode": "GOOD"}, "promoId": "lo7fk3r6"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two-cta-loq0ttx7 {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_two-cta-loq0ttx7 img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two-cta-loq0ttx7 .pd_two-cta--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_two-cta-loq0ttx7 .pd_two-cta_button {\n  border: #fff solid 1px;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_two-cta-loq0ttx7 .pd_two-cta_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n\n<div class=\"pd_two-cta-loq0ttx7\">\n  <a href=\"https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSPD&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=55276,PD_TILE_GGR_CARD_ACQ\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"https://gapprod.a.bigcontent.io/v1/static/111623_PD004_US\" alt=\"extra 20% off your first purchase with your  new gap good rewards credit card\">\n  </a>\n  <div class=\"pd_two-cta--cta-container\">\n    <a href=\"https://www.gap.com/my-account/sign-in?creditOffer=barclays&sitecode=GPSSPD&retUrl=https://www.gap.com/customerService/info.do?cid=1099008&mlink=55276,PD_TILE_GGR_CARD_ACQ\" class=\"pd_two-cta_button\">APPLY NOW</a>\n    <br>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile4"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "combinable with today’s offers.", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "880925", "genericCode": ""}, "promoId": "lo7foivn"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two-cta-lohv86e6 {\n  background-color: #122344; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_two-cta-lohv86e6 img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two-cta-lohv86e6 .pd_two-cta--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_two-cta-lohv86e6 .pd_two-cta_button {\n  border: #fff solid 1px;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_two-cta-lohv86e6 .pd_two-cta_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n\n<div class=\"pd_two-cta-lohv86e6\">\n  <a href=\"/browse/category.do?cid=8792#pageId=0&department=136&mlink=55276,PD_Tile\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"https://gapprod.a.bigcontent.io/v1/static/111623_PD05_US\" alt=\"cardmember exclusive for every gap purchase made with your gap inc credit card, gap will donate $5 to DoSomething.org, up to $400k' ends 11/30\">\n  </a>\n  <div class=\"pd_two-cta--cta-container\">\n    <a href=\"/browse/category.do?cid=8792#pageId=0&department=136&mlink=55276,PD_Tile\" class=\"pd_two-cta_button\">SHOP NOW</a>\n    <br>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile5"}, "applicationDetails": {"type": "auto", "overlay": "Applied at checkout", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "1001517", "genericCode": ""}, "promoId": "lo7r2e6d"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "style": {"mobile": {"fontSize": ".75em !important", "text-transform": "none !important", "letter-spacing": "0px !important"}, "desktop": {"fontSize": ".75em !important", "text-transform": "none !important", "letter-spacing": "0px !important"}}, "text": "{--! headerText !--}"}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}"}}], "style": {"flex-direction": "column"}}}}}, {"instanceDesc": "10-23-23 WCD HP Promo Drawer CSS Modifications", "ciid": "", "name": "HTMLInjectionComponent", "type": "sitewide", "data": {"defaultHeight": {"small": "0", "large": "0"}, "html": "<style>#promo-drawer-button{background-color:#000}</style>"}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "My Offers", "iconAltText": "Open icon", "linkWithModalDisplayStyle": "none", "subHeaderText": "(5 available)"}, "closedState": {"headerText": "Pre-Black Friday Sale: 40% Off Your Purchase + More", "subHeaderText": "", "iconAltText": "Closed icon"}, "aria-label": "Pre-Black Friday Sale: 40% Off Your Purchase + More"}, "pd_id": "pdid_lo7f81f2"}}, "utilitylinks": {"type": "sitewide", "name": "UtilityLinks", "data": {"style": {"fontSize": "10.5px"}, "brandBarShortcutLinks": [{"link": "/stores?sitecode=GPSSSEARCH&mlink=39813,29693256,SEARCHBAR_STORELOCATOR", "text": "Find a store"}, {"link": "/customerService/info.do?cid=1099008&sitecode=GPSSSEARCH&mlink=39813,29666338,SEARCHBAR_GGR_ACQ", "text": "Gap Good Rewards"}, {"link": "/customerService/info.do?cid=2116&sitecode=GPSSSEARCH&mlink=39813,29693256,SEARCHBAR_GIFTCARD", "text": "Gift Card"}]}}, "hamnav": {"sitewide-topnav-ciid": "14596cc8-c1bf-4dc6-9e94-0cc32c39e7e1", "sitewide-hamnav-desc": "Hamnav 10-23-2023", "instanceName": "Hamnav-10-23-2023", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"lazy": false, "defaultHeight": {}, "isVisible": {"large": false, "small": true}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {}, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "desc": "saleToRed-CSS", "data": {"html": "<style> @media (max-width: 1023px) { .hamnav-item-list li[data-testid^='category-65'] a { color: red } } </style>", "style": {}, "classes": ""}}, {"type": "sitewide", "name": "HamburgerNav", "data": {"activeDivisions": ["1086624", "5643", "5063", "1137865", "1137867", "1137868", "1137869", "5997", {"cid": "1156863", "name": "Sale", "customStyles": {"button": {"div": {"color": "red"}}}}, "83061"]}}]}}}}, "search": {"instanceDesc": "Search config code is in the TopNav container", "type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Sweater", "Sweatpants", "<PERSON>igan", "<PERSON><PERSON><PERSON>", "Dresses", "<PERSON><PERSON><PERSON>", "Blazer", "Leggings"]}}, "logo": {"name": "Logo", "type": "sitewide", "altText": "Gap logo", "lightLogoImgPath": "/Asset_Archive/GPWeb/content/0030/015/725/assets/logo/logo_gap--light.svg", "darkLogoImgPath": "/Asset_Archive/GPWeb/content/0030/015/725/assets/logo/logo_gap--dark.svg", "logoImgPath": "/Asset_Archive/GPWeb/content/0028/669/369/assets/logo/Gap_logo_MOB_newV2.svg", "isSquare": true, "className": "gap-topnav-logo-dt"}}, "brand": "gap", "type": "meta", "pmcsEdgeCacheTag": "gap-homepage-en-us-prod"}