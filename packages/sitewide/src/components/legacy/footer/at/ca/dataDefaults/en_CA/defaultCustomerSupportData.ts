// @ts-nocheck
"use client";
import {DefaultCustomerSupportData} from "@sitewide/components/legacy/types"

const footerItem = "footer-item"

const defaultCustomerSupportData: DefaultCustomerSupportData = () => ({
  desktop: {
    columns: [
      {
        header: {
          text: "Customer Support",
          className: "",
        },
        links: [
          {
            type: "link",
            text: "Customer Service",
            to: "/customerService/info.do?cid=44959",
            className: "atfLinks",
            style: {},
          },
          {
            type: "link",
            text: "Shipping",
            to: "/customerService/info.do?cid=1180468",
            className: "atfLinks",
            style: {},
          },
          {
            type: "link",
            text: "Free Returns",
            to: "/customerService/info.do?cid=1180471",
            className: "atfLinks",
            style: {},
          },
          {
            type: "link",
            text: "Track Your Order",
            to: "https://secure-athleta.gapcanada.ca/my-account/order-lookup",
            className: "atfLinks",
            style: {},
          },
          {
            type: "link",
            text: "Gift Cards",
            to:
              "/browse/info.do?cid=52564&mlink=55287,********,Footer_GiftCards&clink=********",
            className: "atfLinks",
            style: {},
          },
          {
            type: "link",
            text: "Size & Fit Guides",
            to:
              "/customerService/info.do?cid=79315&cs=size_charts&mlink=55287,********,Footer_FitGuide&clink=********",
            className: "atfLinks",
            style: {},
          },
          {
            type: "link",
            text: "Site Map",
            to: "/products/index.jsp",
            className: "atfLinks",
            style: {},
          },
        ],
      },
      {
        header: {
          text: "About Us",
          className: "",
        },
        links: [
          {
            type: "link",
            text: "Our Values",
            to:
              "/browse/info.do?cid=1074427&mlink=55287,********,Footer_OurValues&clink=********",
            className: "atfLinks",
            style: {},
          },
          {
            type: "link",
            text: "Sustainability",
            to:
              "/browse/info.do?cid=1074427&mlink=55287,********,Footer_Sustainability&clink=********#BCorp",
            className: "atfLinks",
            style: {},
          },
          {
            type: "link",
            text: "Work at Athleta",
            to: "https://jobs.gapinc.com/athleta-home",
            className: "atfLinks",
            style: {},
          },
          {
            type: "link",
            text: "Gap Inc. Sustainability",
            to: "http://www.gapincsustainability.com/",
            className: "atfLinks",
            style: {},
          },
        ],
      },
      {
        header: {
          text: "Find Us",
          className: "",
        },
        links: [
          {
            type: "text",
            text: "877-328-4538",
            className: "",
            style: {
              fontWeight: "700",
              color: "#76787b",
            },
          },
          {
            type: "link",
            text: "Find a Store",
            to:
              "/customerService/storeLocator.do?mlink=55287,********,Footer_StoreLocator&clink=********",
            className: "atfLinks",
            style: {},
          },
          {
            type: "link",
            text: "Email Sign-Up",
            to:
              "/profile/info.do?cid=57480&mlink=55287,********,Footer_Email&clink=********",
            className: "atfLinks",
            style: {},
          },
        ],
      },
    ],
  },
  mobile: {
    links: [
      {
        type: "link",
        text: "Store Locator",
        to:
          "/customerService/storeLocator.do?mlink=55287,16845079,cs_footer_storelocator&clink=16845079",
        className: footerItem,
        target: "",
      },
      {
        type: "link",
        text: "CUSTOMER SERVICE",
        to: "/customerService/info.do?cid=44959",
        className: footerItem,
      },
      {
        type: "link",
        text: "ORDERS & RETURNS",
        to: "/customerService/info.do?cid=1180471",
        className: footerItem,
      },
      {
        type: "link",
        text: "SHIPPING & HANDLING",
        to: "/customerService/info.do?cid=1180468",
        className: footerItem,
      },
      {
        type: "accordion",
        text: "GIFT CARDS",
        accordionLinks: [
          {
            text: "BUY EGIFT CARDS",
            to: "https://athleta.cashstar.com/gift-card/buy/",
          },
          {
            text: "Buy Gift Cards",
            to:
              "/browse/product.do?pid=000126&cid=1005672&mlink=55287,********,Footer_GiftCards&clink=********",
          },
        ],
        className: footerItem,
      },
      {
        type: "link",
        text: "YOUR REWARDS & OFFERS",
        to:
          "/loyalty/customer-value?mlink=55287,16845079,Footer_ValueCenter&clink=16845079",
        className: footerItem,
      },
      {
        type: "link",
        text: "EMAIL SIGN-UP",
        to:
          "/profile/info.do?cid=57480&mlink=55287,********,Footer_Email&clink=********",
        className: footerItem,
      },
      {
        type: "accordion",
        text: "SHOP OUR OTHER BRANDS",
        accordionLinks: [
          {
            text: "GAP",
            to: "https://www.gapcanada.ca/?ssiteID=at",
            target: "_blank",
          },
          {
            text: "OLD NAVY",
            to: "https://oldnavy.gapcanada.ca/?ssiteID=at",
            target: "_blank",
          },
          {
            text: "BANANA REPUBLIC",
            to: "https://bananarepublic.gapcanada.ca/?ssiteID=at",
            target: "_blank",
          },
        ],
      },
    ],
  },
})

export default defaultCustomerSupportData
