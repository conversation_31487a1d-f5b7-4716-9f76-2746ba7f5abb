// @ts-nocheck
"use client";
import {useContext} from "react"
import {
  BreakpointContext,
  XLARGE,
  LARGE,
} from "@ecom-next/core/breakpoint-provider"
import {useInverseStyleContext} from "@ecom-next/core/react-stitch"
import classnames from "classnames"
import {CopyRightRow} from "./CopyRightRow"
import {LocaleSelector} from "../../LocaleSelector"
import {FooterCopyRightSection, FooterLegalWrapper} from "./styles"
import {CopyRightProps, CopyRightRows} from "./types"

export const updateCopyrightYear = (rows: CopyRightRows) => {
  const copyrightYear = /^©\s\d+/
  return rows.map((row) => {
    return row.map((item) => {
      if (copyrightYear.test(item.text)) {
        const currentCopyrightYear = item.text.replace(
          copyrightYear,
          `© ${new Date().getFullYear()}`
        )
        return {...item, text: currentCopyrightYear}
      }
      return item
    })
  })
}

export const CopyRight = ({
  className,
  containerClassName,
  containerId = "",
  defaultRows,
  shouldUseDarkTheme,
  shouldUseBR2024BackgroundColor,
  isATRedesign2024Enabled,
}: CopyRightProps): JSX.Element => {
  const {minWidth} = useContext(BreakpointContext)
  const isDesktop = minWidth(XLARGE)
  const isMobile = !minWidth(LARGE)
  const {invert} = useInverseStyleContext()
  const updatedCopyRightRows = updateCopyrightYear(defaultRows)

  const FooterLegalWrapperClassName = isATRedesign2024Enabled
    ? "footer-legal__wrapper"
    : "footer-legal__wrapper-ATRedesign"

  return (
    <FooterCopyRightSection
      className={containerClassName}
      data-testid="footer-copyright"
      id={containerId}
      isATRedesign2024Enabled={isATRedesign2024Enabled}
      isDesktop={isDesktop}
      isMobile={isMobile}
      shouldUseBR2024BackgroundColor={shouldUseBR2024BackgroundColor}
      shouldUseDarkTheme={shouldUseDarkTheme}
      shouldUseInvertedColors={invert}
    >
      <LocaleSelector />
      <FooterLegalWrapper
        className={classnames(FooterLegalWrapperClassName, className)}
        data-testid="footer-legal-wrapper"
        isATRedesign2024Enabled={isATRedesign2024Enabled}
        isDesktop={isDesktop}
      >
        {updatedCopyRightRows?.map((row, index) => {
          if (
            isATRedesign2024Enabled &&
            row.some((item) => item.text === "Download our App")
          ) {
            return null
          }
          return (
            <CopyRightRow
              // eslint-disable-next-line react/no-array-index-key
              key={`CopyRightRow${index}`}
              isATRedesign2024Enabled={isATRedesign2024Enabled}
              row={row}
            />
          )
        })}
      </FooterLegalWrapper>
    </FooterCopyRightSection>
  )
}
