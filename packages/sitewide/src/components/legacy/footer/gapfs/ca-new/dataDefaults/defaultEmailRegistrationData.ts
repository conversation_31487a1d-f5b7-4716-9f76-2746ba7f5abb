// @ts-nocheck
'use client';
import { Varian<PERSON>, <PERSON><PERSON>, Color } from '@ecom-next/core/legacy/fixed-button';

export default {
  disclaimerText: {
    data: {
      classes: 'offer-text legal',
      html: '<p><span class="asterisk">*</span>Valid for first-time registrants only &amp; applies to <span>reg. price items only.</span> <a onclick="return contentItemLink(this,\'\',\'CS_Footer_PrivacyPolicy\');" href="https://corporate.gapinc.com/en-us/consumer-privacy-policy">Privacy Policy</a></p><p>Yes! I would like to receive style news and exclusive offers from Gap Inc. and related companies and brands including Gap Inc. and Old Navy Inc., and Banana Republic Inc.</p><p>You can withdraw your consent at any time. For more details see our <a onclick="return contentItemLink(this,\'\',\'CS_Footer_PrivacyPolicy\');" href="https://corporate.gapinc.com/en-us/consumer-privacy-policy">Privacy Policy</a> or <a href="https://corporate.gapinc.com/en-us/consumer-privacy-policy?locale=en_US#contact" onclick="return contentItemLink(this,\'\',\'CS_Footer_ContactUs\');">Contact Us</a>.</p>',
    },
  },
  title: {
    data: {
      classes: 'footer-email-register__title-wrapper',
      html: '<span className="footer-email-register__with-break-large-widths">Sign up for email</span><span><span>&amp; get 25% off</span><span>*</span></span>',
    },
  },
  submitButtonOptions: {
    desktop: {
      className: 'signButton',
      variant: Variant.outline,
      size: Size.medium,
      fullWidth: false,
      crossBrand: false,
      color: Color.white,
    },
    mobile: {
      className: 'signButton',
      variant: Variant.outline,
      size: Size.medium,
      fullWidth: true,
      crossBrand: false,
      color: Color.white,
    },
  },
  submitButtonText: 'Join',
  emailPlaceholderText: 'Enter your email address',
  errorNotificationAriaLabel: 'Error',
  validationMessage: 'Please enter a valid email address',
  notificationsLocation: 'after-button',
};
