// @ts-nocheck
"use client";
import {CSSObject, useTheme} from "@ecom-next/core/react-stitch"
import * as React from "react"
import {FooterCarouselData} from "../FooterCarousel"
import {hideAtLargeStyle} from "../styles/helper"

interface ArrowProps {
  children?: React.ReactNode
  className?: string
  onClick?: React.MouseEventHandler
  style?: CSSObject
}

const ArrowButton = ({
  children,
  className,
  onClick,
  style,
}: ArrowProps): JSX.Element => {
  const {utilities} = useTheme()

  return (
    <button
      className={className}
      css={[utilities.unbuttonize, style]}
      onClick={onClick}
    >
      {children}
    </button>
  )
}

const PreviousArrow = (arrowButtonProps: ArrowProps): JSX.Element => (
  <ArrowButton {...arrowButtonProps}>
    <img
      alt="Previous"
      src="/Asset_Archive/ONWeb/content/0013/895/589/assets/050316_US_HPRefresh_T_carousel_NavLeft.png"
    />
  </ArrowButton>
)

const NextArrow = (arrowButtonProps: ArrowProps): JSX.Element => (
  <ArrowButton {...arrowButtonProps}>
    <img
      alt="Next"
      src="/Asset_Archive/ONWeb/content/0013/895/589/assets/050316_US_HPRefresh_T_carousel_NavRight.png"
    />
  </ArrowButton>
)

export const defaultFooterCarouselData: FooterCarouselData = {
  carouselConfig: {
    prevArrow: <PreviousArrow />,
    nextArrow: <NextArrow />,
    responsive: [
      {
        breakpoint: 767,
        settings: "unslick",
      },
    ],
    prevArrowAlt: "previous slide button",
    nextArrowAlt: "next slide button",
  },
  slides: [
    {
      type: "sitewide",
      name: "SVGOverlay",
      data: {
        containerStyle: {
          mobile: {
            padding: "0 0",
          },
          desktop: {
            padding: "0 0",
          },
        },
        background: {
          content: {
            smallImg:
              "/Asset_Archive/ONWeb/content/0014/326/008/assets/081717_US_GiftCards_hp_carousel_sm_image.jpg",
            largeImg:
              "/Asset_Archive/ONWeb/content/0014/326/008/assets/081717_US_GiftCards_hp_carousel_xl_image.jpg",
            altText: "Shop Gift Cards",
          },
        },
        svgoverlay: {
          smallImg:
            "/Asset_Archive/ONWeb/content/0014/326/008/assets/081717_US_GiftCards_hp_carousel_sm_text.svg",
          largeImg:
            "/Asset_Archive/ONWeb/content/0014/326/008/assets/081717_US_GiftCards_hp_carousel_xl_text.svg",
          altText: "Shop Gift Cards",
          link: {
            url:
              "/customerService/info.do?cid=35433&mlink=5151,1,footer_1_GC&clink=1",
            tid: "footer_1_GC",
          },
        },
        links: {
          verticalStacking: true,
          style: {
            mobile: {
              width: "100%",
              marginLeft: "0",
              marginRight: "0",
              backgroundColor: "#003764",
              hideAtLargeStyle,
            },
            desktop: {
              hideAtLargeStyle,
            },
          },
          content: [
            {
              tid: "footer_1_GC",
              url:
                "/customerService/info.do?cid=35433&mlink=5151,1,footer_1_GC&clink=1",
              text: "Give One Today",
            },
          ],
        },
      },
    },
    {
      type: "sitewide",
      name: "SVGOverlay",
      data: {
        containerStyle: {
          mobile: {
            marginTop: "2rem",
          },
          desktop: {
            padding: "0",
          },
        },
        background: {
          content: {
            smallImg:
              "/Asset_Archive/ONWeb/content/0015/123/286/assets/180403-002B_US_OutletBanner_hp_S.svg",
            largeImg:
              "/Asset_Archive/ONWeb/content/0014/100/796/assets/carousel/081717_US_Outlet_hp_carousel_XL_text.svg",
            altText: "ONWard",
          },
        },
        svgoverlay: {
          smallImg:
            "/Asset_Archive/ONWeb/content/0015/123/286/assets/180403-002B_US_OutletBanner_hp_S.svg",
          link: {
            url:
              "/customerService/info.do?cid=1075373&mlink=5151,1,HP_Outlet&clink=1",
            tid: "HP_Outlet",
          },
        },
        links: {
          verticalStacking: true,
          style: {
            mobile: {
              width: "100%",
              marginLeft: "0",
              marginRight: "0",
              backgroundColor: "#003764",
              hideAtLargeStyle,
            },
            desktop: {
              hideAtLargeStyle,
            },
          },
          content: [
            {
              tid: "HP_Outlet",
              url:
                "/customerService/info.do?cid=1075373&mlink=5151,1,HP_Outlet&clink=1",
              text: "Learn More",
            },
          ],
        },
      },
    },
  ],
}
