// @ts-nocheck
import { render, screen, fireEvent, RenderOptions, act } from "@sitewide/components/legacy/setupTests/test-helpers";
import {merge} from "lodash"
import {RewardsPopover} from "../RewardsPopover"
import {Brand} from "@sitewide/components/legacy/types"

const totalRewardValue = 21
const bouncebackText = "bounceback text"

const renderRewardsPopover = (options: RenderOptions = {}): void => {
  render(<RewardsPopover />, {
    ...options,
  })
}

const renderRewardsPopoverWithGapCashAndOffer = (
  options: RenderOptions = {}
): void => {
  renderRewardsPopover(
    merge(
      {
        personalizationData: {
          virtualValueInterrupterStatus: {
            bouncebackActive: true,
            bouncebackText,
            rewardsActive: true,
            totalRewardValue,
          },
        },
      },
      options
    )
  )
}

const queryRewardsValue = (): HTMLElement | null =>
  screen.queryByText(`$${totalRewardValue}`)

const queryRewardAndBounce = (): HTMLElement | null =>
  screen.queryByText(`You have Rewards & ${bouncebackText}!`)

const queryRewardsAndBounceLink = (): HTMLAnchorElement | null | undefined =>
  queryRewardAndBounce()?.closest("a")

describe("<RewardsPopover />", () => {
  beforeEach(() => {
    sessionStorage.clear()
  })

  it("should not show popover when bounceback is not active", () => {
    renderRewardsPopover({
      personalizationData: {
        virtualValueInterrupterStatus: {
          rewardsActive: true,
          bouncebackActive: false,
          bouncebackText,
          totalRewardValue,
        },
      },
    })

    expect(screen.queryByText("Redeem your Rewards!")).not.toBeInTheDocument()
    expect(queryRewardsValue()).not.toBeInTheDocument()
  })

  it("renders bounceback text correctly", () => {
    renderRewardsPopover({
      personalizationData: {
        virtualValueInterrupterStatus: {bouncebackActive: true, bouncebackText},
      },
    })

    expect(screen.getByText("You have bounceback text!")).toBeInTheDocument()
  })

  it("renders bounceback and rewards text correctly when user is not logged in", () => {
    renderRewardsPopover({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
          bouncebackText,
          rewardsActive: true,
          totalRewardValue,
        },
      },
    })

    const rewardsPopover = screen.getByText(
      `You have Rewards & ${bouncebackText}!`
    )

    expect(rewardsPopover).toBeInTheDocument()
    expect(queryRewardsValue()).not.toBeInTheDocument()
  })

  describe("should render a link redirecting to", () => {
    it("'/my-account/sign-in?targetURL=/loyalty/customer-value' when user is not logged in", () => {
      renderRewardsPopoverWithGapCashAndOffer()

      const rewardsPopoverLink = queryRewardsAndBounceLink()

      expect(rewardsPopoverLink).toHaveAttribute(
        "href",
        "/my-account/sign-in?targetURL=/loyalty/customer-value"
      )
    })

    it("'/loyalty/customer-value' when logged in user browses Athleta US", () => {
      renderRewardsPopoverWithGapCashAndOffer({
        appState: {
          brandName: Brand.Athleta,
          market: "us",
        },
        personalizationData: {
          isLoggedInUser: true,
        },
      })

      const rewardsPopoverLink = queryRewardsAndBounceLink()

      expect(rewardsPopoverLink).toHaveAttribute(
        "href",
        "/loyalty/customer-value"
      )
    })

    it("'/loyalty/customer-value' when user is from CA", () => {
      renderRewardsPopoverWithGapCashAndOffer({
        appState: {
          brandName: Brand.Gap,
          market: "ca",
        },
      })

      const rewardsPopoverLink = queryRewardsAndBounceLink()

      expect(rewardsPopoverLink).toHaveAttribute(
        "href",
        "/loyalty/customer-value"
      )
    })

    it("'/my-account/sign-in?targetURL=/loyalty/customer-value' when user is not logged and not recognized in a brand that has Gap Cash", () => {
      renderRewardsPopoverWithGapCashAndOffer({
        appState: {
          brandName: Brand.Gap,
          market: "us",
        },
      })

      const rewardsPopoverLink = queryRewardsAndBounceLink()

      expect(rewardsPopoverLink).toHaveAttribute(
        "href",
        "/my-account/sign-in?targetURL=/loyalty/customer-value"
      )
    })

    it("'/loyalty/customer-value?target=EarnAndRedeem' when user is logged in a brand that has Gap Cash", () => {
      renderRewardsPopoverWithGapCashAndOffer({
        appState: {
          brandName: Brand.Gap,
          market: "us",
        },
        personalizationData: {
          isLoggedInUser: true,
        },
      })

      const rewardsPopoverLink = queryRewardsAndBounceLink()

      expect(rewardsPopoverLink).toHaveAttribute(
        "href",
        "/loyalty/customer-value?target=EarnAndRedeem"
      )
    })

    it("'/loyalty/customer-value?target=EarnAndRedeem' when user is recognized in a brand that has Gap Cash", () => {
      renderRewardsPopoverWithGapCashAndOffer({
        appState: {
          brandName: Brand.Gap,
          market: "us",
        },
        personalizationData: {
          isLoggedInUser: false,
          isRecognizedUser: true,
        },
      })

      const rewardsPopoverLink = queryRewardsAndBounceLink()

      expect(rewardsPopoverLink).toHaveAttribute(
        "href",
        "/loyalty/customer-value?target=EarnAndRedeem"
      )
    })
  })

  it("renders bounceback and rewards text correctly when user is logged in", () => {
    renderRewardsPopover({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: true,
          bouncebackText,
          rewardsActive: true,
          totalRewardValue,
        },
        isLoggedInUser: true,
      },
    })

    const rewardsPopover = queryRewardAndBounce()
    const rewardsPopoverLink = queryRewardsAndBounceLink()
    const rewardsPopoverRewardsValue = queryRewardsValue()

    expect(rewardsPopover).toBeInTheDocument()
    expect(rewardsPopoverLink).toHaveAttribute(
      "href",
      "/loyalty/customer-value?target=EarnAndRedeem"
    )
    expect(rewardsPopoverRewardsValue).not.toBeInTheDocument()
  })

  it("has no content if there are no rewards or bouncebacks", () => {
    renderRewardsPopover({
      personalizationData: {
        virtualValueInterrupterStatus: {
          bouncebackActive: false,
          bouncebackText,
          rewardsActive: false,
          totalRewardValue,
        },
      },
    })

    expect(queryRewardsValue()).not.toBeInTheDocument()
  })

  it("dismisses the popover when dismiss button is clicked", async () => {
    renderRewardsPopover({
      personalizationData: {
        virtualValueInterrupterStatus: {
          rewardsActive: true,
          bouncebackActive: true,
          bouncebackText,
          totalRewardValue,
        },
      },
    })

    expect(queryRewardAndBounce()).toBeInTheDocument()
    await act(async () => { 

     fireEvent.click(screen.getByRole("button", {name: "close rewards popover"})); 

     })
    expect(queryRewardAndBounce()).not.toBeInTheDocument()
  })

  it("dismisses the popover when the popover content is clicked", async () => {
    renderRewardsPopover({
      personalizationData: {
        virtualValueInterrupterStatus: {
          rewardsActive: true,
          bouncebackActive: true,
          bouncebackText,
          totalRewardValue,
        },
      },
    })

    expect(queryRewardAndBounce()).toBeInTheDocument()
    await act(async () => { 

     fireEvent.click(
       screen.getByRole("link", {name: `You have Rewards & ${bouncebackText}!`})
     ); 

     })
    expect(queryRewardAndBounce()).not.toBeInTheDocument()
  })
})
