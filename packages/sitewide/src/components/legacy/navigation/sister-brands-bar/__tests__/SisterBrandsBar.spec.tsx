// @ts-nocheck
import {
  StitchInverseStyleProvider,
  StitchStyleProvider,
  Theme,
} from "@ecom-next/core/react-stitch"
import {XLARGE} from "@ecom-next/core/breakpoint-provider"

import { fireEvent, render, RenderOptions, RenderResult, screen, waitFor, act } from "@sitewide/components/legacy/setupTests/test-helpers";
import {
  Brand,
  BrandSiteData,
  ExperimentStatus,
  SitewideAppState,
} from "@sitewide/components/legacy/types"
import SisterBrandsBar from "../SisterBrandsBar"
import {brandSiteDataUS, brandSiteDataCA} from "../../fixtures/sisterSite"
import {FakeContentResolver} from "@sitewide/components/legacy/setupTests/withFakeContentProvider"
import {defaultUniversalBarData} from "../../../universal-bar/__fixtures__/defaultUniversalBarData"
import {
  experiments,
  features,
  FONT_COLOR_UPDATE_RGB,
} from "@sitewide/components/legacy/sitewide-constants"
import {ContentData} from "../../../content"
import {UniversalBarProvider} from "../../../universal-bar/UniversalBarContext"
import {Market} from "../../../../../types/app"
import {defaultContentData} from "@sitewide/components/legacy/fixtures/DefaultContentData"
import {LinkProps} from "../types"
import {addThemeToBrandStyles} from "../textBrandLinkStyles"

const brands: [Brand, string][] = [
  [Brand.Athleta, "Athleta"],
  [Brand.BananaRepublic, "Banana Republic"],
  [Brand.BananaRepublicFactoryStore, "Banana Republic Factory"],
  [Brand.Gap, "Gap"],
  [Brand.GapFactoryStore, "Gap Factory"],
  [Brand.OldNavy, "Old Navy"],
]

const brandsLogoFillColors: [Brand, {[kind: string]: string}][] = [
  [Brand.Athleta, {active: "#FFFFFF", inactive: "#000000"}],
  [Brand.BananaRepublic, {active: "#FFFFFF", inactive: "#000000"}],
  [Brand.BananaRepublicFactoryStore, {active: "#FFFFFF", inactive: "#000000"}],
  [Brand.Gap, {active: "#FFFFFF", inactive: "#000000"}],
  [Brand.GapFactoryStore, {active: "#FFFFFF", inactive: "#000000"}],
  [Brand.OldNavy, {active: "#FFFFFF", inactive: "#000000"}],
]

const themeMock = {
  color: {
    wh: "#FFFFFF",
    gray20: "#CCC",
    bk: "#000000",
  },
} as Theme

const getGapSisterBrand = (): HTMLElement => screen.getByLabelText("Gap")

const getBRSisterBrandSVG = (): HTMLElement =>
  screen.getByTestId("Banana-Republic-logo-svg")

const getBRSisterBrandLink = (): HTMLElement =>
  screen.getByTestId("BR-sister-bar-testid")

const getATSisterBrandLogoSymbol = (): HTMLElement =>
  screen.getByTestId("Athleta-logo-symbol-path")

const backgroundColor = "background-color"
const renderSisterBrandsBar = ({
  contentData = defaultContentData,
  options,
}: {
  contentData?: ContentData
  options?: RenderOptions
}): RenderResult => {
  const mergedOptions = {
    ...options,
    appState: {
      ...options?.appState,
      brandSiteData: {
        ...brandSiteDataUS,
        ...(options?.appState?.brandSiteData as SitewideAppState),
      },
    },
  }

  return render(
    <StitchStyleProvider
      brand={mergedOptions.appState?.brandName || Brand.Gap}
      enabledFeatures={mergedOptions.enabledFeatures}
    >
      <StitchInverseStyleProvider
        invert={!!mergedOptions.enabledFeatures?.[features.BR_COLORS_2023]}
      >
        <UniversalBarProvider>
          <div style={{backgroundColor: "#E9E8E3"}}>
            <SisterBrandsBar
              brandSiteData={mergedOptions.appState?.brandSiteData}
              data={{}}
            />
          </div>
        </UniversalBarProvider>
      </StitchInverseStyleProvider>
    </StitchStyleProvider>,
    {
      content: {
        contentData,
        ContentResolver: FakeContentResolver,
      },
      ...mergedOptions,
    }
  )
}

const renderSisterBrandsByMarket = (
  market: Market,
  options: RenderOptions = {},
  currentBrandCode: string,
  brandName?: Brand
): RenderResult => {
  const brandSiteData =
    market === "us"
      ? {...brandSiteDataUS, currentBrandCode}
      : {...brandSiteDataCA, currentBrandCode}
  const appState = {
    market,
    brandSiteData,
  }

  return renderSisterBrandsBar({
    options: {
      appState: {
        ...appState,
        ...(brandName && {brandName}),
      },
      ...options,
    },
  })
}

const renderSisterBrandsWithHeaderRedesign = ({
  brandCode = "0",
  flagEnabled = true,
  brandName = Brand.Gap,
}: {
  brandCode?: string
  flagEnabled?: boolean
  brandName?: Brand
}) => {
  const gidBrandSitesMock = {
    "34": {
      link: "http://fakeUrl.com:3000?ssiteID=GAPFS",
      unsecureUrl: "http://fakeUrl.com:3000",
      secureUrl: "https://secure.fakeUrl.com:3000",
      displayName: "gapfactory.com",
      brandDisplayName: "gapfactory.com",
      brandCode: "34",
      brandAbbr: "GAPFS",
    },
    "35": {
      link: "http://fakeUrl.com:3000?ssiteID=GAPFS",
      unsecureUrl: "http://fakeUrl.com:3000",
      secureUrl: "https://secure.fakeUrl.com:3000",
      displayName: "bananarepublicfactory.com",
      brandDisplayName: "bananarepublicfactory.com",
      brandCode: "35",
      brandAbbr: "BRFS",
    },
  }

  const isFactory =
    brandName === Brand.GapFactoryStore ||
    brandName === Brand.BananaRepublicFactoryStore

  renderSisterBrandsBar({
    options: {
      appState: {
        brandName,
        targetEnv: "local",
        brandSiteData: {
          ...(brandCode && {currentBrandCode: brandCode}),
          ...(isFactory && {gidBrandSites: gidBrandSitesMock}),
        },
      },
      enabledFeatures: {
        [features.SWF_TEXT_SISTER_BRANDS_BAR]: flagEnabled,
        [features.SWF_2024_HEADER_REDESIGN_GAP]: flagEnabled,
        [features.GAP_REDESIGN_2024]: flagEnabled,
      },
    },
  })
}

describe("<SisterBrandsBar />", () => {
  it.each(["string", "number"])(
    "sets active link if currentBrand is a %s",
    (currentBrandType) => {
      const currentBrandCode = currentBrandType === "string" ? "1" : 1
      renderSisterBrandsBar({
        options: {
          appState: {brandSiteData: {...brandSiteDataUS, currentBrandCode}},
        },
      })

      expect(screen.getByLabelText("Gap")).toHaveStyleRule(
        backgroundColor,
        "#FFFFFF"
      )
    }
  )

  describe.each(brands)("%s logo", (brand: Brand, brandLabel: string) => {
    const brandData = {
      brandAbbr: brand,
      brandCode: "1",
      displayName: brand,
      link: `${brand}.com`,
      unsecureUrl: `${brand}.com`,
      secureUrl: `${brand}.com`,
      brandDisplayName: brand,
    }
    describe("when NO props (from WCD content) are provided", () => {
      const mockBrandSiteData: BrandSiteData = {
        currentBrandCode: 0,
        currentBrandSiteId: "gap",
        gidBrandSites: {0: brandData},
      }

      it("renders aria-label correctly", () => {
        renderSisterBrandsBar({
          options: {
            appState: {brandSiteData: mockBrandSiteData},
          },
        })
        expect(screen.getByLabelText(brandLabel)).toBeInTheDocument()
      })

      it("renders link correctly", () => {
        renderSisterBrandsBar({
          options: {
            appState: {brandSiteData: mockBrandSiteData},
          },
        })
        expect(screen.getByLabelText(brandLabel)).toHaveAttribute(
          "href",
          brandData.link
        )
      })
    })
  })

  describe("when props (from WCD content) are provided", () => {
    const customLink = "www.linkfromprops.com"

    const brandSiteData = {
      ...brandSiteDataUS,
      gidBrandSites: {
        ...brandSiteDataUS.gidBrandSites,
        "1": {
          ...brandSiteDataUS.gidBrandSites["1"],
          link: customLink,
        },
      },
    }

    it("renders aria-label correctly", () => {
      renderSisterBrandsBar({
        options: {
          appState: {brandSiteData},
        },
      })
      expect(screen.getByLabelText("Gap")).toBeInTheDocument()
    })

    it("but content is empty, show deafault link ", () => {
      renderSisterBrandsBar({})
      expect(screen.getByLabelText("Gap")).toHaveAttribute(
        "href",
        brandSiteDataUS.gidBrandSites[1].link
      )
    })

    it("renders customLink from props", () => {
      renderSisterBrandsBar({
        options: {
          appState: {brandSiteData},
        },
      })
      expect(screen.getByLabelText("Gap")).toHaveAttribute("href", customLink)
    })
  })

  describe("Athleta Canada", () => {
    it("renders aria-label correctly", () => {
      renderSisterBrandsBar({
        options: {
          appState: {brandSiteData: brandSiteDataCA, market: "ca"},
        },
      })
      expect(screen.queryByLabelText(/athleta/i)).toBeInTheDocument()
    })

    it("renders link correctly", () => {
      renderSisterBrandsBar({
        options: {
          appState: {brandSiteData: brandSiteDataCA, market: "ca"},
        },
      })
      expect(screen.getByLabelText(/athleta/i)).toHaveAttribute(
        "href",
        "https://athleta.ca/?ssiteID=GAP"
      )
    })
  })

  describe("colorful universal bar experiment", () => {
    const expectDefaultActiveColor = (): void => {
      expect(screen.getByLabelText(/banana republic/i)).toHaveStyleRule(
        backgroundColor,
        "#FFFFFF"
      )
    }

    const expectDefaultHoverColor = (): void => {
      fireEvent.mouseOver(screen.getByLabelText(/gap/i))

      expect(screen.getByLabelText(/gap/i)).toHaveStyleRule(
        backgroundColor,
        "#333",
        {target: ":hover"}
      )
    }

    describe("when experiment is active", () => {
      describe("and the content is being passed", () => {
        beforeEach(() => {
          renderSisterBrandsBar({
            contentData: defaultUniversalBarData,
            options: {
              appState: {
                abSeg: {
                  [experiments.COLORFUL_UNIVERSAL_BAR]: ExperimentStatus.Active,
                },
              },
            },
          })
        })

        it("should match active background-color specified in the content json", () => {
          expect(screen.getByLabelText(/banana republic/i)).toHaveStyleRule(
            backgroundColor,
            "purple"
          )
        })

        it("should not match hover background-color when hovering an active brand", () => {
          fireEvent.mouseOver(screen.getByLabelText(/banana republic/i))

          expect(screen.getByLabelText(/banana republic/i)).not.toHaveStyleRule(
            backgroundColor,
            "purple",
            {
              target: ":hover",
            }
          )
        })

        it("should match hover background-color specified in the content json", () => {
          fireEvent.mouseOver(screen.getByLabelText(/gap/i))

          expect(screen.getByLabelText(/gap/i)).toHaveStyleRule(
            backgroundColor,
            "red",
            {target: ":hover"}
          )
        })
      })

      describe("and the content is missing", () => {
        beforeEach(() => {
          renderSisterBrandsBar({
            options: {
              appState: {
                abSeg: {
                  [experiments.COLORFUL_UNIVERSAL_BAR]: ExperimentStatus.Active,
                },
              },
            },
          })
        })

        it("should match default active background-color", () => {
          expectDefaultActiveColor()
        })

        it("should match default hover background-color", () => {
          expectDefaultHoverColor()
        })
      })
    })

    describe("when experiment is inactive", () => {
      describe("and the content is being passed", () => {
        beforeEach(() => {
          renderSisterBrandsBar({
            contentData: defaultUniversalBarData,
            options: {
              appState: {
                abSeg: {
                  [experiments.COLORFUL_UNIVERSAL_BAR]:
                    ExperimentStatus.Control,
                },
              },
            },
          })
        })

        it("should match default active background-color", () => {
          expectDefaultActiveColor()
        })

        it("should match default hover background-color", () => {
          expectDefaultHoverColor()
        })
      })
    })
  })

  describe("test featureFlag FIND_GAP_XX_FONT_COLOR_UPDATE", () => {
    describe.each(["us", "ca"])(
      "render sister brand on %p market",
      (parameter) => {
        const market = parameter as Market
        const currentBrandCode = market === "us" ? "1" : "7"

        it("should have GAP SisterBrand with color #2b2b2b when featureFlag is enabled and sisterlink is active", () => {
          renderSisterBrandsByMarket(
            market,
            {
              enabledFeatures: {
                [`find-gap-${market}-font-color-update`]: true,
              },
            },
            currentBrandCode
          )

          const gapLogo = getGapSisterBrand().querySelector("svg path")
          expect(gapLogo).toHaveAttribute("fill", FONT_COLOR_UPDATE_RGB)
        })

        it("should have GAP SisterBrand with color #002554 when featureFlag is disabled and sisterlink is active", () => {
          renderSisterBrandsByMarket(
            market,
            {
              enabledFeatures: {
                [`find-gap-${market}-font-color-update`]: false,
              },
            },
            currentBrandCode
          )

          const gapLogo = getGapSisterBrand().querySelector("svg path")
          expect(gapLogo).toHaveAttribute("fill", "#2B2B2B")
        })

        it("should have GAP SisterBrand with color #002554 when featureFlag is undefined and sisterlink is active", () => {
          renderSisterBrandsByMarket(market, {}, currentBrandCode)

          const gapLogo = getGapSisterBrand().querySelector("svg path")
          expect(gapLogo).toHaveAttribute("fill", "#2B2B2B")
        })

        it("should have GAP SisterBrand with color #2b2b2b when featureFlag is enabled and sisterlink is not active", () => {
          renderSisterBrandsByMarket(
            market,
            {
              enabledFeatures: {
                [`find-gap-${market}-font-color-update`]: true,
              },
            },
            "10"
          )

          const gapLogo = getGapSisterBrand().querySelector("svg path")
          expect(gapLogo).toHaveAttribute("fill", "#FFFFFF")
        })

        it("should have GAP SisterBrand with color #002554 when featureFlag is undefined and sisterlink is not active", () => {
          renderSisterBrandsByMarket(market, {}, "10")

          const gapLogo = getGapSisterBrand().querySelector("svg path")
          expect(gapLogo).toHaveAttribute("fill", "#FFFFFF")
        })
      }
    )
  })

  describe("test invert colors for BR using featureFlag SWF_INVERT_UNIVERSAL_BAR", () => {
    describe.each(brandsLogoFillColors)(
      "render sister brand %s logo with corect active/inactive color",
      (brand: Brand, fillColor: {[kind: string]: string}) => {
        it(`should have ${brand} SisterBrand with color ${fillColor.active} when featureFlag is enabled and sisterlink is active`, () => {
          renderSisterBrandsBar({
            options: {
              appState: {
                enabledFeatures: {
                  [features.SWF_INVERT_UNIVERSAL_BAR]: true,
                  [features.BR_COLORS_2023]: true,
                },
              },
            },
          })

          const gapLogo = getGapSisterBrand().querySelector("svg path")
          expect(gapLogo).toHaveAttribute("fill", fillColor.active)
        })
      }
    )

    it("should have BR SisterBrand with black fill color and BR is current brand when featureFlag is enabled", () => {
      renderSisterBrandsBar({
        options: {
          appState: {
            brandName: Brand.BananaRepublic,
          },
          enabledFeatures: {
            [features.SWF_INVERT_UNIVERSAL_BAR]: true,
            [features.BR_COLORS_2023]: true,
            [features.SWF_REMOVE_UNIVERSAL_BAR_SELECTED]: true,
          },
        },
      })
      const BRLogo = getBRSisterBrandSVG().querySelector("svg path")
      expect(BRLogo).toHaveAttribute("fill", "#000000")
    })

    it("should have BR SisterBrand background color undefined and is the current brand featureFlag is enabled", () => {
      renderSisterBrandsBar({
        options: {
          appState: {
            brandName: Brand.BananaRepublic,
          },
          enabledFeatures: {
            [features.SWF_INVERT_UNIVERSAL_BAR]: true,
            [features.BR_COLORS_2023]: true,
            [features.SWF_REMOVE_UNIVERSAL_BAR_SELECTED]: true,
          },
        },
      })

      const BRSisterBrandLink = getBRSisterBrandLink()
      const computedStyles = getComputedStyle(BRSisterBrandLink)
      expect(computedStyles.backgroundColor).toBeFalsy()
    })
  })

  describe(`feature flag ${features.AT_REDESIGN_2023}`, () => {
    describe.each(["us", "ca"])(
      "render athleta sister brand logo on %p market",
      (parameter) => {
        const market = parameter as Market
        const brandCodeForMarket = market === "us" ? "10" : "39"

        it.each`
          isFFEnabled  | isActive | expectedColor
          ${true}      | ${true}  | ${"#024A62"}
          ${false}     | ${true}  | ${"#666"}
          ${undefined} | ${true}  | ${"#666"}
          ${true}      | ${false} | ${"#FFFFFF"}
          ${false}     | ${false} | ${"#FFFFFF"}
          ${undefined} | ${false} | ${"#FFFFFF"}
        `(
          `logo symbol should render with correct color when ff is $isFFEnabled and sisterlink is $isActive`,
          ({isFFEnabled, isActive, expectedColor}) => {
            const enabledFeatures =
              isFFEnabled === undefined
                ? {}
                : {[features.AT_REDESIGN_2023]: isFFEnabled}
            const currentBrandCode = isActive ? brandCodeForMarket : "1"
            const brandName = Brand.Athleta
            renderSisterBrandsByMarket(
              market,
              {
                enabledFeatures,
              },
              currentBrandCode,
              brandName
            )

            const ATLogoSymbol = getATSisterBrandLogoSymbol()
            expect(ATLogoSymbol).toHaveAttribute("fill", expectedColor)
          }
        )
      }
    )
  })

  describe("when the brand is BRFS CA and it is a mobile breakpoint", () => {
    it("should hide the sister brand bar", () => {
      renderSisterBrandsBar({
        options: {
          appState: {
            brandName: Brand.BananaRepublicFactoryStore,
            locale: "en-CA",
          },
          breakpoint: XLARGE,
        },
      })

      expect(screen.getByTestId("sister-brands-bar-inner")).not.toBeVisible()
    })
  })

  describe("Header Redesign 2024", () => {
    describe(`When feature flag ${features.SWF_TEXT_SISTER_BRANDS_BAR} is active `, () => {
      it("Should render the new sister brand bar with text brands", () => {
        renderSisterBrandsWithHeaderRedesign({
          brandName: Brand.Gap,
        })

        expect(screen.getByText("Gap")).toBeInTheDocument()
      })

      describe("And sister brand bar have a specific organization", () => {
        it("Should render gap with correct order", () => {
          renderSisterBrandsWithHeaderRedesign({
            brandName: Brand.Gap,
          })

          const gapOption = screen.getByRole("link", {name: "Gap"})
          const gapfsOption = screen.getByRole("link", {name: "Gap Factory"})
          const onOption = screen.getByRole("link", {name: "Old Navy"})
          const brOption = screen.getByRole("link", {name: "Banana Republic"})
          const atOption = screen.getByRole("link", {name: "Athleta"})

          expect(gapOption).toHaveStyle({order: 1})
          expect(gapfsOption).toHaveStyle({order: 2})
          expect(onOption).toHaveStyle({order: 3})
          expect(brOption).toHaveStyle({order: 4})
          expect(atOption).toHaveStyle({order: 5})
        })
        it("Should render gapfs with correct order", () => {
          renderSisterBrandsWithHeaderRedesign({
            brandName: Brand.GapFactoryStore,
          })

          const gapOption = screen.getByRole("link", {name: "Gap"})
          const gapfsOption = screen.getByRole("link", {name: "Gap Factory"})
          const brfsOption = screen.getByRole("link", {
            name: "Banana Republic Factory",
          })

          expect(gapfsOption).toHaveStyle({order: 1})
          expect(gapOption).toHaveStyle({order: 2})
          expect(brfsOption).toHaveStyle({order: 3})
        })
        it("Should render brfs with correct order", () => {
          renderSisterBrandsWithHeaderRedesign({
            brandName: Brand.BananaRepublicFactoryStore,
          })

          const gapfsOption = screen.getByRole("link", {name: "Gap Factory"})
          const brfsOption = screen.getByRole("link", {
            name: "Banana Republic Factory",
          })

          expect(gapfsOption).toHaveStyle({order: 1})
          expect(brfsOption).toHaveStyle({order: 2})
        })

        it("Should render br with correct order", () => {
          renderSisterBrandsWithHeaderRedesign({
            brandName: Brand.BananaRepublic,
          })

          const gapOption = screen.getByRole("link", {name: "Gap"})
          const onOption = screen.getByRole("link", {name: "Old Navy"})
          const brOption = screen.getByRole("link", {name: "Banana Republic"})
          const atOption = screen.getByRole("link", {name: "Athleta"})

          expect(gapOption).toHaveStyle({order: 1})
          expect(onOption).toHaveStyle({order: 2})
          expect(brOption).toHaveStyle({order: 3})
          expect(atOption).toHaveStyle({order: 4})
        })
      })

      describe.each`
        brandName                           | brandFullName                | brandCode
        ${Brand.Gap}                        | ${"Gap"}                     | ${"1"}
        ${Brand.GapFactoryStore}            | ${"Gap Factory"}             | ${"34"}
        ${Brand.OldNavy}                    | ${"Old Navy"}                | ${"3"}
        ${Brand.BananaRepublic}             | ${"Banana Republic"}         | ${"2"}
        ${Brand.BananaRepublicFactoryStore} | ${"Banana Republic Factory"} | ${"35"}
        ${Brand.Athleta}                    | ${"Athleta"}                 | ${"10"}
      `(
        "when brand is $brandName",
        ({
          brandName,
          brandFullName,
          brandCode,
        }: {
          brandName: Brand
          brandFullName: string
          brandCode: string
        }) => {
          const fakeBrandCode = "12345"
          it(`Should the sister brand bar options follow the specific ${brandName} brand style.`, () => {
            renderSisterBrandsWithHeaderRedesign({
              brandName,
              brandCode: fakeBrandCode,
            })

            const brandStyle = addThemeToBrandStyles(brandName, themeMock)
            const brandOption = screen.getByRole("link", {name: brandFullName})

            expect(brandOption).toHaveStyle(brandStyle.specific)
          })
          it(`Should the sister brand bar active option follow the correct ${brandName} brand style.`, () => {
            renderSisterBrandsWithHeaderRedesign({
              brandName,
              brandCode,
            })

            const brandStyle = addThemeToBrandStyles(brandName, themeMock)
            const brandOption = screen.getByRole("link", {name: brandFullName})

            expect(brandOption).toHaveStyle(brandStyle.active)
          })
          it(`Should the sister brand bar hover option follow the correct ${brandName} brand style.`, () => {
            renderSisterBrandsWithHeaderRedesign({
              brandName,
              brandCode: fakeBrandCode,
            })

            const brandStyle = addThemeToBrandStyles(brandName, themeMock)
            const brandOption = screen.getByRole("link", {name: brandFullName})

            brandOption.focus()

            expect(brandOption).toHaveStyle(brandStyle.hover)
          })
        }
      )
    })
    describe(`When feature flag ${features.SWF_TEXT_SISTER_BRANDS_BAR} is disabled `, () => {
      it("should render the old sister brand bar with logo brands", () => {
        renderSisterBrandsWithHeaderRedesign({
          flagEnabled: false,
        })

        const brandLink = screen.getAllByTestId("sister-brands-link")[0]
        expect(brandLink.querySelector("svg")).toBeInTheDocument()
      })
    })
  })
})
