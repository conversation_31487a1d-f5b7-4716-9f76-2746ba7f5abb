// @ts-nocheck
import {enableFetchMocks} from "jest-fetch-mock"

import {ContentType} from "@sitewide/components/legacy/types"
import {
  addPromoDrawerPromotion,
  addValueDrawerPromotion,
  removePromotion,
} from "../promotionClient"
import {PromotionBrands} from "../types"

enableFetchMocks()
const fetchMocked = fetch
const fetchMock = fetchMocked as typeof fetchMocked & {
  mockResponseOnce: (props?: unknown, options?: unknown) => void
  resetMocks: () => void
}
const props = {
  addPageActionCallback: jest.fn(),
  brand: PromotionBrands.gap,
  market: "US",
  promotionCode: "promo-1",
  shoppingBagApi: "https://api.gap.com/commerce/shopping-bags",
  shopperId: "sid123",
}
const promoDrawerProps = {
  genericCode: "promo-1",
  genericCodeId: "2423",
  addPageActionCallback: jest.fn(),
  brand: PromotionBrands.gap,
  market: "US",
  promoDrawerApi: "https://api.gap.com/commerce/shopping-bags",
  useNewPromoDrawerApi: true,
  shopperId: "sid123",
  contentType: "ecom" as ContentType,
}
const defaultPromoDrawerBody = {
  method: "put",
  credentials: "include",
  headers: {
    Accept: "application/json",
    "Content-Type": "application/json",
    shoppingBagId: "sid123",
  },
  referrer: "origin-when-cross-origin",
  body: JSON.stringify({
    promoCodesMap: {
      "promo-1": "2423",
    },
    promoCodes: ["promo-1"],
  }),
}
const expectedUri = `${promoDrawerProps.promoDrawerApi}/commerce/shopping-bags/promo-codes?brand=${promoDrawerProps.brand}&market=${promoDrawerProps.market}&channel=WEB`

describe("promotionClient", () => {
  beforeEach(() => fetchMock.resetMocks())

  it("should call addValueDrawerPromotionRequest with success", async () => {
    fetchMock.mockResponseOnce(JSON.stringify({}))

    await addValueDrawerPromotion(props)

    expect(fetchMock.mock.calls[0][0]).toEqual(
      `${props.shoppingBagApi}/${props.shopperId}/promo-codes?brand=${props.brand}&market=${props.market}&channel=WEB`
    )
    expect(props.addPageActionCallback).toBeCalled()
  })
  it("should call the correct endpoint when the FF is enabled for the current environment with the correct request params and body", async () => {
    fetchMock.mockResponseOnce(JSON.stringify({}))

    await addPromoDrawerPromotion(promoDrawerProps)
    expect(fetchMock).toBeCalledWith(expectedUri, {
      ...defaultPromoDrawerBody,
    })
    expect(promoDrawerProps.addPageActionCallback).toBeCalled()
  })

  it("should use the correct headers when the FF is enabled and contentType is wip", async () => {
    fetchMock.mockResponseOnce(JSON.stringify({}))

    await addPromoDrawerPromotion({...promoDrawerProps, contentType: "wip"})
    expect(fetchMock).toBeCalledWith(expectedUri, {
      ...defaultPromoDrawerBody,
      headers: {
        ...defaultPromoDrawerBody.headers,
        previewType: "WIP",
      },
    })
    expect(promoDrawerProps.addPageActionCallback).toBeCalled()
  })

  it("should use the correct headers when the FF is enabled and contentType is app", async () => {
    fetchMock.mockResponseOnce(JSON.stringify({}))

    await addPromoDrawerPromotion({...promoDrawerProps, contentType: "app"})
    expect(fetchMock).toBeCalledWith(expectedUri, {
      ...defaultPromoDrawerBody,
      headers: {
        ...defaultPromoDrawerBody.headers,
        previewType: "APP",
      },
    })
    expect(promoDrawerProps.addPageActionCallback).toBeCalled()
  })
  it("should call addValueDrawerPromotionRequest and throw error", async () => {
    fetchMock.mockResponseOnce(JSON.stringify({}), {status: 500})

    await expect(addValueDrawerPromotion(props)).rejects.toThrow(
      "Unable to reach promo service, Status: 500, URI:https://api.gap.com/commerce/shopping-bags/sid123/promo-codes?brand=GP&market=US&channel=WEB"
    )
  })

  it("should call removePromotionRequest with success", async () => {
    fetchMock.mockResponseOnce()

    await removePromotion(props)

    expect(fetchMock.mock.calls[0][0]).toEqual(
      `${props.shoppingBagApi}/${props.shopperId}/promo-codes/${props.promotionCode}?brand=${props.brand}&market=${props.market}&channel=WEB`
    )
  })

  it("should call removePromotionRequest and throw error", async () => {
    fetchMock.mockResponseOnce(null, {status: 500})

    await expect(removePromotion(props)).rejects.toThrow(
      "Unable to reach promo service, Status: 500, URI:https://api.gap.com/commerce/shopping-bags/sid123/promo-codes/promo-1?brand=GP&market=US&channel=WEB"
    )
  })
})
