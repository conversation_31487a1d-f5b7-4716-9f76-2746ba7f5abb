// @ts-nocheck
"use client";
import {useContext} from "react"
import {
  forBrands,
  styled,
  useInverseStyleContext,
} from "@ecom-next/core/react-stitch"
import {LoadingPlaceholder} from "@ecom-next/core/legacy/loading-placeholder"
import {BreakpointContext, LARGE} from "@ecom-next/core/breakpoint-provider"
import MinibagBottomButtons from "./MinibagBottomButtons"
import {useShoppingBagUrl} from "../../hooks/useShoppingBagUrl"
import {
  BR_2023_BACKGROUND_COLOR,
  BR_2023_SEPARATOR_COLOR,
  BR_2024_HEADER_COLOR,
  features,
} from "@sitewide/components/legacy/sitewide-constants"
import {
  StyledComponentWithInvert,
  StyledLoadingPlaceholderProps,
} from "../types"
import {useFeature} from "@sitewide/components/legacy/sitewide-constants"

const LoadingPlaceholderWrapper = styled.div<StyledComponentWithInvert>(
  ({invert = false, theme}) => {
    const defaultStyles = {
      background: theme.crossBrand.color.g5,
      display: "flex",
      flexDirection: "column",
      justifyContent: "space-between",
      height: "100%",
      overflow: "hidden",
    }
    const BRStyles = invert && {background: BR_2023_SEPARATOR_COLOR}
    const brandStyles = forBrands(theme, {
      br: BRStyles,
      brfs: BRStyles,
    })
    return {...defaultStyles, ...brandStyles}
  }
)

const PlaceholderContentWrapper = styled.div<StyledLoadingPlaceholderProps>(
  ({width, invert = false, theme}) => {
    const defaultStyles = {
      background: theme.crossBrand.color.g5,
      display: "flex",
      flexDirection: "column",
      width: `${width}px`,
      gap: "16px",
    }
    const BRStyles = invert && {background: BR_2023_SEPARATOR_COLOR}
    const brandStyles = forBrands(theme, {
      br: BRStyles,
      brfs: BRStyles,
    })
    return {...defaultStyles, ...brandStyles}
  }
)

const PlaceholderFooterWrapper = styled.div<StyledLoadingPlaceholderProps>(
  ({width, invert = false, theme, shouldUseBR2024BackgroundColor}) => {
    const defaultStyles = {
      background: theme.crossBrand.color.wh,
      display: "flex",
      flexDirection: "column",
      width: `${width}px`,
      gap: "16px",
      position: "sticky",
      bottom: "0",
    }
    const invertedBrStyles = invert && {
      backgroundColor: BR_2023_BACKGROUND_COLOR,
    }
    const br2024Styles = shouldUseBR2024BackgroundColor && {
      backgroundColor: BR_2024_HEADER_COLOR,
    }
    const brandStyles = forBrands(theme, {
      br: br2024Styles || invertedBrStyles,
      brfs: invertedBrStyles,
    })
    return {...defaultStyles, ...brandStyles}
  }
)

const MinibagContentsContainer = styled.div<StyledLoadingPlaceholderProps>(
  ({width, invert = false, theme, shouldUseBR2024BackgroundColor}) => {
    const defaultStyles = {
      background: theme.crossBrand.color.wh,
      display: "flex",
      flexDirection: "column",
      width: `${width}px`,
      height: "324px",
      padding: "16px 16px 0px 16px",
      gap: "16px",
    }
    const invertedBrStyles = invert && {
      backgroundColor: BR_2023_BACKGROUND_COLOR,
    }
    const br2024Styles = shouldUseBR2024BackgroundColor && {
      backgroundColor: BR_2024_HEADER_COLOR,
    }
    const brandStyles = forBrands(theme, {
      br: br2024Styles || invertedBrStyles,
      brfs: invertedBrStyles,
    })
    return {...defaultStyles, ...brandStyles}
  }
)

const ProductCardContainer = styled.div<StyledLoadingPlaceholderProps>(
  ({width, invert = false, theme, shouldUseBR2024BackgroundColor}) => {
    const defaultStyles = {
      background: theme.crossBrand.color.wh,
      display: "flex",
      flexDirection: "row",
      width: `${width}px`,
      height: "127px",
      gap: "8px",
    }
    const invertedBrStyles = invert && {
      backgroundColor: BR_2023_BACKGROUND_COLOR,
    }
    const br2024Styles = shouldUseBR2024BackgroundColor && {
      backgroundColor: BR_2024_HEADER_COLOR,
    }
    const brandStyles = forBrands(theme, {
      br: br2024Styles || invertedBrStyles,
      brfs: invertedBrStyles,
    })
    return {...defaultStyles, ...brandStyles}
  }
)

const MinibagOrderSummaryContainer = styled.div<StyledLoadingPlaceholderProps>(
  ({width, invert = false, theme, shouldUseBR2024BackgroundColor}) => {
    const defaultStyles = {
      background: theme.crossBrand.color.wh,
      display: "flex",
      flexDirection: "column",
      alignItems: "flex-start",
      width: `${width}px`,
      height: "110px",
      padding: "16px 16px 0px 16px",
      gap: "16px",
    }
    const invertedBrStyles = invert && {
      backgroundColor: BR_2023_BACKGROUND_COLOR,
    }
    const br2024Styles = shouldUseBR2024BackgroundColor && {
      backgroundColor: BR_2024_HEADER_COLOR,
    }
    const brandStyles = forBrands(theme, {
      br: br2024Styles || invertedBrStyles,
      brfs: invertedBrStyles,
    })
    return {...defaultStyles, ...brandStyles}
  }
)

const ProductImagePlaceholder = styled.div<StyledComponentWithInvert>(
  ({invert = false, theme}) => {
    const defaultStyles = {
      background: theme.crossBrand.color.g5,
      display: "flex",
      flexDirection: "column",
      width: "80px",
      height: "107px",
      margin: "10px 0px 10px 10px",
    }
    const BRStyles = invert && {background: BR_2023_SEPARATOR_COLOR}
    const brandStyles = forBrands(theme, {
      br: BRStyles,
      brfs: BRStyles,
    })
    return {...defaultStyles, ...brandStyles}
  }
)

const ProductTilesWrapper = styled.div<StyledLoadingPlaceholderProps>(
  ({width, invert = false, theme, shouldUseBR2024BackgroundColor}) => {
    const defaultStyles = {
      background: theme.crossBrand.color.wh,
      display: "flex",
      flexDirection: "column",
      alignItems: "flex-start",
      margin: "10px 10px 10px 0px",
      height: "107px",
      width: `${width}px`,
      gap: "8px",
    }
    const invertedBrStyles = invert && {
      backgroundColor: BR_2023_BACKGROUND_COLOR,
    }
    const br2024Styles = shouldUseBR2024BackgroundColor && {
      backgroundColor: BR_2024_HEADER_COLOR,
    }
    const brandStyles = forBrands(theme, {
      br: br2024Styles || invertedBrStyles,
      brfs: invertedBrStyles,
    })
    return {...defaultStyles, ...brandStyles}
  }
)

const RectangleTile = styled.div<StyledLoadingPlaceholderProps>(
  ({width, invert = false, theme}) => {
    const defaultStyles = {
      background: theme.crossBrand.color.g5,
      display: "flex",
      flexDirection: "column",
      width: `${width}px`,
      height: "20px",
    }
    const BRStyles = invert && {background: BR_2023_SEPARATOR_COLOR}
    const brandStyles = forBrands(theme, {
      br: BRStyles,
      brfs: BRStyles,
    })
    return {...defaultStyles, ...brandStyles}
  }
)

const getRatio = (width: number) => {
  return {width: width / 20, height: 1}
}

const ProductCardPlaceholder = ({
  width,
  invert,
  shouldUseBR2024BackgroundColor,
}: StyledLoadingPlaceholderProps): JSX.Element => {
  const tilesWrapperWidth = width - 110
  const mediumRectangleTileWidth = tilesWrapperWidth
  const smallRectangleTileWidth = mediumRectangleTileWidth / 2

  return (
    <ProductCardContainer
      data-testid="product-card-container"
      invert={invert}
      shouldUseBR2024BackgroundColor={shouldUseBR2024BackgroundColor}
      width={width}
    >
      <ProductImagePlaceholder invert={invert}>
        <LoadingPlaceholder ratio={{width: 1, height: 1.36}} />
      </ProductImagePlaceholder>
      <ProductTilesWrapper
        data-testid="product-tiles-wrapper"
        invert={invert}
        shouldUseBR2024BackgroundColor={shouldUseBR2024BackgroundColor}
        width={tilesWrapperWidth}
      >
        <RectangleTile invert={invert} width={mediumRectangleTileWidth}>
          <LoadingPlaceholder ratio={getRatio(mediumRectangleTileWidth)} />
        </RectangleTile>
        <RectangleTile invert={invert} width={mediumRectangleTileWidth}>
          <LoadingPlaceholder ratio={getRatio(mediumRectangleTileWidth)} />
        </RectangleTile>
        <RectangleTile invert={invert} width={mediumRectangleTileWidth}>
          <LoadingPlaceholder ratio={getRatio(mediumRectangleTileWidth)} />
        </RectangleTile>
        <RectangleTile invert={invert} width={smallRectangleTileWidth}>
          <LoadingPlaceholder ratio={getRatio(smallRectangleTileWidth)} />
        </RectangleTile>
      </ProductTilesWrapper>
    </ProductCardContainer>
  )
}

const BagContentsPlaceholder = ({
  width,
  invert = false,
  shouldUseBR2024BackgroundColor,
}: StyledLoadingPlaceholderProps): JSX.Element => {
  const largeRectangleTileWidth = width - 32
  const productCardContainerWidth = width - 32

  return (
    <MinibagContentsContainer
      data-testid="minibag-contents-container"
      invert={invert}
      shouldUseBR2024BackgroundColor={shouldUseBR2024BackgroundColor}
      width={width}
    >
      <RectangleTile invert={invert} width={largeRectangleTileWidth}>
        <LoadingPlaceholder ratio={getRatio(largeRectangleTileWidth)} />
      </RectangleTile>
      <ProductCardPlaceholder
        invert={invert}
        shouldUseBR2024BackgroundColor={shouldUseBR2024BackgroundColor}
        width={productCardContainerWidth}
      />
      <ProductCardPlaceholder
        invert={invert}
        shouldUseBR2024BackgroundColor={shouldUseBR2024BackgroundColor}
        width={productCardContainerWidth}
      />
    </MinibagContentsContainer>
  )
}

const OrderSummaryPlaceholder = ({
  width,
  invert,
  shouldUseBR2024BackgroundColor,
}: StyledLoadingPlaceholderProps): JSX.Element => {
  const largeRectangleTileWidth = width - 32
  const mediumRectangleTileWidth = width - 142

  return (
    <MinibagOrderSummaryContainer
      data-testid="minibag-order-summary-container"
      invert={invert}
      shouldUseBR2024BackgroundColor={shouldUseBR2024BackgroundColor}
      width={width}
    >
      <RectangleTile invert={invert} width={largeRectangleTileWidth}>
        <LoadingPlaceholder ratio={getRatio(largeRectangleTileWidth)} />
      </RectangleTile>
      <RectangleTile invert={invert} width={largeRectangleTileWidth}>
        <LoadingPlaceholder ratio={getRatio(largeRectangleTileWidth)} />
      </RectangleTile>
      <RectangleTile invert={invert} width={mediumRectangleTileWidth}>
        <LoadingPlaceholder ratio={getRatio(mediumRectangleTileWidth)} />
      </RectangleTile>
    </MinibagOrderSummaryContainer>
  )
}

const PlaceholderFooter = ({
  width,
  invert,
  shouldUseBR2024BackgroundColor,
}: StyledLoadingPlaceholderProps): JSX.Element => {
  const shoppingBagUrl = useShoppingBagUrl()
  const largeRectangleTileWidth = width - 32

  return (
    <PlaceholderFooterWrapper
      data-testid="placeholder-footer-wrapper"
      invert={invert}
      shouldUseBR2024BackgroundColor={shouldUseBR2024BackgroundColor}
      width={width}
    >
      <MinibagBottomButtons shoppingBagUrl={shoppingBagUrl}>
        <RectangleTile invert={invert} width={largeRectangleTileWidth}>
          <LoadingPlaceholder ratio={getRatio(largeRectangleTileWidth)} />
        </RectangleTile>
      </MinibagBottomButtons>
    </PlaceholderFooterWrapper>
  )
}

export const MinibagLoadingPlaceholder = (): JSX.Element => {
  const {minWidth} = useContext(BreakpointContext)
  const isDesktop = minWidth(LARGE)
  const viewPortTotalWidth = window.innerWidth
  const mobilePlaceholderWidth = viewPortTotalWidth * 0.9
  const placeholderWidth = isDesktop ? 384 : mobilePlaceholderWidth
  const {invert} = useInverseStyleContext()

  const shouldUseBRWhiteBackground = useFeature(features.BR_WHITE_BACKGROUND)
  const shouldUseSitewideRedesignBR2024BackgroundColor = useFeature(
    features.SWF_BR_REDESIGN_2024
  )
  const shouldUseBR2024BackgroundColor =
    shouldUseBRWhiteBackground || shouldUseSitewideRedesignBR2024BackgroundColor

  return (
    <LoadingPlaceholderWrapper
      data-testid="loading-minibag-placeholder"
      invert={invert}
    >
      <PlaceholderContentWrapper invert={invert} width={placeholderWidth}>
        <BagContentsPlaceholder
          invert={invert}
          shouldUseBR2024BackgroundColor={shouldUseBR2024BackgroundColor}
          width={placeholderWidth}
        />
        <OrderSummaryPlaceholder
          invert={invert}
          shouldUseBR2024BackgroundColor={shouldUseBR2024BackgroundColor}
          width={placeholderWidth}
        />
      </PlaceholderContentWrapper>
      <PlaceholderFooter
        invert={invert}
        shouldUseBR2024BackgroundColor={shouldUseBR2024BackgroundColor}
        width={placeholderWidth}
      />
    </LoadingPlaceholderWrapper>
  )
}
