// @ts-nocheck
import type {
  AppsFlyerPlaceholderConstants,
  SitewideHeaderRedesignConstants,
} from "../types/appState"

export const UNIVERSAL_BAR_HEIGHT_VALUE = 40
export const UNIVERSAL_BAR_DESKTOP_HEIGHT_VALUE_REDESIGN_2024 = 46
export const UNIVERSAL_BAR_HEIGHT = `${UNIVERSAL_BAR_HEIGHT_VALUE}px`
export const UNIVERSAL_BAR_DESKTOP_HEIGHT_REDESIGN_2024 = `${UNIVERSAL_BAR_DESKTOP_HEIGHT_VALUE_REDESIGN_2024}px`

export const SITEWIDE_CERTONA_LOAD_PAGE_ACTION =
  "SitewideCertonaScriptLoadSuccess"

export const layering = {
  MINI_BAG: 10,
  BRAND_BAR: 400,
  UNIVERSAL_BAR: 402,
  PROMO_DRAWER: 700,
  PROMO_DRAWER_MOTION: 701,
  STICKY_CONTAINER_ZINDEX: 400,
  VALUE_DRAWER: 700,
  VALUE_DRAWER_BUTTON: 701,
}

export const ENGLISH_CA_LOCALE = "en_CA"
export const FRENCH_CA_LOCALE = "fr_CA"

export const appsFlyerPlaceholderFlags: AppsFlyerPlaceholderConstants = {
  GAP_APPSFLYER_PLACEHOLDER: "gap_appsflyer_placeholder",
  BR_APPSFLYER_PLACEHOLDER: "br_appsflyer_placeholder",
  ON_APPSFLYER_PLACEHOLDER: "on_appsflyer_placeholder",
  AT_APPSFLYER_PLACEHOLDER: "at_appsflyer_placeholder",
  GAPFS_APPSFLYER_PLACEHOLDER: "gapfs_appsflyer_placeholder",
  BRFS_APPSFLYER_PLACEHOLDER: "brfs_appsflyer_placeholder",
}
export const sitewideHeaderRedesign2024Flags: SitewideHeaderRedesignConstants = {
  SWF_2024_HEADER_REDESIGN_AT: "swf-2024-header-redesign-at",
  SWF_2024_HEADER_REDESIGN_BR: "swf-2024-header-redesign-br",
  SWF_2024_HEADER_REDESIGN_BRFS: "swf-2024-header-redesign-brfs",
  SWF_2024_HEADER_REDESIGN_GAP: "swf-2024-header-redesign-gap",
  SWF_2024_HEADER_REDESIGN_GAPFS: "swf-2024-header-redesign-gapfs",
  SWF_2024_HEADER_REDESIGN_ON: "swf-2024-header-redesign-on",
}

export const features = {
  APPSFLYER_SMART_BANNER: "appsflyer-banner",
  ACQUISITION_SNACKBAR: "acquisition-snackbar",
  AUTOSUGGEST_PRODUCT_SEARCH_API: "autosuggest-product-search",
  AT_REDESIGN_2023: "at-redesign-2023",
  SWF_AT_REDESIGN_2024: "swf-at-redesign-2024",
  BARCLAYS_PROGRAM_FOOTER_531: "acquisition-footer-links",
  BR_COLORS_2023: "br-colors-2023",
  CANADA_LOYALTY_UPDATE: "canada-loyalty-updates",
  CATEGORY_USE_ISM: "cat-ism",
  CATEGORY_USE_PMCS: "cat-pmcs",
  CMS_SESSION: "cms-session",
  DISPLAY_CONTENT_ERROR: "display-content-error",
  EMAIL_PAYLOAD_UPDATE: "email-payload-update",
  ENABLE_SISTER_BRANDS_LOGS: "enable-sister-brands-logs",
  EXPOSED_MOBILE_SEARCH: "exposed-mobile-search",
  FIND_GAP_CA_FONT_COLOR_UPDATE: "find-gap-ca-font-color-update",
  FIND_GAP_US_FONT_COLOR_UPDATE: "find-gap-us-font-color-update",
  FIND_HAMNAV_SUBCATS: "find-hamnav-subcats",
  GRAY_UNIVERSAL_BAR: "gray-universal-bar",
  GAPFS_NEWLOGO_2024: "gapfs-newlogo-2024",
  HEADER_SCROLL_ON_PDP: "sitewide-header-scroll-on-pdp",
  JSCRAMBLER_FIRST: "jscrambler-first",
  LONG_LIVED_SIGN_IN_SITEWIDE: "long-lived-sign-in-sitewide",
  LAZY_PROMO_DRAWER: "lazy-promo-drawer",
  MINI_BAG: "sw-mini-bag",
  PARALLEL_LOAD_WITH_TEALIUM: "parallel-load-with-tealium",
  PDP_REDESIGN_2022: "pdp-redesign-2022",
  PILL_NAVIGATION_ATHLETA_HOME: "at-home23",
  PILL_NAVIGATION: "23",
  SHOPPING_API_UPDATE: "shopping-api-update",
  SITEWIDE_CLIENT_FETCH_NAV: "sitewide-client-fetch-nav",
  SW_DISABLE_PMCS_CACHE: "sw-disable-pmcs-cache",
  SW_NEW_STICKY_MANAGER: "sw-new-sticky-manager",
  SW_SITECODE_ORDER_CHANGE: "sw-sitecode-order-change",
  SW_USE_NEW_CACHE: "sw-use-new-cache",
  SWF_AUTOFIRESCROLL_DISABLE_COLAPSESCROLL:
    "swf-autofirescroll-disable-colapsecroll",
  SWF_CONTEXTUAL_HEADING: "swf-contextual-heading",
  SWF_DRAWER_PHASE_ONE: "swf-drawer-phase-one",
  SWF_INVERT_UNIVERSAL_BAR: "swf-invert-universal-bar",
  SWF_USE_PMCS_QUERY_PARAMS_FROM_COOKIES:
    "swf-use-pmcs-query-params-from-cookies",
  SWF_USE_COOKIE_PACKAGE: "swf-use-cookie-package",
  SWF_USE_COREUI_CORE_PROVIDERS: "swf-use-coreui-core-providers",
  SWF_USE_COREUI_DOUBLE_PROVIDERS: "swf-use-coreui-double-providers",
  SWF_USE_COREUI_CORE_FIXED_BUTTON: "swf-use-coreui-core-fixed-button",
  SWF_USE_POINTS_AND_REWARDS_ABSOLUTE_PATHS:
    "swf-use-points-and-rewards-absolute-paths",
  SWF_NEW_PROMODRAWER: "swf-new-promodrawer",
  SWF_NEW_RELIC_2023: "swf-new-relic-2023",
  SWF_DISABLE_NR_FEATURES_LIST: "swf-disable-nr-features-list",
  SWF_ENABLE_FULL_PMCS_LOGGING: "swf-enable-full-pmcs-logging",
  SWF_PRELOAD_TEALIUM: "swf-preload-tealium",
  SWF_PREVIEW_INVENTORY_AWARE: "swf-preview-inventory-aware",
  SWF_PROMO_DRAWER_EVENTS: "swf-promo-drawer-events",
  SWF_VIEWPORT_CONTENTDATA_FILTER: "swf-viewport-contentdata-filter",
  SWF_REMOTE_VERSION: "swf-remote-version",
  SWF_STATE_CLEANUP: "swf-state-cleanup",
  SWF_SIMPLIFIED_FOOTER: "swf-simplified-footer",
  SWF_REMOVE_UNIVERSAL_BAR_SELECTED: "swf-remove-universal-bar-selected",
  SWF_NEW_REGISTRATION_ENDPOINTS: "swf-new-registration-endpoints",
  SWF_FLIP_HEADER_CONFIG_PRECEDENCE: "swf-flip-header-config-precedence",
  SWF_FIXED_WIDTH_HP_CONTAINER: "swf-fixed-width-hp-container",
  SWF_LOAD_CERTONA_LOCALLY: "swf-load-certona-locally",
  TEALIUM_ADOBE_UID: "tealium-adobe-uid",
  TEALIUM_PRIORITY_LOAD_CORE: "tealium-priority-load-core",
  TERTIARY_FONT: "tertiary-font",
  US_COMMUNITY_DROPDOWN: "us-community-dropdown",
  UTILITY_USE_PMCS: "utility-pmcs",
  VALUE_DRAWER: "value-drawer",
  SAFARI_RELOAD_TEST: "safari-reload-test",
  REACT_17: "react-17",
  SWF_BR_REDESIGN_2024: "swf-br-redesign-2024",
  BR_WHITE_BACKGROUND: "br-white-background",
  SWF_HIDE_TERMS_AND_CONDITIONS: "swf-hide-terms-and-conditions",
  SWF_PREVIEW_DATE_FROM_COOKIE: "swf-preview-date-from-cookie",
  SWF_SINGLE_OPTIMIZELY_SNIPPET: "swf-single-optimizely-snippet",
  SWF_APPSFLYER_PLACEHOLDER: "swf-appsflyer-placeholder",
  SWF_DISABLE_RECAPTCHA: "swf-disable-recaptcha",
  SWF_LOAD_OPTLY_FROM_CDN: "swf-load-optly-from-cdn",
  SWF_CERTONA_GAP_HOST: "swf-certona-gap-host",
  GAP_REDESIGN_2024: "gap-redesign-2024",
  SWF_TEXT_SISTER_BRANDS_BAR: "swf-text-sister-brands-bar",
  SWF_2024_HEADER_REDESIGN_EDFS: "swf-2024-header-redesign-edfs",
  SWF_IOS_SAFARI_APPSFLYER_ATTRIBUTION: "swf-ios-safari-appsflyer-attribution",
  SWF_HUI_REWRITE_SISTER_BRANDS_BAR: "swf-hui-rewrite-sister-brands-bar",
  ...appsFlyerPlaceholderFlags,
  ...sitewideHeaderRedesign2024Flags,
}

export const experiments = {
  COLORFUL_UNIVERSAL_BAR: "xb55",
  LLSI_EXPERIMENT: "xb187",
  ENABLE_SERVICE_WORKER: 52,
  EXPOSED_MOBILE_SEARCH: 30,
  CATEGORY_USE_PMCS_AMPLIENCE: "56",
  CATEGORY_USE_ISM: "53",
  UTILITY_USE_PMCS_AMPLIENCE: "110",
  MINIBAG_EXPERIMENT: "xb165",
  MINIBAG_REDIRECT_SHOPPINGBAG_PAGE: "xb184",
  SWE_DRAWER_PHASE_ONE: "171",
  AUTOSUGGEST_PRODUCT_SEARCH_EXPERIMENT: 174,
  SWE_NAV_DATA_CLIENT_SIDE: "xb183",
  SWE_LOAD_CERTONA_LOCALLY: "xb196",
  SWE_2024_HEADER_REDESIGN: "xb215",
}

// these are the test compain numbers required for using PMCS content across MFEs
// @see {@link https://confluence.gapinc.com/pages/viewpage.action?spaceKey=TnLCOE&title=Server+Side+Test+Campaign+Name+List}
export const pmcsTestCampaignNumbers = [
  experiments.CATEGORY_USE_ISM,
  experiments.CATEGORY_USE_PMCS_AMPLIENCE,
  experiments.UTILITY_USE_PMCS_AMPLIENCE,
]

// these are the feature flags required for using PMCS content across MFEs
export const pmcsFeatureFlags = [
  features.CATEGORY_USE_ISM,
  features.CATEGORY_USE_PMCS,
  features.UTILITY_USE_PMCS,
]

/**
 * remote container names.
 */
export const remoteNames = {
  MCS: "mcs",
  Minibag: "minibag",
}

export const SITEWIDE_CACHE_KEY = "sitewide"

export const httpResStatusCodes = {
  HTTP_STATUS_OK: 200,
  HTTP_STATUS_NOT_FOUND: 404,
}

export const FONT_COLOR_UPDATE_RGB = "#2b2b2b"

export const tealiumEvents = {
  BARCLAYS_CLICK_HEADLINE: "barclays_click_headline",
  BARCLAYS_CLICK_ROVER: "barclays_click_rover",
  BARCLAYS_CLICK_EDFS: "barclays_click_edfs",
  BARCLAYS_INTERACTION: "Barclays-Enrollment",
  BARCLAYS_VIEW_SNACKBAR_SUCCESS: "barclays_snackbar_status_cardsuccess",
  BARCLAYS_VIEW_SNACKBAR_TEMP: "barclays_snackbar_status_cardsuccesstemp",
  BARCLAYS_CLICK_MODAL: "barclays_click_modal",
}

export const NEW_RELIC_PAGE_ACTION = {
  HAMNAV: {
    NAV_SERVICE_RESPONSE_WITH_DATA: "hamnavNavServiceResponseWithData",
    NAV_SERVICE__RESPONSE_ZERO_ITEMS: "hamnavNavServiceResponseWithZeroItems",
    NAV_SERVICE__FALLBACK: "hamnavNavServiceFallback",
    WCD_RESPONSE_SUCCESS: "hamnavWCDResponseSuccess",
    FIND_WCD_ENABLED_FEATURES: "findWCDHamnavEnabledFeatures",
    WCD_RESPONSE_ERROR: "hamnavWCDResponseWithZeroPayload",
  },
}

export const CLIENT_ID_PRESCREEN_CAPTURE = "SITEWIDE"

export const BR_2024_HEADER_COLOR = "#FFF"
export const BR_2024_FOOTER_COLOR = "#F2F1EE"

export const ONE_DAY_IN_MS = 86400000
export const ONE_HOUR_IN_MS = 3600000
export const FOURTEEN_DAYS_IN_MS = 1209600000
export const BR_2023_BACKGROUND_COLOR = "#F6F4EB"
export const BR_2023_FONT_COLOR = "#000"
export const BR_2023_SEPARATOR_COLOR = "#E9E8E3"
export const BR_2023_SHADOW_COLOR = "#B9B7AF"
export const BR_2023_HEADER_GRADIENT_BLACK =
  "linear-gradient(to bottom, rgba(0, 0, 0, 0.38) 0%, rgba(0, 0, 0, 0.280037) 70.83%, rgba(0, 0, 0, 0.123233) 87.5%, rgba(0, 0, 0, 0) 100%)"
export const BR_2023_HEADER_GRADIENT_WHITE =
  "linear-gradient(to bottom, rgba(255,255,255, 0.38) 0%, rgba(255,255,255, 0.280037) 70.83%, rgba(255,255,255, 0.123233) 87.5%, rgba(255,255,255, 0) 100%)"
export const MINIBAG_CROSSBRAND_FONT =
  "'Source Sans Pro', Helvetica, Arial, Roboto, sans-serif"
export const pageTypes = {
  CATEGORY: "category",
  CUSTOM_LANGING_PAGE: "custom-landing-page",
  DIVISION: "division",
  HOME: "home",
  SITEWIDE: "sitewide",
  PRODUCT: "product",
  PROFILE: "profile",
  SEARCH: "search",
  SHOPPING_BAG: "ShoppingBag",
  STORE_SERVICE: "store-service",
  UTILITY: "utility",
}

export const APPSFLYER_PLACEHOLDER_ID = "appsflyer-placeholder"
export const APPSFLYER_DISMISS_COOKIE_ID = "afdis"
