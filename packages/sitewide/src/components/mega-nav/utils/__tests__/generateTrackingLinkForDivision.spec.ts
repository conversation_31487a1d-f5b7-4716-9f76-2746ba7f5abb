import { generateTrackingLinkForDivision } from '../../utils';
import { DivisionProps } from '../../types';

describe('generateTrackingLinkForDivision', () => {
  describe('when division has a url with a query param', () => {
    it('should append tracking info without question mark', () => {
      const division = {
        link: '/somefake?query=string',
        name: 'fakeDivision',
      } as DivisionProps;
      expect(generateTrackingLinkForDivision(division)).toBe('/somefake?query=string&nav=meganav%3AfakeDivision%3A%3A');
    });
  });

  describe('when division has a url with no query params', () => {
    it('should append tracking info with a question mark', () => {
      const division = {
        link: '/fakeDivisionURL',
        name: 'fakeDivision',
      } as DivisionProps;
      expect(generateTrackingLinkForDivision(division)).toBe('/fakeDivisionURL?&nav=meganav%3AfakeDivision%3A%3A');
    });
  });

  describe('when division has a url with query params and hash', () => {
    it('should append tracking info without question mark before hash', () => {
      const division = {
        link: '/fakeDivisionURL?before=hash#after=hash',
        name: 'fakeDivision',
      } as DivisionProps;
      expect(generateTrackingLinkForDivision(division)).toBe('/fakeDivisionURL?before=hash&nav=meganav%3AfakeDivision%3A%3A#after=hash');
    });
  });

  describe('when division has a url with hash and no query params', () => {
    it('should append tracking info with question mark before hash', () => {
      const division = {
        link: '/fakeDivisionURL#after=hash',
        name: 'fakeDivision',
      } as DivisionProps;
      expect(generateTrackingLinkForDivision(division)).toBe('/fakeDivisionURL?&nav=meganav%3AfakeDivision%3A%3A#after=hash');
    });
  });

  it("should return '#' if no url is provided", () => {
    const division = {
      divisionId: ['123'],
    } as DivisionProps;
    expect(generateTrackingLinkForDivision(division)).toBe('#');
  });
});
