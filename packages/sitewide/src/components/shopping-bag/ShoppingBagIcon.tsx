'use client';
import Beacon from '@ecom-next/sitewide/beacon';
import { useShoppingBagBeacon } from '../legacy/shopping-bag/hooks/useShoppingBagBeacon';
interface ShoppingBagIconProps {
  totalItemCount: number;
}
export const ShoppingBagDesktopIcon = ({ totalItemCount }: ShoppingBagIconProps): JSX.Element => {
  const { shouldRenderBeacon, animationDuration, animationIterationCount } = useShoppingBagBeacon(totalItemCount);
  const bagFillRule = totalItemCount < 1 ? 'evenodd' : 'inherit';
  const brandMarketSpecificBeaconStyles = {
    animationIterationCount: animationIterationCount,
    animationDuration: animationDuration,
  };

  return (
    <>
      {shouldRenderBeacon && <Beacon device='desktop' style={brandMarketSpecificBeaconStyles} />}
      <svg
        data-testid='shopping-bag-icon-desktop'
        height='27'
        viewBox='0 0 20 27'
        width='20'
        xmlns='http://www.w3.org/2000/svg'
        stroke='#FFF'
        className={'sw_shopping-bag__icon'}
      >
        <g>
          <path
            clipRule='evenodd'
            d='M15.5 18.521V33.5455H30.5V18.521H15.5ZM15 17.0664C14.4477 17.0664 14 17.5006 14 18.0361V34.0304C14 34.5659 14.4477 35.0001 15 35.0001H31C31.5523 35.0001 32 34.5659 32 34.0304V18.0361C32 17.5006 31.5523 17.0664 31 17.0664H15Z'
            data-testid='shopping-bag-icon-desktop-bag-path'
            strokeWidth={0}
            fillRule={bagFillRule}
            id='Rectangle 3 (Stroke)'
            transform='translate(-13,-10)'
          />
          <path
            clipRule='evenodd'
            d='M17.4473 16.2107C17.4473 13.3071 19.9602 11 22.9997 11C26.0391 11 28.5521 13.3071 28.5521 16.2107V20.6941C28.5521 21.0958 28.2163 21.4214 27.8021 21.4214C27.3879 21.4214 27.0521 21.0958 27.0521 20.6941V16.2107C27.0521 14.162 25.2648 12.4545 22.9997 12.4545C20.7345 12.4545 18.9473 14.162 18.9473 16.2107V20.6941C18.9473 21.0958 18.6115 21.4214 18.1973 21.4214C17.7831 21.4214 17.4473 21.0958 17.4473 20.6941V16.2107Z'
            data-testid='shopping-bag-icon-desktop-handle-path'
            fillRule={bagFillRule}
            strokeWidth={0}
            id='Path (Stroke)'
            transform='translate(-13,-10)'
          />
        </g>
      </svg>
    </>
  );
};
export const ShoppingBagMobileIcon = ({ totalItemCount }: ShoppingBagIconProps): JSX.Element => {
  const { shouldRenderBeacon, animationDuration, animationIterationCount } = useShoppingBagBeacon(totalItemCount);
  const brandMarketSpecificBeaconStyles = {
    animationIterationCount: animationIterationCount,
    animationDuration: animationDuration,
  };

  return (
    <>
      {shouldRenderBeacon && <Beacon data-testid='mobile-shopping-bag-beaco n' device='mobile' style={brandMarketSpecificBeaconStyles} />}
      <svg
        data-testid='shopping-bag-icon-mobile'
        fill='none'
        height='25'
        viewBox='0 0 18 25'
        width='18'
        xmlns='http://www.w3.org/2000/svg'
        className='sw_mobile_shopping-bag__icon'
      >
        <g id='Bag Icon'>
          <g id='Bag'>
            <path
              id='Rectangle 3 (Stroke)'
              fillRule='evenodd'
              clipRule='evenodd'
              d='M1.5 8.00586V23.5H16.5V8.00586H1.5ZM1 6.50586C0.447715 6.50586 0 6.95358 0 7.50586V24C0 24.5522 0.447716 25 1 25H17C17.5523 25 18 24.5522 18 24V7.50586C18 6.95357 17.5523 6.50586 17 6.50586H1Z'
            />
            <path
              id='Path (Stroke)'
              fillRule='evenodd'
              clipRule='evenodd'
              d='M3.44684 5.62352C3.44684 2.62923 5.95982 0.25 8.99924 0.25C12.0387 0.25 14.5516 2.62923 14.5516 5.62352V9.24705C14.5516 9.66126 14.2159 9.99705 13.8016 9.99705C13.3874 9.99705 13.0516 9.66126 13.0516 9.24705V5.62352C13.0516 3.51081 11.2644 1.75 8.99924 1.75C6.73407 1.75 4.94684 3.51081 4.94684 5.62352V9.24705C4.94684 9.66126 4.61105 9.99705 4.19684 9.99705C3.78262 9.99705 3.44684 9.66126 3.44684 9.24705V5.62352Z'
            />
          </g>
        </g>
      </svg>
    </>
  );
};
