import { Locale } from '@ecom-next/utils/server';

const AT_TRANSLATIONS = {
  'en-US': {
    translation: {
      'global.signIn': 'Sign In',
      'global.signOut': 'Sign Out',
      'myAccount.settings': 'Account',
      'myAccount.joinText': 'Get free shipping on all orders $50+, earn points, and more! It’s free!',
      'myAccount.joinRewards': 'Join Athleta <PERSON>',
    },
  },
  'en-CA': {
    translation: {
      'global.signIn': 'Sign In',
      'global.signOut': 'Sign Out',
      'myAccount.settings': 'Account',
      'myAccount.joinText': 'Get free shipping on all orders $50+, earn points, and more! It’s free!',
      'myAccount.joinRewards': 'Join Athleta Rewards',
    },
  },
  'fr-CA': {
    translation: {
      'global.signIn': 'Ouvrir une session',
      'global.signOut': 'Fermer la session',
      'myAccount.settings': 'Mon compte',
      'myAccount.joinText': 'Gagnez des points sur chaque achat, accédez à la livraison gratuite et plus encore!',
      'myAccount.joinRewards': 'Inscrivez-vous aux Récompenses Athleta',
    },
  },
};

const BR_TRANSLATIONS = {
  'en-US': {
    translation: {
      'global.signIn': 'Sign In',
      'global.signOut': 'Sign Out',
      'myAccount.settings': 'Account',
      'myAccount.joinText': 'Get free shipping on all orders $50+, earn points, and more! It’s free!',
      'myAccount.joinRewards': 'Join Banana Republic Rewards',
    },
  },
  'en-CA': {
    translation: {
      'global.signIn': 'Sign In',
      'global.signOut': 'Sign Out',
      'myAccount.settings': 'Account',
      'myAccount.joinText': 'Get free shipping on all orders $50+, earn points, and more! It’s free!',
      'myAccount.joinRewards': 'Join Banana Republic Rewards',
    },
  },
  'fr-CA': {
    translation: {
      'global.signIn': 'Ouvrir une session',
      'global.signOut': 'Fermer la session',
      'myAccount.settings': 'Mon compte',
      'myAccount.joinText': 'Gagnez des points sur chaque achat, accédez à la livraison gratuite et plus encore!',
      'myAccount.joinRewards': 'Inscrivez-vous aux Récompenses Banana Republic',
    },
  },
};

const GAP_TRANSLATIONS = {
  'en-US': {
    translation: {
      'global.signIn': 'Sign In',
      'global.signOut': 'Sign Out',
      'myAccount.settings': 'Account',
      'myAccount.joinText': 'Get free shipping on all orders $50+, earn points, and more! It’s free!',
      'myAccount.joinRewards': 'Join Gap Good Rewards',
    },
  },
  'en-CA': {
    translation: {
      'global.signIn': 'Sign In',
      'global.signOut': 'Sign Out',
      'myAccount.settings': 'Account',
      'myAccount.joinText': 'Get free shipping on all orders $50+, earn points, and more! It’s free!',
      'myAccount.joinRewards': 'Join Gap Good Rewards',
    },
  },
  'fr-CA': {
    translation: {
      'global.signIn': 'Ouvrir une session',
      'global.signOut': 'Fermer la session',
      'myAccount.settings': 'Mon compte',
      'myAccount.joinText': 'Gagnez des points sur chaque achat, accédez à la livraison gratuite et plus encore!',
      'myAccount.joinRewards': 'Inscrivez-vous aux Récompenses Gap Good',
    },
  },
};

const ON_TRANSLATIONS = {
  'en-US': {
    translation: {
      'global.signIn': 'Sign In',
      'global.signOut': 'Sign Out',
      'myAccount.settings': 'Account',
      'myAccount.joinText': 'Get free shipping on all orders $50+, earn points, and more! It’s free!',
      'myAccount.joinRewards': 'Join Navyist Rewards',
    },
  },
  'en-CA': {
    translation: {
      'global.signIn': 'Sign In',
      'global.signOut': 'Sign Out',
      'myAccount.settings': 'Account',
      'myAccount.joinText': 'Get free shipping on all orders $50+, earn points, and more! It’s free!',
      'myAccount.joinRewards': 'Join Navyist Rewards',
    },
  },
  'fr-CA': {
    translation: {
      'global.signIn': 'Ouvrir une session',
      'global.signOut': 'Fermer la session',
      'myAccount.settings': 'Mon compte',
      'myAccount.joinText': 'Gagnez des points sur chaque achat, accédez à la livraison gratuite et plus encore!',
      'myAccount.joinRewards': 'Inscrivez-vous aux Récompenses Navyist',
    },
  },
};

export const LOCALIZATION_DATA = {
  at: {
    en_US: {
      locale: 'en_US' as Locale,
      translations: AT_TRANSLATIONS,
    },
    en_CA: {
      locale: 'en_CA' as Locale,
      translations: AT_TRANSLATIONS,
    },
    fr_CA: {
      locale: 'fr_CA' as Locale,
      translations: AT_TRANSLATIONS,
    },
  },
  br: {
    en_US: {
      locale: 'en_US' as Locale,
      translations: BR_TRANSLATIONS,
    },
    en_CA: {
      locale: 'en_CA' as Locale,
      translations: BR_TRANSLATIONS,
    },
    fr_CA: {
      locale: 'fr_CA' as Locale,
      translations: BR_TRANSLATIONS,
    },
  },
  brfs: {
    en_US: {
      locale: 'en_US' as Locale,
      translations: BR_TRANSLATIONS,
    },
    en_CA: {
      locale: 'en_CA' as Locale,
      translations: BR_TRANSLATIONS,
    },
    fr_CA: {
      locale: 'fr_CA' as Locale,
      translations: BR_TRANSLATIONS,
    },
  },
  gap: {
    en_US: {
      locale: 'en_US' as Locale,
      translations: GAP_TRANSLATIONS,
    },
    en_CA: {
      locale: 'en_CA' as Locale,
      translations: GAP_TRANSLATIONS,
    },
    fr_CA: {
      locale: 'fr_CA' as Locale,
      translations: GAP_TRANSLATIONS,
    },
  },
  gapfs: {
    en_US: {
      locale: 'en_US' as Locale,
      translations: GAP_TRANSLATIONS,
    },
    en_CA: {
      locale: 'en_CA' as Locale,
      translations: GAP_TRANSLATIONS,
    },
    fr_CA: {
      locale: 'fr_CA' as Locale,
      translations: GAP_TRANSLATIONS,
    },
  },
  on: {
    en_US: {
      locale: 'en_US' as Locale,
      translations: ON_TRANSLATIONS,
    },
    en_CA: {
      locale: 'en_CA' as Locale,
      translations: ON_TRANSLATIONS,
    },
    fr_CA: {
      locale: 'fr_CA' as Locale,
      translations: ON_TRANSLATIONS,
    },
  },
};
