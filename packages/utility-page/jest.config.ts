import nextJest from 'next/jest';
import baseConfig from '../../jest.config.base';

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './src',
});

// Add any custom config to be passed to Jest
const customJestConfig = {
  ...baseConfig,
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  testEnvironment: 'jest-environment-jsdom',
  globals: {
    IS_DEV: true,
  },
  moduleNameMapper: {
    '@ecom-next/core/legacy/link': ['<rootDir>../core/src/components/migration/link'],
    '@mui/(.*)': ['<rootDir>../marketing-ui/src/$1'],
    '@core/(.*)': ['<rootDir>../core/src/$1'],
    '@sitewide/(.*)': ['<rootDir>/../sitewide/src/$1'],
    '^test-utils$': ['@ecom-next/core/test/test-helper'],
  },
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
export default createJestConfig(customJestConfig);
