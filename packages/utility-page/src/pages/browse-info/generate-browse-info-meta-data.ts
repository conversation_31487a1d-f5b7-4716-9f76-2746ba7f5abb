import { type ResolvingMetadata } from 'next';
import { type PageParams } from '@ecom-next/sitewide/page-wrapper';
import { getPageContext } from '@ecom-next/utils/server';
import { fetchMarketing, type SeoContent } from '@ecom-next/marketing-ui/fetch';
import { getSitewideMeta } from '@ecom-next/sitewide/meta';
import { ICAPIResponse } from '../../utils/types';

import getCid from '../../utils/get-cid';
import generateBaseMetaData from '../../utils/generate-base-meta-data';

const DEFAULT_PATH = '/browse/info.do';

const generateBrowseInfoMetaData = async (props: PageParams, parent: ResolvingMetadata) => {
  const { brand, market, targetEnv } = getPageContext();
  let { contentApi = '', previewDate = '' } = getPageContext();
  const { searchParams } = props;
  contentApi = searchParams['contentApi'] || contentApi;
  previewDate = searchParams['previewDate'] || previewDate;
  const cid = getCid(searchParams);

  const [parentMeta, marketing, sitewideMeta] = await Promise.all([
    parent,
    fetchMarketing(cid, 'utility', brand, contentApi as string, previewDate as string),
    getSitewideMeta({ brand, market, pageType: 'utility', searchParams, env: targetEnv }),
  ]);
  const content = marketing[`${cid}/customhtml`]?.contentItems?.[0] as SeoContent | undefined;

  return {
    ...parentMeta,
    ...generateBaseMetaData(content, cid, DEFAULT_PATH, false, {} as ICAPIResponse),
    ...sitewideMeta,
  };
};

export default generateBrowseInfoMetaData;
