'use client';
/** @jsxImportSource react */

import { useEffect } from 'react';
import { preload } from 'react-dom';

export default function ClientEvent() {
  [
    'https://cdn.c1.amplience.net/c/gapprod/jquery-371v2-min',
    '/Asset_Archive/AllBrands/lib/wcdLib.min.js',
    '/Asset_Archive/ONWeb/content/0012/847/530/assets/slick.js',
  ].forEach(resourceUrl => {
    preload(resourceUrl, { as: 'script' });
  });

  useEffect(() => {
    const hydrateEvent = new Event('next:hydrated');
    document.dispatchEvent(hydrateEvent);
  }, []);

  return null;
}
