/* eslint-disable @next/next/no-sync-scripts */ // need to load scripts before injected content which depends on them
import PageWrapper, { PageParams } from '@ecom-next/sitewide/page-wrapper';
import { CID, isValidCid, fetchMarketing } from '@ecom-next/marketing-ui/fetch';
import { redirect } from 'next/navigation';
import { getPageContext } from '@ecom-next/utils/server';
import UtilPageDatalayer from '../../datalayer';
import ClientEvent from './ClientEvent';
import { getEnabledFeatures } from '@ecom-next/sitewide/feature-flags';
interface InfoPagePMCSResponse {
  [cid: string]: {
    contentItems: {
      metaDescription: string;
      rawHtml: { HTMLEditorObj: string };
      titleOverride: string;
    }[];
  };
}

function hasInfoContent(cid: CID, data: unknown): data is InfoPagePMCSResponse {
  return (data as InfoPagePMCSResponse)[`${cid}/customhtml`] !== undefined;
}

function extractRawHtml(pmcsResponse: InfoPagePMCSResponse, cid: string) {
  const customHtml = pmcsResponse[`${cid}/customhtml`]?.contentItems[0]?.rawHtml?.HTMLEditorObj || '';
  // normalize html response and remove empty div, it causes server side render to fail on hydration
  return customHtml;
}

// eslint-disable-next-line
const InfoPage = async (props: PageParams) => {
  const { params, searchParams = {} } = props;
  const { cid } = params;
  const { contentApi = '', previewDate = '', brand, market, targetEnv: env, locale, requestType } = getPageContext();

  const finalContentApi = searchParams['contentApi'] || contentApi;
  const finalPreviewDate = searchParams['previewDate'] || previewDate;

  if (!isValidCid(cid)) {
    return redirect('/PageNotFound.do');
  }
  const pageType = 'utility';
  const country = market;
  const contentType = requestType;
  const { enabledFeatures } = await getEnabledFeatures({ environment: env, pageType, brand, market, locale, country, contentType }, pageType, {});
  const overrideLocale = enabledFeatures?.['default-canada-to-en-us'] || false;
  const passBadgeIdToPMCS = enabledFeatures?.['pass-badge-id-to-pmcs'] || false;

  const pmcsResponse = await fetchMarketing(cid, 'utility', brand, finalContentApi as string, finalPreviewDate as string, '', overrideLocale, passBadgeIdToPMCS);

  if (!hasInfoContent(cid, pmcsResponse)) {
    return redirect('/PageNotFound.do');
  }

  return (
    <PageWrapper pageType='utility' {...props}>
      <div id='utility-page' data-testid='utility-page'>
        <script src='https://cdn.c1.amplience.net/c/gapprod/jquery-371v2-min' />
        <script src='/Asset_Archive/AllBrands/lib/wcdLib.min.js' />
        <link rel='stylesheet' type='text/css' href='/Asset_Archive/ONWeb/content/0013/961/081/assets/slick-theme.css' />
        <script src='/Asset_Archive/ONWeb/content/0012/847/530/assets/slick.js' />
        <script>{`window.jquery = window.jQuery`}</script>
        <div id='bodyContainer'>
          <div id='mainContent'>
            <div id={cid} data-content-id={cid} className={cid} dangerouslySetInnerHTML={{ __html: extractRawHtml(pmcsResponse, cid) }} />
          </div>
        </div>
      </div>
      <UtilPageDatalayer originalPageType='utility' />
      <ClientEvent />
    </PageWrapper>
  );
};

export { default as generateCustomerServiceInfoPageMetadata } from './generate-customerService-info-meta-data';
export default InfoPage;
