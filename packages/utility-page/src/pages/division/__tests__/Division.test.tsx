import { fetchMarketing, isValidCid } from '@ecom-next/marketing-ui/src/fetchMarketing';
import { ResolvingMetadata } from 'next';
import { getPageContext } from '@ecom-next/utils/server';
import fetchMock from 'fetch-mock-jest';
import { getEnabledFeatures } from '@ecom-next/sitewide/feature-flags';
import { generateDivisionMetaData } from '../Division';
import { fetchMetaDataFromCAPIs } from '../../../utils/fetch-metadata-from-capis';

fetchMock.get('glob:https://browse-api-nginx-cache.aks.prod.azeus.gaptech.com/*', {});

const mockPropsDefault = {
  brand: 'at',
  brandName: 'at',
  cid: '1234',
  pageType: 'division',
  locale: 'en_US',
  market: 'us',
  isDesktop: true,
  brandtype: 'specialty',
  breakpoint: 'Desktop',
  ecomApiBaseUrl: '',
  targetEnv: 'prod',
} as const;

jest.mock('@ecom-next/sitewide/feature-flags', () => {
  return {
    getEnabledFeatures: jest.fn(),
  };
});

jest.mock('react', () => {
  const originalReact = jest.requireActual('react');

  return {
    __esModule: true,
    ...originalReact,
    default: originalReact,
    cache: jest.fn().mockImplementation(fn => fn),
  };
});

jest.mock('next/headers', () => {
  return {
    headers: () => ({
      get: jest.fn().mockReturnValue('mockedhost'),
    }),
    cookies: () => new Map(),
  };
});

jest.mock('@ecom-next/marketing-ui/fetch', () => {
  return {
    fetchMarketing: jest.fn(),
    isValidCid: jest.fn(),
  };
});

jest.mock('../../../utils/fetch-metadata-from-capis', () => {
  return {
    fetchMetaDataFromCAPIs: jest.fn(),
  };
});

jest.mock('@ecom-next/utils/server', () => {
  const orig = jest.requireActual('@ecom-next/utils/server');

  return {
    __esModule: true,
    ...orig,
    getPageContext: jest.fn(),
  };
});

const setMocks = (titleOverride: string, pageDescription: string, shouldReturnMetaData = true, shouldFetchFromCAPIs = false, non_indexable = false) => {
  (isValidCid as unknown as jest.Mock).mockReturnValue(true);
  (getPageContext as jest.Mock).mockReturnValue({
    ...mockPropsDefault,
    headersList: {
      get: (header: string) =>
        ({
          'request-host': 'mockedhost',
          'x-pathname': '/browse/division.do',
        })[header],
    },
  });
  (fetchMarketing as jest.Mock).mockResolvedValue(
    shouldReturnMetaData
      ? {
          '123/page-properties': {
            contentItems: [
              {
                titleOverride,
                pageDescription,
              },
            ],
          },
        }
      : {}
  );
  if (shouldFetchFromCAPIs) {
    (getEnabledFeatures as jest.Mock).mockResolvedValue({
      enabledFeatures: {
        'division-capi-seo-meta-data': true,
      },
    });
    (fetchMetaDataFromCAPIs as jest.Mock).mockResolvedValue({
      web_category: {
        seo_data: {
          page_title: `Shop Men's Clothing | Gap`,
          meta_description: `Shop men's clothing at Gap and upgrade your wardrobe with stylish classics. Browse men's clothes for any occasion, from jeans and sweaters to activewear.`,
          non_indexable,
        },
      },
    });
  } else {
    (getEnabledFeatures as jest.Mock).mockResolvedValue({
      enabledFeatures: {
        'division-capi-seo-meta-data': false,
      },
    });
  }
};

describe('generateDivisionMetaData', () => {
  it('should return correct metadata returned from PMCS', async () => {
    setMocks('PMCS Title', 'PMCS Description');
    const result = await generateDivisionMetaData({ searchParams: { cid: '123' }, params: {} }, {} as ResolvingMetadata);
    expect(result).toEqual({
      title: 'PMCS Title',
      description: 'PMCS Description',
      metadataBase: new URL(`https://mockedhost/`),
      alternates: {
        canonical: '/browse/division.do?cid=123',
      },
      other: {
        'apple-itunes-app': 'app-id=342809508',
      },
    });
  });

  it('should return empty/undefined title and description if it does not exists on PMCS', async () => {
    setMocks('', '', false);
    const result = await generateDivisionMetaData({ searchParams: { cid: '123' }, params: {} }, {} as ResolvingMetadata);
    expect(result).toEqual({
      metadataBase: new URL(`https://mockedhost/`),
      alternates: {
        canonical: '/browse/division.do?cid=123',
      },
      other: {
        'apple-itunes-app': 'app-id=342809508',
      },
    });
  });

  it('should return correct metadata from CAPIs - non-indexale set to true', async () => {
    setMocks('', '', true, true, true);

    const result = await generateDivisionMetaData({ searchParams: { cid: '123' }, params: {} }, {} as ResolvingMetadata);
    expect(result).toEqual({
      title: `Shop Men's Clothing | Gap`,
      description: `Shop men's clothing at Gap and upgrade your wardrobe with stylish classics. Browse men's clothes for any occasion, from jeans and sweaters to activewear.`,
      metadataBase: new URL(`https://mockedhost/`),
      alternates: {
        canonical: '/browse/division.do?cid=123',
      },
      other: {
        'apple-itunes-app': 'app-id=342809508',
      },
      robots: {
        index: false,
      },
    });
  });

  it('should return correct metadata from CAPIs - non-indexale set to false', async () => {
    setMocks('', '', true, true, false);

    const result = await generateDivisionMetaData({ searchParams: { cid: '123' }, params: {} }, {} as ResolvingMetadata);
    expect(result).toEqual({
      title: `Shop Men's Clothing | Gap`,
      description: `Shop men's clothing at Gap and upgrade your wardrobe with stylish classics. Browse men's clothes for any occasion, from jeans and sweaters to activewear.`,
      metadataBase: new URL(`https://mockedhost/`),
      alternates: {
        canonical: '/browse/division.do?cid=123',
      },
      other: {
        'apple-itunes-app': 'app-id=342809508',
      },
    });
  });
});
