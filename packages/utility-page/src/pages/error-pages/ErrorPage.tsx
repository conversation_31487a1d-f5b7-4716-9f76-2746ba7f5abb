import PageWrapper, { type PageParams } from '@ecom-next/sitewide/page-wrapper';
import { Brand, getPageContext, Market, Locale } from '@ecom-next/utils/server';
import { fetchMarketing, isValidCid, MarketingContent } from '@mui/fetchMarketing';
import { MarketingProvider } from '@ecom-next/marketing-ui/marketing-provider';
import CmsMarketingComponents from '@mui/components/legacy-mui-entry';
import JsonDynamicMarketing from '@mui/components/json-marketing';
import Marketing from '@mui/index';
import { ReactNode } from 'react';
import MarketingHtml from '@mui/components/marketing-html';
import { getBrandInformation } from '@mfe/brand-info';
import { headers } from 'next/headers';
import UtilPageDatalayer from '../../datalayer';
import * as staticErrorPages from './static-error-pages';
import cookieFailureContent from './cookieFailureContent.json';
import ErrorPageContent from './ErrorPageContent';
import errorPageMarketingConfig from './errorPageData.json';

type ErrorPageName = keyof (typeof errorPageMarketingConfig)[Brand]['us']['en_US'];

export type ErrorPageProps = PageParams & {
  errorType: ErrorPageName | 'CookieFailure';
};

/**
 * meta data for error pages.
 *
 * TODO: there is no meta title/description overrides in error page content now.
 * so we will not add integration for that here until we need to
 * @param errorPath error page url path
 */
export async function generateErrorPageMetaData(errorPath: string) {
  const { brand, locale, market } = getPageContext();

  const brandName = getBrandInformation(brand, market).displayName;
  const headersList = headers();
  const host = headersList.get('request-host') || headersList.get('host');

  return {
    title: brandName,
    metadataBase: new URL(`https://${host}`),
    alternates: {
      canonical: `${errorPath}${locale === 'fr_CA' ? '?locale=fr_CA' : ''}`,
    },
    robots: {
      index: false,
    },
  };
}

function getContentConfig(brand: Brand, market: Market, locale: Locale) {
  const brandConfig = errorPageMarketingConfig[brand];

  if (market === 'ca' && 'ca' in brandConfig && locale !== 'en_US') {
    return brandConfig[market][locale];
  }

  return brandConfig['us']['en_US'];
}

function getCookieFailureContent(brand: Brand, locale: Locale) {
  const brandConfig = cookieFailureContent[brand];
  if (locale !== 'en_US' && 'en_CA' in brandConfig) {
    return brandConfig[locale];
  }
  return brandConfig['en_US'];
}

export type ErrorPageNames = keyof typeof staticErrorPages;

export default async function ErrorPage(props: ErrorPageProps) {
  const { brand, market, locale } = getPageContext();
  let { contentApi = '', previewDate = '' } = getPageContext();
  const { searchParams } = props;
  contentApi = searchParams['contentApi'] || contentApi;
  previewDate = searchParams['previewDate'] || previewDate;
  const { errorType } = props;
  const contentConfig = getContentConfig(brand, market, locale);

  const contentPath = errorType !== 'CookieFailure' ? contentConfig[errorType] : '';
  const staticErrorPageName = `${brand}_${market}_${locale}_${errorType}`;
  let ele: ReactNode = null;
  if (errorType === 'CookieFailure') {
    const marketing = getCookieFailureContent(brand, locale);
    ele = <MarketingHtml richText={marketing} />;
  } else if (isValidCid(contentPath)) {
    const marketing = await fetchMarketing(contentPath, 'utility', brand, contentApi as string, previewDate as string);
    const content = {
      'error/content': {
        contentItems: [marketing?.content?.['0'] || {}],
      },
    };
    ele = (
      <MarketingProvider
        value={content as MarketingContent<'utility'>}
        jsonMarketingComponent={JsonDynamicMarketing}
        cmsMarketingComponent={CmsMarketingComponents}
      >
        <Marketing cid='error' brand={brand} defaultMarketing={null} locale={locale} market={market} pageType='utility' slot='error/content' />
      </MarketingProvider>
    );
  } else {
    if (staticErrorPageName in staticErrorPages) {
      const StaticErrorContent = staticErrorPages[staticErrorPageName as ErrorPageNames];
      ele = <StaticErrorContent />;
    }
  }
  return (
    <PageWrapper {...props} pageType='Error_Page'>
      <ErrorPageContent />
      <main id='main-content'>{ele}</main>
      <UtilPageDatalayer originalPageType='Error_Page' />
    </PageWrapper>
  );
}
