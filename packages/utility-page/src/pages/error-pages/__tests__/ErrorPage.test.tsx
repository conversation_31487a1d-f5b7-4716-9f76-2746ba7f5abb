import React, { PropsWithChildren } from 'react';
import { render, screen, act } from '@testing-library/react';
import { fetchMarketing } from '@mui/fetchMarketing';
import { getPageContext, Brand, Market, Locale, Breakpoint } from '@ecom-next/utils/server';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { DynamicMarketing } from '@mui/components/json-marketing.client';
import { LocaleTranslation } from '@sitewide/providers/localization/fetchTranslations';
import { BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import { StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { serverFetchText } from '@ecom-next/utils/serverFetch';
import ErrorPage from '../ErrorPage';

interface TestOverrides {
  brand: Brand;
  breakpoint: Breakpoint;
  locale: Locale;
  market: Market;
  translations: LocaleTranslation;
}

export function renderWithContext(node: JSX.Element, overrides?: Partial<TestOverrides>) {
  const { brand = 'at', market = 'us', locale = 'en_US', translations = {}, breakpoint = 'Desktop' } = overrides || {};
  return render(
    <StitchStyleProvider brand={brand}>
      <LocalizationProvider locale={locale} market={market} translations={translations}>
        <BreakpointProvider initialSizeClass={breakpoint}>{node}</BreakpointProvider>
      </LocalizationProvider>
    </StitchStyleProvider>
  );
}

jest.mock('@ecom-next/utils/serverFetch', () => {
  const orig = jest.requireActual('@ecom-next/utils/serverFetch');

  return {
    ...orig,
    __esModule: true,
    serverFetchText: jest.fn(),
  };
});

jest.mock('@sitewide/providers/feature-flags', () => {
  return {
    getEnabledFeatures: jest.fn().mockReturnValue(Promise.resolve({})),
  };
});

jest.mock('@ecom-next/utils/server', () => {
  const orig = jest.requireActual('@ecom-next/utils/server');

  return {
    ...orig,
    __esModule: true,
    getPageContext: jest.fn(),
  };
});

jest.mock('@ecom-next/sitewide/hooks/usePageContext', () => {
  return {
    usePageContext: jest.fn(),
  };
});
jest.mock('@mui/fetchMarketing', () => {
  const orig = jest.requireActual('@mui/fetchMarketing');
  return {
    ...orig,
    __esModule: true,
    fetchMarketing: jest.fn(),
  };
});

jest.mock('next/headers', () => {
  const headersVals = new Map();

  return {
    headers: jest.fn().mockReturnValue(headersVals),
    cookies() {
      return new Map();
    },
  };
});

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn().mockReturnValue('/browse/division.do'),
  redirect: jest.fn(),
  useSearchParams: jest.fn().mockReturnValue(new Map()),
}));

jest.mock('@sitewide/components/legacy/content', () => ({
  useContent: jest.fn().mockReturnValue({
    contentData: {
      sitewide: {},
    },
  }),
  MarketingPlacementWithSitewide: ({ name }: { name: string }) => <h3>marketing {name}</h3>,
}));

jest.mock('@ecom-next/sitewide/datalayer/index', () => ({
  addDataBuilder: jest.fn(),
}));

jest.mock('@ecom-next/sitewide/page-wrapper', () => {
  return (props: PropsWithChildren) => props.children;
});

const mockPropsDefault = {
  brand: 'at',
  brandName: 'at',
  cid: '1234',
  pageType: 'division',
  locale: 'en_US',
  market: 'us',
  isDesktop: true,
  brandtype: 'specialty',
  breakpoint: 'Desktop',
  ecomApiBaseUrl: '',
} as const;

(DynamicMarketing as jest.Mock).mockRestore();

describe('A Dynamic Page', () => {
  beforeEach(() => {
    (getPageContext as jest.Mock).mockReturnValue(mockPropsDefault);
    (usePageContext as jest.Mock).mockReturnValue(mockPropsDefault);
  });

  it('should be of type OutOfStockNoResults page', async () => {
    (fetchMarketing as jest.Mock).mockReturnValue({
      content: {
        '0': {
          output: {
            name: 'TextHeadline',
            data: {
              text: 'This was so wanted, it sold out.',
            },
          },
        },
      },
    });
    await act(async () => {
      const outOfStockPage = await ErrorPage({
        errorType: 'OutOfStock',
        params: {},
        searchParams: {},
      });
      renderWithContext(outOfStockPage);
    });
    expect(screen.getByText(/This was so wanted, it sold out./i)).toBeInTheDocument();
  });

  it('should be of type GeneralNoResult page', async () => {
    (fetchMarketing as jest.Mock).mockReturnValue({
      content: {
        '0': {
          output: {
            name: 'MultiComponent',
            components: [
              {
                name: 'TextHeadline',
                data: {
                  text: 'There is no result for your search',
                },
              },
            ],
          },
        },
      },
    });

    await act(async () => {
      const GeneralNoResult = await ErrorPage({
        errorType: 'General',
        params: {},
        searchParams: {},
      });
      renderWithContext(GeneralNoResult);
    });

    expect(screen.getByText(/There is no result for your search/i)).toBeInTheDocument();
  });

  it('should be of type InvalidIdNoResults page', async () => {
    (fetchMarketing as jest.Mock).mockReturnValue({
      content: {
        '0': {
          output: {
            name: 'MultiComponent',
            components: [
              {
                name: 'TextHeadline',
                data: {
                  text: "We're sorry, but we cannot process your request at this time",
                },
              },
            ],
          },
        },
      },
    });
    const InvalidIdPage = await ErrorPage({
      errorType: 'InvalidId',
      params: {},
      searchParams: {},
    });
    renderWithContext(InvalidIdPage);
    expect(screen.getByText(/We're sorry, but we cannot process your request at this time/i)).toBeInTheDocument();
  });

  afterAll(() => {
    jest.clearAllMocks();
  });
});

describe('A Static Page', () => {
  beforeEach(() => {
    (getPageContext as jest.Mock).mockReturnValue(mockPropsDefault);
    (usePageContext as jest.Mock).mockReturnValue(mockPropsDefault);
  });

  it('should be of type InternalServerError page', async () => {
    (serverFetchText as jest.Mock).mockReturnValue('An internal Athleta.com error occurred.');
    const ServerError = await ErrorPage({
      errorType: 'InternalServerError',
      params: {},
      searchParams: {},
    });
    render(ServerError);
    expect(screen.getByText(/An internal Athleta.com error occurred./i)).toBeInTheDocument();
  });

  it('should be of type PageNotFound page', async () => {
    (serverFetchText as jest.Mock).mockReturnValue('Page Not Found.');
    const PageNotFound = await ErrorPage({
      errorType: 'PageNotFound',
      params: {},
      searchParams: {},
    });
    render(PageNotFound);
    expect(screen.getByText(/An internal Athleta.com error occurred./i)).toBeInTheDocument();
  });

  it('should be of type BadRequest page', async () => {
    (serverFetchText as jest.Mock).mockReturnValue('Sorry, this page cannot be displayed.');
    const BadRequest = await ErrorPage({
      errorType: 'BadRequest',
      params: {},
      searchParams: {},
    });
    render(BadRequest);
    expect(screen.getByText(/Sorry, this page cannot be displayed./i)).toBeInTheDocument();
  });

  it('should be of type CookieFailure page', async () => {
    (serverFetchText as jest.Mock).mockReturnValue('In order to shop our site, you need a browser that enables cookies');
    const CookieFailure = await ErrorPage({
      errorType: 'CookieFailure',
      params: {},
      searchParams: {},
    });
    render(CookieFailure);
    expect(screen.getByText(/In order to shop our site, you need a browser that enables cookies/i)).toBeInTheDocument();
  });

  afterAll(() => {
    jest.clearAllMocks();
  });
});
