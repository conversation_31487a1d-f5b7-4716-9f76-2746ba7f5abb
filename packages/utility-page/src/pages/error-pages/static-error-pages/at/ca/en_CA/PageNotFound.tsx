export default function StaticPageNotFound() {
  return (
    <div className='sds_sp_xl'>
      <div className='sds_display-a sds_pd sds_pd_top' style={{ textAlign: 'center' }}>
        We&#39;re sorry.
      </div>
      <div className='sds_body-a sds_pd'>
        <div style={{ textAlign: 'center' }} className='sds_sp'>
          An error occurred that prevented this page from being displayed. This could be due to one of the following reasons:
        </div>
        <div style={{ display: 'flex', justifyContent: 'center' }} className='sds_sp'>
          - An internal Athleta.ca error occurred.
          <br />
          - A URL was incorrectly entered. <br />
          - The file no longer exists. <br />
        </div>
        <div style={{ textAlign: 'center' }} className='sds_sp_lg'>
          To continue shopping, return to the{' '}
          <a href='/' style={{ textDecoration: 'underline' }}>
            Athleta.ca
          </a>{' '}
          homepage now.
          <br />
          If this problem persists, please give us a call at 1-877-3ATHLETA
        </div>
      </div>
    </div>
  );
}
