export default function StaticBadRequest() {
  return (
    <div>
      <style
        type='text/css'
        dangerouslySetInnerHTML={{
          __html: '\n  #bodyContainer{ width:fit-content; margin:0 auto; text-align: left; padding: 20px; }\n  #omniSendAnalytics { display: none; }\n',
        }}
      />
      <div id='bodyContainer'>
        <a href='/'>
          <img
            src='/Asset_Archive/BFWeb/content/static-error-page/assets/topnav_brfslogo.gif'
            alt='Banana Republic Factory'
            style={{ maxWidth: '100%', border: 0 }}
          />
        </a>
        <br />
        <br />
        <div className='sds_heading-b'>Sorry, this page cannot be displayed.</div>
        <br />
        To continue shopping, return to the{' '}
        <a href='/' style={{ textDecoration: 'underline' }}>
          bananarepublicfactory.gapfactory.com
        </a>{' '}
        homepage now.
        <br />
        <br />
        If you have any questions, please contact us at <strong>1-844-BRFSSHOP</strong>.
      </div>
    </div>
  );
}
