/* eslint-disable no-console */
const HtmlToJsx = require('htmltojsx');
const fs = require('fs-extra');
const errorPageConfigs = require('../errorPageData.json');

const converter = new HtmlToJsx({ createClass: false });

const cid = /^\d+$/;

async function generateErrorJsx() {
  const indexImports = [];

  for (const brand in errorPageConfigs) {
    const brandConfig = errorPageConfigs[brand];

    for (const market in brandConfig) {
      const marketConfig = brandConfig[market];

      for (const locale in marketConfig) {
        const localeConfig = marketConfig[locale];

        for (const errorType in localeConfig) {
          const errorFile = localeConfig[errorType];
          if (cid.test(errorFile)) continue;

          // otherwise it should be html file
          const url = `https://www.gap.com${errorFile}`;
          console.log('fetching ', url);
          const html = await fetch(url).then(resp => resp.text());
          const htmlEscaped = html.replaceAll(/'/g, '-aa-');
          const jsx = converter.convert(htmlEscaped).replaceAll('-aa-', '&#39;');
          const funcName = `Static${errorType}`;
          const result = `
export default function ${funcName}() {
  return (
    ${jsx}
  )
}
          `;
          const file = `./${brand}/${market}/${locale}/${errorType}.tsx`;
          console.log('writing file ', file);
          fs.outputFile(file, result);

          indexImports.push([`export {default as ${brand}_${market}_${locale}_${errorType}} from './${brand}/${market}/${locale}/${errorType}';`]);
        }
      }
    }
  }
  fs.outputFile('./index.tsx', indexImports.join('\n'));
}

generateErrorJsx();
