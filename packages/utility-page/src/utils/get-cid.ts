import { type PageParams } from '@ecom-next/sitewide/page-wrapper';
import { isValidCid } from '@ecom-next/marketing-ui/src/fetchMarketing';
import { redirect } from 'next/navigation';

const getCid = (searchParams: PageParams['searchParams']) => {
  const { cid } = searchParams;
  const normedCid = Array.isArray(cid) ? cid[0] : cid;
  if (!isValidCid(normedCid)) {
    return redirect('/PageNotFound.do');
  }
  return normedCid;
};

export default getCid;
