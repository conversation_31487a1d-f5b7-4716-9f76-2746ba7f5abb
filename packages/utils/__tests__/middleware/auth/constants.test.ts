import * as constants from '../../../src/middleware/auth/constants';
import {
  ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_TEST,
  ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_STAGE,
  ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_PREVIEW,
  ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_PRODUCTION,
  NOT_ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_TEST,
  NOT_ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_STAGE,
  NOT_ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_PREVIEW,
  NOT_ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_PRODUCTION,
} from './mocks/testOrigins';

const SIGN_IN_ORIGIN_MAP = constants.SIGN_IN_ORIGIN_MAP;

describe('Constants', () => {
  let originalNodeEnv: string | undefined;
  let originalTargetEnv: string | undefined;

  beforeAll(() => {
    originalNodeEnv = process.env.NODE_ENV;
    originalTargetEnv = process.env.TARGET_ENV;
  });

  afterAll(() => {
    Object.defineProperty(process.env, 'NODE_ENV', { value: originalNodeEnv });
    Object.defineProperty(process.env, 'TARGET_ENV', { value: originalTargetEnv });
  });

  describe('constants', () => {
    it('should have constants with the correct values', () => {
      expect(constants.HOST).toBe('host');
      expect(constants.GUEST).toBe('GUEST');
      expect(constants.US_MARKET).toBe('us');
      expect(constants.EMPTY_STRING).toBe('');
      expect(constants.CANADA_MARKET).toBe('ca');
      expect(constants.PROTOCOL).toBe('https://');
      expect(constants.BARCLAYS).toBe('barclays');
      expect(constants.ANONYMOUS).toBe('ANONYMOUS');
      expect(constants.PF_APP_COOKIE).toBe('pf_app');
      expect(constants.TARGET_URL).toBe('targetURL');
      expect(constants.RECOGNIZED).toBe('RECOGNIZED');
      expect(constants.X_PATHNAME).toBe('x-pathname');
      expect(constants.ASOTP_S_COOKIE).toBe('asotp_s');
      expect(constants.ASOTP_V_COOKIE).toBe('asotp_v');
      expect(constants.DEVELOPMENT).toBe('development');
      expect(constants.CHECKOUT_PATH).toBe('/checkout');
      expect(constants.REQUEST_HOST).toBe('request-host');
      expect(constants.CHECKOUT_CONTEXT).toBe('checkout');
      expect(constants.FORCE_SIGNUP_PARAM).toBe('signup');
      expect(constants.AUTHENTICATED).toBe('AUTHENTICATED');
      expect(constants.X_SIGN_IN_PAYLOAD).toBe('x-sign-in');
      expect(constants.TEALIUM_COOKIE).toBe('triage_tealium');
      expect(constants.DEFAULT_PATH).toBe('/my-account/home');
      expect(constants.X_AUTHENTICATED).toBe('x-authenticated');
      expect(constants.QUERY_PARAMS_COOKIE).toBe('triage_params');
      expect(constants.BARCLAYS_CREDIT_OFFER).toBe('creditOffer');
      expect(constants.AUTHENTICATED_CONTEXT).toBe('authenticated');
      expect(constants.REDIRECT_ERROR_CONTEXT).toBe('redirectError');
      expect(constants.BARCLAYS_POST_IN_CONTEXT).toBe('barclaysPostIn');
      expect(constants.BARCLAYS_POST_BACK_CONTEXT).toBe('barclaysPostBack');
      expect(constants.MERGE_BAG_PATH).toBe('/shopping-bag?bagChanged=true');
      expect(constants.BARCLAYS_LOYALTY_STATUS).toBe('barclaysLoyaltyStatus');
      expect(constants.CLEAR_ALL_COOKIE_ACTION).toBe('CLEAR_ALL_COOKIE_ACTION');
      expect(constants.FORCED_RESET_PASSWORD_CONTEXT).toBe('forcedResetPassword');
      expect(constants.GET_TEALIUM_COOKIE_ACTION).toBe('GET_TEALIUM_COOKIE_ACTION');
      expect(constants.SET_TEALIUM_COOKIE_ACTION).toBe('SET_TEALIUM_COOKIE_ACTION');
      expect(constants.BARCLAYS_POST_BACK_TYPES).toEqual(['TEMP', 'BANNER', '']);
      expect(constants.RENDER_SIGN_IN_COOKIE_ACTION).toBe('ON_RENDER_SIGN_IN_COOKIE_ACTION');
      expect(constants.AUTHENTICATE_ERROR).toBe('/my-account/sign-in?errorCode=authenticate');
      expect(constants.FORBIDDEN_ERROR).toBe('Access to the requested resource is forbidden');
      expect(constants.RETRY).toBe('retry');
      expect(constants.ERROR_CODE).toBe('errorCode');
      expect(constants.REDIRECT_REENTRY_ERROR).toBe('reentry');
      expect(constants.REDIRECT_SIGN_IN_ERROR).toBe('sign_in');
      expect(constants.REDIRECT_CONTINUE_ERROR).toBe('continue');
      expect(constants.REDIRECT_AUTHENTICATE_ERROR).toBe('authenticate');
      expect(constants.REDIRECT_CREATE_ACCOUNT_ERROR).toBe('create_account');
      expect(constants.REDIRECT_GUEST_CHECKOUT_ERROR).toBe('guest_checkout');
      expect(constants.REDIRECT_EXPIRED_SESSION_ERROR).toBe('expired_session');
      expect(constants.REDIRECT_EMAIL_VALIDATION_ERROR).toBe('email_validation');
      expect(constants.REDIRECT_BARCLAYS_SIGN_IN_ERROR).toBe('barclays_sign_in');
      expect(constants.REDIRECT_BARCLAYS_GUEST_ERROR).toBe('barclays_skip_sign_in');
    });
  });

  describe('ALLOWED_SIGN_IN_ORIGINS', () => {
    it('should return true for allowed test sign-in origins when env is test', () => {
      const regex = SIGN_IN_ORIGIN_MAP.test;
      ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_TEST.forEach(origin => {
        expect(regex.test(origin)).toBe(true);
      });
    });

    it('should return true for allowed stage sign-in origins when env is stage', () => {
      const regex = SIGN_IN_ORIGIN_MAP.stage;
      ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_STAGE.forEach(origin => {
        expect(regex.test(origin)).toBe(true);
      });
    });

    it('should return true for allowed preview sign-in origins when env is preview', () => {
      const regex = SIGN_IN_ORIGIN_MAP.preview;
      ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_PREVIEW.forEach(origin => {
        expect(regex.test(origin)).toBe(true);
      });
    });

    it('should return true for allowed production sign-in origins when env is production', () => {
      const regex = SIGN_IN_ORIGIN_MAP.prod;
      ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_PRODUCTION.forEach(origin => {
        expect(regex.test(origin)).toBe(true);
      });
    });
  });

  describe('NOT ALLOWED_SIGN_IN_ORIGINS', () => {
    it('should return false for not allowed test sign-in origins when env is test', () => {
      const regex = SIGN_IN_ORIGIN_MAP.test;
      NOT_ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_TEST.forEach(origin => {
        expect(regex.test(origin)).toBe(false);
      });
    });

    it('should return false for not allowed stage sign-in origins when env is stage', () => {
      const regex = SIGN_IN_ORIGIN_MAP.stage;
      NOT_ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_STAGE.forEach(origin => {
        expect(regex.test(origin)).toBe(false);
      });
    });

    it('should return false for not allowed preview sign-in origins when env is preview', () => {
      const regex = SIGN_IN_ORIGIN_MAP.preview;
      NOT_ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_PREVIEW.forEach(origin => {
        expect(regex.test(origin)).toBe(false);
      });
    });

    it('should return false for not allowed production sign-in origins when env is production', () => {
      const regex = SIGN_IN_ORIGIN_MAP.prod;
      NOT_ALLOWED_SIGN_IN_ORIGINS_MOCK_LIST_PRODUCTION.forEach(origin => {
        expect(regex.test(origin)).toBe(false);
      });
    });
  });

  describe('ALLOWED_TARGET_URL_HOSTS', () => {
    it('should contain only the allowed TARGET_URL hosts', () => {
      const expectedHosts = [
        /**
         * Production
         */

        /* Production Browse US, CA, Factory */
        'www.gap.com',
        'athleta.gap.com',
        'oldnavy.gap.com',
        'bananarepublic.gap.com',
        'www.gapcanada.ca',
        'athleta.gapcanada.ca',
        'oldnavy.gapcanada.ca',
        'bananarepublic.gapcanada.ca',
        'www.gapfactory.com',
        'www.gapfactory.ca',
        'bananarepublicfactory.gapfactory.com',
        'bananarepublicfactory.gapfactory.ca',

        /* Production Buy US, CA, Factory */
        'secure-www.gap.com',
        'secure-athleta.gap.com',
        'secure-oldnavy.gap.com',
        'secure-bananarepublic.gap.com',
        'secure-www.gapcanada.ca',
        'secure-athleta.gapcanada.ca',
        'secure-oldnavy.gapcanada.ca',
        'secure-bananarepublic.gapcanada.ca',
        'secure-www.gapfactory.com',
        'secure-bananarepublicfactory.gapfactory.com',
        'secure-bananarepublicfactory.gapfactory.ca',

        /* Production ATWell */
        'api.gap.com',

        /**
         * Prod-Preview Wip
         */

        /* Prod-Preview Wip Browse US, CA, Factory */
        'www.wip.prod.gaptecholapps.com',
        'onol.wip.prod.gaptecholapps.com',
        'brol.wip.prod.gaptecholapps.com',
        'atol.wip.prod.gaptecholapps.com',
        'www.wip.prod.gaptecholapps.ca',
        'onol.wip.prod.gaptecholapps.ca',
        'brol.wip.prod.gaptecholapps.ca',
        'atol.wip.prod.gaptecholapps.ca',
        'www.wip.prod.factory-gaptecholapps.com',
        'www.wip.prod.factory-gaptecholapps.ca',
        'brfol.wip.prod.factory-gaptecholapps.com',
        'brfol.wip.prod.factory-gaptecholapps.ca',

        /* Prod-Preview Wip Buy US, CA, Factory */
        'secure.www.wip.prod.gaptecholapps.com',
        'secure.onol.wip.prod.gaptecholapps.com',
        'secure.brol.wip.prod.gaptecholapps.com',
        'secure.atol.wip.prod.gaptecholapps.com',
        'secure.www.wip.prod.gaptecholapps.ca',
        'secure.onol.wip.prod.gaptecholapps.ca',
        'secure.brol.wip.prod.gaptecholapps.ca',
        'secure.atol.wip.prod.gaptecholapps.ca',
        'secure.www.wip.prod.factory-gaptecholapps.com',
        'secure.brfol.wip.prod.factory-gaptecholapps.com',
        'secure.brfol.wip.prod.factory-gaptecholapps.ca',

        /**
         * Prod-Preview App
         */

        /* Prod-Preview App Browse US, CA, Factory */
        'www.app.prod.gaptecholapps.com',
        'onol.app.prod.gaptecholapps.com',
        'brol.app.prod.gaptecholapps.com',
        'atol.app.prod.gaptecholapps.com',
        'www.app.prod.gaptecholapps.ca',
        'onol.app.prod.gaptecholapps.ca',
        'brol.app.prod.gaptecholapps.ca',
        'atol.app.prod.gaptecholapps.ca',
        'www.app.prod.factory-gaptecholapps.com',
        'www.app.prod.factory-gaptecholapps.ca',
        'brfol.app.prod.factory-gaptecholapps.com',
        'brfol.app.prod.factory-gaptecholapps.ca',

        /* Prod-Preview App Buy US, CA, Factory */
        'secure.www.app.prod.gaptecholapps.com',
        'secure.onol.app.prod.gaptecholapps.com',
        'secure.brol.app.prod.gaptecholapps.com',
        'secure.atol.app.prod.gaptecholapps.com',
        'secure.www.app.prod.gaptecholapps.ca',
        'secure.onol.app.prod.gaptecholapps.ca',
        'secure.brol.app.prod.gaptecholapps.ca',
        'secure.atol.app.prod.gaptecholapps.ca',
        'secure.www.app.prod.factory-gaptecholapps.com',
        'secure.brfol.app.prod.factory-gaptecholapps.com',
        'secure.brfol.app.prod.factory-gaptecholapps.ca',

        /**
         * Stage
         */

        /* Stage Browse US, CA, Factory */
        'www.stage.gaptechol.com',
        'onol.stage.gaptechol.com',
        'brol.stage.gaptechol.com',
        'atol.stage.gaptechol.com',
        'www.stage.gaptechol.ca',
        'onol.stage.gaptechol.ca',
        'brol.stage.gaptechol.ca',
        'atol.stage.gaptechol.ca',
        'www.stage.factory-gaptechol.com',
        'www.stage.factory-gaptechol.ca',
        'brfol.stage.factory-gaptechol.com',
        'brfol.stage.factory-gaptechol.ca',

        /* Stage Buy US, CA, Factory */
        'secure-www.stage.gaptechol.com',
        'secure-onol.stage.gaptechol.com',
        'secure-brol.stage.gaptechol.com',
        'secure-atol.stage.gaptechol.com',
        'secure-www.stage.gaptechol.ca',
        'secure-onol.stage.gaptechol.ca',
        'secure-brol.stage.gaptechol.ca',
        'secure-atol.stage.gaptechol.ca',
        'secure-www.stage.factory-gaptechol.com',
        'secure-brfol.stage.factory-gaptechol.com',
        'secure-brfol.stage.factory-gaptechol.ca',

        /* Stage ATWell */
        'stage.api.gap.com',

        /**
         * Stage-Preview Wip
         */

        /* Stage-Preview Wip Browse US, CA, Factory */
        'www.wip.stage.gaptecholapps.com',
        'onol.wip.stage.gaptecholapps.com',
        'brol.wip.stage.gaptecholapps.com',
        'atol.wip.stage.gaptecholapps.com',
        'www.wip.stage.gaptecholapps.ca',
        'onol.wip.stage.gaptecholapps.ca',
        'brol.wip.stage.gaptecholapps.ca',
        'atol.wip.stage.gaptecholapps.ca',
        'www.wip.stage.factory-gaptecholapps.com',
        'brfol.wip.stage.factory-gaptecholapps.com',
        'brfol.wip.stage.factory-gaptecholapps.ca',

        /* Stage-Preview Wip Buy US, CA, Factory */
        'secure.www.wip.stage.gaptecholapps.com',
        'secure.onol.wip.stage.gaptecholapps.com',
        'secure.brol.wip.stage.gaptecholapps.com',
        'secure.atol.wip.stage.gaptecholapps.com',
        'secure.www.wip.stage.gaptecholapps.ca',
        'secure.onol.wip.stage.gaptecholapps.ca',
        'secure.brol.wip.stage.gaptecholapps.ca',
        'secure.atol.wip.stage.gaptecholapps.ca',
        'secure.www.wip.stage.factory-gaptecholapps.com',
        'secure.brfol.wip.stage.factory-gaptecholapps.com',
        'secure.brfol.wip.stage.factory-gaptecholapps.ca',

        /**
         * Stage-Preview App
         */

        /* Stage-Preview App Browse US, CA, Factory */
        'www.app.stage.gaptecholapps.com',
        'onol.app.stage.gaptecholapps.com',
        'brol.app.stage.gaptecholapps.com',
        'atol.app.stage.gaptecholapps.com',
        'www.app.stage.gaptecholapps.ca',
        'onol.app.stage.gaptecholapps.ca',
        'brol.app.stage.gaptecholapps.ca',
        'atol.app.stage.gaptecholapps.ca',
        'www.app.stage.factory-gaptecholapps.com',
        'brfol.app.stage.factory-gaptecholapps.com',
        'brfol.app.stage.factory-gaptecholapps.ca',

        /* Stage-Preview App Buy US, CA, Factory */
        'secure.www.app.stage.gaptecholapps.com',
        'secure.onol.app.stage.gaptecholapps.com',
        'secure.brol.app.stage.gaptecholapps.com',
        'secure.atol.app.stage.gaptecholapps.com',
        'secure.www.app.stage.gaptecholapps.ca',
        'secure.onol.app.stage.gaptecholapps.ca',
        'secure.brol.app.stage.gaptecholapps.ca',
        'secure.atol.app.stage.gaptecholapps.ca',
        'secure.www.app.stage.factory-gaptecholapps.com',
        'secure.brfol.app.stage.factory-gaptecholapps.com',
        'secure.brfol.app.stage.factory-gaptecholapps.ca',

        /**
         * Test
         */

        /* Test Browse US, CA, Factory */
        'www.test.gaptechol.com',
        'onol.test.gaptechol.com',
        'brol.test.gaptechol.com',
        'atol.test.gaptechol.com',
        'www.test.gaptechol.ca',
        'onol.test.gaptechol.ca',
        'brol.test.gaptechol.ca',
        'atol.test.gaptechol.ca',
        'www.test.factory-gaptechol.com',
        'www.test.factory-gaptechol.ca',
        'brfol.test.factory-gaptechol.com',
        'brfol.test.factory-gaptechol.ca',

        /* Test Buy US, CA, Factory */
        'secure-www.test.gaptechol.com',
        'secure-onol.test.gaptechol.com',
        'secure-brol.test.gaptechol.com',
        'secure-atol.test.gaptechol.com',
        'secure-www.test.gaptechol.ca',
        'secure-onol.test.gaptechol.ca',
        'secure-brol.test.gaptechol.ca',
        'secure-atol.test.gaptechol.ca',
        'secure-www.test.factory-gaptechol.com',
        'secure-brfol.test.factory-gaptechol.com',
        'secure-brfol.test.factory-gaptechol.ca',
      ] as const;

      expectedHosts.forEach((host: string) => {
        expect(constants.ALLOWED_TARGET_URL_HOSTS).toContain(host);
      });
      expect(expectedHosts.length).toEqual(constants.ALLOWED_TARGET_URL_HOSTS.length);
    });

    it('should not contain development hosts when not in development mode', () => {
      Object.defineProperty(process.env, 'NODE_ENV', { value: 'production' });
      const developmentHosts = [
        'local.www.stage.gaptechol.com',
        'local.onol.stage.gaptechol.com',
        'local.brol.stage.gaptechol.com',
        'local.atol.stage.gaptechol.com',
        'local.www.stage.gaptechol.ca',
        'local.onol.stage.gaptechol.ca',
        'local.brol.stage.gaptechol.ca',
        'local.atol.stage.gaptechol.ca',
        'local.www.stage.factory-gaptechol.com',
        'local.www.stage.factory-gaptechol.ca',
        'local.brfol.stage.factory-gaptechol.com',
        'local.brfol.stage.factory-gaptechol.ca',
        'local.secure-www.stage.gaptechol.com',
        'local.secure-onol.stage.gaptechol.com',
        'local.secure-brol.stage.gaptechol.com',
        'local.secure-atol.stage.gaptechol.com',
        'local.secure-www.stage.gaptechol.ca',
        'local.secure-onol.stage.gaptechol.ca',
        'local.secure-brol.stage.gaptechol.ca',
        'local.secure-atol.stage.gaptechol.ca',
        'local.secure-www.stage.factory-gaptechol.com',
        'local.secure-brfol.stage.factory-gaptechol.com',
        'local.secure-brfol.stage.factory-gaptechol.ca',
      ] as const;

      developmentHosts.forEach(host => {
        expect(constants.ALLOWED_TARGET_URL_HOSTS).not.toContain(host);
      });
    });
  });
});
