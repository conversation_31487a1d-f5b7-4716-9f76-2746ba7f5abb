const plopfile = function (
  /** @type {import('plop').NodePlopAPI}*/
  plop
) {
  plop.setGenerator('Core UI Component', {
    description: 'This will create a base skeleton for building Core UI Components',
    prompts: [
      {
        type: 'input',
        name: 'componentName',
        message: 'Component name:',
      },
    ],
    actions: [
      {
        type: 'add',
        path: 'packages/core/src/components/fabric/{{kebabCase componentName}}/{{pascalCase componentName}}.tsx',
        templateFile: 'plop-templates/component.template.hbs',
      },
      {
        type: 'add',
        path: 'packages/core/src/components/fabric/{{kebabCase componentName}}/types.ts',
        templateFile: 'plop-templates/types.template.hbs',
      },
      {
        type: 'add',
        path: 'packages/core/src/components/fabric/{{kebabCase componentName}}/styles.css',
        templateFile: 'plop-templates/styles.template.hbs',
      },
      {
        type: 'add',
        path: 'packages/core/src/components/fabric/{{kebabCase componentName}}/index.tsx',
        templateFile: 'plop-templates/index.template.hbs',
      },
      {
        type: 'add',
        path: 'packages/core/src/components/fabric/{{kebabCase componentName}}/__tests__/index.spec.tsx',
        templateFile: 'plop-templates/index.spec.template.hbs',
      },
    ],
  });
};

export default plopfile;
