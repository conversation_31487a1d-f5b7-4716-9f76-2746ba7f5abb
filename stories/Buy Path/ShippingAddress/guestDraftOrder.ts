import type { DraftOrder } from '@ecom-next/checkout/contexts/types';

export const guestDraftOrder: DraftOrder = {
  panels: {
    bagPanel: {
      lineItems: [
        {
          brand: 'GP',
          twoCharBrandCode: 'gp',
          productSkuId: '7311630021824',
          productName: 'Baby Organic Cotton Pull-On Slim Jeans',
          productTypeName: 'baby boys jeans',
          productStyleId: '731163',
          color: 'dark wash',
          size: '18-24 M',
          price: {
            regularPrice: 29.95,
            salePrice: 29.95,
            discountedPrice: 29.95,
            mupPromoPrice: 29.95,
          },
          regularPrice: '29.95',
          salePrice: '29.95',
          discountedPrice: '29.95',
          totalPrice: '29.95',
          quantity: '1',
          imagePath: 'webcontent/0020/596/841/cn20596841.jpg',
          inventoryStatus: 'RESERVED',
          appliedDiscounts: [],
          storeId: '',
        },
      ],
      bopisItems: [],
      bagTotal: 29.95,
    },
    checkoutPanel: {
      conditionals: {
        hasBopisItems: false,
        isBopisOnlyBag: false,
        isBopisGiftCardPurchase: false,
        bopisInBag: false,
        isInvalidBagError: false,
        isOrderLineItems: true,
        shouldDonationsDisplay: false,
        isSummaryViewEnabled: true,
        cvvInvalidErrors: false,
        cardProcessingError: false,
        isShippingMethods: true,
        isShippingAddress: false,
        isShippingAddressSelected: false,
        isPayment: false,
        isOrderReadyToPlace: false,
      },
      totalPrice: 34.95,
      lineItems: [
        {
          mergeType: 'Standard PDP',
          itemId: 'e8fb2c13a1684a0db816f3b82ff899e8',
          brand: 'GP',
          twoCharBrandCode: 'gp',
          productSkuId: '7311630021824',
          productName: 'Baby Organic Cotton Pull-On Slim Jeans',
          customerChoiceNumber: '731163002',
          productTypeName: 'baby boys jeans',
          productStyleId: '731163',
          color: 'dark wash',
          size: '18-24 M',
          colorStyleNumber: '731163002',
          variantDescription: 'Regular',
          merchandiseType: '7',
          inventoryStatusId: 5,
          isBackOrderItem: false,
          backOrdershippingDate: false,
          autoAdded: false,
          noReturnItem: false,
          returnByMailItem: false,
          noIntlShipping: true,
          marketingFlag: '1',
          giftWrappable: true,
          productStyleDescription: 'Baby Organic Cotton Pull-On Slim Jeans',
          primaryCategoryName: '',
          productURL: 'browse/product.do?pid=7311630021824',
          price: {
            regularPrice: 29.95,
            salePrice: 29.95,
            discountedPrice: 29.95,
            percentageOff: 0,
          },
          regularPrice: '29.95',
          salePrice: '29.95',
          discountedPrice: '29.95',
          totalPrice: '29.95',
          quantity: '1',
          imagePath: 'webcontent/0020/596/841/cn20596841.jpg',
          inventoryStatus: 'RESERVED',
          vendorId: '000306965',
          webVendorName: 'THE CIVIL ENGINEERS LIMITED',
          showSellerName: false,
          excludedFromPromotion: false,
          madeToOrder: false,
          estimatedShippingDate: '',
          eligibleReturnLocationCode: '',
          isExcludedFromRewardFreeShipping: false,
          vendorStyleNumber: '',
          vendorUPCCode: '',
          appliedDiscounts: [],
          storeId: '',
        },
      ],
      totalSavings: 0,
      locale: 'en_US',
      bagType: {
        bopisOnlyBag: false,
        omniBag: false,
        shipToHomeBag: true,
      },
      pickupOrderType: 'shipItemsOnly',
      summaryView: {
        shippingAddressPanel: 'R',
        deliveryGroupPanel: 'D',
        pickupPanel: 'H',
        paymentPanel: 'D',
        rewardsPanel: 'H',
        showDonationPanel: false,
      },
      checkoutModuleStatus:
        'shippingAddressPanel:required|deliveryGroupPanel:disabled|pickupPanel:hidden|paymentPanel:disabled|rewardsPanel:hidden|donationPanel:hidden',
    },
    donationPanel: {},
    giftCardPanel: {
      totalPrice: 34.95,
      appliedGiftCards: [],
      errors: [],
      conditionals: {
        hasGiftCardsApplied: false,
      },
      isBopisGiftCardPurchase: false,
      isGiftcardPanelExpanded: false,
      isFormShown: true,
    },
    orderSummaryPanel: {
      isOrdersummarylineItemEnabled: false,
      hasPromotion: false,
      promotionDescription: false,
      isBopisOnly: false,
      subTotal: 29.95,
      hasShippingPrice: true,
      shippingPrice: 5,
      shippingMessage: '(7-9 business days)',
      hasSavings: false,
      totalSavings: 0,
      hasRewards: false,
      rewardsSubTotal: 0,
      giftCardsTotal: 0,
      hasGiftCards: false,
      estimatedTax: 0,
      hasDonations: false,
      donationsTotal: 0,
      totalPrice: '34.95',
      retailDeliveryFee: 0,
      markdownPromoSavings: 0,
      rewardsSavings: 0,
      isAfterpaySelected: false,
    },
    paymentPanel: {
      paymentMethods: [],
      paymentId: '',
      validGiftCardSubTotal: 0,
      shippingAddresses: [],
      billingErrors: [],
      totalPrice: 34.95,
      conditionals: {
        bopisOnlyBag: false,
        shippingAddressSelected: false,
        omniBag: false,
        hasRewards: false,
        isOrderTotalZero: false,
        isPhoneError: false,
        hasBillingAddressId: false,
        hasBopisItems: false,
        hasShipToAddressItems: false,
        isBopisGiftCardPurchase: false,
        showGiftCard: true,
        hasStoreName: false,
        cvvInvalidErrors: false,
        showPaypalButton: true,
        cardProcessingError: false,
        paymentFailedError: false,
        isAfterPayProcessingError: false,
        isGiftCardProcessingError: false,
        isKlarnaProcessingError: false,
        isPaymentPanelError: false,
        isPaypalProcessingError: false,
        isPaypalSelected: false,
        isAfterpaySelected: false,
        isPaypalButtonEnabled: true,
        isPaypalDisplayed: true,
        showAfterPayButton: false,
        isGiftCardCoversOrder: false,
        isLegacyRewardsCodeDisplayed: true,
        isBarclayCodeEnabled: true,
        isBarclaySavingCalculatorEnabled: true,
        isUpsAccessPoint: false,
        isPaymentModalEnabled: true,
      },
      errors: [],
      summaryView: {
        giftCards: [],
      },
      isCardInValid: false,
    },
    paypalLightBoxPanel: {
      flow: 'checkout',
      intent: 'order',
      displayName: 'Gap Inc',
      enableShippingAddress: true,
      shippingAddressEditable: false,
      amount: 34.95,
      currency: 'USD',
      locale: 'en_US',
      shippingAddressOverride: {
        recipientName: 'undefined undefined',
        line1: '',
        city: '',
        state: '',
        postalCode: '',
        countryCode: '',
      },
    },
    pickupPanel: {
      storePickupInfoList: [],
      summaryView: {
        stores: [],
        totalQuantity: 0,
      },
    },
    placeOrderPanel: {
      appliedPromotions: [],
      bopis_order_type: 'shipItemsOnly',
      currencyCode: 'USD',
      donationTotal: '',
      totalPrice: '34.95',
      subTotal: 29.95,
      shippingChargesSubTotal: 5,
      rewardsSubTotal: '',
      promoSubTotal: '',
      emailOptInIndicator: false,
      cvv: '',
      giftCards: [],
      giftCardSubTotal: '',
      isBopisOnlyBag: false,
      merchandiseSubTotal: 29.95,
      estimatedTax: 0,
      placeOrderErrors: [],
      customer: {
        isGuest: true,
        isLoggedIn: false,
        isRecognized: false,
      },
      conditionals: {
        isPopupError: false,
      },
      shippingMethods: [
        {
          shippingMethodName: 'No Rush',
          shippingTypeDescription: '7-9 business days',
          shippingPrice: 5,
          shippingTypeId: 7,
          isSelected: true,
          isEnabled: true,
          shippingId: 38696,
          deliveryDate: 'Nov 18th',
          deliveryWeekDay: 'Monday',
          maxDays: 9,
          minDays: 7,
        },
        {
          shippingMethodName: 'Basic',
          shippingTypeDescription: '5-7 business days',
          shippingPrice: 7,
          shippingTypeId: 7,
          isSelected: false,
          isEnabled: true,
          shippingId: 38703,
          deliveryDate: 'Nov 14th',
          deliveryWeekDay: 'Thursday',
          maxDays: 7,
          minDays: 5,
        },
        {
          shippingMethodName: 'Standard',
          shippingTypeDescription: '3-5 business days',
          shippingPrice: 9,
          shippingTypeId: 1,
          isSelected: false,
          isEnabled: true,
          shippingId: 75071,
          deliveryDate: 'Nov 12th',
          deliveryWeekDay: 'Tuesday',
          maxDays: 5,
          minDays: 3,
        },
        {
          shippingMethodName: 'Express',
          shippingTypeDescription: '2-3 business days',
          shippingPrice: 17,
          shippingTypeId: 3,
          isSelected: false,
          isEnabled: true,
          shippingId: 38698,
          deliveryDate: 'Nov 11th',
          deliveryWeekDay: 'Monday',
          maxDays: 3,
          minDays: 2,
        },
        {
          shippingMethodName: 'Priority',
          shippingTypeDescription: '1 business day',
          shippingPrice: 25,
          shippingTypeId: 5,
          isSelected: false,
          isEnabled: true,
          shippingId: 74458,
          deliveryDate: 'Nov 7th',
          deliveryWeekDay: 'Thursday',
          maxDays: 1,
          minDays: 1,
        },
      ],
      orderItems: [
        {
          appliedDiscounts: '',
          color: 'dark wash',
          imagePath: 'webcontent/0020/596/841/cn20596841.jpg',
          inventoryStatus: 'RESERVED',
          price: {
            discountedPrice: 29.95,
            regularPrice: 29.95,
            salePrice: 29.95,
          },
          productName: 'Baby Organic Cotton Pull-On Slim Jeans',
          productSkuId: '7311630021824',
          productStyleId: '731163',
          productTypeName: 'Baby Organic Cotton Pull-On Slim Jeans',
          quantity: 1,
          size: '18-24 M',
          totalPrice: 29.95,
          twoCharBrandCode: 'GP',
        },
      ],
      paymentMethods: [],
    },
    rewardsPanel: {
      conditionals: {
        isRewardsPanelDisplayed: false,
        isPartialResponse: true,
        isPromoRewardsApplied: false,
        isCvvError: false,
        isPromotionError: false,
        hasErrors: false,
      },
      brand: 'GP',
      market: 'US',
      isGuest: true,
      rewardErrorCode: '',
    },
    shippingAddressPanel: {
      conditionals: {
        isHubBoxEnabled: true,
      },
      lineItemCount: 1,
      bopisInBag: false,
      isOmniBag: false,
      deliveryGroupId: 'GapRegularGroup',
      notEligibleShippingItems: [],
      hasNoEligibleShippingItems: false,
      shouldDisplayGifting: true,
      showUPS: true,
    },
    signInPanel: {
      isGuest: true,
      isLoggedIn: false,
      isRecognized: false,
      marketCode: 'US',
      emailId: '<EMAIL>',
      lineItems: [
        {
          mergeType: 'Standard PDP',
          itemId: 'e8fb2c13a1684a0db816f3b82ff899e8',
          brand: 'GP',
          twoCharBrandCode: 'gp',
          productSkuId: '7311630021824',
          productName: 'Baby Organic Cotton Pull-On Slim Jeans',
          customerChoiceNumber: '731163002',
          productTypeName: 'baby boys jeans',
          productStyleId: '731163',
          color: 'dark wash',
          size: '18-24 M',
          colorStyleNumber: '731163002',
          variantDescription: 'Regular',
          merchandiseType: '7',
          inventoryStatusId: 5,
          isBackOrderItem: false,
          backOrdershippingDate: false,
          autoAdded: false,
          noReturnItem: false,
          returnByMailItem: false,
          noIntlShipping: true,
          marketingFlag: '1',
          giftWrappable: true,
          productStyleDescription: 'Baby Organic Cotton Pull-On Slim Jeans',
          primaryCategoryName: '',
          productURL: 'browse/product.do?pid=7311630021824',
          price: {
            regularPrice: 29.95,
            salePrice: 29.95,
            discountedPrice: 29.95,
            percentageOff: 0,
          },
          regularPrice: '29.95',
          salePrice: '29.95',
          discountedPrice: '29.95',
          totalPrice: '29.95',
          quantity: '1',
          imagePath: 'webcontent/0020/596/841/cn20596841.jpg',
          inventoryStatus: 'RESERVED',
          vendorId: '000306965',
          webVendorName: 'THE CIVIL ENGINEERS LIMITED',
          showSellerName: false,
          excludedFromPromotion: false,
          madeToOrder: false,
          estimatedShippingDate: '',
          eligibleReturnLocationCode: '',
          isExcludedFromRewardFreeShipping: false,
          vendorStyleNumber: '',
          vendorUPCCode: '',
          appliedDiscounts: [],
          storeId: '',
        },
      ],
    },
    deliveryGroupPanel: {
      deliveryGroupLists: [
        {
          deliveryGroupId: 'GapRegularGroup',
          pickupOrderType: 'shipItemsOnly',
          lineItemList: [
            {
              mergeType: 'Standard PDP',
              itemId: 'e8fb2c13a1684a0db816f3b82ff899e8',
              brand: 'GP',
              twoCharBrandCode: 'gp',
              productSkuId: '7311630021824',
              productName: 'Baby Organic Cotton Pull-On Slim Jeans',
              customerChoiceNumber: '731163002',
              productTypeName: 'baby boys jeans',
              productStyleId: '731163',
              color: 'dark wash',
              size: '18-24 M',
              colorStyleNumber: '731163002',
              variantDescription: 'Regular',
              merchandiseType: '7',
              inventoryStatusId: 5,
              isBackOrderItem: false,
              backOrdershippingDate: false,
              autoAdded: false,
              noReturnItem: false,
              returnByMailItem: false,
              noIntlShipping: true,
              marketingFlag: '1',
              giftWrappable: true,
              productStyleDescription: 'Baby Organic Cotton Pull-On Slim Jeans',
              primaryCategoryName: '',
              productURL: 'browse/product.do?pid=7311630021824',
              price: {
                regularPrice: 29.95,
                salePrice: 29.95,
                discountedPrice: 29.95,
                percentageOff: 0,
              },
              regularPrice: '29.95',
              salePrice: '29.95',
              discountedPrice: '29.95',
              totalPrice: '29.95',
              quantity: '1',
              imagePath: 'webcontent/0020/596/841/cn20596841.jpg',
              inventoryStatus: 'RESERVED',
              vendorId: '000306965',
              webVendorName: 'THE CIVIL ENGINEERS LIMITED',
              showSellerName: false,
              excludedFromPromotion: false,
              madeToOrder: false,
              estimatedShippingDate: '',
              eligibleReturnLocationCode: '',
              isExcludedFromRewardFreeShipping: false,
              vendorStyleNumber: '',
              vendorUPCCode: '',
              appliedDiscounts: [],
              storeId: '',
            },
          ],
          offerDetails: "NOT IN A HURRY?.\nSelect this option to give us a few extra days. We'll still do our best to ship your purchase quickly.",
          isBackOrder: false,
          isMadeToOrder: false,
          isDropShipItem: false,
          webVendorName: '',
          shippingMethodList: [
            {
              shippingMethodName: 'No Rush',
              shippingTypeDescription: '7-9 business days',
              shippingPrice: 5,
              shippingTypeId: 7,
              isSelected: true,
              isEnabled: true,
              shippingId: 38696,
              deliveryDate: '18th',
              deliveryWeekDay: 'Monday,',
              deliveryMonth: 'Nov',
              maxDays: 9,
              minDays: 7,
            },
            {
              shippingMethodName: 'Basic',
              shippingTypeDescription: '5-7 business days',
              shippingPrice: 7,
              shippingTypeId: 7,
              isSelected: false,
              isEnabled: true,
              shippingId: 38703,
              deliveryDate: '14th',
              deliveryWeekDay: 'Thursday,',
              deliveryMonth: 'Nov',
              maxDays: 7,
              minDays: 5,
            },
            {
              shippingMethodName: 'Standard',
              shippingTypeDescription: '3-5 business days',
              shippingPrice: 9,
              shippingTypeId: 1,
              isSelected: false,
              isEnabled: true,
              shippingId: 75071,
              deliveryDate: '12th',
              deliveryWeekDay: 'Tuesday,',
              deliveryMonth: 'Nov',
              maxDays: 5,
              minDays: 3,
            },
            {
              shippingMethodName: 'Express',
              shippingTypeDescription: '2-3 business days',
              shippingPrice: 17,
              shippingTypeId: 3,
              isSelected: false,
              isEnabled: true,
              shippingId: 38698,
              deliveryDate: '11th',
              deliveryWeekDay: 'Monday,',
              deliveryMonth: 'Nov',
              maxDays: 3,
              minDays: 2,
            },
            {
              shippingMethodName: 'Priority',
              shippingTypeDescription: '1 business day',
              shippingPrice: 25,
              shippingTypeId: 5,
              isSelected: false,
              isEnabled: true,
              shippingId: 74458,
              deliveryDate: '7th',
              deliveryWeekDay: 'Thursday,',
              deliveryMonth: 'Nov',
              maxDays: 1,
              minDays: 1,
            },
          ],
        },
      ],
      summaryView: [
        {
          id: 'GapRegularGroup',
          name: 'No Rush',
          itemCount: 1,
          deliveryBy: 'By Monday, Nov 18th',
          deliveryWeekDay: 'Monday,',
          deliveryDate: '18th',
          deliveryMonth: 'Nov',
          shippingPrice: 5,
        },
      ],
      currency: 'USD',
      isEasyEnrollEligible: false,
      errors: [],
      hasDropshipItems: false,
      has4101Error: false,
      markdownSubtotal: 29.95,
    },
  },
  session: {
    locale: 'en_US',
    language: 'en',
    brandCode: 'gp',
    twoCharBrandCode: 'gp',
    marketCode: 'US',
    preferredLocale: 'en_US',
    email: '<EMAIL>',
    nodeConfigEnv: 'stage-azure',
    isSessionExpired: false,
    isAuthenticatedReturningCustomer: false,
    isAuthenticatedNewCustomer: true,
    isRecognizedReturningCustomer: false,
    recognition_status: 'guest',
    api: {
      isFetching: false,
    },
    browserOIDCHost: 'https://secure-internal-azeus-ecom-api.live.stage.gaptechol.com',
  },
  viewTag: {
    lv2: '',
    event_name: 'Checkout',
    page_type: 'Checkout',
    bopis_enabled: false,
    bopis_order_type: 'shipItemsOnly',
    brand_code: 'GAP',
    brand_name: 'Gap',
    brand_number: '1',
    business_unit_abbr_name: 'GAP_US_OL',
    business_unit_description: 'Gap',
    business_unit_id: 1,
    brand_short_name: 'gp',
    channel: 'gp:checkout',
    country_code: 'US',
    checkout_type: 'Guest Customer',
    language_code: 'en_US',
    shipping_options: '7-9 business days',
    pfs_order_shipping_method: '',
    checkout_version: 'Checkout_MVP',
    tier_status: '',
    cardholder_status: 'BRNONE|GPNONE|ONNONE|ATNONE',
    customer_uuid: '2fc5b7b823834b75a9a325c7a7683419',
    recognition_status: 'guest',
    encrypted_customer_email: '2c1320a8ad09421ea8152c3c29c7015348d5e9efaba164ab72d9dd289f1559ffd4a84f694a45a731a6d3c0752fd9bd0f',
    encrypted_customer_email_mm: 'a02f6eedbfa3c9daa24773de5e409fc0e421409a',
    hashed_customer_email: '181d28a626fc6e735a78caf80ba67c9c178ed42228a06e5ddbffaba702db399b',
    mtl_member_status: 'false',
    category_preference: '',
    division_preference: '',
    product_id: ['731163'],
    product_cc_id: ['731163002'],
    product_brand: ['GAP'],
    brand_mix: 'GAP',
    product_category: [],
    product_name: ['Baby Organic Cotton Pull-On Slim Jeans'],
    product_quantity: [1],
    product_sku: ['7311630021824'],
    product_markdown_amount: ['0.00'],
    product_gross_retail: ['29.95'],
    product_gross_merchandise: ['29.95'],
    product_net_demand: ['29.95'],
    product_page_type: ['Standard PDP'],
    order_gross_merchandise: 29.95,
    product_dropship: ['false'],
    product_seller_id: ['1'],
    product_seller_name: ['GAP'],
  },
  draftOrderId: 'cd5227fe-502c-4d9f-93d4-370154baf78e',
  features: {
    experiments: {
      EXP_BARCLAY_US: true,
      EXP_BARCLAY_OCP_US: false,
      EXP_BARCLAY_SAVINGS: true,
      EXP_CANARY: false,
      EXP_DONATION_FEATURE: false,
      EXP_ROKT: true,
      EXP_GUEST_FREE_SHIPPING_BANNER: true,
      EXP_ENABLE_BOPIS_GIFT_CARD_CANADA: false,
      EXP_ENABLE_BOPIS_SMS_CANADA: false,
      EXP_PAYMENT_AUTOSELECT: false,
      EXP_ACCELERATED_US: true,
      EXP_ACCELERATED_CA: false,
      EXP_ACCELERATED_US_SP: true,
      EXP_LOYALTY_CA: false,
      EXP_LOYALTY_US: true,
      EXP_PAYMENTMODAL_US: true,
      EXP_PAYMENTMODAL_CA: false,
      EXP_ORDER_SUMMARY_US: false,
      EXP_ORDER_SUMMARY_CA: false,
      EXP_VAULT_SERVICE: true,
      EXP_MARKDOWN: true,
      EXP_OPTIMIZELY_MIGRATION_TEST: true,
      EXP_ACCELERATED_PPE: false,
    },
    experimentVariables: {
      AFTERPAY_MIN_ORDER_AMT: 35,
      AFTERPAY_MAX_ORDER_AMT: 1000,
      GUEST_FREE_SHIPPING_THRESHOLD: 50,
    },
    killSwitches: {
      ENABLE_AFTERPAY: true,
      ENABLE_AFTER_PAY_BACK_ORDER: false,
      ENABLE_BOPIS_FLAG: true,
      ENABLE_BOPIS_GIFTCARD: true,
      ENABLE_BOPIS_OCP: false,
      ENABLE_BOPIS_PICKUP_PANEL: true,
      ENABLE_BOPIS_SHOPPINGBAG_PANEL: false,
      ENABLE_BOPIS_SMS_FLAG: true,
      ENABLE_GOOGLE_AUTOCOMPLETE: true,
      ENABLE_PAYPAL_BUTTON: true,
      ENABLE_PAYPAL_BUTTON_CANADA: true,
      ENABLE_HUBBOX: true,
      ENABLE_SHIPPING_AUTOSELECT: true,
    },
    localizedText: {
      ENABLE_LOCALIZED_TEXT: true,
      bopisTitle: 'Hello',
      shippingMethodFooterText: 'shipping pillview method ENUS',
      myRewardsPanelFooterText: 'Redeemed rewards can’t exceed the subtotal of your order (before tax and shipping).',
    },
    segmentList: ['roktX', 'gfsbX', 'accusX', 'accusspX', 'loyaltyus', 'ordersummaryusC', 'vscuX', 'COMTX', 'accppeX'],
  },
} as unknown as DraftOrder;
