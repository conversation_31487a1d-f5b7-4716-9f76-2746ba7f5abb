import React from 'react';
import { Meta } from '@storybook/react';
import { StarRatings } from '@ecom-next/core/fabric/star-ratings';
import { Link } from '@ecom-next/core/components/fabric/link';
import { ProductImage } from '@ecom-next/core/fabric/product-image';
import StoryWrapper, { Props } from '../../../StoryWrapper';
import Header from './components/Header';
import Price from './components/Price';
import Color from './components/Color';
import FitSelector from './components/FitSelector';
import SizeSelector from './components/SizeSelector';
import Fulfillment from './components/Fulfillment';
import AddToBagButton from './components/AddToBagButton';

type StoryProps = Props & { label: string };

const meta: Meta = {
  title: 'Fabric DS/Demo/BuyBoxDemo',
  parameters: {
    controls: { expanded: true },
  },
  argTypes: {},
};

export default meta;

export const BuyBox = {
  args: {
    brand: 'at',
  },
  parameters: {
    controls: {
      exclude: ['label', 'className', 'isLoading', 'isDisabled', 'isCautionState', 'cautionReason'],
    },
  },
  render: (props: StoryProps) => {
    return (
      <StoryWrapper
        {...props}
        StoryComponent={() => (
          <div className='fds_buy-box flex flex-col items-center justify-center gap-8 bg-gray-100 p-6 sm:flex-row sm:flex-wrap'>
            <div className='fds_buy-box-container flex w-[435px] flex-col items-start justify-center rounded-sm bg-white p-4 shadow-md'>
              <Header />
              <ProductImage
                className='fds_buy-box-product-image'
                height='520px'
                id='demo-img'
                imageUrl='https://cdn.media.amplience.net/i/gapprod/SP257977_imgL_DESK'
                width='403px'
              />
              <div className='flex w-full content-start justify-between'>
                <Price
                  className='fds_buy-box-container-product__price'
                  originalPrice='$00.00'
                  discountPercentage='50% off'
                  salePrice='$29.97'
                  subText='50% off top gifts: price as marked'
                />
                <div className='fds_buy-box-ratings-container flex flex-col items-end'>
                  <StarRatings ratingSize='medium' showRatingValue={false} ratingValue={4.5} />
                  <div className='fds_buy-box-ratings-container__rating-number cursor-pointer'>385</div>
                </div>
              </div>
              <Color />
              <FitSelector />
              <SizeSelector />
              {/*<LengthSelector />*/}
              <div className='fds_buy-box-items-left'>Only a few left</div>
              <AddToBagButton />
              <Fulfillment />
              <Link href='#' className='fds_buy-box-shipping' kind='inline'>
                Shipping & returns
              </Link>
            </div>
          </div>
        )}
      />
    );
  },
};
