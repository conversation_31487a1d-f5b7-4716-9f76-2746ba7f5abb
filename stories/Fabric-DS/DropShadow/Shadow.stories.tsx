import React from 'react';
import StoryWrapper, { Props } from '../../StoryWrapper';

const ShadowWrapper = (props: Props & { isCrossbrand: boolean; name: string; shadowType: string }) => {
  const brandFont = props.isCrossbrand ? 'font-crossbrand' : 'font-brand';

  return <StoryWrapper {...props} StoryComponent={() => <div className={`p-4 ${props.shadowType} ${brandFont}`}>{props.name}</div>} />;
};

const meta = {
  title: 'Fabric DS/DropShadow',
  component: ShadowWrapper,
  argTypes: {
    isCrossbrand: {
      control: 'boolean',
      description: 'Toggle to view the Cross Brand style.',
      table: {
        defaultValue: { summary: false },
        category: 'Brand',
      },
    },
    shadowType: {
      control: {
        type: 'select',
      },
      options: ['shadow-high', 'shadow-mid', 'shadow-low'],
      description: 'Type of box shadow.',
      defaultValue: { summary: 'shadow-high' },
    },
    name: {
      control: { type: 'text' },
      description: 'Text to display inside the box.',
      defaultValue: { summary: 'Box with Shadow' },
    },
  },
};

export default meta;

export const LowShadow = {
  args: {
    shadowType: 'shadow-low',
    brand: 'at',
    name: 'Box with Low Shadow',
  },
  render: (props: Props & { isCrossbrand: boolean }) => {
    return (
      <div className='bg-white p-8'>
        <ShadowWrapper {...props} shadowType='shadow-low' name='Box with Low Shadow' />
      </div>
    );
  },
};

export const MidShadow = {
  args: {
    shadowType: 'shadow-mid',
    name: 'Box with Mid Shadow',
    brand: 'at',
  },
  render: (props: Props & { isCrossbrand: boolean }) => {
    return (
      <div className='bg-white p-8'>
        <ShadowWrapper {...props} shadowType='shadow-mid' name='Box with Mid Shadow' />
      </div>
    );
  },
};

export const HighShadow = {
  args: {
    brand: 'at',
    shadowType: 'shadow-high',
    name: 'Box with Shadow',
  },
  render: (props: Props & { isCrossbrand: boolean }) => {
    return (
      <div className='bg-white p-8'>
        <ShadowWrapper {...props} shadowType='shadow-high' name='Box with High Shadow' />
      </div>
    );
  },
};

export const Showcase = {
  args: {
    brand: 'at',
    isCrossbrand: false,
  },
  parameters: {
    controls: {
      exclude: ['shadowType', 'name'],
    },
  },
  tags: ['visual:check:allBrands', 'visual:check:cb'],
  render: (props: Props & { isCrossbrand: boolean }) => {
    return (
      <div className='flex bg-white'>
        <div className='w-1/3 p-8'>
          <ShadowWrapper {...props} shadowType='shadow-low' name='Box with Low Shadow' />
        </div>
        <div className='w-1/3 p-8'>
          <ShadowWrapper {...props} shadowType='shadow-mid' name='Box with Mid Shadow' />
        </div>
        <div className='w-1/3 p-8'>
          <ShadowWrapper {...props} shadowType='shadow-high' name='Box with High Shadow' />
        </div>
      </div>
    );
  },
};
