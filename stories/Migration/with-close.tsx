import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { DrawerProps } from '@ecom-next/core/migration/drawer/types';
import { Button as ButtonComponent } from '@ecom-next/core/migration/button';

type WithCloseProps = {
  closeIcon: boolean;
  isLengthyContent: boolean;
  isOpen: boolean;
} & Partial<DrawerProps>;

export type InjectedProps = {
  isOpen: boolean;
  onClose: () => void;
} & Partial<DrawerProps>;

const withClose =
  <P extends InjectedProps = InjectedProps>(WrappedComponent: React.ComponentType<P>) =>
  // eslint-disable-next-line react/display-name
  (baseArgs: WithCloseProps): JSX.Element => {
    const { isOpen } = baseArgs;
    const [open, toggleDrawer] = useState(isOpen);
    const openDrawer = (): void => toggleDrawer(true);
    const closeDrawer = (): void => toggleDrawer(false);

    // have to do this because of this TS bug: https://github.com/Microsoft/TypeScript/issues/28938
    const wrapperProps = {
      ...baseArgs,
      isOpen: open,
      onClose: closeDrawer,
    };

    return (
      <div>
        <ButtonComponent kind='primary' fullWidth={true} onClick={openDrawer}>
          Open Drawer
        </ButtonComponent>
        <WrappedComponent {...(wrapperProps as unknown as P)} />
      </div>
    );
  };

export default withClose;

withClose.propTypes = {
  isOpen: PropTypes.bool,
  closeIcon: PropTypes.bool,
  isLengthyContent: PropTypes.bool,
};

withClose.defaultProps = {
  isOpen: false,
  closeIcon: false,
  isLengthyContent: false,
};
