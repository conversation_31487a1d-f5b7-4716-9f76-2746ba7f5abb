SERVER_PORT = '3000'
TARGET_ENV = 'stage'
PMCS_SERVICE_URL = 'https://browse-api-nginx-cache-preview.aks.prod.azeus.gaptech.com/pmcs'
NAVIGATION_SERVICE_URL = 'https://browse-api-nginx-cache-preview.aks.prod.azeus.gaptech.com'
ECOM_CLIENT_API_BASE_URL_US = 'https://ecom-api.stage.gaptechol.com'
ECOM_CLIENT_API_BASE_URL_CA = 'https://ecom-api.stage.gaptechol.ca'
ECOM_CLIENT_API_BASE_URL_FACTORY_US = 'https://ecom-api.stage.factory-gaptechol.com'
ECOM_CLIENT_API_BASE_URL_FACTORY_CA = 'https://ecom-api.stage.factory-gaptechol.ca'
ECOM_SERVER_API_BASE_URL = 'https://internal-azeus-ecom-api.live.stage.gaptechol.com'
SECURE_ECOM_CLIENT_API_BASE_URL_US = 'https://ecom-api.stage.gaptechol.com'
SECURE_ECOM_CLIENT_API_BASE_URL_CA = 'https://ecom-api.stage.gaptechol.ca'
SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_US = 'https://ecom-api.stage.factory-gaptechol.com'
SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_CA = 'https://ecom-api.stage.factory-gaptechol.ca'
APIGEE_INTERNAL_URL = 'https://stage.api.azeus.gaptech.com'
SERVER_OPTIMIZELY_CONFIG_URL = 'https://cdn.optimizely.com/public/6529002483/s/10738892798_10738892798.json'
SHOPPING_BAG_SERVICE_URL = 'https://secure-internal-azeus-ecom-api.live.stage.gaptechol.com/commerce/shopping-bags'
NEW_EMAIL_REGISTRATION_SERVICE_URL = 'https://secure-internal-azeus-ecom-api.live.stage.gaptechol.com/commerce/communication-preference/v2/subscriptions/email'
NEW_SMS_REGISTRATION_SERVICE_URL = 'https://stage.api.azeus.gaptech.com/commerce/communication-preference/v2/subscriptions/sms'
ASSET_CLUSTER_URL = 'https://internal-azeus.brol.preview.app.prod.gaptecholapps.com'
STORE_SERVICE_URL = 'https://browse-api-nginx-cache-preview.aks.prod.azeus.gaptech.com/storespage'
NEXT_TELEMETRY_DISABLED = 1
BLOOMREACH_URL = 'https://core.dxpapi.com'
SEARCH_PAGE_URL = 'https://search-page-preview.apps.cfcommerce.prod.azeus.gaptech.com'
CATALOG_INTERNAL_API_BASE_URL = 'https://ws-catalog-api-service.prod.azeus.gaptech.com'
UJT = 'true'