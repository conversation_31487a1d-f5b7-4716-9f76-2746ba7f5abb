# Playwright User Journey Tests

## Setup

```bash
cd user-journeys
npm i
```

## Running tests

Run on local env

```bash
npm start
```

Use parameters to change brand, market, env, breakpoint and what tests are run, for example:

```bash
npm start --brand=at --market=us --env=local --breakpoint=desktop -- header.spec.ts
```

or

```bash
npm start --brand=at --market=ca --env=stage --breakpoint=mobile -- utility.spec.ts
```

[TIP]
The default parameters are:

- brand=at
- market=us
- breakpoint=desktop
- env=local

Run on stage env

```bash
npm run sitewide-at-us-desktop-stage
```

Run on preview env

```bash
npm run sitewide-at-us-desktop-preview
```

Run on prod env

```bash
npm run sitewide-at-us-desktop-prod
```

## Tips

By defult the scripts run a headless chrome browser. To launch a browser to see the tests run you need to add -- --headed to the end of the npm run script.

Example:

```bash
npm run sitewide-at-us-desktop-prod -- --headed
```

By defult the scripts will run all tests in fully paralell mode. To run a single test, add ".only" after "test" in the spec.tc file

Example:

```
test.only(`${brand}-${market}-${breakpoint}-${env}: TOP-NAV - Go to Gap home page using...
```

## Running tests on LambdaTest WIP (Not Fully Configured)

- Currrenty LambdaTest will only run on production URLs.
- We plan to run on LambdaTest with multiple browsers & devices once a release is configured for Canary deployments.
- The existing home page test will fail becasue it is testing the AKS URL with minimal existing features.
- When a brand is productionalized the cmd below will run tests on LambdaTest with LT_USERNAME & LT_ACCESS_KEY in env variables.

```bash
BRAND="br" npx playwright test --project="chrome:latest:MacOS Ventura@lambdatest" home
```

## Viewing traces generated by failed tests

Traces can be downloaded from GitHub Actions and viewed in the [online traceviewer](https://trace.playwright.dev/) | [Playwright Trace Viewer Docs](https://playwright.dev/docs/trace-viewer#using-traceplaywrightdev)

