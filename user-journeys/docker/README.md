# Steps to build the Playwright Docker image

1. Download the Playwright repository [here](https://github.com/microsoft/playwright)
2. Put all files in this directory in the downloaded repo in the folder `utils/docker/`
3. Login in to the Gap Artifactory docker repository `docker login -u $ARTIFACTORY_USR -p $ARTIFACTORY_PSW https://gapinc-docker-repo.jfrog.io/`
4. Run the follwing commands, but replace the version with the version you are updating to. `./utils/docker/build.sh --amd64 noble playwright:localbuild-noble && docker tag playwright:localbuild-noble gapinc-docker-repo.jfrog.io/playwright:v1.47.2-noble && docker push gapinc-docker-repo.jfrog.io/playwright:v1.47.2-noble`
