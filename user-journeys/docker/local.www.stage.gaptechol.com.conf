# cache settings
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=dummy_cache:10m max_size=1g inactive=60m;

# log formats and set up
log_format main escape=json '{'
'"src":"$remote_addr", "src_ip":"$realip_remote_addr", "user":"$remote_user", '
'"time_local":"$time_local", "protocol":"$server_protocol", "status":"$status", "upstream_status":"$upstream_status", '
'"bytes_out":"$body_bytes_sent", "bytes_in":"$upstream_response_length", '
'"nginx_version":"$nginx_version", "trace_id":"$http_x_b3_traceid", "span_id":"$http_x_b3_spanid",'
'"parent_span_id":"$http_x_b3_parentspanid","response_time":"$upstream_response_time", "req_len": "$request_length", '
'"request_time":"$request_time", "upstream_cache_status":"$upstream_cache_status", '
'"uri":"$uri", "request_uri":"$request_uri", "upstream_uri":"$upstream_addr", '
'"request_host":"$http_request_host", "upstream_status":"$upstream_status", "http_host":"$http_host", '
'}';
access_log /dev/stdout main;
error_log /dev/stderr;

## header sizes buffers
large_client_header_buffers 4 16k;
client_header_buffer_size 4k;
# proxy buffers
proxy_buffer_size 16384k;
proxy_buffers 4 16384k;
proxy_busy_buffers_size 16384k;
proxy_temp_file_write_size 16384k;
proxy_max_temp_file_size 16384k;
# fast fail timeouts
proxy_socket_keepalive on;
proxy_http_version 1.1;
proxy_set_header Connection "";
proxy_ssl_server_name on;

server {
    listen 443 ssl;
    server_name secure-atol.stage.gaptechol.com;
    ssl_certificate /tmp/certs/server.crt;
    ssl_certificate_key /tmp/certs/server.key;

    # basic settings for incoming and outgoing requests
    sendfile on;
    gzip on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 60s;
    reset_timedout_connection on;

    location / {
        proxy_pass http://secure-atol.stage.gaptechol.com:3000;
        proxy_set_header 'request-host' 'secure-atol.stage.gaptechol.com';
    }
}

server {
    listen 443 ssl;
    server_name atol.stage.gaptechol.com;
    ssl_certificate /tmp/certs/server.crt;
    ssl_certificate_key /tmp/certs/server.key;

    # basic settings for incoming and outgoing requests
    sendfile on;
    gzip on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 60s;
    reset_timedout_connection on;

    location / {
        proxy_pass http://atol.stage.gaptechol.com:3000;
        proxy_set_header 'request-host' 'atol.stage.gaptechol.com';
    }
}

server {
    listen 443 ssl;
    ssl_certificate /tmp/certs/server.crt;
    ssl_certificate_key /tmp/certs/server.key;

    # basic settings for incoming and outgoing requests
    sendfile on;
    gzip on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 60s;
    reset_timedout_connection on;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header 'request-host' 'atol.stage.gaptechol.com';
    }
}