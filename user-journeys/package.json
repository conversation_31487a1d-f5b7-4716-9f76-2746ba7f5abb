{"name": "playwright-test-suite", "version": "1.0.0", "engines": {"node": "^18 || ^20 || ^22"}, "description": "Project to run PlayWright tests on Next.JS", "scripts": {"test": "npx playwright test --config=./playwright.config.ts", "start": "BRAND=${npm_config_brand:-at} MARKET=${npm_config_market:-us} BREAKPOINT=${npm_config_breakpoint:-desktop} ENV=${npm_config_env:-local} npx playwright test${npm_config_breakpoint:-desktop} --workers=99%", "all-at-us-desktop-local": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=local npx playwright test", "all-at-us-mobile-local": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=local npx playwright test --grep-invert @skip-mobile sitewide/", "category-at-us-desktop-local": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=local npx playwright test category/", "category-at-us-mobile-local": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=local npx playwright test category/", "checkout-at-us-desktop-local": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=local npx playwright test checkout/", "checkout-at-us-mobile-local": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=local npx playwright test checkout/", "my-account-at-us-desktop-local": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=local npx playwright test my-account/", "my-account-at-us-mobile-local": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=local npx playwright test my-account/", "product-at-us-desktop-local": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=local npx playwright test product/", "product-at-us-mobile-local": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=local npx playwright test product/", "search-at-us-desktop-local": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=local npx playwright test search/", "search-at-us-mobile-local": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=local npx playwright test search/", "shopping-bag-at-us-desktop-local": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=local npx playwright test shopping-bag/", "shopping-bag-at-us-mobile-local": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=local npx playwright test shopping-bag/", "sitewide-at-us-desktop-local": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=local npx playwright test sitewide/", "sitewide-at-us-mobile-local": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=local npx playwright test sitewide/", "utility-at-us-desktop-local": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=local npx playwright test utility/", "utility-at-us-mobile-local": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=local npx playwright test utility/", "all-local": "BRANDS=(at br gp on) MARKETS=(us ca) BREAKPOINTS=(mobile desktop) && \nfor BRAND in ${BRANDS[@]}; do \nfor MARKET in ${MARKETS[@]}; do \nfor BREAKPOINT in ${BREAKPOINTS[@]}; do \nBRAND=$BRAND MARKET=$MARKET BREAKPOINT=$BREAKPOINT ENV=local START_SERVER=false npx playwright test --workers=80%$BREAKPOINT; \ndone; \ndone; \ndone;", "sitewide-at-us-desktop-akstest": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=aksTest npx playwright test --workers=1 sitewide/", "sitewide-at-us-mobile-akstest": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=aksTest BREAKPOINT=mobile npx playwright test\"Mobile Chrome\" --grep-invert @skip-mobile --workers=1 sitewide/", "sitewide-at-us-desktop-aksstage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=aksStage npx playwright test sitewide/", "accessibility-at-us-desktop-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-at-us-mobile-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-at-ca-desktop-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-at-ca-mobile-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-br-us-desktop-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-br-us-mobile-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-br-ca-desktop-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-br-ca-mobile-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-brf-ca-desktop-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-brf-ca-mobile-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-gp-us-desktop-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-gp-us-mobile-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-gp-ca-desktop-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-gp-ca-mobile-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-on-us-desktop-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-on-us-mobile-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-on-ca-desktop-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "accessibility-on-ca-mobile-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test --retries=0 accessibility.spec.ts", "category-at-us-desktop-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test category/", "category-at-us-mobile-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test category/", "category-at-ca-desktop-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test category/", "category-at-ca-mobile-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test category/", "category-br-us-desktop-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test category/", "category-br-us-mobile-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test category/", "category-br-ca-desktop-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test category/", "category-br-ca-mobile-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test category/", "category-brf-us-desktop-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test category/", "category-brf-us-mobile-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test category/", "category-brf-ca-desktop-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test category/", "category-brf-ca-mobile-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test category/", "category-gp-us-desktop-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test category/", "category-gp-us-mobile-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test category/", "category-gp-ca-desktop-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test category/", "category-gp-ca-mobile-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test category/", "category-gpf-us-desktop-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test category/", "category-gpf-us-mobile-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test category/", "category-on-us-desktop-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test category/", "category-on-us-mobile-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test category/", "category-on-ca-desktop-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test category/", "category-on-ca-mobile-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test category/", "checkout-at-us-desktop-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=stage CHECKOUT_BYPASS=true npx playwright test checkout/", "checkout-at-us-mobile-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=stage CHECKOUT_BYPASS=true npx playwright test checkout/", "checkout-brf-ca-desktop-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test checkout/", "checkout-brf-ca-mobile-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test checkout/", "checkout-gpf-us-desktop-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test checkout/", "checkout-gpf-us-mobile-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test checkout/", "checkout-on-ca-desktop-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test checkout/", "checkout-on-ca-mobile-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test checkout/", "my-account-at-us-desktop-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test my-account/", "my-account-at-us-mobile-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test my-account/", "my-account-at-ca-desktop-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test my-account/", "my-account-at-ca-mobile-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test my-account/", "my-account-br-us-desktop-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test my-account/", "my-account-br-us-mobile-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test my-account/", "my-account-br-ca-desktop-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test my-account/", "my-account-br-ca-mobile-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test my-account/", "my-account-brf-ca-desktop-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test my-account/", "my-account-brf-ca-mobile-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test my-account/", "my-account-gp-us-desktop-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test my-account/", "my-account-gp-us-mobile-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test my-account/", "my-account-gp-ca-desktop-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test my-account/", "my-account-gp-ca-mobile-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test my-account/", "my-account-gpf-us-desktop-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test my-account/", "my-account-gpf-us-mobile-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test my-account/", "my-account-on-us-desktop-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test my-account/", "my-account-on-us-mobile-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test my-account/", "my-account-on-ca-desktop-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test my-account/", "my-account-on-ca-mobile-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test my-account/", "product-at-us-desktop-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test product/", "product-at-us-mobile-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test product/", "product-at-ca-desktop-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test product/", "product-at-ca-mobile-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test product/", "product-br-us-desktop-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test product/", "product-br-us-mobile-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test product/", "product-br-ca-desktop-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test product/", "product-br-ca-mobile-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test product/", "product-brf-us-desktop-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test product/", "product-brf-us-mobile-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test product/", "product-brf-ca-desktop-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test product/", "product-brf-ca-mobile-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test product/", "product-gp-us-desktop-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test product/", "product-gp-us-mobile-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test product/", "product-gp-ca-desktop-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test product/", "product-gp-ca-mobile-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test product/", "product-gpf-us-desktop-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test product/", "product-gpf-us-mobile-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test product/", "product-on-us-desktop-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test product/", "product-on-us-mobile-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test product/", "product-on-ca-desktop-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test product/", "product-on-ca-mobile-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test product/", "search-at-us-desktop-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test search/", "search-at-us-mobile-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test search/", "search-at-ca-desktop-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test search/", "search-at-ca-mobile-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test search/", "search-br-us-desktop-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test search/", "search-br-us-mobile-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test search/", "search-br-ca-desktop-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test search/", "search-br-ca-mobile-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test search/", "search-brf-us-desktop-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test search/", "search-brf-us-mobile-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test search/", "search-brf-ca-desktop-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test search/", "search-brf-ca-mobile-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test search/", "search-gp-us-desktop-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test search/", "search-gp-us-mobile-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test search/", "search-gp-ca-desktop-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test search/", "search-gp-ca-mobile-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test search/", "search-gpf-us-desktop-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test search/", "search-gpf-us-mobile-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test search/", "search-on-us-desktop-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test search/", "search-on-us-mobile-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test search/", "search-on-ca-desktop-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test search/", "search-on-ca-mobile-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test search/", "shopping-bag-gp-us-desktop-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test shopping-bag/", "shopping-bag-gp-us-mobile-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test shopping-bag/", "shopping-bag-gp-ca-desktop-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test shopping-bag/", "shopping-bag-gp-ca-mobile-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test shopping-bag/", "shopping-bag-gpf-us-desktop-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test shopping-bag/", "shopping-bag-gpf-us-mobile-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test shopping-bag/", "shopping-bag-brf-ca-desktop-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test shopping-bag/", "shopping-bag-brf-ca-mobile-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test shopping-bag/", "sitewide-at-us-desktop-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test sitewide/", "sitewide-at-us-mobile-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test sitewide/", "sitewide-at-ca-desktop-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test sitewide/", "sitewide-at-ca-mobile-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test sitewide/", "sitewide-br-us-desktop-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test sitewide/", "sitewide-br-us-mobile-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test sitewide/", "sitewide-br-ca-desktop-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test sitewide/", "sitewide-br-ca-mobile-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test sitewide/", "sitewide-brf-us-desktop-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test sitewide/", "sitewide-brf-us-mobile-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test sitewide/", "sitewide-brf-ca-desktop-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test sitewide/", "sitewide-brf-ca-mobile-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test sitewide/", "sitewide-gp-us-desktop-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test sitewide/", "sitewide-gp-us-mobile-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test sitewide/", "sitewide-gp-ca-desktop-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test sitewide/", "sitewide-gp-ca-mobile-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test sitewide/", "sitewide-gpf-us-desktop-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test sitewide/", "sitewide-gpf-us-mobile-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test sitewide/", "sitewide-on-us-desktop-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test sitewide/", "sitewide-on-us-mobile-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test sitewide/", "sitewide-on-ca-desktop-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test sitewide/", "sitewide-on-ca-mobile-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test sitewide/", "utility-at-us-desktop-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test  utility/", "utility-at-us-mobile-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test utility/", "utility-at-ca-desktop-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test utility/", "utility-at-ca-mobile-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test utility/", "utility-br-us-desktop-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test utility/", "utility-br-us-mobile-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test utility/", "utility-br-ca-desktop-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test utility/", "utility-br-ca-mobile-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test utility/", "utility-brf-us-desktop-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test utility/", "utility-brf-us-mobile-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test utility/", "utility-brf-ca-desktop-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test utility/", "utility-brf-ca-mobile-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test utility/", "utility-gp-us-desktop-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test utility/", "utility-gp-us-mobile-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test utility/", "utility-gp-ca-desktop-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test utility/", "utility-gp-ca-mobile-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test utility/", "utility-gpf-us-desktop-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test utility/", "utility-gpf-us-mobile-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test utility/", "utility-on-us-desktop-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test utility/", "utility-on-us-mobile-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test utility/", "utility-on-ca-desktop-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test utility/", "utility-on-ca-mobile-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test utility/", "all-store-locator-stage": "npm-run-all -l -c store-locator-*-stage", "store-locator-at-us-desktop-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-at-us-mobile-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-at-ca-desktop-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-at-ca-mobile-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-br-us-desktop-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-br-us-mobile-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-br-ca-desktop-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-br-ca-mobile-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-brf-us-desktop-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-brf-us-mobile-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-brf-ca-desktop-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-brf-ca-mobile-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gp-us-desktop-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gp-us-mobile-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gp-ca-desktop-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gp-ca-mobile-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gpf-us-desktop-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gpf-us-mobile-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gpf-ca-desktop-stage": "BRAND=gpf MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gpf-ca-mobile-stage": "BRAND=gpf MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-on-us-desktop-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-on-us-mobile-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-on-ca-desktop-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-on-ca-mobile-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=stage npx playwright test store-locator/ --pass-with-no-tests", "visual-at-us-desktop-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=stage VISUAL=true npx playwright test visual", "visual-at-us-mobile-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=stage VISUAL=true npx playwright test visual", "visual-at-ca-desktop-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=stage VISUAL=true npx playwright test visual", "visual-at-ca-mobile-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=stage VISUAL=true npx playwright test visual", "visual-br-us-desktop-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=stage VISUAL=true npx playwright test visual", "visual-br-us-mobile-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=stage VISUAL=true npx playwright test visual", "visual-br-ca-desktop-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=stage VISUAL=true npx playwright test visual", "visual-br-ca-mobile-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=stage VISUAL=true npx playwright test visual", "visual-brf-us-desktop-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=stage VISUAL=true npx playwright test visual", "visual-brf-us-mobile-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=stage VISUAL=true npx playwright test visual", "visual-brf-ca-desktop-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=stage VISUAL=true npx playwright test visual", "visual-brf-ca-mobile-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=stage VISUAL=true npx playwright test visual", "visual-gp-us-desktop-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=stage VISUAL=true npx playwright test visual", "visual-gp-us-mobile-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=stage VISUAL=true npx playwright test visual", "visual-gp-ca-desktop-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=stage VISUAL=true npx playwright test visual", "visual-gp-ca-mobile-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=stage VISUAL=true npx playwright test visual", "visual-gpf-us-desktop-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=stage VISUAL=true npx playwright test visual", "visual-gpf-us-mobile-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=stage VISUAL=true npx playwright test visual", "visual-on-us-desktop-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=stage VISUAL=true npx playwright test visual", "visual-on-us-mobile-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=stage VISUAL=true npx playwright test visual", "visual-on-ca-desktop-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=stage VISUAL=true npx playwright test visual", "visual-on-ca-mobile-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=stage VISUAL=true npx playwright test visual", "category-at-us-desktop-wip-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test category/", "category-at-us-mobile-wip-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test category/", "category-at-ca-desktop-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test category/", "category-at-ca-mobile-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test category/", "category-br-us-desktop-wip-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test category/", "category-br-us-mobile-wip-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test category/", "category-br-ca-desktop-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test category/", "category-br-ca-mobile-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test category/", "category-brf-us-desktop-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test category/", "category-brf-us-mobile-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test category/", "category-brf-ca-desktop-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test category/", "category-brf-ca-mobile-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test category/", "category-gp-us-desktop-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test category/", "category-gp-us-mobile-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test category/", "category-gp-ca-desktop-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test category/", "category-gp-ca-mobile-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test category/", "category-gpf-us-desktop-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test category/", "category-gpf-us-mobile-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test category/", "category-on-us-desktop-wip-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test category/", "category-on-us-mobile-wip-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test category/", "category-on-ca-desktop-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test category/", "category-on-ca-mobile-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test category/", "checkout-at-us-desktop-wip-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=wip-stage CHECKOUT_BYPASS=true npx playwright test checkout/", "checkout-at-us-mobile-wip-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=wip-stage CHECKOUT_BYPASS=true npx playwright test checkout/", "checkout-brf-ca-desktop-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test checkout/", "checkout-brf-ca-mobile-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test checkout/", "checkout-gpf-us-desktop-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test checkout/", "checkout-gpf-us-mobile-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test checkout/", "checkout-on-ca-desktop-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test checkout/", "checkout-on-ca-mobile-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test checkout/", "my-account-at-us-desktop-wip-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test my-account/", "my-account-at-us-mobile-wip-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test my-account/", "my-account-at-ca-desktop-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test my-account/", "my-account-at-ca-mobile-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test my-account/", "my-account-br-us-desktop-wip-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test my-account/", "my-account-br-us-mobile-wip-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test my-account/", "my-account-br-ca-desktop-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test my-account/", "my-account-br-ca-mobile-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test my-account/", "my-account-brf-ca-desktop-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test my-account/", "my-account-brf-ca-mobile-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test my-account/", "my-account-gp-us-desktop-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test my-account/", "my-account-gp-us-mobile-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test my-account/", "my-account-gp-ca-desktop-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test my-account/", "my-account-gp-ca-mobile-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test my-account/", "my-account-gpf-us-desktop-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test my-account/", "my-account-gpf-us-mobile-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test my-account/", "my-account-on-us-desktop-wip-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test my-account/", "my-account-on-us-mobile-wip-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test my-account/", "my-account-on-ca-desktop-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test my-account/", "my-account-on-ca-mobile-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test my-account/", "product-at-us-desktop-wip-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test product/", "product-at-us-mobile-wip-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test product/", "product-at-ca-desktop-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test product/", "product-at-ca-mobile-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test product/", "product-br-us-desktop-wip-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test product/", "product-br-us-mobile-wip-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test product/", "product-br-ca-desktop-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test product/", "product-br-ca-mobile-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test product/", "product-brf-us-desktop-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test product/", "product-brf-us-mobile-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test product/", "product-brf-ca-desktop-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test product/", "product-brf-ca-mobile-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test product/", "product-gp-us-desktop-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test product/", "product-gp-us-mobile-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test product/", "product-gp-ca-desktop-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test product/", "product-gp-ca-mobile-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test product/", "product-gpf-us-desktop-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test product/", "product-gpf-us-mobile-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test product/", "product-on-us-desktop-wip-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test product/", "product-on-us-mobile-wip-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test product/", "product-on-ca-desktop-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test product/", "product-on-ca-mobile-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test product/", "search-at-us-desktop-wip-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test search/", "search-at-us-mobile-wip-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test search/", "search-at-ca-desktop-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test search/", "search-at-ca-mobile-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test search/", "search-br-us-desktop-wip-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test search/", "search-br-us-mobile-wip-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test search/", "search-br-ca-desktop-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test search/", "search-br-ca-mobile-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test search/", "search-brf-us-desktop-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test search/", "search-brf-us-mobile-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test search/", "search-brf-ca-desktop-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test search/", "search-brf-ca-mobile-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test search/", "search-gp-us-desktop-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test search/", "search-gp-us-mobile-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test search/", "search-gp-ca-desktop-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test search/", "search-gp-ca-mobile-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test search/", "search-gpf-us-desktop-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test search/", "search-gpf-us-mobile-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test search/", "search-on-us-desktop-wip-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test search/", "search-on-us-mobile-wip-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test search/", "search-on-ca-desktop-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test search/", "search-on-ca-mobile-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test search/", "shopping-bag-gp-us-desktop-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test shopping-bag/", "shopping-bag-gp-us-mobile-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test shopping-bag/", "shopping-bag-gp-ca-desktop-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test shopping-bag/", "shopping-bag-gp-ca-mobile-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test shopping-bag/", "shopping-bag-gpf-us-desktop-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test shopping-bag/", "shopping-bag-gpf-us-mobile-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test shopping-bag/", "shopping-bag-brf-ca-desktop-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test shopping-bag/", "shopping-bag-brf-ca-mobile-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test shopping-bag/", "sitewide-at-us-desktop-wip-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test sitewide/", "sitewide-at-us-mobile-wip-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test sitewide/", "sitewide-at-ca-desktop-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test sitewide/", "sitewide-at-ca-mobile-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test sitewide/", "sitewide-br-us-desktop-wip-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test sitewide/", "sitewide-br-us-mobile-wip-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test sitewide/", "sitewide-br-ca-desktop-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test sitewide/", "sitewide-br-ca-mobile-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test sitewide/", "sitewide-brf-us-desktop-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test sitewide/", "sitewide-brf-us-mobile-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test sitewide/", "sitewide-brf-ca-desktop-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test sitewide/", "sitewide-brf-ca-mobile-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test sitewide/", "sitewide-gp-us-desktop-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test sitewide/", "sitewide-gp-us-mobile-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test sitewide/", "sitewide-gp-ca-desktop-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test sitewide/", "sitewide-gp-ca-mobile-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test sitewide/", "sitewide-gpf-us-desktop-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test sitewide/", "sitewide-gpf-us-mobile-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test sitewide/", "sitewide-on-us-desktop-wip-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test sitewide/", "sitewide-on-us-mobile-wip-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test sitewide/", "sitewide-on-ca-desktop-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test sitewide/", "sitewide-on-ca-mobile-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test sitewide/", "utility-at-us-desktop-wip-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test  utility/", "utility-at-us-mobile-wip-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test utility/", "utility-at-ca-desktop-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test utility/", "utility-at-ca-mobile-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test utility/", "utility-br-us-desktop-wip-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test utility/", "utility-br-us-mobile-wip-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test utility/", "utility-br-ca-desktop-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test utility/", "utility-br-ca-mobile-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test utility/", "utility-brf-us-desktop-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test utility/", "utility-brf-us-mobile-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test utility/", "utility-brf-ca-desktop-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test utility/", "utility-brf-ca-mobile-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test utility/", "utility-gp-us-desktop-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test utility/", "utility-gp-us-mobile-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test utility/", "utility-gp-ca-desktop-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test utility/", "utility-gp-ca-mobile-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test utility/", "utility-gpf-us-desktop-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test utility/", "utility-gpf-us-mobile-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test utility/", "utility-on-us-desktop-wip-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test utility/", "utility-on-us-mobile-wip-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test utility/", "utility-on-ca-desktop-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test utility/", "utility-on-ca-mobile-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test utility/", "all-store-locator-wip-stage": "npm-run-all -l -c store-locator-*-wip-stage", "store-locator-at-us-desktop-wip-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-at-us-mobile-wip-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-at-ca-desktop-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-at-ca-mobile-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-br-us-desktop-wip-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-br-us-mobile-wip-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-br-ca-desktop-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-br-ca-mobile-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-brf-us-desktop-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-brf-us-mobile-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-brf-ca-desktop-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-brf-ca-mobile-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gp-us-desktop-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gp-us-mobile-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gp-ca-desktop-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gp-ca-mobile-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gpf-us-desktop-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gpf-us-mobile-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gpf-ca-desktop-wip-stage": "BRAND=gpf MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gpf-ca-mobile-wip-stage": "BRAND=gpf MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-on-us-desktop-wip-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-on-us-mobile-wip-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-on-ca-desktop-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "store-locator-on-ca-mobile-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=wip-stage npx playwright test store-locator/ --pass-with-no-tests", "visual-at-us-desktop-wip-stage": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=wip-stage VISUAL=true npx playwright test visual", "visual-at-us-mobile-wip-stage": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=wip-stage VISUAL=true npx playwright test visual", "visual-at-ca-desktop-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=wip-stage VISUAL=true npx playwright test visual", "visual-at-ca-mobile-wip-stage": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=wip-stage VISUAL=true npx playwright test visual", "visual-br-us-desktop-wip-stage": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=wip-stage VISUAL=true npx playwright test visual", "visual-br-us-mobile-wip-stage": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=wip-stage VISUAL=true npx playwright test visual", "visual-br-ca-desktop-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=wip-stage VISUAL=true npx playwright test visual", "visual-br-ca-mobile-wip-stage": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=wip-stage VISUAL=true npx playwright test visual", "visual-brf-us-desktop-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=wip-stage VISUAL=true npx playwright test visual", "visual-brf-us-mobile-wip-stage": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=wip-stage VISUAL=true npx playwright test visual", "visual-brf-ca-desktop-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=wip-stage VISUAL=true npx playwright test visual", "visual-brf-ca-mobile-wip-stage": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=wip-stage VISUAL=true npx playwright test visual", "visual-gp-us-desktop-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=wip-stage VISUAL=true npx playwright test visual", "visual-gp-us-mobile-wip-stage": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=wip-stage VISUAL=true npx playwright test visual", "visual-gp-ca-desktop-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=wip-stage VISUAL=true npx playwright test visual", "visual-gp-ca-mobile-wip-stage": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=wip-stage VISUAL=true npx playwright test visual", "visual-gpf-us-desktop-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=wip-stage VISUAL=true npx playwright test visual", "visual-gpf-us-mobile-wip-stage": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=wip-stage VISUAL=true npx playwright test visual", "visual-on-us-desktop-wip-stage": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=wip-stage VISUAL=true npx playwright test visual", "visual-on-us-mobile-wip-stage": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=wip-stage VISUAL=true npx playwright test visual", "visual-on-ca-desktop-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=wip-stage VISUAL=true npx playwright test visual", "visual-on-ca-mobile-wip-stage": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=wip-stage VISUAL=true npx playwright test visual", "accessibility-at-us-desktop-preview": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-at-us-mobile-preview": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-at-ca-desktop-preview": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-at-ca-mobile-preview": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-br-us-desktop-preview": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-br-us-mobile-preview": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-br-ca-desktop-preview": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-br-ca-mobile-preview": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-brf-ca-desktop-preview": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-brf-ca-mobile-preview": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-gp-us-desktop-preview": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-gp-us-mobile-preview": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-gp-ca-desktop-preview": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-gp-ca-mobile-preview": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-on-us-desktop-preview": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-on-us-mobile-preview": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-on-ca-desktop-preview": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "accessibility-on-ca-mobile-preview": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test --retries=0 accessibility.spec.ts", "category-at-us-desktop-preview": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test category/", "category-at-us-mobile-preview": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test category/", "category-at-ca-desktop-preview": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test category/", "category-at-ca-mobile-preview": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test category/", "category-br-us-desktop-preview": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test category/", "category-br-us-mobile-preview": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test category/", "category-br-ca-desktop-preview": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test category/", "category-br-ca-mobile-preview": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test category/", "category-brf-us-desktop-preview": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test category/", "category-brf-us-mobile-preview": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test category/", "category-brf-ca-desktop-preview": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test category/", "category-brf-ca-mobile-preview": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test category/", "category-gp-us-desktop-preview": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test category/", "category-gp-us-mobile-preview": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test category/", "category-gp-ca-desktop-preview": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test category/", "category-gp-ca-mobile-preview": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test category/", "category-gpf-us-desktop-preview": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test category/", "category-gpf-us-mobile-preview": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test category/", "category-on-us-desktop-preview": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test category/", "category-on-us-mobile-preview": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test category/", "category-on-ca-desktop-preview": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test category/", "category-on-ca-mobile-preview": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test category/", "checkout-at-us-desktop-preview": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test checkout/", "checkout-at-us-mobile-preview": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test checkout/", "checkout-at-ca-desktop-preview": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test checkout/", "checkout-at-ca-mobile-preview": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test checkout/", "checkout-br-us-desktop-preview": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test checkout/", "checkout-br-us-mobile-preview": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test checkout/", "checkout-br-ca-desktop-preview": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test checkout/", "checkout-br-ca-mobile-preview": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test checkout/", "checkout-brf-us-desktop-preview": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test checkout/", "checkout-brf-us-mobile-preview": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test checkout/", "checkout-brf-ca-desktop-preview": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test checkout/", "checkout-brf-ca-mobile-preview": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test checkout/", "checkout-gp-us-desktop-preview": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test checkout/", "checkout-gp-us-mobile-preview": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test checkout/", "checkout-gp-ca-desktop-preview": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test checkout/", "checkout-gp-ca-mobile-preview": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test checkout/", "checkout-gpf-us-desktop-preview": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test checkout/", "checkout-gpf-us-mobile-preview": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test checkout/", "checkout-on-us-desktop-preview": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test checkout/", "checkout-on-us-mobile-preview": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test checkout/", "checkout-on-ca-desktop-preview": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test checkout/", "checkout-on-ca-mobile-preview": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test checkout/", "my-account-at-us-desktop-preview": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test my-account/", "my-account-at-us-mobile-preview": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test my-account/", "my-account-at-ca-desktop-preview": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test my-account/", "my-account-at-ca-mobile-preview": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test my-account/", "my-account-br-us-desktop-preview": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test my-account/", "my-account-br-us-mobile-preview": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test my-account/", "my-account-br-ca-desktop-preview": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test my-account/", "my-account-br-ca-mobile-preview": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test my-account/", "my-account-brf-us-desktop-preview": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test my-account/", "my-account-brf-us-mobile-preview": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test my-account/", "my-account-brf-ca-desktop-preview": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test my-account/", "my-account-brf-ca-mobile-preview": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test my-account/", "my-account-gp-us-desktop-preview": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test my-account/", "my-account-gp-us-mobile-preview": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test my-account/", "my-account-gp-ca-desktop-preview": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test my-account/", "my-account-gp-ca-mobile-preview": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test my-account/", "my-account-gpf-us-desktop-preview": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test my-account/", "my-account-gpf-us-mobile-preview": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test my-account/", "my-account-on-us-desktop-preview": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test my-account/", "my-account-on-us-mobile-preview": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test my-account/", "my-account-on-ca-desktop-preview": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test my-account/", "my-account-on-ca-mobile-preview": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test my-account/", "product-at-us-desktop-preview": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test product/", "product-at-us-mobile-preview": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test product/", "product-at-ca-desktop-preview": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test product/", "product-at-ca-mobile-preview": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test product/", "product-br-us-desktop-preview": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test product/", "product-br-us-mobile-preview": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test product/", "product-br-ca-desktop-preview": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test product/", "product-br-ca-mobile-preview": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test product/", "product-brf-us-desktop-preview": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test product/", "product-brf-us-mobile-preview": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test product/", "product-brf-ca-desktop-preview": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test product/", "product-brf-ca-mobile-preview": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test product/", "product-gp-us-desktop-preview": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test product/", "product-gp-us-mobile-preview": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test product/", "product-gp-ca-desktop-preview": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test product/", "product-gp-ca-mobile-preview": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test product/", "product-gpf-us-desktop-preview": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test product/", "product-gpf-us-mobile-preview": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test product/", "product-on-us-desktop-preview": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test product/", "product-on-us-mobile-preview": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test product/", "product-on-ca-desktop-preview": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test product/", "product-on-ca-mobile-preview": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test product/", "search-at-us-desktop-preview": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test search/", "search-at-us-mobile-preview": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test search/", "search-at-ca-desktop-preview": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test search/", "search-at-ca-mobile-preview": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test search/", "search-br-us-desktop-preview": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test search/", "search-br-us-mobile-preview": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test search/", "search-br-ca-desktop-preview": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test search/", "search-br-ca-mobile-preview": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test search/", "search-brf-us-desktop-preview": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test search/", "search-brf-us-mobile-preview": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test search/", "search-brf-ca-desktop-preview": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test search/", "search-brf-ca-mobile-preview": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test search/", "search-gp-us-desktop-preview": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test search/", "search-gp-us-mobile-preview": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test search/", "search-gp-ca-desktop-preview": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test search/", "search-gp-ca-mobile-preview": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test search/", "search-gpf-us-desktop-preview": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test search/", "search-gpf-us-mobile-preview": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test search/", "search-on-us-desktop-preview": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test search/", "search-on-us-mobile-preview": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test search/", "search-on-ca-desktop-preview": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test search/", "search-on-ca-mobile-preview": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test search/", "shopping-bag-at-us-desktop-preview": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test shopping-bag/", "shopping-bag-at-us-mobile-preview": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test shopping-bag/", "shopping-bag-at-ca-desktop-preview": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test shopping-bag/", "shopping-bag-at-ca-mobile-preview": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test shopping-bag/", "shopping-bag-br-us-desktop-preview": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test shopping-bag/", "shopping-bag-br-us-mobile-preview": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test shopping-bag/", "shopping-bag-br-ca-desktop-preview": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test shopping-bag/", "shopping-bag-br-ca-mobile-preview": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test shopping-bag/", "shopping-bag-gp-us-desktop-preview": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test shopping-bag/", "shopping-bag-gp-us-mobile-preview": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test shopping-bag/", "shopping-bag-gp-ca-desktop-preview": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test shopping-bag/", "shopping-bag-gp-ca-mobile-preview": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test shopping-bag/", "shopping-bag-gpf-us-desktop-preview": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test shopping-bag/", "shopping-bag-gpf-us-mobile-preview": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test shopping-bag/", "shopping-bag-brf-us-desktop-preview": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test shopping-bag/", "shopping-bag-brf-us-mobile-preview": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test shopping-bag/", "shopping-bag-brf-ca-desktop-preview": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test shopping-bag/", "shopping-bag-brf-ca-mobile-preview": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test shopping-bag/", "shopping-bag-on-us-desktop-preview": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test shopping-bag/", "shopping-bag-on-us-mobile-preview": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test shopping-bag/", "shopping-bag-on-ca-desktop-preview": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test shopping-bag/", "shopping-bag-on-ca-mobile-preview": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test shopping-bag/", "sitewide-at-us-desktop-preview": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test sitewide/", "sitewide-at-us-mobile-preview": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test sitewide/", "sitewide-at-ca-desktop-preview": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test sitewide/", "sitewide-at-ca-mobile-preview": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test sitewide/", "sitewide-br-us-desktop-preview": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test sitewide/", "sitewide-br-us-mobile-preview": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test sitewide/", "sitewide-br-ca-desktop-preview": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test sitewide/", "sitewide-br-ca-mobile-preview": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test sitewide/", "sitewide-brf-us-desktop-preview": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test sitewide/", "sitewide-brf-us-mobile-preview": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test sitewide/", "sitewide-brf-ca-desktop-preview": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test sitewide/", "sitewide-brf-ca-mobile-preview": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test sitewide/", "sitewide-gp-us-desktop-preview": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test sitewide/", "sitewide-gp-us-mobile-preview": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test sitewide/", "sitewide-gp-ca-desktop-preview": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test sitewide/", "sitewide-gp-ca-mobile-preview": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test sitewide/", "sitewide-gpf-us-desktop-preview": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test sitewide/", "sitewide-gpf-us-mobile-preview": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test sitewide/", "sitewide-on-us-desktop-preview": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test sitewide/", "sitewide-on-us-mobile-preview": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test sitewide/", "sitewide-on-ca-desktop-preview": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test sitewide/", "sitewide-on-ca-mobile-preview": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test sitewide/", "utility-at-us-desktop-preview": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test utility/", "utility-at-us-mobile-preview": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test utility/", "utility-at-ca-desktop-preview": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test utility/", "utility-at-ca-mobile-preview": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test utility/", "utility-br-us-desktop-preview": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test utility/", "utility-br-us-mobile-preview": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test utility/", "utility-br-ca-desktop-preview": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test utility/", "utility-br-ca-mobile-preview": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test utility/", "utility-brf-us-desktop-preview": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test utility/", "utility-brf-us-mobile-preview": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test utility/", "utility-brf-ca-desktop-preview": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test utility/", "utility-brf-ca-mobile-preview": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test utility/", "utility-gp-us-desktop-preview": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test utility/", "utility-gp-us-mobile-preview": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test utility/", "utility-gp-ca-desktop-preview": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test utility/", "utility-gp-ca-mobile-preview": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test utility/", "utility-gpf-us-desktop-preview": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test utility/", "utility-gpf-us-mobile-preview": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test utility/", "utility-on-us-desktop-preview": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test utility/", "utility-on-us-mobile-preview": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test utility/", "utility-on-ca-desktop-preview": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test utility/", "utility-on-ca-mobile-preview": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test utility/", "all-store-locator-preview": "npm-run-all -l -c store-locator-*-preview", "store-locator-at-us-desktop-preview": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-at-us-mobile-preview": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-at-ca-desktop-preview": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-at-ca-mobile-preview": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-br-us-desktop-preview": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-br-us-mobile-preview": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-br-ca-desktop-preview": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-br-ca-mobile-preview": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-brf-us-desktop-preview": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-brf-us-mobile-preview": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-brf-ca-desktop-preview": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-brf-ca-mobile-preview": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gp-us-desktop-preview": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gp-us-mobile-preview": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gp-ca-desktop-preview": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gp-ca-mobile-preview": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gpf-us-desktop-preview": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gpf-us-mobile-preview": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gpf-ca-desktop-preview": "BRAND=gpf MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-gpf-ca-mobile-preview": "BRAND=gpf MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-on-us-desktop-preview": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-on-us-mobile-preview": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-on-ca-desktop-preview": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "store-locator-on-ca-mobile-preview": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=preview npx playwright test store-locator/ --pass-with-no-tests", "visual-at-us-desktop-preview": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=preview VISUAL=true npx playwright test visual", "visual-at-us-mobile-preview": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=preview VISUAL=true npx playwright test visual", "visual-at-ca-desktop-preview": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=preview VISUAL=true npx playwright test visual", "visual-at-ca-mobile-preview": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=preview VISUAL=true npx playwright test visual", "visual-br-us-desktop-preview": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=preview VISUAL=true npx playwright test visual", "visual-br-us-mobile-preview": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=preview VISUAL=true npx playwright test visual", "visual-br-ca-desktop-preview": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=preview VISUAL=true npx playwright test visual", "visual-br-ca-mobile-preview": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=preview VISUAL=true npx playwright test visual", "visual-brf-us-desktop-preview": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=preview VISUAL=true npx playwright test visual", "visual-brf-us-mobile-preview": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=preview VISUAL=true npx playwright test visual", "visual-brf-ca-desktop-preview": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=preview VISUAL=true npx playwright test visual", "visual-brf-ca-mobile-preview": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=preview VISUAL=true npx playwright test visual", "visual-gp-us-desktop-preview": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=preview VISUAL=true npx playwright test visual", "visual-gp-us-mobile-preview": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=preview VISUAL=true npx playwright test visual", "visual-gp-ca-desktop-preview": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=preview VISUAL=true npx playwright test visual", "visual-gp-ca-mobile-preview": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=preview VISUAL=true npx playwright test visual", "visual-gpf-us-desktop-preview": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=preview VISUAL=true npx playwright test visual", "visual-gpf-us-mobile-preview": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=preview VISUAL=true npx playwright test visual", "visual-on-us-desktop-preview": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=preview VISUAL=true npx playwright test visual", "visual-on-us-mobile-preview": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=preview VISUAL=true npx playwright test visual", "visual-on-ca-desktop-preview": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=preview VISUAL=true npx playwright test visual", "visual-on-ca-mobile-preview": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=preview VISUAL=true npx playwright test visual", "accessibility-at-us-desktop-prod": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-at-us-mobile-prod": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-at-ca-desktop-prod": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-at-ca-mobile-prod": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-br-us-desktop-prod": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-br-us-mobile-prod": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-br-ca-desktop-prod": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-br-ca-mobile-prod": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-brf-ca-desktop-prod": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-brf-ca-mobile-prod": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-gp-us-desktop-prod": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-gp-us-mobile-prod": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-gp-ca-desktop-prod": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-gp-ca-mobile-prod": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-on-us-desktop-prod": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-on-us-mobile-prod": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-on-ca-desktop-prod": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "accessibility-on-ca-mobile-prod": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test --retries=0 accessibility.spec.ts", "category-at-us-desktop-prod": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test category/", "category-at-us-mobile-prod": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test category/", "category-at-ca-desktop-prod": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test category/", "category-at-ca-mobile-prod": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test category/", "category-br-us-desktop-prod": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test category/", "category-br-us-mobile-prod": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test category/", "category-br-ca-desktop-prod": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test category/", "category-br-ca-mobile-prod": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test category/", "category-brf-us-desktop-prod": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test category/", "category-brf-us-mobile-prod": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test category/", "category-brf-ca-desktop-prod": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test category/", "category-brf-ca-mobile-prod": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test category/", "category-gp-us-desktop-prod": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test category/", "category-gp-us-mobile-prod": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test category/", "category-gp-ca-desktop-prod": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test category/", "category-gp-ca-mobile-prod": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test category/", "category-gpf-us-desktop-prod": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test category/", "category-gpf-us-mobile-prod": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test category/", "category-on-us-desktop-prod": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test category/", "category-on-us-mobile-prod": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test category/", "category-on-ca-desktop-prod": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test category/", "category-on-ca-mobile-prod": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test category/", "checkout-at-us-desktop-prod": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test checkout/", "checkout-at-us-mobile-prod": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test checkout/", "checkout-brf-ca-desktop-prod": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test checkout/", "checkout-brf-ca-mobile-prod": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test checkout/", "checkout-gpf-us-desktop-prod": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test checkout/", "checkout-gpf-us-mobile-prod": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test checkout/", "checkout-on-ca-desktop-prod": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test checkout/", "checkout-on-ca-mobile-prod": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test checkout/", "checkout-gp-us-desktop-prod": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test checkout/", "my-account-at-us-desktop-prod": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test my-account/", "my-account-at-us-mobile-prod": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test my-account/", "my-account-at-ca-desktop-prod": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test my-account/", "my-account-at-ca-mobile-prod": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test my-account/", "my-account-br-us-desktop-prod": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test my-account/", "my-account-br-us-mobile-prod": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test my-account/", "my-account-br-ca-desktop-prod": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test my-account/", "my-account-br-ca-mobile-prod": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test my-account/", "my-account-brf-ca-desktop-prod": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test my-account/", "my-account-brf-ca-mobile-prod": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test my-account/", "my-account-gp-us-desktop-prod": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test my-account/", "my-account-gp-us-mobile-prod": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test my-account/", "my-account-gp-ca-desktop-prod": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test my-account/", "my-account-gp-ca-mobile-prod": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test my-account/", "my-account-gpf-us-desktop-prod": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test my-account/", "my-account-gpf-us-mobile-prod": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test my-account/", "my-account-on-us-desktop-prod": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test my-account/", "my-account-on-us-mobile-prod": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test my-account/", "my-account-on-ca-desktop-prod": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test my-account/", "my-account-on-ca-mobile-prod": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test my-account/", "product-at-us-desktop-prod": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test product/", "product-at-us-mobile-prod": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test product/", "product-at-ca-desktop-prod": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test product/", "product-at-ca-mobile-prod": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test product/", "product-br-us-desktop-prod": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test product/", "product-br-us-mobile-prod": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test product/", "product-br-ca-desktop-prod": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test product/", "product-br-ca-mobile-prod": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test product/", "product-brf-us-desktop-prod": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test product/", "product-brf-us-mobile-prod": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test product/", "product-brf-ca-desktop-prod": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test product/", "product-brf-ca-mobile-prod": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test product/", "product-gp-us-desktop-prod": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test product/", "product-gp-us-mobile-prod": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test product/", "product-gp-ca-desktop-prod": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test product/", "product-gp-ca-mobile-prod": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test product/", "product-gpf-us-desktop-prod": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test product/", "product-gpf-us-mobile-prod": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test product/", "product-on-us-desktop-prod": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test product/", "product-on-us-mobile-prod": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test product/", "product-on-ca-desktop-prod": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test product/", "product-on-ca-mobile-prod": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test product/", "search-at-us-desktop-prod": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test search/", "search-at-us-mobile-prod": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test search/", "search-at-ca-desktop-prod": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test search/", "search-at-ca-mobile-prod": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test search/", "search-br-us-desktop-prod": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test search/", "search-br-us-mobile-prod": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test search/", "search-br-ca-desktop-prod": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test search/", "search-br-ca-mobile-prod": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test search/", "search-brf-us-desktop-prod": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test search/", "search-brf-us-mobile-prod": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test search/", "search-brf-ca-desktop-prod": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test search/", "search-brf-ca-mobile-prod": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test search/", "search-gp-us-desktop-prod": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test search/", "search-gp-us-mobile-prod": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test search/", "search-gp-ca-desktop-prod": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test search/", "search-gp-ca-mobile-prod": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test search/", "search-gpf-us-desktop-prod": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test search/", "search-gpf-us-mobile-prod": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test search/", "search-on-us-desktop-prod": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test search/", "search-on-us-mobile-prod": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test search/", "search-on-ca-desktop-prod": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test search/", "search-on-ca-mobile-prod": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test search/", "shopping-bag-gp-us-desktop-prod": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test shopping-bag/", "shopping-bag-gp-us-mobile-prod": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test shopping-bag/", "shopping-bag-gp-ca-desktop-prod": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test shopping-bag/", "shopping-bag-gp-ca-mobile-prod": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test shopping-bag/", "shopping-bag-gpf-us-desktop-prod": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test shopping-bag/", "shopping-bag-gpf-us-mobile-prod": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test shopping-bag/", "shopping-bag-brf-ca-desktop-prod": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test shopping-bag/", "shopping-bag-brf-ca-mobile-prod": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test shopping-bag/", "sitewide-at-us-desktop-prod": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test sitewide/", "sitewide-at-us-mobile-prod": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test sitewide/", "sitewide-at-ca-desktop-prod": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test sitewide/", "sitewide-at-ca-mobile-prod": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test sitewide/", "sitewide-br-us-desktop-prod": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test sitewide/", "sitewide-br-us-mobile-prod": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test sitewide/", "sitewide-br-ca-desktop-prod": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test sitewide/", "sitewide-br-ca-mobile-prod": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test sitewide/", "sitewide-brf-us-desktop-prod": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test sitewide/", "sitewide-brf-us-mobile-prod": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test sitewide/", "sitewide-brf-ca-desktop-prod": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test sitewide/", "sitewide-brf-ca-mobile-prod": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test sitewide/", "sitewide-gp-us-desktop-prod": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test sitewide/", "sitewide-gp-us-mobile-prod": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test sitewide/", "sitewide-gp-ca-desktop-prod": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test sitewide/", "sitewide-gp-ca-mobile-prod": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test sitewide/", "sitewide-gpf-us-desktop-prod": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test sitewide/", "sitewide-gpf-us-mobile-prod": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test sitewide/", "sitewide-on-us-desktop-prod": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test sitewide/", "sitewide-on-us-mobile-prod": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test sitewide/", "sitewide-on-ca-desktop-prod": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test sitewide/", "sitewide-on-ca-mobile-prod": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test sitewide/", "utility-at-us-desktop-prod": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test utility/", "utility-at-us-mobile-prod": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test utility/", "utility-at-ca-desktop-prod": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test utility/", "utility-at-ca-mobile-prod": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test utility/", "utility-br-us-desktop-prod": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test utility/", "utility-br-us-mobile-prod": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test utility/", "utility-br-ca-desktop-prod": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test utility/", "utility-br-ca-mobile-prod": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test utility/", "utility-brf-us-desktop-prod": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test utility/", "utility-brf-us-mobile-prod": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test utility/", "utility-brf-ca-desktop-prod": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test utility/", "utility-brf-ca-mobile-prod": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test utility/", "utility-gp-us-desktop-prod": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test utility/", "utility-gp-us-mobile-prod": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test utility/", "utility-gp-ca-desktop-prod": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test utility/", "utility-gp-ca-mobile-prod": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test utility/", "utility-gpf-us-desktop-prod": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test utility/", "utility-gpf-us-mobile-prod": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test utility/", "utility-on-us-desktop-prod": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test utility/", "utility-on-us-mobile-prod": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test utility/", "utility-on-ca-desktop-prod": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test utility/", "utility-on-ca-mobile-prod": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test utility/", "all-store-locator-prod": "npm-run-all -l -c store-locator-*-prod", "store-locator-at-us-desktop-prod": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test store-locator/", "store-locator-at-us-mobile-prod": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test store-locator/", "store-locator-at-ca-desktop-prod": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test store-locator/", "store-locator-at-ca-mobile-prod": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test store-locator/", "store-locator-br-us-desktop-prod": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test store-locator/", "store-locator-br-us-mobile-prod": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test store-locator/", "store-locator-br-ca-desktop-prod": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test store-locator/", "store-locator-br-ca-mobile-prod": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test store-locator/", "store-locator-brf-us-desktop-prod": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test store-locator/", "store-locator-brf-us-mobile-prod": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test store-locator/", "store-locator-brf-ca-desktop-prod": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test store-locator/", "store-locator-brf-ca-mobile-prod": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test store-locator/", "store-locator-gp-us-desktop-prod": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test store-locator/", "store-locator-gp-us-mobile-prod": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test store-locator/", "store-locator-gp-ca-desktop-prod": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test store-locator/", "store-locator-gp-ca-mobile-prod": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test store-locator/", "store-locator-gpf-us-desktop-prod": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test store-locator/", "store-locator-gpf-us-mobile-prod": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test store-locator/", "store-locator-gpf-ca-desktop-prod": "BRAND=gpf MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test store-locator/", "store-locator-gpf-ca-mobile-prod": "BRAND=gpf MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test store-locator/", "store-locator-on-us-desktop-prod": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=prod npx playwright test store-locator/", "store-locator-on-us-mobile-prod": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=prod npx playwright test store-locator/", "store-locator-on-ca-desktop-prod": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=prod npx playwright test store-locator/", "store-locator-on-ca-mobile-prod": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=prod npx playwright test store-locator/", "visual-at-us-desktop-prod": "BRAND=at MARKET=us BREAKPOINT=desktop ENV=prod VISUAL=true npx playwright test visual", "visual-at-us-mobile-prod": "BRAND=at MARKET=us BREAKPOINT=mobile ENV=prod VISUAL=true npx playwright test visual", "visual-at-ca-desktop-prod": "BRAND=at MARKET=ca BREAKPOINT=desktop ENV=prod VISUAL=true npx playwright test visual", "visual-at-ca-mobile-prod": "BRAND=at MARKET=ca BREAKPOINT=mobile ENV=prod VISUAL=true npx playwright test visual", "visual-br-us-desktop-prod": "BRAND=br MARKET=us BREAKPOINT=desktop ENV=prod VISUAL=true npx playwright test visual", "visual-br-us-mobile-prod": "BRAND=br MARKET=us BREAKPOINT=mobile ENV=prod VISUAL=true npx playwright test visual", "visual-br-ca-desktop-prod": "BRAND=br MARKET=ca BREAKPOINT=desktop ENV=prod VISUAL=true npx playwright test visual", "visual-br-ca-mobile-prod": "BRAND=br MARKET=ca BREAKPOINT=mobile ENV=prod VISUAL=true npx playwright test visual", "visual-brf-us-desktop-prod": "BRAND=brf MARKET=us BREAKPOINT=desktop ENV=prod VISUAL=true npx playwright test visual", "visual-brf-us-mobile-prod": "BRAND=brf MARKET=us BREAKPOINT=mobile ENV=prod VISUAL=true npx playwright test visual", "visual-brf-ca-desktop-prod": "BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=prod VISUAL=true npx playwright test visual", "visual-brf-ca-mobile-prod": "BRAND=brf MARKET=ca BREAKPOINT=mobile ENV=prod VISUAL=true npx playwright test visual", "visual-gp-us-desktop-prod": "BRAND=gp MARKET=us BREAKPOINT=desktop ENV=prod VISUAL=true npx playwright test visual", "visual-gp-us-mobile-prod": "BRAND=gp MARKET=us BREAKPOINT=mobile ENV=prod VISUAL=true npx playwright test visual", "visual-gp-ca-desktop-prod": "BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=prod VISUAL=true npx playwright test visual", "visual-gp-ca-mobile-prod": "BRAND=gp MARKET=ca BREAKPOINT=mobile ENV=prod VISUAL=true npx playwright test visual", "visual-gpf-us-desktop-prod": "BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=prod VISUAL=true npx playwright test visual", "visual-gpf-us-mobile-prod": "BRAND=gpf MARKET=us BREAKPOINT=mobile ENV=prod VISUAL=true npx playwright test visual", "visual-on-us-desktop-prod": "BRAND=on MARKET=us BREAKPOINT=desktop ENV=prod VISUAL=true npx playwright test visual", "visual-on-us-mobile-prod": "BRAND=on MARKET=us BREAKPOINT=mobile ENV=prod VISUAL=true npx playwright test visual", "visual-on-ca-desktop-prod": "BRAND=on MARKET=ca BREAKPOINT=desktop ENV=prod VISUAL=true npx playwright test visual", "visual-on-ca-mobile-prod": "BRAND=on MARKET=ca BREAKPOINT=mobile ENV=prod VISUAL=true npx playwright test visual", "all-store-prod": "npm-run-all store-locator-*"}, "devDependencies": {"@types/jsonfile": "^6.1.0", "@types/lodash": "^4.17.16", "@types/node": "^20.8.9", "jsonfile": "^6.1.0", "npm-run-all": "^4.1.5"}, "dependencies": {"@axe-core/playwright": "^4.9.0", "@playwright/test": "^1.52.0", "allure-commandline": "^2.33.0", "allure-playwright": "^3.2.1", "axe-html-reporter": "^2.2.3", "dotenv": "^16.3.1", "lodash": "^4.17.21", "playwright": "^1.52.0"}}