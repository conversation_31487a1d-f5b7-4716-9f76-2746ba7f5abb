// @ts-nocheck
import os from 'os';
import { execSync } from 'child_process';
import type { PlaywrightTestConfig, ReporterDescription } from '@playwright/test';
import { devices } from '@playwright/test';
import { getBaseURL } from '@/utils/urls';

export type Options = { projectAccountSuffix: string };

const isCI = !!process.env.CI;

// Read from default ".env" file.
require('dotenv').config();
!isCI && require('dotenv').config({ path: '.env.local' });

const brand = process.env.BRAND;
const breakpoint = process.env.BREAKPOINT;
const checkout_bypass = process.env.CHECKOUT_BYPASS;
const chrome = process.env.CHROME;
const env = process.env.ENV;
const lt = process.env.LT;
const market = process.env.MARKET;
const safari = process.env.SAFARI;
const startServer = process.env.START_SERVER;
const isVisualRegressionTest = !!process.env.VISUAL;
const isDeploymentTest = process.env.DEPLOYMENT_TEST === 'true';

const { JENKINS_URL, ACT, ENV } = process.env;

if (env === 'stage' || env === 'local') {
  execSync(`GITHUB_TOKEN=${process.env.GITHUB_TOKEN} scripts/download_stage_data_for_test.sh`);
}

// Set reporter according to environment
const allureConfig: ReporterDescription = [
  'allure-playwright',
  {
    suiteTitle: false,
    outputDir: breakpoint === 'desktop' ? 'allure-results-desktop' : 'allure-results-mobile',
    environmentInfo: {
      os_platform: os.platform(),
      os_release: os.release(),
      os_version: os.version(),
      node_version: process.version,
    },
  },
];

const reporters = (): ReporterDescription[] => {
  if (ACT) {
    return [['html', { open: 'never' }], ['list']];
  }
  if (isCI) {
    if (isVisualRegressionTest) {
      return [['blob'], ['github']];
    }
    if (JENKINS_URL) {
      return [allureConfig, ['line']];
    }
    return [allureConfig, ['line']];
  }
  if (isVisualRegressionTest) {
    return [['blob']];
  }
  return [['html'], ['list']];
};

// Playwright config to run tests on LambdaTest platform and local
const config: PlaywrightTestConfig<Options> = {
  testDir: 'tests',
  timeout: 5 * 60 * 1000, // Maximum time one test can run for
  expect: {
    timeout: startServer === 'false' ? 90 * 1000 : 15 * 1000,
  },
  testMatch: /.*\.spec\.ts/,
  grep: new RegExp(`@run-${brand}([^-\\w]|$)|@run-${brand}-${market}([^-\\w]|$)|@run-${brand}-${market}-${env}([^-\\w]|$)|@run-all([^-\\w]|$)`),
  grepInvert: new RegExp(
    `@skip-${brand}\\b|@skip-${env === 'local' ? 'stage' : env}\\b|@skip-${market}\\b|@skip-${breakpoint}\\b|@skip-${brand}-${env}\\b${env === 'local' ? '|@skip-local\\b' : ''}${(env === 'wip-stage' && '|@skip-stage\\b') || ''}${isDeploymentTest ? '|@skip-for-deployment\\b' : ''}`
  ),
  workers: lt === 'true' ? 4 : 2,
  use: {
    baseURL: getBaseURL(),
    // baseURL: ENV === 'prod' ? getBaseURL() + '?ujt=true' : getBaseURL(), //need to add URL param for prod in order to run on GitHub Actions
    // To allow for synthetic orders to be placed in production & to run on Canary Experience
    extraHTTPHeaders: {
      ...(((process.env.CANARY !== 'false' && isCI && !isVisualRegressionTest) || ENV === 'stage' || ENV === 'wip-stage') && checkout_bypass !== 'true'
        ? { 'Chartis-Force-Route': 'canary' }
        : {}),
    },
    ignoreHTTPSErrors: true,
    actionTimeout: startServer === 'false' ? 60 * 1000 : 20 * 1000, // Maximum time each action such as `click()` can take. Defaults to 0 (no limit).
    navigationTimeout: startServer === 'false' ? 120 * 1000 : 60 * 1000, // Timeout for each navigation action
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'retain-on-failure',
    screenshot: 'only-on-failure',
    video: 'off',
    // contextOptions: { recordHar: { path: './playwright.har' } },
    geolocation: { longitude: 39.0437, latitude: -77.4875 },
    permissions: ['geolocation'],
    timezoneId: market === 'us' ? 'America/Los_Angeles' : 'America/Vancouver',
  },
  webServer:
    ENV === 'local' && startServer !== 'false'
      ? {
          command: 'cd ../ && export NODE_TLS_REJECT_UNAUTHORIZED=0 REDIS_READ_TIMEOUT=1 BLOB_STORAGE_TIMEOUT=1 && npm start 2>&1 > server.log',
          url: 'https://atol.stage.gaptechol.com/healthz',
          timeout: 200000,
          ignoreHTTPSErrors: true,
          reuseExistingServer: true,
        }
      : undefined,
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: isCI,
  /* Retry on CI only */
  retries: isCI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  // workers: isCI ? 5 : 1, // specify in pipeline
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  // reporter: reporters(),
  reporter: reporters(),
  globalTimeout: 0, // Global timeout for the whole test run
  // https://playwright.dev/docs/api/class-testconfig#test-config-report-slow-tests
  reportSlowTests: {
    max: 5,
    threshold: 2 * 60 * 1000,
  },
  projects: [
    // -- LambdaTest Config --
    // name in the format: browserName:browserVersion:platform@lambdatest
    // Browsers allowed: `Chrome`, `MicrosoftEdge`, `pw-chromium`, `pw-firefox` and `pw-webkit`
    // Use additional configuration options provided by Playwright if required: https://playwright.dev/docs/api/class-testconfig
    ...(breakpoint === 'desktop' && chrome !== 'false' && lt !== 'true'
      ? [
          {
            name: 'Desktop Chrome',
            use: {
              ...devices['Desktop Chrome'],
              viewport: {
                width: 1920,
                height: 1540,
              },
            },
          },
        ]
      : []),
    ...(breakpoint === 'desktop' && safari !== 'false' && lt !== 'true'
      ? [
          {
            name: 'Desktop Safari',
            use: {
              ...devices['Desktop Safari'],
              viewport: {
                width: 1920,
                height: 1540,
              },
              ServiceWorkers: 'block',
            },
          },
        ]
      : []),
    ...(breakpoint === 'desktop' && lt === 'true'
      ? [
          {
            name: 'chrome:latest:MacOS Sequoia@lambdatest',
            use: {
              ...devices['Desktop Chrome'],
            },
          },
          {
            name: 'pw-webkit:latest:MacOS Sequoia@lambdatest',
            use: {
              ...devices['Desktop WebKit'],
            },
          },
        ]
      : []),
    ...(breakpoint === 'mobile' && chrome !== 'false' && lt !== 'true'
      ? [
          {
            name: 'Mobile Chrome',
            use: {
              ...devices['iPhone 14'],
              defaultBrowserType: 'chromium',
              channel: 'chromium',
            },
          },
        ]
      : []),
    ...(breakpoint === 'mobile' && safari !== 'false' && lt !== 'true'
      ? [
          {
            name: 'Mobile Safari',
            use: {
              ...devices['iPhone 14 Pro Max'],
              ServiceWorkers: 'block',
            },
          },
        ]
      : []),
    ...(breakpoint === 'mobile' && lt === 'true'
      ? [
          {
            name: 'chrome:latest:MacOS Sequoia@lambdatest',
            use: {
              ...devices['iPhone 14 Pro Max'],
            },
          },
          {
            name: 'pw-webkit:latest:MacOS Sequoia@lambdatest',
            use: {
              ...devices['iPhone 14 Pro Max'],
            },
          },
        ]
      : []),
    // {
    //   name: 'chrome:latest:Windows 11@lambdatest',
    //   use: {
    //     ...devices['Desktop Chrome'],
    //   },
    // },
    // {
    //   name: 'MicrosoftEdge:109:MacOS Ventura@lambdatest',
    //   use: {
    //     ...devices['iPhone 12 Pro Max'],
    //   },
    // },
    // {
    //   name: 'pw-firefox:latest:Windows 11@lambdatest',
    //   use: {
    //     ...devices['Desktop Firefox'],
    //   },
    // },
    // {
    //   name: 'chrome',
    //   use: {
    //     browserName: 'chromium',
    //     channel: 'chrome',
    //   },
    // },
    // {
    //   name: 'safari',
    //   use: {
    //     browserName: 'webkit',
    //     viewport: { width: 1200, height: 750 },
    //   },
    // },
    // {
    //   name: 'firefox',
    //   use: {
    //     ...devices['Desktop Firefox'],
    //   },
    // },
    // {
    //   name: 'Mobile Chrome',
    //   use: {
    //     ...devices['iPhone 14'],
    //     channel: 'chromium',
    //     userAgent: 'iphone',
    //     projectAccountSuffix: 'chrome',
    //   },
    // },
    // {
    //   name: 'chrome@pixel5',
    //   use: {
    //     ...devices['iPhone 12 Pro Max'],
    //   },
    // },
  ],
};

export default config;
