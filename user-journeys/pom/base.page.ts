import path from 'path';
import * as base from '@playwright/test';
import { chromium, Page } from 'playwright';
import * as allure from 'allure-js-commons';
import { waitForHydration } from '../utils/helpers';
import { setSessionStorage } from '../utils/sessionStorage';
import { HomePage } from './common/home.page';
import { Sitewide } from './common/sitewide';
import { HamNav } from './common/ham-nav';
import { SearchPage } from './common/search.page';
import { CategoryPage } from './common/category.page';
import { RewriteCategoryPage } from './common/rewrite-category.page';
import { RewriteSearchPage } from './common/rewrite-search.page';
import { CheckoutPage } from './common/checkout.page';
import { ShoppingBagPage } from './common/shopping-bag.page';
import { GiftCardPage } from './common/gift-card.page';
import { StoreLocatorPage } from './common/store-locator.page';
import { ReturnsPage } from './common/returns.page';
import { CustomerServicePage } from './common/customer-service.page';
import { TermsOfUsePage } from './common/terms-of-use.page';
import { DivisionPage } from './common/division.page';
import { UtilityPage } from './common/utility.page';
import { SignInPage } from './common/sign-in.page';
import { ProductPage } from './common/product.page';
import { ErrorPage } from './common/error.page';
import { MyAccountPage } from './common/my-account.page';
import { RewriteProductPage } from './common/rewrite-product.page';
import { type Options } from '@/playwright.config';
import { CustomPage } from '@/global';
import { ecomNextRouteModifier } from '@/utils/ecomNextRouteModifier';
import { getBaseURL } from '@/utils/urls';
import { buildStorageState, setStorageState } from '@/utils/storageState';
import { filterConsoleLog } from '@/utils/consoleLogFilter';
import { modifyCapabilities } from '@/utils/lambdatest';

type PageObjectModel = {
  categoryPage: CategoryPage;
  checkoutPage: CheckoutPage;
  customerServicePage: CustomerServicePage;
  divisionPage: DivisionPage;
  errorPage: ErrorPage;
  giftCardPage: GiftCardPage;
  hamNav: HamNav;
  homePage: HomePage;
  myAccountPage: MyAccountPage;
  productPage: ProductPage;
  returnsPage: ReturnsPage;
  rewriteCategoryPage: RewriteCategoryPage;
  rewriteProductPage: RewriteProductPage;
  rewriteSearchPage: RewriteSearchPage;
  searchPage: SearchPage;
  shoppingBagPage: ShoppingBagPage;
  signInPage: SignInPage;
  sitewide: Sitewide;
  storeLocatorPage: StoreLocatorPage;
  termsOfUsePage: TermsOfUsePage;
  utilityPage: UtilityPage;
};

const env = process.env.ENV;

if (env === 'local') {
  base.test.beforeEach('Load ABSeg from Preview Environment', async ({ page }) => {
    await page.goto(getBaseURL(process.env.BRAND, process.env.MARKET, 'preview'));
    await waitForHydration(page);
    const ABSeg = (await page.context().cookies()).find(cookie => cookie.name === 'ABSeg');
    const UnknownShopperId = (await page.context().cookies()).find(cookie => cookie.name === 'unknownShopperId');
    await page.context().clearCookies();
    await page.context().addCookies([{ ...ABSeg!, domain: '.gaptechol.com' }]);
    await page.context().addCookies([{ ...UnknownShopperId!, domain: '.gaptechol.com' }]);
  });
}

const setupPageForUse = async (page: CustomPage) => {
  if (env === 'preview' || env === 'wip-stage') {
    page.on('domcontentloaded', async () => {
      try {
        await page.addStyleTag({ content: '#preview-button { display: none !important; }' });
      } catch {
        /* ignore */
      }
    });
  }
  await ecomNextRouteModifier(page);
  await filterConsoleLog(page);
  await setSessionStorage(page as Page);
  await setStorageState(page as Page, buildStorageState());

  page.isStillEnglish = true;
};

const breakpoint = process.env.BREAKPOINT!;

const test = base.test.extend<Options & PageObjectModel>({
  page: async ({ page }, use, testInfo) => {
    // Configure LambdaTest platform for cross-browser testing
    const fileName = testInfo.file.split(path.sep).pop();
    const buildName = `Ecom-Next - ${fileName!.split('.')[0]}`;

    if (testInfo.project.name.match(/lambdatest/)) {
      await page.close();

      await allure.parentSuite(`${breakpoint[0].toUpperCase() + breakpoint.slice(1)} - ${testInfo.project.name}`);

      const capabilities = modifyCapabilities(testInfo.project.name, buildName, testInfo.title);

      const browser = await chromium.connect(`wss://cdp.lambdatest.com/playwright?capabilities=${encodeURIComponent(JSON.stringify(capabilities))}`);
      const ltPage = await browser.newPage({ ...testInfo.project.use });

      const ip = (await (await ltPage.request.get('https://ipinfo.io/ip', { timeout: 20000 })).body()).toString();
      // eslint-disable-next-line no-console
      console.log('IP:', ip);

      await setupPageForUse(ltPage as CustomPage);
      await use(ltPage as base.Page);

      const response = JSON.parse((await ltPage.evaluate(_ => {}, `lambdatest_action: ${JSON.stringify({ action: 'getTestDetails' })}`)) as unknown as string);

      const testStatus = {
        action: 'setTestStatus',
        arguments: {
          status: testInfo.status,
          remark: testInfo.error && testInfo.error.message,
        },
      };
      await ltPage.evaluate(_ => {}, `lambdatest_action: ${JSON.stringify(testStatus)}`);
      await allure.link(response.data.public_url, 'LambdaTest');
      // eslint-disable-next-line no-console
      console.log(response);

      // Clear all route handlers before closing
      await ltPage.unrouteAll({ behavior: 'ignoreErrors' });

      await ltPage.close();
      await browser.close();
    } else {
      if (testInfo.tags.includes('@not-incognito')) {
        const persistentContext = await page
          .context()
          .browser()
          ?.browserType()
          .launchPersistentContext('./userProfile', { ...testInfo.project.use });
        await page.close();
        // eslint-disable-next-line no-param-reassign
        [page] = persistentContext!.pages();
      }

      await setupPageForUse(page as CustomPage);
      await use(page);

      await page.unrouteAll({ behavior: 'ignoreErrors' });
    }
  },

  homePage: async ({ page }, use) => {
    await use(new HomePage(page));
  },
  sitewide: async ({ page }, use) => {
    await use(new Sitewide(page));
  },
  hamNav: async ({ page }, use) => {
    await use(new HamNav(page));
  },
  searchPage: async ({ page }, use) => {
    await use(new SearchPage(page));
  },
  categoryPage: async ({ page }, use) => {
    await use(new CategoryPage(page));
  },
  rewriteCategoryPage: async ({ page }, use) => {
    await use(new RewriteCategoryPage(page));
  },
  productPage: async ({ page }, use) => {
    await use(new ProductPage(page));
  },
  shoppingBagPage: async ({ page }, use) => {
    await use(new ShoppingBagPage(page));
  },
  giftCardPage: async ({ page }, use) => {
    await use(new GiftCardPage(page));
  },
  storeLocatorPage: async ({ page }, use) => {
    await use(new StoreLocatorPage(page));
  },
  returnsPage: async ({ page }, use) => {
    await use(new ReturnsPage(page));
  },
  customerServicePage: async ({ page }, use) => {
    await use(new CustomerServicePage(page));
  },
  termsOfUsePage: async ({ page }, use) => {
    await use(new TermsOfUsePage(page));
  },
  utilityPage: async ({ page }, use) => {
    await use(new UtilityPage(page));
  },
  divisionPage: async ({ page }, use) => {
    await use(new DivisionPage(page));
  },
  signInPage: async ({ page }, use) => {
    await use(new SignInPage(page));
  },
  checkoutPage: async ({ page }, use) => {
    await use(new CheckoutPage(page));
  },
  errorPage: async ({ page }, use) => {
    await use(new ErrorPage(page));
  },
  myAccountPage: async ({ page }, use) => {
    await use(new MyAccountPage(page));
  },
  rewriteProductPage: async ({ page }, use) => {
    await use(new RewriteProductPage(page));
  },
  rewriteSearchPage: async ({ page }, use) => {
    await use(new RewriteSearchPage(page));
  },
  projectAccountSuffix: ['chrome', { option: true }],
});

export default test;
