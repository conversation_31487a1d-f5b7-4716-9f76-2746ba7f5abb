import { Locator, type Page } from '@playwright/test';
import { getBaseURL } from '../../utils/urls';

export class HamNav {
  readonly page: Page;
  readonly hamNavOpen: Locator;
  readonly hamNavFooter: Locator;
  readonly divisionLink: Locator;
  readonly categoryLink: Locator;
  readonly accessSearchInput: Locator;
  readonly searchInput: Locator;
  readonly searchSubmitIcon: Locator;
  readonly signInOrJoin: Locator;
  readonly buyEGiftCardsLink: Locator;
  readonly buyGiftCardsLink: Locator;
  readonly ordersReturns: Locator;
  readonly shippingDelivery: Locator;
  readonly myPointsRewards: Locator;
  readonly exploreBenefits: Locator;
  readonly payCreditCardBill: Locator;
  readonly activateCreditCard: Locator;
  readonly joinRewards: Locator;
  readonly storeLocator: Locator;
  readonly communicationPreferences: Locator;
  readonly signInFooter: Locator;
  readonly mainImages: Locator;
  readonly customerServiceLink: Locator;
  readonly isolationLayer: Locator;
  readonly hamNavWrapper: Locator;

  constructor(page: Page) {
    this.page = page;
    const footer = page.getByTestId('hamburger-nav-footer-redesign');
    this.hamNavOpen = page.locator('button[aria-label="Open Menu"]');
    this.hamNavFooter = page.getByTestId('hamburger-nav-footer-redesign');
    this.divisionLink = page.locator(`button.hamnav-division-button`).first();
    this.categoryLink = page.locator(`.hamnav-item-list a`).nth(1);
    this.accessSearchInput = page.locator(`[data-testid="ska-trigger-button-nonexpposed"]`).first();
    this.searchInput = page.getByLabel('search box').last();
    this.searchSubmitIcon = page.locator(`button[aria-label="search"]`).last();
    this.signInOrJoin = page.locator(`div[data-testid="hamburger-nav-header-redesign"] a[href*="sign-in"]`).first();
    this.storeLocator = footer.getByRole('link', { name: /Store Locator/i });
    this.buyEGiftCardsLink = footer.getByRole('link', { name: /Buy eGiftCard/i });
    this.buyGiftCardsLink = footer.getByRole('link', { name: /Buy GiftCard/i });
    this.ordersReturns = footer.getByRole('link', { name: /Orders & Returns/i });
    this.shippingDelivery = footer.getByRole('link', { name: /Shipping & Delivery/i });
    this.myPointsRewards = footer.getByRole('link', { name: /My Points and Rewards/i });
    this.exploreBenefits = footer.getByRole('link', { name: /Explore Benefits/i });
    this.payCreditCardBill = footer.getByRole('link', { name: /Pay Credit Card Bill/i });
    this.activateCreditCard = footer.getByRole('link', { name: /Activate Credit Card/i });
    this.joinRewards = footer.getByRole('link', { name: /Join/i });
    this.communicationPreferences = footer.getByRole('link', {
      name: /Communication Preferences/i,
    });
    this.customerServiceLink = footer.getByRole('link', { name: /Customer Service/i });
    this.signInFooter = footer.getByRole('link', { name: /Sign In/i });
    this.mainImages = page.locator(`#mainContent img[src]:not([src=""])`);
    this.isolationLayer = page.getByTestId('isolationLayer');
    this.hamNavWrapper = page.locator('#hamburgerNavWrapper');
  }

  async searchFor(searchTerm: string) {
    await this.searchInput.click();
    await this.searchInput.pressSequentially(searchTerm);
    await this.searchSubmitIcon.click();
  }

  async goToHomePage() {
    await this.page.goto(`${getBaseURL()}`);
  }

  async expandAtCustomerServiceLinks() {
    await this.page.locator(`button:text-matches("Customer Service", "i")`).first().click();
  }

  async expandGiftCardLinks() {
    await this.page.locator(`button:text-matches("Gift Card", "i")`).first().click();
  }

  async expandRewardsLinks() {
    await this.hamNavFooter.locator(`button:text-matches("Rewards", "i")`).first().click();
  }

  async waitForMainImages() {
    const imageArray = [];
    const mainImgCount = await this.mainImages.count();
    for (let i = 0; i < mainImgCount; i++) {
      imageArray.push(this.mainImages.nth(i));
    }
    await Promise.all(imageArray.map(img => this.waitForImage(img)));
  }

  async waitForImage(locator: Locator) {
    const src = await locator.getAttribute('src');
    return (await this.page.waitForResponse(src!)).finished();
  }
}
