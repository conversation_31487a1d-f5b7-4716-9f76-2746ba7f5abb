/* eslint-disable class-methods-use-this */

import { Locator, type Page } from '@playwright/test';
import { test } from '@playwright/test';
import _ from 'lodash';

const brand = process.env.BRAND;
const breakpoint = process.env.BREAKPOINT;

export class PLPPage {
  // Page instance
  readonly page: Page;

  // Boolean
  readonly isRewrite: boolean;

  // Locators
  readonly productCards: Locator;
  readonly productCardWithMarketingFlag: Locator;
  readonly productImages: Locator;
  readonly productImageLinks: Locator;
  readonly productPricePercentOff: Locator;
  readonly productPriceMarkDown: Locator;
  readonly productRecommendations: Locator;
  readonly productGrid: Locator;
  readonly colorSwatch: Locator;
  readonly defaultColorSwatch: Locator;
  readonly alternateColorSwatch: Locator;
  readonly quickAddBadge: Locator;
  readonly badge: Locator;
  readonly sortByDesktop: Locator;
  readonly allFacetsButton: Locator;
  readonly facetDrawer: Locator | Page;
  readonly closeFacetsButton: Locator;
  readonly clearFacetsButton: Locator;
  readonly individualFacetClearButton: Locator;
  readonly quickFilterButtons: Locator;
  readonly bestOfNewArrivalsFilter: Locator;
  readonly currentFacets: Locator;
  readonly currentFlexFacets: Locator;
  readonly flexFacets: Locator;
  readonly departmentFilterDropdown: Locator;
  readonly departmentFilterOptions: Locator;
  readonly categoryFilterDropdown: Locator;
  readonly categoryFilterOptions: Locator;
  readonly sizeFilterDropdown: Locator;
  readonly sizeFilterOptions: Locator;
  readonly colorFilterDropdown: Locator;
  readonly colorFilterOptions: Locator;
  readonly priceFilterDropdown: Locator;
  readonly moreColorsonPdp: Locator;
  readonly priceSlider: Locator;
  readonly priceFilterOptionsMinDropdown: Locator;
  readonly priceFilterOptionsMaxDropdown: Locator;
  readonly priceFilterOptionsDowndownItems: Locator;
  readonly filterDone: Locator;

  // String
  readonly facetDiv: string;

  // Number
  readonly topRowSize: number;

  constructor(page: Page) {
    // Page instance
    this.page = page;

    // Boolean
    this.isRewrite = test.info().titlePath.includes('Rewrite');

    // Locators
    this.productCards = page.locator('div.product-card');
    this.productCardWithMarketingFlag = this.productCards.locator(`.product-card__marketing-flag`);
    this.productImages = this.productCards.locator(`.${this.isRewrite ? 'plp' : 'cat'}_product-image img:not([alt="overlay image"])`);
    this.productImageLinks = this.productCards.locator('a:has(img)');
    this.productPricePercentOff = this.productCards.locator(`[class*="product-price__percentage-off"]`).first();
    this.productPriceMarkDown = this.productCards.locator(`.product-price__same-line`).first();
    this.productRecommendations = page.getByTestId('product-recommendations');
    this.productGrid = page.locator(`div.product-grid`).first();
    this.colorSwatch = page.getByTestId('swatch-and-label-container');
    this.defaultColorSwatch = page.locator(`div[data-testid="swatch-and-label-container"] input`).first();
    this.alternateColorSwatch = page.locator(`div[data-testid="swatch-and-label-container"] input`).nth(1);
    this.quickAddBadge = page.getByLabel('Open Quick Add');
    this.badge = this.productCards.locator('img[alt="overlay image"]');
    this.sortByDesktop = page.getByRole('button', { name: /sort/ });
    this.allFacetsButton = page.getByRole('button', { name: /Filter icon(?: All)? Filters/ });
    this.facetDrawer = brand?.startsWith('br') ? page.getByLabel('Product filters') : page.getByTestId(/drawer/i);
    this.closeFacetsButton = page.locator('#closeButton');
    this.clearFacetsButton = (brand?.startsWith('br') ? page.getByLabel('Product filters') : page.getByTestId('grid-header-container')).getByRole('button', {
      name: 'Clear Filters',
    });
    this.individualFacetClearButton = page
      .getByTestId('grid-header-container')
      .getByLabel(/inline-facet-tags.tag-close-button-aria-label/i)
      .or(page.getByTestId('grid-header-container').getByRole('button', { name: /^remove filter(?!.*Out-of-stock).*$/i }));
    this.quickFilterButtons = page
      .getByTestId('grid-header-container')
      .locator('button:not([aria-label^="remove filter:"]):not(:has(.all_filters_icon)):not([aria-expanded])');
    this.bestOfNewArrivalsFilter = page.getByTestId('grid-header-container').getByRole('button', { name: /.*Best of New Arrivals/i });
    this.currentFacets = this.facetDrawer.getByRole('button', { name: /\(\d+\)/i });
    this.currentFlexFacets = this.facetDrawer.getByRole('button', { name: /^(?!.*Department).*?\(\d+\)/i });
    this.flexFacets = this.facetDrawer
      .getByRole('button', { expanded: false })
      .or(this.facetDrawer.getByRole('button', { expanded: true }))
      .filter({ hasNotText: /Department|Category|Color|Size|Price|Sort/i });
    this.departmentFilterDropdown = this.facetDrawer.getByRole('button', { name: /Department/i });
    this.departmentFilterOptions = page.locator('label:has([name="department"])');
    this.categoryFilterDropdown = this.facetDrawer.getByRole('button', { name: /Category/i });
    this.categoryFilterOptions = page
      .locator('label:has([type="checkbox"]):not([role="switch"])')
      .filter({ hasNotText: /pickup/i })
      .filter({ hasNot: page.locator('[role="switch"], [aria-label="Out-Of-Stock Filter"]') });
    this.sizeFilterDropdown = this.facetDrawer.getByRole('button', { name: /Size/i });
    this.sizeFilterOptions = this.facetDrawer.getByLabel(/size/i).locator('visible=true');
    this.colorFilterDropdown = this.facetDrawer.getByRole('button', { name: /Color/i });
    this.colorFilterOptions = this.facetDrawer.getByTestId('swatch-and-label-container');
    this.priceFilterDropdown = this.facetDrawer.getByRole('button', { name: /Price/i });
    this.moreColorsonPdp = this.productCards
      .locator('a')
      .filter({ hasText: /^\+ [1-9]$/ })
      .first();
    this.priceSlider = page.locator('[data-testid="max-range-slider"] ~ div > div:nth-of-type(2)');
    this.priceFilterOptionsMinDropdown = page.locator('.range-facet-dropdown__control, .range-facet-dropdown-native__select').first();
    this.priceFilterOptionsMaxDropdown = page.locator('.range-facet-dropdown__control, .range-facet-dropdown-native__select').last();
    this.priceFilterOptionsDowndownItems = page.locator('.range-facet-dropdown__option');
    this.filterDone = page.getByRole('button', { name: 'Done' }).first();

    // String
    this.facetDiv = brand?.startsWith('br') ? '[aria-label="Product filters"]' : '.search-page__left-column';

    // Number
    this.topRowSize = brand?.startsWith('br') ? 3 : 4;
  }

  async selectRandomProduct() {
    await (await this.getRandomProductImage()).click();
  }

  async getRandomProductCard() {
    return this.productCards.nth(await this.getRandomCardIndex());
  }

  async getRandomProductImage() {
    return this.productImages.nth(await this.getRandomCardIndex());
  }

  async getRandomProductName() {
    return this.getProductName(this.productCards.nth(await this.getRandomCardIndex()));
  }

  async getRandomProductPrice() {
    return this.getProductPrice(this.productCards.nth(await this.getRandomCardIndex()));
  }

  async getRandomQuickAddBadge() {
    return this.getQuickAddBadge(await this.getRandomProductCard());
  }

  async clickNthQuickFilterButton(index: number) {
    return this.quickFilterButtons.nth(index).click({ timeout: 60000 });
  }

  getQuickFilterButtonsCount() {
    return this.quickFilterButtons.count();
  }

  // eslint-disable-next-line class-methods-use-this
  getQuickAddBadge(productCard: Locator) {
    return productCard.getByLabel('Open Quick Add');
  }

  getProductCardQuickAddCount() {
    return this.quickAddBadge.count();
  }

  getProductCardsWithMarktingFlagCount() {
    return this.productCardWithMarketingFlag.count();
  }

  getProductCardsWithBadgingCount() {
    return this.badge.count();
  }

  // eslint-disable-next-line class-methods-use-this
  getProductName(productCard: Locator) {
    return productCard.locator('a:not(:has(img)):not([aria-label^="More colo"], [aria-label$="all_colors_link_aria_label"])').first();
  }

  getProductPrice(productCard: Locator) {
    return productCard.locator('.product-card-price').innerText();
  }

  getProductCardCount() {
    return this.productCards.count();
  }

  async getRandomCardIndex(count?: number) {
    if (count) {
      return _.random(0, count - 1);
    }
    return _.random(0, (await this.getProductCardCount()) - 1);
  }

  async getRandomCardImage() {
    return this.productImages.nth(await this.getRandomCardIndex());
  }

  getItemCountLabel() {
    return this.page.locator(`div[aria-label*="in the product grid"]`).first().innerText();
  }

  async waitForProductCard() {
    await this.productCards.first().waitFor({ timeout: 60000 });
  }

  getProductImage(productCard: Locator | number) {
    if (typeof productCard === 'number') {
      return this.productImages.nth(productCard);
    }
    return productCard.locator('.cat_product-image img:not([alt="overlay image"])');
  }

  getProductImageID() {
    return this.productImages.first().getAttribute('id');
  }

  getProductImagesCount() {
    return this.productImages.count();
  }

  getColorSwatchCount() {
    return this.colorSwatch.count();
  }

  clickNthColorSwatchForProductCard(productCard: Locator | number, index: number) {
    return this.getColorSwatchsForProductCard(productCard).nth(index).click();
  }

  getColorSwatchsForProductCard(productCard: Locator | number) {
    if (typeof productCard === 'number') {
      return this.productCards.nth(productCard).getByTestId('swatch-and-label-container');
    }
    return productCard.getByTestId('swatch-and-label-container');
  }

  getColorSwatchCountForProductCard(productCard: Locator | number) {
    return this.getColorSwatchsForProductCard(productCard).count();
  }

  closeModal() {
    return this.page.getByRole('button', { name: 'Close' }).click({ timeout: 60000 });
  }

  async closeFilters() {
    return this.page.locator('button#closeButton').first().click();
  }

  async clickFilterFacetButton() {
    return this.page.locator('button.filter_button, button:has(div.all_filters_icon)').click({ timeout: 60000 });
  }

  async goToFeaturedSort() {
    if (brand === 'br' || brand === 'brf') {
      await this.clickFilterFacetButton();
      await this.clickSortFacetButton();
      await this.page.locator('li:text("featured"), div[aria-hidden="false"] label:has(input#featured)').click();
      await this.filterDone.click();
    } else {
      if (breakpoint === 'mobile') {
        await this.page.locator('select#sortBySelect').selectOption('featured');
      } else {
        await this.sortByDesktop.click();
        await this.page.locator('li:text("featured"), div[aria-hidden="false"] label:has(input#featured)').click();
      }
    }
  }

  async clickSortFacetButton() {
    let expandedCount = await this.page.locator(`div${this.facetDiv}:has(button[aria-expanded="true"]:text("Sort"))`).count();

    while (expandedCount < 1) {
      await this.page.locator(`button[aria-expanded]:text("Sort")`).click({ timeout: 60000 });

      expandedCount = await this.page.locator(`div${this.facetDiv}:has(button[aria-expanded="true"])`).count();
    }
  }

  async goToPriceLowHighSort() {
    if (brand === 'br' || brand === 'brf') {
      await this.clickFilterFacetButton();
      await this.clickSortFacetButton();
      await this.page.locator('li:text("price: low–high"), div[aria-hidden="false"] label:has(input#low)').click();
      await this.filterDone.click();
    } else if (breakpoint === 'mobile') {
      await this.page.locator('select#sortBySelect').selectOption('price: low–high');
    } else {
      await this.sortByDesktop.click();
      await this.page.locator('li:text("price: low–high"), div[aria-hidden="false"] label:has(input#low)').click();
    }
  }

  async getRandomSortedPrices(count: number) {
    const [first, firstIndex, second, secondIndex] = await this.getTwoRandomProductCards(count);

    const firstPriceUnparsed = await this.getProductPrice(first as Locator);
    const secondPriceUnparsed = await this.getProductPrice(second as Locator);

    const regex = /\$\d{0,3}\.\d{2}/g;
    let firstLowestPrice = Number.MAX_VALUE;
    let secondLowestPrice = Number.MAX_VALUE;
    for (let tempLowestPrice = regex.exec(firstPriceUnparsed); tempLowestPrice !== null; tempLowestPrice = regex.exec(firstPriceUnparsed)) {
      firstLowestPrice = Math.min(+tempLowestPrice[0].replace('$', ''), firstLowestPrice);
    }
    for (let tempLowestPrice = regex.exec(secondPriceUnparsed); tempLowestPrice !== null; tempLowestPrice = regex.exec(secondPriceUnparsed)) {
      secondLowestPrice = Math.min(+tempLowestPrice[0].replace('$', ''), secondLowestPrice);
    }

    if (firstIndex < secondIndex) {
      return [firstLowestPrice, secondLowestPrice];
    }

    return [secondLowestPrice, firstLowestPrice];
  }

  async getTwoRandomProductCards(count?: number) {
    const firstRandomProduct = await this.getRandomCardIndex(count);
    let secondRandomProduct = await this.getRandomCardIndex(count);

    while (firstRandomProduct === secondRandomProduct) {
      secondRandomProduct = await this.getRandomCardIndex(count);
    }

    return [this.productCards.nth(firstRandomProduct), firstRandomProduct, this.productCards.nth(secondRandomProduct), secondRandomProduct];
  }

  async goToPriceHighLowSort() {
    if (brand === 'br' || brand === 'brf') {
      await this.clickFilterFacetButton();
      await this.clickSortFacetButton();
      await this.page.locator('li:text("price: high–low"), div[aria-hidden="false"] label:has(input#high)').click();
      await this.filterDone.click();
    } else if (breakpoint === 'mobile') {
      await this.page.locator('select#sortBySelect').selectOption('price: high–low');
    } else {
      await this.sortByDesktop.click();
      await this.page.locator('li:text("price: high–low"), div[aria-hidden="false"] label:has(input#high)').click();
    }
  }

  async getItemCount() {
    return +(await this.page.locator(`div[aria-label*="in the product grid"]`).first().innerText({ timeout: 0 })).match(/\d+/g)![0];
  }

  getFlexFacetCount() {
    return this.flexFacets.count();
  }

  async clickNthFacet(n: number) {
    await this.flexFacets.nth(n).click();
    await this.clickNthFacetOption();
  }

  async clickNthFacetOption() {
    await this.page.locator(`div[aria-hidden="false"] label`).first().click();
  }

  async clickNthCheckedFacetOption() {
    await this.page.locator('div[aria-hidden="false"] label:has([aria-checked="true"])').first().click();
  }
}
