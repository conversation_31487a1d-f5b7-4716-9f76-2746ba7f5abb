/* eslint-disable playwright/no-force-option */
import { Locator, type Page } from '@playwright/test';
import { scrollElementToCenter, scrollToBottomUntilPageDoesNotGrow, scrollToLocator } from '@/utils/helpers';

const breakpoint = process.env.BREAKPOINT;

export class ProductPage {
  public page: Page;

  // Locators
  readonly signInLink: Locator;
  readonly wantItSooner: Locator;
  readonly addToBag: Locator;
  readonly checkout: Locator;
  readonly keepShopping: Locator;
  readonly closeModal: Locator;
  readonly availableColor: Locator;
  readonly inStockColor: Locator;
  readonly selectedColor: Locator;
  readonly availableSize: Locator;
  readonly allSizes: Locator;
  readonly productDetails: Locator;
  readonly productInfo: Locator;
  readonly fitSizing: Locator;
  readonly fabricCare: Locator;
  readonly recommendationsCarousel: Locator;
  readonly recommendedProduct: Locator;
  readonly reviewsWidget: Locator;
  readonly customerPhotosWidget: Locator;
  readonly shippingReturns: Locator;
  readonly sizeGuide: Locator;
  readonly showMore: Locator;
  readonly reviewRatings: Locator;
  readonly zeroStarRatings: Locator;
  readonly hasStarRatings: Locator;
  readonly writeReview: Locator;
  readonly writeFirstReview: Locator;
  readonly divisionBreadcrumbLink: Locator;
  readonly categoryreadcrumbLink: Locator;
  readonly errorMessage: Locator;
  readonly shippingAndReturnsModal: Locator;
  readonly sizeGuideModal: Locator;
  readonly productImage: Locator;
  readonly relatedCategoriesLink: Locator;
  readonly productPhotoVideo: Locator;
  readonly relatedItemsLink: Locator;
  readonly reviewHeadline: Locator;
  readonly reviewName: Locator;
  readonly reviewComments: Locator;
  readonly reviewEmail: Locator;
  readonly submitReview: Locator;

  constructor(page: Page) {
    this.page = page;
    this.signInLink = page.getByLabel('Fulfillment Method - Pickup').getByRole('link', { name: 'Sign in' });
    this.wantItSooner = page.getByRole('button', { name: 'Want it sooner? Find in store' });
    this.addToBag = page.locator(`#AddToBag_add-to-bag__button`);
    this.checkout = page.locator(`button[data-testid='checkoutButton']`);
    this.keepShopping = page.locator(`button[data-testid='keepShoppingButton']`);
    this.closeModal = page.locator(`button[aria-label='close modal']`);
    this.inStockColor = page.locator(`.swatch input[aria-disabled='false']`);
    this.availableColor = page.locator(`.swatch input[aria-checked='false']`).and(page.locator(`input[aria-disabled='false']`));
    this.selectedColor = page.locator(`.swatch input[aria-checked='true']`).and(page.locator(`input[aria-disabled='false']`));
    this.availableSize = page
      .getByLabel(/Size:[XSLM0-9](?!.*out of stock)/)
      .and(page.locator(`input[aria-checked='false']`))
      .first();
    this.allSizes = page.getByLabel(/Size:[XSLM0-9]/);
    this.productDetails = page.getByRole('button', { name: /product details/i }).or(page.locator(`button:text-matches("product details", "i")`));
    this.fitSizing = page.getByRole('button', { name: /^Fit.*Sizing$/i }).or(page.locator(`button:text-matches("Fit & Sizing", "i")`));
    this.fabricCare = page.getByRole('button', { name: /^Fabric.*Care$|^Materials.*Care$/i }).or(page.locator(`button:text-matches("fabric & care", "i")`));
    this.shippingReturns = page.getByRole('button', { name: /shipping & returns/i });
    this.sizeGuide = page.locator(`button.size-guide-button`).first();
    this.productInfo = page.locator(`div[class^="product-information"]`).first();
    this.recommendationsCarousel = page.locator(`div[class^="recommendations-carousel"]`);
    this.recommendedProduct = page.locator(`.recommendations-carousel a[href*="product.do"]`).locator('visible=true').first();
    this.reviewsWidget = page.locator(`.pdp-reviews-widget`);
    this.customerPhotosWidget = page.locator(`.pdp-customer-photos-widget`);
    this.showMore = page.locator(`.drawer-trigger-container div:text("Show More")`).first();
    this.reviewRatings = page.getByTestId(`star-ratings`).first().or(page.getByTestId(`fds_star-ratings`)).first();
    this.zeroStarRatings = page.getByTestId(`reviewRatingsAriaLabel`).filter({ hasText: /0 Ratings/i });
    this.hasStarRatings = page.getByTestId(`reviewRatingsAriaLabel`).filter({ hasText: /\((\d+)\) Image of 5 stars/i });
    this.writeFirstReview = page
      .getByTestId(`buy-box-wrapper`)
      .getByRole('button', { name: /^Write.*First Review$/i })
      .first();
    this.writeReview = page
      .getByRole('button', { name: /^Write.*Review$/i })
      .first()
      .or(page.getByRole('link', { name: /^Write.*Review$/i }).first());
    this.divisionBreadcrumbLink = page.locator(`nav[aria-label="breadcrumb"] a[href*="division.do"]`).first();
    this.categoryreadcrumbLink = page.locator(`nav[aria-label="breadcrumb"] a[href*="category.do"]`).first();
    this.errorMessage = page.locator(`.messages__error-messaging`);
    this.shippingAndReturnsModal = page.locator(`.iframe-returns-information`);
    this.sizeGuideModal = page.locator(`h2#fui-modal-Size-Guide`);
    this.productImage = page
      .locator(`.l--breadcrumb-photo-wrapper`)
      .or(page.locator(`.product_photos-container`))
      .locator('img')
      .locator('visible=true')
      .first();
    this.relatedCategoriesLink = page.locator(`a[data-testid="tag-link"]`).first();
    this.productPhotoVideo = page.locator(`video[class="product-photo--video"]`);
    this.relatedItemsLink = page.locator(`a[data-testid="tag-link"]`).first();
    this.reviewHeadline = page.getByRole('textbox', { name: 'Review Headline' }).first();
    this.reviewComments = page.locator(`#pr-comments-input`).first();
    this.reviewName = page.getByRole('textbox', { name: /First Name, Last Initial|Nickname|Preferred Name/i }).first();
    this.reviewEmail = page.getByRole('textbox', { name: 'Email' }).first();
    this.submitReview = page.getByRole('button', { name: 'Submit Review' }).first();
  }

  async goToProductPage(productId: string) {
    await this.page.goto(`browse/product.do?pid=${productId}`);
  }
  async clickSignIn(): Promise<Page> {
    try {
      await this.wantItSooner.waitFor({ timeout: 5000 });
      if ((await this.wantItSooner.getAttribute('aria-expanded')) === 'false') {
        await this.wantItSooner.click();
      }
      // eslint-disable-next-line no-empty
    } catch {}
    return (await Promise.all([this.page.waitForEvent('popup'), this.signInLink.click()]))[0];
    // eslint-disable-next-line no-empty
  }

  async selectColor(index: number) {
    await this.availableColor.nth(index).click();
  }

  async selectSize() {
    if (await this.availableSize.isVisible()) {
      await scrollElementToCenter(this.availableSize);
      await this.availableSize.click({ force: true });
    }
  }

  async addProductToBag() {
    await scrollToLocator(this.page, this.addToBag);
    breakpoint === 'mobile' && (await scrollElementToCenter(this.addToBag));
    await this.addToBag.click({ force: breakpoint === 'mobile' });
  }

  async clickKeepShopping() {
    await this.page.addLocatorHandler(this.keepShopping, async () => this.keepShopping.click({ force: true }));
    await this.page.addLocatorHandler(this.closeModal, async () => this.closeModal.click());
  }

  async selectQuantity(quantity: string) {
    await this.page.locator('.pdp-quantity__control').click();
    await this.page.locator(`.pdp-quantity__option:text-is('${quantity}')`).click();
  }

  async clickDivisionBreadcrumb() {
    await scrollToBottomUntilPageDoesNotGrow(this.page);
    breakpoint === 'mobile' && (await scrollElementToCenter(this.divisionBreadcrumbLink));
    await this.divisionBreadcrumbLink.click({ force: breakpoint === 'mobile' });
  }

  async clickCategoryBreadcrumb() {
    await scrollToBottomUntilPageDoesNotGrow(this.page);
    breakpoint === 'mobile' && (await scrollElementToCenter(this.categoryreadcrumbLink));
    await this.categoryreadcrumbLink.click({ force: breakpoint === 'mobile' });
  }

  getSizeSelector() {
    return `div[class*="in-stock"] input.pdp-dimension__radio`;
  }

  async clickInStorePickupOption() {
    breakpoint === 'mobile' && (await scrollElementToCenter(this.page.locator(`div.bopis-label-wrapper`).first()));
    await this.page
      .locator(`div.bopis-label-wrapper`)
      .first()
      .click({ force: breakpoint === 'mobile' });
  }

  async bopisErrorMessageIsVisible() {
    return this.page.locator(`.fulfillment-method-pickup__notification`).isVisible();
  }

  async inStorePickupOptionIsChecked() {
    return this.page.locator(`div.bopis-label-wrapper`).first().isChecked();
  }

  getAddedToBagLocator() {
    return this.page.locator(`h2[id^="fui-modal-Added-to"]`);
  }

  async selectFirstAvailableSize() {
    const size = this.page.locator(this.getSizeSelector()).first();
    await size.click({ timeout: 30000 });
    return size.inputValue();
  }

  async productImageIsVisible() {
    return this.page.locator(`.product_photos-container`).isVisible();
  }

  async clickReviews() {
    if (process.env.BRAND === 'br' || process.env.BRAND === 'brf') {
      await this.page.locator(`button[aria-label="Reviews"]`).first().click();
    } else {
      await this.reviewRatings.click();
    }
  }

  async getNumberOfReviews() {
    await this.page.locator(`.review-ratings`).first().hover({ timeout: 0 }).catch();
    return this.page.locator(`.review-ratings > div[aria-hidden="true"]`).first().innerText();
  }

  async clickSizeGuide() {
    if (process.env.BRAND?.startsWith('br')) {
      await this.fitSizing.click();
    }
    await scrollElementToCenter(this.sizeGuide);
    await this.sizeGuide.click({ timeout: 10000, force: true });
  }

  async selectRecommendedProduct() {
    breakpoint === 'mobile' && (await scrollElementToCenter(this.recommendedProduct));
    await this.recommendedProduct.click({ force: breakpoint === 'mobile' });
  }

  async getErrorMessageText() {
    return this.page.locator(`.messages__error-messaging`).first().innerText();
  }

  async clickRelatedCategoriesLink() {
    await scrollToBottomUntilPageDoesNotGrow(this.page);
    await scrollElementToCenter(this.relatedCategoriesLink);
    await this.relatedCategoriesLink.click({ force: true });
  }

  async clickRelatedItemsLink() {
    await scrollToBottomUntilPageDoesNotGrow(this.page);
    await scrollElementToCenter(this.relatedItemsLink);
    await this.relatedItemsLink.click({ force: true });
  }
}
