/* eslint-disable playwright/no-force-option */
import { Locator, type Page } from '@playwright/test';
import { RewritePLPPage } from './rewrite-plp.page';
import { scrollElementToCenter, scrollToBottomUntilPageDoesNotGrow, waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;

export class RewriteCategoryPage extends RewritePLPPage {
  // Page instance
  readonly page: Page;

  // Locators
  readonly relatedItemsLink: Locator;
  readonly bopisToggle: Locator;

  constructor(page: Page) {
    super(page);
    this.relatedItemsLink = page.locator(`a[data-testid="tag-link"]`).first();
    this.bopisToggle = page.locator(`.fds_switch__span`).first();

    // Page instance
    this.page = page;

    // Locators
  }

  async goToCategoryPage(categoryId: string, styleId: string | undefined = undefined) {
    await this.page.goto(`browse/category.do?cid=${categoryId}${styleId ? `&style=${styleId}` : ''}`);
    await waitForHydration(this.page);
    await this.waitForProductCard();
    return (await this.page.title()).split('|')[0];
  }

  async clickDivisionBreadcrumb() {
    await scrollToBottomUntilPageDoesNotGrow(this.page);
    await scrollElementToCenter(this.page.locator(`nav[aria-label="breadcrumb"] > span > a:nth-child(1)`).first());
    await this.page.locator(`nav[aria-label="breadcrumb"] > span > a:nth-child(1)`).first().click({ force: true });
  }

  async clickRelatedItemsLink() {
    await scrollToBottomUntilPageDoesNotGrow(this.page);
    await scrollElementToCenter(this.relatedItemsLink);
    await this.relatedItemsLink.click({ force: true });
  }

  async clickBopisFilter() {
    await this.page.locator(`div.bopis-bar__details label[role="switch"]`).click();
  }

  async openBopisStoreLocator() {
    if (process.env.BRAND === 'br' || process.env.BRAND === 'brf') {
      await this.page.locator(`.product-grid button:text("Change Store")`).click({ timeout: 60000 });
    } else {
      await this.page.locator(`button:near(div.bopis-bar__details)`).first().click({ timeout: 60000 });
    }
  }

  async bopisSearchByZip(zipCode: string) {
    return this.page.locator(`input#plp-change-store-modal--postalcode-input`).fill(zipCode);
  }

  async openBopisStoreModal() {
    await this.bopisToggle.waitFor({ timeout: 60000 });
    await scrollElementToCenter(this.bopisToggle);
    // return this.bopisToggle.click({ force: true });
    return this.bopisToggle.dispatchEvent('click');
  }

  async selectFirstAvailableStore() {
    return this.page.locator(`button:text("SEE AVAILABILITY")`).first().click();
  }

  async selectBopisStore(zip: string) {
    await this.openBopisStoreModal();
    await this.bopisSearchByZip(zip);
    await this.page.keyboard.press('Enter');
    await this.selectFirstAvailableStore();
    await this.waitForProductCard();
  }

  async selectProduct() {
    await this.waitForProductCard();
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await this.page.waitForTimeout(2000);
    const product = await this.getRandomProductCard();
    const productTitle = await product.innerText({ timeout: 30000 });
    await scrollElementToCenter(product);
    await product.click(`${brand}${market}` === 'gpca' ? { position: { x: 20, y: 20 } } : {});
    await this.page.waitForURL(/product/);
    return productTitle;
  }
}
