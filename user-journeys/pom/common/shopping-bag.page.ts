/* eslint-disable no-console */
import fs, { readFileSync } from 'fs';
import * as subProcess from 'child_process';
import { type APIRequestContext, APIResponse, expect, type Locator, type Page } from '@playwright/test';
import _ from 'lodash';
import { delay } from '@/utils/helpers';
import { getAzeusAPIURL, getBaseURL, getSecureURL } from '@/utils/urls';
import Builder from '@/test_data/builder';

const { ENV, BRAND, MARKET } = process.env;

export class ShoppingBagPage {
  // Page
  readonly page: Page;

  // Builder
  readonly builder: Builder;

  // API Request Context
  readonly request: APIRequestContext;

  // API Brand Formats
  readonly locale: string;

  // Stage data values
  readonly stylesAvailableFromProductDataGenerator: number;

  // Locators
  readonly emptyBagHeader: Locator;
  readonly shippingItemCount: Locator;
  readonly productImage: Locator;
  readonly secondProductImage: Locator;
  readonly productTitle: Locator;
  readonly productNameLink: Locator;
  readonly secondProductNameLink: Locator;
  readonly secondProductTitle: Locator;
  readonly emptyBagSignIn: Locator;
  readonly productSize: Locator;
  readonly productColor: Locator;
  readonly productPrice: Locator;
  readonly productQuantity: Locator;
  readonly deleteProduct: Locator;
  readonly addQuantity: Locator;
  readonly removeQuantity: Locator;
  readonly saveForLater: Locator;
  readonly findAStore: Locator;
  readonly moveToBag: Locator;
  readonly promoInput: Locator;
  readonly savedForLaterItemCount: Locator;
  readonly emptySavedBag: Locator;
  readonly savedForLaterSignInLink: Locator;
  readonly deleteSaveForLaterProduct: Locator;
  readonly promoErrorMessage: Locator;
  readonly closeFindAStoreModal: Locator;
  readonly subTotal: Locator;
  readonly estTotal: Locator;
  readonly checkoutButton: Locator;
  readonly aiRecommendationsPrice: Locator;
  readonly aiRecommendationsCard: Locator;
  readonly aiRecommendationsImage: Locator;
  readonly aiRecommendations: Locator;

  // Strings
  readonly usedProductIds: string[] = [];
  readonly usedStyles: string[] = [];
  readonly secureURL: string;
  brandType: string;
  clientRequestId: string;
  cachedStyles: { [key: string]: string } = {};

  constructor(page: Page) {
    this.page = page;

    this.builder = new Builder();

    this.request = this.page.request;

    this.locale = MARKET === 'us' ? 'en_US' : 'en_CA';

    this.brandType = BRAND === 'brf' || BRAND === 'gpf' ? 'factory' : 'specialty';
    this.brandType = MARKET === 'ca' ? 'ca' : this.brandType;
    this.brandType = MARKET === 'ca' && BRAND === 'brf' ? 'factory' : this.brandType;

    this.stylesAvailableFromProductDataGenerator = 5;

    this.emptyBagHeader = page.getByTestId('empty-bag').first();
    this.shippingItemCount = page.locator(`div[data-testid="ship-items-count"]`).first();
    this.productImage = page.locator(`article[data-testid="product-image"]`).first();
    this.secondProductImage = page.locator(`article[data-testid="product-image"]`).nth(1);
    this.productTitle = page.locator(`h2[data-testid="product-name"]`).first().locator('a');
    this.productNameLink = page.locator(`h2[data-testid="product-name"]`).first().locator('a').first();
    this.secondProductTitle = page.locator(`h2[data-testid="product-name"]`).nth(1);
    this.secondProductNameLink = page.locator(`h2[data-testid="product-name"]`).nth(1).locator('a').first();
    this.emptyBagSignIn = page.locator(`button[data-testid="empty-bag-signin-button"]`).first();
    this.productSize = page.locator(`span[data-testid="product-size"]`).first();
    this.productColor = page.locator(`span[data-testid="product-color"]`).first();
    this.productPrice = page.locator(`div[data-testid="product-price"]`).first();
    this.productQuantity = page.locator(`div[data-testid="product-quantity"]`).first();
    this.deleteProduct = page.locator(`button[data-testid="delete-bag-item"]`).first();
    this.addQuantity = page.locator(`button[data-testid="stepper-plus"]`).first();
    this.removeQuantity = page.locator(`button[data-testid="stepper-minus"]`).first();
    this.saveForLater = page.locator(`button[data-testid="save-for-later"]`).first();
    this.findAStore = page.locator(`[data-testid="change-store-modal"]`).first();
    this.moveToBag = page.locator(`button[data-testid="move-to-active-bag"]`).first();
    this.promoInput = page.locator(`input[data-testid="promo-code-input"]`).first();
    this.savedForLaterItemCount = page.locator(`#saved-bag-item-count`).first();
    this.emptySavedBag = page.locator(`#empty-saved-bag`).first();
    this.savedForLaterSignInLink = page.locator(`#empty-saved-bag, button:text("Sign In")`).first();
    this.deleteSaveForLaterProduct = page.locator(`button[data-testid="delete-saved-item"]`).first();
    this.promoErrorMessage = page.locator(`div[role="alert"]`).locator(`.text-cb-textColor-error`).first();
    this.promoErrorMessage = page.locator(`div.text-cb-textColor-error.cb-base-note.mt-1`).first();
    this.closeFindAStoreModal = page.locator(`svg[data-testid="close-icon"]`).first();
    this.subTotal = page.getByTestId('subtotal-value');
    this.estTotal = page.getByTestId('est-total-value');
    this.checkoutButton = page.getByTestId('checkout-button').first();
    this.secureURL = getSecureURL()!;
    this.aiRecommendationsPrice = page.getByTestId('price-block');
    this.aiRecommendationsCard = page.getByTestId('recommended-product-card');
    this.aiRecommendationsImage = this.aiRecommendationsCard.locator('img');
    this.aiRecommendations = page.locator('#product-recommendations');

    this.clientRequestId = MARKET === 'ca' ? '97d4d143-3c55-4af2-bd3b-af3ed15f3742' : '7430d7f0-f9ce-4a99-a54a-67864d41745b';
  }

  async emptyBagHeaderText() {
    return this.page
      .locator('#empty-bag-header')
      .or(this.page.getByTestId('empty-bag'))
      .or(this.page.locator('.cb-display-default-emphasis'))
      .first()
      .innerHTML();
  }

  async goToShoppingBagPage() {
    await this.page.goto(`${getSecureURL()}shopping-bag`);
    await this.page.waitForLoadState('load');
  }

  getBagSubTotal() {
    return this.page.locator('#subtotal-value').innerText();
  }

  getTotalPrice() {
    return this.page.locator(`span[data-testid="est-total-value"]`).innerText();
  }

  async applyPromoCode(promCode: string) {
    await this.promoInput.fill(promCode);
    await this.page.locator(`button[data-testid="apply-promo-code-button"]`).click();
  }

  async enterBopisZip() {
    await this.page.locator(`.zipcode-input-container input`).fill(this.builder.withBopisZipCode());
    await this.page.keyboard.press('Enter');
  }

  bopisStoreIsVisible() {
    return this.page.locator(`section.stores`).first().isVisible();
  }

  async getAllProductNames() {
    return this.page.locator(`h2[data-testid="product-name"]`);
  }

  async addStandardProductToBag(quantity: number = 1, reload = true, brand: string = BRAND!, market: string = MARKET!) {
    const productId = await this.getStandardProduct(brand, market);

    const response = await this.addToBag(productId!.toString(), quantity, brand, market);

    await expect(response!, `validate the the sku ${productId} was successfully added to bag`).toBeOK();
    this.usedProductIds.push(productId!);

    reload && (await this.page.reload());
  }

  async addToBag(productId: string | number, productQty: string | number, brand: string = BRAND!, market: string = MARKET!) {
    let addToBagURL;

    switch (ENV) {
      case 'prod':
        switch (`${brand}`) {
          case 'at':
          case 'br':
          case 'gp':
          case 'on':
            addToBagURL = `https://api.gap${market === 'ca' ? 'canada' : ''}.${market === 'ca' ? 'ca' : 'com'}/commerce/shopping-bags/items/summary?locale=${this.locale}`;
            break;
          case 'brf':
          case 'gpf':
            addToBagURL = `https://api.gapfactory.${market === 'ca' ? 'ca' : 'com'}/commerce/shopping-bags/items/summary?locale=${this.locale}`;
            break;
          default:
            break;
        }
        break;
      case 'preview':
        addToBagURL = `${getAzeusAPIURL()}commerce/shopping-bags/items/summary?locale=${this.locale}`;
        break;
      case 'stage':
      case 'local':
        // eslint-disable-next-line no-param-reassign
        addToBagURL = `https://ecom-api.stage.${brand === 'brf' || brand === 'gpf' ? 'factory-' : ''}gaptechol.${market === 'ca' ? 'ca' : 'com'}/commerce/shopping-bags/items/summary?locale=${this.locale}`;
        break;
      default:
        break;
    }

    const response = await this.page.request.post(addToBagURL!, {
      headers: {
        ContentType: 'application/json',
        Accept: '*/*',
        guest: 'true',
        market: this.getMarketValues(market),
        brand: this.getBrandValues(brand)[0],
        brandtype: this.brandType,
        channel: 'WEB',
        locale: this.locale,
        'X-Gap-Apimode': 'leapfrog',
        Clientid: 'CXA',
        Origin: getBaseURL().replace(/\/$/, ''),
        Referer: getBaseURL(),
        ...(ENV === 'preview' ? { Previewtype: 'WIP' } : {}),
      },
      data: {
        brand: this.getBrandValues(brand)[0],
        market: this.getMarketValues(market),
        items: [
          {
            sku: productId.toString(),
            quantity: productQty.toString(),
          },
        ],
      },
    });

    return response;
  }

  // eslint-disable-next-line consistent-return
  async addToCatalog(sku: string | number, brand: string = BRAND!, market: string = MARKET!) {
    const requestHeaders = `brand,market,channel,locale,productId\n`;
    const requestPrefix = `${this.getCatalogAPIBrandValues(brand)},${this.getMarketValues(market) === 'CA' ? 'CAN' : this.getMarketValues(market)},ONL,${this.locale},`;

    fs.mkdirSync(`./skus/${sku.toString()}`, { recursive: true });
    fs.writeFileSync(`./skus/${sku.toString()}/sku.txt`, `${requestHeaders}${requestPrefix}${sku.toString()}`);
    fs.writeFileSync(`./skus/${sku.toString()}/style.txt`, `${requestHeaders}${requestPrefix}${sku.toString().substring(0, 6)}`);
    fs.writeFileSync(`./skus/${sku.toString()}/cc.txt`, `${requestHeaders}${requestPrefix}${sku.toString().substring(0, 9)}`);
    subProcess.execSync(`cd ./skus/${sku.toString()}/ && zip style.zip style.txt`);
    subProcess.execSync(`cd ./skus/${sku.toString()}/ && zip cc.zip cc.txt`);
    subProcess.execSync(`cd ./skus/${sku.toString()}/ && zip sku.zip sku.txt`);

    const requestTypes = ['style', 'cc', 'sku'];

    for (let i = 0; i < requestTypes.length; i++) {
      const requestType = requestTypes[i];
      const response = await this.page.request.post(
        `https://catalog-apis-test-data-generator.aks.stage.azeus.gaptech.com/product-data-generator/${requestType}/upload`,
        {
          headers: {
            'X-Client-Application-Name': `CXA`,
          },
          multipart: {
            file: fs.createReadStream(`./skus/${sku.toString()}/${requestType}.zip`),
          },
          params: {
            inventoryAware: 'true',
            priceAware: 'true',
          },
          timeout: 90000,
        }
      );

      await expect(response).toBeOK();
    }

    // eslint-disable-next-line playwright/no-wait-for-timeout
    await delay(2000);

    let retrySyncRequestCount = 0;
    while (!(await this.checkProductSync(sku.toString(), brand, market)).ok() && retrySyncRequestCount < 10) {
      // eslint-disable-next-line no-await-in-loop
      await delay(2000);
      retrySyncRequestCount += 1;
    }
  }

  async checkProductSync(skuOrStyle: string, brand: string = BRAND!, market: string = MARKET!, env: string = ENV!): Promise<APIResponse> {
    const syncEnvironment = env === 'local' || env === 'stage' ? 'stage' : 'prod';
    const response = await this.request.get(`https://catalog-apis-omni-product-service.aks.${syncEnvironment}.azeus.gaptech.com/v3/products`, {
      headers: {
        'X-Client-Application-Name': 'CXA',
      },
      params: {
        product_id: skuOrStyle.toString(),
        brand: this.getCatalogAPIBrandValues(brand),
        market: this.getMarketValues(market) === 'CA' ? 'CAN' : this.getMarketValues(market),
        locale: this.locale,
        ...(env === 'preview' ? { requestType: 'PREVIEW_WIP' } : {}),
      },
    });

    return response;
  }

  async getStandardStyle(
    keyword: string = 'shirts',
    pageSize: number = 20,
    brand: string = BRAND!,
    market: string = MARKET!,
    returnFullList: boolean = false
  ): Promise<string> {
    const cacheKey = `${keyword}_${pageSize}_${brand}_${market}`;
    if (this.cachedStyles[cacheKey]) {
      let styleToReturn;
      do {
        styleToReturn = _.sample(JSON.parse(this.cachedStyles[cacheKey])).id;
      } while (this.usedStyles.includes(styleToReturn) && this.usedStyles.length < pageSize);
      if (!styleToReturn) {
        return 'nostylefound';
      }
      this.usedStyles.push(styleToReturn);
      return styleToReturn;
    }

    switch (brand) {
      case 'brf':
        // eslint-disable-next-line no-param-reassign
        brand = 'brfs';
        break;
      case 'gpf':
        // eslint-disable-next-line no-param-reassign
        brand = 'gapfs';
        break;
      case 'gp':
        // eslint-disable-next-line no-param-reassign
        brand = 'gap';
        break;
      default:
        break;
    }

    const locale = `en_${market.toUpperCase()}`;
    const response = await this.request.get(`https://api.${MARKET === 'us' ? 'gap.com' : 'gapcanada.ca'}/commerce/search/products/v2/style`, {
      params: {
        pageSize,
        brand,
        market,
        locale,
        ignoreInventory: false,
        realm: 'prod',
        keyword,
      },
    });

    await expect(response).toBeOK();

    const productInfo = JSON.parse(await response.text());

    if (returnFullList) return JSON.stringify(productInfo.products);

    const selectedProduct = _.sample(productInfo.products);

    this.cachedStyles[cacheKey] = JSON.stringify(productInfo.products);

    this.usedStyles.push(selectedProduct.id);

    return selectedProduct.id;
  }

  async getStandardProduct(brand: string = BRAND!, market: string = MARKET!, attempt: number = 0): Promise<string> {
    if (ENV === 'stage' || ENV === 'local') {
      const availableSKUs = readFileSync(
        `${process.cwd()}/test_data/github-test-data/${this.getStageDataBrandValues(brand)}_${market === 'ca' ? 'CAN' : market.toUpperCase()}_skus.txt`
      )
        .toString()
        .split('\n')
        .filter(sku => sku !== '');
      if (availableSKUs.length === 0) {
        // eslint-disable-next-line no-console
        console.error(
          'No skus downloaded from the GitHub repository for use. Check the repository for the latest data. https://github.gapinc.com/ecomfrontend/playwright-stage-product-data'
        );
        process.exit(1);
      }
      return _.sample(availableSKUs)!;
    }

    let randomStyle = await this.getStandardStyle('shirts', 20, brand === 'gp' ? 'gap' : brand, market);

    if (randomStyle === 'nostylefound') {
      randomStyle = await this.getStandardStyle('shirts', 100, brand === 'gp' ? 'gap' : brand, market);
    }

    const syncStatusString = (await (await this.checkProductSync(randomStyle, brand, market, ENV)).body()).toString();

    const syncStatus = JSON.parse(syncStatusString);

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const allSkusInStock: string[] = [];
    this.allNodes(syncStatus, 'skus').forEach(skus => {
      console.log('skus', JSON.stringify(skus, null, 2));
      skus.forEach((sku: { alternate_ids: { online_legacy_sku_number: string }; inventory_status: { status: string } }) => {
        if (sku.inventory_status.status === 'IN_STOCK' && !this.usedProductIds.includes(sku.alternate_ids.online_legacy_sku_number)) {
          allSkusInStock.push(sku.alternate_ids.online_legacy_sku_number);
        }
      });
    });

    if (allSkusInStock.length === 0) {
      expect(attempt, `validate that we haven't checked all searched products to have met the requirements`).toBeLessThan(20);
      return await this.getStandardProduct(brand, market, attempt + 1);
    }

    const returningSku = _.sample(allSkusInStock);
    this.usedProductIds.push(returningSku!);

    return returningSku!;
  }

  async getMultiVariantStyle() {
    const products = JSON.parse(await this.getStandardStyle('pants', 20, BRAND!, MARKET!, true));

    const availableStyles: string[] = [];

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    products.forEach((product: { colors: any[]; id: string }) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      product.colors.forEach((color: { skuSizes: any[] }) => {
        const currentColorVariants: string[] = [];
        color.skuSizes.forEach((size: string) => {
          const sizeVariant = size.split('|')[0];
          !currentColorVariants.includes(sizeVariant) && currentColorVariants.push(sizeVariant);
        });
        if (currentColorVariants.length > 1 && !availableStyles.includes(product.id) && !this.usedStyles.includes(product.id)) {
          availableStyles.push(product.id);
        }
      });
    });

    const selectedStyle = _.sample(availableStyles);

    expect(selectedStyle, `No multi-variant styles found for the given criteria.`).toBeDefined();
    this.usedStyles.push(selectedStyle!);
    return selectedStyle!;
  }

  async clearShoppingBag(reload = true) {
    const [, brand] = this.getBrandValues(BRAND);
    const market = this.getMarketValues(MARKET);

    while ((await this.getShoppingBagQuantity()) !== 0) {
      const lineItemId = await this.getLineItemId();

      const response = await this.page.request.post(`${this.secureURL}shopping-bag-xapi/delete-bag-item`, {
        headers: {
          ContentType: 'application/json',
          Accept: '*/*',
          guest: 'true',
          market,
          brand: brand,
          brandtype: this.brandType,
          channel: 'WEB',
          locale: this.locale,
          clientRequestId: this.clientRequestId,
          Origin: getBaseURL().replace(/\/$/, ''),
          Referer: getBaseURL(),
        },
        data: {
          lineItemId,
        },
      });

      await expect(response).toBeOK();
    }
    reload && (await this.page.reload());
  }

  async getShoppingBag() {
    const [, brand] = this.getBrandValues(BRAND);
    const market = MARKET!.toUpperCase();

    const response = await this.page.request.get(`${this.secureURL}shopping-bag-xapi/get-bag`, {
      headers: {
        ContentType: 'application/json',
        Accept: '*/*',
        guest: 'true',
        market,
        brand: brand,
        brandtype: this.brandType,
        channel: 'WEB',
        locale: this.locale,
        clientRequestId: this.clientRequestId,
        Origin: getBaseURL().replace(/\/$/, ''),
        Referer: getBaseURL(),
      },
    });

    await expect(response).toBeOK();

    return response;
  }

  async getShoppingBagQuantity(prefetchedResponse: APIResponse | undefined = undefined) {
    const response = prefetchedResponse ? prefetchedResponse : await this.getShoppingBag();
    const responseBody = JSON.parse(await response.text());
    return responseBody.productList.length;
  }

  async getLineItemId(prefetchedResponse: APIResponse | undefined = undefined) {
    const response = prefetchedResponse ? prefetchedResponse : await this.getShoppingBag();
    const responseBody = JSON.parse(await response.text());
    expect(_.isEmpty(responseBody.productList), `ShoppingBagApi: get-bag shows no line items`).toBe(false);
    return responseBody.productList[0].id;
  }

  // eslint-disable-next-line class-methods-use-this
  getBrandValues(brand: string = BRAND!) {
    let returnBrand = brand!.toUpperCase();
    let headerBrand;
    if (returnBrand === 'BRF' || returnBrand === 'GPF') {
      if (returnBrand === 'BRF') {
        headerBrand = 'BF';
      } else {
        headerBrand = 'GF';
      }
      returnBrand += 'S';
    } else {
      headerBrand = returnBrand;
    }
    return [returnBrand, headerBrand];
  }

  getCatalogAPIBrandValues(brand: string = BRAND!) {
    const [returnBrand] = this.getBrandValues(brand);
    switch (returnBrand) {
      case 'GPFS':
        return 'GO';
      case 'GP':
        return 'GAP';
      default:
        return returnBrand;
    }
  }

  // eslint-disable-next-line class-methods-use-this
  getStageDataBrandValues(brand: string = BRAND!) {
    switch (brand) {
      case 'brf':
        return 'BRFS';
      case 'gp':
        return 'GAP';
      case 'gpf':
        return 'GO';
      default:
        return brand.toUpperCase();
    }
  }

  // eslint-disable-next-line class-methods-use-this
  getMarketValues(market: string = MARKET!) {
    return market!.toUpperCase();
  }

  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-explicit-any
  allNodes(obj: { [x: string]: any } | null, key: string | RegExp, array: any[] = []) {
    if (typeof obj === 'object' && obj !== null) {
      for (const k in obj) {
        if ((typeof key === 'string' && k === key) || (key instanceof RegExp && key.test(k))) {
          array.push(obj[k]);
        }
        this.allNodes(obj[k], key, array);
      }
    }
    return array;
  }
}
