/* eslint-disable playwright/no-force-option */
import { Locator, expect, type Page } from '@playwright/test';
import { scrollElementToCenter, scrollToBottomUntilPageDoesNotGrow, scrollToLocator } from '../../utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;

interface DictType {
  'c([0-9]{1,3})': string;
  cc: string;
  ch: string;
  events: string;
  g: string;
  'h([0-9]{1,2})': string;
  'l([0-9]{1,2})': string;
  pageName: string;
  pageType: string;
  products: string;
  purchaseID: string;
  server: string;
  tnt: string;
  'v([0-9]{1,3})': string;
  v0: string;
  vid: string;
  xact: string;
  zip: string;
}

const brandNameMap = {
  at: 'Athleta',
  br: 'Banana Republic',
  on: 'Old Navy',
  gap: 'Gap',
} as Record<string, string>;

export class Sitewide {
  readonly page: Page;
  readonly signInButton: Locator;
  readonly divisionLinkAthleta: Locator;
  readonly divisionLink: Locator;
  readonly divisionLinkBanana: Locator;
  readonly divisionLinkBananaFactoryCa: Locator;
  readonly signInYourAccount: Locator;
  readonly buyEGiftCardsLink: Locator;
  readonly buyGiftCardsLink: Locator;
  readonly ourValuesLink: Locator;
  readonly peoplePlanetLink: Locator;
  readonly powerOfSheLink: Locator;
  readonly wellProLink: Locator;
  readonly workAtAthletaLink: Locator;
  readonly sustainabilityLink: Locator;
  readonly myPointsLink: Locator;
  readonly exploreBenefits: Locator;
  readonly payCreditCardLink: Locator;
  readonly activateCardLink: Locator;
  readonly joinRewardsLink: Locator;
  readonly applyForCreditCardLink: Locator;
  readonly emailSignUpLink: Locator;
  readonly bagIcon: Locator;
  readonly gapFooterLink: Locator;
  readonly brFooterLink: Locator;
  readonly onFooterLink: Locator;
  readonly privacyPolicyLink: Locator;
  readonly privacyChoicesLink: Locator;
  readonly brandHeader: Locator;
  readonly privacyPreferenceToggle: Locator;
  readonly privacyPreferenceSubmit: Locator;
  readonly privacyPreferenceStatus: Locator;
  readonly calPrivacyLink: Locator;
  readonly careers: Locator;
  readonly termsOfUseLink: Locator;
  readonly sustainabilityLegalLink: Locator;
  readonly socialResponsibility: Locator;
  readonly aboutGapLink: Locator;
  readonly brFactoryStoreLink: Locator;
  readonly americansDisabilitiesActLink: Locator;
  readonly ontariansDisabilitiesActLink: Locator;
  readonly caSupplyChainsLink: Locator;
  readonly gapIncPoliciesLink: Locator;
  readonly edfsSignInJoinLink: Locator;
  readonly edfsDetailsLink: Locator;
  readonly joinRewardsDropDownLink: Locator;
  readonly orderReturnsDropDownLink: Locator;
  readonly pointsDropDownLink: Locator;
  readonly giftCardDropDownLink: Locator;
  readonly customerServiceLink: Locator;
  readonly shippingFooterLink: Locator;
  readonly returnsFooterLink: Locator;
  readonly trackYourOrderLink: Locator;
  readonly giftCardsFooterLink: Locator;
  readonly sizeAndFitGuidesLink: Locator;
  readonly searchSuggestions: Locator;
  readonly downloadOurApp: Locator;
  readonly storesAndServices: Locator;
  readonly buyOnlinePickUpinStore: Locator;
  readonly atelierStylingService: Locator;
  readonly babyStores: Locator;
  readonly artStores: Locator;
  readonly brPhoneNumber: Locator;
  readonly rewardsUtilityLink: Locator;
  readonly weAreChangeLink: Locator;
  readonly nextImage: Locator;
  readonly ourAffiliateProgram: Locator;
  readonly superCashFooter: Locator;
  readonly searchInput: Locator;
  readonly searchForm: Locator;
  readonly searchSubmitIcon: Locator;
  readonly dismissSearch: Locator;
  readonly mobileSearchButton: Locator;
  readonly mobileSearchInput: Locator;
  readonly mobileSearchBox: Locator;
  readonly footerLocaleLink: Locator;
  readonly expandCustomerService: Locator;
  readonly certonaRecommendations: Locator;
  readonly storeLocatorLink: Locator;
  readonly rewardsFooterLink: Locator;
  readonly bagCount: Locator;
  readonly gapFactoryPopup: Locator;
  readonly gapFactoryPopupClose: Locator;
  readonly meganavWrapper: Locator;

  // Analytics
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonly adobeAnalyticsData: any[] = [];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonly tealiumAnalyticsData: any[] = [];

  constructor(page: Page) {
    this.page = page;
    const footer = page
      .locator('#sitewide-footer:visible')
      .or(
        page
          .locator('.sitewide-xppbfq:visible')
          .or(page.locator('#footer:visible').or(page.locator('.br-footer:visible').or(page.locator('.on-footer:visible'))))
      )
      .first();
    this.babyStores = page.getByRole('link', { name: /BR Baby Stores/ });
    this.brPhoneNumber = page.locator(`1-888-BRSTYLE (**************)`);
    this.artStores = page.getByRole('link', { name: /BR Art Stores/ });
    this.atelierStylingService = page.getByRole('link', { name: /Styling Service/ });
    this.buyOnlinePickUpinStore = footer.getByRole('link', {
      name: /Buy Online[,.] Pick [Uu]p [iI]n[\s-][Ss]tore/,
    });
    this.signInButton = page.locator(`a[data-testid="account-dropdown-sign-in"]`).first();
    this.signInYourAccount = page.getByLabel('my account button').locator('> button').or(page.getByTestId('account-icon')).first();
    this.divisionLinkAthleta = page.locator(`a[aria-label="athleta girl"]`).first();
    this.divisionLink = page.locator(`.topnav-container a[aria-label^="women"]`).first();
    this.divisionLinkBanana = page.locator(`.topnav-container a[aria-label^="sale"]`).first();
    this.divisionLinkBananaFactoryCa = page.getByLabel("women's clothing", { exact: true });
    this.buyEGiftCardsLink = footer.getByRole('link', { name: 'BUY EGIFT CARDS' });
    this.buyGiftCardsLink = footer.getByRole('link', { name: 'Buy Gift Cards' });
    this.powerOfSheLink = footer.getByRole('link', { name: /Power of She/ });
    this.wellProLink = footer.getByRole('link', { name: 'WellPro' });
    this.workAtAthletaLink = footer.getByRole('link', { name: 'Search Jobs' });
    this.sustainabilityLink = footer.getByTestId('footer-textlink-container').getByRole('link', { name: 'Sustainability' });
    this.gapFooterLink = footer.getByTestId('mobile-customer-support').getByRole('link', { name: 'GAP' });
    this.brFooterLink = footer.getByRole('link', { name: 'BANANA REPUBLIC' });
    this.onFooterLink = footer.getByRole('link', { name: 'OLD NAVY' });
    this.myPointsLink = this.page.locator('#sitewide-footer').getByRole('link', {
      name: /My (?:Points|Rewards) (?:and|&) (?:Rewards|Benefits)/i,
    });
    this.exploreBenefits = footer.getByRole('link', { name: 'Explore Benefits' });
    this.payCreditCardLink = this.page.locator('#sitewide-footer').getByRole('link', { name: /pay credit card bill/i });
    this.activateCardLink = footer.getByRole('link', { name: /ACTIVATE/i });
    // eslint-disable-next-line no-useless-escape
    this.joinRewardsLink = this.page.locator('#sitewide-footer').getByRole('link', { name: /Join(?=.*Rewards)/i });
    this.applyForCreditCardLink = this.page.locator('#sitewide-footer').getByRole('link', {
      name: /APPLY( NOW)? FOR( a){0,1} CREDIT CARD/i,
    });
    this.emailSignUpLink = footer.getByRole('link', { name: /EMAIL SIGN/i });
    this.bagIcon = page.locator(`button#shopping-bag`).first();
    this.privacyPolicyLink = page.locator('#sitewide-footer').getByRole('link', { name: 'Privacy Policy' }).last();
    this.privacyChoicesLink = page.getByRole('button', { name: 'Your Privacy Choices' });
    this.brandHeader = page.getByTestId('brand-header').first();
    this.privacyPreferenceToggle = page.locator(`.ot-switch`).first();
    this.privacyPreferenceSubmit = page.locator(`button.save-preference-btn-handler`).first();
    // this.privacyPreferenceToggle = page.locator('label').filter({ hasText: 'Step 1: To opt-out of Online' }).locator('span').first()
    this.privacyPreferenceStatus = page.locator(`#ot-pc-content`).first();
    this.calPrivacyLink = page.getByRole('link', { name: 'Your California Privacy Rights' });
    this.careers = page.locator('.footer_copyright-row').nth(1).locator('a').nth(3);
    this.termsOfUseLink = page.getByTestId('footer-legal-wrapper').getByRole('link', { name: 'Terms of Use' });
    this.sustainabilityLegalLink = page.getByTestId('footer-legal-wrapper').getByRole('link', { name: /Sustainability|Social Responsibility/ });
    this.aboutGapLink = page.getByRole('link', { name: 'About Gap Inc.' });
    this.americansDisabilitiesActLink = page.getByRole('link', {
      name: 'Americans with Disabilities Act',
    });
    this.ontariansDisabilitiesActLink = page.locator('#sitewide-footer').getByRole('link', {
      name: 'Accessibility for Ontarians with Disabilities Act',
    });
    this.caSupplyChainsLink = page.getByRole('link', {
      name: 'California Transparency in Supply Chains Act',
    });
    this.brFactoryStoreLink = footer.getByRole('link', { name: /Banana Republic Factory Store/ });
    this.storesAndServices = page.getByRole('link', { name: /Stores & Services/ });
    this.gapIncPoliciesLink = page.getByRole('link', { name: 'Gap Inc. Policies' });
    this.socialResponsibility = page.locator('.footer_copyright-row').nth(1).locator('a').nth(4);
    this.aboutGapLink = page.getByRole('link', { name: 'About Gap Inc.' });
    this.edfsSignInJoinLink = page.getByRole('link', { name: /Sign In or Join/i });
    this.edfsDetailsLink = page.locator(`div[data-testid="universal-nav"] button:text("DETAILS")`).first();
    this.joinRewardsDropDownLink = page.locator(`a[data-testid='my-account-join-link']`).first();
    this.orderReturnsDropDownLink = page
      .getByTestId('my-account-dropdown')
      .getByRole('link', { name: /Orders (&|and) Returns/ })
      .or(page.getByRole('link', { name: 'Orders & returns', exact: true }));
    this.pointsDropDownLink = page
      .getByTestId('my-account-dropdown')
      .getByRole('link', { name: /My( Points and){0,1} Rewards/ })
      .or(page.getByRole('link', { name: 'Points & Rewards' }));
    this.giftCardDropDownLink = page
      .getByTestId('my-account-dropdown')
      .getByRole('link', { name: 'Check Gift Card Balances' })
      .or(page.getByRole('link', { name: 'Gift Cards', exact: true }).or(page.getByRole('link', { name: 'Gift cards', exact: true })));
    this.customerServiceLink = page.getByRole('link', { name: 'Customer Service' }).last();
    this.shippingFooterLink = footer.getByRole('link', { name: /Shipping/i });
    this.returnsFooterLink = page.locator('#sitewide-footer').getByRole('link', { name: /(^(?!Get it.*).*Returns|Orders & Returns)/i });
    this.trackYourOrderLink = footer.getByRole('link', { name: 'Track Your Order' });
    this.giftCardsFooterLink = footer.or(page.locator('.footer-container-wrapper')).getByRole('link', { name: /Gift {0,1}Cards{0,1}/i });
    this.sizeAndFitGuidesLink = page.getByRole('link', { name: /Size & Fit Guides|Size charts/ });
    this.ourValuesLink = page.getByRole('link', { name: 'Our Values' });
    this.peoplePlanetLink = page.getByRole('link', { name: 'People & Planet' });
    this.searchSuggestions = page.getByTestId('top-search-terms-desktop');
    this.downloadOurApp = page.getByRole('link', { name: /DOWNLOAD OUR APP/i });
    this.rewardsUtilityLink = page.getByTestId('topnav-utility-link').filter({ hasText: /Rewards/i });
    this.weAreChangeLink = page.locator('[href*=wearechange]');
    this.nextImage = page.getByRole('img', { name: 'Next', exact: true });
    this.ourAffiliateProgram = footer.getByRole('link', { name: 'Our Affiliate program' });
    this.superCashFooter = footer.getByRole('link', { name: 'Super Cash' });
    this.searchInput = page.getByLabel('search box').first();
    this.searchForm = page.locator(`form[data-testid="search-form"]`).first();
    this.searchSubmitIcon = page.locator(`button[aria-label="search"]`).first();
    this.dismissSearch = page.getByLabel('dismiss search').first();
    this.mobileSearchButton = page.getByTestId(/mobile-search-button|search-icon-mobile/).first();
    this.mobileSearchInput = page.getByPlaceholder('Search').first();
    this.mobileSearchBox = page.getByTestId('ska-mobileExposedSearch').first();
    this.footerLocaleLink = page.locator(`#footer-locale-link`).first();
    this.expandCustomerService = page.getByRole('button', { name: 'Customer Support' });
    this.certonaRecommendations = page.locator(`#mui-certona-recs-container`);
    this.storeLocatorLink = page.locator(`[href*="/stores"]:visible, [href*="/customerService/storeLocator"]:visible`).last();
    this.rewardsFooterLink = page
      .locator(`h2:text-matches("REWARDS", "i")`)
      .first()
      .or(page.getByRole('button', { name: 'Gap Good Rewards' }));
    this.bagCount = page.getByTestId('shopping-anchor').first();

    this.gapFactoryPopup = page.locator('iframe[title="Sign Up via Text for Offers"]').contentFrame().getByText('Would You Like20% Off?Yes');
    this.gapFactoryPopupClose = page.locator('iframe[title="Sign Up via Text for Offers"]').contentFrame().getByTestId('closeIcon');
    this.meganavWrapper = page.getByTestId('topNavWrapper');
  }

  async checkSiteWideElementsAreOnPage() {
    if (breakpoint === 'desktop')
      switch (brand) {
        case 'at':
          await expect(this.divisionLinkAthleta).toBeVisible();
          break;
        default:
          await expect(this.divisionLink).toBeVisible();
          break;
      }
  }

  getSisterLink(sisterBrand: string) {
    const sisterLinkA = this.page.locator(`.sister-brands-bar a[aria-label="${brandNameMap[sisterBrand]}"]`);
    const sisterLinkB = this.page.locator(`a[data-testid="sister-brand-link-${sisterBrand}"]`);
    return sisterLinkA.or(sisterLinkB);
  }

  async clickSisterLink(sisterBrand: string) {
    const sisterLink = this.getSisterLink(sisterBrand);
    await sisterLink.click();
  }

  async clickEdfsSignInJoinLink() {
    await this.page.locator(`div[data-testid="universal-nav"] a:text("SIGN IN OR JOIN")`).click();
  }

  async clickEdfsDetailsLink() {
    await this.edfsDetailsLink.click();
  }

  async expandSignInYourAccount() {
    await this.signInYourAccount.click();
  }

  async clickBagIcon() {
    brand === 'on' && market === 'ca' ? await this.bagIcon.dispatchEvent('click') : await this.bagIcon.click({ timeout: 30000 });
  }

  async clickSearchBox() {
    try {
      await this.page.locator(`input[type="search"]`).click();
    } catch {
      !!process.env.CI &&
        breakpoint === 'desktop' &&
        brand === 'gpf' &&
        (await this.page.addStyleTag({ content: '#main-content > div > div:nth-child(3) > div { height: 139px; }' }));
      await this.page.locator(`input[type="search"]`).click();
    }
  }

  async fillSearch(searchText: string = 'shirts') {
    await this.page.locator(`input[type="search"]`).pressSequentially(searchText);
  }

  async clickSearchIcon() {
    await this.page.locator(`button[aria-label="search"]`).first().click({ timeout: 30000 });
  }

  async getBagCount() {
    let bagCount = await this.page.getByTestId('shopping-anchor').first().innerText();
    if (bagCount === '') {
      bagCount = '0';
    }
    return bagCount;
  }

  async hoverDivision() {
    if (brand === 'at') {
      const topsDivision = this.page.locator(`.topnav-container a[aria-label^="tops"]`).first();
      await topsDivision.waitFor({ timeout: 60000 });
      await topsDivision.first().hover();
    } else {
      await this.divisionLink.hover();
    }
  }

  async selectDivision() {
    switch (brand) {
      case 'at':
        await this.divisionLinkAthleta.waitFor({ timeout: 60000 });
        await this.divisionLinkAthleta.click({ force: true });
        break;
      case 'br':
        await this.divisionLinkBanana.click();
        break;
      case 'brf':
        await this.divisionLink.hover();
        await this.divisionLinkBananaFactoryCa.click();
        break;
      case 'gp':
      case 'gpf':
      case 'on':
        await this.divisionLink.click();
        break;
      default:
        break;
    }
  }

  async selectCategory() {
    const topsDivision = this.page.locator(`button[aria-expanded="true"][data-divisionname]`);
    await topsDivision.waitFor({ timeout: 60000 });
    if (process.env.BRAND === 'at') {
      const category = this.page.locator(`a[aria-label^="categories"]`).first();
      await category.click({ force: true, timeout: 30000 });
    } else {
      const pantsCategory = this.page.locator(`li[class^="catnav--item"] a[aria-label*="pants"]`).first();
      await pantsCategory.click({ timeout: 30000 });
    }
  }

  async signInFromDropDown() {
    await this.signInYourAccount.click();
    await this.signInButton.click();
  }

  async useRewardsFromDropDown() {
    await this.signInYourAccount.click();
    await this.page.locator(`a[data-testid='navigation-use-rewards']`).first().click();
  }

  async joinRewardsFromDropDown() {
    await this.signInYourAccount.click();
    await this.joinRewardsDropDownLink.click({ timeout: 30000 });
  }

  async lookUpOrderFromDropDown() {
    await this.signInYourAccount.click();
    await this.page.locator(`ul[data-testid='my-account-dropdown'] a:text("Orders & Returns")`).first().click({ timeout: 30000 });
  }

  async myRewardsFromDropDown() {
    await this.signInYourAccount.click();
    await this.page.locator(`ul[data-testid='my-account-dropdown'] a:text("My Points")`).first().click();
  }

  async checkGiftcardFromDropDown() {
    await this.signInYourAccount.click();
    await this.page.locator(`ul[data-testid='my-account-dropdown'] a:text("Check Gift Card")`).first().click();
  }

  async clickFindAStoreLink() {
    if (`${brand}` === 'on') {
      await this.page.getByRole('search').getByRole('link', { name: 'Find a store' }).click();
    } else {
      await this.page
        .getByTestId('brand-header')
        .getByRole('link', { name: /Find A Store/i })
        .or(this.page.getByRole('link', { name: 'Stores & Events' }))
        .click();
    }
  }

  async clickGiftCardLink() {
    await this.page.locator(`a[data-testid="topnav-utility-link"]:text("Gift Card")`).click();
  }

  async clickTermsOfUseFooter() {
    await scrollToLocator(this.page, this.page.locator(`a[data-testid='terms-of-use']`).first());
    await this.page.locator(`a[data-testid='terms-of-use']`).first().click();
  }

  async expandGiftCardsMobileFooter() {
    await scrollToLocator(this.page, this.page.locator(`h2:text-matches("GIFT CARDS", "i")`).first());
    await this.page.locator(`h2:text-matches("GIFT CARDS", "i")`).first().click();
  }

  async expandRewardsMobileFooter() {
    if (brand === 'gpf') {
      await scrollToBottomUntilPageDoesNotGrow(this.page);
    } else {
      await scrollToLocator(this.page, this.rewardsFooterLink);
    }
    await scrollElementToCenter(this.rewardsFooterLink);
    await this.rewardsFooterLink.click({ force: brand === 'gpf' });
  }

  async expandBrandsMobileFooter() {
    await scrollToLocator(this.page, this.page.locator(`h2:text-matches("Shop Our Other Brands", "i") `).first());
    await this.page.locator(`h2:text-matches("Shop Our Other Brands", "i") `).first().click({ timeout: 30000 });
  }

  async clickOrdersReturnsFooter() {
    await scrollToBottomUntilPageDoesNotGrow(this.page);
    await this.page.locator(`h2:text-matches("ORDERS & RETURNS", "i"):visible`).first().click();
  }

  async clickFindAStoreFooter() {
    await scrollToBottomUntilPageDoesNotGrow(this.page);
    if (brand?.startsWith('gp') && breakpoint === 'mobile') {
      await scrollElementToCenter(this.expandCustomerService);
      await this.expandCustomerService.click({ force: brand === 'gpf' });
      await scrollElementToCenter(this.storeLocatorLink);
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await this.page.waitForTimeout(1000);
    }
    await this.storeLocatorLink.click({ timeout: 30000 });
  }

  async clickBOPISFooter() {
    if ((await this.buyOnlinePickUpinStore.getAttribute('target')) === 'blank') {
      const [newPage] = await Promise.all([this.page.waitForEvent('popup'), this.buyOnlinePickUpinStore.click({ timeout: 30000 })]);
      return newPage;
    }
    await this.buyOnlinePickUpinStore.click({ timeout: 30000 });
    return this.page;
  }

  async searchFor(searchTerm: string = 'shirts') {
    if (breakpoint === 'mobile') {
      (await this.mobileSearchBox.isHidden()) ? await this.mobileSearchButton.click() : await this.mobileSearchInput.click();
      await this.searchInput.click();
      await this.searchInput.pressSequentially(searchTerm);
      await this.searchSubmitIcon.click();
    } else {
      await this.clickSearchBox();
      await this.fillSearch(searchTerm);
      await this.clickSearchIcon();
    }
  }

  async enterSearchAndDismiss(searchTerm: string) {
    (await this.mobileSearchBox.isHidden()) ? await this.mobileSearchButton.click() : await this.mobileSearchInput.click();
    await this.searchInput.click();
    await this.searchInput.fill(searchTerm);
    await this.dismissSearch.click();
  }

  // Analytics methods
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setAdobeAnalyticsData(tags: any) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const analyticsTempObject: any = {};
    const dict: DictType = {
      pageName: 'Page Name',
      pageType: 'Page Type',
      ch: 'Site Section',
      server: 'Server',
      g: 'Current URL',
      events: 'Events',
      purchaseID: 'Purchase ID',
      products: 'Products',
      xact: 'Transaction ID',
      v0: 'Campaign',
      'h([0-9]{1,2})': 'Hier',
      'l([0-9]{1,2})': 'List eVar',
      'v([0-9]{1,3})': 'eVar',
      'c([0-9]{1,3})': 'prop',
      zip: 'Zip',
      tnt: 'TnT Campaign',
      cc: 'Currency Code',
      vid: 'Manualy set visitor ID',
    };

    const dictionaryIndex: string[] = Object.keys(dict);

    const patterns = dictionaryIndex.reduce((result: RegExp[], current: string) => {
      result.push(new RegExp(current));
      return result;
    }, []);
    Object.keys(tags).forEach(tag => {
      let matched = false;
      patterns.forEach((pattern, index) => {
        if (pattern.test(tag)) {
          const match = tag.match(pattern);
          analyticsTempObject[match![1] ? dict[dictionaryIndex[index] as keyof DictType] + match![1] : dict[dictionaryIndex[index] as keyof DictType]] =
            tags[tag];
          matched = true;
        }
      });
      if (!matched) {
        analyticsTempObject[tag] = tags[tag];
      }
    });
    this.adobeAnalyticsData.push(analyticsTempObject);
  }

  setTealiumAnalyticsData(postData: string) {
    const matches = postData.match(/({[\s\S]*})/gm);
    this.tealiumAnalyticsData.push(JSON.parse(matches![0]));
  }
}
