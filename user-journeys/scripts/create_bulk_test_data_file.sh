#!/bin/bash

should_add_item() {
  echo "Checking for dropship via URL https://catalog-apis-omni-product-service.aks.prod.azeus.gaptech.com/v3/products?product_id=${1}&market=${3}&locale=${5}&brand=${2}"
  curl -s -k  "https://catalog-apis-omni-product-service.aks.prod.azeus.gaptech.com/v3/products?product_id=${1}&market=${3}&locale=${5}&brand=${2}" \
    --header 'x-client-application-name: CXA' | grep '"Dropship"'
  return $?
}

if [ -z "$1" ]; then
  cd site-reliability-data
  FILES=$(ls *add*.txt)
else
  FILES=$1
fi

if [ -z "$2" ]; then
  RUN_COUNT=0
else
  RUN_COUNT=$2
fi

if [ $RUN_COUNT -gt 5 ]; then
  echo "Reached maximum number of runs"
  exit 1
fi

echo "brand,market,channel,locale,productId" > style.txt

for i in $FILES; do
  export BRAND=$( echo $i | awk -F'_' '{print substr($1, 1, length($1)-3)}' | tr '[:lower:]' '[:upper:]' )
  export BRAND=$( [[ $BRAND = "GAPFS" ]] && echo "GO" || echo $BRAND )
  export MARKET=$(echo $i | awk -F'_' '{print substr($2, 1, length($2)-4)}')
  export MARKET=$( [[ $MARKET = "CA" ]] && echo "CAN" || echo $MARKET )
  export CHANNEL="ONL"
  export LOCALE=$( [[ $MARKET = "US" ]] && echo "en_US" || echo "en_CA" )

  echo "Processing $i"
  rm ${i}.tmp 2> /dev/null || true
  rm ${i}.tmp.uniq 2> /dev/null || true
  rm ${i}.tmp.uniq.shuf 2> /dev/null || true
  cp $i ${i}.tmp
  sed -i 's/[0-9]\{7\}$//' ${i}.tmp
  uniq ${i}.tmp ${i}.tmp.uniq
  rm ${i}.tmp
  shuf ${i}.tmp.uniq -o ${i}.tmp.uniq.shuf
  rm ${i}.tmp.uniq

  found_styles=()
  line_to_check=1
  while [ ${#found_styles[@]} -lt 5 ]; do
    item=$(sed -n "${line_to_check}p" "${i}.tmp.uniq.shuf")
    echo "Checking $item"
    if ! should_add_item $item $BRAND $MARKET $CHANNEL $LOCALE; then
      echo "$BRAND,$MARKET,$CHANNEL,$LOCALE,$item"  >> style.txt
      found_styles+=("$item")
    fi
    line_to_check=$((line_to_check+1))
    sleep 1
  done

  rm ${i}.tmp.uniq.shuf
done

zip style.zip style.txt
echo "style.txt content"
cat style.txt
curl -vvv -k -X POST 'https://catalog-apis-test-data-generator.aks.stage.azeus.gaptech.com/product-data-generator/style/upload?inventoryAware=true&priceAware=true' \
  -H 'X-Client-Application-Name: CXA' \
  --form file=@style.zip
sleep 30
NODE_TLS_REJECT_UNAUTHORIZED='0' node ../create_list_of_active_skus.js
rm style.txt
rm style.zip
for i in $FILES; do
  export BRAND=$( echo $i | awk -F'_' '{print substr($1, 1, length($1)-3)}' | tr '[:lower:]' '[:upper:]' )
  export BRAND=$( [[ $BRAND = "GAPFS" ]] && echo "GO" || echo $BRAND )
  export MARKET=$(echo $i | awk -F'_' '{print substr($2, 1, length($2)-4)}')
  export MARKET=$( [[ $MARKET = "CA" ]] && echo "CAN" || echo $MARKET )
  export CHANNEL="ONL"
  export LOCALE=$( [[ $MARKET = "US" ]] && echo "en_US" || echo "en_CA" )
  comm -12 <(sort $i) <(sort ${BRAND}_${MARKET}_skus.txt) > ../${BRAND}_${MARKET}_skus.txt
  if [ $(wc -l < ../${BRAND}_${MARKET}_skus.txt) -lt 3 ]; then
    ../create_bulk_test_data_file.sh $i $RUN_COUNT
  fi
done