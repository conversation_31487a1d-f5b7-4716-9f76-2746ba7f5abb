/* eslint-disable no-console */
import { readFileSync, writeFileSync } from 'fs';

const styleFileContents = readFileSync('style.txt', 'utf8')
  .split('\n')
  .slice(0, -1)
  .map(line => line.split(','));

const brandsSeen = [];

for (const [brand, market, _, locale, style] of styleFileContents) {
  const shouldOverwrite = !brandsSeen.includes(brand);
  shouldOverwrite && brandsSeen.push(brand);

  const url = `https://catalog-apis-omni-product-service.aks.stage.azeus.gaptech.com/v3/products?product_id=${style}&market=${market}&locale=${locale}&brand=${brand}`;
  const options = { method: 'GET', headers: { 'x-client-application-name': 'CXA' } };

  fetch(url, options)
    .then(response => response.json())
    .then(data => {
      const allSkusInStock = [];
      allNodes(data, 'skus').forEach(skus => {
        console.log('skus', JSON.stringify(skus, null, 2));
        skus.forEach(sku => {
          if (sku.inventory_status.status === 'IN_STOCK' && sku.alternate_ids.online_legacy_sku_number.startsWith(style)) {
            allSkusInStock.push(sku.alternate_ids.online_legacy_sku_number);
          }
        });
      });

      console.log(`${brand}_${market}_skus.txt`, allSkusInStock);
      writeFileSync(`${brand}_${market}_skus.txt`, `\n${allSkusInStock.join('\n')}`, { flag: shouldOverwrite ? 'w' : 'a' });
    })
    .catch(error => {
      console.error(`Failed to grab sync data for ${brand} ${market} due to: ${error}`);
    });
}

function allNodes(obj, key, array = []) {
  if (typeof obj === 'object' && obj !== null) {
    for (const k in obj) {
      if ((typeof key === 'string' && k === key) || (key instanceof RegExp && key.test(k))) {
        array.push(obj[k]);
      }
      allNodes(obj[k], key, array);
    }
  }
  return array;
}
