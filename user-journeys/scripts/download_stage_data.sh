#!/bin/bash

source .env.local 2> /dev/null || true
rm -rf site-reliability-data 2> /dev/null || true
mkdir -p site-reliability-data
cd site-reliability-data

# Define the base URL and files to download
export BASE_URL="https://github.gapinc.com/api/v3/repos/site-reliability/Load-Test-Scripts/contents/DataFiles"
export FILES=(
  "atadd_US.txt"
  "atadd_CA.txt"
  "bradd_US.txt"
  "bradd_CA.txt"
  "brfsadd_US.txt"
  "brfsadd_CA.txt"
  "gapadd_US.txt"
  "gapadd_CA.txt"
  "gapfsadd_US.txt"
  "onadd_US.txt"
  "onadd_CA.txt"
)

# Download the files
for FILE in "${FILES[@]}"; do
  curl -s -H "Authorization: token ${GITHUB_TOKEN}" \
    -H "Accept: application/vnd.github.v3.raw" \
    -O "${BASE_URL}/${FILE}" &
done

# Wait for all background jobs to finish
wait

cd ../