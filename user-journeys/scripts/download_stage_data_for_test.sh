#!/bin/bash

if [ -n "$CI" ] && [ -d "test_data/github-test-data" ]; then
  echo "test_data/github-test-data already exists in CI, skipping script."
else
  echo "Downloading stage data for test..."

  source .env.local 2> /dev/null || true
  rm -rf test_data/github-test-data 2> /dev/null || true
  mkdir -p test_data/github-test-data
  cd test_data/github-test-data

  # Define the base URL and files to download
  export BASE_URL="https://github.gapinc.com/api/v3/repos/ecomfrontend/playwright-stage-product-data/contents"
  export FILES=(
    "AT_CAN_skus.txt"
    "AT_US_skus.txt"
    "BRFS_CAN_skus.txt"
    "BRFS_US_skus.txt"
    "BR_CAN_skus.txt"
    "BR_US_skus.txt"
    "GAP_CAN_skus.txt"
    "GAP_US_skus.txt"
    "GO_US_skus.txt"
    "ON_CAN_skus.txt"
    "ON_US_skus.txt"
  )

  # Download the files
  for FILE in "${FILES[@]}"; do
    curl -s -H "Authorization: token ${GITHUB_TOKEN}" \
      -H "Accept: application/vnd.github.v3.raw" \
      -O "${BASE_URL}/${FILE}" &
  done

  # Wait for all background jobs to finish
  wait

  cd ../../
fi