#!/bin/bash

# The entire .npmrc is being generated here to maintain a single source of truth for the artifactory url
# Environment variables are escaped differently because they are evaluated at different stages in our pipeline
echo "registry = ${ARTIFACTORY_BASE_URL}/api/npm/npm-repos/" > ~/.npmrc
curl -su ${ARTIFACTORY_USR}:${ARTIFACTORY_PSW} "${ARTIFACTORY_BASE_URL}/api/npm/auth" -s >> ~/.npmrc
npm config fix
# sed -i 's/_auth/\/\/gapinc.jfrog.io\/gapinc\/api\/npm\/npm-repos\/:_auth/' ~/.npmrc

# cp ~/.npmrc .npmrc