# collect all entries into one variable
scripts=""

scripts+=$'\n'"\"${1}-at-us-desktop-${2}\": \"BRAND=at MARKET=us BREAKPOINT=desktop ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-at-us-mobile-${2}\":   \"BRAND=at MARKET=us BREAKPOINT=mobile  ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-br-us-desktop-${2}\": \"BRAND=br MARKET=us BREAKPOINT=desktop ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-br-us-mobile-${2}\":   \"BRAND=br MARKET=us BREAKPOINT=mobile  ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-brf-us-desktop-${2}\": \"BRAND=brf MARKET=us BREAKPOINT=desktop ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-brf-us-mobile-${2}\":   \"BRAND=brf MARKET=us BREAKPOINT=mobile  ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-gp-us-desktop-${2}\": \"BRAND=gp MARKET=us BREAKPOINT=desktop ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-gp-us-mobile-${2}\":   \"BRAND=gp MARKET=us BREAKPOINT=mobile  ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-gpf-us-desktop-${2}\": \"BRAND=gpf MARKET=us BREAKPOINT=desktop ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-gpf-us-mobile-${2}\":   \"BRAND=gpf MARKET=us BREAKPOINT=mobile  ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-on-us-desktop-${2}\": \"BRAND=on MARKET=us BREAKPOINT=desktop ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-on-us-mobile-${2}\":   \"BRAND=on MARKET=us BREAKPOINT=mobile  ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-at-ca-desktop-${2}\": \"BRAND=at MARKET=ca BREAKPOINT=desktop ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-at-ca-mobile-${2}\":   \"BRAND=at MARKET=ca BREAKPOINT=mobile  ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-br-ca-desktop-${2}\": \"BRAND=br MARKET=ca BREAKPOINT=desktop ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-br-ca-mobile-${2}\":   \"BRAND=br MARKET=ca BREAKPOINT=mobile  ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-brf-ca-desktop-${2}\": \"BRAND=brf MARKET=ca BREAKPOINT=desktop ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-brf-ca-mobile-${2}\":   \"BRAND=brf MARKET=ca BREAKPOINT=mobile  ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-gp-ca-desktop-${2}\": \"BRAND=gp MARKET=ca BREAKPOINT=desktop ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-gp-ca-mobile-${2}\":   \"BRAND=gp MARKET=ca BREAKPOINT=mobile  ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-gpf-ca-desktop-${2}\": \"BRAND=gpf MARKET=ca BREAKPOINT=desktop ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-gpf-ca-mobile-${2}\":   \"BRAND=gpf MARKET=ca BREAKPOINT=mobile  ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-on-ca-desktop-${2}\": \"BRAND=on MARKET=ca BREAKPOINT=desktop ENV=${2} npx playwright test ${1}/\","
scripts+=$'\n'"\"${1}-on-ca-mobile-${2}\":   \"BRAND=on MARKET=ca BREAKPOINT=mobile  ENV=${2} npx playwright test ${1}/\","

# finally output everything at once
echo "$scripts" | pbcopy
echo "All scripts for $1 with env $2 copied to clipboard."