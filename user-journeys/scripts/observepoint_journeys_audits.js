/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-floating-promises */
const API_KEY = process.env.OBSERVEPOINT_KEY;

const folderToRun = 82628;

const concurrency = 10;

(async () => {
  let [audits, webJourneys] = await Promise.all([getAudits(), getWebJourneys()]);
  audits = audits
    ? audits.reduce((accumulator, audit) => {
        audit.folderId === folderToRun && accumulator.push(audit.auditId);
        return accumulator;
      }, [])
    : [];
  webJourneys = webJourneys
    ? webJourneys.reduce((accumulator, journey) => {
        journey.folderId === folderToRun && accumulator.push(journey.id);
        return accumulator;
      }, [])
    : [];
  console.log('Running Audits:', audits.length, 'Journeys:', webJourneys.length);
  let auditResults = [];
  for (let i = 0; i < audits.length; i += concurrency) {
    if (i + concurrency < audits.length) {
      auditResults = auditResults.concat(await Promise.all(audits.slice(i, i + concurrency).map(audit => runAudit(audit))));
    } else {
      auditResults = auditResults.concat(await Promise.all(audits.slice(i).map(audit => runAudit(audit))));
    }
  }
  let webJourneyResults = [];
  for (let i = 0; i < webJourneys.length; i += concurrency) {
    if (i + concurrency < webJourneys.length) {
      webJourneyResults = webJourneyResults.concat(await Promise.all(webJourneys.slice(i, i + concurrency).map(journey => runWebJourney(journey))));
    } else {
      webJourneyResults = webJourneyResults.concat(await Promise.all(webJourneys.slice(i).map(journey => runWebJourney(journey))));
    }
  }

  console.log('Audit Results:', JSON.stringify(auditResults, null, 2));
  console.log('Web Journey Results:', JSON.stringify(webJourneyResults, null, 2));

  const totalAudits = auditResults.length;
  const totalWebJourneys = webJourneyResults.length;

  const auditSuccesses = auditResults.filter(result => result.response >= 200 && result.response < 300).length;
  const webJourneySuccesses = webJourneyResults.filter(result => result.response >= 200 && result.response < 300).length;

  const auditAlreadyRunning = auditResults.filter(result => result.reponse === 423).length;
  const webJourneyAlreadyRunning = webJourneyResults.filter(result => result.reponse === 423).length;

  console.log(`${auditAlreadyRunning} out of ${totalAudits} audits were already running`);
  console.log(`${webJourneyAlreadyRunning} out of ${totalWebJourneys} journeys were already running`);

  console.log(`${totalAudits - (auditSuccesses + auditAlreadyRunning)} out of ${totalAudits} audits failed to start`);
  console.log(`${totalWebJourneys - (webJourneySuccesses + webJourneyAlreadyRunning)} out of ${totalWebJourneys} journeys failed to start\n\n`);

  console.log(`Successfully triggered ${auditSuccesses} out of ${totalAudits} audits`);
  console.log(`Successfully triggered ${webJourneySuccesses} out of ${totalWebJourneys} journeys`);
})();

// eslint-disable-next-line consistent-return
async function getAudits() {
  const url = 'https://api.observepoint.com/v3/web-audits';
  const options = {
    method: 'GET',
    headers: {
      Authorization: API_KEY,
      accept: 'application/json',
    },
  };

  try {
    const response = await fetch(url, options);
    return response.json();
  } catch (error) {
    console.error('Error:', error);
  }
}

// eslint-disable-next-line consistent-return
async function getWebJourneys() {
  const url = 'https://api.observepoint.com/v3/web-journeys';
  const options = {
    method: 'GET',
    headers: {
      Authorization: API_KEY,
      accept: 'application/json',
    },
  };

  try {
    const response = await fetch(url, options);
    return response.json();
  } catch (error) {
    console.error(error);
  }
}

async function runAudit(auditId) {
  const url = `https://api.observepoint.com/v2/web-audits/${auditId}/runs`;
  const options = {
    method: 'POST',
    headers: {
      Authorization: API_KEY,
    },
  };

  try {
    const response = await fetch(url, options);
    return { audit: auditId, response: response.status };
  } catch (error) {
    return { audit: auditId, response: error.message };
  }
}

async function runWebJourney(journeyId) {
  const url = `https://api.observepoint.com/v2/web-journeys/${journeyId}/runs`;
  const options = {
    method: 'POST',
    headers: {
      Authorization: API_KEY,
    },
  };

  try {
    const response = await fetch(url, options);
    return { journey: journeyId, response: response.status };
  } catch (error) {
    return { journey: journeyId, response: error.message };
  }
}
