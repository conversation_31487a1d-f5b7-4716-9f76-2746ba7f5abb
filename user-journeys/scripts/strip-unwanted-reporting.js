const fs = require('fs');
const { resolve } = require('path');

const result = require(resolve(process.argv[2]));

function removeTrace(object) {
  if (typeof object !== 'object' || object === null) return;

  if (Array.isArray(object)) {
    for (let i = object.length - 1; i >= 0; i--) {
      if (typeof object[i] === 'object' && object[i] !== null) {
        if (object[i].name === 'trace' || (typeof object[i].name === 'string' && object[i].name.includes('route'))) {
          object.splice(i, 1);
        } else {
          removeTrace(object[i]);
        }
      }
    }
  } else {
    for (const key in object) {
      if (typeof object[key] === 'object' && object[key] !== null) {
        if (object[key].name === 'trace' || (typeof object[key].name === 'string' && object[key].name.includes('route'))) {
          delete object[key];
        } else {
          removeTrace(object[key]);
        }
      }
    }
  }
}

removeTrace(result);

fs.writeFileSync(resolve(process.argv[2]), JSON.stringify(result, null, 2));
// eslint-disable-next-line no-console
console.log(`Unwanted reporting data has been stripped from the file ${process.argv[2]}.`);
