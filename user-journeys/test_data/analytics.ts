import { CommonAdobeTags } from '@/global';
import { getBaseURL } from '@/utils/urls';

export const commonTealiumTags = {
  brand_code: 'GAP',
  brand_name: 'Gap',
  brand_number: '1',
  business_unit_abbr_name: 'GAP_US_OL',
  business_unit_description: 'Gap',
  business_unit_id: '1',
  brand_short_name: 'gap',
  country_code: 'US',
  currency_code: 'USD',
  entry_brand: 'GAP',
  is_react: 'true',
  // language_code: 'en_US',
  tealium_account: 'gapinc',
  tealium_profile: 'usgap',
  channel: 'gp:Home',
};

export const commonAdobeTags: CommonAdobeTags = {
  ce: 'UTF-8',
  ns: 'gap',
  'Page Name': 'gp:browse:Home',
  'Currency Code': 'USD',
  'Site Section': 'gp:Home',
  Hier1: 'gp:browse:Home',
  eVar4: 'GAP',
  prop11: `${new URL(getBaseURL()).host}/`,
  prop32: 'GAP',
  prop33: 'Home',
  eVar35: 'GAP',
  eVar44: 'Home',
  eVar49: 'en_US',
  eVar76: 'gp:browse:Home',
};
