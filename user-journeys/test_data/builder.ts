/* eslint-disable class-methods-use-this */
import { now, random } from 'lodash';
import * as product from './products';
import * as card from './creditCard';
import * as userProfile from './userProfile';
import * as styleProperty from './productStyles';
import type { Brand, StyleAttributes } from './productStyles';
import { user, Address } from '@/global';

const { BRAND, MARKET, BREAKPOINT } = process.env;

// TODO: maybe make this a module and have tests just import what they need
export default class Builder {
  email!: string;
  pw: string;
  productId!: string;
  profile!: user;
  address!: Address;
  // creditCard!: creditCard;
  cc!: string;
  expiry!: string;
  cvv!: string;
  static withRandomProduct: string;
  static getRandomProduct: string;

  constructor() {
    this.pw = process.env.TEST_ACCOUNT_PW!;
  }

  static guestUser() {
    throw new Error('Method not implemented.');
  }

  returningPreviewCustomer() {
    this.email = '<EMAIL>';
    return this;
  }

  returningCustomer() {
    switch (BRAND) {
      case 'at':
        this.email = '<EMAIL>';
        break;
      case 'br':
        this.email = '<EMAIL>';
        break;
      case 'brf':
        this.email = '<EMAIL>';
        break;
      case 'gp':
        this.email = '<EMAIL>';
        break;
      case 'gpf':
        this.email = '<EMAIL>';
        break;
      case 'on':
        this.email = '<EMAIL>';
        break;
      default:
        this.email = '<EMAIL>';
        break;
    }
    return this;
  }

  returningCheckoutCustomer() {
    this.email = `hui-checkout-${BRAND}-${MARKET}-${BREAKPOINT}@gap.com`;
    return this;
  }

  guestCustomerCreditCard() {
    switch (`${BRAND}-${MARKET}-${BREAKPOINT}`) {
      case 'at-us-desktop':
        this.cc = `****************`;
        break;
      case 'at-us-mobile':
        this.cc = `****************`;
        break;
      case 'at-ca-desktop':
        this.cc = `****************`;
        break;
      case 'at-ca-mobile':
        this.cc = `****************`;
        break;
      case 'br-us-desktop':
        this.cc = `****************`;
        break;
      case 'br-us-mobile':
        this.cc = `****************`;
        break;
      case 'br-ca-desktop':
        this.cc = `****************`;
        break;
      case 'br-ca-mobile':
        this.cc = `****************`;
        break;
      case 'gp-us-desktop':
        this.cc = `****************`;
        break;
      case 'gp-us-mobile':
        this.cc = `****************`;
        break;
      case 'gp-ca-desktop':
        this.cc = `****************`;
        break;
      case 'gp-ca-mobile':
        this.cc = `5175059508108671`;
        break;
      case 'on-us-desktop':
        this.cc = `5173059427238015`;
        break;
      case 'on-us-mobile':
        this.cc = `5173058171906967`;
        break;
      case 'on-ca-desktop':
        this.cc = `4748781091563605`;
        break;
      case 'on-ca-mobile':
        this.cc = `4726972577179380`;
        break;
      case 'gpf-us-desktop':
        this.cc = `4979265267441507`;
        break;
      case 'gpf-us-mobile':
        this.cc = `4236910095397921`;
        break;
      case 'brf-us-desktop':
        this.cc = `****************`;
        break;
      case 'brf-us-mobile':
        this.cc = `****************`;
        break;
      case 'brf-ca-desktop':
        this.cc = `****************`;
        break;
      case 'brf-ca-mobile':
        this.cc = `****************`;
        break;
      default:
        this.cc = `****************`;
        break;
    }
    this.expiry = '05/26';
    MARKET === 'us' ? (this.cvv = '298') : (this.cvv = '999');
    return this;
  }

  returningCustomerAddCc(projectAccountSuffix: string) {
    const dashProjectAccountSuffix = projectAccountSuffix ? `-${projectAccountSuffix}` : '';

    switch (`${BRAND}${MARKET}`) {
      case 'atus':
        this.email = `cx_auto-profile-ccard-at${dashProjectAccountSuffix}@gap.com`;
        break;
      case 'brus':
        this.email = `cx_auto-profile-ccard-br${dashProjectAccountSuffix}@gap.com`;
        break;
      case 'onus':
        this.email = `cx_auto-profile-ccard-on${dashProjectAccountSuffix}@gap.com`;
        break;
      default:
        this.email = `cx_auto-profile-ccard${dashProjectAccountSuffix}@gap.com`;
        break;
    }
    return this;
  }

  corePreviewCustomer() {
    this.email = '<EMAIL>';
    this.pw = 'Test$1234';
    return this;
  }

  iconPreviewCustomer() {
    this.email = '<EMAIL>';
    this.pw = 'Test$1234';
    return this;
  }

  enthusPreviewCustomer() {
    this.email = '<EMAIL>';
    this.pw = 'Test$1234';
    return this;
  }

  orderHistoryCustomer() {
    this.email = '<EMAIL>';
    return this;
  }

  privacyPolicyCustomer() {
    this.email = '<EMAIL>';
    return this;
  }

  guestUser() {
    this.email = `cx_automation_guest_${now()}${random(9)}@gap.com`;
    return this;
  }

  withAnyProduct() {
    this.productId = product.getRandomProduct();
    return this;
  }

  withRandomProduct(brand: string = BRAND!, market: string = MARKET!) {
    return product.getRandomProduct(brand, market);
  }

  withRandomProductOver50() {
    return product.getRandomProductOver50();
  }

  withBelow50Product() {
    return product.getBelow50Product();
  }

  withBelow35Product() {
    return product.getBelow35Product();
  }
  withOver35Product() {
    return product.getOver35Product();
  }

  withBopisProduct() {
    return product.getBopisProduct();
  }

  withBopisProductLocationId() {
    return product.getBopisProductLocationId();
  }

  withRandomProductStyleColor() {
    return product.getRandomProductStyleColor();
  }

  withAnyStyleColor() {
    this.productId = product.getRandomProduct().slice(0, 9);
    return this;
  }

  withDropshipProduct() {
    return product.getDropshipProduct();
  }

  withDropshipMadeToOrderProduct() {
    return product.getDropshipMadeToOrderProduct();
  }

  withCategory() {
    return product.getRandomCategory();
  }

  withBopisCategory() {
    return product.getBopisCategory();
  }

  withFilter() {
    return product.getFilterCategory();
  }

  withVrtCategory() {
    return product.getVrtCategory();
  }

  withCategoryForISM() {
    return product.getCategoryForISM();
  }

  withDropshipStyleColor() {
    this.productId = product.getDropshipStyleColor();
    return this;
  }

  withProfile() {
    this.profile = userProfile.getProfile();
    if (MARKET === 'ca') {
      this.address = userProfile.getOntarioAddress()!;
    } else {
      this.address = userProfile.getSFAddress()!;
    }
    return this;
  }

  withAnyCard() {
    return card.getRandomCard();
    // return this;
  }

  withRandomCategoryId() {
    return product.getRandomCategory();
  }

  withBopisZipCode() {
    return MARKET === 'us' ? '10036' : 'H1B 5K8';
  }

  withQuickAddCategory() {
    return product.getQuickAddProduct();
  }

  withPriceFilterCategory() {
    return product.getPriceFilterCategory();
  }

  withDivision() {
    return product.getDivision();
  }

  withGiftCard() {
    return product.getGiftCardProduct();
  }

  withStandardProduct() {
    return product.getStandardProduct();
  }

  withStyleProperty(styleAttribute: string) {
    return styleProperty.getStyleProperty(BRAND as Brand, styleAttribute as keyof StyleAttributes);
  }
}
