!(function (t) {
  var e;
  'object' == typeof exports && 'undefined' != typeof module
    ? (module.exports = t())
    : 'function' == typeof define && define.amd
      ? define([], t)
      : ((
          (e = 'undefined' != typeof window ? window : 'undefined' != typeof global ? global : 'undefined' != typeof self ? self : this).braintree ||
          (e.braintree = {})
        ).dataCollector = t());
})(function () {
  return (function r(i, o, s) {
    function a(e, t) {
      if (!o[e]) {
        if (!i[e]) {
          var n = 'function' == typeof require && require;
          if (!t && n) return n(e, !0);
          if (c) return c(e, !0);
          throw (((t = new Error("Cannot find module '" + e + "'")).code = 'MODULE_NOT_FOUND'), t);
        }
        (n = o[e] = { exports: {} }),
          i[e][0].call(
            n.exports,
            function (t) {
              return a(i[e][1][t] || t);
            },
            n,
            n.exports,
            r,
            i,
            o,
            s
          );
      }
      return o[e].exports;
    }
    for (var c = 'function' == typeof require && require, t = 0; t < s.length; t++) a(s[t]);
    return a;
  })(
    {
      1: [
        function (t, e, n) {
          'use strict';
          var r =
              (this && this.__importDefault) ||
              function (t) {
                return t && t.__esModule ? t : { default: t };
              },
            r = (Object.defineProperty(n, '__esModule', { value: !0 }), (n.PromiseGlobal = void 0), r(t('promise-polyfill'))),
            t = 'undefined' != typeof Promise ? Promise : r.default;
          n.PromiseGlobal = t;
        },
        { 'promise-polyfill': 8 },
      ],
      2: [
        function (t, e, n) {
          'use strict';
          var s = t('./lib/promise'),
            a = {};
          function r(n) {
            var t,
              r,
              e,
              i,
              o = JSON.stringify(n);
            return (
              (!n.forceScriptReload && (t = a[o])) ||
                ((r = document.createElement('script')),
                (e = n.dataAttributes || {}),
                (i = n.container || document.head),
                (r.src = n.src),
                (r.id = n.id || ''),
                (r.async = !0),
                n.crossorigin && r.setAttribute('crossorigin', '' + n.crossorigin),
                Object.keys(e).forEach(function (t) {
                  r.setAttribute('data-' + t, '' + e[t]);
                }),
                (t = new s.PromiseGlobal(function (t, e) {
                  r.addEventListener('load', function () {
                    t(r);
                  }),
                    r.addEventListener('error', function () {
                      e(new Error(n.src + ' failed to load.'));
                    }),
                    r.addEventListener('abort', function () {
                      e(new Error(n.src + ' has aborted.'));
                    }),
                    i.appendChild(r);
                })),
                (a[o] = t)),
              t
            );
          }
          (r.clearCache = function () {
            a = {};
          }),
            (e.exports = r);
        },
        { './lib/promise': 1 },
      ],
      3: [
        function (t, e, n) {
          e.exports = t('./dist/load-script');
        },
        { './dist/load-script': 2 },
      ],
      4: [
        function (t, e, n) {
          'use strict';
          Object.defineProperty(n, '__esModule', { value: !0 }),
            (n.deferred = function (n) {
              return function () {
                for (var t = [], e = 0; e < arguments.length; e++) t[e] = arguments[e];
                setTimeout(function () {
                  try {
                    n.apply(void 0, t);
                  } catch (t) {
                    console.log('Error in callback function'), console.log(t);
                  }
                }, 1);
              };
            });
        },
        {},
      ],
      5: [
        function (t, e, n) {
          'use strict';
          Object.defineProperty(n, '__esModule', { value: !0 }),
            (n.once = function (n) {
              var r = !1;
              return function () {
                for (var t = [], e = 0; e < arguments.length; e++) t[e] = arguments[e];
                r || ((r = !0), n.apply(void 0, t));
              };
            });
        },
        {},
      ],
      6: [
        function (t, e, n) {
          'use strict';
          Object.defineProperty(n, '__esModule', { value: !0 }),
            (n.promiseOrCallback = function (t, e) {
              if (!e) return t;
              t.then(function (t) {
                return e(null, t);
              }).catch(function (t) {
                return e(t);
              });
            });
        },
        {},
      ],
      7: [
        function (t, e, n) {
          'use strict';
          var i = t('./lib/deferred'),
            o = t('./lib/once'),
            s = t('./lib/promise-or-callback');
          function a(r) {
            return function () {
              for (var t, e = [], n = 0; n < arguments.length; n++) e[n] = arguments[n];
              return 'function' == typeof e[e.length - 1] && ((t = e.pop()), (t = o.once(i.deferred(t)))), s.promiseOrCallback(r.apply(this, e), t);
            };
          }
          (a.wrapPrototype = function (r, t) {
            var i = (t = void 0 === t ? {} : t).ignoreMethods || [],
              o = !0 === t.transformPrivateMethods;
            return (
              Object.getOwnPropertyNames(r.prototype)
                .filter(function (t) {
                  var e = 'constructor' !== t && 'function' == typeof r.prototype[t],
                    n = -1 === i.indexOf(t),
                    t = o || '_' !== t.charAt(0);
                  return e && t && n;
                })
                .forEach(function (t) {
                  var e = r.prototype[t];
                  r.prototype[t] = a(e);
                }),
              r
            );
          }),
            (e.exports = a);
        },
        { './lib/deferred': 4, './lib/once': 5, './lib/promise-or-callback': 6 },
      ],
      8: [
        function (t, e, n) {
          'use strict';
          var r = setTimeout;
          function c(t) {
            return Boolean(t && void 0 !== t.length);
          }
          function i() {}
          function o(t) {
            if (!(this instanceof o)) throw new TypeError('Promises must be constructed via new');
            if ('function' != typeof t) throw new TypeError('not a function');
            (this._state = 0), (this._handled = !1), (this._value = void 0), (this._deferreds = []), f(t, this);
          }
          function s(n, r) {
            for (; 3 === n._state; ) n = n._value;
            0 === n._state
              ? n._deferreds.push(r)
              : ((n._handled = !0),
                o._immediateFn(function () {
                  var t,
                    e = 1 === n._state ? r.onFulfilled : r.onRejected;
                  if (null === e) (1 === n._state ? a : u)(r.promise, n._value);
                  else {
                    try {
                      t = e(n._value);
                    } catch (t) {
                      return void u(r.promise, t);
                    }
                    a(r.promise, t);
                  }
                }));
          }
          function a(e, t) {
            try {
              if (t === e) throw new TypeError('A promise cannot be resolved with itself.');
              if (t && ('object' == typeof t || 'function' == typeof t)) {
                var n = t.then;
                if (t instanceof o) return (e._state = 3), (e._value = t), void h(e);
                if ('function' == typeof n)
                  return void f(
                    ((r = n),
                    (i = t),
                    function () {
                      r.apply(i, arguments);
                    }),
                    e
                  );
              }
              (e._state = 1), (e._value = t), h(e);
            } catch (t) {
              u(e, t);
            }
            var r, i;
          }
          function u(t, e) {
            (t._state = 2), (t._value = e), h(t);
          }
          function h(t) {
            2 === t._state &&
              0 === t._deferreds.length &&
              o._immediateFn(function () {
                t._handled || o._unhandledRejectionFn(t._value);
              });
            for (var e = 0, n = t._deferreds.length; e < n; e++) s(t, t._deferreds[e]);
            t._deferreds = null;
          }
          function l(t, e, n) {
            (this.onFulfilled = 'function' == typeof t ? t : null), (this.onRejected = 'function' == typeof e ? e : null), (this.promise = n);
          }
          function f(t, e) {
            var n = !1;
            try {
              t(
                function (t) {
                  n || ((n = !0), a(e, t));
                },
                function (t) {
                  n || ((n = !0), u(e, t));
                }
              );
            } catch (t) {
              n || ((n = !0), u(e, t));
            }
          }
          (o.prototype.catch = function (t) {
            return this.then(null, t);
          }),
            (o.prototype.then = function (t, e) {
              var n = new this.constructor(i);
              return s(this, new l(t, e, n)), n;
            }),
            (o.prototype.finally = function (e) {
              var n = this.constructor;
              return this.then(
                function (t) {
                  return n.resolve(e()).then(function () {
                    return t;
                  });
                },
                function (t) {
                  return n.resolve(e()).then(function () {
                    return n.reject(t);
                  });
                }
              );
            }),
            (o.all = function (e) {
              return new o(function (i, o) {
                if (!c(e)) return o(new TypeError('Promise.all accepts an array'));
                var s = Array.prototype.slice.call(e);
                if (0 === s.length) return i([]);
                var a = s.length;
                for (var t = 0; t < s.length; t++)
                  !(function e(n, t) {
                    try {
                      if (t && ('object' == typeof t || 'function' == typeof t)) {
                        var r = t.then;
                        if ('function' == typeof r)
                          return void r.call(
                            t,
                            function (t) {
                              e(n, t);
                            },
                            o
                          );
                      }
                      (s[n] = t), 0 == --a && i(s);
                    } catch (t) {
                      o(t);
                    }
                  })(t, s[t]);
              });
            }),
            (o.allSettled = function (n) {
              return new this(function (i, t) {
                if (!n || void 0 === n.length) return t(new TypeError(typeof n + ' ' + n + ' is not iterable(cannot read property Symbol(Symbol.iterator))'));
                var o = Array.prototype.slice.call(n);
                if (0 === o.length) return i([]);
                var s = o.length;
                for (var e = 0; e < o.length; e++)
                  !(function e(n, t) {
                    if (t && ('object' == typeof t || 'function' == typeof t)) {
                      var r = t.then;
                      if ('function' == typeof r)
                        return void r.call(
                          t,
                          function (t) {
                            e(n, t);
                          },
                          function (t) {
                            (o[n] = { status: 'rejected', reason: t }), 0 == --s && i(o);
                          }
                        );
                    }
                    (o[n] = { status: 'fulfilled', value: t }), 0 == --s && i(o);
                  })(e, o[e]);
              });
            }),
            (o.resolve = function (e) {
              return e && 'object' == typeof e && e.constructor === o
                ? e
                : new o(function (t) {
                    t(e);
                  });
            }),
            (o.reject = function (n) {
              return new o(function (t, e) {
                e(n);
              });
            }),
            (o.race = function (i) {
              return new o(function (t, e) {
                if (!c(i)) return e(new TypeError('Promise.race accepts an array'));
                for (var n = 0, r = i.length; n < r; n++) o.resolve(i[n]).then(t, e);
              });
            }),
            (o._immediateFn =
              'function' == typeof setImmediate
                ? function (t) {
                    setImmediate(t);
                  }
                : function (t) {
                    r(t, 0);
                  }),
            (o._unhandledRejectionFn = function (t) {
              'undefined' != typeof console && console && console.warn('Possible Unhandled Promise Rejection:', t);
            }),
            (e.exports = o);
        },
        {},
      ],
      9: [
        function (t, e, n) {
          'use strict';
          t = t('../lib/braintree-error');
          e.exports = {
            DATA_COLLECTOR_KOUNT_NOT_ENABLED: {
              type: t.types.MERCHANT,
              code: 'DATA_COLLECTOR_KOUNT_NOT_ENABLED',
              message: 'Kount is not enabled for this merchant.',
            },
            DATA_COLLECTOR_KOUNT_ERROR: { type: t.types.MERCHANT, code: 'DATA_COLLECTOR_KOUNT_ERROR' },
            DATA_COLLECTOR_REQUIRES_CREATE_OPTIONS: {
              type: t.types.MERCHANT,
              code: 'DATA_COLLECTOR_REQUIRES_CREATE_OPTIONS',
              message: 'Data Collector must be created with Kount and/or PayPal.',
            },
          };
        },
        { '../lib/braintree-error': 16 },
      ],
      10: [
        function (t, e, n) {
          'use strict';
          var i,
            o = t('../lib/constants').FRAUDNET_FNCLS,
            s = t('../lib/constants').FRAUDNET_SOURCE,
            a = t('../lib/constants').FRAUDNET_URL,
            c = t('../lib/assets').loadScript;
          function r() {}
          function u(t) {
            t && t.parentNode && t.parentNode.removeChild(t);
          }
          (r.prototype.initialize = function (t) {
            var e,
              n = t.environment,
              r = this;
            return (
              (this.sessionId =
                t.sessionId ||
                (function () {
                  var t,
                    e = '';
                  for (t = 0; t < 32; t++) e += Math.floor(16 * Math.random()).toString(16);
                  return e;
                })()),
              t.sessionId || (i = this.sessionId),
              (this._beaconId =
                ((t = this.sessionId), (e = new Date().getTime() / 1e3), 'https://b.stats.paypal.com/counter.cgi?i=127.0.0.1&p=' + t + '&t=' + e + '&a=14')),
              (this._parameterBlock = (function (t, e, n) {
                var r = document.body.appendChild(document.createElement('script')),
                  t = { f: t, s: s, b: e };
                'production' !== n && (t.sandbox = !0);
                return (r.type = 'application/json'), r.setAttribute('fncls', o), (r.text = JSON.stringify(t)), r;
              })(this.sessionId, this._beaconId, n)),
              c({ src: a })
                .then(function (t) {
                  return (r._thirdPartyBlock = t), r;
                })
                .catch(function () {
                  return null;
                })
            );
          }),
            (r.prototype.teardown = function () {
              u(document.querySelector('iframe[title="ppfniframe"]')),
                u(document.querySelector('iframe[title="pbf"]')),
                u(this._parameterBlock),
                u(this._thirdPartyBlock);
            }),
            (e.exports = {
              setup: function (t) {
                var e = new r();
                return !(t = t || {}).sessionId && i ? ((e.sessionId = i), Promise.resolve(e)) : e.initialize(t);
              },
              clearSessionIdCache: function () {
                i = null;
              },
            });
        },
        { '../lib/assets': 14, '../lib/constants': 18 },
      ],
      11: [
        function (t, e, n) {
          'use strict';
          var s = t('./kount'),
            a = t('./fraudnet'),
            c = t('../lib/braintree-error'),
            u = t('../lib/basic-component-verification'),
            h = t('../lib/create-deferred-client'),
            l = t('../lib/create-assets-url'),
            f = t('../lib/methods'),
            d = t('../lib/convert-methods-to-error'),
            p = t('@braintree/wrap-promise'),
            m = t('./errors');
          e.exports = {
            create: p(function (r) {
              var i,
                n = 'Data Collector',
                o = { _instances: [] };
              return u.verify({ name: n, client: r.client, authorization: r.authorization }).then(function () {
                var t, e;
                return (
                  (o._instantiatedWithAClient = !r.useDeferredClient),
                  (o._createPromise = h
                    .create({ authorization: r.authorization, client: r.client, debug: r.debug, assetsUrl: l.create(r.authorization), name: n })
                    .then(function (t) {
                      var e,
                        n = t.getConfiguration();
                      if (!0 === r.kount && n.gatewayConfiguration.kount) {
                        try {
                          e = s.setup({ environment: n.gatewayConfiguration.environment, merchantId: n.gatewayConfiguration.kount.kountMerchantId });
                        } catch (t) {
                          return Promise.reject(
                            new c({ type: m.DATA_COLLECTOR_KOUNT_ERROR.type, code: m.DATA_COLLECTOR_KOUNT_ERROR.code, message: t.message })
                          );
                        }
                        (i = e.deviceData), o._instances.push(e);
                      } else i = {};
                      return Promise.resolve(t);
                    })
                    .then(function (t) {
                      return a
                        .setup({
                          sessionId: r.riskCorrelationId || r.clientMetadataId || r.correlationId,
                          environment: t.getConfiguration().gatewayConfiguration.environment,
                        })
                        .then(function (t) {
                          t && ((i.correlation_id = t.sessionId), o._instances.push(t));
                        });
                    })
                    .then(function () {
                      return 0 === o._instances.length
                        ? Promise.reject(new c(m.DATA_COLLECTOR_REQUIRES_CREATE_OPTIONS))
                        : ((o.deviceData = JSON.stringify(i)), (o.rawDeviceData = i), o);
                    })),
                  (o.teardown =
                    ((t = o),
                    p(function () {
                      return t._createPromise.then(function () {
                        t._instances.forEach(function (t) {
                          t && t.teardown();
                        }),
                          d(t, f(t));
                      });
                    }))),
                  (o.getDeviceData =
                    ((e = o),
                    p(function (t) {
                      return (
                        (t = t || {}),
                        e._createPromise.then(function () {
                          return t.raw ? Promise.resolve(e.rawDeviceData) : Promise.resolve(e.deviceData);
                        })
                      );
                    }))),
                  o._instantiatedWithAClient ? o._createPromise : o
                );
              });
            }),
            VERSION: '3.100.0',
          };
        },
        {
          '../lib/basic-component-verification': 15,
          '../lib/braintree-error': 16,
          '../lib/convert-methods-to-error': 19,
          '../lib/create-assets-url': 20,
          '../lib/create-deferred-client': 21,
          '../lib/methods': 24,
          './errors': 9,
          './fraudnet': 10,
          './kount': 12,
          '@braintree/wrap-promise': 7,
        },
      ],
      12: [
        function (t, e, n) {
          'use strict';
          var r = t('./vendor/sjcl'),
            i = t('../lib/camel-case-to-snake-case'),
            t = 'https://assets.qa.braintreepayments.com/data',
            o = { development: t, qa: t, sandbox: 'https://assets.braintreegateway.com/sandbox/data', production: 'https://assets.braintreegateway.com/data' },
            s = {};
          function a(t) {
            var e = a.getCachedDeviceData(t.merchantId);
            e
              ? ((this.deviceData = e), (this._isCached = !0))
              : ((this._currentEnvironment = this._initializeEnvironment(t)),
                r.random.startCollectors(),
                (this._deviceSessionId = this._generateDeviceSessionId()),
                (this.deviceData = this._getDeviceData()),
                a.setCachedDeviceData(t.merchantId, this.deviceData),
                (this._iframe = this._setupIFrame()));
          }
          (a.getCachedDeviceData = function (t) {
            return s[t];
          }),
            (a.setCachedDeviceData = function (t, e) {
              s[t] = e;
            }),
            (a.prototype.teardown = function () {
              this._isCached || (r.random.stopCollectors(), this._removeIframe());
            }),
            (a.prototype._removeIframe = function () {
              this._iframe.parentNode.removeChild(this._iframe);
            }),
            (a.prototype._getDeviceData = function () {
              return i({ deviceSessionId: this._deviceSessionId, fraudMerchantId: this._currentEnvironment.id });
            }),
            (a.prototype._generateDeviceSessionId = function () {
              var t = r.random.randomWords(4, 0);
              return r.codec.hex.fromBits(t);
            }),
            (a.prototype._setupIFrame = function () {
              var t = this,
                e = '?m=' + this._currentEnvironment.id + '&s=' + this._deviceSessionId,
                n = document.createElement('iframe');
              return (
                (n.width = 1),
                (n.id = 'braintreeDataFrame-' + this._deviceSessionId),
                (n.height = 1),
                (n.frameBorder = 0),
                (n.scrolling = 'no'),
                (n.style.position = 'fixed'),
                (n.style.left = '-999999px'),
                (n.style.top = '-999999px'),
                (n.title = 'Braintree-Kount-iframe'),
                n.setAttribute('aria-hidden', 'true'),
                document.body.appendChild(n),
                setTimeout(function () {
                  (n.src = t._currentEnvironment.url + '/logo.htm' + e),
                    (n.innerHTML = '<img src="' + t._currentEnvironment.url + '/logo.gif' + e + '" alt="" />');
                }, 10),
                n
              );
            }),
            (a.prototype._initializeEnvironment = function (t) {
              var e = o[t.environment];
              if (null == e) throw new Error(t.environment + ' is not a valid environment for kount.environment');
              return { url: e, name: t.environment, id: t.merchantId };
            }),
            (e.exports = {
              setup: function (t) {
                return new a(null != t ? t : {});
              },
              Kount: a,
              environmentUrls: o,
            });
        },
        { '../lib/camel-case-to-snake-case': 17, './vendor/sjcl': 13 },
      ],
      13: [
        function (t, e, n) {
          'use strict';
          var r,
            i,
            o,
            s,
            a,
            _ = {
              cipher: {},
              hash: {},
              keyexchange: {},
              mode: {},
              misc: {},
              codec: {},
              exception: {
                corrupt: function (t) {
                  (this.toString = function () {
                    return 'CORRUPT: ' + this.message;
                  }),
                    (this.message = t);
                },
                invalid: function (t) {
                  (this.toString = function () {
                    return 'INVALID: ' + this.message;
                  }),
                    (this.message = t);
                },
                bug: function (t) {
                  (this.toString = function () {
                    return 'BUG: ' + this.message;
                  }),
                    (this.message = t);
                },
                notReady: function (t) {
                  (this.toString = function () {
                    return 'NOT READY: ' + this.message;
                  }),
                    (this.message = t);
                },
              },
            };
          function c(t, e, n) {
            if (4 !== e.length) throw new _.exception.invalid('invalid aes block size');
            var r = t.b[n],
              i = e[0] ^ r[0],
              o = e[n ? 3 : 1] ^ r[1],
              s = e[2] ^ r[2];
            e = e[n ? 1 : 3] ^ r[3];
            for (var a, c, u = r.length / 4 - 2, h = 4, l = [0, 0, 0, 0], f = t.l[n], d = ((t = f[0]), f[1]), p = f[2], m = f[3], y = f[4], v = 0; v < u; v++)
              (f = t[i >>> 24] ^ d[(o >> 16) & 255] ^ p[(s >> 8) & 255] ^ m[255 & e] ^ r[h]),
                (a = t[o >>> 24] ^ d[(s >> 16) & 255] ^ p[(e >> 8) & 255] ^ m[255 & i] ^ r[h + 1]),
                (c = t[s >>> 24] ^ d[(e >> 16) & 255] ^ p[(i >> 8) & 255] ^ m[255 & o] ^ r[h + 2]),
                (e = t[e >>> 24] ^ d[(i >> 16) & 255] ^ p[(o >> 8) & 255] ^ m[255 & s] ^ r[h + 3]),
                (h += 4),
                (i = f),
                (o = a),
                (s = c);
            for (v = 0; v < 4; v++)
              (l[n ? 3 & -v : v] = (y[i >>> 24] << 24) ^ (y[(o >> 16) & 255] << 16) ^ (y[(s >> 8) & 255] << 8) ^ y[255 & e] ^ r[h++]),
                (f = i),
                (i = o),
                (o = s),
                (s = e),
                (e = f);
            return l;
          }
          function u(t, e) {
            for (var n, r, i = t.u, o = t.b, s = i[0], a = i[1], c = i[2], u = i[3], h = i[4], l = i[5], f = i[6], d = i[7], p = 0; p < 64; p++)
              (n =
                (n =
                  p < 16
                    ? e[p]
                    : ((n = e[(p + 1) & 15]),
                      (r = e[(p + 14) & 15]),
                      (e[15 & p] =
                        (((n >>> 7) ^ (n >>> 18) ^ (n >>> 3) ^ (n << 25) ^ (n << 14)) +
                          ((r >>> 17) ^ (r >>> 19) ^ (r >>> 10) ^ (r << 15) ^ (r << 13)) +
                          e[15 & p] +
                          e[(p + 9) & 15]) |
                        0))) +
                d +
                ((h >>> 6) ^ (h >>> 11) ^ (h >>> 25) ^ (h << 26) ^ (h << 21) ^ (h << 7)) +
                (f ^ (h & (l ^ f))) +
                o[p]),
                (d = f),
                (f = l),
                (l = h),
                (h = (u + n) | 0),
                (u = c),
                (c = a),
                (s = (n + (((a = s) & c) ^ (u & (a ^ c))) + ((a >>> 2) ^ (a >>> 13) ^ (a >>> 22) ^ (a << 30) ^ (a << 19) ^ (a << 10))) | 0);
            (i[0] = (i[0] + s) | 0),
              (i[1] = (i[1] + a) | 0),
              (i[2] = (i[2] + c) | 0),
              (i[3] = (i[3] + u) | 0),
              (i[4] = (i[4] + h) | 0),
              (i[5] = (i[5] + l) | 0),
              (i[6] = (i[6] + f) | 0),
              (i[7] = (i[7] + d) | 0);
          }
          function h(t, e) {
            var n,
              r = _.random.B[t],
              i = [];
            for (n in r) r.hasOwnProperty(n) && i.push(r[n]);
            for (n = 0; n < i.length; n++) i[n](e);
          }
          function l(t, e) {
            'undefined' != typeof window && window.performance && 'function' == typeof window.performance.now
              ? t.addEntropy(window.performance.now(), e, 'loadtime')
              : t.addEntropy(new Date().valueOf(), e, 'loadtime');
          }
          function f(t) {
            (t.b = d(t).concat(d(t))), (t.C = new _.cipher.aes(t.b));
          }
          function d(t) {
            for (var e = 0; e < 4 && ((t.g[e] = (t.g[e] + 1) | 0), !t.g[e]); e++);
            return t.C.encrypt(t.g);
          }
          function p(t, e) {
            return function () {
              e.apply(t, arguments);
            };
          }
          (_.cipher.aes = function (t) {
            this.l[0][0][0] || this.G();
            var e,
              n,
              r,
              i = this.l[0][4],
              o = this.l[1],
              s = t.length,
              a = 1;
            if (4 !== s && 6 !== s && 8 !== s) throw new _.exception.invalid('invalid aes key size');
            for (this.b = [(n = t.slice(0)), (r = [])], t = s; t < 4 * s + 28; t++)
              (e = n[t - 1]),
                (0 == t % s || (8 === s && 4 == t % s)) &&
                  ((e = (i[e >>> 24] << 24) ^ (i[(e >> 16) & 255] << 16) ^ (i[(e >> 8) & 255] << 8) ^ i[255 & e]),
                  0 == t % s && ((e = (e << 8) ^ (e >>> 24) ^ (a << 24)), (a = (a << 1) ^ (283 * (a >> 7))))),
                (n[t] = n[t - s] ^ e);
            for (s = 0; t; s++, t--)
              (e = n[3 & s ? t : t - 4]),
                (r[s] = t <= 4 || s < 4 ? e : o[0][i[e >>> 24]] ^ o[1][i[(e >> 16) & 255]] ^ o[2][i[(e >> 8) & 255]] ^ o[3][i[255 & e]]);
          }),
            (_.cipher.aes.prototype = {
              encrypt: function (t) {
                return c(this, t, 0);
              },
              decrypt: function (t) {
                return c(this, t, 1);
              },
              l: [
                [[], [], [], [], []],
                [[], [], [], [], []],
              ],
              G: function () {
                for (var t, e, n, r, i, o, s = this.l[0], a = this.l[1], c = s[4], u = a[4], h = [], l = [], f = 0; f < 256; f++)
                  l[(h[f] = (f << 1) ^ (283 * (f >> 7))) ^ f] = f;
                for (t = e = 0; !c[t]; t ^= n || 1, e = l[e] || 1)
                  for (
                    o =
                      (16843009 *
                        (r = h[(f = h[(n = h[(u[(c[t] = i = ((i = e ^ (e << 1) ^ (e << 2) ^ (e << 3) ^ (e << 4)) >> 8) ^ (255 & i) ^ 99)] = t)])])])) ^
                      (65537 * f) ^
                      (257 * n) ^
                      (16843008 * t),
                      r = (257 * h[i]) ^ (16843008 * i),
                      f = 0;
                    f < 4;
                    f++
                  )
                    (s[f][t] = r = (r << 24) ^ (r >>> 8)), (a[f][i] = o = (o << 24) ^ (o >>> 8));
                for (f = 0; f < 5; f++) (s[f] = s[f].slice(0)), (a[f] = a[f].slice(0));
              },
            }),
            (_.bitArray = {
              bitSlice: function (t, e, n) {
                return (t = _.bitArray.M(t.slice(e / 32), 32 - (31 & e)).slice(1)), void 0 === n ? t : _.bitArray.clamp(t, n - e);
              },
              extract: function (t, e, n) {
                var r = Math.floor((-e - n) & 31);
                return (-32 & ((e + n - 1) ^ e) ? (t[(e / 32) | 0] << (32 - r)) ^ (t[(e / 32 + 1) | 0] >>> r) : t[(e / 32) | 0] >>> r) & ((1 << n) - 1);
              },
              concat: function (t, e) {
                var n, r;
                return 0 === t.length || 0 === e.length || ((n = t[t.length - 1]), 32 === (r = _.bitArray.getPartial(n)))
                  ? t.concat(e)
                  : _.bitArray.M(e, r, 0 | n, t.slice(0, t.length - 1));
              },
              bitLength: function (t) {
                var e = t.length;
                return 0 === e ? 0 : 32 * (e - 1) + _.bitArray.getPartial(t[e - 1]);
              },
              clamp: function (t, e) {
                var n;
                return (
                  32 * t.length < e ||
                    ((n = (t = t.slice(0, Math.ceil(e / 32))).length),
                    (e &= 31),
                    0 < n && e && (t[n - 1] = _.bitArray.partial(e, t[n - 1] & (2147483648 >> (e - 1)), 1))),
                  t
                );
              },
              partial: function (t, e, n) {
                return 32 === t ? e : (n ? 0 | e : e << (32 - t)) + 1099511627776 * t;
              },
              getPartial: function (t) {
                return Math.round(t / 1099511627776) || 32;
              },
              equal: function (t, e) {
                if (_.bitArray.bitLength(t) !== _.bitArray.bitLength(e)) return !1;
                for (var n = 0, r = 0; r < t.length; r++) n |= t[r] ^ e[r];
                return 0 === n;
              },
              M: function (t, e, n, r) {
                var i = 0;
                for (void 0 === r && (r = []); 32 <= e; e -= 32) r.push(n), (n = 0);
                if (0 === e) return r.concat(t);
                for (i = 0; i < t.length; i++) r.push(n | (t[i] >>> e)), (n = t[i] << (32 - e));
                return (
                  (i = t.length ? t[t.length - 1] : 0), (t = _.bitArray.getPartial(i)), r.push(_.bitArray.partial((e + t) & 31, 32 < e + t ? n : r.pop(), 1)), r
                );
              },
              Y: function (t, e) {
                return [t[0] ^ e[0], t[1] ^ e[1], t[2] ^ e[2], t[3] ^ e[3]];
              },
              byteswapM: function (t) {
                for (var e, n = 0; n < t.length; ++n) (e = t[n]), (t[n] = (e >>> 24) | ((e >>> 8) & 65280) | ((65280 & e) << 8) | (e << 24));
                return t;
              },
            }),
            (_.codec.utf8String = {
              fromBits: function (t) {
                for (var e, n = '', r = _.bitArray.bitLength(t), i = 0; i < r / 8; i++)
                  0 == (3 & i) && (e = t[i / 4]), (n += String.fromCharCode(((e >>> 8) >>> 8) >>> 8)), (e <<= 8);
                return decodeURIComponent(escape(n));
              },
              toBits: function (t) {
                t = unescape(encodeURIComponent(t));
                for (var e = [], n = 0, r = 0; r < t.length; r++) (n = (n << 8) | t.charCodeAt(r)), 3 == (3 & r) && (e.push(n), (n = 0));
                return 3 & r && e.push(_.bitArray.partial(8 * (3 & r), n)), e;
              },
            }),
            (_.codec.hex = {
              fromBits: function (t) {
                for (var e = '', n = 0; n < t.length; n++) e += (0xf00000000000 + (0 | t[n])).toString(16).substr(4);
                return e.substr(0, _.bitArray.bitLength(t) / 4);
              },
              toBits: function (t) {
                var e,
                  n = [],
                  r = (t = t.replace(/\s|0x/g, '')).length;
                for (t += '00000000', e = 0; e < t.length; e += 8) n.push(0 ^ parseInt(t.substr(e, 8), 16));
                return _.bitArray.clamp(n, 4 * r);
              },
            }),
            (_.hash.sha256 = function (t) {
              this.b[0] || this.G(), t ? ((this.u = t.u.slice(0)), (this.o = t.o.slice(0)), (this.h = t.h)) : this.reset();
            }),
            (_.hash.sha256.hash = function (t) {
              return new _.hash.sha256().update(t).finalize();
            }),
            (_.hash.sha256.prototype = {
              blockSize: 512,
              reset: function () {
                return (this.u = this.K.slice(0)), (this.o = []), (this.h = 0), this;
              },
              update: function (t) {
                'string' == typeof t && (t = _.codec.utf8String.toBits(t));
                var e = (this.o = _.bitArray.concat(this.o, t));
                if (((i = this.h), 9007199254740991 < (t = this.h = i + _.bitArray.bitLength(t))))
                  throw new _.exception.invalid('Cannot hash more than 2^53 - 1 bits');
                if ('undefined' != typeof Uint32Array) {
                  for (var n = new Uint32Array(e), r = 0, i = 512 + i - ((512 + i) & 511); i <= t; i += 512)
                    u(this, n.subarray(16 * r, 16 * (r + 1))), (r += 1);
                  e.splice(0, 16 * r);
                } else for (i = 512 + i - ((512 + i) & 511); i <= t; i += 512) u(this, e.splice(0, 16));
                return this;
              },
              finalize: function () {
                for (var t = this.o, e = this.u, n = (t = _.bitArray.concat(t, [_.bitArray.partial(1, 1)])).length + 2; 15 & n; n++) t.push(0);
                for (t.push(Math.floor(this.h / 4294967296)), t.push(0 | this.h); t.length; ) u(this, t.splice(0, 16));
                return this.reset(), e;
              },
              K: [],
              b: [],
              G: function () {
                function t(t) {
                  return (4294967296 * (t - Math.floor(t))) | 0;
                }
                for (var e, n, r = 0, i = 2; r < 64; i++) {
                  for (n = !0, e = 2; e * e <= i; e++)
                    if (0 == i % e) {
                      n = !1;
                      break;
                    }
                  n && (r < 8 && (this.K[r] = t(Math.pow(i, 0.5))), (this.b[r] = t(Math.pow(i, 1 / 3))), r++);
                }
              },
            }),
            (_.prng = function (t) {
              (this.c = [new _.hash.sha256()]),
                (this.i = [0]),
                (this.H = 0),
                (this.v = {}),
                (this.F = 0),
                (this.J = {}),
                (this.L = this.f = this.j = this.T = 0),
                (this.b = [0, 0, 0, 0, 0, 0, 0, 0]),
                (this.g = [0, 0, 0, 0]),
                (this.C = void 0),
                (this.D = t),
                (this.s = !1),
                (this.B = { progress: {}, seeded: {} }),
                (this.m = this.S = 0),
                (this.w = 1),
                (this.A = 2),
                (this.O = 65536),
                (this.I = [0, 48, 64, 96, 128, 192, 256, 384, 512, 768, 1024]),
                (this.P = 3e4),
                (this.N = 80);
            }),
            (_.prng.prototype = {
              randomWords: function (t, e) {
                var n = [];
                if ((i = this.isReady(e)) === this.m) throw new _.exception.notReady("generator isn't seeded");
                if (i & this.A) {
                  var r,
                    i = !(i & this.w),
                    o = [],
                    s = 0;
                  for (this.L = o[0] = new Date().valueOf() + this.P, r = 0; r < 16; r++) o.push((4294967296 * Math.random()) | 0);
                  for (r = 0; r < this.c.length && ((o = o.concat(this.c[r].finalize())), (s += this.i[r]), (this.i[r] = 0), i || !(this.H & (1 << r))); r++);
                  for (
                    this.H >= 1 << this.c.length && (this.c.push(new _.hash.sha256()), this.i.push(0)),
                      this.f -= s,
                      s > this.j && (this.j = s),
                      this.H++,
                      this.b = _.hash.sha256.hash(this.b.concat(o)),
                      this.C = new _.cipher.aes(this.b),
                      i = 0;
                    i < 4 && ((this.g[i] = (this.g[i] + 1) | 0), !this.g[i]);
                    i++
                  );
                }
                for (i = 0; i < t; i += 4) 0 == (i + 1) % this.O && f(this), (o = d(this)), n.push(o[0], o[1], o[2], o[3]);
                return f(this), n.slice(0, t);
              },
              setDefaultParanoia: function (t, e) {
                if (0 === t && 'Setting paranoia=0 will ruin your security; use it only for testing' !== e)
                  throw new _.exception.invalid('Setting paranoia=0 will ruin your security; use it only for testing');
                this.D = t;
              },
              addEntropy: function (t, e, n) {
                n = n || 'user';
                var r,
                  i = new Date().valueOf(),
                  o = this.v[n],
                  s = this.isReady(),
                  a = 0,
                  c = this.J[n];
                switch (
                  (void 0 === c && (c = this.J[n] = this.T++), void 0 === o && (o = this.v[n] = 0), (this.v[n] = (this.v[n] + 1) % this.c.length), typeof t)
                ) {
                  case 'number':
                    void 0 === e && (e = 1), this.c[o].update([c, this.F++, 1, e, i, 1, 0 | t]);
                    break;
                  case 'object':
                    if ('[object Uint32Array]' === (n = Object.prototype.toString.call(t))) {
                      for (r = [], n = 0; n < t.length; n++) r.push(t[n]);
                      t = r;
                    } else for ('[object Array]' !== n && (a = 1), n = 0; n < t.length && !a; n++) 'number' != typeof t[n] && (a = 1);
                    if (!a) {
                      if (void 0 === e) for (n = e = 0; n < t.length; n++) for (r = t[n]; 0 < r; ) e++, (r >>>= 1);
                      this.c[o].update([c, this.F++, 2, e, i, t.length].concat(t));
                    }
                    break;
                  case 'string':
                    void 0 === e && (e = t.length), this.c[o].update([c, this.F++, 3, e, i, t.length]), this.c[o].update(t);
                    break;
                  default:
                    a = 1;
                }
                if (a) throw new _.exception.bug('random: addEntropy only supports number, array of numbers or string');
                (this.i[o] += e),
                  (this.f += e),
                  s === this.m && (this.isReady() !== this.m && h('seeded', Math.max(this.j, this.f)), h('progress', this.getProgress()));
              },
              isReady: function (t) {
                return (
                  (t = this.I[void 0 !== t ? t : this.D]),
                  this.j && this.j >= t
                    ? this.i[0] > this.N && new Date().valueOf() > this.L
                      ? this.A | this.w
                      : this.w
                    : this.f >= t
                      ? this.A | this.m
                      : this.m
                );
              },
              getProgress: function (t) {
                return (t = this.I[t || this.D]), this.j >= t || this.f > t ? 1 : this.f / t;
              },
              startCollectors: function () {
                if (!this.s) {
                  if (
                    ((this.a = {
                      loadTimeCollector: p(this, this.V),
                      mouseCollector: p(this, this.W),
                      keyboardCollector: p(this, this.U),
                      accelerometerCollector: p(this, this.R),
                      touchCollector: p(this, this.X),
                    }),
                    window.addEventListener)
                  )
                    window.addEventListener('load', this.a.loadTimeCollector, !1),
                      window.addEventListener('mousemove', this.a.mouseCollector, !1),
                      window.addEventListener('keypress', this.a.keyboardCollector, !1),
                      window.addEventListener('devicemotion', this.a.accelerometerCollector, !1),
                      window.addEventListener('touchmove', this.a.touchCollector, !1);
                  else {
                    if (!document.attachEvent) throw new _.exception.bug("can't attach event");
                    document.attachEvent('onload', this.a.loadTimeCollector),
                      document.attachEvent('onmousemove', this.a.mouseCollector),
                      document.attachEvent('keypress', this.a.keyboardCollector);
                  }
                  this.s = !0;
                }
              },
              stopCollectors: function () {
                this.s &&
                  (window.removeEventListener
                    ? (window.removeEventListener('load', this.a.loadTimeCollector, !1),
                      window.removeEventListener('mousemove', this.a.mouseCollector, !1),
                      window.removeEventListener('keypress', this.a.keyboardCollector, !1),
                      window.removeEventListener('devicemotion', this.a.accelerometerCollector, !1),
                      window.removeEventListener('touchmove', this.a.touchCollector, !1))
                    : document.detachEvent &&
                      (document.detachEvent('onload', this.a.loadTimeCollector),
                      document.detachEvent('onmousemove', this.a.mouseCollector),
                      document.detachEvent('keypress', this.a.keyboardCollector)),
                  (this.s = !1));
              },
              addEventListener: function (t, e) {
                this.B[t][this.S++] = e;
              },
              removeEventListener: function (t, e) {
                var n,
                  r,
                  i = this.B[t],
                  o = [];
                for (r in i) i.hasOwnProperty(r) && i[r] === e && o.push(r);
                for (n = 0; n < o.length; n++) delete i[(r = o[n])];
              },
              U: function () {
                l(this, 1);
              },
              W: function (t) {
                var e, n;
                try {
                  (e = t.x || t.clientX || t.offsetX || 0), (n = t.y || t.clientY || t.offsetY || 0);
                } catch (t) {
                  n = e = 0;
                }
                0 != e && 0 != n && this.addEntropy([e, n], 2, 'mouse'), l(this, 0);
              },
              X: function (t) {
                (t = t.touches[0] || t.changedTouches[0]), this.addEntropy([t.pageX || t.clientX, t.pageY || t.clientY], 1, 'touch'), l(this, 0);
              },
              V: function () {
                l(this, 2);
              },
              R: function (t) {
                var e;
                (t = t.accelerationIncludingGravity.x || t.accelerationIncludingGravity.y || t.accelerationIncludingGravity.z),
                  window.orientation && 'number' == typeof (e = window.orientation) && this.addEntropy(e, 1, 'accelerometer'),
                  t && this.addEntropy(t, 2, 'accelerometer'),
                  l(this, 0);
              },
            }),
            (_.random = new _.prng(6));
          t: try {
            if ((s = void 0 !== e && e.exports)) {
              try {
                a = t('crypto');
              } catch (t) {
                a = null;
              }
              s = i = a;
            }
            if (s && i.randomBytes)
              (r = i.randomBytes(128)), (r = new Uint32Array(new Uint8Array(r).buffer)), _.random.addEntropy(r, 1024, "crypto['randomBytes']");
            else if ('undefined' != typeof window && 'undefined' != typeof Uint32Array) {
              if (((o = new Uint32Array(32)), window.crypto && window.crypto.getRandomValues)) window.crypto.getRandomValues(o);
              else {
                if (!window.msCrypto || !window.msCrypto.getRandomValues) break t;
                window.msCrypto.getRandomValues(o);
              }
              _.random.addEntropy(o, 1024, "crypto['getRandomValues']");
            }
          } catch (t) {
            'undefined' != typeof window && window.console && (console.log('There was an error collecting entropy from the browser:'), console.log(t));
          }
          void 0 !== e && e.exports && (e.exports = _);
        },
        { crypto: void 0 },
      ],
      14: [
        function (t, e, n) {
          'use strict';
          t = t('@braintree/asset-loader/load-script');
          e.exports = { loadScript: t };
        },
        { '@braintree/asset-loader/load-script': 3 },
      ],
      15: [
        function (t, e, n) {
          'use strict';
          var r = t('./braintree-error'),
            i = t('./errors'),
            o = '3.100.0';
          e.exports = {
            verify: function (t) {
              var e, n;
              return t
                ? ((n = t.name),
                  (e = t.client),
                  (t = t.authorization),
                  e || t
                    ? t || e.getVersion() === o
                      ? Promise.resolve()
                      : Promise.reject(
                          new r({
                            type: i.INCOMPATIBLE_VERSIONS.type,
                            code: i.INCOMPATIBLE_VERSIONS.code,
                            message: 'Client (version ' + e.getVersion() + ') and ' + n + ' (version ' + o + ') components must be from the same SDK version.',
                          })
                        )
                    : Promise.reject(
                        new r({
                          type: i.INSTANTIATION_OPTION_REQUIRED.type,
                          code: i.INSTANTIATION_OPTION_REQUIRED.code,
                          message: 'options.client is required when instantiating ' + n + '.',
                        })
                      ))
                : Promise.reject(
                    new r({
                      type: i.INVALID_USE_OF_INTERNAL_FUNCTION.type,
                      code: i.INVALID_USE_OF_INTERNAL_FUNCTION.code,
                      message: 'Options must be passed to basicComponentVerification function.',
                    })
                  );
            },
          };
        },
        { './braintree-error': 16, './errors': 23 },
      ],
      16: [
        function (t, e, n) {
          'use strict';
          t = t('./enumerate');
          function r(t) {
            if (!r.types.hasOwnProperty(t.type)) throw new Error(t.type + ' is not a valid type.');
            if (!t.code) throw new Error('Error code required.');
            if (!t.message) throw new Error('Error message required.');
            (this.name = 'BraintreeError'), (this.code = t.code), (this.message = t.message), (this.type = t.type), (this.details = t.details);
          }
          (((r.prototype = Object.create(Error.prototype)).constructor = r).types = t(['CUSTOMER', 'MERCHANT', 'NETWORK', 'INTERNAL', 'UNKNOWN'])),
            (r.findRootError = function (t) {
              return t instanceof r && t.details && t.details.originalError ? r.findRootError(t.details.originalError) : t;
            }),
            (e.exports = r);
        },
        { './enumerate': 22 },
      ],
      17: [
        function (t, e, n) {
          'use strict';
          e.exports = function (n) {
            return Object.keys(n).reduce(function (t, e) {
              return (
                (t[
                  e
                    .replace(/([a-z\d])([A-Z])/g, '$1_$2')
                    .replace(/([A-Z]+)([A-Z][a-z\d]+)/g, '$1_$2')
                    .toLowerCase()
                ] = n[e]),
                t
              );
            }, {});
          };
        },
        {},
      ],
      18: [
        function (t, e, n) {
          'use strict';
          var r = '3.100.0';
          e.exports = {
            ANALYTICS_PREFIX: 'web.',
            ANALYTICS_REQUEST_TIMEOUT_MS: 2e3,
            ASSETS_URLS: { production: 'https://assets.braintreegateway.com', sandbox: 'https://assets.braintreegateway.com' },
            CLIENT_API_URLS: { production: 'https://api.braintreegateway.com:443', sandbox: 'https://api.sandbox.braintreegateway.com:443' },
            FRAUDNET_SOURCE: 'BRAINTREE_SIGNIN',
            FRAUDNET_FNCLS: 'fnparams-dede7cc5-15fd-4c75-a9f4-36c430ee3a99',
            FRAUDNET_URL: 'https://c.paypal.com/da/r/fb.js',
            BUS_CONFIGURATION_REQUEST_EVENT: 'BUS_CONFIGURATION_REQUEST',
            GRAPHQL_URLS: { production: 'https://payments.braintree-api.com/graphql', sandbox: 'https://payments.sandbox.braintree-api.com/graphql' },
            INTEGRATION_TIMEOUT_MS: 6e4,
            VERSION: r,
            INTEGRATION: 'custom',
            SOURCE: 'client',
            PLATFORM: 'web',
            BRAINTREE_LIBRARY_VERSION: 'braintree/web/' + r,
          };
        },
        {},
      ],
      19: [
        function (t, e, n) {
          'use strict';
          var r = t('./braintree-error'),
            i = t('./errors');
          e.exports = function (e, t) {
            t.forEach(function (t) {
              e[t] = function () {
                throw new r({
                  type: i.METHOD_CALLED_AFTER_TEARDOWN.type,
                  code: i.METHOD_CALLED_AFTER_TEARDOWN.code,
                  message: t + ' cannot be called after teardown.',
                });
              };
            });
          };
        },
        { './braintree-error': 16, './errors': 23 },
      ],
      20: [
        function (t, e, n) {
          'use strict';
          var r = t('./constants').ASSETS_URLS;
          e.exports = {
            create: function (t) {
              return r.production;
            },
          };
        },
        { './constants': 18 },
      ],
      21: [
        function (t, e, n) {
          'use strict';
          var r = t('./braintree-error'),
            i = t('./assets'),
            o = t('./errors'),
            s = '3.100.0';
          e.exports = {
            create: function (t) {
              var e = Promise.resolve();
              return t.client
                ? Promise.resolve(t.client)
                : (e =
                    window.braintree && window.braintree.client
                      ? e
                      : i.loadScript({ src: t.assetsUrl + '/web/' + s + '/js/client.min.js' }).catch(function (t) {
                          return Promise.reject(
                            new r({
                              type: o.CLIENT_SCRIPT_FAILED_TO_LOAD.type,
                              code: o.CLIENT_SCRIPT_FAILED_TO_LOAD.code,
                              message: o.CLIENT_SCRIPT_FAILED_TO_LOAD.message,
                              details: { originalError: t },
                            })
                          );
                        })).then(function () {
                    return window.braintree.client.VERSION !== s
                      ? Promise.reject(
                          new r({
                            type: o.INCOMPATIBLE_VERSIONS.type,
                            code: o.INCOMPATIBLE_VERSIONS.code,
                            message:
                              'Client (version ' +
                              window.braintree.client.VERSION +
                              ') and ' +
                              t.name +
                              ' (version ' +
                              s +
                              ') components must be from the same SDK version.',
                          })
                        )
                      : window.braintree.client.create({ authorization: t.authorization, debug: t.debug });
                  });
            },
          };
        },
        { './assets': 14, './braintree-error': 16, './errors': 23 },
      ],
      22: [
        function (t, e, n) {
          'use strict';
          e.exports = function (t, n) {
            return (
              (n = null == n ? '' : n),
              t.reduce(function (t, e) {
                return (t[e] = n + e), t;
              }, {})
            );
          };
        },
        {},
      ],
      23: [
        function (t, e, n) {
          'use strict';
          t = t('./braintree-error');
          e.exports = {
            INVALID_USE_OF_INTERNAL_FUNCTION: { type: t.types.INTERNAL, code: 'INVALID_USE_OF_INTERNAL_FUNCTION' },
            INSTANTIATION_OPTION_REQUIRED: { type: t.types.MERCHANT, code: 'INSTANTIATION_OPTION_REQUIRED' },
            INCOMPATIBLE_VERSIONS: { type: t.types.MERCHANT, code: 'INCOMPATIBLE_VERSIONS' },
            CLIENT_SCRIPT_FAILED_TO_LOAD: {
              type: t.types.NETWORK,
              code: 'CLIENT_SCRIPT_FAILED_TO_LOAD',
              message: 'Braintree client script could not be loaded.',
            },
            METHOD_CALLED_AFTER_TEARDOWN: { type: t.types.MERCHANT, code: 'METHOD_CALLED_AFTER_TEARDOWN' },
          };
        },
        { './braintree-error': 16 },
      ],
      24: [
        function (t, e, n) {
          'use strict';
          e.exports = function (e) {
            return Object.keys(e).filter(function (t) {
              return 'function' == typeof e[t];
            });
          };
        },
        {},
      ],
    },
    {},
    [11]
  )(11);
});
