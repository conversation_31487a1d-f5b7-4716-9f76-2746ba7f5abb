{"data": {"clientConfiguration": {"analyticsUrl": "https://client-analytics.braintreegateway.com/8qqhzhdnhnsj5tq8", "environment": "PRODUCTION", "merchantId": "8qqhzhdnhnsj5tq8", "assetsUrl": "https://assets.braintreegateway.com", "clientApiUrl": "https://api.braintreegateway.com:443/merchants/8qqhzhdnhnsj5tq8/client_api", "creditCard": {"supportedCardBrands": [], "challenges": [], "threeDSecureEnabled": false, "threeDSecure": null}, "applePayWeb": null, "googlePay": null, "ideal": null, "kount": {"merchantId": null}, "masterpass": null, "paypal": {"displayName": "The Gap, Inc.", "clientId": "AQesi_Pz2M8XZ9rDl3BPodoyvLh7Ag8P4XbYWggEgUfxqX7xR20eUUGDpKazEm25b7hhDChHwkUaz3mx", "assetsUrl": "https://checkout.paypal.com", "environment": "LIVE", "environmentNoNetwork": false, "unvettedMerchant": false, "braintreeClientId": "ARKrYRDh3AGXDzW7sO_3bSkq-U1C7HG_uWNC-z57LjYSDNUOSaOtIa9q6VpW", "billingAgreementsEnabled": true, "merchantAccountId": "GapUSD", "currencyCode": "USD", "payeeEmail": null}, "unionPay": null, "usBankAccount": null, "venmo": null, "visaCheckout": null, "braintreeApi": null, "supportedFeatures": ["TOKENIZE_CREDIT_CARDS"]}}, "extensions": {"requestId": "3ad8616c-00af-4dc0-b9d3-dd4079d5fc48"}}