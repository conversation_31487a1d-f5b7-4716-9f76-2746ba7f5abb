!(function (e) {
  var t;
  'object' == typeof exports && 'undefined' != typeof module
    ? (module.exports = e())
    : 'function' == typeof define && define.amd
      ? define([], e)
      : ((
          (t = 'undefined' != typeof window ? window : 'undefined' != typeof global ? global : 'undefined' != typeof self ? self : this).braintree ||
          (t.braintree = {})
        ).paypalCheckout = e());
})(function () {
  return (function n(i, o, s) {
    function a(t, e) {
      if (!o[t]) {
        if (!i[t]) {
          var r = 'function' == typeof require && require;
          if (!e && r) return r(t, !0);
          if (c) return c(t, !0);
          throw (((e = new Error("Cannot find module '" + t + "'")).code = 'MODULE_NOT_FOUND'), e);
        }
        (r = o[t] = { exports: {} }),
          i[t][0].call(
            r.exports,
            function (e) {
              return a(i[t][1][e] || e);
            },
            r,
            r.exports,
            n,
            i,
            o,
            s
          );
      }
      return o[t].exports;
    }
    for (var c = 'function' == typeof require && require, e = 0; e < s.length; e++) a(s[e]);
    return a;
  })(
    {
      1: [
        function (e, t, r) {
          'use strict';
          var n =
              (this && this.__importDefault) ||
              function (e) {
                return e && e.__esModule ? e : { default: e };
              },
            n = (Object.defineProperty(r, '__esModule', { value: !0 }), (r.PromiseGlobal = void 0), n(e('promise-polyfill'))),
            e = 'undefined' != typeof Promise ? Promise : n.default;
          r.PromiseGlobal = e;
        },
        { 'promise-polyfill': 48 },
      ],
      2: [
        function (e, t, r) {
          'use strict';
          var s = e('./lib/promise'),
            a = {};
          function n(r) {
            var e,
              n,
              t,
              i,
              o = JSON.stringify(r);
            return (
              (!r.forceScriptReload && (e = a[o])) ||
                ((n = document.createElement('script')),
                (t = r.dataAttributes || {}),
                (i = r.container || document.head),
                (n.src = r.src),
                (n.id = r.id || ''),
                (n.async = !0),
                r.crossorigin && n.setAttribute('crossorigin', '' + r.crossorigin),
                Object.keys(t).forEach(function (e) {
                  n.setAttribute('data-' + e, '' + t[e]);
                }),
                (e = new s.PromiseGlobal(function (e, t) {
                  n.addEventListener('load', function () {
                    e(n);
                  }),
                    n.addEventListener('error', function () {
                      t(new Error(r.src + ' failed to load.'));
                    }),
                    n.addEventListener('abort', function () {
                      t(new Error(r.src + ' has aborted.'));
                    }),
                    i.appendChild(n);
                })),
                (a[o] = e)),
              e
            );
          }
          (n.clearCache = function () {
            a = {};
          }),
            (t.exports = n);
        },
        { './lib/promise': 1 },
      ],
      3: [
        function (e, t, r) {
          t.exports = e('./dist/load-script');
        },
        { './dist/load-script': 2 },
      ],
      4: [
        function (e, t, r) {
          'use strict';
          t.exports = function (e) {
            return (e = e || window.navigator.userAgent), /Android/i.test(e);
          };
        },
        {},
      ],
      5: [
        function (e, t, r) {
          'use strict';
          var n = e('./is-edge'),
            i = e('./is-samsung'),
            o = e('./is-duckduckgo'),
            s = e('./is-opera'),
            a = e('./is-silk');
          t.exports = function (e) {
            return !((-1 === (e = e || window.navigator.userAgent).indexOf('Chrome') && -1 === e.indexOf('CriOS')) || n(e) || i(e) || o(e) || s(e) || a(e));
          };
        },
        { './is-duckduckgo': 6, './is-edge': 7, './is-opera': 14, './is-samsung': 15, './is-silk': 16 },
      ],
      6: [
        function (e, t, r) {
          'use strict';
          t.exports = function (e) {
            return -1 !== (e = e || window.navigator.userAgent).indexOf('DuckDuckGo/');
          };
        },
        {},
      ],
      7: [
        function (e, t, r) {
          'use strict';
          t.exports = function (e) {
            return -1 !== (e = e || window.navigator.userAgent).indexOf('Edge/');
          };
        },
        {},
      ],
      8: [
        function (e, t, r) {
          'use strict';
          t.exports = function (e) {
            return (e = e || window.navigator.userAgent), /FxiOS/i.test(e);
          };
        },
        {},
      ],
      9: [
        function (e, t, r) {
          'use strict';
          var n = e('./is-ios');
          t.exports = function (e) {
            return (e = e || window.navigator.userAgent), n(e) && /\bGSA\b/.test(e);
          };
        },
        { './is-ios': 12 },
      ],
      10: [
        function (e, t, r) {
          'use strict';
          var n = e('./is-ios'),
            i = e('./is-ios-google-search-app');
          t.exports = function (e) {
            return (e = e || window.navigator.userAgent), !!n(e) && (!!i(e) || /.+AppleWebKit(?!.*Safari)/i.test(e));
          };
        },
        { './is-ios': 12, './is-ios-google-search-app': 9 },
      ],
      11: [
        function (e, t, r) {
          'use strict';
          var n = e('./is-ios-webview');
          t.exports = function (e, t) {
            return (t = void 0 !== t ? t : window.statusbar.visible), n(e) && t;
          };
        },
        { './is-ios-webview': 10 },
      ],
      12: [
        function (e, t, r) {
          'use strict';
          var i = e('./is-ipados');
          t.exports = function (e, t, r) {
            void 0 === t && (t = !0), (e = e || window.navigator.userAgent);
            var n = /iPhone|iPod|iPad/i.test(e);
            return t ? n || i(e, r) : n;
          };
        },
        { './is-ipados': 13 },
      ],
      13: [
        function (e, t, r) {
          'use strict';
          t.exports = function (e, t) {
            return (e = e || window.navigator.userAgent), (t = t || window.document), /Mac|iPad/i.test(e) && 'ontouchend' in t;
          };
        },
        {},
      ],
      14: [
        function (e, t, r) {
          'use strict';
          t.exports = function (e) {
            return -1 !== (e = e || window.navigator.userAgent).indexOf('OPR/') || -1 !== e.indexOf('Opera/') || -1 !== e.indexOf('OPT/');
          };
        },
        {},
      ],
      15: [
        function (e, t, r) {
          'use strict';
          t.exports = function (e) {
            return (e = e || window.navigator.userAgent), /SamsungBrowser/i.test(e);
          };
        },
        {},
      ],
      16: [
        function (e, t, r) {
          'use strict';
          t.exports = function (e) {
            return -1 !== (e = e || window.navigator.userAgent).indexOf('Silk/');
          };
        },
        {},
      ],
      17: [
        function (e, t, r) {
          'use strict';
          var n = e('./is-android'),
            i = e('./is-ios-firefox'),
            o = e('./is-ios-webview'),
            s = e('./is-chrome'),
            a = e('./is-samsung'),
            c = e('./is-duckduckgo');
          function u(e) {
            return -1 < (e = e || window.navigator.userAgent).indexOf('Opera Mini');
          }
          t.exports = function (e) {
            return (
              (e = e || window.navigator.userAgent),
              !(
                o(e) ||
                i(e) ||
                ((t = (t = e) || window.navigator.userAgent),
                (n(t) && /Version\/[\d.]+/i.test(t) && !u(t) && !c(t)) ||
                  u(e) ||
                  ((t = (t = (t = e) || window.navigator.userAgent).match(/CriOS\/(\d+)\./)) && parseInt(t[1], 10) < 48) ||
                  (!s((t = e)) && !a(t) && /samsung/i.test(t)))
              )
            );
            var t;
          };
        },
        { './is-android': 4, './is-chrome': 5, './is-duckduckgo': 6, './is-ios-firefox': 8, './is-ios-webview': 10, './is-samsung': 15 },
      ],
      18: [
        function (e, t, r) {
          t.exports = e('./dist/is-ios-wkwebview');
        },
        { './dist/is-ios-wkwebview': 11 },
      ],
      19: [
        function (e, t, r) {
          t.exports = e('./dist/is-ios');
        },
        { './dist/is-ios': 12 },
      ],
      20: [
        function (e, t, r) {
          t.exports = e('./dist/supports-popups');
        },
        { './dist/supports-popups': 17 },
      ],
      21: [
        function (e, t, r) {
          'use strict';
          var n = 'undefined' != typeof Promise ? Promise : null,
            n =
              ((i.defaultOnResolve = function (e) {
                return i.Promise.resolve(e);
              }),
              (i.defaultOnReject = function (e) {
                return i.Promise.reject(e);
              }),
              (i.setPromise = function (e) {
                i.Promise = e;
              }),
              (i.shouldCatchExceptions = function (e) {
                return e.hasOwnProperty('suppressUnhandledPromiseMessage')
                  ? Boolean(e.suppressUnhandledPromiseMessage)
                  : Boolean(i.suppressUnhandledPromiseMessage);
              }),
              (i.all = function (e) {
                return i.Promise.all(e);
              }),
              (i.allSettled = function (e) {
                return i.Promise.allSettled(e);
              }),
              (i.race = function (e) {
                return i.Promise.race(e);
              }),
              (i.reject = function (e) {
                return i.Promise.reject(e);
              }),
              (i.resolve = function (e) {
                return i.Promise.resolve(e);
              }),
              (i.prototype.then = function () {
                for (var e, t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                return (e = this._promise).then.apply(e, t);
              }),
              (i.prototype.catch = function () {
                for (var e, t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
                return (e = this._promise).catch.apply(e, t);
              }),
              (i.prototype.resolve = function (e) {
                var t = this;
                return (
                  this.isFulfilled ||
                    (this._setResolved(),
                    i.Promise.resolve()
                      .then(function () {
                        return t._onResolve(e);
                      })
                      .then(function (e) {
                        t._resolveFunction(e);
                      })
                      .catch(function (e) {
                        t._resetState(), t.reject(e);
                      })),
                  this
                );
              }),
              (i.prototype.reject = function (e) {
                var t = this;
                return (
                  this.isFulfilled ||
                    (this._setRejected(),
                    i.Promise.resolve()
                      .then(function () {
                        return t._onReject(e);
                      })
                      .then(function (e) {
                        t._setResolved(), t._resolveFunction(e);
                      })
                      .catch(function (e) {
                        return t._rejectFunction(e);
                      })),
                  this
                );
              }),
              (i.prototype._resetState = function () {
                (this.isFulfilled = !1), (this.isResolved = !1), (this.isRejected = !1);
              }),
              (i.prototype._setResolved = function () {
                (this.isFulfilled = !0), (this.isResolved = !0), (this.isRejected = !1);
              }),
              (i.prototype._setRejected = function () {
                (this.isFulfilled = !0), (this.isResolved = !1), (this.isRejected = !0);
              }),
              (i.Promise = n),
              i);
          function i(e) {
            var r = this;
            'function' == typeof e
              ? (this._promise = new i.Promise(e))
              : ((this._promise = new i.Promise(function (e, t) {
                  (r._resolveFunction = e), (r._rejectFunction = t);
                })),
                (this._onResolve = (e = e || {}).onResolve || i.defaultOnResolve),
                (this._onReject = e.onReject || i.defaultOnReject),
                i.shouldCatchExceptions(e) && this._promise.catch(function () {}),
                this._resetState());
          }
          t.exports = n;
        },
        {},
      ],
      22: [
        function (e, t, r) {
          'use strict';
          var n = e('./lib/set-attributes'),
            i = e('./lib/default-attributes'),
            o = e('./lib/assign');
          t.exports = function (e) {
            void 0 === e && (e = {});
            var t = document.createElement('iframe'),
              e = o.assign({}, i.defaultAttributes, e);
            return (
              e.style && 'string' != typeof e.style && (o.assign(t.style, e.style), delete e.style),
              n.setAttributes(t, e),
              t.getAttribute('id') || (t.id = t.name),
              t
            );
          };
        },
        { './lib/assign': 23, './lib/default-attributes': 24, './lib/set-attributes': 25 },
      ],
      23: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }),
            (r.assign = void 0),
            (r.assign = function (r) {
              for (var e = [], t = 1; t < arguments.length; t++) e[t - 1] = arguments[t];
              return (
                e.forEach(function (t) {
                  'object' == typeof t &&
                    Object.keys(t).forEach(function (e) {
                      r[e] = t[e];
                    });
                }),
                r
              );
            });
        },
        {},
      ],
      24: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }),
            (r.defaultAttributes = void 0),
            (r.defaultAttributes = { src: 'about:blank', frameBorder: 0, allowtransparency: !0, scrolling: 'no' });
        },
        {},
      ],
      25: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }),
            (r.setAttributes = void 0),
            (r.setAttributes = function (e, t) {
              for (var r in t) {
                var n;
                t.hasOwnProperty(r) && (null == (n = t[r]) ? e.removeAttribute(r) : e.setAttribute(r, n));
              }
            });
        },
        {},
      ],
      26: [
        function (e, t, r) {
          'use strict';
          t.exports = function () {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (e) {
              var t = (16 * Math.random()) | 0;
              return ('x' === e ? t : (3 & t) | 8).toString(16);
            });
          };
        },
        {},
      ],
      27: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }),
            (r.deferred = function (r) {
              return function () {
                for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t];
                setTimeout(function () {
                  try {
                    r.apply(void 0, e);
                  } catch (e) {
                    console.log('Error in callback function'), console.log(e);
                  }
                }, 1);
              };
            });
        },
        {},
      ],
      28: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }),
            (r.once = function (r) {
              var n = !1;
              return function () {
                for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t];
                n || ((n = !0), r.apply(void 0, e));
              };
            });
        },
        {},
      ],
      29: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }),
            (r.promiseOrCallback = function (e, t) {
              if (!t) return e;
              e.then(function (e) {
                return t(null, e);
              }).catch(function (e) {
                return t(e);
              });
            });
        },
        {},
      ],
      30: [
        function (e, t, r) {
          'use strict';
          var i = e('./lib/deferred'),
            o = e('./lib/once'),
            s = e('./lib/promise-or-callback');
          function a(n) {
            return function () {
              for (var e, t = [], r = 0; r < arguments.length; r++) t[r] = arguments[r];
              return 'function' == typeof t[t.length - 1] && ((e = t.pop()), (e = o.once(i.deferred(e)))), s.promiseOrCallback(n.apply(this, t), e);
            };
          }
          (a.wrapPrototype = function (n, e) {
            var i = (e = void 0 === e ? {} : e).ignoreMethods || [],
              o = !0 === e.transformPrivateMethods;
            return (
              Object.getOwnPropertyNames(n.prototype)
                .filter(function (e) {
                  var t = 'constructor' !== e && 'function' == typeof n.prototype[e],
                    r = -1 === i.indexOf(e),
                    e = o || '_' !== e.charAt(0);
                  return t && e && r;
                })
                .forEach(function (e) {
                  var t = n.prototype[e];
                  n.prototype[e] = a(t);
                }),
              n
            );
          }),
            (t.exports = a);
        },
        { './lib/deferred': 27, './lib/once': 28, './lib/promise-or-callback': 29 },
      ],
      31: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }), (r.Framebus = void 0);
          var a = e('./lib'),
            e = 'undefined' != typeof window && window.Promise;
          function o(e) {
            (this.origin = (e = void 0 === e ? {} : e).origin || '*'),
              (this.channel = e.channel || ''),
              (this.verifyDomain = e.verifyDomain),
              (this.targetFrames = e.targetFrames || []),
              (this.limitBroadcastToFramesArray = Boolean(e.targetFrames)),
              (this.isDestroyed = !1),
              (this.listeners = []),
              (this.hasAdditionalChecksForOnListeners = Boolean(this.verifyDomain || this.limitBroadcastToFramesArray));
          }
          (o.setPromise = function (e) {
            o.Promise = e;
          }),
            (o.target = function (e) {
              return new o(e);
            }),
            (o.prototype.addTargetFrame = function (e) {
              this.limitBroadcastToFramesArray && this.targetFrames.push(e);
            }),
            (o.prototype.include = function (e) {
              return null != e && null != e.Window && e.constructor === e.Window && (a.childWindows.push(e), !0);
            }),
            (o.prototype.target = function (e) {
              return o.target(e);
            }),
            (o.prototype.emit = function (e, t, r) {
              if (this.isDestroyed) return !1;
              var n = this.origin;
              if (((e = this.namespaceEvent(e)), (0, a.isntString)(e))) return !1;
              if ((0, a.isntString)(n)) return !1;
              'function' == typeof t && ((r = t), (t = void 0));
              var i = (0, a.packagePayload)(e, n, t, r);
              return (
                !!i &&
                (this.limitBroadcastToFramesArray
                  ? this.targetFramesAsWindows().forEach(function (e) {
                      (0, a.sendMessage)(e, i, n);
                    })
                  : (0, a.broadcast)(i, { origin: n, frame: window.top || window.self }),
                !0)
              );
            }),
            (o.prototype.emitAsPromise = function (r, n) {
              var i = this;
              return new o.Promise(function (t, e) {
                i.emit(r, n, function (e) {
                  t(e);
                }) || e(new Error('Listener not added for "'.concat(r, '"')));
              });
            }),
            (o.prototype.on = function (e, r) {
              var n, t, i;
              return (
                !this.isDestroyed &&
                ((t = (n = this).origin),
                (i = r),
                (e = this.namespaceEvent(e)),
                !(0, a.subscriptionArgsInvalid)(e, i, t) &&
                  (this.hasAdditionalChecksForOnListeners &&
                    (i = function () {
                      for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t];
                      n.passesVerifyDomainCheck(this && this.origin) && n.hasMatchingTargetFrame(this && this.source) && r.apply(void 0, e);
                    }),
                  this.listeners.push({ eventName: e, handler: i, originalHandler: r }),
                  (a.subscribers[t] = a.subscribers[t] || {}),
                  (a.subscribers[t][e] = a.subscribers[t][e] || []),
                  a.subscribers[t][e].push(i),
                  !0))
              );
            }),
            (o.prototype.off = function (e, t) {
              var r = t;
              if (!this.isDestroyed) {
                if (this.verifyDomain)
                  for (var n = 0; n < this.listeners.length; n++) {
                    var i = this.listeners[n];
                    i.originalHandler === t && (r = i.handler);
                  }
                e = this.namespaceEvent(e);
                var o = this.origin;
                if (!(0, a.subscriptionArgsInvalid)(e, r, o)) {
                  var s = a.subscribers[o] && a.subscribers[o][e];
                  if (s) for (n = 0; n < s.length; n++) if (s[n] === r) return s.splice(n, 1), !0;
                }
              }
              return !1;
            }),
            (o.prototype.teardown = function () {
              if (!this.isDestroyed) {
                this.isDestroyed = !0;
                for (var e = 0; e < this.listeners.length; e++) {
                  var t = this.listeners[e];
                  this.off(t.eventName, t.handler);
                }
                this.listeners.length = 0;
              }
            }),
            (o.prototype.passesVerifyDomainCheck = function (e) {
              return !this.verifyDomain || this.checkOrigin(e);
            }),
            (o.prototype.targetFramesAsWindows = function () {
              return this.limitBroadcastToFramesArray
                ? this.targetFrames
                    .map(function (e) {
                      return e instanceof HTMLIFrameElement ? e.contentWindow : e;
                    })
                    .filter(function (e) {
                      return e;
                    })
                : [];
            }),
            (o.prototype.hasMatchingTargetFrame = function (t) {
              var e;
              return (
                !this.limitBroadcastToFramesArray ||
                ((e = this.targetFramesAsWindows().find(function (e) {
                  return e === t;
                })),
                Boolean(e))
              );
            }),
            (o.prototype.checkOrigin = function (e) {
              var t,
                r = document.createElement('a');
              return (
                (r.href = location.href),
                (t = 'https:' === r.protocol ? r.host.replace(/:443$/, '') : 'http:' === r.protocol ? r.host.replace(/:80$/, '') : r.host),
                r.protocol + '//' + t === e || !this.verifyDomain || this.verifyDomain(e)
              );
            }),
            (o.prototype.namespaceEvent = function (e) {
              return this.channel ? ''.concat(this.channel, ':').concat(e) : e;
            }),
            (o.Promise = e),
            (r.Framebus = o);
        },
        { './lib': 39 },
      ],
      32: [
        function (e, t, r) {
          'use strict';
          var n = e('./lib'),
            e = e('./framebus');
          (0, n.attach)(), (t.exports = e.Framebus);
        },
        { './framebus': 31, './lib': 39 },
      ],
      33: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }), (r.detach = r.attach = void 0);
          var n = e('./'),
            i = !1;
          (r.attach = function () {
            i || 'undefined' == typeof window || ((i = !0), window.addEventListener('message', n.onMessage, !1));
          }),
            (r.detach = function () {
              (i = !1), window.removeEventListener('message', n.onMessage, !1);
            });
        },
        { './': 39 },
      ],
      34: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }), (r.broadcastToChildWindows = void 0);
          var o = e('./');
          r.broadcastToChildWindows = function (e, t, r) {
            for (var n = o.childWindows.length - 1; 0 <= n; n--) {
              var i = o.childWindows[n];
              i.closed ? o.childWindows.splice(n, 1) : r !== i && (0, o.broadcast)(e, { origin: t, frame: i.top });
            }
          };
        },
        { './': 39 },
      ],
      35: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }), (r.broadcast = void 0);
          var a = e('./');
          r.broadcast = function e(t, r) {
            var n,
              i = 0,
              o = r.origin,
              s = r.frame;
            try {
              for (s.postMessage(t, o), (0, a.hasOpener)(s) && s.opener.top !== window.top && e(t, { origin: o, frame: s.opener.top }); (n = s.frames[i]); )
                e(t, { origin: o, frame: n }), i++;
            } catch (e) {}
          };
        },
        { './': 39 },
      ],
      36: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }),
            (r.subscribers = r.childWindows = r.prefix = void 0),
            (r.prefix = '/*framebus*/'),
            (r.childWindows = []),
            (r.subscribers = {});
        },
        {},
      ],
      37: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }), (r.dispatch = void 0);
          var a = e('./');
          r.dispatch = function (e, t, r, n, i) {
            if (a.subscribers[e] && a.subscribers[e][t]) {
              var o = [];
              r && o.push(r), n && o.push(n);
              for (var s = 0; s < a.subscribers[e][t].length; s++) a.subscribers[e][t][s].apply(i, o);
            }
          };
        },
        { './': 39 },
      ],
      38: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }),
            (r.hasOpener = void 0),
            (r.hasOpener = function (e) {
              return e.top === e && null != e.opener && e.opener !== e && !0 !== e.opener.closed;
            });
        },
        {},
      ],
      39: [
        function (e, t, r) {
          'use strict';
          var n =
              (this && this.__createBinding) ||
              (Object.create
                ? function (e, t, r, n) {
                    void 0 === n && (n = r);
                    var i = Object.getOwnPropertyDescriptor(t, r);
                    (i && ('get' in i ? t.__esModule : !i.writable && !i.configurable)) ||
                      (i = {
                        enumerable: !0,
                        get: function () {
                          return t[r];
                        },
                      }),
                      Object.defineProperty(e, n, i);
                  }
                : function (e, t, r, n) {
                    e[(n = void 0 === n ? r : n)] = t[r];
                  }),
            i =
              (this && this.__exportStar) ||
              function (e, t) {
                for (var r in e) 'default' === r || Object.prototype.hasOwnProperty.call(t, r) || n(t, e, r);
              };
          Object.defineProperty(r, '__esModule', { value: !0 }),
            i(e('./attach'), r),
            i(e('./broadcast-to-child-windows'), r),
            i(e('./broadcast'), r),
            i(e('./constants'), r),
            i(e('./dispatch'), r),
            i(e('./has-opener'), r),
            i(e('./is-not-string'), r),
            i(e('./message'), r),
            i(e('./package-payload'), r),
            i(e('./send-message'), r),
            i(e('./subscribe-replier'), r),
            i(e('./subscription-args-invalid'), r),
            i(e('./types'), r),
            i(e('./unpack-payload'), r);
        },
        {
          './attach': 33,
          './broadcast': 35,
          './broadcast-to-child-windows': 34,
          './constants': 36,
          './dispatch': 37,
          './has-opener': 38,
          './is-not-string': 40,
          './message': 41,
          './package-payload': 42,
          './send-message': 43,
          './subscribe-replier': 44,
          './subscription-args-invalid': 45,
          './types': 46,
          './unpack-payload': 47,
        },
      ],
      40: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }),
            (r.isntString = void 0),
            (r.isntString = function (e) {
              return 'string' != typeof e;
            });
        },
        {},
      ],
      41: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }), (r.onMessage = void 0);
          var i = e('./');
          r.onMessage = function (e) {
            var t, r, n;
            (0, i.isntString)(e.data) ||
              ((t = (0, i.unpackPayload)(e)) &&
                ((r = t.eventData),
                (n = t.reply),
                (0, i.dispatch)('*', t.event, r, n, e),
                (0, i.dispatch)(e.origin, t.event, r, n, e),
                (0, i.broadcastToChildWindows)(e.data, t.origin, e.source)));
          };
        },
        { './': 39 },
      ],
      42: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }), (r.packagePayload = void 0);
          var o = e('./');
          r.packagePayload = function (e, t, r, n) {
            var i,
              e = { event: e, origin: t };
            'function' == typeof n && (e.reply = (0, o.subscribeReplier)(n, t)), (e.eventData = r);
            try {
              i = o.prefix + JSON.stringify(e);
            } catch (e) {
              throw new Error('Could not stringify event: '.concat(e.message));
            }
            return i;
          };
        },
        { './': 39 },
      ],
      43: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }),
            (r.sendMessage = void 0),
            (r.sendMessage = function (e, t, r) {
              try {
                e.postMessage(t, r);
              } catch (e) {}
            });
        },
        {},
      ],
      44: [
        function (e, t, r) {
          'use strict';
          var n =
              (this && this.__importDefault) ||
              function (e) {
                return e && e.__esModule ? e : { default: e };
              },
            s = (Object.defineProperty(r, '__esModule', { value: !0 }), (r.subscribeReplier = void 0), e('../framebus')),
            a = n(e('@braintree/uuid'));
          r.subscribeReplier = function (n, i) {
            var o = (0, a.default)();
            return (
              s.Framebus.target({ origin: i }).on(o, function e(t, r) {
                n(t, r), s.Framebus.target({ origin: i }).off(o, e);
              }),
              o
            );
          };
        },
        { '../framebus': 31, '@braintree/uuid': 26 },
      ],
      45: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }), (r.subscriptionArgsInvalid = void 0);
          var n = e('./');
          r.subscriptionArgsInvalid = function (e, t, r) {
            return !!(0, n.isntString)(e) || 'function' != typeof t || (0, n.isntString)(r);
          };
        },
        { './': 39 },
      ],
      46: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 });
        },
        {},
      ],
      47: [
        function (e, t, r) {
          'use strict';
          Object.defineProperty(r, '__esModule', { value: !0 }), (r.unpackPayload = void 0);
          var o = e('./');
          r.unpackPayload = function (e) {
            var t, r, n, i;
            if (e.data.slice(0, o.prefix.length) !== o.prefix) return !1;
            try {
              t = JSON.parse(e.data.slice(o.prefix.length));
            } catch (e) {
              return !1;
            }
            return (
              t.reply &&
                ((r = e.origin),
                (n = e.source),
                (i = t.reply),
                (t.reply = function (e) {
                  !n || ((e = (0, o.packagePayload)(i, r, e)) && n.postMessage(e, r));
                })),
              t
            );
          };
        },
        { './': 39 },
      ],
      48: [
        function (e, t, r) {
          'use strict';
          var n = setTimeout;
          function c(e) {
            return Boolean(e && void 0 !== e.length);
          }
          function i() {}
          function o(e) {
            if (!(this instanceof o)) throw new TypeError('Promises must be constructed via new');
            if ('function' != typeof e) throw new TypeError('not a function');
            (this._state = 0), (this._handled = !1), (this._value = void 0), (this._deferreds = []), l(e, this);
          }
          function s(r, n) {
            for (; 3 === r._state; ) r = r._value;
            0 === r._state
              ? r._deferreds.push(n)
              : ((r._handled = !0),
                o._immediateFn(function () {
                  var e,
                    t = 1 === r._state ? n.onFulfilled : n.onRejected;
                  if (null === t) (1 === r._state ? a : u)(n.promise, r._value);
                  else {
                    try {
                      e = t(r._value);
                    } catch (e) {
                      return void u(n.promise, e);
                    }
                    a(n.promise, e);
                  }
                }));
          }
          function a(t, e) {
            try {
              if (e === t) throw new TypeError('A promise cannot be resolved with itself.');
              if (e && ('object' == typeof e || 'function' == typeof e)) {
                var r = e.then;
                if (e instanceof o) return (t._state = 3), (t._value = e), void p(t);
                if ('function' == typeof r)
                  return void l(
                    ((n = r),
                    (i = e),
                    function () {
                      n.apply(i, arguments);
                    }),
                    t
                  );
              }
              (t._state = 1), (t._value = e), p(t);
            } catch (e) {
              u(t, e);
            }
            var n, i;
          }
          function u(e, t) {
            (e._state = 2), (e._value = t), p(e);
          }
          function p(e) {
            2 === e._state &&
              0 === e._deferreds.length &&
              o._immediateFn(function () {
                e._handled || o._unhandledRejectionFn(e._value);
              });
            for (var t = 0, r = e._deferreds.length; t < r; t++) s(e, e._deferreds[t]);
            e._deferreds = null;
          }
          function d(e, t, r) {
            (this.onFulfilled = 'function' == typeof e ? e : null), (this.onRejected = 'function' == typeof t ? t : null), (this.promise = r);
          }
          function l(e, t) {
            var r = !1;
            try {
              e(
                function (e) {
                  r || ((r = !0), a(t, e));
                },
                function (e) {
                  r || ((r = !0), u(t, e));
                }
              );
            } catch (e) {
              r || ((r = !0), u(t, e));
            }
          }
          (o.prototype.catch = function (e) {
            return this.then(null, e);
          }),
            (o.prototype.then = function (e, t) {
              var r = new this.constructor(i);
              return s(this, new d(e, t, r)), r;
            }),
            (o.prototype.finally = function (t) {
              var r = this.constructor;
              return this.then(
                function (e) {
                  return r.resolve(t()).then(function () {
                    return e;
                  });
                },
                function (e) {
                  return r.resolve(t()).then(function () {
                    return r.reject(e);
                  });
                }
              );
            }),
            (o.all = function (t) {
              return new o(function (i, o) {
                if (!c(t)) return o(new TypeError('Promise.all accepts an array'));
                var s = Array.prototype.slice.call(t);
                if (0 === s.length) return i([]);
                var a = s.length;
                for (var e = 0; e < s.length; e++)
                  !(function t(r, e) {
                    try {
                      if (e && ('object' == typeof e || 'function' == typeof e)) {
                        var n = e.then;
                        if ('function' == typeof n)
                          return void n.call(
                            e,
                            function (e) {
                              t(r, e);
                            },
                            o
                          );
                      }
                      (s[r] = e), 0 == --a && i(s);
                    } catch (e) {
                      o(e);
                    }
                  })(e, s[e]);
              });
            }),
            (o.allSettled = function (r) {
              return new this(function (i, e) {
                if (!r || void 0 === r.length) return e(new TypeError(typeof r + ' ' + r + ' is not iterable(cannot read property Symbol(Symbol.iterator))'));
                var o = Array.prototype.slice.call(r);
                if (0 === o.length) return i([]);
                var s = o.length;
                for (var t = 0; t < o.length; t++)
                  !(function t(r, e) {
                    if (e && ('object' == typeof e || 'function' == typeof e)) {
                      var n = e.then;
                      if ('function' == typeof n)
                        return void n.call(
                          e,
                          function (e) {
                            t(r, e);
                          },
                          function (e) {
                            (o[r] = { status: 'rejected', reason: e }), 0 == --s && i(o);
                          }
                        );
                    }
                    (o[r] = { status: 'fulfilled', value: e }), 0 == --s && i(o);
                  })(t, o[t]);
              });
            }),
            (o.resolve = function (t) {
              return t && 'object' == typeof t && t.constructor === o
                ? t
                : new o(function (e) {
                    e(t);
                  });
            }),
            (o.reject = function (r) {
              return new o(function (e, t) {
                t(r);
              });
            }),
            (o.race = function (i) {
              return new o(function (e, t) {
                if (!c(i)) return t(new TypeError('Promise.race accepts an array'));
                for (var r = 0, n = i.length; r < n; r++) o.resolve(i[r]).then(e, t);
              });
            }),
            (o._immediateFn =
              'function' == typeof setImmediate
                ? function (e) {
                    setImmediate(e);
                  }
                : function (e) {
                    n(e, 0);
                  }),
            (o._unhandledRejectionFn = function (e) {
              'undefined' != typeof console && console && console.warn('Possible Unhandled Promise Rejection:', e);
            }),
            (t.exports = o);
        },
        {},
      ],
      49: [
        function (e, t, r) {
          'use strict';
          var o = e('./create-authorization-data'),
            s = e('./json-clone'),
            a = e('./constants');
          t.exports = function (e, t) {
            var r,
              n = t ? s(t) : {},
              t = o(e.authorization).attrs,
              i = s(e.analyticsMetadata);
            for (r in ((n.braintreeLibraryVersion = a.BRAINTREE_LIBRARY_VERSION), n._meta)) n._meta.hasOwnProperty(r) && (i[r] = n._meta[r]);
            return (n._meta = i), t.tokenizationKey ? (n.tokenizationKey = t.tokenizationKey) : (n.authorizationFingerprint = t.authorizationFingerprint), n;
          };
        },
        { './constants': 55, './create-authorization-data': 59, './json-clone': 74 },
      ],
      50: [
        function (e, t, r) {
          'use strict';
          var a = e('./constants'),
            c = e('./add-metadata');
          t.exports = {
            sendEvent: function (e, i, o) {
              var s = Date.now();
              return Promise.resolve(e)
                .then(function (e) {
                  var t = Date.now(),
                    r = e.getConfiguration(),
                    e = e._request,
                    n = r.gatewayConfiguration.analytics.url,
                    t = { analytics: [{ kind: a.ANALYTICS_PREFIX + i, isAsync: Math.floor(t / 1e3) !== Math.floor(s / 1e3), timestamp: s }] };
                  e({ url: n, method: 'post', data: c(r, t), timeout: a.ANALYTICS_REQUEST_TIMEOUT_MS }, o);
                })
                .catch(function (e) {
                  o && o(e);
                });
            },
          };
        },
        { './add-metadata': 49, './constants': 55 },
      ],
      51: [
        function (e, t, r) {
          'use strict';
          e = e('@braintree/asset-loader/load-script');
          t.exports = { loadScript: e };
        },
        { '@braintree/asset-loader/load-script': 3 },
      ],
      52: [
        function (e, t, r) {
          'use strict';
          var n = 'function' == typeof Object.assign ? Object.assign : i;
          function i(e) {
            for (var t, r, n = 1; n < arguments.length; n++) for (r in (t = arguments[n])) t.hasOwnProperty(r) && (e[r] = t[r]);
            return e;
          }
          t.exports = { assign: n, _assign: i };
        },
        {},
      ],
      53: [
        function (e, t, r) {
          'use strict';
          var n = e('./braintree-error'),
            i = e('./errors'),
            o = '3.100.0';
          t.exports = {
            verify: function (e) {
              var t, r;
              return e
                ? ((r = e.name),
                  (t = e.client),
                  (e = e.authorization),
                  t || e
                    ? e || t.getVersion() === o
                      ? Promise.resolve()
                      : Promise.reject(
                          new n({
                            type: i.INCOMPATIBLE_VERSIONS.type,
                            code: i.INCOMPATIBLE_VERSIONS.code,
                            message: 'Client (version ' + t.getVersion() + ') and ' + r + ' (version ' + o + ') components must be from the same SDK version.',
                          })
                        )
                    : Promise.reject(
                        new n({
                          type: i.INSTANTIATION_OPTION_REQUIRED.type,
                          code: i.INSTANTIATION_OPTION_REQUIRED.code,
                          message: 'options.client is required when instantiating ' + r + '.',
                        })
                      ))
                : Promise.reject(
                    new n({
                      type: i.INVALID_USE_OF_INTERNAL_FUNCTION.type,
                      code: i.INVALID_USE_OF_INTERNAL_FUNCTION.code,
                      message: 'Options must be passed to basicComponentVerification function.',
                    })
                  );
            },
          };
        },
        { './braintree-error': 54, './errors': 62 },
      ],
      54: [
        function (e, t, r) {
          'use strict';
          e = e('./enumerate');
          function n(e) {
            if (!n.types.hasOwnProperty(e.type)) throw new Error(e.type + ' is not a valid type.');
            if (!e.code) throw new Error('Error code required.');
            if (!e.message) throw new Error('Error message required.');
            (this.name = 'BraintreeError'), (this.code = e.code), (this.message = e.message), (this.type = e.type), (this.details = e.details);
          }
          (((n.prototype = Object.create(Error.prototype)).constructor = n).types = e(['CUSTOMER', 'MERCHANT', 'NETWORK', 'INTERNAL', 'UNKNOWN'])),
            (n.findRootError = function (e) {
              return e instanceof n && e.details && e.details.originalError ? n.findRootError(e.details.originalError) : e;
            }),
            (t.exports = n);
        },
        { './enumerate': 61 },
      ],
      55: [
        function (e, t, r) {
          'use strict';
          var n = '3.100.0';
          t.exports = {
            ANALYTICS_PREFIX: 'web.',
            ANALYTICS_REQUEST_TIMEOUT_MS: 2e3,
            ASSETS_URLS: { production: 'https://assets.braintreegateway.com', sandbox: 'https://assets.braintreegateway.com' },
            CLIENT_API_URLS: { production: 'https://api.braintreegateway.com:443', sandbox: 'https://api.sandbox.braintreegateway.com:443' },
            FRAUDNET_SOURCE: 'BRAINTREE_SIGNIN',
            FRAUDNET_FNCLS: 'fnparams-dede7cc5-15fd-4c75-a9f4-36c430ee3a99',
            FRAUDNET_URL: 'https://c.paypal.com/da/r/fb.js',
            BUS_CONFIGURATION_REQUEST_EVENT: 'BUS_CONFIGURATION_REQUEST',
            GRAPHQL_URLS: { production: 'https://payments.braintree-api.com/graphql', sandbox: 'https://payments.sandbox.braintree-api.com/graphql' },
            INTEGRATION_TIMEOUT_MS: 6e4,
            VERSION: n,
            INTEGRATION: 'custom',
            SOURCE: 'client',
            PLATFORM: 'web',
            BRAINTREE_LIBRARY_VERSION: 'braintree/web/' + n,
          };
        },
        {},
      ],
      56: [
        function (e, t, r) {
          'use strict';
          var n = e('./braintree-error'),
            i = e('./errors');
          t.exports = function (t, e) {
            e.forEach(function (e) {
              t[e] = function () {
                throw new n({
                  type: i.METHOD_CALLED_AFTER_TEARDOWN.type,
                  code: i.METHOD_CALLED_AFTER_TEARDOWN.code,
                  message: e + ' cannot be called after teardown.',
                });
              };
            });
          };
        },
        { './braintree-error': 54, './errors': 62 },
      ],
      57: [
        function (e, t, r) {
          'use strict';
          var n = e('./braintree-error');
          t.exports = function (e, t) {
            return e instanceof n ? e : new n({ type: t.type, code: t.code, message: t.message, details: { originalError: e } });
          };
        },
        { './braintree-error': 54 },
      ],
      58: [
        function (e, t, r) {
          'use strict';
          var n = e('./constants').ASSETS_URLS;
          t.exports = {
            create: function (e) {
              return n.production;
            },
          };
        },
        { './constants': 55 },
      ],
      59: [
        function (e, t, r) {
          'use strict';
          var i = e('../lib/vendor/polyfill').atob,
            o = e('../lib/constants').CLIENT_API_URLS;
          t.exports = function (e) {
            var t,
              r,
              n = { attrs: {}, configUrl: '' };
            return (
              /^[a-zA-Z0-9]+_[a-zA-Z0-9]+_[a-zA-Z0-9_]+$/.test(e)
                ? ((r = (t = (t = e).split('_'))[0]),
                  (t = { merchantId: t.slice(2).join('_'), environment: r }),
                  (n.environment = t.environment),
                  (n.attrs.tokenizationKey = e),
                  (n.configUrl = o[t.environment] + '/merchants/' + t.merchantId + '/client_api/v1/configuration'))
                : ((r = JSON.parse(i(e))),
                  (n.environment = r.environment),
                  (n.attrs.authorizationFingerprint = r.authorizationFingerprint),
                  (n.configUrl = r.configUrl),
                  (n.graphQL = r.graphQL)),
              n
            );
          };
        },
        { '../lib/constants': 55, '../lib/vendor/polyfill': 78 },
      ],
      60: [
        function (e, t, r) {
          'use strict';
          var n = e('./braintree-error'),
            i = e('./assets'),
            o = e('./errors'),
            s = '3.100.0';
          t.exports = {
            create: function (e) {
              var t = Promise.resolve();
              return e.client
                ? Promise.resolve(e.client)
                : (t =
                    window.braintree && window.braintree.client
                      ? t
                      : i.loadScript({ src: e.assetsUrl + '/web/' + s + '/js/client.min.js' }).catch(function (e) {
                          return Promise.reject(
                            new n({
                              type: o.CLIENT_SCRIPT_FAILED_TO_LOAD.type,
                              code: o.CLIENT_SCRIPT_FAILED_TO_LOAD.code,
                              message: o.CLIENT_SCRIPT_FAILED_TO_LOAD.message,
                              details: { originalError: e },
                            })
                          );
                        })).then(function () {
                    return window.braintree.client.VERSION !== s
                      ? Promise.reject(
                          new n({
                            type: o.INCOMPATIBLE_VERSIONS.type,
                            code: o.INCOMPATIBLE_VERSIONS.code,
                            message:
                              'Client (version ' +
                              window.braintree.client.VERSION +
                              ') and ' +
                              e.name +
                              ' (version ' +
                              s +
                              ') components must be from the same SDK version.',
                          })
                        )
                      : window.braintree.client.create({ authorization: e.authorization, debug: e.debug });
                  });
            },
          };
        },
        { './assets': 51, './braintree-error': 54, './errors': 62 },
      ],
      61: [
        function (e, t, r) {
          'use strict';
          t.exports = function (e, r) {
            return (
              (r = null == r ? '' : r),
              e.reduce(function (e, t) {
                return (e[t] = r + t), e;
              }, {})
            );
          };
        },
        {},
      ],
      62: [
        function (e, t, r) {
          'use strict';
          e = e('./braintree-error');
          t.exports = {
            INVALID_USE_OF_INTERNAL_FUNCTION: { type: e.types.INTERNAL, code: 'INVALID_USE_OF_INTERNAL_FUNCTION' },
            INSTANTIATION_OPTION_REQUIRED: { type: e.types.MERCHANT, code: 'INSTANTIATION_OPTION_REQUIRED' },
            INCOMPATIBLE_VERSIONS: { type: e.types.MERCHANT, code: 'INCOMPATIBLE_VERSIONS' },
            CLIENT_SCRIPT_FAILED_TO_LOAD: {
              type: e.types.NETWORK,
              code: 'CLIENT_SCRIPT_FAILED_TO_LOAD',
              message: 'Braintree client script could not be loaded.',
            },
            METHOD_CALLED_AFTER_TEARDOWN: { type: e.types.MERCHANT, code: 'METHOD_CALLED_AFTER_TEARDOWN' },
          };
        },
        { './braintree-error': 54 },
      ],
      63: [
        function (e, t, r) {
          'use strict';
          var n = e('./strategies/popup'),
            i = e('./strategies/popup-bridge'),
            o = e('./strategies/modal'),
            s = e('framebus'),
            a = e('../shared/events'),
            c = e('../shared/errors'),
            u = e('../shared/constants'),
            p = e('@braintree/uuid'),
            d = e('@braintree/iframer'),
            l = e('../../braintree-error'),
            f = e('../shared/browser-detection'),
            h = e('./../../assign').assign,
            _ = e('../../constants').BUS_CONFIGURATION_REQUEST_EVENT,
            m = ['name', 'dispatchFrameUrl', 'openFrameUrl'];
          function y() {}
          function A(e) {
            var t = e;
            if (!t) throw new Error('Valid configuration is required');
            if (
              (m.forEach(function (e) {
                if (!t.hasOwnProperty(e)) throw new Error('A valid frame ' + e + ' must be provided');
              }),
              !/^[\w_]+$/.test(t.name))
            )
              throw new Error('A valid frame name must be provided');
            (this._serviceId = p().replace(/-/g, '')),
              (this._options = {
                name: e.name + '_' + this._serviceId,
                dispatchFrameUrl: e.dispatchFrameUrl,
                openFrameUrl: e.openFrameUrl,
                height: e.height,
                width: e.width,
                top: e.top,
                left: e.left,
              }),
              (this.state = e.state || {}),
              (this._bus = new s({ channel: this._serviceId })),
              this._setBusEvents();
          }
          (A.prototype.initialize = function (e) {
            var t = function () {
              e(), this._bus.off(a.DISPATCH_FRAME_READY, t);
            }.bind(this);
            this._bus.on(a.DISPATCH_FRAME_READY, t), this._writeDispatchFrame();
          }),
            (A.prototype._writeDispatchFrame = function () {
              var e = u.DISPATCH_FRAME_NAME + '_' + this._serviceId,
                t = this._options.dispatchFrameUrl;
              (this._dispatchFrame = d({
                'aria-hidden': !0,
                name: e,
                title: e,
                src: t,
                class: u.DISPATCH_FRAME_CLASS,
                height: 0,
                width: 0,
                style: { position: 'absolute', left: '-9999px' },
              })),
                document.body.appendChild(this._dispatchFrame);
            }),
            (A.prototype._setBusEvents = function () {
              this._bus.on(
                a.DISPATCH_FRAME_REPORT,
                function (e, t) {
                  this._onCompleteCallback && this._onCompleteCallback.call(null, e.err, e.payload),
                    this._frame.close(),
                    (this._onCompleteCallback = null),
                    t && t();
                }.bind(this)
              ),
                this._bus.on(
                  _,
                  function (e) {
                    e(this.state);
                  }.bind(this)
                );
            }),
            (A.prototype.open = function (e, t) {
              (this._frame = this._getFrameForEnvironment((e = e || {}))),
                this._frame.initialize(t),
                this._frame instanceof i ||
                  (h(this.state, e.state),
                  (this._onCompleteCallback = t),
                  this._frame.open(),
                  this.isFrameClosed() ? (this._cleanupFrame(), t && t(new l(c.FRAME_SERVICE_FRAME_OPEN_FAILED))) : this._pollForPopupClose());
            }),
            (A.prototype.redirect = function (e) {
              this._frame && !this.isFrameClosed() && this._frame.redirect(e);
            }),
            (A.prototype.close = function () {
              this.isFrameClosed() || this._frame.close();
            }),
            (A.prototype.focus = function () {
              this.isFrameClosed() || this._frame.focus();
            }),
            (A.prototype.createHandler = function (e) {
              return (
                (e = e || {}),
                {
                  close: function () {
                    e.beforeClose && e.beforeClose(), this.close();
                  }.bind(this),
                  focus: function () {
                    e.beforeFocus && e.beforeFocus(), this.focus();
                  }.bind(this),
                }
              );
            }),
            (A.prototype.createNoopHandler = function () {
              return { close: y, focus: y };
            }),
            (A.prototype.teardown = function () {
              this.close(), this._dispatchFrame.parentNode.removeChild(this._dispatchFrame), (this._dispatchFrame = null), this._cleanupFrame();
            }),
            (A.prototype.isFrameClosed = function () {
              return null == this._frame || this._frame.isClosed();
            }),
            (A.prototype._cleanupFrame = function () {
              (this._frame = null), clearInterval(this._popupInterval), (this._popupInterval = null);
            }),
            (A.prototype._pollForPopupClose = function () {
              return (
                (this._popupInterval = setInterval(
                  function () {
                    this.isFrameClosed() && (this._cleanupFrame(), this._onCompleteCallback && this._onCompleteCallback(new l(c.FRAME_SERVICE_FRAME_CLOSED)));
                  }.bind(this),
                  u.POPUP_POLL_INTERVAL
                )),
                this._popupInterval
              );
            }),
            (A.prototype._getFrameForEnvironment = function (e) {
              var t = f.supportsPopups(),
                r = Boolean(window.popupBridge),
                e = h({}, this._options, e);
              return new (r ? i : t ? n : o)(e);
            }),
            (t.exports = A);
        },
        {
          '../../braintree-error': 54,
          '../../constants': 55,
          '../shared/browser-detection': 70,
          '../shared/constants': 71,
          '../shared/errors': 72,
          '../shared/events': 73,
          './../../assign': 52,
          './strategies/modal': 65,
          './strategies/popup': 68,
          './strategies/popup-bridge': 66,
          '@braintree/iframer': 22,
          '@braintree/uuid': 26,
          framebus: 32,
        },
      ],
      64: [
        function (e, t, r) {
          'use strict';
          var n = e('./frame-service');
          t.exports = {
            create: function (e, t) {
              var r = new n(e);
              r.initialize(function () {
                t(r);
              });
            },
          };
        },
        { './frame-service': 63 },
      ],
      65: [
        function (e, t, r) {
          'use strict';
          var n = e('@braintree/iframer'),
            i = e('../../../assign').assign,
            o = e('../../shared/browser-detection'),
            s = { position: 'fixed', top: 0, left: 0, bottom: 0, padding: 0, margin: 0, border: 0, outline: 'none', zIndex: 20001, background: '#FFFFFF' };
          function a() {}
          function c(e) {
            (this._closed = null), (this._frame = null), (this._options = e || {}), (this._container = this._options.container || document.body);
          }
          (c.prototype.initialize = a),
            (c.prototype.open = function () {
              var e = {
                src: this._options.openFrameUrl,
                name: this._options.name,
                scrolling: 'yes',
                height: '100%',
                width: '100%',
                style: i({}, s),
                title: 'Lightbox Frame',
              };
              o.isIos()
                ? (o.isIosWKWebview() && (this._lockScrolling(), (e.style = {})),
                  (this._el = document.createElement('div')),
                  i(this._el.style, s, { height: '100%', width: '100%', overflow: 'auto', '-webkit-overflow-scrolling': 'touch' }),
                  (this._frame = n(e)),
                  this._el.appendChild(this._frame))
                : (this._el = this._frame = n(e)),
                (this._closed = !1),
                this._container.appendChild(this._el);
            }),
            (c.prototype.focus = a),
            (c.prototype.close = function () {
              this._container.removeChild(this._el), (this._frame = null), (this._closed = !0), o.isIosWKWebview() && this._unlockScrolling();
            }),
            (c.prototype.isClosed = function () {
              return Boolean(this._closed);
            }),
            (c.prototype.redirect = function (e) {
              this._frame.src = e;
            }),
            (c.prototype._unlockScrolling = function () {
              (document.body.style.overflow = this._savedBodyProperties.overflowStyle),
                (document.body.style.position = this._savedBodyProperties.positionStyle),
                window.scrollTo(this._savedBodyProperties.left, this._savedBodyProperties.top),
                delete this._savedBodyProperties;
            }),
            (c.prototype._lockScrolling = function () {
              var e = document.documentElement;
              (this._savedBodyProperties = {
                left: (window.pageXOffset || e.scrollLeft) - (e.clientLeft || 0),
                top: (window.pageYOffset || e.scrollTop) - (e.clientTop || 0),
                overflowStyle: document.body.style.overflow,
                positionStyle: document.body.style.position,
              }),
                (document.body.style.overflow = 'hidden'),
                (document.body.style.position = 'fixed'),
                window.scrollTo(0, 0);
            }),
            (t.exports = c);
        },
        { '../../../assign': 52, '../../shared/browser-detection': 70, '@braintree/iframer': 22 },
      ],
      66: [
        function (e, t, r) {
          'use strict';
          var o = e('../../../braintree-error'),
            s = e('../../shared/errors');
          function n() {}
          function i(e) {
            (this._closed = null), (this._options = e);
          }
          (i.prototype.initialize = function (n) {
            var i = this;
            window.popupBridge.onComplete = function (e, t) {
              var r = !t && !e;
              (i._closed = !0), e || r ? n(new o(s.FRAME_SERVICE_FRAME_CLOSED)) : n(null, t);
            };
          }),
            (i.prototype.open = function (e) {
              e = (e = e || {}).openFrameUrl || this._options.openFrameUrl;
              (this._closed = !1), window.popupBridge.open(e);
            }),
            (i.prototype.focus = n),
            (i.prototype.close = n),
            (i.prototype.isClosed = function () {
              return Boolean(this._closed);
            }),
            (i.prototype.redirect = function (e) {
              this.open({ openFrameUrl: e });
            }),
            (t.exports = i);
        },
        { '../../../braintree-error': 54, '../../shared/errors': 72 },
      ],
      67: [
        function (e, t, r) {
          'use strict';
          var i = e('../../../shared/constants'),
            n = e('./position');
          function o(e, t, r) {
            return void 0 !== t ? t : n[e](r);
          }
          t.exports = function (e) {
            var t = e.height || i.DEFAULT_POPUP_HEIGHT,
              r = e.width || i.DEFAULT_POPUP_WIDTH,
              n = o('top', e.top, t),
              e = o('left', e.left, r);
            return [i.POPUP_BASE_OPTIONS, 'height=' + t, 'width=' + r, 'top=' + n, 'left=' + e].join(',');
          };
        },
        { '../../../shared/constants': 71, './position': 69 },
      ],
      68: [
        function (e, t, r) {
          'use strict';
          var n = e('./compose-options');
          function i(e) {
            (this._frame = null), (this._options = e || {});
          }
          (i.prototype.initialize = function () {}),
            (i.prototype.open = function () {
              this._frame = window.open(this._options.openFrameUrl, this._options.name, n(this._options));
            }),
            (i.prototype.focus = function () {
              this._frame.focus();
            }),
            (i.prototype.close = function () {
              this._frame.closed || this._frame.close();
            }),
            (i.prototype.isClosed = function () {
              return !this._frame || Boolean(this._frame.closed);
            }),
            (i.prototype.redirect = function (e) {
              this._frame.location.href = e;
            }),
            (t.exports = i);
        },
        { './compose-options': 67 },
      ],
      69: [
        function (e, t, r) {
          'use strict';
          function n(e, t, r) {
            return (e - t) / 2 + r;
          }
          t.exports = {
            top: function (e) {
              return ((window.outerHeight || document.documentElement.clientHeight) - e) / 2 + (null == window.screenY ? window.screenTop : window.screenY);
            },
            left: function (e) {
              return ((window.outerWidth || document.documentElement.clientWidth) - e) / 2 + (null == window.screenX ? window.screenLeft : window.screenX);
            },
            center: n,
          };
        },
        {},
      ],
      70: [
        function (e, t, r) {
          'use strict';
          t.exports = {
            isIos: e('@braintree/browser-detection/is-ios'),
            isIosWKWebview: e('@braintree/browser-detection/is-ios-wkwebview'),
            supportsPopups: e('@braintree/browser-detection/supports-popups'),
          };
        },
        { '@braintree/browser-detection/is-ios': 19, '@braintree/browser-detection/is-ios-wkwebview': 18, '@braintree/browser-detection/supports-popups': 20 },
      ],
      71: [
        function (e, t, r) {
          'use strict';
          t.exports = {
            DISPATCH_FRAME_NAME: 'dispatch',
            DISPATCH_FRAME_CLASS: 'braintree-dispatch-frame',
            POPUP_BASE_OPTIONS: 'resizable,scrollbars',
            DEFAULT_POPUP_WIDTH: 450,
            DEFAULT_POPUP_HEIGHT: 535,
            POPUP_POLL_INTERVAL: 100,
            POPUP_CLOSE_TIMEOUT: 100,
          };
        },
        {},
      ],
      72: [
        function (e, t, r) {
          'use strict';
          e = e('../../braintree-error');
          t.exports = {
            FRAME_SERVICE_FRAME_CLOSED: {
              type: e.types.INTERNAL,
              code: 'FRAME_SERVICE_FRAME_CLOSED',
              message: 'Frame closed before tokenization could occur.',
            },
            FRAME_SERVICE_FRAME_OPEN_FAILED: { type: e.types.INTERNAL, code: 'FRAME_SERVICE_FRAME_OPEN_FAILED', message: 'Frame failed to open.' },
          };
        },
        { '../../braintree-error': 54 },
      ],
      73: [
        function (e, t, r) {
          'use strict';
          e = e('../../enumerate');
          t.exports = e(['DISPATCH_FRAME_READY', 'DISPATCH_FRAME_REPORT'], 'frameService:');
        },
        { '../../enumerate': 61 },
      ],
      74: [
        function (e, t, r) {
          'use strict';
          t.exports = function (e) {
            return JSON.parse(JSON.stringify(e));
          };
        },
        {},
      ],
      75: [
        function (e, t, r) {
          'use strict';
          t.exports = function (t) {
            return Object.keys(t).filter(function (e) {
              return 'function' == typeof t[e];
            });
          };
        },
        {},
      ],
      76: [
        function (e, t, r) {
          'use strict';
          function n(e) {
            return (e = e || window.location.href), /\?/.test(e);
          }
          function s(e, t) {
            var r,
              n,
              i,
              o = [];
            for (n in e)
              e.hasOwnProperty(n) &&
                ((r = e[n]),
                (i = t
                  ? (i = e) && 'object' == typeof i && 'number' == typeof i.length && '[object Array]' === Object.prototype.toString.call(i)
                    ? t + '[]'
                    : t + '[' + n + ']'
                  : n),
                o.push('object' == typeof r ? s(r, i) : encodeURIComponent(i) + '=' + encodeURIComponent(r)));
            return o.join('&');
          }
          t.exports = {
            parse: function (e) {
              return n((e = e || window.location.href))
                ? (e.split('?')[1] || '')
                    .replace(/#.*$/, '')
                    .split('&')
                    .reduce(function (e, t) {
                      var t = t.split('='),
                        r = decodeURIComponent(t[0]),
                        t = decodeURIComponent(t[1]);
                      return (e[r] = t), e;
                    }, {})
                : {};
            },
            stringify: s,
            queryify: function (e, t) {
              return (
                (e = e || ''),
                (e =
                  null != t &&
                  'object' == typeof t &&
                  (function (e) {
                    for (var t in e) if (e.hasOwnProperty(t)) return 1;
                  })(t)
                    ? (e = (e += -1 === e.indexOf('?') ? '?' : '') + (-1 !== e.indexOf('=') ? '&' : '')) + s(t)
                    : e)
              );
            },
            hasQueryParams: n,
          };
        },
        {},
      ],
      77: [
        function (e, t, r) {
          'use strict';
          t.exports = function (e) {
            return e ? '' : '.min';
          };
        },
        {},
      ],
      78: [
        function (e, t, r) {
          'use strict';
          var n = 'function' == typeof atob ? atob : i;
          function i(e) {
            var t,
              r,
              n,
              i,
              o,
              s = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
              a = '';
            if (!new RegExp('^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{4})([=]{1,2})?$').test(e))
              throw new Error('Non base64 encoded input passed to window.atob polyfill');
            for (
              o = 0;
              (r = s.indexOf(e.charAt(o++))),
                (t = ((15 & (n = s.indexOf(e.charAt(o++)))) << 4) | (((i = s.indexOf(e.charAt(o++))) >> 2) & 15)),
                (i = ((3 & i) << 6) | (63 & s.indexOf(e.charAt(o++)))),
                (a += String.fromCharCode(((63 & r) << 2) | ((n >> 4) & 3)) + (t ? String.fromCharCode(t) : '') + (i ? String.fromCharCode(i) : '')),
                o < e.length;

            );
            return a;
          }
          t.exports = {
            atob: function (e) {
              return n.call(window, e);
            },
            _atob: i,
          };
        },
        {},
      ],
      79: [
        function (e, t, r) {
          'use strict';
          e = e('../lib/braintree-error');
          t.exports = {
            PAYPAL_NOT_ENABLED: { type: e.types.MERCHANT, code: 'PAYPAL_NOT_ENABLED', message: 'PayPal is not enabled for this merchant.' },
            PAYPAL_SANDBOX_ACCOUNT_NOT_LINKED: {
              type: e.types.MERCHANT,
              code: 'PAYPAL_SANDBOX_ACCOUNT_NOT_LINKED',
              message:
                'A linked PayPal Sandbox account is required to use PayPal Checkout in Sandbox. See https://developer.paypal.com/braintree/docs/guides/paypal/testing-go-live#linked-paypal-testing for details on linking your PayPal sandbox with Braintree.',
            },
            PAYPAL_ACCOUNT_TOKENIZATION_FAILED: {
              type: e.types.NETWORK,
              code: 'PAYPAL_ACCOUNT_TOKENIZATION_FAILED',
              message: "Could not tokenize user's PayPal account.",
            },
            PAYPAL_FLOW_FAILED: { type: e.types.NETWORK, code: 'PAYPAL_FLOW_FAILED', message: 'Could not initialize PayPal flow.' },
            PAYPAL_FLOW_OPTION_REQUIRED: {
              type: e.types.MERCHANT,
              code: 'PAYPAL_FLOW_OPTION_REQUIRED',
              message: 'PayPal flow property is invalid or missing.',
            },
            PAYPAL_START_VAULT_INITIATED_CHECKOUT_PARAM_REQUIRED: { type: e.types.MERCHANT, code: 'PAYPAL_START_VAULT_INITIATED_CHECKOUT_PARAM_REQUIRED' },
            PAYPAL_START_VAULT_INITIATED_CHECKOUT_SETUP_FAILED: {
              type: e.types.NETWORK,
              code: 'PAYPAL_START_VAULT_INITIATED_CHECKOUT_SETUP_FAILED',
              message: 'Something went wrong when setting up the checkout workflow.',
            },
            PAYPAL_START_VAULT_INITIATED_CHECKOUT_POPUP_OPEN_FAILED: {
              type: e.types.MERCHANT,
              code: 'PAYPAL_START_VAULT_INITIATED_CHECKOUT_POPUP_OPEN_FAILED',
              message: 'PayPal popup failed to open, make sure to initiate the vault checkout in response to a user action.',
            },
            PAYPAL_START_VAULT_INITIATED_CHECKOUT_CANCELED: {
              type: e.types.CUSTOMER,
              code: 'PAYPAL_START_VAULT_INITIATED_CHECKOUT_CANCELED',
              message: 'Customer closed PayPal popup before authorizing.',
            },
            PAYPAL_START_VAULT_INITIATED_CHECKOUT_IN_PROGRESS: {
              type: e.types.MERCHANT,
              code: 'PAYPAL_START_VAULT_INITIATED_CHECKOUT_IN_PROGRESS',
              message: 'Vault initiated checkout already in progress.',
            },
            PAYPAL_INVALID_PAYMENT_OPTION: { type: e.types.MERCHANT, code: 'PAYPAL_INVALID_PAYMENT_OPTION', message: 'PayPal payment options are invalid.' },
            PAYPAL_MISSING_REQUIRED_OPTION: { type: e.types.MERCHANT, code: 'PAYPAL_MISSING_REQUIRED_OPTION', message: 'Missing required option.' },
          };
        },
        { '../lib/braintree-error': 54 },
      ],
      80: [
        function (e, t, r) {
          'use strict';
          var n = e('../lib/basic-component-verification'),
            i = e('@braintree/wrap-promise'),
            o = e('./paypal-checkout');
          t.exports = {
            create: i(function (e) {
              return n.verify({ name: 'PayPal Checkout', client: e.client, authorization: e.authorization }).then(function () {
                return new o(e)._initialize(e);
              });
            }),
            isSupported: function () {
              return !0;
            },
            VERSION: '3.100.0',
          };
        },
        { '../lib/basic-component-verification': 53, './paypal-checkout': 81, '@braintree/wrap-promise': 30 },
      ],
      81: [
        function (e, t, r) {
          'use strict';
          var s = e('../lib/analytics'),
            i = e('../lib/assign').assign,
            n = e('../lib/create-deferred-client'),
            o = e('../lib/create-assets-url'),
            a = e('@braintree/extended-promise'),
            c = e('@braintree/wrap-promise'),
            u = e('../lib/braintree-error'),
            p = e('../lib/convert-to-braintree-error'),
            d = e('./errors'),
            l = e('../paypal/shared/constants'),
            f = e('../lib/frame-service/external'),
            h = e('../lib/create-authorization-data'),
            _ = e('../lib/methods'),
            m = e('../lib/use-min'),
            y = e('../lib/convert-methods-to-error'),
            A = e('../lib/querystring'),
            P = e('../lib/constants').INTEGRATION_TIMEOUT_MS,
            v = ['amount', 'currency', 'vaultInitiatedCheckoutPaymentMethodToken'];
          function g(e) {
            (this._merchantAccountId = e.merchantAccountId), (this._autoSetDataUserIdToken = Boolean(e.autoSetDataUserIdToken));
          }
          (a.suppressUnhandledPromiseMessage = !0),
            (g.prototype._initialize = function (e) {
              var t;
              return (
                e.client
                  ? ((t = e.client.getConfiguration()),
                    (this._authorizationInformation = { fingerprint: t.authorizationFingerprint, environment: t.gatewayConfiguration.environment }))
                  : ((t = h(e.authorization)),
                    (this._authorizationInformation = { fingerprint: t.attrs.authorizationFingerprint, environment: t.environment })),
                (this._clientPromise = n
                  .create({ authorization: e.authorization, client: e.client, debug: e.debug, assetsUrl: o.create(e.authorization), name: 'PayPal Checkout' })
                  .then(
                    function (e) {
                      return (
                        (this._configuration = e.getConfiguration()),
                        this._merchantAccountId ||
                          (this._configuration.gatewayConfiguration.paypalEnabled
                            ? !0 === this._configuration.gatewayConfiguration.paypal.environmentNoNetwork &&
                              (this._setupError = new u(d.PAYPAL_SANDBOX_ACCOUNT_NOT_LINKED))
                            : (this._setupError = new u(d.PAYPAL_NOT_ENABLED))),
                        this._setupError
                          ? Promise.reject(this._setupError)
                          : (s.sendEvent(e, 'paypal-checkout.initialized'), (this._frameServicePromise = this._setupFrameService(e)), e)
                      );
                    }.bind(this)
                  )),
                e.client
                  ? this._clientPromise.then(
                      function () {
                        return this;
                      }.bind(this)
                    )
                  : Promise.resolve(this)
              );
            }),
            (g.prototype._setupFrameService = function (e) {
              var t = new a(),
                r = e.getConfiguration(),
                n = setTimeout(function () {
                  s.sendEvent(e, 'paypal-checkout.frame-service.timed-out'), t.reject(new u(d.PAYPAL_START_VAULT_INITIATED_CHECKOUT_SETUP_FAILED));
                }, P);
              return (
                (this._assetsUrl = r.gatewayConfiguration.paypal.assetsUrl + '/web/3.100.0'),
                (this._isDebug = r.isDebug),
                (this._loadingFrameUrl = this._assetsUrl + '/html/paypal-landing-frame' + m(this._isDebug) + '.html'),
                f.create(
                  {
                    name: 'braintreepaypallanding',
                    dispatchFrameUrl: this._assetsUrl + '/html/dispatch-frame' + m(this._isDebug) + '.html',
                    openFrameUrl: this._loadingFrameUrl,
                  },
                  function (e) {
                    (this._frameService = e), clearTimeout(n), t.resolve();
                  }.bind(this)
                ),
                t
              );
            }),
            (g.prototype.createPayment = function (t) {
              return t && l.FLOW_ENDPOINTS.hasOwnProperty(t.flow)
                ? (s.sendEvent(this._clientPromise, 'paypal-checkout.createPayment'),
                  this._createPaymentResource(t).then(function (e) {
                    e = 'checkout' === t.flow ? A.parse(e.paymentResource.redirectUrl).token : e.agreementSetup.tokenId;
                    return e;
                  }))
                : Promise.reject(new u(d.PAYPAL_FLOW_OPTION_REQUIRED));
            }),
            (g.prototype._createPaymentResource = function (t, r) {
              var n = this,
                i = 'paypal_hermes/' + l.FLOW_ENDPOINTS[t.flow];
              return (
                delete this.intentFromCreatePayment,
                (r = r || {}),
                !0 === t.offerCredit && s.sendEvent(this._clientPromise, 'paypal-checkout.credit.offered'),
                this._clientPromise
                  .then(function (e) {
                    return e.request({ endpoint: i, method: 'post', data: n._formatPaymentResourceData(t, r) }).then(function (e) {
                      return (n.intentFromCreatePayment = t.intent), e;
                    });
                  })
                  .catch(function (e) {
                    return n._setupError
                      ? Promise.reject(n._setupError)
                      : 422 === (e.details && e.details.httpStatus)
                        ? Promise.reject(
                            new u({
                              type: d.PAYPAL_INVALID_PAYMENT_OPTION.type,
                              code: d.PAYPAL_INVALID_PAYMENT_OPTION.code,
                              message: d.PAYPAL_INVALID_PAYMENT_OPTION.message,
                              details: { originalError: e },
                            })
                          )
                        : Promise.reject(p(e, { type: d.PAYPAL_FLOW_FAILED.type, code: d.PAYPAL_FLOW_FAILED.code, message: d.PAYPAL_FLOW_FAILED.message }));
                  })
              );
            }),
            (g.prototype.updatePayment = function (t) {
              var r = this;
              return !t || this._hasMissingOption(t, l.REQUIRED_OPTIONS)
                ? (s.sendEvent(r._clientPromise, 'paypal-checkout.updatePayment.missing-options'), Promise.reject(new u(d.PAYPAL_MISSING_REQUIRED_OPTION)))
                : this._verifyConsistentCurrency(t)
                  ? (s.sendEvent(this._clientPromise, 'paypal-checkout.updatePayment'),
                    this._clientPromise
                      .then(function (e) {
                        return e.request({ endpoint: 'paypal_hermes/patch_payment_resource', method: 'post', data: r._formatUpdatePaymentData(t) });
                      })
                      .catch(function (e) {
                        return 422 === (e.details && e.details.httpStatus)
                          ? (s.sendEvent(r._clientPromise, 'paypal-checkout.updatePayment.invalid'),
                            Promise.reject(
                              new u({
                                type: d.PAYPAL_INVALID_PAYMENT_OPTION.type,
                                code: d.PAYPAL_INVALID_PAYMENT_OPTION.code,
                                message: d.PAYPAL_INVALID_PAYMENT_OPTION.message,
                                details: { originalError: e },
                              })
                            ))
                          : (s.sendEvent(r._clientPromise, 'paypal-checkout.updatePayment.' + d.PAYPAL_FLOW_FAILED.code),
                            Promise.reject(p(e, { type: d.PAYPAL_FLOW_FAILED.type, code: d.PAYPAL_FLOW_FAILED.code, message: d.PAYPAL_FLOW_FAILED.message })));
                      }))
                  : (s.sendEvent(r._clientPromise, 'paypal-checkout.updatePayment.inconsistent-currencies'),
                    Promise.reject(
                      new u({
                        type: d.PAYPAL_INVALID_PAYMENT_OPTION.type,
                        code: d.PAYPAL_INVALID_PAYMENT_OPTION.code,
                        message: d.PAYPAL_INVALID_PAYMENT_OPTION.message,
                        details: { originalError: new Error('One or more shipping option currencies differ from checkout currency.') },
                      })
                    ));
            }),
            (g.prototype.startVaultInitiatedCheckout = function (r) {
              var t,
                n = this;
              return this._vaultInitiatedCheckoutInProgress
                ? (s.sendEvent(this._clientPromise, 'paypal-checkout.startVaultInitiatedCheckout.error.already-in-progress'),
                  Promise.reject(new u(d.PAYPAL_START_VAULT_INITIATED_CHECKOUT_IN_PROGRESS)))
                : (v.forEach(function (e) {
                    r.hasOwnProperty(e) || (t = e);
                  }),
                  t
                    ? Promise.reject(
                        new u({
                          type: d.PAYPAL_START_VAULT_INITIATED_CHECKOUT_PARAM_REQUIRED.type,
                          code: d.PAYPAL_START_VAULT_INITIATED_CHECKOUT_PARAM_REQUIRED.code,
                          message: 'Required param ' + t + ' is missing.',
                        })
                      )
                    : ((this._vaultInitiatedCheckoutInProgress = !0),
                      this._addModalBackdrop(r),
                      (r = i({}, r, { flow: 'checkout' })),
                      s.sendEvent(this._clientPromise, 'paypal-checkout.startVaultInitiatedCheckout.started'),
                      this._waitForVaultInitiatedCheckoutDependencies()
                        .then(function () {
                          var t = new a(),
                            e = n
                              ._createPaymentResource(r, {
                                returnUrl: n._constructVaultCheckutUrl('redirect-frame'),
                                cancelUrl: n._constructVaultCheckutUrl('cancel-frame'),
                              })
                              .then(function (e) {
                                e = e.paymentResource.redirectUrl;
                                return n._frameService.redirect(e), t;
                              });
                          return n._frameService.open({}, n._createFrameServiceCallback(t)), e;
                        })
                        .catch(function (e) {
                          return (
                            (n._vaultInitiatedCheckoutInProgress = !1),
                            n._removeModalBackdrop(),
                            'FRAME_SERVICE_FRAME_CLOSED' === e.code
                              ? (s.sendEvent(n._clientPromise, 'paypal-checkout.startVaultInitiatedCheckout.canceled.by-customer'),
                                Promise.reject(new u(d.PAYPAL_START_VAULT_INITIATED_CHECKOUT_CANCELED)))
                              : (n._frameService && n._frameService.close(),
                                e.code && -1 < e.code.indexOf('FRAME_SERVICE_FRAME_OPEN_FAILED')
                                  ? (s.sendEvent(n._clientPromise, 'paypal-checkout.startVaultInitiatedCheckout.failed.popup-not-opened'),
                                    Promise.reject(
                                      new u({
                                        code: d.PAYPAL_START_VAULT_INITIATED_CHECKOUT_POPUP_OPEN_FAILED.code,
                                        type: d.PAYPAL_START_VAULT_INITIATED_CHECKOUT_POPUP_OPEN_FAILED.type,
                                        message: d.PAYPAL_START_VAULT_INITIATED_CHECKOUT_POPUP_OPEN_FAILED.message,
                                        details: { originalError: e },
                                      })
                                    ))
                                  : Promise.reject(e))
                          );
                        })
                        .then(function (e) {
                          return (
                            n._frameService.close(),
                            (n._vaultInitiatedCheckoutInProgress = !1),
                            n._removeModalBackdrop(),
                            s.sendEvent(n._clientPromise, 'paypal-checkout.startVaultInitiatedCheckout.succeeded'),
                            Promise.resolve(e)
                          );
                        })));
            }),
            (g.prototype._addModalBackdrop = function (e) {
              e.optOutOfModalBackdrop ||
                (this._modalBackdrop ||
                  ((this._modalBackdrop = document.createElement('div')),
                  this._modalBackdrop.setAttribute('data-braintree-paypal-vault-initiated-checkout-modal', !0),
                  (this._modalBackdrop.style.position = 'fixed'),
                  (this._modalBackdrop.style.top = 0),
                  (this._modalBackdrop.style.bottom = 0),
                  (this._modalBackdrop.style.left = 0),
                  (this._modalBackdrop.style.right = 0),
                  (this._modalBackdrop.style.zIndex = 9999),
                  (this._modalBackdrop.style.background = 'black'),
                  (this._modalBackdrop.style.opacity = '0.7'),
                  this._modalBackdrop.addEventListener(
                    'click',
                    function () {
                      this.focusVaultInitiatedCheckoutWindow();
                    }.bind(this)
                  )),
                document.body.appendChild(this._modalBackdrop));
            }),
            (g.prototype._removeModalBackdrop = function () {
              this._modalBackdrop && this._modalBackdrop.parentNode && this._modalBackdrop.parentNode.removeChild(this._modalBackdrop);
            }),
            (g.prototype.closeVaultInitiatedCheckoutWindow = function () {
              return (
                this._vaultInitiatedCheckoutInProgress && s.sendEvent(this._clientPromise, 'paypal-checkout.startVaultInitiatedCheckout.canceled.by-merchant'),
                this._waitForVaultInitiatedCheckoutDependencies().then(
                  function () {
                    this._frameService.close();
                  }.bind(this)
                )
              );
            }),
            (g.prototype.focusVaultInitiatedCheckoutWindow = function () {
              return this._waitForVaultInitiatedCheckoutDependencies().then(
                function () {
                  this._frameService.focus();
                }.bind(this)
              );
            }),
            (g.prototype._createFrameServiceCallback = function (r) {
              var n = this;
              return function (e, t) {
                e
                  ? r.reject(e)
                  : t &&
                    (n._frameService.redirect(n._loadingFrameUrl),
                    n
                      .tokenizePayment({ paymentToken: t.token, payerID: t.PayerID, paymentID: t.paymentId, orderID: t.orderId })
                      .then(function (e) {
                        r.resolve(e);
                      })
                      .catch(function (e) {
                        r.reject(e);
                      }));
              };
            }),
            (g.prototype._waitForVaultInitiatedCheckoutDependencies = function () {
              var e = this;
              return this._clientPromise.then(function () {
                return e._frameServicePromise;
              });
            }),
            (g.prototype._constructVaultCheckutUrl = function (e) {
              var t = this._frameService._serviceId;
              return this._assetsUrl + '/html/' + e + m(this._isDebug) + '.html?channel=' + t;
            }),
            (g.prototype.tokenizePayment = function (e) {
              var t,
                r = this,
                n = !0,
                i = { flow: e.billingToken && !e.paymentID ? 'vault' : 'checkout', intent: e.intent || this.intentFromCreatePayment },
                o = {
                  ecToken: e.paymentToken,
                  billingToken: e.billingToken,
                  payerId: e.payerID,
                  paymentId: e.paymentID,
                  orderId: e.orderID,
                  shippingOptionsId: e.shippingOptionsId,
                };
              return (
                e.hasOwnProperty('vault') && (n = e.vault),
                (i.vault = n),
                s.sendEvent(this._clientPromise, 'paypal-checkout.tokenization.started'),
                this._clientPromise
                  .then(function (e) {
                    return e.request({ endpoint: 'payment_methods/paypal_accounts', method: 'post', data: r._formatTokenizeData(i, o) });
                  })
                  .then(function (e) {
                    return (
                      (t = r._formatTokenizePayload(e)),
                      s.sendEvent(r._clientPromise, 'paypal-checkout.tokenization.success'),
                      t.creditFinancingOffered && s.sendEvent(r._clientPromise, 'paypal-checkout.credit.accepted'),
                      t
                    );
                  })
                  .catch(function (e) {
                    return r._setupError
                      ? Promise.reject(r._setupError)
                      : (s.sendEvent(r._clientPromise, 'paypal-checkout.tokenization.failed'),
                        Promise.reject(
                          p(e, {
                            type: d.PAYPAL_ACCOUNT_TOKENIZATION_FAILED.type,
                            code: d.PAYPAL_ACCOUNT_TOKENIZATION_FAILED.code,
                            message: d.PAYPAL_ACCOUNT_TOKENIZATION_FAILED.message,
                          })
                        ));
                  })
              );
            }),
            (g.prototype.getClientId = function () {
              return this._clientPromise.then(function (e) {
                return e.getConfiguration().gatewayConfiguration.paypal.clientId;
              });
            }),
            (g.prototype.loadPayPalSDK = function (t) {
              var e = new a(),
                r = (t && t.dataAttributes) || {},
                n =
                  (n = r['user-id-token'] || r['data-user-id-token']) ||
                  (this._authorizationInformation.fingerprint && this._authorizationInformation.fingerprint.split('?')[0]);
              return (
                (this._paypalScript = document.createElement('script')),
                delete (t = i({}, { components: 'buttons' }, t)).dataAttributes,
                t.vault ? (t.intent = t.intent || 'tokenize') : ((t.intent = t.intent || 'authorize'), (t.currency = t.currency || 'USD')),
                (this._paypalScript.onload = function () {
                  e.resolve();
                }),
                Object.keys(r).forEach(
                  function (e) {
                    this._paypalScript.setAttribute('data-' + e.replace(/^data\-/, ''), r[e]);
                  }.bind(this)
                ),
                (t['client-id'] ? Promise.resolve(t['client-id']) : this.getClientId()).then(
                  function (e) {
                    (t['client-id'] = e),
                      this._autoSetDataUserIdToken &&
                        n &&
                        (this._paypalScript.setAttribute('data-user-id-token', n),
                        this._attachPreloadPixel({ id: e, userIdToken: n, amount: r.amount, currency: t.currency, merchantId: t['merchant-id'] })),
                      (this._paypalScript.src = A.queryify('https://www.paypal.com/sdk/js?', t)),
                      document.head.insertBefore(this._paypalScript, document.head.firstElementChild);
                  }.bind(this)
                ),
                e.then(
                  function () {
                    return this;
                  }.bind(this)
                )
              );
            }),
            (g.prototype._attachPreloadPixel = function (e) {
              var t = e.id,
                r = e.userIdToken,
                n = this._authorizationInformation.environment,
                n = 'https://www.{ENV}paypal.com/smart/buttons/preload'.replace('{ENV}', 'production' === n ? '' : 'sandbox.'),
                t = { 'client-id': t, 'user-id-token': r };
              e.amount && (t.amount = e.amount),
                e.currency && (t.currency = e.currency),
                e.merchantId && (t['merchant-id'] = e.merchantId),
                (r = new XMLHttpRequest()).open('GET', A.queryify(n, t)),
                r.send();
            }),
            (g.prototype._formatPaymentResourceData = function (e, t) {
              var r,
                n = this._configuration.gatewayConfiguration,
                i = e.intent,
                o = {
                  returnUrl: t.returnUrl || 'https://www.paypal.com/checkoutnow/error',
                  cancelUrl: t.cancelUrl || 'https://www.paypal.com/checkoutnow/error',
                  offerPaypalCredit: !0 === e.offerCredit,
                  merchantAccountId: this._merchantAccountId,
                  experienceProfile: {
                    brandName: e.displayName || n.paypal.displayName,
                    localeCode: e.locale,
                    noShipping: (!e.enableShippingAddress).toString(),
                    addressOverride: !1 === e.shippingAddressEditable,
                    landingPageType: e.landingPageType,
                  },
                  shippingOptions: e.shippingOptions,
                };
              if ('checkout' === e.flow) {
                for (r in ((o.amount = e.amount),
                (o.currencyIsoCode = e.currency),
                (o.requestBillingAgreement = e.requestBillingAgreement),
                i && (o.intent = i = 'capture' === i ? 'sale' : i),
                e.hasOwnProperty('lineItems') && (o.lineItems = e.lineItems),
                e.hasOwnProperty('vaultInitiatedCheckoutPaymentMethodToken') &&
                  (o.vaultInitiatedCheckoutPaymentMethodToken = e.vaultInitiatedCheckoutPaymentMethodToken),
                e.hasOwnProperty('shippingOptions') && (o.shippingOptions = e.shippingOptions),
                e.shippingAddressOverride))
                  e.shippingAddressOverride.hasOwnProperty(r) && (o[r] = e.shippingAddressOverride[r]);
                e.hasOwnProperty('billingAgreementDetails') && (o.billingAgreementDetails = e.billingAgreementDetails);
              } else (o.shippingAddress = e.shippingAddressOverride), e.billingAgreementDescription && (o.description = e.billingAgreementDescription);
              return (this._riskCorrelationId = e.riskCorrelationId), e.riskCorrelationId && (o.correlationId = this._riskCorrelationId), o;
            }),
            (g.prototype._verifyConsistentCurrency = function (t) {
              return (
                !(t.currency && t.hasOwnProperty('shippingOptions') && Array.isArray(t.shippingOptions)) ||
                t.shippingOptions.every(function (e) {
                  return e.amount && e.amount.currency && t.currency.toLowerCase() === e.amount.currency.toLowerCase();
                })
              );
            }),
            (g.prototype._hasMissingOption = function (e, t) {
              var r, n;
              if (((t = t || []), !e.hasOwnProperty('amount') && !e.hasOwnProperty('lineItems'))) return !0;
              for (r = 0; r < t.length; r++) if (((n = t[r]), !e.hasOwnProperty(n))) return !0;
              return !1;
            }),
            (g.prototype._formatUpdatePaymentData = function (e) {
              var t = { merchantAccountId: this._merchantAccountId, paymentId: e.paymentId || e.orderId, currencyIsoCode: e.currency };
              return (
                e.hasOwnProperty('amount') && (t.amount = e.amount),
                e.hasOwnProperty('lineItems') && (t.lineItems = e.lineItems),
                e.hasOwnProperty('shippingOptions') && (t.shippingOptions = e.shippingOptions),
                e.hasOwnProperty('shippingAddress') &&
                  (s.sendEvent(this._clientPromise, 'paypal-checkout.updatePayment.shippingAddress.provided.by-the-merchant'),
                  (t.line1 = e.shippingAddress.line1),
                  e.shippingAddress.hasOwnProperty('line2') && (t.line2 = e.shippingAddress.line2),
                  (t.city = e.shippingAddress.city),
                  (t.state = e.shippingAddress.state),
                  (t.postalCode = e.shippingAddress.postalCode),
                  (t.countryCode = e.shippingAddress.countryCode),
                  e.shippingAddress.hasOwnProperty('phone') && (t.phone = e.shippingAddress.phone),
                  e.shippingAddress.hasOwnProperty('recipientName') && (t.recipientName = e.shippingAddress.recipientName)),
                t
              );
            }),
            (g.prototype._formatTokenizeData = function (e, t) {
              var r = this._configuration,
                n = r.gatewayConfiguration,
                r = 'TOKENIZATION_KEY' === r.authorizationType,
                i = 'vault' === e.flow,
                r = { paypalAccount: { correlationId: this._riskCorrelationId || t.billingToken || t.ecToken, options: { validate: i && !r && e.vault } } };
              return (
                i
                  ? (r.paypalAccount.billingAgreementToken = t.billingToken)
                  : ((r.paypalAccount.paymentToken = t.paymentId || t.orderId),
                    (r.paypalAccount.payerId = t.payerId),
                    (r.paypalAccount.unilateral = n.paypal.unvettedMerchant),
                    e.intent && (r.paypalAccount.intent = e.intent)),
                this._merchantAccountId && (r.merchantAccountId = this._merchantAccountId),
                r
              );
            }),
            (g.prototype._formatTokenizePayload = function (e) {
              var t = {},
                e = { nonce: (t = e.paypalAccounts ? e.paypalAccounts[0] : t).nonce, details: {}, type: t.type };
              return (
                t.details && t.details.payerInfo && (e.details = t.details.payerInfo),
                t.details && t.details.creditFinancingOffered && (e.creditFinancingOffered = t.details.creditFinancingOffered),
                t.details && t.details.shippingOptionId && (e.shippingOptionId = t.details.shippingOptionId),
                t.details && t.details.cobrandedCardLabel && (e.cobrandedCardLabel = t.details.cobrandedCardLabel),
                e
              );
            }),
            (g.prototype.teardown = function () {
              var e = this;
              return (
                y(this, _(g.prototype)),
                this._paypalScript && this._paypalScript.parentNode && this._paypalScript.parentNode.removeChild(this._paypalScript),
                this._frameServicePromise
                  .catch(function () {})
                  .then(function () {
                    return e._frameService ? e._frameService.teardown() : Promise.resolve();
                  })
              );
            }),
            (t.exports = c.wrapPrototype(g));
        },
        {
          '../lib/analytics': 50,
          '../lib/assign': 52,
          '../lib/braintree-error': 54,
          '../lib/constants': 55,
          '../lib/convert-methods-to-error': 56,
          '../lib/convert-to-braintree-error': 57,
          '../lib/create-assets-url': 58,
          '../lib/create-authorization-data': 59,
          '../lib/create-deferred-client': 60,
          '../lib/frame-service/external': 64,
          '../lib/methods': 75,
          '../lib/querystring': 76,
          '../lib/use-min': 77,
          '../paypal/shared/constants': 82,
          './errors': 79,
          '@braintree/extended-promise': 21,
          '@braintree/wrap-promise': 30,
        },
      ],
      82: [
        function (e, t, r) {
          'use strict';
          t.exports = {
            LANDING_FRAME_NAME: 'braintreepaypallanding',
            FLOW_ENDPOINTS: { checkout: 'create_payment_resource', vault: 'setup_billing_agreement' },
            REQUIRED_OPTIONS: ['paymentId', 'currency'],
          };
        },
        {},
      ],
    },
    {},
    [80]
  )(80);
});
