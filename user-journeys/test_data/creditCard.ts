const _ = require('lodash');
//TODO: there were some cards for specific countries on this page so we might want to change the "cards" data structure to include "us", "ca", etc.
// https://authorvinod.medium.com/fake-credit-cards-for-testing-dd463a5ce99c

const cards = {
  cc: [
    '****************',
    '****************',
    '****************',
    '****************',
    '****************',
    '****************',
    '****************',
    '****************',
    '2222420000001113',
    '2223000048410010',
    '2222405343248877',
    '2222990905257051',
    '2223007648726984',
    '2223577120017656',
    '****************',
  ],
  amex: ['***************', '***************', '***************', '***************'],
};

export function getRandomCard() {
  const cardNumber = _.sample(cards.cc);
  const cc = new CreditCard(cardNumber);
  return cc;
}

class CreditCard {
  number = null;
  expiry = '08/26';
  cvv = '999';

  constructor(number: null) {
    this.number = number;
  }
}
