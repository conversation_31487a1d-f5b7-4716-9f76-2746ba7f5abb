import test from '../pom/base.page';
import { getFullBrand } from '../utils/helpers';

const { BRAND } = process.env;

export type TestOptions = {
  bopisTitle: string | RegExp;
  buyGiftCardsTitle: string | RegExp;
  changeOrCancelTitle: string | RegExp;
  chatOrCallTitle: string | RegExp;
  customerServiceCid: number;
  descriptionMetaChangeCancel: string | RegExp;
  descriptionMetaChatOrCall: string | RegExp;
  descriptionMetaManageAccount: string | RegExp;
  descriptionMetaOrderStatus: string | RegExp;
  descriptionMetaPriceAdjustments: string | RegExp;
  giftCardsTitle: string | RegExp;
  manageAccountTitle: string | RegExp;
  orderStatusTitle: string | RegExp;
  paymentOptionsTitle: string | RegExp;
  priceAdjustmentsTitle: string | RegExp;
  returnsPolicyFaqTitle: string | RegExp;
};

export function setTestOptions() {
  const extendedTest = test.extend<TestOptions>({
    customerServiceCid: [0, { option: true }],
    changeOrCancelTitle: ['', { option: true }],
    descriptionMetaChangeCancel: ['', { option: true }],
    orderStatusTitle: ['', { option: true }],
    descriptionMetaOrderStatus: ['', { option: true }],
    paymentOptionsTitle: ['', { option: true }],
    bopisTitle: ['', { option: true }],
    manageAccountTitle: ['', { option: true }],
    descriptionMetaManageAccount: ['', { option: true }],
    chatOrCallTitle: ['', { option: true }],
    descriptionMetaChatOrCall: ['', { option: true }],
    giftCardsTitle: ['', { option: true }],
    returnsPolicyFaqTitle: ['', { option: true }],
    buyGiftCardsTitle: ['', { option: true }],
    priceAdjustmentsTitle: ['', { option: true }],
    descriptionMetaPriceAdjustments: ['', { option: true }],
  });

  // Put all brand specific logic here
  switch (BRAND) {
    case 'at':
      extendedTest.use({
        changeOrCancelTitle: 'Changing or Canceling Your Order',
        descriptionMetaChangeCancel: 'Changing or Canceling Your Order',
        orderStatusTitle: 'Get the Status of Your Order',
        descriptionMetaOrderStatus: 'Get the Status of Your Order',
        paymentOptionsTitle: 'Payment Options',
        bopisTitle: 'Buy Online, Pickup In-Store',
        manageAccountTitle: 'Managing Your Account',
        descriptionMetaManageAccount: 'Managing Your Account',
        chatOrCallTitle: `Contact Us`,
        descriptionMetaChatOrCall: 'Contact Us',
        giftCardsTitle: 'Returning a Gift Card',
        returnsPolicyFaqTitle: 'Return Policy FAQ',
        buyGiftCardsTitle: 'Buying a Gift Card',
        priceAdjustmentsTitle: 'Getting a Price Adjustment',
        descriptionMetaPriceAdjustments: 'Getting a Price Adjustment',
      });
      break;
    case 'br':
      extendedTest.use({
        changeOrCancelTitle: 'Order Status & Tracking',
        descriptionMetaChangeCancel: 'Get order status. Change or cancel. Price adjustments. Customer Service',
        orderStatusTitle: 'Order Status & Tracking',
        descriptionMetaOrderStatus: 'Get order status. Change or cancel. Price adjustments. Customer Service',
        paymentOptionsTitle: 'Online Ordering & Shipping Information | Banana Republic',
        bopisTitle: 'Online Ordering & Shipping Information | Banana Republic',
        manageAccountTitle: 'Manage Your Account | Banana Republic',
        descriptionMetaManageAccount:
          'Having an online account allows you to check out faster, review past orders, save frequently used addresses, and get the latest deals. Create and/or manage your account now.',
        chatOrCallTitle: `Contact us`,
        descriptionMetaChatOrCall: 'Contact Us. Chat, Call. Customer Service',
        giftCardsTitle: 'Returns & Exchanges Policy - Online Purchases | Banana Republic',
        returnsPolicyFaqTitle: 'Returns & Exchanges Policy - Online Purchases | Banana Republic',
        buyGiftCardsTitle: 'Online Ordering & Shipping Information | Banana Republic',
        priceAdjustmentsTitle: 'Order Status & Tracking',
        descriptionMetaPriceAdjustments: 'Get order status. Change or cancel. Price adjustments. Customer Service',
      });
      break;
    case 'brf':
      extendedTest.use({
        changeOrCancelTitle: /Tracking or Changing Orders/i,
        descriptionMetaChangeCancel: /Get all the details of your order/i,
        orderStatusTitle: /Tracking or Changing Orders/i,
        descriptionMetaOrderStatus: /Get all the details of your order/i,
        paymentOptionsTitle: /Online Ordering and Shipping Information/i,
        bopisTitle: /Online Ordering and Shipping Information/i,
        manageAccountTitle: /Account Sign Up/i,
        descriptionMetaManageAccount: /An online account smoothes and speeds your shopping path/i,
        chatOrCallTitle: /Contact Us/i,
        giftCardsTitle: /Online Ordering and Shipping Information/i,
        returnsPolicyFaqTitle: /Returning Or Exchanging Items/i,
        priceAdjustmentsTitle: /Tracking or Changing Orders/i,
        descriptionMetaPriceAdjustments: /Get all the details of your order/i,
      });
      break;
    case 'gp':
      extendedTest.use({
        changeOrCancelTitle: 'Order Changes & Cancellations | Gap',
        descriptionMetaChangeCancel:
          "We'll do everything we can to accommodate your order change or cancellation. Learn about how to change or cancel your order here.",
        orderStatusTitle: 'Order Status | Gap',
        descriptionMetaOrderStatus: 'As soon as you place your order, you can see where it is on its way to your door. Check on the status of your order now.',
        paymentOptionsTitle: 'Payment Options | Gap',
        bopisTitle: 'Buy Online, Pick-Up In-Store FAQ | Gap',
        manageAccountTitle: 'Managing Your Account | Gap',
        descriptionMetaManageAccount: '',
        chatOrCallTitle: `Contact Us | ${getFullBrand()}`,
        descriptionMetaChatOrCall:
          "Chat or call — we're always happy to hear your comments and answer your questions here at Gap. Contact us now for customer support.",
        giftCardsTitle: 'Returning a Gift Card | Gap',
        returnsPolicyFaqTitle: 'Return Policy FAQ | Gap',
        buyGiftCardsTitle: 'Buying a Gift Card | Gap',
        priceAdjustmentsTitle: 'Getting a Price Adjustment | Gap',
        descriptionMetaPriceAdjustments: '',
      });
      break;
    case 'on':
      extendedTest.use({
        changeOrCancelTitle: /Tracking or Changing Orders/i,
        descriptionMetaChangeCancel: /Get order status. Change or cancel. Price adjustments. Customer Service/i,
        orderStatusTitle: /Tracking or Changing Orders/i,
        descriptionMetaOrderStatus: /Get order status. Change or cancel. Price adjustments. Customer Service/i,
        paymentOptionsTitle: /Online Ordering & Shipping Information/i,
        bopisTitle: /Online Ordering & Shipping Information/i,
        manageAccountTitle: /Managing Your Account/i,
        descriptionMetaManageAccount: /Having an online account allows you to check out faster/i,
        chatOrCallTitle: /Contact Us/i,
        descriptionMetaChatOrCall: /Chat or Call/i,
        giftCardsTitle: /Returns and Exchanges/i,
        returnsPolicyFaqTitle: /Returns and Exchanges/i,
        buyGiftCardsTitle: /Online Ordering & Shipping/i,
        priceAdjustmentsTitle: /Tracking or Changing Orders/i,
        descriptionMetaPriceAdjustments: /Price adjustments/i,
      });
      break;
    case 'gpf':
      extendedTest.use({
        changeOrCancelTitle: 'Changing or Canceling an Order | Gap Factory',
        descriptionMetaChangeCancel: '',
        orderStatusTitle: 'Get the Status of Your Order | Gap Factory',
        descriptionMetaOrderStatus:
          'As soon as you place your order, you can see where it is on its way to your door. Check on the status of your Gap Factory order now!',
        paymentOptionsTitle: 'Payment Options | Gap Factory',
        bopisTitle: 'Buy Online & Pickup In-Store FAQ | Gap Factory',
        manageAccountTitle: 'Managing Your Account | Gap Factory',
        descriptionMetaManageAccount:
          'Having an online Gap Factory account allows you to check out faster, review past orders, save frequently used addresses, and get the latest deals. Create and/or manage your account now.',
        chatOrCallTitle: `Contact Us | Gap Factory`,
        descriptionMetaChatOrCall:
          "Chat, call, or write a letter. We're always happy to hear your comments and answer your questions here at Gap Factory. Contact us now for customer support.",
        giftCardsTitle: 'Returning a Gift Card | Gap Factory',
        returnsPolicyFaqTitle: 'Return Policy FAQ | Gap Factory',
        buyGiftCardsTitle: 'Buying a Gift Card | Gap Factory',
        priceAdjustmentsTitle: 'Price Adjustments | Gap Factory',
        descriptionMetaPriceAdjustments: 'Get order status. Change or cancel. Price adjustments. Customer Service',
      });
      break;
    default:
      extendedTest.use({
        changeOrCancelTitle: '',
        descriptionMetaChangeCancel: '',
        orderStatusTitle: '',
        descriptionMetaOrderStatus: '',
        paymentOptionsTitle: '',
        bopisTitle: '',
        manageAccountTitle: '',
        descriptionMetaManageAccount: '',
        chatOrCallTitle: `Contact Us | ${getFullBrand()}`,
        descriptionMetaChatOrCall: '',
        giftCardsTitle: '',
        returnsPolicyFaqTitle: '',
        buyGiftCardsTitle: '',
        priceAdjustmentsTitle: '',
        descriptionMetaPriceAdjustments: '',
      });
      break;
  }
  return extendedTest;
}

export function getCustomerServiceCid(): number {
  switch (BRAND) {
    case 'at':
      return 44959;
    case 'br':
      return 6740;
    case 'gp':
      return 2136;
    case 'on':
      return 3171;
    case 'brf':
      return 1037835;
    case 'gpf':
      return 1037175;
    default:
      return 0;
  }
}

export function getCSReturningOrExchangingCid(): number {
  switch (BRAND) {
    case 'at':
      return 79311;
    case 'on':
      return 82724;
    case 'br':
      return 80735;
    case 'brf':
      return 1037874;
    default:
      return 0;
  }
}
