{"CookieSPAEnabled": false, "CookieSameSiteNoneEnabled": false, "CookieV2CSPEnabled": false, "MultiVariantTestingEnabled": false, "UseV2": true, "MobileSDK": false, "SkipGeolocation": false, "ScriptType": "PRODUCTION", "Version": "202308.2.0", "OptanonDataJSON": "fd7e4b94-2914-43ad-a566-f72ac86a29aa", "GeolocationUrl": "https://geolocation.onetrust.com/cookieconsentpub/v1/geo/location", "BulkDomainCheckUrl": "https://cookies-data.onetrust.io/bannersdk/v1/domaingroupcheck", "RuleSet": [{"Id": "9661c9c2-6817-4770-8a7c-5c103aa0cdec", "Name": "Quebec Citizens (Default - Opt Out of Sharing)", "Countries": [], "States": {"ca": ["qc"]}, "LanguageSwitcherPlaceholder": {"default": "en", "fr-CA": "fr-CA"}, "BannerPushesDown": false, "Default": false, "Global": false, "Type": "QUEBEC", "UseGoogleVendors": false, "VariantEnabled": false, "TestEndTime": null, "Variants": [], "TemplateName": "DNSS - Quebec Canada", "Conditions": [], "GCEnable": false, "IsGPPEnabled": false}, {"Id": "073d05e7-2994-41ca-b844-d35011593041", "Name": "Global", "Countries": ["pr", "ps", "pw", "py", "qa", "ad", "ae", "af", "ag", "ai", "al", "am", "ao", "aq", "ar", "as", "au", "aw", "az", "ba", "bb", "rs", "bd", "ru", "bf", "rw", "bh", "bi", "bj", "bl", "bm", "bn", "bo", "sa", "bq", "sb", "br", "sc", "sd", "bs", "bt", "sg", "bv", "sh", "bw", "by", "sj", "bz", "sl", "sn", "so", "ca", "sr", "cc", "ss", "cd", "st", "sv", "cf", "cg", "ch", "sx", "sy", "ci", "sz", "ck", "cl", "cm", "cn", "co", "tc", "cr", "td", "cu", "tf", "tg", "cv", "th", "cw", "cx", "tj", "tk", "tl", "tm", "tn", "to", "tr", "tt", "tv", "tw", "dj", "tz", "dm", "do", "ua", "ug", "dz", "um", "ec", "us", "eg", "eh", "uy", "uz", "va", "vc", "er", "ve", "et", "vg", "vi", "vn", "vu", "fj", "fk", "fm", "fo", "wf", "ga", "gb", "ws", "gd", "ge", "gg", "gh", "gi", "gl", "gm", "gn", "gq", "gs", "gt", "gu", "gw", "gy", "xk", "hk", "hm", "hn", "ht", "ye", "id", "il", "im", "in", "io", "za", "iq", "ir", "zm", "je", "zw", "jm", "jo", "jp", "ke", "kg", "kh", "ki", "km", "kn", "kp", "kr", "kw", "ky", "kz", "la", "lb", "lc", "lk", "lr", "ls", "ly", "ma", "md", "me", "mg", "mh", "mk", "ml", "mm", "mn", "mo", "mp", "mr", "ms", "mu", "mv", "mw", "mx", "my", "mz", "na", "nc", "ne", "nf", "ng", "ni", "np", "nr", "nu", "nz", "om", "pa", "pe", "pf", "pg", "ph", "pk", "pm", "pn"], "States": {}, "LanguageSwitcherPlaceholder": {"default": "en", "fr-CA": "fr-CA"}, "BannerPushesDown": false, "Default": true, "Global": true, "Type": "CCPA", "UseGoogleVendors": false, "VariantEnabled": false, "TestEndTime": null, "Variants": [], "TemplateName": "*CCPA Custom Template - Web (Canada)", "Conditions": [], "GCEnable": false, "IsGPPEnabled": false}], "IabData": {"cookieVersion": "1", "createdTime": "2023-09-21T21:30:47.971437164", "updatedTime": "2023-09-21T21:30:47.971444264", "cmpId": "28", "cmpVersion": "1", "consentScreen": "1", "consentLanguage": null, "vendorListVersion": 0, "maxVendorId": 0, "encodingType": "0", "globalVendorListUrl": "https://cdn.cookielaw.org/vendorlist/iabData.json"}, "IabV2Data": {"cookieVersion": "1", "createdTime": "2023-09-21T21:30:48.154115032", "updatedTime": "2023-09-21T21:30:48.154120732", "cmpId": "28", "cmpVersion": "1", "consentScreen": "1", "consentLanguage": null, "vendorListVersion": 0, "maxVendorId": 0, "encodingType": "0", "globalVendorListUrl": "https://cdn.cookielaw.org/vendorlist/iab2Data.json"}, "Iab2V2Data": {"cookieVersion": "1", "createdTime": "2023-09-21T21:30:48.337237408", "updatedTime": "2023-09-21T21:30:48.337243208", "cmpId": "28", "cmpVersion": "1", "consentScreen": "1", "consentLanguage": null, "vendorListVersion": 0, "maxVendorId": 0, "encodingType": "0", "globalVendorListUrl": "https://cdn.cookielaw.org/vendorlist/iab2V2Data.json"}, "GoogleData": {"vendorListVersion": 1, "googleVendorListUrl": "https://cdn.cookielaw.org/vendorlist/googleData.json"}, "ScriptDynamicLoadEnabled": false, "TenantFeatures": {"CookieV2BannerFocus": true, "CookieV2TargetedTemplates": true, "CookieV2GPC": true, "CookieV2AssignTemplateRule": true, "CookieV2GeolocationJsonApi": true, "CookieV2TCF21": true, "CookieV2CookieDateTimeInISO": true, "CookieV2RemoveSettingsIcon": true, "CookieV2BannerLogo": true, "CookieV2GeneralVendors": true, "CookieV2VendorServiceScript": true, "CookieV2GPP": true}, "IsSuppressBanner": false, "IsSuppressPC": false, "PublisherCC": "US", "Domain": "gapcanada.ca", "TenantGuid": "e1cf9ab6-fc39-4557-b4ef-4393861dad52", "EnvId": "app-prod", "RemoteActionsEnabled": false, "GeoRuleGroupName": "*GAP Inc Canada ", "GATrackToggle": false, "GATrackAssignedCategory": "", "WebFormIntegrationEnabled": false, "WebFormSrcUrl": "", "WebFormWorkerUrl": "", "GppData": {"cmpId": "28"}}