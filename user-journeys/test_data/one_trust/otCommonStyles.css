#onetrust-banner-sdk .onetrust-vendors-list-handler {
    cursor: pointer;
    color: #1f96db;
    font-size: inherit;
    font-weight: bold;
    text-decoration: none;
    margin-left: 5px
}

#onetrust-banner-sdk .onetrust-vendors-list-handler:hover {
    color: #1f96db
}

#onetrust-banner-sdk:focus {
    outline: 2px solid #000;
    outline-offset: -2px
}

#onetrust-banner-sdk a:focus {
    outline: 2px solid #000
}

#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler {
    outline-offset: 1px
}

#onetrust-banner-sdk.ot-bnr-w-logo .ot-bnr-logo {
    height: 64px;
    width: 64px
}

#onetrust-banner-sdk .ot-tcf2-vendor-count.ot-text-bold {
    font-weight: bold
}

#onetrust-banner-sdk .ot-close-icon,#onetrust-pc-sdk .ot-close-icon,#ot-sync-ntfy .ot-close-icon {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    height: 12px;
    width: 12px
}

#onetrust-banner-sdk .powered-by-logo,#onetrust-banner-sdk .ot-pc-footer-logo a,#onetrust-pc-sdk .powered-by-logo,#onetrust-pc-sdk .ot-pc-footer-logo a,#ot-sync-ntfy .powered-by-logo,#ot-sync-ntfy .ot-pc-footer-logo a {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    height: 25px;
    width: 152px;
    display: block;
    text-decoration: none;
    font-size: .75em
}

#onetrust-banner-sdk .powered-by-logo:hover,#onetrust-banner-sdk .ot-pc-footer-logo a:hover,#onetrust-pc-sdk .powered-by-logo:hover,#onetrust-pc-sdk .ot-pc-footer-logo a:hover,#ot-sync-ntfy .powered-by-logo:hover,#ot-sync-ntfy .ot-pc-footer-logo a:hover {
    color: #565656
}

#onetrust-banner-sdk h3 *,#onetrust-banner-sdk h4 *,#onetrust-banner-sdk h6 *,#onetrust-banner-sdk button *,#onetrust-banner-sdk a[data-parent-id] *,#onetrust-pc-sdk h3 *,#onetrust-pc-sdk h4 *,#onetrust-pc-sdk h6 *,#onetrust-pc-sdk button *,#onetrust-pc-sdk a[data-parent-id] *,#ot-sync-ntfy h3 *,#ot-sync-ntfy h4 *,#ot-sync-ntfy h6 *,#ot-sync-ntfy button *,#ot-sync-ntfy a[data-parent-id] * {
    font-size: inherit;
    font-weight: inherit;
    color: inherit
}

#onetrust-banner-sdk .ot-hide,#onetrust-pc-sdk .ot-hide,#ot-sync-ntfy .ot-hide {
    display: none !important
}

#onetrust-banner-sdk button.ot-link-btn:hover,#onetrust-pc-sdk button.ot-link-btn:hover,#ot-sync-ntfy button.ot-link-btn:hover {
    text-decoration: underline;
    opacity: 1
}

#onetrust-pc-sdk .ot-sdk-row .ot-sdk-column {
    padding: 0
}

#onetrust-pc-sdk .ot-sdk-container {
    padding-right: 0
}

#onetrust-pc-sdk .ot-sdk-row {
    flex-direction: initial;
    width: 100%
}

#onetrust-pc-sdk [type=checkbox]:checked,#onetrust-pc-sdk [type=checkbox]:not(:checked) {
    pointer-events: initial
}

#onetrust-pc-sdk [type=checkbox]:disabled+label::before,#onetrust-pc-sdk [type=checkbox]:disabled+label:after,#onetrust-pc-sdk [type=checkbox]:disabled+label {
    pointer-events: none;
    opacity: .7
}

#onetrust-pc-sdk #vendor-list-content {
    transform: translate3d(0, 0, 0)
}

#onetrust-pc-sdk li input[type=checkbox] {
    z-index: 1
}

#onetrust-pc-sdk li .ot-checkbox label {
    z-index: 2
}

#onetrust-pc-sdk li .ot-checkbox input[type=checkbox] {
    height: auto;
    width: auto
}

#onetrust-pc-sdk li .host-title a,#onetrust-pc-sdk li .ot-host-name a,#onetrust-pc-sdk li .accordion-text,#onetrust-pc-sdk li .ot-acc-txt {
    z-index: 2;
    position: relative
}

#onetrust-pc-sdk input {
    margin: 3px .1ex
}

#onetrust-pc-sdk .pc-logo,#onetrust-pc-sdk .ot-pc-logo {
    height: 60px;
    width: 180px;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    display: inline-flex;
    justify-content: center;
    align-items: center
}

#onetrust-pc-sdk .pc-logo img,#onetrust-pc-sdk .ot-pc-logo img {
    max-height: 100%;
    max-width: 100%
}

#onetrust-pc-sdk .screen-reader-only,#onetrust-pc-sdk .ot-scrn-rdr,.ot-sdk-cookie-policy .screen-reader-only,.ot-sdk-cookie-policy .ot-scrn-rdr {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

#onetrust-pc-sdk.ot-fade-in,.onetrust-pc-dark-filter.ot-fade-in,#onetrust-banner-sdk.ot-fade-in {
    animation-name: onetrust-fade-in;
    animation-duration: 400ms;
    animation-timing-function: ease-in-out
}

#onetrust-pc-sdk.ot-hide {
    display: none !important
}

.onetrust-pc-dark-filter.ot-hide {
    display: none !important
}

#ot-sdk-btn.ot-sdk-show-settings,#ot-sdk-btn.optanon-show-settings {
    color: #68b631;
    border: 1px solid #68b631;
    height: auto;
    white-space: normal;
    word-wrap: break-word;
    padding: .8em 2em;
    font-size: .8em;
    line-height: 1.2;
    cursor: pointer;
    -moz-transition: .1s ease;
    -o-transition: .1s ease;
    -webkit-transition: 1s ease;
    transition: .1s ease
}

#ot-sdk-btn.ot-sdk-show-settings:hover,#ot-sdk-btn.optanon-show-settings:hover {
    color: #fff;
    background-color: #68b631
}

.onetrust-pc-dark-filter {
    background: rgba(0,0,0,.5);
    z-index: 2147483646;
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0
}

@keyframes onetrust-fade-in {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

.ot-cookie-label {
    text-decoration: underline
}

@media only screen and (min-width: 426px)and (max-width: 896px)and (orientation: landscape) {
    #onetrust-pc-sdk p {
        font-size:.75em
    }
}

#onetrust-banner-sdk .banner-option-input:focus+label {
    outline: 1px solid #000;
    outline-style: auto
}

.category-vendors-list-handler+a:focus,.category-vendors-list-handler+a:focus-visible {
    outline: 2px solid #000
}

#onetrust-pc-sdk .ot-userid-title {
    margin-top: 10px
}

#onetrust-pc-sdk .ot-userid-title>span,#onetrust-pc-sdk .ot-userid-timestamp>span {
    font-weight: 700
}

#onetrust-pc-sdk .ot-userid-desc {
    font-style: italic
}

#onetrust-pc-sdk .ot-host-desc a {
    pointer-events: initial
}

#onetrust-pc-sdk .ot-ven-hdr>p a {
    position: relative;
    z-index: 2;
    pointer-events: initial
}

#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-vnd-info a,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-vnd-info a {
    margin-right: auto
}

#onetrust-pc-sdk .ot-pc-footer-logo img {
    width: 136px;
    height: 16px
}

#onetrust-pc-sdk .ot-pur-vdr-count {
    font-weight: 400;
    font-size: .7rem;
    padding-top: 3px;
    display: block
}

#onetrust-banner-sdk .ot-optout-signal,#onetrust-pc-sdk .ot-optout-signal {
    border: 1px solid #32ae88;
    border-radius: 3px;
    padding: 5px;
    margin-bottom: 10px;
    background-color: #f9fffa;
    font-size: .85rem;
    line-height: 2
}

#onetrust-banner-sdk .ot-optout-signal .ot-optout-icon,#onetrust-pc-sdk .ot-optout-signal .ot-optout-icon {
    display: inline;
    margin-right: 5px
}

#onetrust-banner-sdk .ot-optout-signal svg,#onetrust-pc-sdk .ot-optout-signal svg {
    height: 20px;
    width: 30px;
    transform: scale(0.5)
}

#onetrust-banner-sdk .ot-optout-signal svg path,#onetrust-pc-sdk .ot-optout-signal svg path {
    fill: #32ae88
}

#onetrust-consent-sdk .ot-general-modal {
    overflow: hidden;
    position: fixed;
    margin: 0 auto;
    top: 50%;
    left: 50%;
    width: 40%;
    padding: 1.5rem;
    max-width: 575px;
    min-width: 575px;
    z-index: **********;
    border-radius: 2.5px;
    transform: translate(-50%, -50%)
}

#onetrust-consent-sdk .ot-signature-health-group {
    margin-top: 1rem;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    margin-bottom: .625rem;
    width: calc(100% - 2.5rem)
}

#onetrust-consent-sdk .ot-signature-health-group .ot-signature-health-form {
    gap: .5rem
}

#onetrust-consent-sdk .ot-signature-health .ot-signature-health-form {
    width: 70%;
    gap: .35rem
}

#onetrust-consent-sdk .ot-signature-health .ot-signature-input {
    height: 38px;
    padding: 6px 10px;
    background-color: #fff;
    border: 1px solid #d1d1d1;
    border-radius: 4px;
    box-shadow: none;
    box-sizing: border-box
}

#onetrust-consent-sdk .ot-signature-health .ot-signature-subtitle {
    font-size: 1.125rem
}

#onetrust-consent-sdk .ot-signature-health .ot-signature-group-title {
    font-size: 1.25rem;
    font-weight: bold
}

#onetrust-consent-sdk .ot-signature-health,#onetrust-consent-sdk .ot-signature-health-group {
    display: flex;
    flex-direction: column;
    gap: 1rem
}

#onetrust-consent-sdk .ot-signature-health .ot-signature-cont,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-cont {
    display: flex;
    flex-direction: column;
    gap: .25rem
}

#onetrust-consent-sdk .ot-signature-health .ot-signature-paragraph,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-paragraph {
    margin: 0;
    line-height: 20px;
    font-size: max(14px,.875rem)
}

#onetrust-consent-sdk .ot-signature-health .ot-health-signature-error,#onetrust-consent-sdk .ot-signature-health-group .ot-health-signature-error {
    color: #4d4d4d;
    font-size: min(12px,.75rem)
}

#onetrust-consent-sdk .ot-signature-health .ot-signature-buttons-cont,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-buttons-cont {
    margin-top: max(.75rem,2%);
    gap: 1rem;
    display: flex;
    justify-content: flex-end
}

#onetrust-consent-sdk .ot-signature-health .ot-signature-button,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-button {
    flex: 1;
    height: auto;
    color: #fff;
    cursor: pointer;
    line-height: 1.2;
    min-width: 125px;
    font-weight: 600;
    font-size: .813em;
    border-radius: 2px;
    padding: 12px 10px;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
    background-color: #68b631;
    border: 2px solid #68b631
}

#onetrust-consent-sdk .ot-signature-health .ot-signature-button.reject,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-button.reject {
    background-color: #fff
}

#onetrust-consent-sdk .ot-input-field-cont {
    display: flex;
    flex-direction: column;
    gap: .5rem
}

#onetrust-consent-sdk .ot-input-field-cont .ot-signature-input {
    width: 65%
}

#onetrust-consent-sdk .ot-signature-health-form {
    display: flex;
    flex-direction: column
}

#onetrust-consent-sdk .ot-signature-health-form .ot-signature-label {
    margin-bottom: 0;
    line-height: 20px;
    font-size: max(14px,.875rem)
}

@media only screen and (max-width: 600px) {
    #onetrust-consent-sdk .ot-general-modal {
        min-width:100%
    }

    #onetrust-consent-sdk .ot-signature-health .ot-signature-health-form {
        width: 100%
    }

    #onetrust-consent-sdk .ot-input-field-cont .ot-signature-input {
        width: 100%
    }
}

#onetrust-banner-sdk,#onetrust-pc-sdk,#ot-sdk-cookie-policy,#ot-sync-ntfy {
    font-size: 16px
}

#onetrust-banner-sdk *,#onetrust-banner-sdk ::after,#onetrust-banner-sdk ::before,#onetrust-pc-sdk *,#onetrust-pc-sdk ::after,#onetrust-pc-sdk ::before,#ot-sdk-cookie-policy *,#ot-sdk-cookie-policy ::after,#ot-sdk-cookie-policy ::before,#ot-sync-ntfy *,#ot-sync-ntfy ::after,#ot-sync-ntfy ::before {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box
}

#onetrust-banner-sdk div,#onetrust-banner-sdk span,#onetrust-banner-sdk h1,#onetrust-banner-sdk h2,#onetrust-banner-sdk h3,#onetrust-banner-sdk h4,#onetrust-banner-sdk h5,#onetrust-banner-sdk h6,#onetrust-banner-sdk p,#onetrust-banner-sdk img,#onetrust-banner-sdk svg,#onetrust-banner-sdk button,#onetrust-banner-sdk section,#onetrust-banner-sdk a,#onetrust-banner-sdk label,#onetrust-banner-sdk input,#onetrust-banner-sdk ul,#onetrust-banner-sdk li,#onetrust-banner-sdk nav,#onetrust-banner-sdk table,#onetrust-banner-sdk thead,#onetrust-banner-sdk tr,#onetrust-banner-sdk td,#onetrust-banner-sdk tbody,#onetrust-banner-sdk .ot-main-content,#onetrust-banner-sdk .ot-toggle,#onetrust-banner-sdk #ot-content,#onetrust-banner-sdk #ot-pc-content,#onetrust-banner-sdk .checkbox,#onetrust-pc-sdk div,#onetrust-pc-sdk span,#onetrust-pc-sdk h1,#onetrust-pc-sdk h2,#onetrust-pc-sdk h3,#onetrust-pc-sdk h4,#onetrust-pc-sdk h5,#onetrust-pc-sdk h6,#onetrust-pc-sdk p,#onetrust-pc-sdk img,#onetrust-pc-sdk svg,#onetrust-pc-sdk button,#onetrust-pc-sdk section,#onetrust-pc-sdk a,#onetrust-pc-sdk label,#onetrust-pc-sdk input,#onetrust-pc-sdk ul,#onetrust-pc-sdk li,#onetrust-pc-sdk nav,#onetrust-pc-sdk table,#onetrust-pc-sdk thead,#onetrust-pc-sdk tr,#onetrust-pc-sdk td,#onetrust-pc-sdk tbody,#onetrust-pc-sdk .ot-main-content,#onetrust-pc-sdk .ot-toggle,#onetrust-pc-sdk #ot-content,#onetrust-pc-sdk #ot-pc-content,#onetrust-pc-sdk .checkbox,#ot-sdk-cookie-policy div,#ot-sdk-cookie-policy span,#ot-sdk-cookie-policy h1,#ot-sdk-cookie-policy h2,#ot-sdk-cookie-policy h3,#ot-sdk-cookie-policy h4,#ot-sdk-cookie-policy h5,#ot-sdk-cookie-policy h6,#ot-sdk-cookie-policy p,#ot-sdk-cookie-policy img,#ot-sdk-cookie-policy svg,#ot-sdk-cookie-policy button,#ot-sdk-cookie-policy section,#ot-sdk-cookie-policy a,#ot-sdk-cookie-policy label,#ot-sdk-cookie-policy input,#ot-sdk-cookie-policy ul,#ot-sdk-cookie-policy li,#ot-sdk-cookie-policy nav,#ot-sdk-cookie-policy table,#ot-sdk-cookie-policy thead,#ot-sdk-cookie-policy tr,#ot-sdk-cookie-policy td,#ot-sdk-cookie-policy tbody,#ot-sdk-cookie-policy .ot-main-content,#ot-sdk-cookie-policy .ot-toggle,#ot-sdk-cookie-policy #ot-content,#ot-sdk-cookie-policy #ot-pc-content,#ot-sdk-cookie-policy .checkbox,#ot-sync-ntfy div,#ot-sync-ntfy span,#ot-sync-ntfy h1,#ot-sync-ntfy h2,#ot-sync-ntfy h3,#ot-sync-ntfy h4,#ot-sync-ntfy h5,#ot-sync-ntfy h6,#ot-sync-ntfy p,#ot-sync-ntfy img,#ot-sync-ntfy svg,#ot-sync-ntfy button,#ot-sync-ntfy section,#ot-sync-ntfy a,#ot-sync-ntfy label,#ot-sync-ntfy input,#ot-sync-ntfy ul,#ot-sync-ntfy li,#ot-sync-ntfy nav,#ot-sync-ntfy table,#ot-sync-ntfy thead,#ot-sync-ntfy tr,#ot-sync-ntfy td,#ot-sync-ntfy tbody,#ot-sync-ntfy .ot-main-content,#ot-sync-ntfy .ot-toggle,#ot-sync-ntfy #ot-content,#ot-sync-ntfy #ot-pc-content,#ot-sync-ntfy .checkbox {
    font-family: inherit;
    font-weight: normal;
    -webkit-font-smoothing: auto;
    letter-spacing: normal;
    line-height: normal;
    padding: 0;
    margin: 0;
    height: auto;
    min-height: 0;
    max-height: none;
    width: auto;
    min-width: 0;
    max-width: none;
    border-radius: 0;
    border: none;
    clear: none;
    float: none;
    position: static;
    bottom: auto;
    left: auto;
    right: auto;
    top: auto;
    text-align: left;
    text-decoration: none;
    text-indent: 0;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    background: none;
    overflow: visible;
    vertical-align: baseline;
    visibility: visible;
    z-index: auto;
    box-shadow: none
}

#onetrust-banner-sdk label:before,#onetrust-banner-sdk label:after,#onetrust-banner-sdk .checkbox:after,#onetrust-banner-sdk .checkbox:before,#onetrust-pc-sdk label:before,#onetrust-pc-sdk label:after,#onetrust-pc-sdk .checkbox:after,#onetrust-pc-sdk .checkbox:before,#ot-sdk-cookie-policy label:before,#ot-sdk-cookie-policy label:after,#ot-sdk-cookie-policy .checkbox:after,#ot-sdk-cookie-policy .checkbox:before,#ot-sync-ntfy label:before,#ot-sync-ntfy label:after,#ot-sync-ntfy .checkbox:after,#ot-sync-ntfy .checkbox:before {
    content: "";
    content: none
}

#onetrust-banner-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container,#ot-sdk-cookie-policy .ot-sdk-container {
    position: relative;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box
}

#onetrust-banner-sdk .ot-sdk-column,#onetrust-banner-sdk .ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-column,#onetrust-pc-sdk .ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-column,#ot-sdk-cookie-policy .ot-sdk-columns {
    width: 100%;
    float: left;
    box-sizing: border-box;
    padding: 0;
    display: initial
}

@media(min-width: 400px) {
    #onetrust-banner-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container,#ot-sdk-cookie-policy .ot-sdk-container {
        width:90%;
        padding: 0
    }
}

@media(min-width: 550px) {
    #onetrust-banner-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container,#ot-sdk-cookie-policy .ot-sdk-container {
        width:100%
    }

    #onetrust-banner-sdk .ot-sdk-column,#onetrust-banner-sdk .ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-column,#onetrust-pc-sdk .ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-column,#ot-sdk-cookie-policy .ot-sdk-columns {
        margin-left: 4%
    }

    #onetrust-banner-sdk .ot-sdk-column:first-child,#onetrust-banner-sdk .ot-sdk-columns:first-child,#onetrust-pc-sdk .ot-sdk-column:first-child,#onetrust-pc-sdk .ot-sdk-columns:first-child,#ot-sdk-cookie-policy .ot-sdk-column:first-child,#ot-sdk-cookie-policy .ot-sdk-columns:first-child {
        margin-left: 0
    }

    #onetrust-banner-sdk .ot-sdk-two.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-two.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-two.ot-sdk-columns {
        width: 13.3333333333%
    }

    #onetrust-banner-sdk .ot-sdk-three.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-three.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-three.ot-sdk-columns {
        width: 22%
    }

    #onetrust-banner-sdk .ot-sdk-four.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-four.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-four.ot-sdk-columns {
        width: 30.6666666667%
    }

    #onetrust-banner-sdk .ot-sdk-eight.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-eight.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-eight.ot-sdk-columns {
        width: 65.3333333333%
    }

    #onetrust-banner-sdk .ot-sdk-nine.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-nine.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-nine.ot-sdk-columns {
        width: 74%
    }

    #onetrust-banner-sdk .ot-sdk-ten.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-ten.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-ten.ot-sdk-columns {
        width: 82.6666666667%
    }

    #onetrust-banner-sdk .ot-sdk-eleven.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-eleven.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-eleven.ot-sdk-columns {
        width: 91.3333333333%
    }

    #onetrust-banner-sdk .ot-sdk-twelve.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-twelve.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-twelve.ot-sdk-columns {
        width: 100%;
        margin-left: 0
    }
}

#onetrust-banner-sdk h1,#onetrust-banner-sdk h2,#onetrust-banner-sdk h3,#onetrust-banner-sdk h4,#onetrust-banner-sdk h5,#onetrust-banner-sdk h6,#onetrust-pc-sdk h1,#onetrust-pc-sdk h2,#onetrust-pc-sdk h3,#onetrust-pc-sdk h4,#onetrust-pc-sdk h5,#onetrust-pc-sdk h6,#ot-sdk-cookie-policy h1,#ot-sdk-cookie-policy h2,#ot-sdk-cookie-policy h3,#ot-sdk-cookie-policy h4,#ot-sdk-cookie-policy h5,#ot-sdk-cookie-policy h6 {
    margin-top: 0;
    font-weight: 600;
    font-family: inherit
}

#onetrust-banner-sdk h1,#onetrust-pc-sdk h1,#ot-sdk-cookie-policy h1 {
    font-size: 1.5rem;
    line-height: 1.2
}

#onetrust-banner-sdk h2,#onetrust-pc-sdk h2,#ot-sdk-cookie-policy h2 {
    font-size: 1.5rem;
    line-height: 1.25
}

#onetrust-banner-sdk h3,#onetrust-pc-sdk h3,#ot-sdk-cookie-policy h3 {
    font-size: 1.5rem;
    line-height: 1.3
}

#onetrust-banner-sdk h4,#onetrust-pc-sdk h4,#ot-sdk-cookie-policy h4 {
    font-size: 1.5rem;
    line-height: 1.35
}

#onetrust-banner-sdk h5,#onetrust-pc-sdk h5,#ot-sdk-cookie-policy h5 {
    font-size: 1.5rem;
    line-height: 1.5
}

#onetrust-banner-sdk h6,#onetrust-pc-sdk h6,#ot-sdk-cookie-policy h6 {
    font-size: 1.5rem;
    line-height: 1.6
}

@media(min-width: 550px) {
    #onetrust-banner-sdk h1,#onetrust-pc-sdk h1,#ot-sdk-cookie-policy h1 {
        font-size:1.5rem
    }

    #onetrust-banner-sdk h2,#onetrust-pc-sdk h2,#ot-sdk-cookie-policy h2 {
        font-size: 1.5rem
    }

    #onetrust-banner-sdk h3,#onetrust-pc-sdk h3,#ot-sdk-cookie-policy h3 {
        font-size: 1.5rem
    }

    #onetrust-banner-sdk h4,#onetrust-pc-sdk h4,#ot-sdk-cookie-policy h4 {
        font-size: 1.5rem
    }

    #onetrust-banner-sdk h5,#onetrust-pc-sdk h5,#ot-sdk-cookie-policy h5 {
        font-size: 1.5rem
    }

    #onetrust-banner-sdk h6,#onetrust-pc-sdk h6,#ot-sdk-cookie-policy h6 {
        font-size: 1.5rem
    }
}

#onetrust-banner-sdk p,#onetrust-pc-sdk p,#ot-sdk-cookie-policy p {
    margin: 0 0 1em 0;
    font-family: inherit;
    line-height: normal
}

#onetrust-banner-sdk a,#onetrust-pc-sdk a,#ot-sdk-cookie-policy a {
    color: #565656;
    text-decoration: underline
}

#onetrust-banner-sdk a:hover,#onetrust-pc-sdk a:hover,#ot-sdk-cookie-policy a:hover {
    color: #565656;
    text-decoration: none
}

#onetrust-banner-sdk .ot-sdk-button,#onetrust-banner-sdk button,#onetrust-pc-sdk .ot-sdk-button,#onetrust-pc-sdk button,#ot-sdk-cookie-policy .ot-sdk-button,#ot-sdk-cookie-policy button {
    margin-bottom: 1rem;
    font-family: inherit
}

#onetrust-banner-sdk .ot-sdk-button,#onetrust-banner-sdk button,#onetrust-pc-sdk .ot-sdk-button,#onetrust-pc-sdk button,#ot-sdk-cookie-policy .ot-sdk-button,#ot-sdk-cookie-policy button {
    display: inline-block;
    height: 38px;
    padding: 0 30px;
    color: #555;
    text-align: center;
    font-size: .9em;
    font-weight: 400;
    line-height: 38px;
    letter-spacing: .01em;
    text-decoration: none;
    white-space: nowrap;
    background-color: rgba(0,0,0,0);
    border-radius: 2px;
    border: 1px solid #bbb;
    cursor: pointer;
    box-sizing: border-box
}

#onetrust-banner-sdk .ot-sdk-button:hover,#onetrust-banner-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,#onetrust-banner-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus,#onetrust-pc-sdk .ot-sdk-button:hover,#onetrust-pc-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,#onetrust-pc-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus,#ot-sdk-cookie-policy .ot-sdk-button:hover,#ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,#ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus {
    color: #333;
    border-color: #888;
    opacity: .7
}

#onetrust-banner-sdk .ot-sdk-button:focus,#onetrust-banner-sdk :not(.ot-leg-btn-container)>button:focus,#onetrust-pc-sdk .ot-sdk-button:focus,#onetrust-pc-sdk :not(.ot-leg-btn-container)>button:focus,#ot-sdk-cookie-policy .ot-sdk-button:focus,#ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:focus {
    outline: 2px solid #000
}

#onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary,#onetrust-banner-sdk button.ot-sdk-button-primary,#onetrust-banner-sdk input[type=submit].ot-sdk-button-primary,#onetrust-banner-sdk input[type=reset].ot-sdk-button-primary,#onetrust-banner-sdk input[type=button].ot-sdk-button-primary,#onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary,#onetrust-pc-sdk button.ot-sdk-button-primary,#onetrust-pc-sdk input[type=submit].ot-sdk-button-primary,#onetrust-pc-sdk input[type=reset].ot-sdk-button-primary,#onetrust-pc-sdk input[type=button].ot-sdk-button-primary,#ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary,#ot-sdk-cookie-policy button.ot-sdk-button-primary,#ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary,#ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary,#ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary {
    color: #fff;
    background-color: #33c3f0;
    border-color: #33c3f0
}

#onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary:hover,#onetrust-banner-sdk button.ot-sdk-button-primary:hover,#onetrust-banner-sdk input[type=submit].ot-sdk-button-primary:hover,#onetrust-banner-sdk input[type=reset].ot-sdk-button-primary:hover,#onetrust-banner-sdk input[type=button].ot-sdk-button-primary:hover,#onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary:focus,#onetrust-banner-sdk button.ot-sdk-button-primary:focus,#onetrust-banner-sdk input[type=submit].ot-sdk-button-primary:focus,#onetrust-banner-sdk input[type=reset].ot-sdk-button-primary:focus,#onetrust-banner-sdk input[type=button].ot-sdk-button-primary:focus,#onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary:hover,#onetrust-pc-sdk button.ot-sdk-button-primary:hover,#onetrust-pc-sdk input[type=submit].ot-sdk-button-primary:hover,#onetrust-pc-sdk input[type=reset].ot-sdk-button-primary:hover,#onetrust-pc-sdk input[type=button].ot-sdk-button-primary:hover,#onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary:focus,#onetrust-pc-sdk button.ot-sdk-button-primary:focus,#onetrust-pc-sdk input[type=submit].ot-sdk-button-primary:focus,#onetrust-pc-sdk input[type=reset].ot-sdk-button-primary:focus,#onetrust-pc-sdk input[type=button].ot-sdk-button-primary:focus,#ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary:hover,#ot-sdk-cookie-policy button.ot-sdk-button-primary:hover,#ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary:hover,#ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary:hover,#ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary:hover,#ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary:focus,#ot-sdk-cookie-policy button.ot-sdk-button-primary:focus,#ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary:focus,#ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary:focus,#ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary:focus {
    color: #fff;
    background-color: #1eaedb;
    border-color: #1eaedb
}

#onetrust-banner-sdk input[type=text],#onetrust-pc-sdk input[type=text],#ot-sdk-cookie-policy input[type=text] {
    height: 38px;
    padding: 6px 10px;
    background-color: #fff;
    border: 1px solid #d1d1d1;
    border-radius: 4px;
    box-shadow: none;
    box-sizing: border-box
}

#onetrust-banner-sdk input[type=text],#onetrust-pc-sdk input[type=text],#ot-sdk-cookie-policy input[type=text] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

#onetrust-banner-sdk input[type=text]:focus,#onetrust-pc-sdk input[type=text]:focus,#ot-sdk-cookie-policy input[type=text]:focus {
    border: 1px solid #000;
    outline: 0
}

#onetrust-banner-sdk label,#onetrust-pc-sdk label,#ot-sdk-cookie-policy label {
    display: block;
    margin-bottom: .5rem;
    font-weight: 600
}

#onetrust-banner-sdk input[type=checkbox],#onetrust-pc-sdk input[type=checkbox],#ot-sdk-cookie-policy input[type=checkbox] {
    display: inline
}

#onetrust-banner-sdk ul,#onetrust-pc-sdk ul,#ot-sdk-cookie-policy ul {
    list-style: circle inside
}

#onetrust-banner-sdk ul,#onetrust-pc-sdk ul,#ot-sdk-cookie-policy ul {
    padding-left: 0;
    margin-top: 0
}

#onetrust-banner-sdk ul ul,#onetrust-pc-sdk ul ul,#ot-sdk-cookie-policy ul ul {
    margin: 1.5rem 0 1.5rem 3rem;
    font-size: 90%
}

#onetrust-banner-sdk li,#onetrust-pc-sdk li,#ot-sdk-cookie-policy li {
    margin-bottom: 1rem
}

#onetrust-banner-sdk th,#onetrust-banner-sdk td,#onetrust-pc-sdk th,#onetrust-pc-sdk td,#ot-sdk-cookie-policy th,#ot-sdk-cookie-policy td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e1e1e1
}

#onetrust-banner-sdk button,#onetrust-pc-sdk button,#ot-sdk-cookie-policy button {
    margin-bottom: 1rem;
    font-family: inherit
}

#onetrust-banner-sdk .ot-sdk-container:after,#onetrust-banner-sdk .ot-sdk-row:after,#onetrust-pc-sdk .ot-sdk-container:after,#onetrust-pc-sdk .ot-sdk-row:after,#ot-sdk-cookie-policy .ot-sdk-container:after,#ot-sdk-cookie-policy .ot-sdk-row:after {
    content: "";
    display: table;
    clear: both
}

#onetrust-banner-sdk .ot-sdk-row,#onetrust-pc-sdk .ot-sdk-row,#ot-sdk-cookie-policy .ot-sdk-row {
    margin: 0;
    max-width: none;
    display: block
}
