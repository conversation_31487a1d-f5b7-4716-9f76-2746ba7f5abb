{"DomainData": {"pclifeSpanYr": "Year", "pclifeSpanYrs": "Years", "pclifeSpanSecs": "A few seconds", "pclifeSpanWk": "Week", "pclifeSpanWks": "Weeks", "pccontinueWithoutAcceptText": "Continue without Accepting", "pccloseButtonType": "Icon", "MainText": "Privacy Preference Page", "MainInfoText": "<p style=\"font-size: 12px;\">\nWe use and share your personal information with others to personalize your shopping experience and show you targeted ads on other sites (like Google and Facebook) based on your interests and online activities. Information includes cookies, pixel tags, and similar technologies connected to your browser or device (<span style=\"color:black\"><strong>Online Info</strong></span>), as well as information associated with your email address based on your other interactions with us, like creating an account or shopping in our stores (<span style=\"color:black\"><strong>Offline Info</strong></span>). Others may use this information for their own marketing and business purposes.  \n</p>\n\n<p style=\"font-size: 12px;\">\n You can choose to opt-out of sharing one or both types of information.\n</p>", "AboutText": "", "AboutCookiesText": "Your Privacy", "ConfirmText": "", "AllowAllText": "Save Settings", "CookiesUsedText": "Cookies used", "CookiesDescText": "Description", "AboutLink": "", "ActiveText": "Active", "AlwaysActiveText": "Always Active", "AlwaysInactiveText": "Always Inactive", "PCShowAlwaysActiveToggle": false, "AlertNoticeText": "We use and share personal information through cookies and similar technologies on our sites to personalize your shopping experience and ads. Others may use this information for their own marketing and business purposes. To opt out of information sharing, click <span id=\"ot-sdk-btn\" class=\"ot-sdk-show-settings\">Your Privacy Choices</span> and check our <a href=\"https://www.gapinc.com/en-us/consumer-privacy-policy\" target=\"_blank\" rel=\"nofollow noopener noreferrer\" style=\"color: darkblue;\">Privacy Policy</a> for more details.", "AlertCloseText": "Close", "AlertMoreInfoText": "Your Privacy Choices", "CookieSettingButtonText": "Your Privacy Choices", "AlertAllowCookiesText": "Accept Cookies", "CloseShouldAcceptAllCookies": false, "LastReconsentDate": 1671227219787, "BannerTitle": "", "ForceConsent": false, "BannerPushesDownPage": false, "InactiveText": "Inactive", "CookiesText": "Cookies", "CategoriesText": "Cookie Subgroup", "IsLifespanEnabled": false, "LifespanText": "Lifespan", "VendorLevelOptOut": false, "HasScriptArchive": false, "BannerPosition": "bottom-left", "PreferenceCenterPosition": "default", "PreferenceCenterConfirmText": "Submit", "VendorListText": "List of IAB Vendors", "ThirdPartyCookieListText": "Cookies Details", "PreferenceCenterManagePreferencesText": "", "PreferenceCenterMoreInfoScreenReader": "Opens in a new Tab", "CookieListTitle": "<PERSON><PERSON>", "CookieListDescription": "A cookie is a small piece of data (text file) that a website – when visited by a user – asks your browser to store on your device in order to remember information about you, such as your language preference or login information. Those cookies are set by us and called first-party cookies. We also use third-party cookies – which are cookies from a domain different than the domain of the website you are visiting – for our advertising and marketing efforts. More specifically, we use cookies and other tracking technologies for the following purposes:", "Groups": [{"ShowInPopup": true, "ShowInPopupNonIAB": true, "ShowSDKListLink": true, "Order": "3", "OptanonGroupId": "C0004", "Parent": "SPD_BG", "ShowSubgroup": true, "ShowSubGroupDescription": true, "ShowSubgroupToggle": false, "AlwaysShowCategory": false, "GroupDescription": "These cookies may be set by our advertising partners to show you ads on other websites based on your interests and online activities and to help us measure the effectiveness of our advertising efforts. \n", "GroupDescriptionOTT": "These cookies may be set by our advertising partners to show you ads on other websites based on your interests and online activities and to help us measure the effectiveness of our advertising efforts. \n", "GroupNameMobile": "Advertising Cookies", "GroupNameOTT": "Advertising Cookies", "GroupName": "Advertising Cookies", "IsIabPurpose": false, "GeneralVendorsIds": [], "FirstPartyCookies": [{"id": "e2a42c88-7966-42a2-8790-17fe45bc55c6", "Name": "__qca", "Host": "gap.com", "IsSession": false, "Length": "391", "description": "This is a cookie usually associated with Quantcast, a digital advertising company.  They provide website rankings, and the data they collect is also used for audience segmentation and targeted advertising.\n## Adding these details based on host name \"Quantcast\"\nTag Title(s) : Quantcast - All - US, Factory, CA, JP [BR]\nUID(s) : 1130,1131,417,418,522,523\nVendor(s): Quantcast\nTag Container(s):Quantcast Easy Tag for Advertise", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "<PERSON>ie__qca", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "d2e83329-ca7f-49bd-9c21-5ab5e8ba742d", "Name": "_dlt", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "This cookie is associated with Google's real time bidding advertising exchange. The main purpose of this cookie is targeting.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookie_dlt", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "da57b481-8952-498f-8295-28415a704177", "Name": "_fbp", "Host": "gap.com", "IsSession": false, "Length": "89", "description": "Used by Facebook to deliver a series of advertisement products such as real time bidding from third party advertisers; Tag Title(s) : Facebook - Confirmation - GOL Products (NEW),Facebook - Confirmation - BR Products (NEW),Facebook - Confirmation - ON Products (NEW),Facebook - Confirmation - AT Products (NEW),Facebook Pixel - US (New),Facebook - Confirmation - GFOL Products,Facebook - Confirmation - BRFOL Products,Facebook - Confirmation - ONOL Products (NEW),Facebook - Confirmation - BROL Products (NEW),Facebook - Confirmation - ATOL Products (NEW)\n; UID(s) : 740,742,743,744,749,255,328,329,367,434,435,436,438\n; Vendor(s): Facebook\n; Tag Container(s):Facebook Pixel", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookie_fbp", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "6fa4e6c6-1a4a-46cb-8f6e-bae371c78e76", "Name": "_scid", "Host": "gap.com", "IsSession": false, "Length": "395", "description": "<PERSON><PERSON> associated with Snapchat. Sets a unique ID for the visitor, that allows third party advertisers to target the visitor with relevant advertisement. This pairing service is provided by third party advertisement hubs, which facilitates realtime bidding for advertisers.; Tag Title(s) : SnapChat - All - Social Code - NOT BROL\n; UID(s) : 811\n; Vendor(s): SnapChat\n; Tag Container(s):Tealium Custom Container", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookie_scid", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "25b78bda-2f4b-4541-8cd6-2d0356751997", "Name": "_uetsid", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "This cookie is used by Bing to determine what ads should be shown that may be relevant to the end user perusing the site.; Tag Title(s) : Bing - All - US,CA,Factory\n; UID(s) : 703,247,354\n; Vendor(s): Bing\n; Tag Container(s):Bing Ads Universal Event Tracking (UET) ", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "<PERSON><PERSON>_<PERSON><PERSON>id", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "b62417a0-e19c-4000-aff4-8b2496c070c5", "Name": "AMCV_", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "This is a pattern type cookie name associated with Adobe Marketing Cloud. It stores a unique visitor identifier, and uses an organisation identifier to allow a company to track users across their domains and services.; Tag Title(s) : Adobe Analytics AppMeasurement for JS, Adobe ; Experience Cloud ID Service\n; UID(s) : 4,28,828\n; Vendor(s): Adobe\n; Tag Container(s):Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service", "thirdPartyDescription": "This is a pattern type cookie name associated with Adobe Marketing Cloud. It stores a unique visitor identifier, and uses an organisation identifier to allow a company to track users across their domains and services.", "patternKey": "AMCV_", "thirdPartyKey": "Pattern|AMCV_", "firstPartyKey": "Pattern|AMCV_", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "7c7d0598-6ebc-4df6-a85b-2dd8af363c57", "Name": "AMCVS_", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "This is a pattern type cookie name associated with Adobe Marketing Cloud. It stores a unique visitor identifier, and uses an organisation identifier.; Tag Title(s) : Adobe Analytics AppMeasurement for JS, Adobe ; Experience Cloud ID Service\n; UID(s) : 4,28,828\n; Vendor(s): Adobe\n; Tag Container(s):Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service", "thirdPartyDescription": "This is a pattern type cookie name associated with Adobe Marketing Cloud. It stores a unique visitor identifier, and uses an organisation identifier.", "patternKey": "AMCVS_", "thirdPartyKey": "Pattern|AMCVS_", "firstPartyKey": "Pattern|AMCVS_", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "641f88da-0fb4-40dc-9874-d84f0bb4cd31", "Name": "amp_", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "Supports Amplitude product analytics.", "thirdPartyDescription": "Supports Amplitude product analytics.", "patternKey": "amp_", "thirdPartyKey": "Pattern|amp_", "firstPartyKey": "Pattern|amp_", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "590da5e7-fca5-48c8-b4f9-7e076c8c7320", "Name": "amp_xxxxx", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "Supports Amplitude product analytics.", "thirdPartyDescription": "Supports Amplitude product analytics.", "patternKey": "amp_", "thirdPartyKey": "Pattern|amp_", "firstPartyKey": "Pattern|amp_", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "e3e78ed1-e858-4082-b29a-c5ff845f47c9", "Name": "tfc-l", "Host": "bananarepublic.gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 2 based on internal GAP feedback::\nTag Title(s) : TruFit - Confirmation - ONOL Tracking,TruFit - Confirmation - ONOL (STAGING ONLY),TruFit - Confirmation - GOL (STAGING ONLY),TruFit - Confirmation - <PERSON>OL (STAGING ONLY),TruFit - Confirmation - ATOL (STAGING ONLY) ,TruFit - Confirmation - GOL,TruFit - Confirmation - BROL,TruFit - Confirmation - ATOL,TrueFit - Confirmation - GPFS,TrueFit - Confirmation - BRFS,TrueFit - Confirmation - ON CA,TrueFit - Confirmation - Gap CA,TrueFit - Confirmation - BR CA,TrueFit - Confirmation - AT CA\nUID(s) : 569,624,650,651,652,705,706,707,267,268,374,375,376,441\nVendor(s): TruFit\nTag Container(s): Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "d332dfc9-c342-4a07-9936-4d0425b1ef17", "Name": "thx_guid", "Host": "content.gap.com", "IsSession": false, "Length": "1799", "description": "Allows unique identification of a device (PC, telephone, etc.) used to place orders on this website for subsequent analysis.::\nTag Title(s) : Bing - All - US,CA,Factory\nUID(s) : 703,247,354\nVendor(s): Bing\nTag Container(s):Bing Ads Universal Event Tracking (UET)\n", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookiethx_guid", "DurationType": 1, "category": null, "isThirdParty": false}], "Hosts": [{"HostName": "sharethrough.com", "DisplayName": "sharethrough.com", "HostId": "H218", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "stx_user_id", "Host": "sharethrough.com", "IsSession": false, "Length": "29", "description": "This domain is owned by Sharethrough, a USA based company providing an online native advertising technology platform and services.", "thirdPartyDescription": "This domain is owned by Sharethrough, a USA based company providing an online native advertising technology platform and services.", "patternKey": null, "thirdPartyKey": "Cookie|sharethrough.com", "firstPartyKey": "Cookiestx_user_id", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "amazon-adsystem.com", "DisplayName": "amazon-adsystem.com", "HostId": "H70", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "ad-id", "Host": "amazon-adsystem.com", "IsSession": false, "Length": "214", "description": "This domain is owned by online retailer Amazon and is used as part of its affiliate marketing programme.", "thirdPartyDescription": "This domain is owned by online retailer Amazon and is used as part of its affiliate marketing programme.", "patternKey": null, "thirdPartyKey": "Cookie|amazon-adsystem.com", "firstPartyKey": "Cookiead-id", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "2c14e4f7-c5a3-46b1-b5b6-0cc3a5f5e1c8", "Name": "ad-privacy", "Host": "amazon-adsystem.com", "IsSession": false, "Length": "1858", "description": "This domain is owned by online retailer Amazon and is used as part of its affiliate marketing programme.", "thirdPartyDescription": "This domain is owned by online retailer Amazon and is used as part of its affiliate marketing programme.", "patternKey": null, "thirdPartyKey": "Cookie|amazon-adsystem.com", "firstPartyKey": "Cookiead-privacy", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "agkn.com", "DisplayName": "agkn.com", "HostId": "H186", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "ab", "Host": "agkn.com", "IsSession": false, "Length": "364", "description": "This domain is owned by Aggregate Knowledge, which has been acquired by Neustar Inc. Aggregate Knowledge provides a data management platform which enables real time targeting and consumer profiling functionality. ", "thirdPartyDescription": "This domain is owned by Aggregate Knowledge, which has been acquired by Neustar Inc. Aggregate Knowledge provides a data management platform which enables real time targeting and consumer profiling functionality. ", "patternKey": null, "thirdPartyKey": "Cookie|agkn.com", "firstPartyKey": "<PERSON><PERSON>", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "semasio.net", "DisplayName": "semasio.net", "HostId": "H187", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "SEUNCY", "Host": "semasio.net", "IsSession": false, "Length": "364", "description": "This domain is owned by Semasio, a German business specialising in profiling and behavioural targeting of consumers. ", "thirdPartyDescription": "This domain is owned by Semasio, a German business specialising in profiling and behavioural targeting of consumers. ", "patternKey": null, "thirdPartyKey": "Cookie|semasio.net", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "quantserve.com", "DisplayName": "quantserve.com", "HostId": "H162", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "d", "Host": "quantserve.com", "IsSession": false, "Length": "89", "description": "This domain is owned by Quantcast. The main business activity is: Market and Audience Segmentation, Targeted advertising services", "thirdPartyDescription": "This domain is owned by Quantcast. The main business activity is: Market and Audience Segmentation, Targeted advertising services", "patternKey": null, "thirdPartyKey": "Cookie|quantserve.com", "firstPartyKey": "Cookied", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "37e98291-6b8f-4929-ac99-76835e213374", "Name": "mc", "Host": "quantserve.com", "IsSession": false, "Length": "395", "description": "This domain is owned by Quantcast. The main business activity is: Market and Audience Segmentation, Targeted advertising services", "thirdPartyDescription": "This domain is owned by Quantcast. The main business activity is: Market and Audience Segmentation, Targeted advertising services", "patternKey": null, "thirdPartyKey": "Cookie|quantserve.com", "firstPartyKey": "Cookiemc", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "pubmatic.com", "DisplayName": "pubmatic.com", "HostId": "H212", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "KRTBCOOKIE_xxxx", "Host": "pubmatic.com", "IsSession": false, "Length": "89", "description": "This cookie is used to correlate IDs with those of Pubmatic partners (such as demand side platform clients or other advertising technology companies). Pubmatic passes information stored by the partner in this cookie to the partner when it is considering whether to purchase advertisements. This enables the partner to make better decisions about whether to display an advertisement to you.", "thirdPartyDescription": "This cookie is used to correlate IDs with those of Pubmatic partners (such as demand side platform clients or other advertising technology companies). Pubmatic passes information stored by the partner in this cookie to the partner when it is considering whether to purchase advertisements. This enables the partner to make better decisions about whether to display an advertisement to you.", "patternKey": "KRTBCOOKIE_xxxx", "thirdPartyKey": "Pattern|KRTBCOOKIE_xxxx", "firstPartyKey": "Pattern|KRTBCOOKIE_xxxx", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "bat.bing.com", "DisplayName": "bat.bing.com", "HostId": "H189", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "MR", "Host": "bat.bing.com", "IsSession": false, "Length": "6", "description": "This domain is owned by Microsoft - it is the site for the search engine Bing.", "thirdPartyDescription": "This domain is owned by Microsoft - it is the site for the search engine Bing.", "patternKey": null, "thirdPartyKey": "Cookie|bat.bing.com", "firstPartyKey": "<PERSON><PERSON>", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "yahoo.com", "DisplayName": "yahoo.com", "HostId": "H165", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "A3", "Host": "yahoo.com", "IsSession": false, "Length": "365", "description": "This domain is owned by Yahoo. The main business activity is: Search / Advertising", "thirdPartyDescription": "This domain is owned by Yahoo. The main business activity is: Search / Advertising", "patternKey": null, "thirdPartyKey": "Cookie|yahoo.com", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "dpm.demdex.net", "DisplayName": "dpm.demdex.net", "HostId": "H166", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "dpm", "Host": "dpm.demdex.net", "IsSession": false, "Length": "179", "description": "This domain is owned by Adobe Audience Manager. The main business activity is online profiling for targeted marketing.", "thirdPartyDescription": "This domain is owned by Adobe Audience Manager. The main business activity is online profiling for targeted marketing.", "patternKey": null, "thirdPartyKey": "Cookie|dpm.demdex.net", "firstPartyKey": "Cookiedpm", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "rubiconproject.com", "DisplayName": "rubiconproject.com", "HostId": "H190", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "audit", "Host": "rubiconproject.com", "IsSession": false, "Length": "364", "description": "This domain is owned by Rubicon Project. The main business activity is: Advertising", "thirdPartyDescription": "This domain is owned by Rubicon Project. The main business activity is: Advertising", "patternKey": null, "thirdPartyKey": "Cookie|rubiconproject.com", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "0190d7d0-1317-73b4-a7c0-7c272506d15e", "Name": "audit_p", "Host": "rubiconproject.com", "IsSession": false, "Length": "364", "description": "This domain is owned by Rubicon Project. The main business activity is: Advertising", "thirdPartyDescription": "This domain is owned by Rubicon Project. The main business activity is: Advertising", "patternKey": null, "thirdPartyKey": "Cookie|rubiconproject.com", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "6e950c89-7fd9-4b33-af5a-4fc70a59f131", "Name": "khaos", "Host": "rubiconproject.com", "IsSession": false, "Length": "364", "description": "This domain is owned by Rubicon Project. The main business activity is: Advertising", "thirdPartyDescription": "This domain is owned by Rubicon Project. The main business activity is: Advertising", "patternKey": null, "thirdPartyKey": "Cookie|rubiconproject.com", "firstPartyKey": "<PERSON><PERSON><PERSON><PERSON>", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "0190d7d0-1317-7d3a-a7a8-e802a66351fb", "Name": "khaos_p", "Host": "rubiconproject.com", "IsSession": false, "Length": "364", "description": "This domain is owned by Rubicon Project. The main business activity is: Advertising", "thirdPartyDescription": "This domain is owned by Rubicon Project. The main business activity is: Advertising", "patternKey": null, "thirdPartyKey": "Cookie|rubiconproject.com", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "bttrack.com", "DisplayName": "bttrack.com", "HostId": "H51", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "bt-es-15041", "Host": "bttrack.com", "IsSession": true, "Length": "0", "description": "This domain appears to be owned by Bidtellect, A USA based adtechnology company specialising in native advertising.", "thirdPartyDescription": "This domain appears to be owned by Bidtellect, A USA based adtechnology company specialising in native advertising.", "patternKey": null, "thirdPartyKey": "Cookie|bttrack.com", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "abd57aa7-dfc6-4723-8789-2b4552ad74d7", "Name": "bt-es-15338", "Host": "bttrack.com", "IsSession": true, "Length": "0", "description": "This domain appears to be owned by Bidtellect, A USA based adtechnology company specialising in native advertising.", "thirdPartyDescription": "This domain appears to be owned by Bidtellect, A USA based adtechnology company specialising in native advertising.", "patternKey": null, "thirdPartyKey": "Cookie|bttrack.com", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "6746cdd5-9a2c-4f3e-b835-1791825856b3", "Name": "bt-es-15341", "Host": "bttrack.com", "IsSession": true, "Length": "0", "description": "This domain appears to be owned by Bidtellect, A USA based adtechnology company specialising in native advertising.", "thirdPartyDescription": "This domain appears to be owned by Bidtellect, A USA based adtechnology company specialising in native advertising.", "patternKey": null, "thirdPartyKey": "Cookie|bttrack.com", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "a8333f64-431c-437e-be17-cdc0b67d80f4", "Name": "GLOBALID", "Host": "bttrack.com", "IsSession": false, "Length": "89", "description": "This domain appears to be owned by Bidtellect, A USA based adtechnology company specialising in native advertising.", "thirdPartyDescription": "This domain appears to be owned by Bidtellect, A USA based adtechnology company specialising in native advertising.", "patternKey": null, "thirdPartyKey": "Cookie|bttrack.com", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "twitter.com", "DisplayName": "twitter.com", "HostId": "H67", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "guest_id", "Host": "twitter.com", "IsSession": false, "Length": "729", "description": "Twitter does not currently provide information on the use of specific cookies.", "thirdPartyDescription": "Twitter does not currently provide information on the use of specific cookies.", "patternKey": null, "thirdPartyKey": "Cookieguest_id|twitter.com", "firstPartyKey": "Cookieguest_id", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "7085f87a-65c3-472f-b68d-73d1c95d824f", "Name": "guest_id_ads", "Host": "twitter.com", "IsSession": false, "Length": "729", "description": "This domain is owned by Twitter. The main business activity is: Social Networking Services.  Where twitter acts as a third party host, it collects data through a range of plug-ins and integrations, that is primarily used for tracking and targeting.", "thirdPartyDescription": "This domain is owned by Twitter. The main business activity is: Social Networking Services.  Where twitter acts as a third party host, it collects data through a range of plug-ins and integrations, that is primarily used for tracking and targeting.", "patternKey": null, "thirdPartyKey": "Cookie|twitter.com", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "9c9db23b-d30b-48e9-a07e-dc5539f0ef70", "Name": "guest_id_marketing", "Host": "twitter.com", "IsSession": false, "Length": "729", "description": "This domain is owned by Twitter. The main business activity is: Social Networking Services.  Where twitter acts as a third party host, it collects data through a range of plug-ins and integrations, that is primarily used for tracking and targeting.", "thirdPartyDescription": "This domain is owned by Twitter. The main business activity is: Social Networking Services.  Where twitter acts as a third party host, it collects data through a range of plug-ins and integrations, that is primarily used for tracking and targeting.", "patternKey": null, "thirdPartyKey": "Cookie|twitter.com", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "tealiumiq.com", "DisplayName": "tealiumiq.com", "HostId": "H168", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "TAPID", "Host": "tealiumiq.com", "IsSession": false, "Length": "364", "description": "This domain is owned by Tealium and is used for its audience streaming service which enables targeting and personalisation of content on a domain.", "thirdPartyDescription": "This domain is owned by Tealium and is used for its audience streaming service which enables targeting and personalisation of content on a domain.", "patternKey": null, "thirdPartyKey": "Cookie|tealiumiq.com", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "facebook.com", "DisplayName": "facebook.com", "HostId": "H251", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "fr", "Host": "facebook.com", "IsSession": false, "Length": "89", "description": "Contains browser and user unique ID combination, used for targeted advertising.", "thirdPartyDescription": "Contains browser and user unique ID combination, used for targeted advertising.", "patternKey": null, "thirdPartyKey": "Cookiefr|facebook.com", "firstPartyKey": "<PERSON><PERSON><PERSON>", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "bing.com", "DisplayName": "bing.com", "HostId": "H169", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "MSPTC", "Host": "bing.com", "IsSession": false, "Length": "389", "description": "This domain is owned by Microsoft - it is the site for the search engine Bing.", "thirdPartyDescription": "This domain is owned by Microsoft - it is the site for the search engine Bing.", "patternKey": null, "thirdPartyKey": "Cookie|bing.com", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "f984d4f7-33bb-4e30-8706-50560149be85", "Name": "MUID", "Host": "bing.com", "IsSession": false, "Length": "389", "description": "This domain is owned by Microsoft - it is the site for the search engine Bing.", "thirdPartyDescription": "This domain is owned by Microsoft - it is the site for the search engine Bing.", "patternKey": null, "thirdPartyKey": "Cookie|bing.com", "firstPartyKey": "Cookie<PERSON>", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "pinterest.com", "DisplayName": "pinterest.com", "HostId": "H304", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "ar_debug", "Host": "pinterest.com", "IsSession": false, "Length": "364", "description": "This domain is owned by Pinterest. The main business activity is: Social Content Sharing platform", "thirdPartyDescription": "This domain is owned by Pinterest. The main business activity is: Social Content Sharing platform", "patternKey": null, "thirdPartyKey": "Cookie|pinterest.com", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "casalemedia.com", "DisplayName": "casalemedia.com", "HostId": "H77", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "CMID", "Host": "casalemedia.com", "IsSession": false, "Length": "364", "description": "This domain is owned by Casale Media. The main business activity is: Advertising", "thirdPartyDescription": "This domain is owned by Casale Media. The main business activity is: Advertising", "patternKey": null, "thirdPartyKey": "Cookie|casalemedia.com", "firstPartyKey": "Cookie<PERSON>", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "b7113ee9-6f75-47c4-bfd9-5816fe84504d", "Name": "CMPRO", "Host": "casalemedia.com", "IsSession": false, "Length": "89", "description": "This domain is owned by Casale Media. The main business activity is: Advertising", "thirdPartyDescription": "This domain is owned by Casale Media. The main business activity is: Advertising", "patternKey": null, "thirdPartyKey": "Cookie|casalemedia.com", "firstPartyKey": "CookieCM<PERSON>", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "fca6c00a-cf1c-45bc-964e-2447f34c7dce", "Name": "CMPS", "Host": "casalemedia.com", "IsSession": false, "Length": "89", "description": "This domain is owned by Casale Media. The main business activity is: Advertising", "thirdPartyDescription": "This domain is owned by Casale Media. The main business activity is: Advertising", "patternKey": null, "thirdPartyKey": "Cookie|casalemedia.com", "firstPartyKey": "Cookie<PERSON>", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "tapad.com", "DisplayName": "tapad.com", "HostId": "H192", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "TapAd_3WAY_SYNCS", "Host": "tapad.com", "IsSession": false, "Length": "59", "description": "This domain is owned by Tapad Inc. a US based company that provides technology to track users across devices to enhance targeting capabilities.", "thirdPartyDescription": "This domain is owned by Tapad Inc. a US based company that provides technology to track users across devices to enhance targeting capabilities.", "patternKey": null, "thirdPartyKey": "Cookie|tapad.com", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "7920c0c4-86e8-47af-bc52-9090cfe67f05", "Name": "TapAd_DID", "Host": "tapad.com", "IsSession": false, "Length": "59", "description": "This domain is owned by Tapad Inc. a US based company that provides technology to track users across devices to enhance targeting capabilities.", "thirdPartyDescription": "This domain is owned by Tapad Inc. a US based company that provides technology to track users across devices to enhance targeting capabilities.", "patternKey": null, "thirdPartyKey": "Cookie|tapad.com", "firstPartyKey": "CookieTapAd_DID", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "e8ade607-242b-45ae-aeba-868e37fe3d72", "Name": "TapAd_TS", "Host": "tapad.com", "IsSession": false, "Length": "59", "description": "TS", "thirdPartyDescription": "TS", "patternKey": "TS", "thirdPartyKey": "Pattern|TS", "firstPartyKey": "CookieTap<PERSON>d_TS,Pattern|TS", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "demdex.net", "DisplayName": "demdex.net", "HostId": "H172", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "demdex", "Host": "demdex.net", "IsSession": false, "Length": "179", "description": "This cookie helps Adobe Audience Manger perform basic functions such as visitor identification, ID synchronization, segmentation, modeling, reporting, etc.", "thirdPartyDescription": "This cookie helps Adobe Audience Manger perform basic functions such as visitor identification, ID synchronization, segmentation, modeling, reporting, etc.", "patternKey": "demdex", "thirdPartyKey": "Pattern|demdex", "firstPartyKey": "Pattern|demdex", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "analytics.yahoo.com", "DisplayName": "analytics.yahoo.com", "HostId": "H194", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "IDSYNC", "Host": "analytics.yahoo.com", "IsSession": false, "Length": "364", "description": "This domain is owned by Yahoo Inc. whose main business model is online advertising.  Although this domain is associated with Yahoo's web analytics service, because these are third party cookies they can allow Yahoo, in combination with other cookies set, to collect data for targeted advertising purposes.", "thirdPartyDescription": "This domain is owned by Yahoo Inc. whose main business model is online advertising.  Although this domain is associated with Yahoo's web analytics service, because these are third party cookies they can allow Yahoo, in combination with other cookies set, to collect data for targeted advertising purposes.", "patternKey": null, "thirdPartyKey": "Cookie|analytics.yahoo.com", "firstPartyKey": "CookieID<PERSON>", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "everesttech.net", "DisplayName": "everesttech.net", "HostId": "H42", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "everest_g_v2", "Host": "everesttech.net", "IsSession": false, "Length": "364", "description": "This domain is owned by Adobe. The main business activity is: Advertising", "thirdPartyDescription": "This domain is owned by Adobe. The main business activity is: Advertising", "patternKey": null, "thirdPartyKey": "Cookie|everesttech.net", "firstPartyKey": "Cookieeverest_g_v2", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "89e4d4c7-87d1-4613-a622-a14a1b5f33bb", "Name": "everest_session_v2", "Host": "everesttech.net", "IsSession": true, "Length": "0", "description": "This domain is owned by Adobe. The main business activity is: Advertising", "thirdPartyDescription": "This domain is owned by Adobe. The main business activity is: Advertising", "patternKey": null, "thirdPartyKey": "Cookie|everesttech.net", "firstPartyKey": "Cookieeverest_session_v2", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "adnxs.com", "DisplayName": "adnxs.com", "HostId": "H195", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "uuid2", "Host": "adnxs.com", "IsSession": false, "Length": "89", "description": "This domain is owned by AppNexus Inc. The company provides a range of online advertising technology and services.", "thirdPartyDescription": "This domain is owned by AppNexus Inc. The company provides a range of online advertising technology and services.", "patternKey": null, "thirdPartyKey": "Cookie|adnxs.com", "firstPartyKey": "Cookieuuid2", "DurationType": 1, "category": null, "isThirdParty": false}]}], "PurposeId": "82DC14ED-BDD8-4C78-9A63-8939653029F0", "CustomGroupId": "C0004", "GroupId": "84a40d64-a8e2-4845-9587-474788af7df7", "Status": "active", "IsDntEnabled": false, "Type": "COOKIE", "DescriptionLegal": "", "IabIllustrations": [], "HasLegIntOptOut": false, "HasConsentOptOut": true, "IsGpcEnabled": true, "VendorServices": null, "TrackingTech": null}, {"ShowInPopup": true, "ShowInPopupNonIAB": true, "ShowSDKListLink": true, "Order": "4", "OptanonGroupId": "C0005", "Parent": "SPD_BG", "ShowSubgroup": true, "ShowSubGroupDescription": true, "ShowSubgroupToggle": false, "AlwaysShowCategory": false, "GroupDescription": "These cookies are set by a range of social media services that we have added to the site to enable you to share our content with your friends and networks. They are capable of tracking your browser across other sites and building up a profile of your interests. This may impact the content and messages you see on other websites you visit. If you do not allow these cookies you may not be able to use or see these sharing tools.", "GroupDescriptionOTT": "These cookies are set by a range of social media services that we have added to the site to enable you to share our content with your friends and networks. They are capable of tracking your browser across other sites and building up a profile of your interests. This may impact the content and messages you see on other websites you visit. If you do not allow these cookies you may not be able to use or see these sharing tools.", "GroupNameMobile": "Social Media Cookies", "GroupNameOTT": "Social Media Cookies", "GroupName": "Social Media Cookies", "IsIabPurpose": false, "GeneralVendorsIds": [], "FirstPartyCookies": [], "Hosts": [], "PurposeId": "90AFF18A-31C0-4479-B4DA-5794C8F02CBA", "CustomGroupId": "C0005", "GroupId": "29e9c2d8-a041-4a4b-9bf4-ccfef6bbbca2", "Status": "active", "IsDntEnabled": false, "Type": "COOKIE", "DescriptionLegal": "", "IabIllustrations": [], "HasLegIntOptOut": false, "HasConsentOptOut": true, "IsGpcEnabled": true, "VendorServices": null, "TrackingTech": null}, {"ShowInPopup": true, "ShowInPopupNonIAB": true, "ShowSDKListLink": true, "Order": "5", "OptanonGroupId": "C0015", "Parent": "SPD_BG", "ShowSubgroup": true, "ShowSubGroupDescription": true, "ShowSubgroupToggle": false, "AlwaysShowCategory": false, "GroupDescription": "GAP Sale or Share of Customer Data - category 2", "GroupDescriptionOTT": "GAP Sale or Share of Customer Data - category 2", "GroupNameMobile": "GAP Category Two", "GroupNameOTT": "GAP Category Two", "GroupName": "GAP Category Two", "IsIabPurpose": false, "GeneralVendorsIds": [], "FirstPartyCookies": [{"id": "0af8a3ed-4b96-412e-9b5e-ad4e040f7e4a", "Name": "__attentive_cco", "Host": "athleta.gap.com", "IsSession": false, "Length": "21899", "description": "Updated from Unknown to Category 2. <PERSON><PERSON> is included for Consent in Tealium; Tag Title(s) : Attentive - All - Gap US,Attentive - Confirmation - Gap Products,Attentive - All [GPFS]; UID(s) : 971,972,409; Vendor(s): Attentive; Tag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "2ba1bab7-386f-4a81-8b98-2339a31d3d22", "Name": "__attentive_dv", "Host": "athleta.gap.com", "IsSession": false, "Length": "365", "description": "Updated from Unknown to Category 2. <PERSON><PERSON> is included for Consent in Tealium; Tag Title(s) : Attentive - All - Gap US,Attentive - Confirmation - Gap Products,Attentive - All [GPFS]; UID(s) : 971,972,409; Vendor(s): Attentive; Tag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "43322db9-9fc7-4160-8278-8cbacae49cb9", "Name": "__attentive_id", "Host": "athleta.gap.com", "IsSession": false, "Length": "21899", "description": "Updated from Unknown to Category 2. <PERSON><PERSON> is included for Consent in Tealium; Tag Title(s) : Attentive - All - Gap US,Attentive - Confirmation - Gap Products,Attentive - All [GPFS]; UID(s) : 971,972,409; Vendor(s): Attentive; Tag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "bc04ab0b-10eb-42ad-bd94-6f2091572571", "Name": "__attentive_pv", "Host": "athleta.gap.com", "IsSession": false, "Length": "365", "description": "Updated from Unknown to Category 2. <PERSON><PERSON> is included for Consent in Tealium; Tag Title(s) : Attentive - All - Gap US,Attentive - Confirmation - Gap Products,Attentive - All [GPFS]; UID(s) : 971,972,409; Vendor(s): Attentive; Tag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "042981ac-3b6e-496c-abd1-75c705581177", "Name": "__attentive_ss_referrer", "Host": "athleta.gap.com", "IsSession": false, "Length": "365", "description": "Updated from Unknown to Category 2. <PERSON><PERSON> is included for Consent in Tealium; Tag Title(s) : Attentive - All - Gap US,Attentive - Confirmation - Gap Products,Attentive - All [GPFS]; UID(s) : 971,972,409; Vendor(s): Attentive; Tag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "cb48b77f-7258-4561-bb87-87cc893f8e9c", "Name": "_attn_", "Host": "athleta.gap.com", "IsSession": false, "Length": "3649", "description": "Updated from Unknown to Category 2. <PERSON><PERSON> is included for Consent in Tealium; Tag Title(s) : Attentive - All - Gap US,Attentive - Confirmation - Gap Products,Attentive - All [GPFS]; UID(s) : 971,972,409; Vendor(s): Attentive; Tag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "0901d924-7038-4758-beb6-d211dde934b7", "Name": "_fmSession", "Host": "gap.com", "IsSession": false, "Length": "365", "description": "Changing from Unknown to Category 2 based on internal GAP feedback.::\nVendor: FIndMine\nUID: 460 and 461", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "455e1539-822b-4158-a4b9-e2ab3269da63", "Name": "_gcl_au", "Host": ".gapcanada.ca", "IsSession": false, "Length": "1095", "description": "Manually added this cookie based on _gcl*** cookies.\nGoogle conversion tracking cookie; Tag Title(s) : Google AdWords - GOL Confirmation,Google AdWords - Confirmation - Core,Google Cookie Matching Service - All,Google - All - PGM (gtag.js),Google - All - AdWords (gtag.js),Google - PHD - AT<PERSON> Girl (gtag.js),Google AdWords - BOPIS - All,Google - Loyalty Signup - AdWords (gtag.js) Copy 1,Google Ads - BOPIS - OCP,Google Ads - All - GID Conversion Pixel,Google Ads - Loyalty Signup,Google - All - PHD/DCM  (gtag.js),Google - ONOL Confirmation - unknown owner,Google - All - gtag.js\n; UID(s) : 366,368,450,527,565,603,872,1160,1192,1194,1195,138,166,203,212,479,176,209,311,318,571\n; Vendor(s): Google\n; Tag Container(s):Google AdWords Conversion,Tealium Generic Tag,Google Cookie Matching Service for Doubleclick,DoubleClick Floodlight (gtag.js),Google Ads Conversion Tracking &amp; Remarketing (gtag.js),Floodlight (gtag.js),Google AdWords Remarketing", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "d970cdc3-dcdb-4ab5-9e7d-e7e0a3d9afc6", "Name": "_rdt_uuid", "Host": "gap.com", "IsSession": false, "Length": "89", "description": "Vendor: Reddit\nUID:1236", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "124302ce-dafc-4668-82c9-e50c0a8ee40a", "Name": "_schn1", "Host": "gap.com", "IsSession": false, "Length": "365", "description": "This cookie is associated with Snapchat. It tracks individual sessions on the website, allowing the website to compile statistical data from multiple visits. This data can also be used to create leads for marketing purposes.; Tag Title(s) : SnapChat - All - Social Code - NOT BROL\n; UID(s) : 811\n; Vendor(s): SnapChat\n; Tag Container(s):Tealium Custom Container", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "e6e308f1-be0e-4a76-bc35-5a792ad9f5ef", "Name": "_sctr", "Host": "gap.com", "IsSession": false, "Length": "395", "description": "Tag Title(s) : SnapChat - All - Social Code - NOT BROL\nUID(s) : 811\nVendor(s): SnapChat\nTag Container(s):Tealium Custom Container", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "3abbf4f0-fbce-4ec3-b305-495f11d94e05", "Name": "_svsid", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "Manually added this cookie based on tealium Doc.\nTag Title(s) : 4Cite - BR US - All \nUID(s) : 1120\nVendor(s): 4Cite\nTag Container(s):Tealium Custom Container\n", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "8da6dfe7-7cae-4d02-840b-cc6eee69556e", "Name": "_tfcUserCookie", "Host": "bananarepublic.gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 2 based on internal GAP feedback.; Tag Title(s) : TruFit - Confirmation - ONOL Tracking,TruFit - Confirmation - ONOL (STAGING ONLY),TruFit - Confirmation - GOL (STAGING ONLY),TruFit - Confirmation - BROL (STAGING ONLY),TruFit - Confirmation - ATOL (STAGING ONLY) ,TruFit - Confirmation - GOL,TruFit - Confirmation - BROL,TruFit - Confirmation - ATOL,TrueFit - Confirmation - GPFS,TrueFit - Confirmation - BRFS,TrueFit - Confirmation - ON CA,TrueFit - Confirmation - Gap CA,TrueFit - Confirmation - BR CA,TrueFit - Confirmation - AT CA\n; UID(s) : 569,624,650,651,652,705,706,707,267,268,374,375,376,441\n; Vendor(s): TruFit\n; Tag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "e4b73406-a608-4a06-869f-ca7045226698", "Name": "AEC", "Host": "google.com", "IsSession": false, "Length": "365", "description": "Manually adding this cookie and categorizing based on source.\nTag Title(s) : Google AdWords - GOL Confirmation,Google AdWords - Confirmation - Core,Google Cookie Matching Service - All,Google - All - PGM (gtag.js),Google - All - AdWords (gtag.js),Google - PHD - AT<PERSON> Girl (gtag.js),Google AdWords - BOPIS - All,Google - Loyalty Signup - AdWords (gtag.js) Copy 1,Google Ads - BOPIS - OCP,Google Ads - All - GID Conversion Pixel,Google Ads - Loyalty Signup,Google - All - PHD/DCM  (gtag.js),Google - ONOL Confirmation - unknown owner,Google - All - gtag.js\nUID(s) : 366,368,450,527,565,603,872,1160,1192,1194,1195,138,166,203,212,479,176,209,311,318,571\nVendor(s): Google\nTag Container(s):Google AdWords Conversion,Tealium Generic Tag,Google Cookie Matching Service for Doubleclick,DoubleClick Floodlight (gtag.js),Google Ads Conversion Tracking &amp; Remarketing (gtag.js),Floodlight (gtag.js),Google AdWords Remarketing", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "55b42f29-a715-4c68-9d1a-2fc913bec1fe", "Name": "AMCVS_93BE1C8B532956910A490D4D%40AdobeOrg", "Host": ".gapcanada.ca", "IsSession": true, "Length": "0", "description": " Manually added this cookie based on other AMCV cookies. ::\nTag Title(s) : Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service\n; UID(s) : 4,28,828\n; Vendor(s): Adobe\n; Tag Container(s):Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "eba05e97-16f5-4d67-bbbb-41ab67f66127", "Name": "amplitude_testgap.com", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 2 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "b7196c75-eb5e-4145-9a80-82bc83d9ef5c", "Name": "bc_invalidateUrlCache_targeting", "Host": "athleta.gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 2 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "3f96804f-4cb7-4f71-9946-ede4fa835219", "Name": "bluecoreNV", "Host": "athleta.gap.com", "IsSession": false, "Length": "347", "description": "Changing from Unknown to Category 2 based on internal GAP feedback. ::\nTag Title(s) : BlueCore - Confirmation, Bluecore - Purchase Event - Confirmation\nUID(s) : 345,1053,121,384,193,484\nVendor(s): BlueCore\nTag Container(s):Tealium Generic Tag, TriggerMail", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "f84e46c1-8251-4b90-8e93-d4da40020d4b", "Name": "c", "Host": "paypal.com", "IsSession": false, "Length": "365", "description": "Tag Title(s) : PayPal SDK - Bag - US/Factory,PayPal SDK - Product - US/Factory [not BR/BRFS]\nUID(s) : 1045,1066,371,391,479,496\nVendor(s): PayPal\nTag Container(s):Tealium Generic Tag", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "e14a3819-0089-45a8-bc87-62efd810b947", "Name": "fuiMatchMedia", "Host": "secure-www.gap.com", "IsSession": true, "Length": "0", "description": "FullStory cookie\nIn tealium Document\nUID: 867\n", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "7ae988e6-765f-441d-9c66-4a796258a80d", "Name": "fw_uid", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "Vendor name: Firework; CAT 2 for US;  UIDs: 1118, 1159", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "9e2c1d94-1cee-4cbe-9431-828877ccd2f4", "Name": "fw_utm", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "Vendor name: Firework; CAT 2 for US;  UIDs: 1118, 1159", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "b7fc1af7-75f7-471b-911b-17e60003a233", "Name": "fw_vrsn", "Host": "www.gap.com", "IsSession": false, "Length": "364", "description": "Vendor name: Firework; CAT 2 for US;  UIDs: 1118, 1159", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "63f219eb-3765-4a9a-8af6-08137dcbea3b", "Name": "GLOBALID", "Host": ".bttrack.com", "IsSession": false, "Length": "1095", "description": "Manually added this cookie based on bttrack.com host name.\nTag Title(s) : Bidtellect Conversion - All [US, Factory, CDA],Bidtellect - Analytics - All [US, Factory, CDA],Bidtellect - Retargeting - All [US, Factory, CDA]\nUID(s) : 817,820,824,279,280,286,384,385,390\nVendor(s): Bidtellect\nTag Container(s):Tealium Generic Tag,Bidtellect Conversion\n", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "a35b1ec0-8949-4a76-9611-eb24b633a687", "Name": "mp_atus_mixpanel", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 2.  <PERSON><PERSON> exists in Tealium. \"; Tag Title(s) : BlueCore - Confirmation, Bluecore - Purchase Event - Confirmation\n; UID(s) : 345,1053,121,384,193,483\n; Vendor(s): <PERSON>Core\n; Tag Container(s):Tealium Generic Tag, TriggerMail;\"", "thirdPartyDescription": "Used to identify individual users. This tool is used to measure site performance and usage patterns", "patternKey": "mp_", "thirdPartyKey": "Pattern|mp_", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "0f765bac-1cf2-4d54-a37f-6d5be2dfbd9c", "Name": "mp_brus_mixpanel", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 2.  <PERSON><PERSON> exists in Tealium. \"; Tag Title(s) : BlueCore - Confirmation, Bluecore - Purchase Event - Confirmation\n; UID(s) : 345,1053,121,384,193,483\n; Vendor(s): <PERSON>Core\n; Tag Container(s):Tealium Generic Tag, TriggerMail;\"", "thirdPartyDescription": "Used to identify individual users. This tool is used to measure site performance and usage patterns", "patternKey": "mp_", "thirdPartyKey": "Pattern|mp_", "firstPartyKey": "Pattern|mp_", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "90f096e0-8925-4433-a6aa-e33728e852e6", "Name": "mp_gpus_mixpanel", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "cde04aeb-a418-4924-bc34-61e37d88e6c0", "Name": "mp_onus_mixpanel", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 2.  <PERSON><PERSON> exists in Tealium ::\nTag Title(s) : BlueCore - Confirmation, Bluecore - Purchase Event - Confirmation\nUID(s) : 345,1053,121,384,193,483\nVendor(s): BlueCore\nTag Container(s):Tealium Generic Tag, TriggerMail", "thirdPartyDescription": "Used to identify individual users. This tool is used to measure site performance and usage patterns", "patternKey": "mp_", "thirdPartyKey": "Pattern|mp_", "firstPartyKey": "Pattern|mp_", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "78de66f5-8ce9-479c-94bf-09db5f164d01", "Name": "TEST_AMCV_COOKIE_WRITE", "Host": "gap.com", "IsSession": true, "Length": "0", "description": "Tag Title(s) : Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service\n; UID(s) : 4,28,828\n; Vendor(s): Adobe\n; Tag Container(s):Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service\"", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "4aa5500c-5d3d-4dc7-ab8a-464b788e4609", "Name": "tfcAnalytics", "Host": "bananarepublic.gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 2  based on internal GAP feedback::\nTag Title(s) : TruFit - Confirmation - ONOL Tracking,TruFit - Confirmation - ONOL (STAGING ONLY),TruFit - Confirmation - GOL (STAGING ONLY),TruFit - Confirmation - <PERSON>OL (STAGING ONLY),TruFit - Confirmation - ATOL (STAGING ONLY) ,TruFit - Confirmation - GOL,TruFit - Confirmation - BROL,TruFit - Confirmation - ATOL,TrueFit - Confirmation - GPFS,TrueFit - Confirmation - BRFS,TrueFit - Confirmation - ON CA,TrueFit - Confirmation - Gap CA,TrueFit - Confirmation - BR CA,TrueFit - Confirmation - AT CA\nUID(s) : 569,624,650,651,652,705,706,707,267,268,374,375,376,441\nVendor(s): TruFit\nTag Container(s): Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "2abb2cfc-6b5a-4fcc-9a0a-eff1efd04ffc", "Name": "tfc-s", "Host": "bananarepublic.gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 2  based on internal GAP feedback::\nTag Title(s) : TruFit - Confirmation - ONOL Tracking,TruFit - Confirmation - ONOL (STAGING ONLY),TruFit - Confirmation - GOL (STAGING ONLY),TruFit - Confirmation - <PERSON>OL (STAGING ONLY),TruFit - Confirmation - ATOL (STAGING ONLY) ,TruFit - Confirmation - GOL,TruFit - Confirmation - BROL,TruFit - Confirmation - ATOL,TrueFit - Confirmation - GPFS,TrueFit - Confirmation - BRFS,TrueFit - Confirmation - ON CA,TrueFit - Confirmation - Gap CA,TrueFit - Confirmation - BR CA,TrueFit - Confirmation - AT CA\nUID(s) : 569,624,650,651,652,705,706,707,267,268,374,375,376,441\nVendor(s): TruFit\nTag Container(s): Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "c890f07a-df6d-4418-9a25-346d4b51af96", "Name": "tfcUserVisit", "Host": "bananarepublic.gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 2 based on internal GAP feedback.::\nTag Title(s) : TruFit - Confirmation - ONOL Tracking,TruFit - Confirmation - ONOL (STAGING ONLY),TruFit - Confirmation - GOL (STAGING ONLY),TruFit - Confirmation - <PERSON><PERSON> (STAGING ONLY),TruFit - Confirmation - ATOL (STAGING ONLY) ,TruFit - Confirmation - GOL,TruFit - Confirmation - BROL,TruFit - Confirmation - ATOL,TrueFit - Confirmation - GPFS,TrueFit - Confirmation - BRFS,TrueFit - Confirmation - ON CA,TrueFit - Confirmation - Gap CA,TrueFit - Confirmation - BR CA,TrueFit - Confirmation - AT CA\nUID(s) : 569,624,650,651,652,705,706,707,267,268,374,375,376,441\nVendor(s): TruFit\nTag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "21a5a00b-27ea-46e4-9121-e9fd08fe5046", "Name": "tfpai", "Host": "www.gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 2 based on internal GAP feedback.::\nTag Title(s) : Teads - All - US Factory, CA\nUID(s) : 419, 524\nVendor(s): Teads\nTag Container(s):Teads Advertiser Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "60d057a5-76fe-4704-8e63-7794c00630ff", "Name": "tfpsi", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "Changed from Unknown to Category 2.  <PERSON><PERSON> exists in Tealium\nUID = 419\nVENDOR = TEADS\nTAG TITLE = Teads - All - US Factory, CA\n", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "a170afcd-d618-4fee-901d-dc954c7a74e9", "Name": "tfpvi", "Host": "gap.com", "IsSession": false, "Length": "365", "description": "Teads Vendor in Tealium\nUID 1132,419,524", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "c080e4c0-d65a-4933-962c-07b90d29dc7b", "Name": "TLTSID", "Host": "paypal.com", "IsSession": false, "Length": "365", "description": "Manually adding this cookie and categorizing based on source.\nTag Title(s) : PayPal SDK - Bag - US/Factory,PayPal SDK - Product - US/Factory [not BR/BRFS]\nUID(s) : 1045,1066,371,391,479,496\nVendor(s): PayPal\nTag Container(s):Tealium Generic Tag\n", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "edada0e2-07e5-4a28-81c2-a9613b4d1752", "Name": "tt_viewer", "Host": "teads.tv", "IsSession": false, "Length": "1095", "description": "Manually added this cookie based on teads.tv host name.\nChanging from Unknown to Category 2 based on internal GAP feedback.::\nTag Title(s) : Teads - All - US Factory, CA\nUID(s) : 419, 524\nVendor(s): Teads\nTag Container(s):Teads Advertiser Tag", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}], "Hosts": [{"HostName": "t.co", "DisplayName": "t.co", "HostId": "H216", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "muc_ads", "Host": "t.co", "IsSession": false, "Length": "729", "description": "This domain is owned by Twitter. It is used as a URL shortening service, which allows Twitter to collect data on how links are shared.", "thirdPartyDescription": "This domain is owned by Twitter. It is used as a URL shortening service, which allows Twitter to collect data on how links are shared.", "patternKey": null, "thirdPartyKey": "Cookie|t.co", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "pixel.lilystyle.ai", "DisplayName": "pixel.lilystyle.ai", "HostId": "H109", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "LilySessionBegins", "Host": "pixel.lilystyle.ai", "IsSession": false, "Length": "729", "description": "Changed from Unknown to Category 2. <PERSON><PERSON> is in Tealium. \"; Tag Title(s) : Lily AI - US, Factory, Canada\n; UID(s) : 1124, 413, 519\n; Vendor(s): <PERSON>\n; Tag Container(s):Tealium Custom Container\"", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "8a460af8-aa8d-4584-8bdf-5df6a6bf6239", "Name": "LilySessionExpires", "Host": "pixel.lilystyle.ai", "IsSession": false, "Length": "729", "description": "Changed from Unknown to Category 2. <PERSON><PERSON> is in Tealium. ; Tag Title(s) : Lily AI - US, Factory, Canada\n; UID(s) : 1124, 413, 519\n; Vendor(s): <PERSON>\n; Tag Container(s):Tealium Custom Container", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "62fc3724-377a-44fc-b6d1-ab3ebaad63ee", "Name": "LilySessionID", "Host": "pixel.lilystyle.ai", "IsSession": false, "Length": "729", "description": "Changed from Unknown to Category 2. <PERSON><PERSON> is in Tealium. \"; Tag Title(s) : Lily AI - US, Factory, Canada\n; UID(s) : 1124, 413, 519\n; Vendor(s): <PERSON>\n; Tag Container(s):Tealium Custom Container\"", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "5e12259b-020b-4e24-b584-cf97b9ef3cca", "Name": "lpid", "Host": "pixel.lilystyle.ai", "IsSession": false, "Length": "729", "description": "Changed from Unknown to Category 2. <PERSON><PERSON> is in Tealium\nUID: 519\n", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "bidswitch.net", "DisplayName": "bidswitch.net", "HostId": "H185", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "c", "Host": "bidswitch.net", "IsSession": false, "Length": "364", "description": "This domain is owned by IPONWEB and is used to provide a real time bidding platform for online advertising.", "thirdPartyDescription": "This domain is owned by IPONWEB and is used to provide a real time bidding platform for online advertising.", "patternKey": null, "thirdPartyKey": "Cookie|bidswitch.net", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "e936fbd3-649c-4bd3-8d6f-eb944ec4ccd3", "Name": "tuuid", "Host": "bidswitch.net", "IsSession": false, "Length": "364", "description": "This domain is owned by IPONWEB and is used to provide a real time bidding platform for online advertising.", "thirdPartyDescription": "This domain is owned by IPONWEB and is used to provide a real time bidding platform for online advertising.", "patternKey": null, "thirdPartyKey": "Cookie|bidswitch.net", "firstPartyKey": "Cookietuuid", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "5d6bd741-61c3-435e-a8ea-c15fd3b19b8a", "Name": "tuuid_lu", "Host": "bidswitch.net", "IsSession": false, "Length": "364", "description": "This domain is owned by IPONWEB and is used to provide a real time bidding platform for online advertising.", "thirdPartyDescription": "This domain is owned by IPONWEB and is used to provide a real time bidding platform for online advertising.", "patternKey": null, "thirdPartyKey": "Cookie|bidswitch.net", "firstPartyKey": "Cookietuuid_lu", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "attentivemobile.com", "DisplayName": "attentivemobile.com", "HostId": "H140", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "tpc_id", "Host": "attentivemobile.com", "IsSession": false, "Length": "730", "description": "Tag Title(s) : Attentive - All - Gap US,Attentive - Confirmation - Gap Products,Attentive - All [GPFS]\nUID(s) : 971,972,409\nVendor(s): Attentive\nTag Container(s):Tealium Generic Tag\n", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "ct.pinterest.com", "DisplayName": "ct.pinterest.com", "HostId": "H214", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "8d0ea7a0-43a5-4991-865f-b255140ee<PERSON>da", "Name": "_pinterest_ct_ua", "Host": "ct.pinterest.com", "IsSession": false, "Length": "364", "description": "This domain is associated with Pinterest, a social media platform that allows users to share images and media content through collections known as pinboards. The domain is likely used for conversion tracking and advertising purposes.", "thirdPartyDescription": "This domain is associated with Pinterest, a social media platform that allows users to share images and media content through collections known as pinboards. The domain is likely used for conversion tracking and advertising purposes.", "patternKey": null, "thirdPartyKey": "Cookie|ct.pinterest.com", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "p.teads.tv", "DisplayName": "p.teads.tv", "HostId": "H64", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "tfpai", "Host": "p.teads.tv", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 2 based on internal GAP feedback.::\nTag Title(s) : Teads - All - US Factory, CA\nUID(s) : 419, 524\nVendor(s): Teads\nTag Container(s):Teads Advertiser Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "0944aecd-6152-456c-ba62-24146adecb75", "Name": "tfpsi", "Host": "p.teads.tv", "IsSession": false, "Length": "0", "description": "Changed from Unknown to Category 2.  <PERSON><PERSON> exists in Tealium\nUID = 419\nVENDOR = TEADS\nTAG TITLE = Teads - All - US Factory, CA\n", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "d5c2f90a-2e37-4639-93c4-89b33dc4f054", "Name": "tfpvi", "Host": "p.teads.tv", "IsSession": false, "Length": "365", "description": "Teads Vendor in Tealium\nUID 1132,419,524", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 365, "category": null, "isThirdParty": false}]}, {"HostName": "pubmatic.com", "DisplayName": "pubmatic.com", "HostId": "H212", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "PugT", "Host": "pubmatic.com", "IsSession": false, "Length": "29", "description": "This domain is owned by Pubmatic. It operates an advertising exchange platform where online publishers can sell targeted advertising space to media buyers using real time bidding.", "thirdPartyDescription": "This domain is owned by Pubmatic. It operates an advertising exchange platform where online publishers can sell targeted advertising space to media buyers using real time bidding.", "patternKey": null, "thirdPartyKey": "Cookie|pubmatic.com", "firstPartyKey": "CookiePugT", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "cdn.attn.tv", "DisplayName": "cdn.attn.tv", "HostId": "H74", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "__attentive_cco", "Host": "cdn.attn.tv", "IsSession": false, "Length": "21899", "description": "Updated from Unknown to Category 2; <PERSON><PERSON> is included for Consent in Tealium;\n\nTag Title(s) : Attentive - All - Gap US,Attentive - Confirmation - Gap Products,Attentive - All [GPFS];\nUID(s) : 971,972,409;\nVendor(s): Attentive;\nTag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "bad0dee0-fb7b-4f0d-90e4-2fe088272c2c", "Name": "__attentive_id", "Host": "cdn.attn.tv", "IsSession": false, "Length": "21899", "description": "Changed from Unknown to Category 2.  <PERSON><PERSON> exists in Tealium; Tag Title(s) : Attentive - All - Gap US,Attentive - Confirmation - Gap Products,Attentive - All [GPFS];\nUID(s) : 971,972,409;\nVendor(s): Attentive;\nTag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "f2d72ca2-8b49-4eea-9e50-8db8806d0dd7", "Name": "__attentive_pv", "Host": "cdn.attn.tv", "IsSession": false, "Length": "0", "description": "Updated from Unknown to Category 2.  <PERSON><PERSON> is included for Consent in Tealium; Tag Title(s) : Attentive - All - Gap US,Attentive - Confirmation - Gap Products,Attentive - All [GPFS];\nUID(s) : 971,972,409;\nVendor(s): Attentive;\nTag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "25e8e5c9-c16c-4485-9f46-97cefd23adf8", "Name": "__attentive_ss_referrer", "Host": "cdn.attn.tv", "IsSession": false, "Length": "0", "description": "Changed from Unknown to Category 2.  <PERSON><PERSON> exists in Tealium ; Tag Title(s) : Attentive - All - Gap US,Attentive - Confirmation - Gap Products,Attentive - All [GPFS];\nUID(s) : 971,972,409;\nVendor(s): Attentive;\nTag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "truefitcorp.com", "DisplayName": "truefitcorp.com", "HostId": "H20", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "prod1-tf-prod-86479863pn", "Host": "truefitcorp.com", "IsSession": true, "Length": "0", "description": "Vendor exists in Tealium\nVendor:truefit\n ONCANADA  UID: 374\nGAPCANADA  UID: 375\nBRCANADA UID: 376\nATCANADA UID: 441\n \n", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "6c84cee8-f31c-4388-b39b-88e3ba0842b9", "Name": "tfcuser", "Host": "truefitcorp.com", "IsSession": false, "Length": "727", "description": "user", "thirdPartyDescription": "user", "patternKey": "user", "thirdPartyKey": "Pattern|user", "firstPartyKey": "Pattern|user", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "h.online-metrix.net", "DisplayName": "h.online-metrix.net", "HostId": "H167", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "thx_global_guid", "Host": "h.online-metrix.net", "IsSession": false, "Length": "1799", "description": "Changing from Unknown to Category 2. ::  thx_guid exists in tealium. Assuming same tag info for this cookie.\nTag Title(s) : Bing - All - US,CA,Factory\nUID(s) : 703,247,354\nVendor(s): Bing\nTag Container(s):Bing Ads Universal Event Tracking (UET)", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "nebula-cdn.kampyle.com", "DisplayName": "nebula-cdn.kampyle.com", "HostId": "H47", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "kampyleInvitePresented", "Host": "nebula-cdn.kampyle.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 2 based on internal GAP feedback.::\nTag Title(s) : Medallia- All,Medallia - Feedback Link Injection,Medallia - Confirmation,Medallia - All,Medallia - Confirmation - AT only,Medallia - Confirmation [CA ONLY]\nUID(s) : 498,512,721,190,251,282,357,440,527\nVendor(s): Medallia\nTag Container(s):Tealium Generic Tag, Tealium Custom Container", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "6a79526b-e542-4569-b24c-32adb017c3b1", "Name": "kampyleUserPercentile", "Host": "nebula-cdn.kampyle.com", "IsSession": false, "Length": "364", "description": "Manual Categorization from Unknown to Category 2.  <PERSON><PERSON> exists in Tealium.; Tag Title(s) : Medallia- All,Medallia - Feedback Link Injection,Medallia - Confirmation,Medallia - All,Medallia - Confirmation - AT only,Medallia - Confirmation [CA ONLY]\n; UID(s) : 498,512,721,190,251,282,357,440,527\n; Vendor(s): Medallia\n; Tag Container(s):Tealium Generic Tag, Tealium Custom Container", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "twitter.com", "DisplayName": "twitter.com", "HostId": "H67", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "personalization_id", "Host": "twitter.com", "IsSession": false, "Length": "729", "description": "This domain is owned by Twitter. The main business activity is: Social Networking Services.  Where twitter acts as a third party host, it collects data through a range of plug-ins and integrations, that is primarily used for tracking and targeting.", "thirdPartyDescription": "This domain is owned by Twitter. The main business activity is: Social Networking Services.  Where twitter acts as a third party host, it collects data through a range of plug-ins and integrations, that is primarily used for tracking and targeting.", "patternKey": null, "thirdPartyKey": "Cookie|twitter.com", "firstPartyKey": "Cookiepersonalization_id", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "paypal.com", "DisplayName": "paypal.com", "HostId": "H28", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "KHcl0EuY7AKSMgfvHl7J5E7hPtK", "Host": "paypal.com", "IsSession": false, "Length": "1095", "description": "Manually added this cookie based on hostname (Paypal)::\nTag Title(s) : PayPal SDK - Bag - US/Factory,PayPal SDK - Product - US/Factory [not BR/BRFS]\nUID(s) : 1045,1066,371,391,479,496\nVendor(s): PayPal\nTag Container(s):Tealium Generic Tag\n", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "295df9f2-5820-49fc-b1e4-9cfc81bf808c", "Name": "sc_f", "Host": "paypal.com", "IsSession": false, "Length": "1095", "description": "Manually added this cookie based on hostname (Paypal)::\nTag Title(s) : PayPal SDK - Bag - US/Factory,PayPal SDK - Product - US/Factory [not BR/BRFS]\nUID(s) : 1045,1066,371,391,479,496\nVendor(s): PayPal\nTag Container(s):Tealium Generic Tag\n", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}]}, {"HostName": "doubleclick.net", "DisplayName": "doubleclick.net", "HostId": "H56", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "IDE", "Host": "doubleclick.net", "IsSession": false, "Length": "389", "description": "This domain is owned by Doubleclick (Google). The main business activity is: Doubleclick is Googles real time bidding advertising exchange", "thirdPartyDescription": "This domain is owned by Doubleclick (Google). The main business activity is: Doubleclick is Googles real time bidding advertising exchange", "patternKey": null, "thirdPartyKey": "Cookie|doubleclick.net", "firstPartyKey": "<PERSON><PERSON>", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "197bfbfc-4208-419f-bfa9-2effb84c3e9d", "Name": "test_cookie", "Host": "doubleclick.net", "IsSession": false, "Length": "0", "description": "This domain is owned by Doubleclick (Google). The main business activity is: Doubleclick is Googles real time bidding advertising exchange", "thirdPartyDescription": "This domain is owned by Doubleclick (Google). The main business activity is: Doubleclick is Googles real time bidding advertising exchange", "patternKey": null, "thirdPartyKey": "Cookie|doubleclick.net", "firstPartyKey": "Cookietest_cookie", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "triggeredmail.appspot.com", "DisplayName": "triggeredmail.appspot.com", "HostId": "H57", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "bc_invalidateUrlCache_targeting", "Host": "triggeredmail.appspot.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 2 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "f82ad0b7-670f-45b6-8c4e-07aa4ef72990", "Name": "mp_brus_mixpanel", "Host": "triggeredmail.appspot.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 2.  <PERSON><PERSON> exists in Tealium. \"; Tag Title(s) : BlueCore - Confirmation, Bluecore - Purchase Event - Confirmation\n; UID(s) : 345,1053,121,384,193,483\n; Vendor(s): <PERSON>Core\n; Tag Container(s):Tealium Generic Tag, TriggerMail;\"", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "7fb9863b-537f-40ea-8dad-5886ab07260a", "Name": "mp_gpus_mixpanel", "Host": "triggeredmail.appspot.com", "IsSession": false, "Length": "0", "description": "", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "kargo.com", "DisplayName": "kargo.com", "HostId": "H299", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "ktcid", "Host": "kargo.com", "IsSession": false, "Length": "364", "description": "Testing", "thirdPartyDescription": "Testing", "patternKey": null, "thirdPartyKey": "Cookie|kargo.com", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "adsrvr.org", "DisplayName": "adsrvr.org", "HostId": "H193", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "TDCPM", "Host": "adsrvr.org", "IsSession": false, "Length": "365", "description": "This domain is owned by TheTradeDesk. The main business activity is: Ad Serving Platform", "thirdPartyDescription": "This domain is owned by TheTradeDesk. The main business activity is: Ad Serving Platform", "patternKey": null, "thirdPartyKey": "Cookie|adsrvr.org", "firstPartyKey": "CookieTDCPM", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "ea1b4355-c252-4625-ba00-fc525a4818d7", "Name": "TDID", "Host": "adsrvr.org", "IsSession": false, "Length": "365", "description": "This domain is owned by TheTradeDesk. The main business activity is: Ad Serving Platform", "thirdPartyDescription": "This domain is owned by TheTradeDesk. The main business activity is: Ad Serving Platform", "patternKey": null, "thirdPartyKey": "Cookie|adsrvr.org", "firstPartyKey": "CookieTDID", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "sc-static.net", "DisplayName": "sc-static.net", "HostId": "H68", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "X-AB", "Host": "sc-static.net", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 2 based on internal GAP feedback.\nVendor: Snapchat\nUID: 380", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "paypalobjects.com", "DisplayName": "paypalobjects.com", "HostId": "H271", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "paypal-offers--cust", "Host": "paypalobjects.com", "IsSession": false, "Length": "0", "description": "Tag Title(s) : PayPal SDK - Bag - US/Factory,PayPal SDK - Product - US/Factory [not BR/BRFS]\nUID(s) : 1045,1066,371,391,479,496\nVendor(s): PayPal\nTag Container(s):Tealium Generic Tag\n", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}], "PurposeId": "A78CDDDB-0189-49E2-BE72-365078F4B744", "CustomGroupId": "C0015", "GroupId": "8833c17b-d369-476a-8cbc-a6ae4920a806", "Status": "active", "IsDntEnabled": false, "Type": "COOKIE", "DescriptionLegal": "", "IabIllustrations": [], "HasLegIntOptOut": false, "HasConsentOptOut": true, "IsGpcEnabled": true, "VendorServices": null, "TrackingTech": null}, {"ShowInPopup": true, "ShowInPopupNonIAB": true, "ShowSDKListLink": true, "Order": "1", "OptanonGroupId": "SPD_BG", "Parent": "", "ShowSubgroup": false, "ShowSubGroupDescription": false, "ShowSubgroupToggle": false, "AlwaysShowCategory": false, "GroupDescription": "<ul style=\"font-size: 12px; margin-top: 0; padding-left: 20px;\">     <li>If you have an account, sign in first, and we’ll remember your preference when you’re signed in.</li>     <li>Your preference is tied to your current browser or device. If you’re not signed in, use a different browser or device to visit our sites, or disable certain cookies, you may need to reset your preferences.</li>     <li>Even if you opt-out, you may still see our ads, but they won’t be personalized for you.</li>     <li>You can also opt-out of Online Info sharing by setting the Global Privacy Control in your browser before visiting our sites. Learn more at globalprivacycontrol.org. Once set, you’ll see “Browser Opt-Out Honored” at the top of this Page.</li> </ul>  <p style=\"font-size: 12px;\">     <span style=\"color: black;\"><strong>Step 2:</strong></span> To opt-out of <span style=\"color: black;\"><strong>Offline Info</strong></span> sharing, please fill out <a href=\"https://privacyportal.onetrust.com/webform/************************************/5069878c-b35b-463f-a1ee-b8d846c30070\" target=\"_blank\" style=\"color: #007bff; font-weight: bold;\" rel=\"nofollow noopener noreferrer\">this Webform</a>. If you start with Step 1, just click the <span style=\"color: black;\"><strong>Your Privacy Choices</strong></span> link at the bottom of our site afterwards to return and complete Step 2. </p>  <p style=\"font-size: 12px;\">     Check our <a href=\"https://www.gapinc.com/en-us/consumer-privacy-policy\" target=\"_blank\" rel=\"nofollow noopener noreferrer\">Privacy Policy</a> for more information, including the <span style=\"color: black;\"><strong>Your Rights and Choices</strong></span> section on how to make requests to know, delete, or correct your information. </p>", "GroupDescriptionOTT": "<ul style=\"font-size: 12px; margin-top: 0; padding-left: 20px;\">     <li>If you have an account, sign in first, and we’ll remember your preference when you’re signed in.</li>     <li>Your preference is tied to your current browser or device. If you’re not signed in, use a different browser or device to visit our sites, or disable certain cookies, you may need to reset your preferences.</li>     <li>Even if you opt-out, you may still see our ads, but they won’t be personalized for you.</li>     <li>You can also opt-out of Online Info sharing by setting the Global Privacy Control in your browser before visiting our sites. Learn more at globalprivacycontrol.org. Once set, you’ll see “Browser Opt-Out Honored” at the top of this Page.</li> </ul>  <p style=\"font-size: 12px;\">     <span style=\"color: black;\"><strong>Step 2:</strong></span> To opt-out of <span style=\"color: black;\"><strong>Offline Info</strong></span> sharing, please fill out <a href=\"https://privacyportal.onetrust.com/webform/************************************/5069878c-b35b-463f-a1ee-b8d846c30070\" target=\"_blank\" style=\"color: #007bff; font-weight: bold;\" rel=\"nofollow noopener noreferrer\">this Webform</a>. If you start with Step 1, just click the <span style=\"color: black;\"><strong>Your Privacy Choices</strong></span> link at the bottom of our site afterwards to return and complete Step 2. </p>  <p style=\"font-size: 12px;\">     Check our <a href=\"https://www.gapinc.com/en-us/consumer-privacy-policy\" target=\"_blank\" rel=\"nofollow noopener noreferrer\">Privacy Policy</a> for more information, including the <span style=\"color: black;\"><strong>Your Rights and Choices</strong></span> section on how to make requests to know, delete, or correct your information. </p>", "GroupNameMobile": "Step 1: <span style=\"color:black\"><strong>To opt-out of</strong></span> Online Info <span style=\"color:black\"><strong>sharing, toggle the button to the left and click</strong></span> Submit<span style=\"color:black\"><strong>.</strong></span> ", "GroupNameOTT": "Step 1: <span style=\"color:black\"><strong>To opt-out of</strong></span> Online Info <span style=\"color:black\"><strong>sharing, toggle the button to the left and click</strong></span> Submit<span style=\"color:black\"><strong>.</strong></span> ", "GroupName": "Step 1: <span style=\"color:black\"><strong>To opt-out of</strong></span> Online Info <span style=\"color:black\"><strong>sharing, toggle the button to the left and click</strong></span> Submit<span style=\"color:black\"><strong>.</strong></span> ", "IsIabPurpose": false, "GeneralVendorsIds": [], "FirstPartyCookies": [], "Hosts": [], "PurposeId": "", "CustomGroupId": "SPD_BG", "GroupId": "23b73a54-0717-4446-8785-d63a63e405f3", "Status": "active", "IsDntEnabled": false, "Type": "BRANCH", "DescriptionLegal": "", "IabIllustrations": [], "HasLegIntOptOut": false, "HasConsentOptOut": true, "IsGpcEnabled": true, "VendorServices": [], "TrackingTech": {"SessionStorages": [], "LocalStorages": []}}, {"ShowInPopup": true, "ShowInPopupNonIAB": true, "ShowSDKListLink": true, "Order": "2", "OptanonGroupId": "C0001", "Parent": "BG149", "ShowSubgroup": true, "ShowSubGroupDescription": true, "ShowSubgroupToggle": false, "AlwaysShowCategory": false, "GroupDescription": "These cookies are essential for the website to function properly and cannot be switched off. ", "GroupDescriptionOTT": "These cookies are essential for the website to function properly and cannot be switched off. ", "GroupNameMobile": "Essential Cookies", "GroupNameOTT": "Essential Cookies", "GroupName": "Essential Cookies", "IsIabPurpose": false, "GeneralVendorsIds": [], "FirstPartyCookies": [{"id": "018f08e7-d6ae-7712-a36d-e1f1b7c4a0cc", "Name": "__tvpa", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "This cookie is generated by an integration between the Beacon API and Google Tag Manager (GTM) to enhance data tracking capabilities for web applications. The Beacon API is specifically designed to handle situations where data needs to be sent from the browser to the server when a page session is ending. It ensures that data is sent in a non-blocking and reliable manner, unlike traditional AJAX requests that might not complete if the page is unloading.", "thirdPartyDescription": "This cookie is generated by an integration between the Beacon API and Google Tag Manager (GTM) to enhance data tracking capabilities for web applications. The Beacon API is specifically designed to handle situations where data needs to be sent from the browser to the server when a page session is ending. It ensures that data is sent in a non-blocking and reliable manner, unlike traditional AJAX requests that might not complete if the page is unloading.", "patternKey": "__t", "thirdPartyKey": "Pattern|__t", "firstPartyKey": "Pattern|__t", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "bd979851-3614-4121-b025-190e6bc71105", "Name": "_tldtest_xxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "Host": "gap.com", "IsSession": true, "Length": "0", "description": "Amplitude", "thirdPartyDescription": "Amplitude", "patternKey": "_tldtest_", "thirdPartyKey": "Pattern|_tldtest_", "firstPartyKey": "Pattern|_tldtest_", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "33a8e2d2-df8a-43b3-b52a-27e1016db377", "Name": "ak_bmsc", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "This cookie is associated with Akamai and is used to differentiate between traffic from humans and bots.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookieak_bmsc", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "0c217899-5d75-48f1-9c15-bf5575f7b7ce", "Name": "amp_cookie_test", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "These cookies are used by Amplitude for session tracking for analytics purposes.", "thirdPartyDescription": "These cookies are used by Amplitude for session tracking for analytics purposes.", "patternKey": "amp_cookie_test", "thirdPartyKey": "Pattern|amp_cookie_test", "firstPartyKey": "Pattern|amp_cookie_test", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "148ec4b6-3dad-4f7c-8ce1-41f4fd9d7030", "Name": "bm_mi", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "This cookie is associated with Akamai. It is used to analyse traffic to determine if it is automated traffic generated by bots or a human user.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookiebm_mi", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "2c4cbdb6-360a-4804-be99-cab6f02cd37e", "Name": "JSESSIONID", "Host": "gap.com", "IsSession": true, "Length": "0", "description": "General purpose platform session cookie, used by sites written in JSP. Usually used to maintain an anonymous user session by the server.::\nInfosec <PERSON>ie, Owner: Personalization\n", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieJSESSIONID", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "697af628-19d8-46ec-94c1-49a5361c67fb", "Name": "OptanonAlertBoxClosed", "Host": "gap.com", "IsSession": false, "Length": "1094", "description": "This cookie is set by websites using certain versions of the cookie law compliance solution from OneTrust.  It is set after visitors have seen a cookie information notice and in some cases only when they actively close the notice down.  It enables the website not to show the message more than once to a user.  The cookie has a one year lifespan and contains no personal information.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieOptanonAlertBoxClosed", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "03137e56-5515-47df-93c0-ae3088e13da4", "Name": "OptanonConsent", "Host": "gap.com", "IsSession": false, "Length": "1094", "description": "This cookie is set by the cookie compliance solution from OneTrust. It stores information about the categories of cookies the site uses and whether visitors have given or withdrawn consent for the use of each category. This enables site owners to prevent cookies in each category from being set in the users browser, when consent is not given. The cookie has a normal lifespan of one year, so that returning visitors to the site will have their preferences remembered. It contains no information that can identify the site visitor.\n::\nTag Title(s) : OneTrust Container\nUID(s) : 1182,432,532,30\nVendor(s): OneTrust\nTag Container(s):Tealium Custom Container", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieOptanonConsent", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "aa12a0f4-dbc7-4ebf-9cc6-66f8cec6d74d", "Name": "utag_main", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "This cookie name is associated with the Tealium data platform and is used to enforce consent preferences across 3rd party tags. Within the cookie are several built-in values that keep track of the visitor session.::\nTag Title(s) : Tealium Collect - athleta CDH, Tealium Collect\nUID(s) : 1113,2,25\nVendor(s): Tealium\nTag Container(s): Tealium Collect", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookieutag_main", "DurationType": 1, "category": null, "isThirdParty": false}], "Hosts": [{"HostName": "draprpubsubtest.firebaseapp.com", "DisplayName": "draprpubsubtest.firebaseapp.com", "HostId": "H203", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "amp_cookie_test", "Host": "draprpubsubtest.firebaseapp.com", "IsSession": true, "Length": "0", "description": "These cookies are used by Amplitude for session tracking for analytics purposes.", "thirdPartyDescription": "These cookies are used by Amplitude for session tracking for analytics purposes.", "patternKey": "amp_cookie_test", "thirdPartyKey": "Pattern|amp_cookie_test", "firstPartyKey": "Pattern|amp_cookie_test", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "paypal.com", "DisplayName": "paypal.com", "HostId": "H28", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "ts", "Host": "paypal.com", "IsSession": false, "Length": "1095", "description": "This domain is owned by Paypal. The main business activity is: E-commerce Provision", "thirdPartyDescription": "This domain is owned by Paypal. The main business activity is: E-commerce Provision", "patternKey": null, "thirdPartyKey": "Cookie|paypal.com", "firstPartyKey": "Cook<PERSON>s", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "824ce026-f628-4ef7-ad39-2f3b0be543b8", "Name": "ts_c", "Host": "paypal.com", "IsSession": false, "Length": "1095", "description": "This domain is owned by Paypal. The main business activity is: E-commerce Provision", "thirdPartyDescription": "This domain is owned by Paypal. The main business activity is: E-commerce Provision", "patternKey": null, "thirdPartyKey": "Cookie|paypal.com", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "cf60f947-9357-4e41-b76a-914e1922f2a7", "Name": "tsrce", "Host": "paypal.com", "IsSession": false, "Length": "2", "description": "This domain is owned by Paypal. The main business activity is: E-commerce Provision", "thirdPartyDescription": "This domain is owned by Paypal. The main business activity is: E-commerce Provision", "patternKey": null, "thirdPartyKey": "Cookie|paypal.com", "firstPartyKey": "Cookietsrce", "DurationType": 1, "category": null, "isThirdParty": false}]}], "PurposeId": "3F950CD4-020C-4C80-B01D-95EF85881FF5", "CustomGroupId": "C0001", "GroupId": "98d53d76-5742-4f66-ad48-2467e9c42402", "Status": "always active", "IsDntEnabled": false, "Type": "COOKIE", "DescriptionLegal": "", "IabIllustrations": [], "HasLegIntOptOut": false, "HasConsentOptOut": true, "IsGpcEnabled": false, "VendorServices": null, "TrackingTech": null}, {"ShowInPopup": true, "ShowInPopupNonIAB": true, "ShowSDKListLink": true, "Order": "3", "OptanonGroupId": "C0003", "Parent": "BG149", "ShowSubgroup": true, "ShowSubGroupDescription": true, "ShowSubgroupToggle": false, "AlwaysShowCategory": false, "GroupDescription": "These cookies allow the website to provide enhanced functionality and personalization and provide you with a better user experience including remembering your preferences and login information. \n", "GroupDescriptionOTT": "These cookies allow the website to provide enhanced functionality and personalization and provide you with a better user experience including remembering your preferences and login information. \n", "GroupNameMobile": "Functional Cookies", "GroupNameOTT": "Functional Cookies", "GroupName": "Functional Cookies", "IsIabPurpose": false, "GeneralVendorsIds": [], "FirstPartyCookies": [{"id": "fb740dff-43a3-463f-84f5-96947bd5ca12", "Name": "__pr.8ofv5x", "Host": "gap.com", "IsSession": false, "Length": "365", "description": "Manually categorizing this to Category 2 based on email from <PERSON> on 4/19/2023 and <PERSON><PERSON> name : __pr.2nrbxi\n############\nTag Title(s) : Power Reviews - Product - US, Factory, CDA\nUID(s) : 373\nVendor(s): Power Reviews\nTag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "1914bb70-eb68-47ef-bb05-c34c0a078fc0", "Name": "_br_uid_2", "Host": "gap.com", "IsSession": false, "Length": "36499", "description": "This cookie is used to enable user to share pages through third party social networking websites.; Tag Title(s) : BloomReach - All;\nUID(s) : 453,168,253;\nVendor(s): <PERSON>Reach;\nTag Container(s):BloomReach\n\n", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "<PERSON><PERSON>_br_uid_2", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "6341d98b-7880-4a42-a630-a2140c2ccb91", "Name": "_pin_unauth", "Host": "athleta.gap.com", "IsSession": false, "Length": "364", "description": "This cookie is assocate with pinterest.  It is used to track the usage of services.; Tag Title(s) : Pinterest - All - US & CA\n; UID(s) : 284, 93, 162\n; Vendor(s): Pinterest\n; Tag Container(s):Pinterest Tag\n", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookie_pin_unauth", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "37d96469-b109-471c-9347-60c8ae899182", "Name": "liveagent_oref", "Host": "www.gap.com", "IsSession": false, "Length": "365", "description": "This cookie name is associated with technology from LiveAgent, which provides online chat and customer support functionality.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookieliveagent_oref", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "59c418b2-3852-4975-a170-3f23086a7594", "Name": "liveagent_ptid", "Host": "www.gap.com", "IsSession": false, "Length": "365", "description": "This cookie name is associated with technology from LiveAgent, which provides online chat and customer support functionality.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookieliveagent_ptid", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "f5195698-df5a-4d74-a107-01e8dfb8ee00", "Name": "liveagent_sid", "Host": "www.gap.com", "IsSession": true, "Length": "0", "description": "This cookie name is associated with technology from LiveAgent, which provides online chat and customer support functionality.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookieliveagent_sid", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "5d279f2f-5712-4502-94f7-e35af63ac9ab", "Name": "liveagent_vc", "Host": "www.gap.com", "IsSession": false, "Length": "365", "description": "This cookie name is associated with technology from LiveAgent, which provides online chat and customer support functionality.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookieliveagent_vc", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "6bee2142-ddf4-4bbe-baf3-c414622af833", "Name": "weird_get_top_level_domain", "Host": "gap.com", "IsSession": true, "Length": "0", "description": "This cookie appears to be set by some Javascript code designed to work out what the domain of the site is. Its exact usage and purpose is unclear.weird_get_top_level_domain", "thirdPartyDescription": "weird_get_top_level_domain", "patternKey": "weird_get_top_level_domain", "thirdPartyKey": "Pattern|weird_get_top_level_domain", "firstPartyKey": "Cookieweird_get_top_level_domain,Pattern|weird_get_top_level_domain", "DurationType": 1, "category": null, "isThirdParty": false}], "Hosts": [{"HostName": "vimeo.com", "DisplayName": "vimeo.com", "HostId": "H19", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "__cf_bm", "Host": "vimeo.com", "IsSession": false, "Length": "0", "description": "This is a CloudFoundry cookie", "thirdPartyDescription": "This is a CloudFoundry cookie", "patternKey": "_cf_bm", "thirdPartyKey": "Pattern|_cf_bm", "firstPartyKey": "<PERSON>ie__cf_bm,<PERSON><PERSON>|_cf_bm", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "b5f0d40a-b62f-410c-8d3a-2f1a6caf37d2", "Name": "player", "Host": "vimeo.com", "IsSession": false, "Length": "364", "description": "This domain is owned by Vimeo. The main business activity is: Video Hosting/Sharing", "thirdPartyDescription": "This domain is owned by Vimeo. The main business activity is: Video Hosting/Sharing", "patternKey": null, "thirdPartyKey": "Cookie|vimeo.com", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "c8fabbc5-9f41-4e10-838c-d6f7a3666c91", "Name": "vuid", "Host": "vimeo.com", "IsSession": false, "Length": "729", "description": "This domain is owned by Vimeo. The main business activity is: Video Hosting/Sharing", "thirdPartyDescription": "This domain is owned by Vimeo. The main business activity is: Video Hosting/Sharing", "patternKey": null, "thirdPartyKey": "Cookie|vimeo.com", "firstPartyKey": "<PERSON><PERSON><PERSON><PERSON>", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "rum.optimizely.com", "DisplayName": "rum.optimizely.com", "HostId": "H41", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "AWSELB", "Host": "rum.optimizely.com", "IsSession": true, "Length": "0", "description": "This domain is associated with Optimizely, a platform providing A/B testing and personalization for websites. The cookies are used to measure the performance and effectiveness of different website designs.", "thirdPartyDescription": "This domain is associated with Optimizely, a platform providing A/B testing and personalization for websites. The cookies are used to measure the performance and effectiveness of different website designs.", "patternKey": null, "thirdPartyKey": "Cookie|rum.optimizely.com", "firstPartyKey": "CookieAWSELB", "DurationType": 1, "category": null, "isThirdParty": false}]}], "PurposeId": "9C8F5CE8-6D05-4F7F-A24A-6DB6129C119F", "CustomGroupId": "C0003", "GroupId": "8552b73c-767a-4ff3-9a82-b4f1ae1c3b2e", "Status": "always active", "IsDntEnabled": false, "Type": "COOKIE", "DescriptionLegal": "", "IabIllustrations": [], "HasLegIntOptOut": false, "HasConsentOptOut": true, "IsGpcEnabled": false, "VendorServices": null, "TrackingTech": null}, {"ShowInPopup": true, "ShowInPopupNonIAB": true, "ShowSDKListLink": true, "Order": "5", "OptanonGroupId": "C0002", "Parent": "BG149", "ShowSubgroup": true, "ShowSubGroupDescription": true, "ShowSubgroupToggle": false, "AlwaysShowCategory": false, "GroupDescription": "These cookies allow us to analyze website usage, visits, and traffic sources, so we can measure and improve performance. \n", "GroupDescriptionOTT": "These cookies allow us to analyze website usage, visits, and traffic sources, so we can measure and improve performance. \n", "GroupNameMobile": "Performance/Analytics Cookies", "GroupNameOTT": "Performance/Analytics Cookies", "GroupName": "Performance/Analytics Cookies", "IsIabPurpose": false, "GeneralVendorsIds": [], "FirstPartyCookies": [{"id": "e200ed00-ccdf-4eb1-8699-91d7ba67ceed", "Name": "__pdst", "Host": "oldnavy.gap.com", "IsSession": false, "Length": "364", "description": "Podsights cookie used to track Hubspots podcast advertising results.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "<PERSON>ie__pdst", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "70fdec80-1344-4936-ad62-ebb2a2c94864", "Name": "_gclxxxx", "Host": "gap.com", "IsSession": false, "Length": "89", "description": "Google conversion tracking cookie; Tag Title(s) : Google AdWords - GOL Confirmation,Google AdWords - Confirmation - Core,Google Cookie Matching Service - All,Google - All - PGM (gtag.js),Google - All - AdWords (gtag.js),Google - PHD - ATOL Girl (gtag.js),Google AdWords - BOPIS - All,Google - Loyalty Signup - AdWords (gtag.js) Copy 1,Google Ads - BOPIS - OCP,Google Ads - All - GID Conversion Pixel,Google Ads - Loyalty Signup,Google - All - PHD/DCM  (gtag.js),Google - ONOL Confirmation - unknown owner,Google - All - gtag.js\n; UID(s) : 366,368,450,527,565,603,872,1160,1192,1194,1195,138,166,203,212,479,176,209,311,318,571\n; Vendor(s): Google\n; Tag Container(s):Google AdWords Conversion,Tealium Generic Tag,Google Cookie Matching Service for Doubleclick,DoubleClick Floodlight (gtag.js),Google Ads Conversion Tracking &amp; Remarketing (gtag.js),Floodlight (gtag.js),Google AdWords Remarketing", "thirdPartyDescription": "Google conversion tracking cookie", "patternKey": "_gclxxxx", "thirdPartyKey": "Pattern|_gclxxxx", "firstPartyKey": "Pattern|_gclxxxx", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "bda8029f-14cf-4dbe-8e3d-bd27377c92f9", "Name": "_uetvid", "Host": "gap.com", "IsSession": false, "Length": "389", "description": "This is a cookie utilised by Microsoft Bing Ads and is a tracking cookie. It allows us to engage with a user that has previously visited our website.; Tag Title(s) : Bing - All - US,CA,Factory\n; UID(s) : 703\n; Vendor(s): Bing\n; Tag Container(s):Bing Ads Universal Event Tracking (UET)", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "<PERSON><PERSON>_uetvid", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "6818f845-a6f6-40bb-b60e-601ca359e3ae", "Name": "AKA_A2", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "This cookie is generally provided by Akamai and is used for the Advanced Acceleration feature, which enables DNS Prefetch and HTTP2 Push.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieAKA_A2", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "add0c4c0-8a73-40c8-92dd-d631c3a157cc", "Name": "optimizelyDomainTestCookie", "Host": "athleta.gap.com", "IsSession": false, "Length": "364", "description": "These Optimizely cookies allow us to test different versions of website pages to different users at the same time and to track how users navigate around our website. We use this information to test new features and make the website easier to use. \"; Tag Title(s) : Optimizely FullStack SDK\n; UID(s) : 8, 9, 10\n; Vendor(s): Optimizely\n; Tag Container(s):Tealium Custom Container\"", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieoptimizelyDomainTestCookie", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "decc9d3e-fb10-455e-950d-a018e702052d", "Name": "optimizelyEndUserId", "Host": "gap.com", "IsSession": false, "Length": "179", "description": "Cookie set by the Optimizely website optimisation plaftform. This cookie is a unique user identifier ::\nTag Title(s) : Optimizely FullStack SDK\nUID(s) : 8, 9, 10\nVendor(s): Optimizely\nTag Container(s):Tealium Custom Container", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieoptimizelyEndUserId", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "a1cc0cc8-f423-4172-8da2-1aa5559b3b1f", "Name": "RT", "Host": "gap.com", "IsSession": false, "Length": "6", "description": "The roundtrip (RT) Boomerang \ncookie is used by Akamai to measure page load time and/or other timers associated with the page.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "<PERSON><PERSON>", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "b0e2cd01-3505-4390-93b7-96ca47a6a980", "Name": "s_cc", "Host": "gap.com", "IsSession": true, "Length": "0", "description": "This cookie is associated with the Adobe Site Catalyst. It determines whether cookies are enabled in the web browser.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookies_cc", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "f1d44f71-2f67-40c9-9652-28d17eab1c35", "Name": "s_ips", "Host": "gap.com", "IsSession": true, "Length": "0", "description": "This cookie is associated with Adobe Analytics. The cookie is used for measuring a visitor’s scroll activity to see how much of a page they view before moving on to another page::\nTag Title(s) : Adobe Analytics AppMeasurement for JS,Adobe Experience Cloud ID Service\nUID(s) : 4,5,6,28,828\nVendor(s): Adobe\nTag Container(s):Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookies_ips", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "2a7ce99c-aac3-4db0-9e29-aa325801a55a", "Name": "s_ppv", "Host": "gap.com", "IsSession": true, "Length": "0", "description": "This cookie is associated with Adobe Analytics site tracking and is used to measure a user's scroll activity to see how much of a page they view before moving to another page.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookies_ppv", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "8f606f59-e9e7-4587-b618-8af4c9c4ab49", "Name": "s_tp", "Host": "gap.com", "IsSession": true, "Length": "0", "description": "This cookie is associated with Adobe Analytics. The cookie is used for measuring a visitor’s scroll activity to see how much of a page they view before moving on to another page::\nTag Title(s) : Adobe Analytics AppMeasurement for JS,Adobe Experience Cloud ID Service\nUID(s) : 4,5,6,28,828\nVendor(s): Adobe\nTag Container(s):Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookies_tp", "DurationType": 1, "category": null, "isThirdParty": false}], "Hosts": [{"HostName": "snapchat.com", "DisplayName": "snapchat.com", "HostId": "H269", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "sc_at", "Host": "snapchat.com", "IsSession": false, "Length": "389", "description": "<PERSON><PERSON> associated with embedding content from Snapchat.", "thirdPartyDescription": "<PERSON><PERSON> associated with embedding content from Snapchat.", "patternKey": null, "thirdPartyKey": "Cookie|snapchat.com", "firstPartyKey": "Cookiesc_at", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "force.com", "DisplayName": "force.com", "HostId": "H50", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "BrowserId", "Host": "force.com", "IsSession": false, "Length": "364", "description": "Testing", "thirdPartyDescription": "Testing", "patternKey": null, "thirdPartyKey": "Cookie|force.com", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "nr-data.net", "DisplayName": "nr-data.net", "HostId": "H173", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "JSESSIONID", "Host": "nr-data.net", "IsSession": true, "Length": "0", "description": "This domain is controlled by New Relic, which provides a platform for monitoring the performance of web and mobile applications.", "thirdPartyDescription": "This domain is controlled by New Relic, which provides a platform for monitoring the performance of web and mobile applications.", "patternKey": null, "thirdPartyKey": "Cookie|nr-data.net", "firstPartyKey": "CookieJSESSIONID", "DurationType": 1, "category": null, "isThirdParty": false}]}], "PurposeId": "C01AEEED-E3A8-4C01-89DE-837DBE329EBD", "CustomGroupId": "C0002", "GroupId": "081b4fcf-c2b2-4c06-b1f9-668fe91b114a", "Status": "always active", "IsDntEnabled": false, "Type": "COOKIE", "DescriptionLegal": "", "IabIllustrations": [], "HasLegIntOptOut": false, "HasConsentOptOut": true, "IsGpcEnabled": false, "VendorServices": null, "TrackingTech": null}, {"ShowInPopup": true, "ShowInPopupNonIAB": true, "ShowSDKListLink": true, "Order": "6", "OptanonGroupId": "C0014", "Parent": "BG149", "ShowSubgroup": true, "ShowSubGroupDescription": true, "ShowSubgroupToggle": false, "AlwaysShowCategory": false, "GroupDescription": "Manual categorization from GAP to Category 1", "GroupDescriptionOTT": "Manual categorization from GAP to Category 1", "GroupNameMobile": "GAP Category One", "GroupNameOTT": "GAP Category One", "GroupName": "GAP Category One", "IsIabPurpose": false, "GeneralVendorsIds": [], "FirstPartyCookies": [{"id": "db3f5933-e976-457d-9450-2b9ac6ca2e72", "Name": "__VCAP_ID__", "Host": "athleta.gap.com", "IsSession": true, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.\n", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "98007719-e8ad-4d03-b151-fcf8ebe107a0", "Name": "_abck", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.::\n<PERSON><PERSON><PERSON><PERSON>, Owner: <PERSON>", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "bdc29d12-1c32-4a94-b95f-3b32843fb39f", "Name": "ABSeg", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback. Privacy Team's Notes:gaptech sets sessions that controls testing (strictly necessary).::\nIn<PERSON><PERSON><PERSON>, Owner: WebApps ToolChain", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "dfc6a971-5fa3-46ab-a589-e9a21ae6a735", "Name": "AMCV_93BE1C8B532956910A490D4D%40AdobeOrg", "Host": "gap.com", "IsSession": true, "Length": "0", "description": " Manually added this cookie based on other AMCV cookies. ::\nTag Title(s) : Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service\n; UID(s) : 6\n; Vendor(s): Adobe\n; Tag Container(s):Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "1ecbb637-3a68-4235-ada1-df54e9bca8ae", "Name": "badges", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.; Privacy Team's Category:tealium cookie - controls tealium audience stream that controls experiences - category 1\n; Privacy Team's Notes:tealium cookie - controls tealium audience stream that controls experiences - category 1\n::\nIn<PERSON>se<PERSON>, Owner: DPG/CoE", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "d3240e5f-d64e-46c0-a39e-acf55dd006c6", "Name": "bm_sz", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "340109ff-5908-41cf-8da0-86f1415b8ee7", "Name": "CONSENTMGR", "Host": "gap.com", "IsSession": false, "Length": "89", "description": "Changing from Unknown to Category 1 based on internal GAP feedback. ; Tag Title(s) : Tealium Collect - athleta CDH, Tealium Collect\n; UID(s) : 1113,2,25\n; Vendor(s): Tealium\n; Tag Container(s):Tealium Collect", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "e582c6d2-c8e5-40fc-a8bf-ee6db74861bd", "Name": "entry_brand", "Host": "gap.com", "IsSession": true, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.:: Privacy Team's Notes:analytics cookie used to report on which brand a user started session on - category 1 :: Infosec Cookie", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "d4957300-123e-4832-806e-78076d12e70e", "Name": "fs_uid", "Host": "gap.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookiefs_uid", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "cbb4ec8b-3ec5-473e-8078-fae87c0214a6", "Name": "fuiMatchMedia", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "bc3699c0-4f77-4fba-9729-77b81c8233e7", "Name": "ktn", "Host": "secure-bananarepublic.gap.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.::\n<PERSON><PERSON><PERSON><PERSON>, Owner: PT - Profile Web Experience", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "1dfc359b-e133-477b-a090-50804bca963f", "Name": "locale", "Host": "gap.com", "IsSession": false, "Length": "71", "description": "Changing from Unknown to Category 1 based on internal GAP feedback. Infosec <PERSON>, Owner: WebApps ToolChain", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "1a54edb9-2bd1-4d60-a75b-30c93df9f55d", "Name": "NGRedirect", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "2c0961f8-f044-4b9e-bbc7-18e44297ee93", "Name": "optimizelyOptOut", "Host": "athleta.gap.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieoptimizelyOptOut", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "dded38ab-de15-41e0-b758-3dea8a4801ff", "Name": "PARTNER", "Host": "gap.com", "IsSession": false, "Length": "6", "description": "Changing from Unknown to Category 1 based on internal GAP feedback. Infose<PERSON>, Owner: Personalization", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "cff88efb-14bd-4912-a581-36965190be18", "Name": "PF", "Host": "api.gap.com", "IsSession": true, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "b9abc07f-c324-4b3f-a8ad-b408742172d4", "Name": "previewType", "Host": "secure-bananarepublic.gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "67c48988-8959-4005-875f-0c3657ecb0c3", "Name": "previous_page_type", "Host": "gap.com", "IsSession": true, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "86f57fb6-9e91-41a2-acce-dd30833fd0ae", "Name": "prlb", "Host": "gap.com", "IsSession": true, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "a0da9e0c-c2ec-4ad5-aec0-c9adfd43f4a6", "Name": "RES_TRACKINGID", "Host": "gap.com", "IsSession": false, "Length": "395", "description": "Changing from Unknown to Category 1 based on internal GAP feedback::\nTag Title(s) : Certona - US, CDA & Factory - All\nUID(s) : 616,225,329\nVendor(s): <PERSON><PERSON><PERSON>\nTag Container(s):<PERSON><PERSON>a Hosted Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "8c11e8bb-5438-473a-8dc6-b61e99acbd37", "Name": "s_chan", "Host": "gap.com", "IsSession": false, "Length": "1825", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "5fbbf8ed-9408-41df-9194-6df5befa44d9", "Name": "s_depth", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "4d2ecdd0-034e-40de-b6a7-4daa0b5415f5", "Name": "s_fid", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "Manually added this cookie based on other s_ cookies (Adobe)::\nTag Title(s) : Adobe Analytics AppMeasurement for JS,Adobe Experience Cloud ID Service\nUID(s) : 4,5,6,28,828\nVendor(s): Adobe\nTag Container(s):Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "91223688-a71a-4f46-9a66-74715c3a0cc6", "Name": "s_ghn", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "93897acc-7903-491e-be6e-6369e9a7c4e9", "Name": "s_gpn", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "076cab35-e662-4533-a821-432629884c27", "Name": "s_lpt", "Host": "gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback::\nTag Title(s) : Adobe Analytics AppMeasurement for JS,Adobe Experience Cloud ID Service\nUID(s) : 4,5,6,28,828\nVendor(s): Adobe\nTag Container(s):Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "46c6d90c-30c7-49be-9c55-39f4df69aa80", "Name": "s_sq", "Host": "gap.com", "IsSession": false, "Length": "1095", "description": "Changing from Unknown to Category 1 based on internal GAP feedback::\nTag Title(s) : Adobe Analytics AppMeasurement for JS,Adobe Experience Cloud ID Service\nUID(s) : 4,5,6,28,828\nVendor(s): Adobe\nTag Container(s):Adobe Analytics AppMeasurement for JS, Adobe Experience Cloud ID Service", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 365, "category": null, "isThirdParty": false}, {"id": "d501e902-5ef7-4b16-993a-4cb24f26e128", "Name": "s-prlb", "Host": "gap.com", "IsSession": true, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "d4f4be66-ee5d-4c5a-8cd0-a0f9ed235bb1", "Name": "tmxSessionID", "Host": "athleta.gap.com", "IsSession": false, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "e30bcdc5-40dc-422e-abb2-73fc0ea3d035", "Name": "triage_flowId", "Host": "secure-bananarepublic.gap.com", "IsSession": true, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback. Infosec <PERSON>, Owner: PT - Profile Web Experience", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "5bd43b0d-7cfa-4fe1-8776-a4cca4915086", "Name": "triage_params", "Host": "secure-bananarepublic.gap.com", "IsSession": true, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback. Infosec <PERSON>, Owner: PT - Profile Web Experience", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "528aea55-8125-4515-be32-d226d2fa7aa8", "Name": "triage_tealium", "Host": "secure-bananarepublic.gap.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback. Infosec <PERSON>, Owner: PT - Profile Web Experience", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "3c8d0f6f-2746-4b92-bdb4-2c735bae00f2", "Name": "unknownShopperId", "Host": "gap.com", "IsSession": false, "Length": "730", "description": "Changing from Unknown to Category 1 based on internal GAP feedback. Infosec <PERSON>, Owner: WebApps ToolChain", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "33f7b518-0f2b-4bd0-8996-8bc92189da72", "Name": "usprivacy", "Host": "gapinc-branch-qa.azurewebsites.net", "IsSession": true, "Length": "0", "description": "5/19: This cookie's purpose is to save the privacy preferences of the customer while on the website & when applicable, to store California consumer opt-out choices in the local domain.", "thirdPartyDescription": "", "patternKey": null, "thirdPartyKey": null, "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}], "Hosts": [{"HostName": "fonts.net", "DisplayName": "fonts.net", "HostId": "H213", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "__cf_bm", "Host": "fonts.net", "IsSession": false, "Length": "365", "description": "This is a CloudFoundry cookie", "thirdPartyDescription": "This is a CloudFoundry cookie", "patternKey": "_cf_bm", "thirdPartyKey": "Pattern|_cf_bm", "firstPartyKey": "<PERSON>ie__cf_bm,<PERSON><PERSON>|_cf_bm", "DurationType": 365, "category": null, "isThirdParty": false}]}, {"HostName": "d.la3-c2-ia4.salesforceliveagent.com", "DisplayName": "d.la3-c2-ia4.salesforceliveagent.com", "HostId": "H159", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "X-Salesforce-CHAT", "Host": "d.la3-c2-ia4.salesforceliveagent.com", "IsSession": true, "Length": "0", "description": "6/19 - Under Tealium doc UID: 862, 873, 974, 998, 1050, 1084, 1191. 5/22 - <PERSON> confirms cat 1. \n Sales Force uses these cookies to be able to provide live chat functionality to the user.\nChanging from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieX-Salesforce-CHAT", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "wip.prod.gaptecholapps.com", "DisplayName": "wip.prod.gaptecholapps.com", "HostId": "H63", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "prlb", "Host": "wip.prod.gaptecholapps.com", "IsSession": true, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "6cdf7179-79a7-489b-b4c6-75cae85ad970", "Name": "s-prlb", "Host": "wip.prod.gaptecholapps.com", "IsSession": true, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "assets-gap.com", "DisplayName": "assets-gap.com", "HostId": "H164", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "prlb", "Host": "assets-gap.com", "IsSession": true, "Length": "0", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "cdn.attn.tv", "DisplayName": "cdn.attn.tv", "HostId": "H74", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "_attn_", "Host": "cdn.attn.tv", "IsSession": false, "Length": "3649", "description": "6/15: Note from Crystal: Change to Cat 1. Falls under optimization. Changing from Unknown to Category 2 based on internal GAP feedback.::\nTag Title(s) : Attentive - All - Gap US,Attentive - Confirmation - Gap Products,Attentive - All [GPFS];\nUID(s) : 971,972,409;\nVendor(s): Attentive;\nTag Container(s):Tealium Generic Tag", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "d.la1-c1-ph2.salesforceliveagent.com", "DisplayName": "d.la1-c1-ph2.salesforceliveagent.com", "HostId": "H71", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "liveagent_ptid", "Host": "d.la1-c1-ph2.salesforceliveagent.com", "IsSession": false, "Length": "364", "description": "This cookie name is associated with technology from LiveAgent, which provides online chat and customer support functionality. \"; Tag Title(s) : Salesforce LiveChat - PROD - All - BRUS ONLY - [EXCLUDE PROFILE],Salesforce LiveChat - All Pages - US & Factory,Salesforce LiveChat - STAGE - All - US, Factory, CA,Salesforce LiveChat - PROD - Order History - ON US,Salesforce LiveChat - WIP/APP - All\n; UID(s) : 1191,300,339,401,469,405,445,454,566\n; Vendor(s): Salesforce\n; Tag Container(s):Tealium Custom Container\"\nChanging from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookieliveagent_ptid", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "56edf62a-fcb5-43b2-9796-129247bbdeb6", "Name": "liveagent_sid", "Host": "d.la1-c1-ph2.salesforceliveagent.com", "IsSession": true, "Length": "0", "description": "This cookie name is associated with technology from LiveAgent, which provides online chat and customer support functionality.\nChanging from Unknown to Category 1 based on internal GAP feedback. \"; Tag Title(s) : Salesforce LiveChat - PROD - All - BRUS ONLY - [EXCLUDE PROFILE],Salesforce LiveChat - All Pages - US & Factory,Salesforce LiveChat - STAGE - All - US, Factory, CA,Salesforce LiveChat - PROD - Order History - ON US,Salesforce LiveChat - WIP/APP - All\n; UID(s) : 1191,300,339,401,469,405,445,454,566\n; Vendor(s): Salesforce\n; Tag Container(s):Tealium Custom Container\"", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookieliveagent_sid", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "b516bb69-e840-48fa-9445-147b2ff42799", "Name": "liveagent_vc", "Host": "d.la1-c1-ph2.salesforceliveagent.com", "IsSession": false, "Length": "364", "description": "This cookie name is associated with technology from LiveAgent, which provides online chat and customer support functionality.\nChanging from Unknown to Category 1 based on internal GAP feedback. \"; Tag Title(s) : Salesforce LiveChat - PROD - All - BRUS ONLY - [EXCLUDE PROFILE],Salesforce LiveChat - All Pages - US & Factory,Salesforce LiveChat - STAGE - All - US, Factory, CA,Salesforce LiveChat - PROD - Order History - ON US,Salesforce LiveChat - WIP/APP - All\n; UID(s) : 1191,300,339,401,469,405,445,454,566\n; Vendor(s): Salesforce\n; Tag Container(s):Tealium Custom Container\"", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "Cookieliveagent_vc", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "90de6bd8-da42-43ce-b397-26284c8d1ff6", "Name": "X-Salesforce-CHAT", "Host": "d.la1-c1-ph2.salesforceliveagent.com", "IsSession": true, "Length": "0", "description": "6/19 - Under Tealium doc UID: 862, 873, 974, 998, 1050, 1084, 1191. 5/22 - <PERSON> confirms cat 1. \n Sales Force uses these cookies to be able to provide live chat functionality to the user.\nChanging from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieX-Salesforce-CHAT", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "service.force.com", "DisplayName": "service.force.com", "HostId": "H46", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "CookieConsentPolicy", "Host": "service.force.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieCookieConsentPolicy", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "56460be3-3820-4332-a4a7-952a9676bcd6", "Name": "LSKey-c$<PERSON>onsentPolicy", "Host": "service.force.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "<PERSON><PERSON><PERSON>-c$<PERSON>entPolicy", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "nebula-cdn.kampyle.com", "DisplayName": "nebula-cdn.kampyle.com", "HostId": "H47", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "md_isSurveySubmittedInSession", "Host": "nebula-cdn.kampyle.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "force.com", "DisplayName": "force.com", "HostId": "H50", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "BrowserId", "Host": "force.com", "IsSession": false, "Length": "364", "description": "Testing", "thirdPartyDescription": "Testing", "patternKey": null, "thirdPartyKey": "Cookie|force.com", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "822ee11b-4481-4014-9ee8-734435bb3923", "Name": "BrowserId_sec", "Host": "force.com", "IsSession": false, "Length": "364", "description": "Testing", "thirdPartyDescription": "Testing", "patternKey": null, "thirdPartyKey": "Cookie|force.com", "firstPartyKey": "CookieBrowserId_sec", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "c.la1-c1-ph2.salesforceliveagent.com", "DisplayName": "c.la1-c1-ph2.salesforceliveagent.com", "HostId": "H215", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "X-Salesforce-CHAT", "Host": "c.la1-c1-ph2.salesforceliveagent.com", "IsSession": true, "Length": "0", "description": "6/19 - Under Tealium doc UID: 862, 873, 974, 998, 1050, 1084, 1191. 5/22 - <PERSON> confirms cat 1. \nSales Force uses these cookies to be able to provide live chat functionality to the user.\nChanging from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieX-Salesforce-CHAT", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "d.la3-c2-ia5.salesforceliveagent.com", "DisplayName": "d.la3-c2-ia5.salesforceliveagent.com", "HostId": "H294", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "X-Salesforce-CHAT", "Host": "d.la3-c2-ia5.salesforceliveagent.com", "IsSession": true, "Length": "0", "description": "6/19 - Under Tealium doc UID: 862, 873, 974, 998, 1050, 1084, 1191. 5/22 - <PERSON> confirms cat 1. \n\nSales Force uses these cookies to be able to provide live chat functionality to the user.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieX-Salesforce-CHAT", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "myfonts.net", "DisplayName": "myfonts.net", "HostId": "H270", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "__cf_bm", "Host": "myfonts.net", "IsSession": false, "Length": "0", "description": "This is a CloudFoundry cookie", "thirdPartyDescription": "This is a CloudFoundry cookie", "patternKey": "_cf_bm", "thirdPartyKey": "Pattern|_cf_bm", "firstPartyKey": "<PERSON>ie__cf_bm,<PERSON><PERSON>|_cf_bm", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "athletaclienteling.secure.force.com", "DisplayName": "athletaclienteling.secure.force.com", "HostId": "H85", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "CookieConsentPolicy", "Host": "athletaclienteling.secure.force.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieCookieConsentPolicy", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "f271e4f4-9607-4c55-8739-50d533ff4ec7", "Name": "LSKey-c$<PERSON>onsentPolicy", "Host": "athletaclienteling.secure.force.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "<PERSON><PERSON><PERSON>-c$<PERSON>entPolicy", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "gapinc.my.salesforce-sites.com", "DisplayName": "gapinc.my.salesforce-sites.com", "HostId": "H303", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "BrowserId", "Host": "gapinc.my.salesforce-sites.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "174e84f3-edef-4774-a69c-aa5dec7ec321", "Name": "BrowserId_sec", "Host": "gapinc.my.salesforce-sites.com", "IsSession": false, "Length": "364", "description": "Used to log secure browser sessions/visits for internal-only product analytics.\nChanging from Unknown to Category 1 based on internal GAP feedback.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieBrowserId_sec", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "93e545f6-d122-4001-8c34-6e739ec433a6", "Name": "CookieConsentPolicy", "Host": "gapinc.my.salesforce-sites.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback. Used to apply end-user cookie consent preferences set by our client-side utility.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "CookieCookieConsentPolicy", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "f748bd1a-ec45-470a-98da-5922c3b37e14", "Name": "LSKey-c$<PERSON>onsentPolicy", "Host": "gapinc.my.salesforce-sites.com", "IsSession": false, "Length": "364", "description": "Changing from Unknown to Category 1 based on internal GAP feedback. Used to apply end-user cookie consent preferences set by our client-side utility.", "thirdPartyDescription": null, "patternKey": null, "thirdPartyKey": "", "firstPartyKey": "<PERSON><PERSON><PERSON>-c$<PERSON>entPolicy", "DurationType": 1, "category": null, "isThirdParty": false}]}, {"HostName": "rum.optimizely.com", "DisplayName": "rum.optimizely.com", "HostId": "H41", "Description": "", "PrivacyPolicy": "", "Cookies": [{"id": "************************************", "Name": "AWSELBCORS", "Host": "rum.optimizely.com", "IsSession": true, "Length": "0", "description": "This domain is associated with Optimizely, a platform providing A/B testing and personalization for websites. The cookies are used to measure the performance and effectiveness of different website designs.", "thirdPartyDescription": "This domain is associated with Optimizely, a platform providing A/B testing and personalization for websites. The cookies are used to measure the performance and effectiveness of different website designs.", "patternKey": null, "thirdPartyKey": "Cookie|rum.optimizely.com", "firstPartyKey": "CookieAWSELBCORS", "DurationType": 1, "category": null, "isThirdParty": false}, {"id": "557108af-fd76-4332-a549-6834c75f5f0c", "Name": "optimizelyRumLB", "Host": "rum.optimizely.com", "IsSession": true, "Length": "0", "description": "This domain is associated with Optimizely, a platform providing A/B testing and personalization for websites. The cookies are used to measure the performance and effectiveness of different website designs.", "thirdPartyDescription": "This domain is associated with Optimizely, a platform providing A/B testing and personalization for websites. The cookies are used to measure the performance and effectiveness of different website designs.", "patternKey": null, "thirdPartyKey": "Cookie|rum.optimizely.com", "firstPartyKey": null, "DurationType": 1, "category": null, "isThirdParty": false}]}], "PurposeId": "231082C1-7074-4CF6-871B-5D9A5A8C2C6A", "CustomGroupId": "C0014", "GroupId": "2fc1aea6-c954-4fbe-960f-e82448e19e30", "Status": "always active", "IsDntEnabled": false, "Type": "COOKIE", "DescriptionLegal": "", "IabIllustrations": [], "HasLegIntOptOut": false, "HasConsentOptOut": true, "IsGpcEnabled": false, "VendorServices": null, "TrackingTech": null}, {"ShowInPopup": true, "ShowInPopupNonIAB": true, "ShowSDKListLink": true, "Order": "7", "OptanonGroupId": "C0011", "Parent": "BG149", "ShowSubgroup": true, "ShowSubGroupDescription": true, "ShowSubgroupToggle": false, "AlwaysShowCategory": false, "GroupDescription": "Data Extract", "GroupDescriptionOTT": "Data Extract", "GroupNameMobile": "Data Extract", "GroupNameOTT": "Data Extract", "GroupName": "Data Extract", "IsIabPurpose": false, "GeneralVendorsIds": [], "FirstPartyCookies": [], "Hosts": [], "PurposeId": "B83B7781-0E9C-47E0-9C57-C3472A820918", "CustomGroupId": "C0011", "GroupId": "a704d376-f5a6-4273-92a0-66df49ea2614", "Status": "always active", "IsDntEnabled": false, "Type": "COOKIE", "DescriptionLegal": "", "IabIllustrations": [], "HasLegIntOptOut": false, "HasConsentOptOut": true, "IsGpcEnabled": false, "VendorServices": null, "TrackingTech": null}, {"ShowInPopup": true, "ShowInPopupNonIAB": true, "ShowSDKListLink": true, "Order": "19", "OptanonGroupId": "BG149", "Parent": "", "ShowSubgroup": false, "ShowSubGroupDescription": false, "ShowSubgroupToggle": false, "AlwaysShowCategory": false, "GroupDescription": "", "GroupDescriptionOTT": "", "GroupNameMobile": "<br>", "GroupNameOTT": "<br>", "GroupName": "<br>", "IsIabPurpose": false, "GeneralVendorsIds": [], "FirstPartyCookies": [], "Hosts": [], "PurposeId": "", "CustomGroupId": "BG149", "GroupId": "5b114228-d209-465e-89fd-83553421da5f", "Status": "always active", "IsDntEnabled": false, "Type": "BRANCH", "DescriptionLegal": "", "IabIllustrations": [], "HasLegIntOptOut": false, "HasConsentOptOut": true, "IsGpcEnabled": false, "VendorServices": [], "TrackingTech": {"SessionStorages": [], "LocalStorages": []}}], "Language": {"Culture": "en"}, "ShowPreferenceCenterCloseButton": true, "CustomJs": "", "LifespanTypeText": "Session", "LifespanDurationText": "", "CloseText": "Close", "BannerCloseButtonText": "Close", "AddLinksToCookiepedia": false, "showBannerCloseButton": true, "AlertLayout": "bottom", "ShowAlertNotice": true, "IsConsentLoggingEnabled": true, "IsIabEnabled": false, "IsIabThirdPartyCookieEnabled": false, "ScrollCloseBanner": false, "OnClickCloseBanner": true, "NextPageCloseBanner": true, "AcceptAllCookies": false, "ConsentModel": "custom", "VendorConsentModel": "opt-out", "Vendors": [], "OverriddenVendors": {}, "OverridenGoogleVendors": null, "publisher": {"restrictions": {}}, "ScrollAcceptAllCookies": false, "OnClickAcceptAllCookies": false, "NextPageAcceptAllCookies": false, "Flat": false, "FloatingFlat": false, "FloatingRoundedCorner": true, "FloatingRoundedIcon": false, "FloatingRounded": false, "CenterRounded": false, "Center": true, "Panel": false, "Popup": false, "List": false, "Tab": false, "ChoicesBanner": false, "NoBanner": false, "BannerIABPartnersLink": "", "BannerPurposeTitle": "", "BannerPurposeDescription": "", "BannerFeatureTitle": "", "BannerFeatureDescription": "", "BannerInformationTitle": "", "BannerInformationDescription": "", "BannerShowRejectAllButton": false, "BannerRejectAllButtonText": "", "PCenterShowRejectAllButton": false, "PCenterRejectAllButtonText": "", "BannerSettingsButtonDisplayLink": false, "BannerDPDTitle": null, "BannerDPDDescription": null, "BannerDPDDescriptionFormat": null, "PCFirstPartyCookieListText": "First Party Cookies", "PCViewCookiesText": "View Cookies", "PCenterBackText": "Back", "PCenterVendorsListText": "Vendors List", "PCenterViewPrivacyPolicyText": "View Privacy Policy", "PCenterClearFiltersText": "Clear Filters", "PCenterApplyFiltersText": "Apply", "PCenterAllowAllConsentText": "Allow All Consent", "PCenterCookiesListText": "<PERSON><PERSON>", "PCenterCancelFiltersText": "Cancel", "PCenterEnableAccordion": true, "IabType": "", "AdvancedAnalyticsCategory": "C0001", "PCGrpDescType": null, "PCGrpDescLinkPosition": null, "PCVendorFullLegalText": null, "LegIntSettings": null, "PCAccordionStyle": "NoAccordion", "PCShowConsentLabels": false, "PCActiveText": "Active", "PCInactiveText": "Inactive", "BannerAdditionalDescription": "", "BannerAdditionalDescPlacement": "AfterDescription", "PCenterSelectAllVendorsText": "Select All Vendors", "PCenterFilterText": "Filter", "ReconsentFrequencyDays": 1095, "UseGoogleVendors": false, "PCIABVendorsText": "IAB Vendors", "PCIllusText": "Illustrations", "PCGoogleVendorsText": "Google Vendors", "PCTemplateUpgrade": true, "PCShowPersistentCookiesHoverButton": false, "PCenterDynamicRenderingEnable": false, "GlobalRestrictionEnabled": false, "GlobalRestrictions": {}, "PCenterUseGeneralVendorsToggle": false, "PCenterGeneralVendorsText": "Other Vendors", "PCenterAllowVendorOptout": true, "PCenterGeneralVendorThirdPartyCookiesText": "View Vendor Details", "GeneralVendors": [], "BannerNonIABVendorListText": "", "PCenterVendorListLifespan": "Lifespan", "PCenterVendorListDisclosure": "Device Storage Disclosure", "PCenterVendorListNonCookieUsage": "This vendor utilizes other methods of storage or accessing information in addition to cookies.", "PCenterVendorListDescText": "", "PCenterVendorListStorageIdentifier": "Identifier", "PCenterVendorListStorageType": "Type", "PCenterVendorListStoragePurposes": "Purposes", "PCenterVendorListStorageDomain": "Domain", "PCVLSDomainsUsed": "Domains Used", "PCVLSUse": "Use", "PCenterVendorListLifespanDay": "Day", "PCenterVendorListLifespanDays": "Days", "PCenterVendorListLifespanMonth": "Month", "PCenterVendorListLifespanMonths": "Months", "PCLifeSpanYr": "Year", "PCLifeSpanYrs": "Years", "PCLifeSpanSecs": "A few seconds", "PCLifeSpanWk": "Week", "PCLifeSpanWks": "Weeks", "PCCookiePolicyText": "View Privacy Policy", "BShowSaveBtn": true, "BSaveBtnText": "Save Choices", "CookieFirstPartyText": "First Party", "CookieThirdPartyText": "Third Party", "PCCookiePolicyLinkScreenReader": "More information about your privacy, opens in a new tab", "PCLogoScreenReader": "Company Logo", "BnrLogoAria": "Company Logo", "PublisherCC": "US", "BCloseButtonType": "Icon", "BContinueText": "Continue without Accepting", "PCCloseButtonType": "Icon", "PCContinueText": "Continue without Accepting", "BannerFocus": false, "BRejectConsentType": "ObjectToLI", "BannerRelativeFontSizesToggle": true, "PCAllowToggleLbl": "Allow", "GCEnable": false, "GCAnalyticsStorage": "C0002", "GCAdStorage": "C0004", "GCAdUserData": "", "GCAdPersonalization": "", "GCRedactEnable": false, "GCWaitTime": 0, "GCFunctionalityStorage": "", "GCPersonalizationStorage": "", "GCSecurityStorage": "", "PCGeneralVendorsPolicyText": "View Privacy Policy", "PCIABVendorLegIntClaimText": "View Legitimate Interest Claim", "PCOpensCookiesDetailsAlert": "Cookie Details button opens Cookie List menu", "PCOpensVendorDetailsAlert": "IAB Vendor Details button opens Vendor List menu", "AriaOpenPreferences": "Open Preferences", "AriaClosePreferences": "Close Preferences", "AriaPrivacy": "Privacy", "AriaDescribedBy": "Processing is required, choice is always enabled.", "BCookiePolicyLinkScreenReader": "More information about your privacy, opens in a new tab", "BNoCursorFocus": true, "BShowPolicyLink": false, "PCenterVendorListSearch": "Search…", "PCenterCookieListSearch": "Search…", "PCenterLegitInterestText": "Legitimate Interest", "PCenterLegIntColumnHeader": "Legitimate Interest", "PCenterConsentText": "Consent", "PCenterVendorListFilterAria": "Filter I<PERSON>", "PCenterCookieListFilterAria": "Filter I<PERSON>", "BInitialFocus": false, "BInitialFocusLinkAndButton": false, "NewVendorsInactiveEnabled": false, "PCenterFilterAppliedAria": "Applied", "PCenterFilterClearedAria": "Filters Cleared", "PCenterVendorListScreenReader": "Vendor Details button opens Vendor List menu", "PCenterCookieSearchAriaLabel": "Cookie list search", "PCenterVendorSearchAriaLabel": "Vendor list search", "PCenterVendorListTitle": null, "PCenterVendorListLinkText": "", "PCenterVendorListLinkAriaLabel": "", "IsShowAlwaysActiveText": true, "PCenterUserIdTitleText": "", "PCenterUserIdDescriptionText": "", "PCenterUserIdTimestampTitleText": "", "PCenterUserIdNotYetConsentedText": "", "BImprintLinkScreenReader": "Legal Information, opens in a new tab", "BShowImprintLink": false, "PCenterImprintLinkScreenReader": "", "PCenterImprintLinkText": "", "PCenterImprintLinkUrl": "", "PCCategoryStyle": "Toggle", "PCVSEnable": false, "PCVSOptOut": false, "PCVSCategoryView": false, "PCVSExpandCategory": false, "PCVSExpandGroup": false, "PCVSListTitle": null, "PCVSNameText": null, "PCVSParentCompanyText": null, "PCVSAddressText": null, "PCVSDefaultCategoryText": null, "PCVSDefaultDescriptionText": null, "PCVSDPOEmailText": null, "PCVSDPOLinkText": null, "PCVSPrivacyPolicyLinkText": null, "PCVSCookiePolicyLinkText": null, "PCVSOptOutLinkText": null, "PCVSLegalBasisText": null, "PCVSAllowAllText": null, "BShowOptOutSignal": true, "BOptOutSignalText": "Browser Opt-Out Honored", "PCShowOptOutSignal": true, "PCOptOutSignalText": "Browser Opt-Out Honored", "PCHostNotFound": "did not match any Hosts", "PCVendorNotFound": "did not match any Vendors", "PCTechNotFound": "did not match any Technologies", "PCTrackingTechTitle": "Technology List", "PCTechDetailsText": "Technologies List", "PCTechDetailsAriaLabel": "Technologies List button opens Tracking Tech list menu", "PCShowTrackingTech": false, "PCCookiesLabel": "Cookies", "PCLocalStorageLabel": "Local Storage[s]", "PCSessionStorageLabel": "Session Storage[s]", "PCLocalStorageDurationText": "Permanent", "PCSessionStorageDurationText": "Session", "BRegionAriaLabel": "<PERSON><PERSON> banner", "PCRegionAriaLabel": "Preference center", "IsGPPEnabled": false, "IsGPPKnownChildApplicable": false, "IsGPPDataProcessingApplicable": false, "GPPPurposes": {"SaleOptOutCID": "", "SharingOptOutCID": "", "PersonalDataCID": "", "KnownChildSellPICID": "", "KnownChildProcessCID": "", "KnownChildSharePICID": "", "SensitivePICID": "", "TargetedAdvertisingOptOutCID": "", "SensitiveSICID": "", "GeolocationCID": "", "RREPInfoCID": "", "CommunicationCID": "", "GeneticCID": "", "BiometricCID": "", "HealthCID": "", "SexualOrientationCID": "", "RaceCID": "", "ReligionCID": "", "ImmigrationCID": "", "PDCAboveAgeCID": "", "PDCBelowAgeCID": "", "UnionMembershipCID": ""}, "IsMSPAEnabled": false, "MSPAOptionMode": "", "UseGPPUSNational": true, "PCVListDataDeclarationText": "Data Declaration", "PCVListDataRetentionText": "Data Retention", "PCVListStdRetentionText": "Standard Retention", "PCVendorsCountText": "[VENDOR_NUMBER] partners can use this purpose", "PCVendorsCountFeatureText": "[VENDOR_NUMBER] partners can use this feature", "PCVendorsCountSpcFeatureText": "[VENDOR_NUMBER] partners can use this special feature", "PCVendorsCountSpcPurposeText": "[VENDOR_NUMBER] partners can use this special purpose", "IABDataCategories": [], "IABGroupsPrefixData": null, "UseNonStandardStacks": true, "IsRequireSignatureEnabled": false, "RequireSignatureCID": "", "PCRequireSignatureFieldLabel": "Email", "PCRequireSignatureHeaderText": "Authorization Needed", "PCRequireSignatureHeaderDesc": "Additional consent authorization is needed for the following reason:", "PCRequireSignatureConfirmBtnText": "Confirm", "PCRequireSignatureRejectBtnText": "Reject", "PCRequireSignatureHelpText": "This field is required to consent to this category."}, "CommonData": {"pcenterContinueWoAcceptLinkColor": "#696969", "IabThirdPartyCookieUrl": "cookies.onetrust.mgr.consensu.org", "OptanonHideAcceptButton": "hide-accept-button", "OptanonStyle": "modern", "OptanonStaticContentLocation": "", "BannerCustomCSS": "\n#ot-sdk-btn {\n    display: inline !important; /* Display as inline element */\n    background: none !important; /* Remove background */\n    border: none !important; /* Remove border */\n    padding: 0 !important; /* Remove padding */\n    margin: 0 !important; /* Remove margin */\n    font-size: inherit !important; /* Use the same font size as the parent */\n    font-family: inherit !important; /* Use the same font family as the parent */\n    color: #00008B !important; /* Use the same color as the parent */\n    text-decoration: underline !important; /* Add underline for a link-like appearance */\n    cursor: pointer !important; /* Change cursor to pointer on hover */\n    outline: none !important; /* Remove outline */\n}\n\n#onetrust-banner-sdk .ot-sdk-container, #onetrust-pc-sdk .ot-sdk-container, #ot-sdk-cookie-policy .ot-sdk-container {\n   position: relative;\n   max-width: 100%;\n   margin:0 auto;\npadding: 0 10px 10px 10px; /* top right bottom left */\n   box-sizing: border-box;\n}\n\n\n#onetrust-banner-sdk.ot-wo-title #onetrust-group-container {\n    margin-top: 0px;\n}\n\n#onetrust-banner-sdk.ot-bnr-w-logo .ot-bnr-logo {\n    height: 30px;\n    width: 300px;\n}", "PCCustomCSS": "#onetrust-pc-sdk .ot-pc-logo {\n    height: 40px;\n    width: 300px;\n}\n\n#onetrust-pc-sdk div.ot-cat-item[data-optanongroupid=\"BG71\"] {\n    display: none !important;\n}\n\n#onetrust-pc-sdk .ot-pc-footer-logo {\ndisplay: none !important; \n}\n\n\n\n#onetrust-pc-sdk #ot-category-title {\n    font-size: 100px; \n}\n\n\n#onetrust-pc-sdk #ot-pc-title {\n    font-size: 2em !important; \n}\n\n\n#onetrust-pc-sdk .ot-cat-header {\n    font-size: 14px !important;\n}\n\n#onetrust-pc-sdk .ot-category-title {\n    padding: 0 !important;\n    margin: 0 !important;\n}\n\n#onetrust-pc-sdk #ot-pc-desc {\n    margin-bottom: 0px;\n}\n\n\n#onetrust-pc-sdk .ot-cat-item {\n    margin-top: 0px;\n}\n\n\n#onetrust-pc-sdk #ot-category-title {\n    padding-bottom: 0px;\n}\n\n#onetrust-pc-sdk ul li {\n    padding: 5px 10px !important; /* Adjust padding to reduce top, bottom, and left padding */\n}\n\n#onetrust-pc-sdk .ot-cat-header {\n    font-size: 12px !important;\n}\n", "PcTextColor": "#000000", "PcButtonColor": "#2b2b2b", "PcButtonTextColor": "#FFFFFF", "PcBackgroundColor": "#FFFFFF", "PcMenuColor": "#F4F4F4", "PcMenuHighLightColor": "#FFFFFF", "PcAccordionBackgroundColor": "#F8F8F8", "PCenterExpandToViewText": "", "PcEnableToggles": false, "PcLinksTextColor": "#0070c0", "TextColor": "#000000", "ButtonColor": "#346E4A", "BannerMPButtonColor": "#ffffff", "BannerMPButtonTextColor": "#346E4A", "ButtonTextColor": "#FFFFFF", "BackgroundColor": "#FFFFFF", "BannerLinksTextColor": "#3860BE", "BannerAccordionBackgroundColor": "#E9E9E9", "CookiePersistentLogo": "https://cdn.cookielaw.org/logos/static/ot_persistent_cookie_icon.png", "OptanonLogo": "https://cdn.cookielaw.org/logos/************************************/d229a1c3-b8ad-46ad-9bdb-8311fd460a5f/c85f6701-9eae-4a9f-b770-87eee2dae9fc/<EMAIL>", "BnrLogo": "", "OneTrustFooterLogo": "https://cdn.cookielaw.org/logos/static/powered_by_logo.svg", "OptanonCookieDomain": "gap.com", "OptanonGroupIdPerformanceCookies": "C0002", "OptanonGroupIdFunctionalityCookies": "C0003", "OptanonGroupIdTargetingCookies": "C0004", "OptanonGroupIdSocialCookies": "C0005", "ShowSubGroupCookies": false, "LegacyBannerLayout": "default_flat_bottom_two_button_black", "OptanonHideCookieSettingButton": "hide-cookie-setting-button", "UseRTL": false, "ShowBannerAcceptButton": false, "ShowBannerCookieSettings": false, "ShowCookieList": false, "PCShowCookieHost": false, "PCShowCookieDuration": false, "PCShowCookieType": false, "PCShowCookieCategory": false, "PCShowCookieDescription": false, "AllowHostOptOut": false, "CookieListTitleColor": "#696969", "CookieListGroupNameColor": "#696969", "CookieListTableHeaderColor": "#696969", "CookieListTableHeaderBackgroundColor": "#F8F8F8", "CookiesV2NewCookiePolicy": true, "CookieListPrimaryColor": "#696969", "CookieListCustomCss": "", "TTLGroupByTech": false, "TTLShowTechDesc": false, "ConsentIntegration": {"ConsentApi": "https://privacyportal.onetrust.com/request/v1/consentreceipts", "RequestInformation": "eyJhbGciOiJSUzUxMiJ9.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.VzVmTC0ARbItPKBzKWm0nHrfnEtzMgTpKUpepP39i-vzh4dQHjj01lIdbi5u5aBhjpnRbNsE6Zs3yfZEeT_xC6ZLrSAXII_dMOUvLyxFWiMp-ArlgtpYS3v0GtnOzbcTS6-YIizJyqBkTyopyYUghKFGrlH6xCJTbcXFHdPfuzQHGnLi1-2F9CzVcEvZhcuhwDVta_03hBtWlFoULDIQEfxBbJc34BoRDDVD1xqzayGKWQTIiQqWQH30g8tlVvBqyrek48c4kyQjNXaLaqA-7q1I72_QiGR5_9woe3eNdLaL93EPibg7QiSNx7qzjbgwGasSTGfhCHZ4KhmYHhugIE4DTrOwUW7wI8ocWjNt4WIiJqxv37PluOwBdF91RznDv91IFRz5f5gzC79s34RyRiMDdl-n553gVUQOHvcbRdgQF2-gi0rpQ7gbP3UAlF2Jy9uqkn3cAkYEuankj6YJbLaE5YD0NrcStm7IP0L5_Xgncdwk3vFHSTvXPD4lvNiOLk3PTJFVoYxxKv-_GGXid7vj4U9LVQgxKkPjZvSYZrzgYsR5vZJfn8UDEWE68jQxn86sNEoy9H-T2nowfmsM1G503H5CgXO4Xwmul6ysMQxeX5OEBHuV82pR-Ql7c2AKz9lmk3RH4Rk_wjyXzT220mv-VOlaYzrgucXPzduqBFs", "IdentifiedReceiptsAllowed": false, "DefaultIdentifier": "<PERSON>ie Unique <PERSON>d", "DefaultAnonymousIdentifier": "<PERSON>ie Unique <PERSON>d", "EnableJWTAuthForKnownUsers": false}, "BConsentPurposesText": "Consent Purposes", "BFeaturesText": "Features", "BLegitimateInterestPurposesText": "Legitimate Interest Purposes", "BSpecialFeaturesText": "Special Features", "BSpecialPurposesText": "Special Purposes", "BConsentText": "Consent", "BLegitInterestText": "Legitimate Interest", "IabLegalTextUrl": "https://tcf.cookiepedia.co.uk", "PCCListName": "Name", "PCCListHost": "Host", "PCCListDuration": "Duration", "PCCListType": "Type", "PCCListCategory": "Category", "PCCListDescription": "Description", "PCDialogClose": "[`dialog closed`]", "PcLegIntButtonColor": "#FFFFFF", "PcLegIntButtonTextColor": "#78808E", "BCategoryContainerColor": "#F9F9FC", "BCategoryStyleColor": "#3860BE", "BLineBreakColor": "#E9E9E9", "BSaveBtnColor": "#346E4A", "BCategoryStyle": "Checkbox", "BAnimation": "Remove_Animation", "BContinueColor": "#696969", "PCContinueColor": "#696969", "PCFooterLogoUrl": "https://www.onetrust.com/products/cookie-consent/", "PCFooterCookieProLogoUrl": null, "BFocusBorderColor": "#000000", "PCFocusBorderColor": "#000000", "TemplateName": "*GPC TRUE - CCPA Custom Template - Web", "GeoRuleGroupName": "Global Audience", "GeoRuleName": "*GAP Inc Sites", "OTCloseBtnLogo": "https://cdn.cookielaw.org/logos/static/ot_close.svg", "OTExternalLinkLogo": "https://cdn.cookielaw.org/logos/static/ot_external_link.svg", "OTGuardLogo": "https://cdn.cookielaw.org/logos/static/ot_guard_logo.svg"}, "NtfyConfig": {"ShowNtfy": false, "NtfyDuration": 5, "ShowCS": true, "CSType": "BUTTON", "CSTxt": "<PERSON><PERSON>", "Sync": {"Title": "Cookie Preferences", "TitleAlign": "left", "TitleColor": "#696969", "Desc": "Syncing...", "DescAlign": "left", "DescColor": "#696969", "BgColor": "#FFFFFF", "BdrColor": "#FFFFFF", "IconBgColor": "#1276CE", "ShowClose": true, "CloseAria": "Close", "ShowIcon": true}, "Complete": {"Title": "Cookie Preferences", "TitleAlign": "left", "TitleColor": "#696969", "Desc": "Synced", "DescAlign": "left", "DescColor": "#696969", "BgColor": "#FFFFFF", "BdrColor": "#FFFFFF", "IconBgColor": "#1276CE", "ShowClose": true, "CloseAria": "Close", "ShowIcon": true}, "CSButton": {"Color": "#FFFFFF", "BgColor": "#1276CE", "BdrColor": "#1276CE", "Align": "center"}, "CSLink": {"Color": "#1276CE", "Align": "center"}}, "OTTData": null, "MobileData": null}