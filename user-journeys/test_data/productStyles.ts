export interface StyleAttributes {
  fontFamily: string;
  titleFontSize: string;
  titleFontWeight: string;
  titleLetterSpacing: string;
}

export type Brand = 'at' | 'on' | 'gp' | 'gpf' | 'br' | 'brf';

type BrandStyleProperty = {
  [key in Brand]: StyleAttributes;
};

const styleProperty: BrandStyleProperty = {
  at: {
    fontFamily: '"Phantom Sans 0.9 v1", "Gap Sans", Helvetica, Arial, Roboto, sans-serif',
    titleFontSize: '20px',
    titleFontWeight: '400',
    titleLetterSpacing: '0px',
  },
  on: {
    fontFamily: '"ON Sans Text v1", "Gap Sans", Helvetica, Arial, Roboto, sans-serif',
    titleFontSize: '18px',
    titleFontWeight: '325',
    titleLetterSpacing: 'normal',
  },
  gp: {
    fontFamily: '"Gap Sans v1", Helvetica, Arial, Roboto, sans-serif',
    titleFontSize: '18px',
    titleFontWeight: '400',
    titleLetterSpacing: 'normal',
  },
  gpf: {
    fontFamily: '"Gap Sans v1", Helvetica, Arial, Roboto, sans-serif',
    titleFontSize: '18px',
    titleFontWeight: '400',
    titleLetterSpacing: 'normal',
  },
  br: {
    fontFamily: '"Banana v1", "Times New Roman", serif',
    titleFontSize: '18px',
    titleFontWeight: '400',
    titleLetterSpacing: '1px',
  },
  brf: {
    fontFamily: '"Banana v1", "Times New Roman", serif',
    titleFontSize: '18px',
    titleFontWeight: '400',
    titleLetterSpacing: '1px',
  },
};

export function getStyleProperty(brand: Brand, styleAttribute: keyof StyleAttributes): string {
  const stylePropertyValue = styleProperty[brand]?.[styleAttribute];
  return stylePropertyValue;
}
