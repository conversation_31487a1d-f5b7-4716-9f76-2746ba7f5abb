const _ = require('lodash');

const { BRAND, MARKET } = process.env;
const ENV = process.env.ENV === 'stage' || process.env.ENV === 'local' || process.env.ENV === 'wip-stage' ? 'preview' : process.env.ENV;

interface Brand {
  at: Market;
  br: Market;
  brf: Market;
  gp: Market;
  gpf: Market;
  on: Market;
}
interface Market {
  ca?: Environments;
  us: Environments;
}
interface Environments {
  preview: Products;
  prod: Products;
  stage: Products;
}

interface Products {
  bopisCid: string[];
  bopisLocationId: string[];
  bopisProduct: string[];
  bopisZipCode: string[];
  category: string[];
  cid: string[];
  divisionId: string[];
  dropshipProduct: string[];
  dropshipProductMadeToOrder: string[];
  dropshipStyleColor: string[];
  filterCid: string[];
  giftCard: string[];
  priceFilterCategory: string[];
  productBelow35: string[];
  productsOver35: string[];
  productBelow50: string[];
  productStyleColor: string[];
  productsOver50: string[];
  quickAddCategory: string[];
  standardProduct: string[];
  vrtCid: string[];
}

const products: Brand = {
  gp: {
    us: {
      prod: {
        standardProduct: [
          // We need to make sure all SKUs have reviews/ratings
          '5708680022702', // womens Mid Rise Organic Cotton '90s Loose Jeans
          '*************', // mens classic t-shirt
          '*************', // mens organic t-shirt
          '*************', // Chambray Shirt
          '*************', // Classic Cotton T-Shirt
          '*************', // Cotton Classic V T-Shirt
          '*************', // womens jeans
        ],
        productStyleColor: ['740140032'], // V-Neck T-Shirt
        dropshipProduct: [
          '6632540020000', // toy
        ],
        bopisProduct: [
          '6659470020002', // skirts
          '*************', // womens jeans
        ],
        bopisZipCode: [
          '10036', // Time Square - NYC
        ],
        cid: [
          '1127938', // womens pants
        ],
        bopisCid: [
          '34608', // womens Shirts & Tops
        ],
        filterCid: [
          '35300', // shoes
        ],
        vrtCid: [
          '1082280', // boys shoes
        ],
        divisionId: [
          '5643', // women
        ],
        giftCard: ['000173'],
        category: [
          '5664', // Womens jeans
        ],
        productsOver50: [
          '7363950120003', // Trench coat
          '8562750020003', // Heavyweight hoodie
          '8562570120001', // Mens Pullover
        ],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow50: [],
        productBelow35: [],
        productsOver35: [],
      },
      preview: {
        standardProduct: [
          // We need to make sure all SKUs have reviews/ratings
          '*************', // mens Classic Cotton t-shirt
          '7953465720002', // women Organic Cotton Vintage T-Shirt
        ],
        productBelow50: [
          '6275530420002', // Puff Sleeve Shirt $49.95
        ],
        productStyleColor: [
          '740140032', // V-Neck T-Shirt
        ],
        dropshipProduct: [
          '722433', // crib
        ],
        bopisProduct: [
          '665835', // Preview Jeans with bopisLocationId=130
        ],
        bopisLocationId: ['130'],
        bopisZipCode: [
          '10036', // Time Square - NYC
        ],
        cid: [
          '1127938', // womens pants
        ],
        bopisCid: [
          '34608', // womens Shirts & Tops
        ],
        filterCid: [
          '35300', // shoes
        ],
        vrtCid: [
          '1082280', // boys shoes
        ],
        divisionId: [
          '5643', // women
        ],
        giftCard: ['000173'],
        category: [
          '5664', // Womens jeans
        ],
        productsOver50: [
          '7714430020001', // Womens jacket
        ],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow35: ['795399002'],
        productsOver35: ['406629002'],
      },
      stage: {
        standardProduct: [],
        productBelow50: [],
        productStyleColor: [],
        dropshipProduct: [
          '722433', //crib
        ],
        bopisProduct: ['818568'],
        bopisLocationId: [],
        bopisZipCode: [],
        cid: [],
        bopisCid: [],
        filterCid: [],
        vrtCid: [],
        divisionId: [],
        giftCard: ['000173'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow35: ['795399002'],
        productsOver35: ['406629002'],
      },
    },
    ca: {
      prod: {
        standardProduct: [
          // We need to make sure all SKUs have reviews/ratings
          '5709750032502', // High Rise Cheeky Straight Jeans
          '5708680032702', // womens Mid Rise Organic Cotton '90s Loose Jeans
        ],
        productBelow50: [
          '*************', // Kids Organic Cotton Uniform Oxford Shirt
        ],
        productBelow35: [
          '7673060130009', // Poplin PJ Shirt
        ],
        productStyleColor: ['740140433'], // V-Neck T-Shirt
        bopisProduct: [
          '6546610032701', // skirts
          '5708680032702', // womens jeans
        ],
        bopisZipCode: [
          'H1B 5K8', // Montreal
        ],
        cid: [
          '1127938', // womens pants
        ],
        bopisCid: [
          '34608', // womens Shirts & Tops
        ],
        vrtCid: [
          '1082280', // boys shoes
        ],
        divisionId: [
          '5643', // women
        ],
        giftCard: ['000166'],
        category: [
          '5664', // Womens jeans
        ],
        productsOver50: [
          '7421020030003', // Womens Sherpa Coat
          '8151400130004', // Womens Cable knit
          '7961650031106', // Mens Wool jacket
          '7742290130004', // Mens Sweater Hoodie
        ],
        dropshipProduct: [],
        filterCid: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productsOver35: [],
      },
      preview: {
        standardProduct: [
          // We need to make sure all SKUs have reviews/ratings
          '7368720030000', // Denim Pocket Shirt
          '7953460130002', // women Organic Cotton Vintage T-Shirt
        ],
        productBelow50: [
          '*************', // Kids Organic Cotton Uniform Oxford Shirt
        ],
        productBelow35: [
          '7673060130009', // Poplin PJ Shirt
        ],
        productStyleColor: [
          '740140032', // V-Neck T-Shirt
        ],
        dropshipProduct: [
          '6632540020000', // toy
        ],
        bopisProduct: [
          '508733002', // skirts
          '406750002', // womens jeans
        ],
        bopisZipCode: [
          '10036', // Time Square - NYC
        ],
        cid: [
          '1127938', // womens pants
        ],
        bopisCid: [
          '34608', // womens Shirts & Tops
        ],
        vrtCid: [
          '1082280', // boys shoes
        ],
        divisionId: [
          '5643', // women
        ],
        giftCard: ['000166'],
        category: [
          '5664', // Womens jeans
        ],
        filterCid: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productsOver35: [],
      },
      stage: {
        standardProduct: [],
        productBelow50: [],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['818568'],
        bopisLocationId: [],
        bopisZipCode: [],
        cid: [],
        bopisCid: [],
        filterCid: [],
        vrtCid: [],
        divisionId: [],
        giftCard: ['000166'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow35: [],
        productsOver35: [],
      },
    },
  },
  gpf: {
    us: {
      prod: {
        standardProduct: [
          '7648240013402', // womens Mid Rise Classic Straight Jeans
          '8923170210002', // women Organic Cotton Vintage Crewneck T-Shirt
        ],
        productBelow50: [
          '*************', // Denim Big Shirt
        ],
        productBelow35: [
          '6658130711104', // Oxford Shirt in Standard Fit
        ],
        productStyleColor: ['158082051'], // V-Neck T-Shirt
        bopisProduct: [
          '*************', // Waffle-Knit Graphic T-Shirt
        ],
        bopisZipCode: [
          '10036', // Time Square - NYC
        ],
        cid: [
          '1150781', // women pants
        ],
        bopisCid: [
          '1127845', // women shirts & tops
        ],
        filterCid: [
          '1150781', // all styles
        ],
        vrtCid: [
          '1041827', // Accessories & Socks
        ],
        divisionId: [
          '1040941', // women
        ],
        giftCard: ['000220'],
        category: [
          '1041624', // Womens jeans
        ],
        dropshipProduct: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productsOver35: [],
      },
      preview: {
        standardProduct: [
          '5388901010002', // Kids Gap Logo Joggers
          '5554930110000', // GapFit Relaxed Brushed Tech Jersey Straight Leg Pants
          '8923170210002', // women Organic Cotton Vintage Crewneck T-Shirt
        ],
        productBelow50: [
          '*************', // Denim Big Shirt
        ],
        productBelow35: [
          '6658130711104', // Oxford Shirt in Standard Fit
        ],
        bopisZipCode: [
          '10036', // Time Square - NYC
        ],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['538890021'],
        cid: [
          '1150781', // women pants
        ],
        bopisCid: [
          '1127845', // women shirts & tops
        ],
        filterCid: [],
        vrtCid: [
          '1041827', // Accessories & Socks
        ],
        divisionId: [
          '1040941', // women
        ],
        giftCard: ['000220'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productsOver35: [],
      },
      stage: {
        standardProduct: [],
        productBelow50: [],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['538890021'],
        bopisLocationId: [],
        bopisZipCode: [],
        cid: [],
        bopisCid: [],
        filterCid: [],
        vrtCid: [],
        divisionId: [],
        giftCard: ['000220'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow35: ['538890021'],
        productsOver35: [],
      },
    },
  },
  on: {
    us: {
      prod: {
        standardProduct: [
          '7348940020602', // womens High-Waisted Wow Super-Skinny Jeans
          '*************', // women Organic Cotton Vintage Crewneck T-Shirt
        ],
        productStyleColor: [
          '539511102', // womens Tank Top
        ],
        bopisProduct: [
          '*************', // womens T-Shirt
        ],
        bopisZipCode: [
          '10036', // Time Square - NYC
        ],
        cid: [
          '1185233', // womens pants
        ],
        bopisCid: [
          '1035712', // womens t-shirts
        ],
        filterCid: [
          '1035712', // t-shirts
        ],
        vrtCid: [
          '5984', // boys shoes
        ],
        divisionId: [
          '1185233', // women
        ],
        quickAddCategory: [
          '3016752', // family matching shirts
        ],
        priceFilterCategory: [
          '5199', // men's jeans
        ],
        giftCard: ['000152'],
        productsOver50: [
          '8467130020002', // Womens Trench Coat
          '7520750120001', // Womens Jacket
          '8466500220002', // Mens Jacket
          '7466900020001', // Mens Blazer
        ],
        dropshipProduct: [],
        category: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        productBelow50: [],
        productBelow35: [],
        productsOver35: [],
      },
      preview: {
        standardProduct: [
          '5491070625628', // mens Classic Cotton t-shirt
          '7502600320000', // Plush Long-Sleeve Crew-Neck T-Shirt for Women
          '*************', // women Organic Cotton Vintage Crewneck T-Shirt
        ],
        productBelow50: [
          '5408720020000', // Black Jean Utility Workwear Shirt for Men
        ],
        productBelow35: [
          '220383002', // Long-Sleeve Split-Neck Top for Women
        ],
        dropshipProduct: ['1170657'],
        bopisProduct: [
          '855428002', // skirts
          // '*************', // womens jeans
        ],
        bopisZipCode: [
          '10036', // Time Square - NYC
        ],
        cid: [
          '1185233', // womens pants
        ],
        bopisCid: [
          '1035712', // womens t-shirts
        ],
        vrtCid: [
          '5984', // boys shoes
        ],
        divisionId: [
          '1185233', // women
        ],
        giftCard: ['000152'],
        category: [
          // '5664', // Womens jeans
          '7502600320003', // Women's Long Sleeve
        ],
        productsOver50: [
          '*************', // Mens topcoat
          '7520970020003', // Womens sherpa
        ],
        productStyleColor: [
          '539511102', // womens Tank Top
        ],
        filterCid: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productsOver35: ['760566002'],
      },
      stage: {
        standardProduct: [],
        productBelow50: [],
        productStyleColor: ['407511012'],
        dropshipProduct: ['1170657'],
        bopisProduct: ['734889002'],
        bopisLocationId: ['130'],
        bopisZipCode: [],
        cid: [],
        bopisCid: [],
        filterCid: [],
        vrtCid: [],
        divisionId: [],
        giftCard: ['000152'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow35: ['220383002'],
        productsOver35: ['760566002'],
      },
    },
    ca: {
      prod: {
        standardProduct: [
          '7324730030602', // High-Waisted Wow Super-Skinny Jeans for Women
          '*************', // PowerSoft Support Crop Top
          '*************', // High-Waisted PowerSoft Joggers
          '8922290330002', // Light Support PowerSoft Longline Sports Bra
        ],
        productStyleColor: [
          '539511102', // womens Tank Top
        ],
        bopisProduct: [
          '*************', // womens T-Shirt
        ],
        bopisZipCode: [
          'H1B 5K8', // Montreal
        ],
        cid: [
          '10018', // womens new arrivals
        ],
        bopisCid: [
          '1035712', // womens t-shirts
        ],
        filterCid: [
          '1035712', // t-shirts
        ],
        vrtCid: [
          '5984', // boys shoes
        ],
        divisionId: [
          '1185233', // women
        ],
        quickAddCategory: [
          '3016752', // family matching shirts
        ],
        priceFilterCategory: [
          '5199', // men's jeans
        ],
        giftCard: ['000157'],
        productsOver50: [
          '8467130020002', // Womens Trench Coat
          '7520750120001', // Womens jacket
          '8466500220002', // Mens Jacket
          '7466900020001', // Mens Blazer
        ],
        dropshipProduct: [],
        category: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        productBelow50: [],
        productBelow35: [],
        productsOver35: [],
      },
      preview: {
        standardProduct: [
          // We need to make sure all SKUs have reviews/ratings
          '5593211130000', // Oversized Boyfriend Shirt for Women
          '5373780630002', // Bestee Crop T-Shirt
          '5373881530002', // Vintage Crew-Neck T-Shirt
        ],
        productBelow50: [
          '7452900530003', // High-Waisted Pulla Utility Pants $44.99
        ],
        productBelow35: [
          '7265930130002', // Long-Sleeve Swing Top for Girls $34.99
        ],
        productStyleColor: [
          // '740140032', // V-Neck T-Shirt
        ],
        dropshipProduct: [
          // '6632540020000', // toy
        ],
        bopisProduct: [
          '832418123', // skirts
          // '*************', // womens jeans
        ],
        bopisZipCode: [
          'H1B 5K8', // Montreal
        ],
        cid: [
          '10018', // womens new arrivals
        ],
        bopisCid: [
          '1035712', // womens t-shirts
        ],
        vrtCid: [
          '5984', // boys shoes
        ],
        divisionId: [
          '1185233', // women
        ],
        giftCard: ['000157'],
        category: [
          // '5664', // Womens jeans
        ],
        filterCid: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productsOver35: [],
      },
      stage: {
        standardProduct: [],
        productBelow50: [],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['770697002'],
        bopisLocationId: [],
        bopisZipCode: [],
        cid: [],
        bopisCid: [],
        filterCid: [],
        vrtCid: [],
        divisionId: [],
        giftCard: ['000157'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow35: [],
        productsOver35: [],
      },
    },
  },
  at: {
    us: {
      prod: {
        standardProduct: [
          '5336550020004', // Trekkie North Jogger
          '5334090020003', // WITH EASE TEE
        ],
        productStyleColor: [
          '530501392', // seamless tank black
        ],
        bopisProduct: [
          '5336730120001', // stash tight
          '5305040520002', // seamless tee
        ],
        bopisZipCode: [
          '94115', // targeting Fillmore SF to top of list
        ],
        cid: [
          '1032080', // Pants
        ],
        bopisCid: [
          '1011679', // Short Sleeve Tops
        ],
        filterCid: [
          '1017102', // new arrivals
        ],
        vrtCid: [
          '1013846', // hats
        ],
        giftCard: ['000126'],
        productsOver50: [
          '6574900820004', // Salutation zip up
          '5997500320001', // Elation Tight
          '*************', // Triumph Hoodie
          '6593100020001', // Sweatshirt Dress
        ],
        dropshipProduct: [],
        divisionId: [
          '1054832', // Athleta Girl
        ],
        category: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow50: [],
        productBelow35: [],
        productsOver35: [],
      },
      preview: {
        standardProduct: [
          '5997490020002', // Women's leggings
          '2917490020003', // Women's shorts
        ],
        productsOver50: [
          '6594470120004', // Wrap leggings
          '6585170120001', // Womens sweater
        ],
        productStyleColor: [
          '530501392', // seamless tank black
        ],
        productBelow50: [
          '6591040320000', // Ether Seamless Mesh Tee
        ],
        productBelow35: [
          '*************', // All About Medium Cosmetic Pouch
        ],
        bopisZipCode: [
          '10036', // Time Square - NYC
        ],
        cid: [
          '1032080', // Pants
        ],
        bopisCid: [
          '1011679', // Short Sleeve Tops
        ],
        vrtCid: [
          '1013846', // hats
        ],
        dropshipProduct: ['11706610220000'],
        bopisProduct: ['531288322'],
        filterCid: [],
        divisionId: [
          '1054832', // Athleta Girl
        ],
        giftCard: ['000126'],
        category: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productsOver35: ['658398042'],
      },
      stage: {
        standardProduct: ['5997490020002'],
        productBelow50: [],
        productStyleColor: [],
        dropshipProduct: ['11706610220000'],
        bopisProduct: ['531288322'],
        bopisLocationId: [],
        bopisZipCode: [],
        cid: [],
        bopisCid: [],
        filterCid: [],
        vrtCid: [],
        divisionId: [],
        giftCard: ['000126'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow35: ['408463042'],
        productsOver35: ['658398042'],
      },
    },
    ca: {
      prod: {
        standardProduct: [
          '6583121230001', // COASTER LUXE JOGGER
          '5334090030003', // WITH EASE TEE
        ],
        productStyleColor: [
          '530501392', // seamless tank black
        ],
        bopisProduct: [
          '8539651530000', // stash tight
          '8539640430000', // seamless tee
        ],
        bopisZipCode: [
          'H1B 5K8', // Montreal
        ],
        cid: [
          '1032080', // All tops
        ],
        bopisCid: [
          '1011679', // Short Sleeve Tops
        ],
        filterCid: [
          '1017102', // new arrivals
        ],
        vrtCid: [
          '1013846', // hats
        ],
        giftCard: ['000262'],
        productsOver50: [
          '6574900820004', // Salutation Zip Up
          '5997500320001', // Elation Tight
          '*************', // Triumph Hoodie
          '6015430220003', // Pacifica Dress
        ],
        dropshipProduct: [],
        divisionId: [
          '1054832', // Athleta Girl
        ],
        category: [
          '1032080', // All Tops
        ],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow50: [],
        productBelow35: [],
        productsOver35: [],
      },
      preview: {
        standardProduct: [
          '5305045030003', // Momentum Seamless Tee
          '5334090030003', // WITH EASE TEE
        ],
        productBelow50: [
          '6578680030000', // Uptown Shirt
        ],
        productBelow35: [
          '5564010130000', // Momentum Seamless Polo Tee
        ],
        bopisZipCode: [
          'H1B 5K8', // Montreal
        ],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['531288322'],
        cid: [
          '1032080', // All tops
        ],
        bopisCid: [
          '1011679', // Short Sleeve Tops
        ],
        filterCid: [],
        vrtCid: [
          '1013846', // hats
        ],
        divisionId: [
          '1054832', // Athleta Girl
        ],
        giftCard: ['000262'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productsOver35: [],
      },
      stage: {
        standardProduct: [],
        productBelow50: [],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['531288322'],
        bopisLocationId: [],
        bopisZipCode: [],
        cid: [],
        bopisCid: [],
        filterCid: [],
        vrtCid: [],
        divisionId: [],
        giftCard: ['000262'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow35: [],
        productsOver35: [],
      },
    },
  },
  br: {
    us: {
      prod: {
        standardProduct: [
          '7570480520001', // CASHMERE SWEATER
          '7545520220402', // REFINED UTILITY PANT
        ],
        productStyleColor: [
          '443576032', // SHORT-SLEEVE TSHIRT
        ],
        dropshipProduct: [
          '636561012', // lion frame
          '638189002', // elephant frame
        ],
        dropshipProductMadeToOrder: [
          '6841110020016', // Frame
        ],
        cid: [
          '1158705', // pants
        ],
        bopisCid: [
          '1055063', // women t-shirts
        ],
        filterCid: [
          '26495', // new arrivals'
        ],
        vrtCid: [
          '1074157', // new arrivals'
        ],
        divisionId: [
          '5343', // men
        ],
        bopisZipCode: [
          '10036', // Time Square - NYC
        ],
        giftCard: ['000204'],
        productsOver50: [
          '7465270720004', // Womens Cashmere Turtleneck
          '7551300023202', // Womens corduroy pants
          '8109830120002', // Mens Wool Coat
          '8091410123202', // Mens suit pant
        ],
        bopisProduct: [],
        category: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow50: [],
        productBelow35: [],
        productsOver35: [],
      },
      preview: {
        dropshipProductMadeToOrder: [
          '6841110020016', // Frame
        ],
        standardProduct: [
          '7459960320000', // The Classic Shirt
          '7443051123130', // Men's pants
        ],
        productsOver50: [
          '5804170120003', // Women's trenchcoat
          '*************', // Mens jacket
        ],
        productStyleColor: [
          '443576032', // SHORT-SLEEVE TSHIRT
        ],
        bopisZipCode: [
          '10036', // Time Square - NYC
        ],
        cid: [
          '1158705', // pants
        ],
        bopisCid: [
          '1055063', // women t-shirts
        ],
        vrtCid: [
          '1074157', // // men hats
        ],
        divisionId: [
          '5343', // men
        ],
        dropshipProduct: [],
        bopisProduct: ['443576032'],
        filterCid: [],
        giftCard: ['000204'],
        category: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow50: [],
        productBelow35: ['744271042'],
        productsOver35: ['320601002'],
      },
      stage: {
        standardProduct: [],
        productBelow50: [],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['744271'],
        bopisLocationId: [],
        bopisZipCode: [],
        cid: [],
        bopisCid: [],
        filterCid: [],
        vrtCid: [],
        divisionId: [],
        giftCard: ['000204'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow35: ['744271042'],
        productsOver35: ['320601002'],
      },
    },
    ca: {
      prod: {
        standardProduct: [
          '7556170030009', // Bliss Silk Resort Shirt
          '5434410130009', // Silky Cotton Button-Down Shirt
        ],
        productStyleColor: [
          '443576032', // SHORT-SLEEVE TSHIRT
        ],
        dropshipProduct: [
          '636561012', // lion frame
          '638189002', // elephant frame
        ],
        dropshipProductMadeToOrder: [
          '6841110020016', // Frame
        ],
        cid: [
          '1158705', // pants
        ],
        bopisCid: [
          '1055063', // women t-shirts
        ],
        filterCid: [
          '26495', // new arrivals
        ],
        vrtCid: [
          '1074157', // men hats
        ],
        divisionId: [
          '5343', // men
        ],
        bopisZipCode: [
          'H1B 5K8', // Montreal
        ],
        giftCard: ['000154'],
        productsOver50: [
          '7465270720004', // Womens Cashmere Turtleneck
          '7551300023202', // Womens corduroy pants
          '7335140120000', // Mens Quilted jacket
          '8091410123202', // Mens suit pant
        ],
        bopisProduct: [''],
        category: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow50: [],
        productBelow35: [],
        productsOver35: [],
      },
      preview: {
        standardProduct: [
          '8001390030000', // SOFT WASH LONG-SLEEVE T-SHIRT
        ],
        productBelow50: [
          '7319630520000', // SOFT WASH HENLEY T-SHIRT
        ],
        productBelow35: [
          '5698370520000', // RUGGED SLUB CREW-NECK T-SHIRT
        ],
        dropshipProductMadeToOrder: [
          '6841110020016', // Frame
        ],
        bopisZipCode: [
          'H1B 5K8', // Montreal
        ],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['755269003'],
        cid: [
          '1158705', // pants
        ],
        bopisCid: [
          '1055063', // women t-shirts
        ],
        vrtCid: [
          '1074157', // // men hats
        ],
        filterCid: [],
        divisionId: [
          '5343', // men
        ],
        giftCard: ['000154'],
        category: [],
        productsOver50: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productsOver35: [],
      },
      stage: {
        standardProduct: [],
        productBelow50: [],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['744271'],
        bopisLocationId: [],
        bopisZipCode: [],
        cid: [],
        bopisCid: [],
        filterCid: [],
        vrtCid: [],
        divisionId: [],
        giftCard: ['000154'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow35: [],
        productsOver35: [],
      },
    },
  },
  brf: {
    us: {
      prod: {
        standardProduct: [
          '7627100314001', // UTILITY SHIRT
          '7584550010000', // HERITAGE SHIRT
        ],
        productStyleColor: [
          '405135101', // forever cardigan
        ],
        cid: [
          '1145487', // pants
        ],
        bopisCid: [
          '1044980', // women t-shirts
        ],
        filterCid: [
          '1191428', // pants
        ],
        vrtCid: [
          '1118439', // hats & scarves
        ],
        bopisZipCode: [
          '10036', // Time Square - NYC
        ],
        giftCard: ['000218'],
        dropshipProduct: [],
        bopisProduct: [],
        divisionId: [
          '1044620', // Women's Clothong
        ],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow50: [],
        productBelow35: [],
        productsOver35: [],
      },
      preview: {
        standardProduct: [
          '7156513010009', // SATIN CLASSIC SHIRT
          '5715950910002', // HERITAGE SHIRT
        ],
        productBelow50: [
          '7628670110000', // CROPPED DOUBLE POCKET SHIRT
        ],
        productBelow35: [
          '6030550410000', // SLIM OXFORD SHIRT
        ],
        bopisZipCode: [
          '10036', // Time Square - NYC
        ],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['885458041'],
        cid: [
          '1145487', // pants
        ],
        bopisCid: [
          '1044980', // women t-shirts
        ],
        filterCid: [],
        vrtCid: [
          '1118439', // hats & scarves
        ],
        divisionId: [
          '1044620', // Women's Clothong
        ],
        giftCard: ['000218'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productsOver35: [],
      },
      stage: {
        standardProduct: [],
        productBelow50: [],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['885458041'],
        bopisLocationId: [],
        bopisZipCode: [],
        cid: [],
        bopisCid: [],
        filterCid: [],
        vrtCid: [],
        divisionId: [],
        giftCard: ['000218'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow35: [],
        productsOver35: [],
      },
    },
    ca: {
      prod: {
        standardProduct: [
          '7627100314001', // UTILITY SHIRT
          '7584550010000', // HERITAGE SHIRT
        ],
        productStyleColor: [
          '405135101', // forever cardigan
        ],
        cid: [
          '1145487', // pants
        ],
        bopisCid: [
          '1044980', // women t-shirts
        ],
        filterCid: [
          '1191428', // pants
        ],
        vrtCid: [
          '1118446', // Handbags & Wallets
        ],
        bopisZipCode: [],
        giftCard: ['000710'],
        dropshipProduct: [],
        bopisProduct: [],
        divisionId: [
          '1044620', // Women's Clothong
        ],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow50: [],
        productBelow35: [],
        productsOver35: [],
      },
      preview: {
        standardProduct: [
          '7156513010009', // SATIN CLASSIC SHIRT
          '5715950910002', // HERITAGE SHIRT
        ],
        productBelow50: [
          '7628670110000', // CROPPED DOUBLE POCKET SHIRT
        ],
        productBelow35: [
          '6030550410000', // SLIM OXFORD SHIRT
        ],
        bopisZipCode: [],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['696134144'],
        cid: [
          '1145487', // pants
        ],
        bopisCid: [
          '1044980', // women t-shirts
        ],
        filterCid: [],
        vrtCid: [
          '1118439', // hats
        ],
        divisionId: [
          '1044620', // Women's Clothong
        ],
        giftCard: ['000710'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        bopisLocationId: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productsOver35: [],
      },
      stage: {
        standardProduct: [],
        productBelow50: [],
        productStyleColor: [],
        dropshipProduct: [],
        bopisProduct: ['603132124'],
        bopisLocationId: [],
        bopisZipCode: [],
        cid: [],
        bopisCid: [],
        filterCid: [],
        vrtCid: [],
        divisionId: [],
        giftCard: ['000710'],
        category: [],
        productsOver50: [],
        dropshipProductMadeToOrder: [],
        dropshipStyleColor: [],
        quickAddCategory: [],
        priceFilterCategory: [],
        productBelow35: [],
        productsOver35: [],
      },
    },
  },
};

export function getRandomProduct(brand: string = BRAND!, market: string = MARKET!): string {
  const productId = pickRandom(products[brand as keyof Brand]![market as keyof Market]![ENV as keyof Environments]!.standardProduct);
  return productId;
}

export function getRandomProductOver50(): string {
  const productId = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.productsOver50);
  return productId;
}

export function getRandomProductStyleColor(): string {
  const productId = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.productStyleColor);
  return productId;
}

export function getDropshipProduct(): string {
  const productId = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.dropshipProduct);
  return productId;
}

export function getDropshipMadeToOrderProduct(): string {
  const productId = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.dropshipProductMadeToOrder);
  return productId;
}

export function getDropshipStyleColor(): string {
  const productId = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.dropshipStyleColor);
  return productId;
}

export function getBopisProduct(): string {
  const productId = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.bopisProduct);
  return productId;
}
export function getBopisProductLocationId(): string {
  const productId = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.bopisLocationId);
  return productId;
}

export function getRandomCategory(): string {
  const categoryId = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.cid);
  return categoryId;
}

export function getBopisCategory(): string {
  const categoryId = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.bopisCid);
  return categoryId;
}

export function getFilterCategory(): string {
  const categoryId = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.filterCid);
  return categoryId;
}

export function getVrtCategory(): string {
  const categoryId = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.vrtCid);
  return categoryId;
}

export function getCategoryForISM(): string {
  const category = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.category);
  return category;
}

export function getBopisZipCode(): string {
  const bopisZip = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.bopisZipCode);
  return bopisZip;
}

export function getQuickAddProduct(): string {
  const quickAddCat = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.quickAddCategory);
  return quickAddCat;
}

export function getPriceFilterCategory(): string {
  const priceFilterCat = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.priceFilterCategory);
  return priceFilterCat;
}

export function getDivision(): string {
  const division = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.divisionId);
  return division;
}

export function getBelow50Product(): string {
  const shipProduct = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.productBelow50);
  return shipProduct;
}

export function getBelow35Product(): string {
  const shipProduct = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.productBelow35);
  return shipProduct;
}

function pickRandom(array: string[]) {
  return _.sample(array);
}

export function getGiftCardProduct(): string {
  const giftCardProduct = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.giftCard);
  return giftCardProduct;
}

export function getOver35Product(): string {
  const productId = pickRandom(products[BRAND as keyof Brand]![MARKET as keyof Market]![ENV as keyof Environments]!.productsOver35);
  return productId;
}
