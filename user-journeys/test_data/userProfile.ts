const { MARKET } = process.env;
const phone = '4158675309';

interface Market {
  ca: Users;
  us: Users;
}
interface Users {
  ok?: Attributes;
  ontario?: Attributes;
  sf?: Attributes;
  user: Attributes;
}

interface Attributes {
  city?: string;
  firstName?: string;
  lastName?: string;
  mobilePhone?: string;
  phone?: string;
  state?: string;
  street?: string;
  zipCode?: string;
}

const profiles: Market = {
  us: {
    user: {
      firstName: `<PERSON>`,
      lastName: `Shopsalot`,
      mobilePhone: phone,
    },
    sf: {
      street: '2 Folsom St',
      city: 'San Francisco',
      state: 'California',
      zipCode: '94105-1205',
      phone,
    },
    ok: {
      street: '1285 Luke Lane',
      city: 'Oklahoma',
      state: 'Oklahoma',
      zipCode: '73102',
      phone,
    },
  },
  ca: {
    user: {
      firstName: `Betty`,
      lastName: `Boutique`,
      mobilePhone: phone,
    },
    ontario: {
      street: '500 Kingston Rd',
      city: 'Toronto',
      state: 'Ontario',
      zipCode: 'M4L1V3',
      phone: '4111111111',
    },
  },
};

export function getProfile() {
  const profile = profiles[MARKET as keyof Market].user;
  return profile;
}

export function getSFAddress() {
  return getAddress('sf');
}

export function getOklahomaAddress() {
  return getAddress('ok');
}

export function getOntarioAddress() {
  return getAddress('ontario');
}

function getAddress(address: string) {
  return profiles[MARKET as keyof Market][address as keyof Users];
}
