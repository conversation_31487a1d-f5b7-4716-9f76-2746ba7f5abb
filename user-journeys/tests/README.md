# Creating and Updating Tests

## Page Object Model (POM)

Use the POM in favor of multiple spec files for tests that are specific to brand, market, or breakpoint. Tagging '@skip-mobile', '@skip-at' or similar skip tags is the current solution being used to avoid multiple spec files when something is truly unique. The test logic can often be reused and the selectors can be the only differing part if necessary. The locator [`or`](https://playwright.dev/docs/api/class-locator#locator-or) function can specify multiple values that apply if needed.

Logic for each page should be put in the corresponding POM file for organization. Locators should not be placed inside of the spec files.

## Test Creation

### A couple things to keep in mind

- When creating tests for covering new brands or breakpoints, we want to reuse as much logic as possible and differing only where absolutely necessary
- Use a little conditional logic as possible to reduce flakiness
- Every test needs an [assertion](https://playwright.dev/docs/test-assertions)
- Assertions should typically be [retrying assertions](https://playwright.dev/docs/test-assertions#auto-retrying-assertions)
- [waitForTimeout](https://playwright.dev/docs/api/class-page#page-wait-for-timeout) functions are flaky and **should be avoided** in favor of using waits that are more predictable like waiting on DOM elements to be visible

### Everything should be tested on a branch in GitHub Actions

Before a branch can be considered ready to publish, make sure you have ran each environment on [GitHub Actions](https://github.gapinc.com/ecomfrontend/ecom-next/actions/workflows/ujt-run-all-aks-test.yaml). Make sure every environment is tested to verify changes don't break any environment.

### Adding new testing scenarios to the package.json (new pages to be tested)

New package.json script lines should fit the following format:\
`"<page of functional area>-<brand abbr>-<market abbr>-<breakpoint>-<environment>": "BRAND=<brand abbr> MARKET=<market abbr> BREAKPOINT=<breakpoint> ENV=<environment> npx playwright test --project=<desktop or mobile> --invert-grep \"@skip-<brand abbr>\\b|@skip-<market abbr>\\b|@skip-<breakpoint>\\b|@skip-<environment>\\b\" --workers=99% <test directory>"`

> \\\b is used as a regex character to make sure the proper ending is identified.

#### The following values can be used:

##### Brand Abbreviation `<brand abbr>`:

```
at
br
brf
gp
gpf
on
```

##### Market Abbreviation `<market abbr>`:

```
us
ca
```

##### Breakpoint `<breakpoint>`:

```
desktop
mobile
```

##### Environment `<environment>`:

```
local
stage
preview
prod
```

### Adding the new npm scripts to be run in GitHub Actions

Edit .github/workflows/ujt-run-all-aks-test.yaml

There is a section under the setup job at the top with an array of npm scripts to run. Add your script name there:

```
prod)
    echo 'scripts<<EOF
    [
        "sitewide-at-us-desktop-prod",
        "sitewide-at-us-mobile-prod",
        "sitewide-at-ca-desktop-prod",
        "sitewide-at-ca-mobile-prod",
        "sitewide-br-us-desktop-prod",
        "utility-at-us-desktop-prod",
        "utility-at-us-mobile-prod",
        "utility-at-ca-desktop-prod",
        "utility-at-ca-mobile-prod"
    ]' >> $GITHUB_OUTPUT
    echo 'EOF' >> $GITHUB_OUTPUT
    ;;
```
