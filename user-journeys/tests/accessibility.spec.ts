/* eslint-disable playwright/expect-expect */
// @ts-check
import { Page, TestInfo } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';
import { createHtmlReport } from 'axe-html-reporter';
import * as allure from 'allure-js-commons';
import test from '../pom/base.page';

const { BRAND: brand, MARKET: market, BREAKPOINT: breakpoint, ENV: env } = process.env;

const commonAccessibilitySteps = async (page: Page, testInfo: TestInfo, name: string) => {
  await page.waitForLoadState('load');

  await test.step('Create accessibility results', async () => {
    const accessibilityScanResults = await new AxeBuilder({ page }).withTags(['wcag2a', 'wcag2aa']).analyze();

    const reportHTML = createHtmlReport({
      results: accessibilityScanResults,
      options: {
        projectKey: `${name} page`,
        doNotCreateReportFile: true,
      },
    });

    await testInfo.attach('Axe Report', { body: reportHTML, contentType: 'text/html' });

    accessibilityScanResults.violations.length > 0 ? await allure.parameter('Status', 'needs attention') : await allure.parameter('Status', 'no issues');
  });
};

test.describe(`Accessiblity Testing`, { tag: `@run-${brand}` }, () => {
  test(`${brand}-${market}-${breakpoint}-${env}: Home page should not have any automatically detectable accessibility issues`, async ({
    homePage,
    page,
  }, testInfo) => {
    await test.step(`I go to the home page`, async () => {
      await homePage.goToHomePage();
    });

    await commonAccessibilitySteps(page, testInfo, 'Home');
  });

  test(`${brand}-${market}-${breakpoint}-${env}: Customer service page should not have any automatically detectable accessibility issues`, async ({
    customerServicePage,
    page,
  }, testInfo) => {
    await test.step(`I go to the home page`, async () => {
      await customerServicePage.goToCustomerServicePage();
    });

    await commonAccessibilitySteps(page, testInfo, 'Customer Service');
  });
});
