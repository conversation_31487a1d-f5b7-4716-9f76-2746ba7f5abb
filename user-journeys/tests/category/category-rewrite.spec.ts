/* eslint-disable playwright/no-force-option */
// @ts-check
import { expect } from '@playwright/test';
import * as allure from 'allure-js-commons';
import { areWeInNext, getPageType, scrollToBottomUntilPageDoesNotGrow, scrollElementToCenter, scrollToTop, waitForHydration } from '../../utils/helpers';
import test from '../../pom/base.page';
import Builder from '@/test_data/builder';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`Category`, { tag: ['@run-all'] }, () => {
  test.describe(`Rewrite`, () => {
    test.beforeEach(async ({ page, rewriteCategoryPage, sitewide }) => {
      await test.step(`Go to Category Page`, async () => {
        // await page.route(/cn.*\.jpe?g/, route => route.abort('aborted'));
        await allure.description('Category tests have product images removed to improve performance in CI.');
        const category = builder.withCategory();
        await rewriteCategoryPage.goToCategoryPage(category);
        await waitForHydration(page);
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });

      await test.step(`Ensure that Sitewide Components are visible on page.`, async () => {
        await sitewide.checkSiteWideElementsAreOnPage();
      });
    });

    test.describe(`NAVIGATE`, () => {
      // skipping ON CA for this test as there is no division page type for ON CA
      test(
        `${brand}-${market}-${breakpoint}-${env}: Navigate to Category Page from Division page.`,
        { tag: ['@skip-on-ca'] },
        async ({ page, utilityPage, rewriteCategoryPage }) => {
          await test.step(`Go to Division Page`, async () => {
            const division = builder.withDivision();
            await utilityPage.navigateToDivision(division);
          });

          await test.step('I click on Category Link from the Division page', async () => {
            await utilityPage.selectCategory();
          });

          await test.step('I validate we land on the category page', async () => {
            await page.waitForURL(/cid=/);
            await expect.poll(async () => await getPageType(page)).toBe('category');
            await page.evaluate(() => window.scrollTo(0, document.documentElement.scrollHeight));
            // Verify the product grid is visible on the category page
            await expect(rewriteCategoryPage.productGrid, 'Expected product grid to be visible on category page').toBeVisible({ timeout: 30000 });
          });
        }
      );

      test(
        `${brand}-${market}-${breakpoint}-${env}: Navigate from using breadcrumb link on category page.`,
        { tag: ['@run-at', '@run-br-ca'] },
        async ({ page, rewriteCategoryPage }) => {
          await test.step('I click on Division breadcrumb Link from the Category page', async () => {
            await rewriteCategoryPage.clickDivisionBreadcrumb();
          });

          await test.step('I validate that we land on the expected Page Type', async () => {
            await page.waitForURL(/cid=/);
            await expect.poll(async () => await getPageType(page)).toMatch(/category|division/);
          });
        }
      );

      test(
        `${brand}-${market}-${breakpoint}-${env}: Navigate from Category to Product Page using the swatch More Colors Link.`,
        { tag: ['@run-at-us', '@skip-br', '@skip-on-ca'] },
        async ({ page, rewriteCategoryPage }) => {
          await test.step('I click on More Colors link from the Category page', async () => {
            await scrollToBottomUntilPageDoesNotGrow(page);
            await scrollElementToCenter(rewriteCategoryPage.moreColorsonPdp.first());
            await rewriteCategoryPage.moreColorsonPdp.click({ force: true });
          });

          await test.step('I validate that we land on the Product Page with Page Type of product', async () => {
            await page.waitForURL(/pid=/);
            await expect.poll(async () => await getPageType(page), 'ensure we see Product Page type').toBe('product');
          });
        }
      );

      test(
        `${brand}-${market}-${breakpoint}-${env}: Navigate to related category using Related Items link.`,
        { tag: ['@run-at', '@run-br-ca', '@skip-stage'] },
        async ({ page, rewriteCategoryPage }) => {
          await test.step('I click on Related Items Link to go to new category page', async () => {
            const initialCatPage = page.url();
            await rewriteCategoryPage.clickRelatedItemsLink();
            await expect(page).not.toHaveURL(initialCatPage);
          });

          await test.step('Ensure product grid is visible on page with the expected Page Type', async () => {
            await waitForHydration(page);
            await expect.poll(async () => await getPageType(page)).toMatch(/category|division/);
          });
        }
      );
    });

    test.describe(`PRODUCT GRID`, { tag: ['@run-all'] }, () => {
      test(
        `${brand}-${market}-${breakpoint}-${env}: Ensure number of columns in product grid is 3 when the viewport is a small desktop`,
        { tag: ['@skip-mobile'] },
        async ({ page, rewriteCategoryPage }) => {
          await test.step(`I change the viewport size to small desktop 1024 to 1279px`, async () => {
            await page.setViewportSize({ width: 1195, height: 1080 });
            await page.reload();
            await waitForHydration(page);
          });

          await test.step('I check the grid columns to be 3', async () => {
            await expect(rewriteCategoryPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteCategoryPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(3);
          });

          await test.step(`I change the viewport size lesser than a small desktop 1024 to 1279px`, async () => {
            await page.setViewportSize({ width: 1023, height: 1080 });
            await page.reload();
            await waitForHydration(page);
          });

          await test.step('I check the grid columns to be 2', async () => {
            await expect(rewriteCategoryPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteCategoryPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(2);
          });

          await test.step(`I change the viewport size back to small desktop 1024 to 1279px`, async () => {
            await page.setViewportSize({ width: 1024, height: 1080 });
            await page.reload();
            await waitForHydration(page);
          });

          await test.step('I check the grid columns to be 3', async () => {
            await expect(rewriteCategoryPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteCategoryPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(3);
          });
        }
      );

      test(
        `${brand}-${market}-${breakpoint}-${env}: Ensure number of columns in product grid is 4 when the viewport is a large desktop`,
        { tag: ['@skip-mobile', '@skip-br'] },
        async ({ page, rewriteCategoryPage }) => {
          await test.step(`I change the viewport size to Large Desktop 1280 to 1567px`, async () => {
            await page.setViewportSize({ width: 1395, height: 1080 });
            await page.reload();
            await waitForHydration(page);
          });

          await test.step('I check the grid columns to be 4', async () => {
            expect(page.viewportSize()?.width).toBeGreaterThan(1390);
            await expect(rewriteCategoryPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteCategoryPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(4);
          });

          await test.step(`I change the viewport size to XL desktop 1568px+`, async () => {
            await page.setViewportSize({ width: 1695, height: 1080 });
            await page.reload();
            await waitForHydration(page);
          });

          await test.step('I check the grid columns to be 4', async () => {
            expect(page.viewportSize()?.width).toBeGreaterThan(1568);
            await expect(rewriteCategoryPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteCategoryPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(4);
          });

          await test.step(`I change the viewport size back to Large desktop 1280 to 1567px`, async () => {
            await page.setViewportSize({ width: 1280, height: 1080 });
            await page.reload();
            await waitForHydration(page);
          });

          await test.step('I check the grid columns to be 4', async () => {
            expect(page.viewportSize()?.width).toBeLessThan(1568);
            await expect(rewriteCategoryPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteCategoryPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(4);
          });

          await test.step(`I change the viewport size down to Small Desktop 1024 to 1279px`, async () => {
            await page.setViewportSize({ width: 1279, height: 1080 });
            await page.reload();
            await waitForHydration(page);
          });

          await test.step('I check the grid columns to be 3', async () => {
            expect(page.viewportSize()?.width).toBeLessThan(1280);
            await expect(rewriteCategoryPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteCategoryPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(3);
          });
        }
      );

      test(
        `${brand}-${market}-${breakpoint}-${env}: Ensure number of columns in product grid is 2 when the viewport is a mobile or tablet`,
        { tag: ['@skip-desktop'] },
        async ({ rewriteCategoryPage }) => {
          await test.step('I check the grid columns to be 2', async () => {
            await expect(rewriteCategoryPage.productGridInnerGrid).toBeVisible({ timeout: 20000 });
            await expect
              .poll(
                async () =>
                  (await rewriteCategoryPage.productGridInnerGrid.evaluate(el => window.getComputedStyle(el).getPropertyValue('grid-template-columns'))).split(
                    ' '
                  ).length
              )
              .toBe(2);
          });
        }
      );
    });

    test.describe(`PRODUCT CARD`, { tag: ['@run-all'] }, () => {
      test(
        `${brand}-${market}-${breakpoint}-${env}: Ensure products images, description & price are in place & accessible.`,
        { tag: ['@run-at', '@run-on'] },
        async ({ rewriteCategoryPage }) => {
          // await test.step(`I ensure Product Cards are visible on the page.`, async () => {
          //   expect(await rewriteCategoryPage.getProductCardCount()).toBeGreaterThanOrEqual(1);
          // });

          await test.step(`I ensure Product images are visible on the page and link to a product page.`, async () => {
            expect(await rewriteCategoryPage.getProductImagesCount()).toBeGreaterThanOrEqual(1);
            await expect(rewriteCategoryPage.productImageLinks.first()).toHaveAttribute('href', /product.do/);
          });

          await test.step(`I ensure Product Name is visible on the page and links to a product page.`, async () => {
            await expect(rewriteCategoryPage.getProductName(rewriteCategoryPage.productCards.first())).not.toHaveText('');
            await expect(rewriteCategoryPage.getProductInfo(rewriteCategoryPage.productCards.first())).toHaveAttribute('href', /product.do/);
          });

          await test.step('I check the price of a product and ensure it has a value.', async () => {
            const price = await rewriteCategoryPage.getProductPrice(await rewriteCategoryPage.getRandomProductCard());
            expect(price).toMatch(/\$\d+\.\d{2}/);
          });
        }
      );
    });

    test.describe(`SORT`, { tag: ['@run-all'] }, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Check sort by by checking item order`, async ({ page, rewriteCategoryPage }) => {
        await test.step(`I check that the default sort selected is sort by featured`, async () => {
          await rewriteCategoryPage.allFacetsButton.click();
          await rewriteCategoryPage.sortFacetButton.click();
          await waitForHydration(page);
          await expect(
            rewriteCategoryPage.facetDrawer.locator('.plp_grid-drawer__radio-options').locator('#featured-label').getByText('Featured')
          ).toBeChecked();
          await rewriteCategoryPage.closeFilterDrawerButton.click();
          await rewriteCategoryPage.waitForProductCard();
        });

        await test.step('I go to price low-high sort', async () => {
          await rewriteCategoryPage.goToSort('priceLowHigh');
        });

        let requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/cc/);
        requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/cc/);

        await test.step(`I check that the URL updated correctly for sort by low to high`, async () => {
          const request = await requestPromise;
          const url = new URL(request.url());
          expect(url.searchParams.get('sortByField'), 'validate the API call to /commerce/search/products/v2/cc sortByField is set to price').toBe('price');
          expect(url.searchParams.get('sortByDir'), 'validate the API call to /commerce/search/products/v2/cc sortByDir is set to asc').toBe('asc');
          await waitForHydration(page);
          await rewriteCategoryPage.waitForProductCard();
          await expect(page, 'validate the url contains the query param sortBy is set to featured').toHaveURL(/.*sortByField=price.*/);
          await expect(page, 'validate the url contains the query param sortByDir is set to asc').toHaveURL(/.*sortByDir=asc.*/);
        });

        requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/cc/);
        await test.step('I go to price high-low sort', async () => {
          await scrollToTop(page);
          await rewriteCategoryPage.goToSort('priceHighLow');
        });

        await test.step(`I check that the URL updated correctly for sort by high to low`, async () => {
          const request = await requestPromise;
          const url = new URL(request.url());
          expect(url.searchParams.get('sortByField'), 'validate the API call to /commerce/search/products/v2/cc sortByField is set to price').toBe('price');
          expect(url.searchParams.get('sortByDir'), 'validate the API call to /commerce/search/products/v2/cc sortByDir is set to desc').toBe('desc');
          await waitForHydration(page);
          await rewriteCategoryPage.waitForProductCard();
          await expect(page, 'validate the url contains the query param sortBy is set to featured').toHaveURL(/.*sortByField=price.*/);
          await expect(page, 'validate the url contains the query param sortByDir is set to desc').toHaveURL(/.*sortByDir=desc.*/);
        });

        requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/cc/);
        await test.step('I go to featured sort', async () => {
          await scrollToTop(page);
          await rewriteCategoryPage.goToSort('featured');
        });

        await test.step(`I check that the URL updated correctly for sort by featured`, async () => {
          const request = await requestPromise;
          const url = new URL(request.url());
          expect(url.searchParams.get('sortByField'), "validate the API call to /commerce/search/products/v2/cc doesn't contain sortByField").toBe(null);
          expect(url.searchParams.get('sortByDir'), "validate the API call to /commerce/search/products/v2/cc doesn't contain sortByDir").toBe(null);
          await waitForHydration(page);
          await rewriteCategoryPage.waitForProductCard();
          await expect(page, 'validate the url contains the query param sortByField is set to featured').toHaveURL(/.*sortByField=featured.*/);
          await expect(page, 'validate the url contains the query param sortByDir is set to unset').toHaveURL(/.*sortByDir=unset.*/);
        });
      });
    });

    test.describe(`FILTER`, { tag: '@run-all' }, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Filter category results with Category, Color, Price, and Size`, async ({ rewriteCategoryPage, page }) => {
        await page.waitForFunction('!!window.gap');
        const buildVersion = await page.evaluate('window.gap.buildVersion.nextApp');

        await test.step(`Build Version is ${buildVersion}`, async () => {});

        let initialNumberOfItems = await rewriteCategoryPage.getItemCount();
        await test.step('I click the filter button', async () => {
          await rewriteCategoryPage.allFacetsButton.click();
        });

        await test.step('I select a Category', async () => {
          await rewriteCategoryPage.categoryFilterDropdown.click();
          await scrollElementToCenter(rewriteCategoryPage.categoryFilterOptions.first());
          await rewriteCategoryPage.categoryFilterOptions.first().click();
          await rewriteCategoryPage.closeFilterDrawerButton.click();
        });

        await test.step('I validate the url shows the category filter selected', async () => {
          await page.waitForURL(/.*style=.*/);
          await expect(page, 'validate the url has the category facet info').toHaveURL(/.*style=.*/);
          await waitForHydration(page);
        });

        await test.step('I validate that new products appear', async () => {
          await rewriteCategoryPage.waitForProductCard();
          await expect(rewriteCategoryPage.productCards.first(), 'validate the first product card is visible').toBeVisible({ timeout: 60000 });
          expect(await rewriteCategoryPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
            initialNumberOfItems
          );
        });

        await test.step('I clear the filters and validate products are updated', async () => {
          const filteredNumberOfItems = await rewriteCategoryPage.getItemCount();
          if (await rewriteCategoryPage.clearAllFacetsButton.isVisible()) {
            await rewriteCategoryPage.clearAllFacetsButton.click();
          } else {
            await rewriteCategoryPage.individualFacetClearButton.click();
          }
          await page.waitForURL(/^(?!(?=.*style=)).*$/);
          await expect(page, 'validate the url no longer contains facet information').not.toHaveURL(/style=/);
          await rewriteCategoryPage.waitForProductCard();
          await waitForHydration(page);
          expect(await rewriteCategoryPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
            filteredNumberOfItems
          );
        });

        initialNumberOfItems = await rewriteCategoryPage.getItemCount();
        await test.step('I select a Size', async () => {
          await rewriteCategoryPage.allFacetsButton.click();
          await waitForHydration(page);
          if (await rewriteCategoryPage.sizeFilterDropdown.isVisible()) {
            await rewriteCategoryPage.sizeFilterDropdown.click();
            await rewriteCategoryPage.sizeFilterOptions.first().click();
          } else {
            await rewriteCategoryPage.departmentFilterDropdown.click();
            if (brand === 'gpf') {
              await rewriteCategoryPage.departmentFilterOptions.getByText(/women/i).first().click();
            } else {
              await rewriteCategoryPage.departmentFilterOptions.first().click();
            }
            await rewriteCategoryPage.sizeFilterDropdown.click();
            await rewriteCategoryPage.sizeFilterOptions.first().click();
          }
        });

        await test.step('I validate the url shows the size filter selected', async () => {
          await page.waitForURL(/.*size=.*/);
          await expect(page, 'validate the url has the size facet info').toHaveURL(/.*size=.*/);
          await waitForHydration(page);
        });

        await test.step('I validate that new products appear', async () => {
          await rewriteCategoryPage.waitForProductCard();
          await expect(rewriteCategoryPage.productCards.first()).toBeVisible({ timeout: 60000 });
          expect(await rewriteCategoryPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
            initialNumberOfItems
          );
        });

        await test.step('I clear the filters and validate products are updated', async () => {
          const filteredNumberOfItems = await rewriteCategoryPage.getItemCount();
          await rewriteCategoryPage.closeFacetsButton.click();
          if (await rewriteCategoryPage.clearAllFacetsButton.isVisible()) {
            await rewriteCategoryPage.clearAllFacetsButton.click();
          } else {
            await rewriteCategoryPage.individualFacetClearButton.click();
          }
          await page.waitForURL(/^(?!(?=.*size=)).*$/);
          await expect(page, 'validate the url no longer contains facet information').not.toHaveURL(/size=/);
          await rewriteCategoryPage.waitForProductCard();
          await waitForHydration(page);
          expect(await rewriteCategoryPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
            filteredNumberOfItems
          );
        });

        await test.step('I select a Color', async () => {
          initialNumberOfItems = await rewriteCategoryPage.getItemCount();
          await rewriteCategoryPage.allFacetsButton.click();
          await rewriteCategoryPage.colorFilterDropdown.click();
          await rewriteCategoryPage.colorFilterOptions.first().click();
          await rewriteCategoryPage.closeFilterDrawerButton.click();
        });

        await test.step('I validate the url shows the color filter selected', async () => {
          await waitForHydration(page);
          await page.waitForURL(/.*color=.*/);
          await expect(page).toHaveURL(/.*color=.*/);
          await waitForHydration(page);
        });

        await test.step('I validate that new products appear', async () => {
          await rewriteCategoryPage.waitForProductCard();
          await expect(rewriteCategoryPage.productCards.first()).toBeVisible({ timeout: 60000 });
          expect(await rewriteCategoryPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
            initialNumberOfItems
          );
        });

        await test.step('I clear the filters and validate products are updated', async () => {
          const filteredNumberOfItems = await rewriteCategoryPage.getItemCount();
          if (await rewriteCategoryPage.clearAllFacetsButton.isVisible()) {
            await rewriteCategoryPage.clearAllFacetsButton.click();
          } else {
            await rewriteCategoryPage.individualFacetClearButton.click();
          }
          await waitForHydration(page);
          await page.waitForURL(/^(?!color=).*$/);
          await expect(page, 'validate the url no longer contains facet information').not.toHaveURL(/color=/);
          await rewriteCategoryPage.waitForProductCard();
          await waitForHydration(page);
          expect(await rewriteCategoryPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
            filteredNumberOfItems
          );
        });

        initialNumberOfItems = await rewriteCategoryPage.getItemCount();
        await test.step('I select a Price range', async () => {
          await rewriteCategoryPage.allFacetsButton.click();
          await rewriteCategoryPage.priceFilterDropdown.click();
          const { width, height } = (await rewriteCategoryPage.priceSlider.boundingBox())!;
          await rewriteCategoryPage.priceSlider.hover({ position: { x: 0, y: height / 2 }, force: true });
          await page.mouse.down();
          await rewriteCategoryPage.priceSlider.hover({ position: { x: width / 5, y: height / 2 }, force: true });
          await page.mouse.up();
          await rewriteCategoryPage.priceSlider.hover({ position: { x: width, y: height / 2 }, force: true });
          await page.mouse.down();
          await rewriteCategoryPage.priceSlider.hover({ position: { x: width - width / 5, y: height / 2 }, force: true });
          await page.mouse.up();
        });

        await test.step('I validate the url shows the price filter selected', async () => {
          await page.waitForURL(/.*price=.*/);
          await expect(page).toHaveURL(/.*price=.*/);
          await waitForHydration(page);
        });

        await test.step('I validate that new products appear', async () => {
          await rewriteCategoryPage.waitForProductCard();
          await expect(rewriteCategoryPage.productCards.first()).toBeVisible({ timeout: 60000 });
          expect(await rewriteCategoryPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
            initialNumberOfItems
          );
        });

        await test.step('I clear the filters and validate products are updated', async () => {
          const filteredNumberOfItems = await rewriteCategoryPage.getItemCount();
          await rewriteCategoryPage.closeFacetsButton.click();
          if (await rewriteCategoryPage.clearAllFacetsButton.isVisible()) {
            await rewriteCategoryPage.clearAllFacetsButton.click();
          } else {
            await rewriteCategoryPage.individualFacetClearButton.click();
          }
          await page.waitForURL(/^(?!price=).*$/);
          await expect(page, 'validate the url no longer contains facet information').not.toHaveURL(/price=/);
          await rewriteCategoryPage.waitForProductCard();
          await waitForHydration(page);
          expect(await rewriteCategoryPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
            filteredNumberOfItems
          );
        });
      });

      test(`${brand}-${market}-${breakpoint}-${env}: Check flex facets functionality`, async ({ page, rewriteCategoryPage }) => {
        await test.step('I click the filter button', async () => {
          await rewriteCategoryPage.allFacetsButton.click();
        });

        let flexFacetCount;
        let facetCountGrabbedCount = 0;
        do {
          // eslint-disable-next-line playwright/no-wait-for-timeout
          await page.waitForTimeout(1000);
          flexFacetCount = await rewriteCategoryPage.getFlexFacetCount();
          facetCountGrabbedCount++;
        } while (flexFacetCount < 1 && facetCountGrabbedCount < 5);

        expect(flexFacetCount, 'validate that the flex facet count is greater than 0').toBeGreaterThan(0);
        for (let i = 0; i < flexFacetCount && i < 5; i++) {
          const initialNumberOfItems = await rewriteCategoryPage.getItemCount();
          await test.step('I select a facet option', async () => {
            const requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/cc/, { timeout: 40000 });
            await rewriteCategoryPage.clickNthFacet(i);
            await requestPromise;
          });

          await test.step('I validate that new products appear', async () => {
            await expect(rewriteCategoryPage.currentFacets).toHaveCount(1);
            await rewriteCategoryPage.waitForProductCard();
            await expect(rewriteCategoryPage.productCards.first()).toBeVisible({ timeout: 60000 });
            expect(await rewriteCategoryPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
              initialNumberOfItems
            );
          });

          await test.step('I clear the filters and validate products are updated', async () => {
            const filteredNumberOfItems = await rewriteCategoryPage.getItemCount();
            await rewriteCategoryPage.closeFacetsButton.first().click();
            const requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/cc/, { timeout: 40000 });
            if (await rewriteCategoryPage.clearAllFacetsButton.isVisible()) {
              await rewriteCategoryPage.clearAllFacetsButton.click();
            } else {
              await rewriteCategoryPage.individualFacetClearButton.click();
            }
            await requestPromise;
            await rewriteCategoryPage.waitForProductCard();
            await waitForHydration(page);
            await rewriteCategoryPage.currentFacets.waitFor({ state: 'hidden' });
            expect(await rewriteCategoryPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
              filteredNumberOfItems
            );
          });

          await test.step('I click the filter button', async () => {
            await rewriteCategoryPage.allFacetsButton.click();
          });
        }
      });

      test(`${brand}-${market}-${breakpoint}-${env}: Check quick filter functionality`, async ({ page, rewriteCategoryPage }) => {
        await rewriteCategoryPage.waitForProductCard();
        const quickFilterCount = await rewriteCategoryPage.getQuickFilterButtonsCount();
        for (let i = 0; i < quickFilterCount; i++) {
          const quickFilterText = await rewriteCategoryPage.quickFilterButtons.nth(i).textContent();
          await test.step(`I click the ${quickFilterText} quick filter button and confirm that the correct filter facet opens`, async () => {
            await rewriteCategoryPage.clickNthQuickFilterButton(i);
            await page.waitForFunction('!!window.gap');
            await expect(rewriteCategoryPage.facetDrawer.locator('button[aria-expanded="true"] span')).toContainText(quickFilterText as string);
          });
          await rewriteCategoryPage.closeFilters();
          await page.waitForFunction('!!window.gap');
        }
      });
    });
  });
});
