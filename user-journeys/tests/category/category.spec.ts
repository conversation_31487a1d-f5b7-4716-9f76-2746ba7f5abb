/* eslint-disable playwright/no-force-option */
// @ts-check
import { expect } from '@playwright/test';
import * as allure from 'allure-js-commons';
import { areWeInNext, getPageType, scrollElementToCenter, scrollToBottomUntilPageDoesNotGrow, scrollToTop, waitForHydration } from '../../utils/helpers';
import test from '../../pom/base.page';
import Builder from '@/test_data/builder';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`Category`, { tag: ['@category'] }, () => {
  test.beforeEach(async ({ page, categoryPage, sitewide }) => {
    await test.step(`Go to Category Page`, async () => {
      await page.route(/cn.*\.jpe?g/, route => route.abort('aborted'));
      await allure.description('Category tests have product images removed to improve performance in CI.');
      const category = builder.withCategory();
      await categoryPage.goToCategoryPage(category);
      await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      await waitForHydration(page);
    });

    await test.step(`Ensure that Sitewide Components are visible on page.`, async () => {
      await sitewide.checkSiteWideElementsAreOnPage();
    });
  });

  test.describe(`NAVIGATE`, { tag: ['@run-br-us', '@run-gp', '@run-gpf', '@run-on'] }, () => {
    // skipping ON CA for this test as there is no division page type for ON CA
    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Category Page from Division page.`,
      { tag: ['@run-brf', '@skip-on-ca'] },
      async ({ page, utilityPage, categoryPage }) => {
        await test.step(`Go to Division Page`, async () => {
          const division = builder.withDivision();
          await utilityPage.navigateToDivision(division);
        });

        await test.step('I click on Category Link from the Division page', async () => {
          await utilityPage.selectCategory();
        });

        await test.step('I validate we land on the category page', async () => {
          await page.waitForURL(/cid=/);
          await expect(categoryPage.productGrid).toBeVisible({ timeout: 30000 });
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate from using breadcrumb link on category page.`,
      { tag: ['@run-at', '@run-br-ca'] },
      async ({ page, categoryPage }) => {
        await test.step('I click on Division breadcrumb Link from the Category page', async () => {
          await categoryPage.clickDivisionBreadcrumb();
        });

        await test.step('I validate that we land on the expected Page Type', async () => {
          await page.waitForURL(/cid=/);
          expect(await getPageType(page)).toMatch(/category|division/);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate from Category to Product Page using the swatch More Colors Link.`,
      { tag: ['@run-at-us', '@skip-br', '@skip-on-ca'] },
      async ({ page, categoryPage }) => {
        await test.step('I click on More Colors link from the Category page', async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
          await scrollElementToCenter(categoryPage.moreColorsonPdp.first());
          await categoryPage.moreColorsonPdp.click({ force: true });
        });

        await test.step('I validate that we land on the Product Page with Page Type of product', async () => {
          await page.waitForURL(/pid=/);
          await expect.poll(async () => await getPageType(page), 'ensure we see Product Page type').toBe('product');
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to related category using Related Items link.`,
      { tag: ['@run-at', '@run-br-ca', '@skip-stage'] },
      async ({ page, categoryPage }) => {
        await test.step('I click on Related Items Link to go to new category page', async () => {
          const initialCatPage = page.url();
          await categoryPage.clickRelatedItemsLink();
          await expect(page).not.toHaveURL(initialCatPage);
        });

        await test.step('Ensure product grid is visible on page with the expected Page Type', async () => {
          await waitForHydration(page);
          await expect(categoryPage.productGrid).toBeVisible({ timeout: 30000 });
          expect(await getPageType(page)).toMatch(/category|division/);
        });
      }
    );
  });

  test.describe(`PRODUCT CARD`, { tag: ['@run-all'] }, () => {
    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure products images, description & price are in place & accessible.`,
      { tag: ['@run-at', '@run-on'] },
      async ({ categoryPage }) => {
        await test.step(`I ensure Product Cards are visible on the page.`, async () => {
          expect(await categoryPage.getProductCardCount()).toBeGreaterThanOrEqual(1);
        });

        await test.step(`I ensure Product images are visible on the page and link to a product page.`, async () => {
          expect(await categoryPage.getProductImagesCount()).toBeGreaterThanOrEqual(1);
          await expect(categoryPage.productImageLinks.first()).toHaveAttribute('href', /product.do/);
        });

        await test.step(`I ensure Product Name is visible on the page and links to a product page.`, async () => {
          await expect(categoryPage.getProductName(categoryPage.productCards.first())).toHaveAttribute('href', /product.do/);
        });

        await test.step('I check the price of a product and ensure it has a value.', async () => {
          const price = await categoryPage.getProductPrice(await categoryPage.getRandomProductCard());
          expect(price).toMatch(/\$\d+\.\d{2}/);
        });
      }
    );
  });

  test.describe(`SORT`, { tag: ['@run-all'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Check sort by by checking item order`, async ({ page, categoryPage }) => {
      let requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/cc/);
      await test.step('I go to featured sort', async () => {
        await categoryPage.goToFeaturedSort();
      });

      await test.step(`I check that the URL updated correctly for sort by featured`, async () => {
        const request = await requestPromise;
        const url = new URL(request.url());
        expect(url.searchParams.get('sortByField'), "validate the API call to /commerce/search/products/v2/cc doesn't contain sortByField").toBe(null);
        expect(url.searchParams.get('sortByDir'), "validate the API call to /commerce/search/products/v2/cc doesn't contain sortByDir").toBe(null);
        await waitForHydration(page);
        await categoryPage.waitForProductCard();
        await expect(page, 'validate the url contains the query param sortByField is set to featured').toHaveURL(/.*sortByField=featured.*/);
        await expect(page, 'validate the url contains the query param sortByDir is set to unset').toHaveURL(/.*sortByDir=unset.*/);
      });

      requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/cc/);
      await test.step('I go to price low-high sort', async () => {
        await categoryPage.goToPriceLowHighSort();
      });

      await test.step(`I check that the URL updated correctly for sort by low to high`, async () => {
        const request = await requestPromise;
        const url = new URL(request.url());
        expect(url.searchParams.get('sortByField'), 'validate the API call to /commerce/search/products/v2/cc sortByField is set to price').toBe('price');
        expect(url.searchParams.get('sortByDir'), 'validate the API call to /commerce/search/products/v2/cc sortByDir is set to asc').toBe('asc');
        await waitForHydration(page);
        await categoryPage.waitForProductCard();
        await expect(page, 'validate the url contains the query param sortBy is set to featured').toHaveURL(/.*sortByField=price.*/);
        await expect(page, 'validate the url contains the query param sortByDir is set to asc').toHaveURL(/.*sortByDir=asc.*/);
      });

      requestPromise = page.waitForRequest(/\/commerce\/search\/products\/v2\/cc/);
      await test.step('I go to price high-low sort', async () => {
        await scrollToTop(page);
        await categoryPage.goToPriceHighLowSort();
      });

      await test.step(`I check that the URL updated correctly for sort by high to low`, async () => {
        const request = await requestPromise;
        const url = new URL(request.url());
        expect(url.searchParams.get('sortByField'), 'validate the API call to /commerce/search/products/v2/cc sortByField is set to price').toBe('price');
        expect(url.searchParams.get('sortByDir'), 'validate the API call to /commerce/search/products/v2/cc sortByDir is set to desc').toBe('desc');
        await waitForHydration(page);
        await categoryPage.waitForProductCard();
        await expect(page, 'validate the url contains the query param sortBy is set to featured').toHaveURL(/.*sortByField=price.*/);
        await expect(page, 'validate the url contains the query param sortByDir is set to desc').toHaveURL(/.*sortByDir=desc.*/);
      });
    });
  });

  test.describe(`FILTER`, { tag: '@run-all' }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Filter category results with Category, Color, Price, and Size`, async ({ categoryPage, page }) => {
      await test.step('I click the filter button', async () => {
        if (brand === 'br' || brand === 'brf' || breakpoint === 'mobile') {
          await categoryPage.clickFilterFacetButton();
        } else {
          await categoryPage.allFacetsButton.click();
        }
      });

      await page.waitForFunction('!!window.gap');
      const buildVersion = await page.evaluate('window.gap.buildVersion.nextApp');

      await test.step(`Build Version is ${buildVersion}`, async () => {});

      let initialNumberOfItems = await categoryPage.getItemCount();
      await test.step('I select a Category', async () => {
        await categoryPage.categoryFilterDropdown.click();
        await scrollElementToCenter(categoryPage.categoryFilterOptions.first());
        await categoryPage.categoryFilterOptions.first().click();
      });

      await test.step('I validate the url shows the category filter selected', async () => {
        await page.waitForURL(/.*style=.*/);
        await expect(page, 'validate the url has the category facet info').toHaveURL(/.*style=.*/);
        await waitForHydration(page);
      });

      await test.step('I validate that new products appear', async () => {
        await categoryPage.waitForProductCard();
        await expect(categoryPage.productCards.first(), 'validate the first product card is visible').toBeVisible({ timeout: 60000 });
        expect(await categoryPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
          initialNumberOfItems
        );
      });

      await test.step('I clear the filters and validate products are updated', async () => {
        const filteredNumberOfItems = await categoryPage.getItemCount();
        !brand?.startsWith('br') && (await categoryPage.closeFacetsButton.click());
        brand?.startsWith('br') ? await categoryPage.categoryFilterOptions.first().click() : await categoryPage.individualFacetClearButton.first().click();
        await page.waitForURL(/^(?!(?=.*style=)).*$/);
        await expect(page, 'validate the url no longer contains facet information').not.toHaveURL(/style=/);
        await categoryPage.waitForProductCard();
        await waitForHydration(page);
        expect(await categoryPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
          filteredNumberOfItems
        );
      });

      if (!brand?.startsWith('br')) {
        await test.step('I click the filter button', async () => {
          if (breakpoint === 'mobile') {
            if (await categoryPage.allFacetsButton.isVisible()) {
              await categoryPage.allFacetsButton.filter({ hasNotText: `${brand}-${market}` === 'gp-us' ? /\d/ : '2' }).waitFor();
            }
            await categoryPage.clickFilterFacetButton();
          } else {
            await categoryPage.allFacetsButton.filter({ hasNotText: `${brand}-${market}` === 'gp-us' ? /\d/ : '2' }).waitFor();
            await categoryPage.allFacetsButton.click();
          }
        });
      }

      initialNumberOfItems = await categoryPage.getItemCount();
      let clearAdditionalFacetRequired = false;
      await test.step('I select a Size', async () => {
        let actOnDepartmentFilter = true;
        await page.addLocatorHandler(
          categoryPage.departmentFilterDropdown,
          async () => {
            if (!actOnDepartmentFilter) return;
            if ((await categoryPage.currentFacets.count()) !== 0) return;
            await categoryPage.departmentFilterDropdown.click();
            await categoryPage.departmentFilterOptions.first().click();
            clearAdditionalFacetRequired = true;
          },
          { times: 1, noWaitAfter: true }
        );
        await categoryPage.sizeFilterDropdown.click({ timeout: 40000 });
        actOnDepartmentFilter = false;
        await categoryPage.sizeFilterOptions.first().click();
      });

      await test.step('I validate the url shows the size filter selected', async () => {
        await page.waitForURL(/.*size=.*/);
        await expect(page, 'validate the url has the size facet info').toHaveURL(/.*size=.*/);
        await waitForHydration(page);
      });

      await test.step('I validate that new products appear', async () => {
        await categoryPage.waitForProductCard();
        await expect(categoryPage.productCards.first()).toBeVisible({ timeout: 60000 });
        expect(await categoryPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
          initialNumberOfItems
        );
      });

      await test.step('I clear the filters and validate products are updated', async () => {
        const filteredNumberOfItems = await categoryPage.getItemCount();
        if (brand?.startsWith('br')) {
          await categoryPage.sizeFilterOptions.first().click();
        } else {
          await categoryPage.closeFacetsButton.click();
          await categoryPage.individualFacetClearButton.first().click();
          if (clearAdditionalFacetRequired) {
            await categoryPage.individualFacetClearButton.first().click();
          }
        }
        await page.waitForURL(/^(?!(?=.*size=)).*$/);
        await expect(page, 'validate the url no longer contains facet information').not.toHaveURL(/size=/);
        await categoryPage.waitForProductCard();
        await waitForHydration(page);
        expect(await categoryPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
          filteredNumberOfItems
        );
      });

      // Skip color as it's broken at the moment
      if (brand !== 'gp') {
        if (!brand?.startsWith('br')) {
          await test.step('I click the filter button', async () => {
            if (breakpoint === 'mobile') {
              if (await categoryPage.allFacetsButton.isVisible()) {
                await categoryPage.allFacetsButton.filter({ hasNotText: `${brand}-${market}` === 'gp-us' ? /\d/ : '2' }).waitFor();
              }
              await categoryPage.clickFilterFacetButton();
            } else {
              await categoryPage.allFacetsButton.filter({ hasNotText: `${brand}-${market}` === 'gp-us' ? /\d/ : '2' }).waitFor();
              await categoryPage.allFacetsButton.click();
            }
          });
        }

        await test.step('I select a Color', async () => {
          initialNumberOfItems = await categoryPage.getItemCount();
          await categoryPage.colorFilterDropdown.click();
          await categoryPage.colorFilterOptions.first().click();
        });

        await test.step('I validate the url shows the color filter selected', async () => {
          await page.waitForURL(/.*color=.*/);
          await expect(page).toHaveURL(/.*color=.*/);
          await waitForHydration(page);
        });

        await test.step('I validate that new products appear', async () => {
          await categoryPage.waitForProductCard();
          await expect(categoryPage.productCards.first()).toBeVisible({ timeout: 60000 });
          expect(await categoryPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
            initialNumberOfItems
          );
        });

        await test.step('I clear the filters and validate products are updated', async () => {
          const filteredNumberOfItems = await categoryPage.getItemCount();
          !brand?.startsWith('br') && (await categoryPage.closeFacetsButton.click());
          brand?.startsWith('br') ? await categoryPage.clearFacetsButton.click() : await categoryPage.individualFacetClearButton.first().click();
          await page.waitForURL(/^(?!color=).*$/);
          await expect(page, 'validate the url no longer contains facet information').not.toHaveURL(/color=/);
          await categoryPage.waitForProductCard();
          await waitForHydration(page);
          expect(await categoryPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
            filteredNumberOfItems
          );
        });
      }

      if (!brand?.startsWith('br')) {
        await test.step('I click the filter button', async () => {
          if (breakpoint === 'mobile') {
            if (await categoryPage.allFacetsButton.isVisible()) {
              await categoryPage.allFacetsButton.filter({ hasNotText: `${brand}-${market}` === 'gp-us' ? /\d/ : '2' }).waitFor();
            }
            await categoryPage.clickFilterFacetButton();
          } else {
            await categoryPage.allFacetsButton.filter({ hasNotText: `${brand}-${market}` === 'gp-us' ? /\d/ : '2' }).waitFor();
            await categoryPage.allFacetsButton.click();
          }
        });
      }

      initialNumberOfItems = await categoryPage.getItemCount();
      await test.step('I select a Price range', async () => {
        await categoryPage.priceFilterDropdown.click();
        if (brand?.startsWith('br')) {
          if (breakpoint === 'mobile') {
            await categoryPage.categoryFilterDropdown.scrollIntoViewIfNeeded();
            await categoryPage.priceFilterOptionsMinDropdown.selectOption({ index: 2 });
            await categoryPage.priceFilterOptionsMaxDropdown.selectOption({ index: 2 });
          } else {
            await categoryPage.priceFilterOptionsMinDropdown.click();
            await categoryPage.priceFilterOptionsDowndownItems.nth(1).click();
            await categoryPage.priceFilterOptionsMaxDropdown.click();
            await categoryPage.priceFilterOptionsDowndownItems.nth(1).click();
          }
        } else {
          const { width, height } = (await categoryPage.priceSlider.boundingBox())!;
          await categoryPage.priceSlider.hover({ position: { x: 0, y: height / 2 }, force: true });
          await page.mouse.down();
          await categoryPage.priceSlider.hover({ position: { x: width / 5, y: height / 2 }, force: true });
          await page.mouse.up();
          await categoryPage.priceSlider.hover({ position: { x: width, y: height / 2 }, force: true });
          await page.mouse.down();
          await categoryPage.priceSlider.hover({ position: { x: width - width / 5, y: height / 2 }, force: true });
          await page.mouse.up();
        }
      });

      await test.step('I validate the url shows the price filter selected', async () => {
        await page.waitForURL(/.*price=.*/);
        await expect(page).toHaveURL(/.*price=.*/);
        await waitForHydration(page);
      });

      await test.step('I validate that new products appear', async () => {
        await categoryPage.waitForProductCard();
        await expect(categoryPage.productCards.first()).toBeVisible({ timeout: 60000 });
        expect(await categoryPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
          initialNumberOfItems
        );
      });

      await test.step('I clear the filters and validate products are updated', async () => {
        const filteredNumberOfItems = await categoryPage.getItemCount();
        !brand?.startsWith('br') && (await categoryPage.closeFacetsButton.click());
        brand?.startsWith('br') ? await categoryPage.clearFacetsButton.click() : await categoryPage.individualFacetClearButton.first().click();
        await page.waitForURL(/^(?!price=).*$/);
        await expect(page, 'validate the url no longer contains facet information').not.toHaveURL(/price=/);
        await categoryPage.waitForProductCard();
        await waitForHydration(page);
        expect(await categoryPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
          filteredNumberOfItems
        );
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Check flex facets functionality`, async ({ page, categoryPage }) => {
      await test.step('Wait for products to appear', async () => {
        await categoryPage.waitForProductCard();
      });

      await test.step('I click the filter button', async () => {
        if (brand === 'br' || brand === 'brf' || breakpoint === 'mobile') {
          await categoryPage.clickFilterFacetButton();
        } else {
          await categoryPage.allFacetsButton.click();
        }
      });

      let flexFacetCount;
      let facetCountGrabbedCount = 0;
      do {
        // eslint-disable-next-line playwright/no-wait-for-timeout
        await page.waitForTimeout(1000);
        flexFacetCount = await categoryPage.getFlexFacetCount();
        facetCountGrabbedCount++;
      } while (flexFacetCount < 1 && facetCountGrabbedCount < 5);

      expect(flexFacetCount, 'validate that the flex facet count is greater than 0').toBeGreaterThan(0);
      for (let i = 0; i < flexFacetCount && i < 5; i++) {
        const initialNumberOfItems = await categoryPage.getItemCount();
        await test.step('I select a facet option', async () => {
          await categoryPage.clickNthFacet(i);
        });

        await test.step('I validate that new products appear', async () => {
          await expect(categoryPage.currentFlexFacets).toHaveCount(1);
          await categoryPage.waitForProductCard();
          await expect(categoryPage.productCards.first()).toBeVisible({ timeout: 60000 });
          expect(await categoryPage.getItemCount(), 'validate with a filter applied that the item count has not increased').not.toBeGreaterThan(
            initialNumberOfItems
          );
        });

        await test.step('I clear the filters and validate products are updated', async () => {
          const filteredNumberOfItems = await categoryPage.getItemCount();
          !brand?.startsWith('br') && (await categoryPage.closeFacetsButton.click());
          brand?.startsWith('br') ? await categoryPage.clickNthCheckedFacetOption() : await categoryPage.individualFacetClearButton.first().click();
          await categoryPage.currentFlexFacets.waitFor({ state: 'hidden' });
          await waitForHydration(page);
          await categoryPage.waitForProductCard();
          expect(await categoryPage.getItemCount(), 'validate without a filter applied that the item count has not increased').toBeGreaterThanOrEqual(
            filteredNumberOfItems
          );
        });

        if (!brand?.startsWith('br')) {
          await test.step('I click the filter button', async () => {
            if (breakpoint === 'mobile') {
              await categoryPage.clickFilterFacetButton();
            } else {
              await categoryPage.allFacetsButton.click();
            }
          });
        }
      }
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Check quick filter functionality`, { tag: '@skip-br' }, async ({ page, categoryPage }) => {
      await categoryPage.waitForProductCard();
      const quickFilterCount = await categoryPage.getQuickFilterButtonsCount();
      for (let i = 0; i < quickFilterCount; i++) {
        const quickFilterText = await categoryPage.quickFilterButtons.nth(i).textContent();
        await test.step(`I click the ${quickFilterText} quick filter button and confirm that the correct filter facet opens`, async () => {
          await categoryPage.clickNthQuickFilterButton(i);
          await page.waitForFunction('!!window.gap');
          await expect(categoryPage.facetDrawer.locator('button[aria-expanded="true"]')).toContainText(quickFilterText as string);
        });
        await categoryPage.closeModal();
        await page.waitForFunction('!!window.gap');
      }
    });
  });
});
