import { expect, Page } from '@playwright/test';
import * as allure from 'allure-js-commons';
import test from '../../pom/base.page';
import { waitForHydration } from '../../utils/helpers';
import { RewriteCategoryPage } from '@/pom/common/rewrite-category.page';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`Category`, () => {
  test.describe(`Rewrite`, () => {
    test.describe(`SUBCATEGORY`, { tag: ['@run-at-us'] }, () => {
      const goToSubcategoryPage = async ({ rewriteCategoryPage, page }: { page: Page; rewriteCategoryPage: RewriteCategoryPage }) => {
        await test.step(`Go to Subcategory Page`, async () => {
          await page.route(/cn.*\.jpe?g/, route => route.abort('aborted'));
          await allure.description('Subcategory tests have product images removed to improve performance in CI.');
          await rewriteCategoryPage.goToCategoryPage('1006482', '3029710');
          await waitForHydration(page);
        });
      };

      test(`${brand}-${market}-${breakpoint}-${env}: Set style id on PSAPI call`, async ({ page, rewriteCategoryPage }) => {
        const requestPromise = rewriteCategoryPage.page.waitForRequest(/\/commerce\/search\/products\/v2\/cc/);
        await goToSubcategoryPage({ page, rewriteCategoryPage });

        const request = await requestPromise;
        const url = new URL(request.url());
        expect(url.searchParams.get('cid'), 'validate the API call to /commerce/search/products/v2/cc has the correct cid').toBe('1006482');
        expect(url.searchParams.get('style'), 'validate the API call to /commerce/search/products/v2/cc has the correct style').toBe('3029710');
      });

      test(`${brand}-${market}-${breakpoint}-${env}: Filter subcategory based on style id query param`, async ({ page, rewriteCategoryPage }) => {
        await goToSubcategoryPage({ page, rewriteCategoryPage });
        await expect(rewriteCategoryPage.bestOfNewArrivalsFilter).toBeVisible();
      });
    });
  });
});
