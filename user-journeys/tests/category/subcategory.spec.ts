import { expect, Page } from '@playwright/test';
import * as allure from 'allure-js-commons';
import test from '../../pom/base.page';
import { areWeInNext, waitForHydration } from '../../utils/helpers';
import { CategoryPage } from '@/pom/common/category.page';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`Category`, { tag: ['@category'] }, () => {
  test.describe(`SUBCATEGORY`, { tag: ['@run-at-us'] }, () => {
    const goToSubcategoryPage = async ({ categoryPage, page }: { categoryPage: CategoryPage; page: Page }) => {
      await test.step(`Go to Subcategory Page`, async () => {
        await page.route(/cn.*\.jpe?g/, route => route.abort('aborted'));
        await allure.description('Subcategory tests have product images removed to improve performance in CI.');
        await categoryPage.goToCategoryPage('1006482', '3029710');
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        await waitForHydration(page);
      });
    };

    test(`${brand}-${market}-${breakpoint}-${env}: Set style id on PSAPI call`, async ({ page, categoryPage }) => {
      const requestPromise = categoryPage.page.waitForRequest(/\/commerce\/search\/products\/v2\/cc/);
      await goToSubcategoryPage({ page, categoryPage });

      const request = await requestPromise;
      const url = new URL(request.url());
      expect(url.searchParams.get('cid'), 'validate the API call to /commerce/search/products/v2/cc has the correct cid').toBe('1006482');
      expect(url.searchParams.get('style'), 'validate the API call to /commerce/search/products/v2/cc has the correct style').toBe('3029710');
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Filter subcategory based on style id query param`, async ({ page, categoryPage }) => {
      await goToSubcategoryPage({ page, categoryPage });
      await expect(categoryPage.bestOfNewArrivalsFilter).toBeVisible();
    });
  });
});
