// @ts-check
import { readFileSync } from 'fs';
import { expect } from '@playwright/test';
import { areWeInNext, hidePreviewButton, scrollElementToCenter, waitForHydration } from '../../utils/helpers';
import test from '../../pom/base.page';
import Builder from '@/test_data/builder';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

const paypalScript = readFileSync('./test_data/checkout-scripts/paypal-checkout.min.js', 'utf8');
const clientMinScript = readFileSync('./test_data/checkout-scripts/client.min.js', 'utf8');
const dataCollectorScript = readFileSync('./test_data/checkout-scripts/data-collector.min.js', 'utf8');
const afterpayScript = readFileSync('./test_data/checkout-scripts/afterpay.js', 'utf8');
const presentAfterpayScript = readFileSync('./test_data/checkout-scripts/present-afterpay.js', 'utf8');
const graphqlScript = readFileSync('./test_data/checkout-scripts/graphql', 'utf8');

test.describe(`Checkout`, () => {
  test.beforeEach(async ({ shoppingBagPage, page }) => {
    await test.step(`Go to the '${brand}'-brand Shopping Bag page`, async () => {
      await shoppingBagPage.goToShoppingBagPage();
      // eslint-disable-next-line playwright/no-nested-step
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });
      await waitForHydration(page);
      await hidePreviewButton(page);
    });
  });

  test.afterEach(async ({ shoppingBagPage }) => {
    await test.step(`Clear Shopping Bag`, async () => {
      await shoppingBagPage.clearShoppingBag(false);
    });
  });

  test.describe(`PLACE ORDER`, () => {
    test(
      `${brand}-${market}-${breakpoint}-${env}: A returning customer should place an order with a standard product.`,
      { tag: ['@run-at', '@run-brf', '@run-gp', '@run-gpf', '@run-on', '@skip-preview', '@checkout'] },
      async ({ signInPage, shoppingBagPage, checkoutPage, page }) => {
        const builder = new Builder();
        const returningCustomer = builder.returningCheckoutCustomer();
        await shoppingBagPage.addStandardProductToBag();

        await test.step(`I click the Checkout button from shopping bag page`, async () => {
          await expect(shoppingBagPage.checkoutButton).not.toHaveClass('disabled');
          await shoppingBagPage.checkoutButton.click();
        });

        await test.step(`I get redirected to Sign In page`, async () => {
          await page.waitForURL(/sign-in/);
          await expect(page).toHaveTitle(/Sign in/);
        });

        await signInPage.signInWithEmailAndPassword(returningCustomer.email, returningCustomer.pw);

        await test.step(`I am checking to see if I am on the Checkout page`, async () => {
          await expect(page).toHaveURL(/checkout/);
          await waitForHydration(page);
        });

        await test.step(`Select default shipping address if needed`, async () => {
          await checkoutPage.selectSavedShippingAddress();
        });

        await test.step(`I select the saved card`, async () => {
          try {
            await checkoutPage.defaultCard.waitFor({ timeout: 2000 });
            await checkoutPage.selectDefaultCard();
            await checkoutPage.cvv.waitFor({ timeout: 2000 });
            await checkoutPage.enterSecurityCode({ cvv: '999' });
            // eslint-disable-next-line no-empty
          } catch (e) {}
        });

        await test.step(`I click "Place order"`, async () => {
          await checkoutPage.placeOrderButton.click();
          try {
            await checkoutPage.enterSecurityCode({ cvv: '999' });
            await checkoutPage.placeOrderButton.click();
            // eslint-disable-next-line no-empty
          } catch (e) {}
        });

        await test.step(`I see order confirmation message "Thank you for your order!"`, async () => {
          await page.waitForURL(/thankyou/);
          await expect(page.getByText(/Thank you for your order/i)).toBeVisible();
        });

        const orderNumber = await checkoutPage.getOrderNumber();
        await test.step(`The Order Confirmatin Number is ${orderNumber}`, async () => {});
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: A guest customer should place an order with a standard product.`,
      { tag: ['@run-at', '@run-brf', '@run-gp', '@run-gpf', '@run-on', '@checkout'] },
      async ({ shoppingBagPage, checkoutPage, page, signInPage }) => {
        const builder = new Builder();
        const guestUser = builder.guestUser().withProfile();
        const guestCreditCard = builder.guestCustomerCreditCard();

        await shoppingBagPage.addStandardProductToBag();

        await test.step(`I click the Checkout button from shopping bag page`, async () => {
          await expect(shoppingBagPage.checkoutButton).not.toHaveClass('disabled');
          brand === 'brf' && (await scrollElementToCenter(shoppingBagPage.checkoutButton));
          await shoppingBagPage.checkoutButton.click({ force: brand === 'brf' || (brand === 'gpf' && breakpoint === 'mobile') });
        });

        await test.step(`I get redirected to Sign In page`, async () => {
          await page.waitForURL(/sign-in/);
          await expect(page).toHaveTitle(/Sign in/);
        });

        await test.step(`I enter guest email '${guestUser.email}' and click Continue as Guest`, async () => {
          await signInPage.signInAsGuest(guestUser.email);
        });

        await test.step(`I'm on the Checkout page`, async () => {
          await page.waitForLoadState('load');
          await page.waitForURL(/checkout/);
          await waitForHydration(page);
        });

        await test.step(`I enter shipping information`, async () => {
          const fullName = `${guestUser.profile.firstName} ${guestUser.profile.lastName}`;
          await checkoutPage.enterShippingInfo(fullName, guestUser.address);
        });

        await test.step(`I enter credit card information`, async () => {
          await checkoutPage.addCreditCard(guestCreditCard.cc, guestCreditCard.expiry, guestCreditCard.cvv);
        });

        await test.step(`I click "Place order"`, async () => {
          await checkoutPage.placeOrderButton.click();
        });

        await test.step(`I see order confirmation message "Thank you for your order!"`, async () => {
          await page.waitForURL(/thankyou/);
          await expect(page.getByText(/Thank you for your order/i)).toBeVisible();
        });

        const orderNumber = await checkoutPage.getOrderNumber();
        await test.step(`The Order Confirmatin Number is ${orderNumber}`, async () => {});
      }
    );
  });

  test.describe(`PAYMENTS`, () => {
    test(
      `${brand}-${market}-${breakpoint}-${env}: A guest customer should place an order and pay with PayPal.`,
      { tag: ['@alternate-user-agent'] },
      async ({ shoppingBagPage, checkoutPage, page, signInPage }) => {
        const builder = new Builder();
        const guestUser = builder.guestUser().withProfile();

        await page.route(/https:\/\/js\.braintreegateway\.com\/web\/[\d.]*\/js\/paypal-checkout.min\.js/, route => {
          route.fulfill({
            status: 200,
            body: paypalScript,
          });
        });

        await page.route(/https:\/\/js\.braintreegateway\.com\/web\/[\d.]*\/js\/client\.min\.js/, route => {
          route.fulfill({
            status: 200,
            body: clientMinScript,
          });
        });

        await page.route(/https:\/\/js\.braintreegateway\.com\/web\/[\d.]*\/js\/data-collector\.min\.js/, route => {
          route.fulfill({
            status: 200,
            body: dataCollectorScript,
          });
        });

        await page.route('https://payments.braintree-api.com/graphql', route => {
          route.fulfill({
            status: 200,
            body: graphqlScript,
          });
        });

        await shoppingBagPage.addStandardProductToBag();

        await test.step(`I click the Checkout button from shopping bag page`, async () => {
          await expect(shoppingBagPage.checkoutButton).not.toHaveClass('disabled');
          await shoppingBagPage.checkoutButton.click();
        });

        await test.step(`I get redirected to Sign In page`, async () => {
          await page.waitForURL(/sign-in/);
          await expect(page).toHaveTitle(/Sign in/);
        });

        await test.step(`I enter guest email '${guestUser.email}' and click Continue as Guest`, async () => {
          await signInPage.signInAsGuest(guestUser.email);
        });

        await test.step(`I'm on the Checkout page`, async () => {
          await page.waitForLoadState('load');
          await page.waitForURL(/checkout/);
          await waitForHydration(page);
        });

        await test.step(`I enter shipping information`, async () => {
          const fullName = `${guestUser.profile.firstName} ${guestUser.profile.lastName}`;
          await checkoutPage.enterShippingInfo(fullName, guestUser.address);
        });

        await test.step(`I go to PayPal and login`, async () => {
          // Scroll to bottom to avoid page loading but not allowing to scroll to PayPal button
          // await page.pause();
          await page.waitForTimeout(5000);
          // await page.evaluate(() =>
          //   window.scrollTo({ behavior: 'smooth', left: 0, top: document.documentElement.scrollTop + document.documentElement.scrollHeight })
          // );
          await page.getByRole('button', { name: 'Shipping' }).click();

          // Login to PayPal
          await checkoutPage.loginAndPayWithPayPal();
        });

        await test.step(`I click "Place order"`, async () => {
          await expect(checkoutPage.placeOrderButton).toBeVisible();
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: A guest customer should be able to pay with AfterPay.`,
      { tag: ['@alternate-user-agent', '@run-at-us-stage'] },
      async ({ signInPage, shoppingBagPage, checkoutPage, page }) => {
        const builder = new Builder();
        const guestUser = builder.guestUser().withProfile();

        await page.route('https://portal.sandbox.afterpay.com/afterpay.js', route => {
          route.fulfill({ body: afterpayScript });
        });

        await page.route('https://static-us.afterpay.com/javascript/present-afterpay.js', route => {
          route.fulfill({ body: presentAfterpayScript });
        });

        await shoppingBagPage.addStandardProductToBag(4);

        await test.step(`I click the Checkout button from shopping bag page`, async () => {
          await expect(shoppingBagPage.checkoutButton).not.toHaveClass('disabled');
          await shoppingBagPage.checkoutButton.click();
        });

        await test.step(`I get redirected to Sign In page`, async () => {
          await page.waitForURL(/sign-in/);
          await expect(page).toHaveTitle(/Sign in/);
        });

        await test.step(`I enter guest email '${guestUser.email}' and click Continue as Guest`, async () => {
          await signInPage.signInAsGuest(guestUser.email);
        });

        await test.step(`I'm on the Checkout page`, async () => {
          await page.waitForLoadState('load');
          await page.waitForURL(/checkout/);
          await waitForHydration(page);
        });

        await test.step(`I enter shipping information`, async () => {
          const fullName = `${guestUser.profile.firstName} ${guestUser.profile.lastName}`;
          await checkoutPage.enterShippingInfo(fullName, guestUser.address);
          await page.waitForTimeout(5000);
          // await page.evaluate(() =>
          //   window.scrollTo({ behavior: 'smooth', left: 0, top: document.documentElement.scrollTop + document.documentElement.scrollHeight })
          // );
          await page.getByRole('button', { name: 'Shipping' }).click();
        });

        await test.step(`I select AfterPay and Login`, async () => {
          await checkoutPage.loginAndPayWithAfterPay();
        });

        await test.step(`I Ensure "Place order" button is visible & accessible`, async () => {
          await expect(checkoutPage.placeOrderButton).toBeVisible();
        });

        // await test.step(`I see order confirmation message "Thank you for your order!"`, async () => {
        //   await page.waitForURL(/thankyou/);
        //   await expect(page.getByText(/Thank you for your order/i)).toBeVisible();
        // });

        // const orderNumber = await checkoutPage.getOrderNumber();
        // await test.step(`The Order Confirmatin Number is ${orderNumber}`, async () => {});
      }
    );
  });
});
