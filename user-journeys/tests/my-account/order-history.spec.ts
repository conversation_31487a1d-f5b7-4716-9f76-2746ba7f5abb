// @ts-check
import { expect } from '@playwright/test';
import test from '@/pom/base.page';
import Builder from '@/test_data/builder';
import { waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`My Account`, { tag: ['@run-all'] }, () => {
  test.describe(`Profile`, () => {
    test.describe(`ORDER HISTORY`, () => {
      test.beforeEach(`Sign in to`, async ({ page, signInPage, sitewide }) => {
        await signInPage.goToSignInPage();
        await waitForHydration(page);
        const customerInfo = builder.returningCustomer();
        await signInPage.signInWithEmailAndPassword(customerInfo.email, customerInfo.pw);
        await waitForHydration(page);

        await test.step(`Ensure that Sitewide Components are visible on page.`, async () => {
          await sitewide.checkSiteWideElementsAreOnPage();
        });
      });

      test(`${brand}-${market}-${breakpoint}-${env}: Go to my account page, click on order history, and validate it renders.`, async ({
        myAccountPage,
        page,
      }) => {
        await test.step(`Go to Order History page`, async () => {
          await expect(myAccountPage.orderHistoryAccountHomeLink, `validate order history section is present in account home`).toBeVisible();
          await myAccountPage.clickOnOrderHistory();
          await waitForHydration(page);
          await expect(myAccountPage.orderHistoryHeader, `validate order history header is present`).toBeVisible();
        });
      });
    });
  });
});
