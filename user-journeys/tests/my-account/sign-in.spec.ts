// @ts-check
import { expect, TestInfo } from '@playwright/test';
import test from '@/pom/base.page';
import Builder from '@/test_data/builder';
import { areWeInNext, hidePreviewButton, scrollToBottomUntilPageDoesNotGrow, waitForHydration } from '@/utils/helpers';
import { SignInPage } from '@/pom/common/sign-in.page';
import { getSecureURL } from '@/utils/urls';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();
const customerInfo = builder.returningCustomer();

test.describe(`My Account`, () => {
  test.afterEach(async ({ shoppingBagPage }) => {
    await shoppingBagPage.clearShoppingBag(false);
  });

  test.describe(`Sign In`, { tag: ['@run-at', '@run-br', '@run-gp', '@run-gpf', '@run-on'] }, () => {
    test.beforeEach(async ({ page, productPage, shoppingBagPage, signInPage }, testInfo: TestInfo) => {
      await test.step(`Go to the '${brand}'-brand test starting point`, async () => {
        if (testInfo.tags.includes('@clear-cookies')) {
          await page.context().clearCookies();
        }

        switch (true) {
          case testInfo.title.includes('checkout'):
            await signInPage.goToSignInPage();
            await signInPage.goToSignInPage(`${getSecureURL()}checkout`);
            break;
          case testInfo.title.includes('product page'):
            // eslint-disable-next-line no-case-declarations
            const stdProduct = await shoppingBagPage.getStandardProduct();
            await productPage.goToProductPage(stdProduct);
            break;
          case testInfo.title.includes('reentry'):
            await signInPage.goToSignInPage(undefined, true);
            break;
          default:
            await signInPage.goToSignInPage();
            break;
        }
      });

      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        await waitForHydration(page);
      });
    });

    test.describe(`LLSI`, { tag: ['@skip-ca', '@skip-gpf', '@skip-preview'] }, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Sign in with valid credentials without LLSI.`, async ({ page, signInPage }) => {
        await signInPage.signInWithEmailAndPassword(customerInfo.email, customerInfo.pw, false);

        await test.step(`Check if we made it to my account home without LLSI cookie.`, async () => {
          await expect(page).toHaveURL(/my-account\/home/<USER>

          // AT US my-account/home is broken temporarily on next
          // await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
          await expect
            .poll(async () => {
              const cookies = await page.context().cookies();
              return !cookies.find(c => c.name === 'pf_app');
            })
            .toBeTruthy();
        });
      });

      test(
        `${brand}-${market}-${breakpoint}-${env}: Sign in with valid credentials with LLSI.`,
        { tag: ['@allow-tealium-for-athleta-us'] },
        async ({ page, signInPage }) => {
          await signInPage.signInWithEmailAndPassword(customerInfo.email, customerInfo.pw, true);

          await test.step(`Check if we made it to my account home with LLSI cookie.`, async () => {
            await expect(page).toHaveURL(/my-account\/home/<USER>

            // AT US my-account/home is broken temporarily on next
            // await expect.poll(async () => await areWeInNext(page)).toBeTruthy();

            const cookies = await page.context().cookies();
            expect(cookies.find(c => c.name === 'pf_app')?.value).toBe('true');
          });
        }
      );
    });

    test.describe(`TARGET URL`, () => {
      test(
        `${brand}-${market}-${breakpoint}-${env}: Sign in with valid credentials with targetURL for checkout.`,
        { tag: '@run-brf' },
        async ({ page, signInPage }) => {
          await signInPage.signInWithEmailAndPassword(customerInfo.email, customerInfo.pw);

          await test.step(`Check if we made it to checkout page.`, async () => {
            await expect(page).toHaveURL(/checkout/);
          });
        }
      );

      test(
        `${brand}-${market}-${breakpoint}-${env}: Navigate to a product page click sign in, sign in, and make sure it returns.`,
        { tag: ['@skip-stage', '@skip-preview', '@skip-prod'] },
        async ({ page, productPage, signInPage }) => {
          const originalUrl = new URL(page.url());
          await test.step(`Go to sign in page from product page.`, async () => {
            await hidePreviewButton(page);
            // eslint-disable-next-line no-param-reassign
            page = await productPage.clickSignIn();
            // eslint-disable-next-line no-param-reassign
            signInPage = new SignInPage(page);
          });

          await signInPage.signInWithEmailAndPassword(customerInfo.email, customerInfo.pw);

          await test.step(`Check if user is signed in and returned to the same product page.`, async () => {
            await expect
              .poll(() => {
                const currentUrl = new URL(page.url());
                return (
                  currentUrl.origin === originalUrl.origin &&
                  currentUrl.pathname === originalUrl.pathname &&
                  currentUrl.searchParams.get('pid') === originalUrl.searchParams.get('pid')
                );
              })
              .toBeTruthy();
          });
        }
      );
    });

    test.describe(`ERROR HANDLING`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Provide bad credentials and get to error screen.`, { tag: '@run-brf' }, async ({ signInPage }) => {
        await signInPage.signInWithEmailAndPassword(customerInfo.email, 'badpassword', undefined, false);

        await test.step(`Check that password error prompt shows.`, async () => {
          await expect(signInPage.error).toBeVisible();
        });
      });

      test(
        `${brand}-${market}-${breakpoint}-${env}: Go to reentry page and validate it renders as expected.`,
        { tag: '@run-brf' },
        async ({ page, signInPage }) => {
          await test.step(`Validate reentry page.`, async () => {
            await expect(signInPage.reentryError).toBeVisible();
            await expect
              .poll(() => {
                const currentUrl = new URL(page.url());
                return currentUrl.pathname === '/my-account/sign-in' && currentUrl.searchParams.get('errorCode') === 'reentry';
              })
              .toBeTruthy();
          });

          await test.step(`Click sign in.`, async () => {
            await signInPage.clickSignIn();
          });

          await signInPage.signInWithEmailAndPassword(customerInfo.email, customerInfo.pw);

          await test.step(`Check if we made it to my account home.`, async () => {
            await expect(page).toHaveURL(/my-account\/home/<USER>

            market === 'US' && (await expect.poll(async () => await areWeInNext(page)).toBeTruthy());
          });
        }
      );
    });

    // Holding off on running until we find out more about failure
    test.describe(`PRIVACY`, { tag: ['@clear-cookies', '@skip-stage', '@skip-preview', '@skip-prod'] }, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Sign in as opted out of online info sharing & ensure it persists.`, async ({
        signInPage,
        homePage,
        page,
        sitewide,
      }) => {
        // eslint-disable-next-line @typescript-eslint/no-shadow
        const customerInfo = builder.privacyPolicyCustomer();
        await signInPage.signInWithEmailAndPassword(customerInfo.email, customerInfo.pw);

        await test.step(`I go to the home page to access footer links`, async () => {
          await homePage.goToHomePage();
          await waitForHydration(page);
        });

        await test.step(`Click the "Priviacy Choices" footer link.`, async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
          await sitewide.privacyChoicesLink.click();
        });

        await test.step(`Ensure that online info sharing Opt-Out is honored and privacy is preserved.`, async () => {
          await expect(sitewide.privacyPreferenceStatus).toHaveText(/Browser Opt-Out Honored/);
          await expect(sitewide.privacyPreferenceToggle).not.toBeChecked();
        });
      });
    });
  });
});
