// @ts-check
import { expect } from '@playwright/test';
import test from '@/pom/base.page';
import Builder from '@/test_data/builder';
import { waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`My Account`, { tag: ['@run-all'] }, () => {
  test.describe(`Profile`, () => {
    test.describe(`VALUE CENTER`, () => {
      test.beforeEach(`Sign in to`, async ({ page, signInPage, sitewide }) => {
        await signInPage.goToSignInPage();
        await waitForHydration(page);
        const customerInfo = builder.returningCustomer();
        await signInPage.signInWithEmailAndPassword(customerInfo.email, customerInfo.pw);
        await waitForHydration(page);

        await test.step(`Ensure that Sitewide Components are visible on page.`, async () => {
          await sitewide.checkSiteWideElementsAreOnPage();
        });
      });

      test(`${brand}-${market}-${breakpoint}-${env}: Go to my account page, click on value center, and validate it renders.`, async ({
        myAccountPage,
        page,
      }) => {
        await test.step(`Go to Value Center page`, async () => {
          await expect(myAccountPage.valueCenterAccountHomeLink, `validate value center section is present in account home`).toBeVisible();
          await myAccountPage.clickOnValueCenter();
          await waitForHydration(page);
          env !== 'preview' && (await expect(myAccountPage.membershipHeader, `validate membership header is present`).toBeVisible());
          env !== 'preview' && (await expect(myAccountPage.startShoppingSection, `validate start shopping section is present`).toBeVisible());
          await expect(myAccountPage.yourFeaturedBenefitsSection, `validate your featured benefits section is present`).toBeVisible();
          await expect(myAccountPage.compareBenefitsSection, `validate compare benefits section is present`).toBeVisible();
        });

        await test.step(`Go to Track Points tab`, async () => {
          await myAccountPage.trackPointsTab.click();
          await expect(myAccountPage.trackPointsSection, `validate track points section is present`).toBeVisible();
        });

        await test.step(`Go to Earn and Redeem tab`, async () => {
          await myAccountPage.earnAndRedeemTab.click();
          await expect(myAccountPage.payItForwardSection, `validate pay it forward section is present`).toBeVisible();
          await expect(myAccountPage.earnMorePointsSection, `validate earn more points section is present`).toBeVisible();
        });
      });
    });
  });
});
