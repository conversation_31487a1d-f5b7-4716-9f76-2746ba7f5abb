/* eslint-disable no-console */
// @ts-check
import { expect } from '@playwright/test';
import Builder from '../../test_data/builder';
import test from '@/pom/base.page';
import { areWeInNext, scrollSlight, waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`STANDARD PRODUCT`, { tag: ['@run-all'] }, () => {
  test.describe(`Rewrite`, () => {
    test.beforeEach(async ({ page, rewriteProductPage }) => {
      await test.step(`Go to '${brand}'-brand product page`, async () => {
        const product = builder.withBopisProduct();
        await rewriteProductPage.goToRewriteProductPage(product);
      });
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        await waitForHydration(page);
        await scrollSlight(page);
      });
    });
    test.describe(`INTERACTIONS ON ATB`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Land on a product page and  check ATB modal .`, async ({ rewriteProductPage }) => {
        await test.step(`Check ATB and Click Add to Bag Modal is visible and check the details`, async () => {
          const productTitle = (await rewriteProductPage.productTitle.innerText()).toLowerCase();
          await rewriteProductPage.selectSize();
          await rewriteProductPage.addToBag.click({ timeout: 5000 });
          await rewriteProductPage.atbModalIsVisible();
          const atbProductTitle = await rewriteProductPage.getATBProductTitle();
          expect(atbProductTitle).toEqual(productTitle);
          await rewriteProductPage.atbProductDetails();
          await rewriteProductPage.atbProductImageVisible();
          await rewriteProductPage.viewBaglink();
          await rewriteProductPage.atbSubtotal();
          await rewriteProductPage.atbQuantityVisible();
          await rewriteProductPage.atbRecommendation();
          await rewriteProductPage.atbcloseModal();
          await rewriteProductPage.addToBag.click({ timeout: 5000 });
          await rewriteProductPage.atbModalIsVisible();
          //  expect(rewriteProductPage.itemAddedToBagVisible()).not.toBeNull();
        });
      });
    });
    test.describe(`INTERACTIONS ON Product Title on ATB Modal`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Land on a product page and  Click ATB  .`, async ({ rewriteProductPage }) => {
        await test.step(`Click  the product image and product title ,should take you to diffrent tab`, async () => {
          await rewriteProductPage.selectSize();
          await rewriteProductPage.addToBag.click({ timeout: 5000 });
          await rewriteProductPage.atbModalIsVisible();
          await rewriteProductPage.clickProductTitle();
          expect(rewriteProductPage.page.url()).toContain('/browse/product.do?pid');
        });
      });
    });
    test.describe(`INTERACTIONS ON Product  Image on ATB Modal`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Land on a product page and  Click ATB  .`, async ({ rewriteProductPage }) => {
        await test.step(`Click the product image and verify the URL'`, async () => {
          await rewriteProductPage.selectSize();
          await rewriteProductPage.addToBag.click({ timeout: 5000 });
          await rewriteProductPage.atbModalIsVisible();
          await rewriteProductPage.clickProductImage();
          expect(rewriteProductPage.page.url()).toContain('/browse/product.do?pid');
        });
      });
    });
  });
});
