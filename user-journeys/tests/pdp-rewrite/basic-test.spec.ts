/* eslint-disable no-console */
// @ts-check
import { expect } from '@playwright/test';
import test from '@/pom/base.page';
import { areWeInNext, scrollSlight, waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`PRODUCT PAGE`, { tag: ['@run-all'] }, () => {
  test.beforeEach(async ({ page, rewriteProductPage, shoppingBagPage }) => {
    await test.step(`Go to '${brand}'-brand product page`, async () => {
      const stdProduct = await shoppingBagPage.getStandardProduct();
      await rewriteProductPage.goToRewriteProductPage(stdProduct);
    });

    await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
      await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      await waitForHydration(page);
      await scrollSlight(page);
    });
  });

  test.describe(`BASIC TESTS`, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Land on the rewrite page.`, { tag: ['@skip-mobile'] }, async ({ rewriteProductPage }) => {
      await test.step(`Check the page title is present`, async () => {
        await expect(rewriteProductPage.productTitle).toBeTruthy();
      });

      await test.step(`Check the size guide link, click on it and check the modal opened`, async () => {
        await rewriteProductPage.clickSizeGuide();
        await rewriteProductPage.sizeGuideModalIsVisible();
      });

      await test.step(`Check the shipping & returns link, click on it and check the modal opened`, async () => {
        await rewriteProductPage.shippingReturns.click();
        await rewriteProductPage.shippingAndReturnsModalIsVisible();
      });
    });
  });
});
