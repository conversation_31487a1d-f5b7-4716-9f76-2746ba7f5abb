/* eslint-disable no-console */
// @ts-check
import { expect } from '@playwright/test';
import Builder from '../../test_data/builder';
import test from '@/pom/base.page';
import { areWeInNext, scrollSlight, waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`DROP SHIP PRODUCT`, { tag: ['@run-gp'] }, () => {
  test.describe(`Rewrite`, () => {
    test.beforeEach(async ({ page, rewriteProductPage }) => {
      await test.step(`Go to '${brand}'-brand product page`, async () => {
        const dropshipProduct = builder.withDropshipProduct();
        await rewriteProductPage.goToRewriteProductPage(dropshipProduct);
      });
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        await waitForHydration(page);
        await scrollSlight(page);
      });
    });
    test.describe(`CHECKS ON PAGE`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Land on a Drop Ship page.`, { tag: ['@skip-mobile'] }, async ({ rewriteProductPage, page }) => {
        await test.step(`Check the page title is present`, async () => {
          const productTitle = await rewriteProductPage.productTitle.innerText();
          expect(productTitle).toContain('Crib');
        });
        await test.step(`basic check  on dropship`, async () => {
          await expect(rewriteProductPage.sizeSelectorContainer).toBeVisible();
          await expect(rewriteProductPage.colorContainer).toBeVisible();
          await expect(rewriteProductPage.fulFillment).toBeHidden();
          await expect(rewriteProductPage.paymentMethod).toBeHidden();
        });

        await test.step(`check marketing flag and vendor messaging`, async () => {
          const dropshipExcludeFlag = await rewriteProductPage.dropshipExcludeFlag.innerText();
          expect(dropshipExcludeFlag).toMatch('Excluded from Promotions');
          const dropshipReturnFlag = await rewriteProductPage.dropshipReturnFlag.innerText();
          expect(dropshipReturnFlag).toMatch('Return by Mail Only.');
          const dropshipVendorText = await rewriteProductPage.dropshipVendorText.innerText();
          expect(dropshipVendorText).toMatch(/Sold & Shipped by [a-zA-Z]+/);
        });

        await test.step(`Test for product info section on dropshp product`, async () => {
          await expect(rewriteProductPage.productDetails).toBeVisible();
          await expect(rewriteProductPage.dimenSions).toBeVisible();
          await expect(rewriteProductPage.fitSizing).toBeHidden();
          await expect(rewriteProductPage.fabricCare).toBeHidden();
          await expect(rewriteProductPage.assemblyInstruction).toBeAttached();
          const [newPage] = await Promise.all([page.waitForEvent('popup'), rewriteProductPage.assemblyInstruction.click()]);
          await newPage.waitForLoadState('load');
          const pdfUrl = newPage.url();
          expect(pdfUrl).toContain('https://marketplacer.imgix.net/Rn/p3yxxf_Air2UVWcdr1lFWH2Xk.pdf?s=31ce520f43da2b5a1cccdada48f21594');
        });
      });
    });
    test(`${brand}-${market}-${breakpoint}-${env}: Check for Shipping icon functionality`, async ({ rewriteProductPage }) => {
      await test.step(`Click on shipping and returns icon and check the modal is visible`, async () => {
        const dropshipProduct = builder.withDropshipProduct();
        await rewriteProductPage.goToRewriteProductPage(dropshipProduct);
        await rewriteProductPage.clickShippinglink();
        await rewriteProductPage.shippingReturnsModal();
        await rewriteProductPage.closeshippingModal();
      });
    });
    test(`${brand}-${market}-${breakpoint}-${env}: Check for ADD TO BAG Functionality`, async ({ rewriteProductPage }) => {
      await test.step(`Click on ATB  and  check the modal is visible`, async () => {
        const dropshipProduct = builder.withDropshipProduct();
        await rewriteProductPage.goToRewriteProductPage(dropshipProduct);
        await rewriteProductPage.addToBag.click();
        await rewriteProductPage.atbModalIsVisible();
        await rewriteProductPage.dropshipFlagIsVisible();
      });
    });
  });
});
