/* eslint-disable no-console */
// @ts-check
import { expect } from '@playwright/test';
import Builder from '../../test_data/builder';
import test from '@/pom/base.page';
import { areWeInNext, scrollSlight, waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`PRODUCT PAGE`, { tag: ['@run-all'] }, () => {
  test.describe(`Rewrite`, () => {
    test.beforeEach(async ({ page, rewriteProductPage }) => {
      await test.step(`Go to '${brand}'-brand product page`, async () => {
        const standardProduct = builder.withStandardProduct();
        await rewriteProductPage.goToRewriteProductPage(standardProduct);
      });
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        await waitForHydration(page);
        await scrollSlight(page);
      });
    });
    test.describe(`ERROR MESSAGING ON ADD TO BAG`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Land on a product page with single size dimension.`, async ({ rewriteProductPage }) => {
        await test.step(`Click Add to Bag button and check for error message`, async () => {
          await rewriteProductPage.addToBag.click();
          const notificationMessage = await rewriteProductPage.getErrorMessageText();
          expect(notificationMessage).toMatch('Select a size before adding to bag');
          await expect(rewriteProductPage.addToBag).toBeDisabled();
        });

        await test.step(`Click on change store link and check for error message`, async () => {
          await rewriteProductPage.changeStoreLink.click();
          const notificationMessage = await rewriteProductPage.getErrorMessageText();
          expect(notificationMessage).toMatch('Select a size for pickup options');
          await expect(rewriteProductPage.addToBag).toBeDisabled();
        });

        await test.step(`Select a size and click on Change store tto open the modal`, async () => {
          await rewriteProductPage.availableSize.click();
          await rewriteProductPage.changeStoreLink.click();
          await expect(rewriteProductPage.changeStoreModalHeader).toBeVisible();
        });
      });

      test(`${brand}-${market}-${breakpoint}-${env}: Land on a product page and check out of stock messaging on ATB.`, async ({ rewriteProductPage }) => {
        await test.step(`Click Add to Bag button and check the out of stock error message for shipping`, async () => {
          if (await rewriteProductPage.unAvailableSize.isVisible()) {
            await rewriteProductPage.unAvailableSize.click();
            await rewriteProductPage.addToBag.click();
            const notificationMessage = await rewriteProductPage.getErrorMessageText();
            expect(notificationMessage).toMatch('This selection is out of stock. Try another color or size.');
            await expect(rewriteProductPage.addToBag).toBeDisabled();
          }
        });

        await test.step(`Click Add to Bag button and check the out of stock error message for pick up`, async () => {
          if (await rewriteProductPage.unAvailableSize.isVisible()) {
            await rewriteProductPage.pickupFulfillmentTile.click();
            await rewriteProductPage.addToBag.click();
            const notificationMessage = await rewriteProductPage.getErrorMessageText();
            expect(notificationMessage).toMatch(
              'This selection is out of stock at this store. Change store, try another color or size, or ship to an address.'
            );
            await expect(rewriteProductPage.addToBag).toBeDisabled();
          }
        });
      });

      test(`${brand}-${market}-${breakpoint}-${env}: Land on a product page with Size & In seam dimensions.`, async ({ rewriteProductPage }) => {
        const inseamProduct = builder.withRandomProductStyleColor();
        await rewriteProductPage.goToRewriteProductPage(inseamProduct);
        await test.step(`Click Add to Bag button and check for error message`, async () => {
          await rewriteProductPage.addToBag.click();
          const notificationMessage = await rewriteProductPage.getErrorMessageText();
          expect(notificationMessage).toMatch('Select a size & inseam before adding to bag');
          await expect(rewriteProductPage.addToBag).toBeDisabled();
          await rewriteProductPage.availableSize.click();
          const notificationMessage1 = await rewriteProductPage.getErrorMessageText();
          expect(notificationMessage1).toMatch('Select an inseam before adding to bag');
          await rewriteProductPage.availableSizeDimension2.click();
          await expect(rewriteProductPage.addToBagNotification).toBeHidden();
        });
      });

      test(`${brand}-${market}-${breakpoint}-${env}: Land on a product page with Waist & Length dimensions.`, async ({ rewriteProductPage }) => {
        const waistProduct = builder.withBelow35Product();
        await rewriteProductPage.goToRewriteProductPage(waistProduct);
        await test.step(`Click Add to Bag button and check for error message`, async () => {
          await rewriteProductPage.addToBag.click();
          const notificationMessage = await rewriteProductPage.getErrorMessageText();
          expect(notificationMessage).toMatch('Select a waist & length before adding to bag');
          await expect(rewriteProductPage.addToBag).toBeDisabled();
          await rewriteProductPage.availableSize.click();
          const notificationMessage1 = await rewriteProductPage.getErrorMessageText();
          expect(notificationMessage1).toMatch('Select a length before adding to bag');
          await rewriteProductPage.availableSizeDimension2.click();
          await expect(rewriteProductPage.addToBagNotification).toBeHidden();
        });
      });
    });
  });
});
