/* eslint-disable no-console */
// @ts-check
import { expect } from '@playwright/test';
import Builder from '../../test_data/builder';
import test from '@/pom/base.page';
import { areWeInNext, scrollSlight, waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`PRODUCT PAGE`, { tag: ['@run-all'] }, () => {
  test.describe(`Rewrite`, () => {
    test.beforeEach(async ({ page, rewriteProductPage }) => {
      await test.step(`Go to '${brand}'-brand product page`, async () => {
        const inseamProduct = builder.withRandomProductStyleColor();
        await rewriteProductPage.goToRewriteProductPage(inseamProduct);
      });
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        await waitForHydration(page);
        await scrollSlight(page);
      });
    });
    test.describe(`CHECK THE SHIPPING AND PICKUP TILE`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Land on the standard product page`, { tag: ['@skip-mobile'] }, async ({ rewriteProductPage }) => {
        await test.step(`Check the shipping tile and its contents`, async () => {
          await expect(rewriteProductPage.shippingTile).toBeVisible();
          expect(await rewriteProductPage.getShippingHeader()).toMatch('Free Shipping');
          expect(await rewriteProductPage.getLoyaltyMessage()).toMatch('on $50+ for Rewards Members.');
          expect(await rewriteProductPage.getLoyaltyLinkMessage()).toMatch('Sign in or Join');
          expect(await rewriteProductPage.getLoyaltyLink()).toContain('/my-account/sign-in?targetURL=/browse/product.do');
          await expect(rewriteProductPage.pickupTile).toBeVisible();
          expect(await rewriteProductPage.getPickupHeader()).toMatch('In-Store Pickup');
          expect(await rewriteProductPage.getChangeStoreText()).toMatch('Select a Store');
        });
      });
    });

    test.describe(`CHECK THE FULFILLMENT AFTER SIZE SELECTION`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Land on the standard product page`, { tag: ['@skip-mobile'] }, async ({ rewriteProductPage }) => {
        await test.step(`Select a size and check the fulfillment Tiles`, async () => {
          await rewriteProductPage.availableSize.click();
          expect(await rewriteProductPage.getShippingInventory()).toMatch('In Stock');
          await rewriteProductPage.changeStoreLink.click();
          await rewriteProductPage.fillZipCode('94102');
          await rewriteProductPage.toggleInStockStores();
          if (await rewriteProductPage.changeStoreInStockStoresVisible()) {
            await rewriteProductPage.selectStore(1);
            await rewriteProductPage.clickDoneBtn();
            expect(await rewriteProductPage.getPickupInventory()).toMatch('In Stock');
          }
        });

        await test.step(`Select an unavailable size and check the fulfillment Tiles`, async () => {
          if (await rewriteProductPage.unAvailableSize.isVisible()) {
            await rewriteProductPage.unAvailableSize.click();
            expect(await rewriteProductPage.getShippingInventory()).toMatch('Out of Stock');
            expect(await rewriteProductPage.getPickupInventory()).toMatch('Out of stock at selected store');
          }
        });
      });
    });
  });
});
