/* eslint-disable no-console */
// @ts-check
import { expect } from '@playwright/test';
import Builder from '../../test_data/builder';
import test from '@/pom/base.page';
import { areWeInNext, scrollSlight, waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`GIFT CARD PRODUCT`, { tag: ['@run-all'] }, () => {
  test.describe(`Rewrite`, () => {
    test.beforeEach(async ({ page, rewriteProductPage }) => {
      await test.step(`Go to '${brand}'-brand product page`, async () => {
        const giftCardProduct = builder.withGiftCard();
        await rewriteProductPage.goToRewriteProductPage(giftCardProduct);
      });
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        await waitForHydration(page);
        await scrollSlight(page);
      });
    });
    test.describe(`CHECKS ON PAGE`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Land on a Gift card page.`, { tag: ['@skip-mobile'] }, async ({ rewriteProductPage }) => {
        await test.step(`Check the page title is present`, async () => {
          const productTitle = await rewriteProductPage.productTitle.innerText();
          const GiftCard = brand === 'on' ? ' Gift Card US' : brand === 'gp' || brand === 'gpfs' ? ' GiftCard' : ' Gift Card';
          const expectedBrand =
            brand === 'gp' || brand === 'gpfs' ? 'Gap' : brand === 'br' || brand === 'brfs' ? 'Banana Republic' : brand === 'at' ? 'Athleta' : 'Old Navy';
          expect(productTitle).toContain(expectedBrand + GiftCard);
        });
        await test.step(`basic Check  on gift cards`, async () => {
          await expect(rewriteProductPage.sizeSelectorContainer).toBeVisible();
          await expect(rewriteProductPage.starRatings).not.toBeAttached();
          await expect(rewriteProductPage.colorContainer).toBeVisible();
          await expect(rewriteProductPage.fulFillment).toBeHidden();
        });
        await test.step(`Click Add to Bag button and check for error message`, async () => {
          await rewriteProductPage.addToBag.click();
          const notificationMessage = await rewriteProductPage.addToBagNotification.innerText();
          expect(notificationMessage).toMatch('Make a selection before adding to bag');
        });
        await test.step(`Test Product Information on gift card product`, async () => {
          await expect(rewriteProductPage.productDetails).toBeVisible();
          if (brand === 'br' || brand === 'brfs') {
            await expect(rewriteProductPage.shippingReturns).not.toBeAttached();
          } else {
            await expect(rewriteProductPage.shippingReturns).toBeAttached();
          }
          await expect(rewriteProductPage.fabricCare).not.toBeAttached();
          await expect(rewriteProductPage.fitSizing).not.toBeAttached();
          await expect(rewriteProductPage.detailsInfocontent).toBeVisible();
          const sixDigitNumberPattern = /\b\d{6}\b/;
          await expect(rewriteProductPage.detailsInfocontent).toHaveText(sixDigitNumberPattern);
          await expect(rewriteProductPage.detailsInfocontent).toBeVisible();
        });
      });

      test(`${brand}-${market}-${breakpoint}-${env}: Check for ADD TO BAG Functionality`, async ({ rewriteProductPage }) => {
        await test.step(`Click on ATB  and  check the modal is visible`, async () => {
          await rewriteProductPage.selectSize();
          await rewriteProductPage.addToBag.click();
          await rewriteProductPage.atbModalIsVisible();
          await expect(rewriteProductPage.bagCount).toHaveText('1 item');
        });
      });
    });
  });
});
