/* eslint-disable no-console */
// @ts-check
import { expect } from '@playwright/test';
import Builder from '../../test_data/builder';
import test from '@/pom/base.page';
import { areWeInNext, scrollSlight, waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`STANDARD PRODUCT`, { tag: ['@run-all'] }, () => {
  test.describe(`Rewrite`, () => {
    test.beforeEach(async ({ page, rewriteProductPage }) => {
      await test.step(`Go to '${brand}'-brand product page`, async () => {
        const product = builder.withBopisProduct();
        await rewriteProductPage.goToRewriteProductPage(product);
      });
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        await waitForHydration(page);
        await scrollSlight(page);
      });
    });
    test.describe(`INTERACTIONS ON Product Information`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Product Info .`, async ({ rewriteProductPage }) => {
        await test.step(`Check the product information on the page`, async () => {
          await expect(rewriteProductPage.productinfoToggle).toBeHidden();
          await expect(rewriteProductPage.productDetails).toBeVisible();
          await rewriteProductPage.productDetails.click();
          await expect(rewriteProductPage.fabricCare).toBeVisible();
          await rewriteProductPage.fabricCare.click();
          await expect(rewriteProductPage.fitSizing).toBeVisible();
          await rewriteProductPage.fitSizing.click();
          await expect(rewriteProductPage.detailsInfocontent).toBeVisible();
          await expect(rewriteProductPage.shippingReturns).toBeVisible();
          const detailsInfocontentElement = await rewriteProductPage.detailsInfocontent;
          const productDetailsContentText = await detailsInfocontentElement.innerText();
          const sixDigitNumberPattern = /\b\d{6}\b/;
          expect(sixDigitNumberPattern.test(productDetailsContentText)).toBeTruthy();
        });

        await test.step(`Check the product Heading as per brand`, async () => {
          const expectedHeading = brand === 'at' ? 'fabric + care' : 'Materials & Care';
          const actualHeading = rewriteProductPage.fabricCare;
          await expect(actualHeading).toHaveText(expectedHeading);
        });
      });

      test.skip(
        `${brand}-${market}-${breakpoint}-${env}: Check the product information on the page`,
        { tag: ['@run-on'] },
        async ({ rewriteProductPage, page }) => {
          await test.step(`Click on UTAG Message check it navigate to another window`, async () => {
            await rewriteProductPage.productDetails.click();
            await page.waitForTimeout(3000);
            await rewriteProductPage.utahlawLabel.click();
            const newPage = await page.waitForEvent('popup');
            await newPage.waitForLoadState();
            expect(newPage.url()).toContain('https://prod.globalrsinc.com/lawlabellookup/search/oldnavy');
          });
        }
      );
    });
  });
});
