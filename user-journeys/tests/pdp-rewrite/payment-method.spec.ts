/* eslint-disable no-console */
// @ts-check
import { expect } from '@playwright/test';
import Builder from '../../test_data/builder';
import test from '@/pom/base.page';
import { areWeInNext, scrollSlight, waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`PAYMENT METHODS`, { tag: ['@run-all'] }, () => {
  test.describe(`Rewrite`, () => {
    test.beforeEach(async ({ page, rewriteProductPage }) => {
      await test.step(`Go to '${brand}'-brand product page`, async () => {
        const paymentProduct = builder.withBelow35Product();
        await rewriteProductPage.goToRewriteProductPage(paymentProduct);
      });
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        await waitForHydration(page);
        await scrollSlight(page);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Test for method below 35$`, { tag: ['@run-all'] }, async ({ rewriteProductPage }) => {
      await test.step('Check payment method visibility', async () => {
        const paymentMethod = await rewriteProductPage.paymentMethod.innerText();
        expect(paymentMethod).toContain('available for orders above $35');
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Check the Paypal link and check the modal`, { tag: ['@run-all'] }, async ({ page, rewriteProductPage }) => {
      await test.step('Check Paypal modal visibility', async () => {
        await rewriteProductPage.paypalIcon.click();
        await rewriteProductPage.paypalModalIsVisible();
        const paymentMethod = page.getByText('available for orders above $35');
        await expect(paymentMethod).toHaveCSS('color', 'rgb(0, 0, 0)');
      });
    });

    test(
      `${brand}-${market}-${breakpoint}-${env}: Check the afterpay link and check the modal`,
      { tag: ['@run-all'] },
      async ({ page, rewriteProductPage }) => {
        await test.step('Check Afterpay modal visibility', async () => {
          await rewriteProductPage.afterpayIcon.click();
          await rewriteProductPage.afterpayModalIsVisible();
          const paymentMethod = page.getByText('available for orders above $35 ');
          await expect(paymentMethod).toHaveCSS('color', 'rgb(0, 0, 0)');
        });
      }
    );

    test(`${brand}-${market}-${breakpoint}-${env}: Test for method Over 35$.`, { tag: ['@run-all'] }, async ({ rewriteProductPage }) => {
      await test.step('Go to product page and check payment method', async () => {
        const paymentProduct = builder.withOver35Product();
        await rewriteProductPage.goToRewriteProductPage(paymentProduct);
        const paymentMethod = await rewriteProductPage.paymentMethod.innerText();
        expect(paymentMethod).toContain('4 interest-free payments ');
        const currentPriceText = await rewriteProductPage.currentSaleprice.innerText();
        const currentPrice = parseFloat(currentPriceText.replace(/[^0-9.-]+/g, ''));
        const splitPrice = (currentPrice / 4).toFixed(2);
        await expect(rewriteProductPage.paymentMethod).toContainText(splitPrice);
      });
    });
  });
});
