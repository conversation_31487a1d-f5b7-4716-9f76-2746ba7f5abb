/* eslint-disable no-console */
// @ts-check
import { expect } from '@playwright/test';
//import { getStyle } from '../../test_data/productStyles';
import Builder from '../../test_data/builder';
import test from '@/pom/base.page';
import { areWeInNext, scrollSlight, waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`PRODUCT PAGE`, { tag: ['@run-all'] }, () => {
  test.describe(`Rewrite`, () => {
    test.beforeEach(async ({ page, rewriteProductPage, shoppingBagPage }) => {
      await test.step(`Go to '${brand}'-brand product page`, async () => {
        const stdProduct = await shoppingBagPage.getStandardProduct();
        await rewriteProductPage.goToRewriteProductPage(stdProduct);
      });

      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        await waitForHydration(page);
        await scrollSlight(page);
      });
    });

    test.describe(`STYLE TESTS`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Check the Page Title styles.`, async ({ rewriteProductPage }) => {
        await test.step(`Check the product title styles`, async () => {
          await expect(rewriteProductPage.productTitle).toHaveCSS('color', 'rgb(0, 0, 0)');
          await expect(rewriteProductPage.productTitle).toHaveCSS('font-size', builder.withStyleProperty('titleFontSize'));
          await expect(rewriteProductPage.productTitle).toHaveCSS('line-height', 'normal');
          await expect(rewriteProductPage.productTitle).toHaveCSS('font-style', 'normal');
          await expect(rewriteProductPage.productTitle).toHaveCSS('font-weight', builder.withStyleProperty('titleFontWeight'));
          await expect(rewriteProductPage.productTitle).toHaveCSS('font-family', builder.withStyleProperty('fontFamily'));
          await expect(rewriteProductPage.productTitle).toHaveCSS('letter-spacing', builder.withStyleProperty('titleLetterSpacing'));
        });
      });
    });
  });
});
