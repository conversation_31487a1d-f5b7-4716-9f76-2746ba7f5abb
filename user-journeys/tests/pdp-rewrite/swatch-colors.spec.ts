/* eslint-disable no-console */
// @ts-check
import { expect } from '@playwright/test';
import Builder from '../../test_data/builder';
import test from '@/pom/base.page';
import { areWeInNext, scrollSlight, waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`STANDARD PRODUCT`, { tag: ['@run-all'] }, () => {
  test.describe(`Rewrite`, () => {
    test.beforeEach(async ({ page, rewriteProductPage }) => {
      await test.step(`Go to '${brand}'-brand product page`, async () => {
        const product = builder.withBopisProduct();
        await rewriteProductPage.goToRewriteProductPage(product);
      });
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        await waitForHydration(page);
        await scrollSlight(page);
      });
    });
    test.describe(`INTERACTIONS ON SWATCHES`, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Interact with swatch colors and verify color label update.`, async ({ rewriteProductPage, page }) => {
        await test(`Click on a color swatch and check if the color label updates`, async () => {
          const initialColorLabel = await rewriteProductPage.colorLabel.innerText();
          const colorSwatches = rewriteProductPage.colorSwatches;
          const colorSwatchCount = await colorSwatches.count();

          if (colorSwatchCount > 0) {
            await colorSwatches.first().click();
            const updatedColorLabel = rewriteProductPage.colorLabel;
            await await expect(updatedColorLabel).not.toHaveText(initialColorLabel);
          }
        });
        await test.step(`Hover on Color swatches and check the color label is updated`, async () => {
          const initialColorLabel = await rewriteProductPage.colorLabel.innerText();
          const colorSwatches = rewriteProductPage.colorSwatches;
          const colorSwatchCount = await colorSwatches.count();

          if (colorSwatchCount > 0) {
            await colorSwatches.first().hover();
            const updatedColorLabel = rewriteProductPage.colorLabel;
            await await expect(updatedColorLabel).not.toHaveText(initialColorLabel);
          }
        });
        await test.step(`Click on swatch  and check main image is updated`, async () => {
          const imgSrc = rewriteProductPage.mainImage;
          await expect(imgSrc).toHaveAttribute('src', '/webcontent/0057/373/199/cn57373199.jpg');
          const swatchColor = rewriteProductPage.colorSwatches.nth(2);
          await swatchColor.click();
          await page.waitForTimeout(20000);
          const newImgSrc = rewriteProductPage.mainImage;
          await await expect(newImgSrc).toHaveAttribute('src', '/webcontent/0055/828/751/cn55828751.jpg');
        });
      });
    });
  });
});
