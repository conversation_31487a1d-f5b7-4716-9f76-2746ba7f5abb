/* eslint-disable playwright/no-force-option */
/* eslint-disable no-console */
// @ts-check
import { expect } from '@playwright/test';
import _ from 'lodash';
import test from '@/pom/base.page';
import {
  areWeInNext,
  getPageType,
  scrollElementToCenter,
  scrollSlight,
  scrollToBottomUntilPageDoesNotGrow,
  scrollToLocator,
  waitForHydration,
} from '@/utils/helpers';
import Builder from '@/test_data/builder';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();
const zipCode = builder.withBopisZipCode();

test.describe(`Product`, () => {
  test.describe(`Rewrite`, () => {
    test.beforeEach(async ({ page, rewriteProductPage, shoppingBagPage, categoryPage, sitewide }) => {
      switch (true) {
        case test.info().title.includes('Add a BOPIS product'):
          await test.step(`Go to Category Page`, async () => {
            const category = builder.withBopisCategory();
            await categoryPage.goToCategoryPage(category);
          });
          break;
        case test.info().title.includes('breadcrumb') || test.info().title.includes('related items'):
          await test.step(`Go to Product Page`, async () => {
            const category = builder.withCategory();
            await categoryPage.goToCategoryPage(category);
            await waitForHydration(page);
            await categoryPage.selectProduct();
          });
          break;
        case test.info().title.includes('Error messaging') ||
          test.info().title.includes('Size Guide') ||
          test.info().title.includes('update images') ||
          test.info().title.includes('Review'):
          await test.step(`Go to Product Page`, async () => {
            const stdStyle = await shoppingBagPage.getStandardStyle();
            await rewriteProductPage.goToProductPage(stdStyle);
          });
          break;
        case test.info().title.includes('Switch product variants'):
          // eslint-disable-next-line no-case-declarations
          const multiVariantStyle = await shoppingBagPage.getMultiVariantStyle();
          await test.step(`Go to Product Page with multiple variants`, async () => {
            await rewriteProductPage.goToProductPage(multiVariantStyle);
          });
          break;
        default:
          await test.step(`Go to '${brand}'-brand product page`, async () => {
            const productSyle = await shoppingBagPage.getStandardProduct();
            await rewriteProductPage.goToProductPage(productSyle);
          });
          break;
      }
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        await waitForHydration(page);
        await scrollSlight(page);
      });

      await test.step(`Ensure that Sitewide Components are visible on page.`, async () => {
        await sitewide.checkSiteWideElementsAreOnPage();
      });
    });

    test.describe(`ADD TO BAG`, { tag: ['@run-all', '@skip-wip-stage'] }, () => {
      test(
        `${brand}-${market}-${breakpoint}-${env}: Add to bag with multiple quantity & click checkout to go to shopping bag.`,
        { tag: ['@skip-mobile'] },
        async ({ page, rewriteProductPage, shoppingBagPage, sitewide }) => {
          const productQuantity = '2';
          await test.step(`Select product quantity of '${productQuantity}'`, async () => {
            await rewriteProductPage.selectQuantity(productQuantity);
          });

          await test.step(`Click Add to Bag with existing color & size.`, async () => {
            const addToBagResponsePromise = page.waitForResponse(/\/shopping-bags\/items\/summary/);
            await rewriteProductPage.addProductToBag();
            const addToBagResponse = await addToBagResponsePromise;
            !addToBagResponse.ok() && console.error((await addToBagResponse.body()).toString());
            expect(addToBagResponse.ok(), `validate the add to bag request processed correctly`).toBeTruthy();
          });

          await test.step(`Click Checkout from add to bag modal to go to shopping bag page.`, async () => {
            await rewriteProductPage.checkout.click();
          });

          await test.step(`Land on shopping bag page with product in bag`, async () => {
            await page.waitForURL(/shopping-bag/);
            await expect(shoppingBagPage.productNameLink).toHaveAttribute('href', /product.do/);
            await expect(sitewide.bagCount, `ensure the bag count is incremented`).toHaveText('2');
          });
        }
      );

      test(`${brand}-${market}-${breakpoint}-${env}: Add to bag with alternate size & Keep Shopping to remain on product page.`, async ({
        page,
        rewriteProductPage,
      }) => {
        await test.step(`Select an alternate size.`, async () => {
          await rewriteProductPage.selectSize();
        });

        await test.step(`Click Add to Bag with with new size.`, async () => {
          const addToBagPromise = page.waitForRequest(/\/commerce\/shopping-bags\/items\/summary/, { timeout: 40000 });
          await rewriteProductPage.addProductToBag();
          const addToBagRequest = await addToBagPromise;
          const addToBagJSON = JSON.parse(addToBagRequest.postData()!);
          const pageURL = new URL(page.url());
          console.log('addToBagJSON', addToBagJSON);
          expect(addToBagJSON.items[0].sku, 'validate the SKU requested from add to bag matched the pid in the URL').toBe(pageURL.searchParams.get('pid'));
        });
      });

      test(
        `${brand}-${market}-${breakpoint}-${env}: Add a BOPIS product to bag & Keep Shopping to remain on product page.`,
        { tag: ['@skip-brf', '@skip-stage'] },
        async ({ page, categoryPage, rewriteProductPage, sitewide }) => {
          await test.step(`I land on a category and enable BOPIS (In Store Pickup) for '${zipCode}'`, async () => {
            if (brand === 'br') {
              await categoryPage.clickFilterFacetButton();
            } else {
              await categoryPage.allFacetsButton.click({ force: true });
            }
            await categoryPage.selectBopisStore(zipCode);
          });

          await test.step('I select a Size from category filter', async () => {
            await categoryPage.sizeFilterDropdown.click();
            await categoryPage.sizeFilterOptions.nth(2).click();
            await categoryPage.closeFacetsButton.click();
          });

          const bopisResponsePromise = page.waitForResponse(/\/commerce\/catalog\/inventory\/v1\/style\/store\/status/, { timeout: 60000 });
          await test.step(`I click on a BOPIS product`, async () => {
            await categoryPage.selectProduct();
          });

          await test.step(`I select the first available BOPIS size'`, async () => {
            const bopisResponse = await bopisResponsePromise;
            const bopisAvailability = await bopisResponse.json();
            const bopisVariants = bopisAvailability.storeStyleInventory.variantList.filter(
              (variant: { customerChoiceList: { skuList: { status: string }[] }[]; status: string }) =>
                variant.status === 'IN_STOCK' && variant.customerChoiceList.some(cc => cc.skuList.some(sku => sku.status === 'IN_STOCK'))
            );
            const bopisMergedCustomerChoices = bopisVariants.map((variant: { customerChoiceList: unknown[] }) => variant.customerChoiceList).flat();
            const bopisMergedCustomerChoicesInStock = bopisMergedCustomerChoices.filter((cc: { status: string }) => cc.status === 'IN_STOCK');
            const bopisMergedSkuLists = bopisMergedCustomerChoicesInStock.map((cc: { skuList: unknown[] }) => cc.skuList).flat();
            const bopisMergedSkuListsInStock = bopisMergedSkuLists.filter((sku: { status: string }) => sku.status === 'IN_STOCK');
            const bopisSku = _.sample(bopisMergedSkuListsInStock);

            await rewriteProductPage.goToProductPage(bopisSku.skuId);
            await waitForHydration(page);

            await rewriteProductPage.clickInStorePickupOption();
          });

          await test.step(`I click Add To Bag`, async () => {
            await rewriteProductPage.addProductToBag();
          });

          await test.step(`I validate the "ADDED TO YOUR BAG" dialog appears`, async () => {
            await expect(rewriteProductPage.getAddedToBagLocator()).toHaveText(/Added to/i);
          });

          await test.step(`Click Keep Shopping from add to bag modal to remain on Product Page.`, async () => {
            await rewriteProductPage.clickKeepShopping();
          });

          await test.step(`Remain on Product Page with product in bag`, async () => {
            await expect.poll(async () => getPageType(page), 'ensure we see Product Page type').toBe('product');
            await expect(sitewide.bagCount, `ensure the bag count is incremented`).toHaveText('1');
          });
        }
      );
    });

    test.describe(`DETAILS`, { tag: ['@run-all'] }, () => {
      test(
        `${brand}-${market}-${breakpoint}-${env}: Switch product variants and ensure page loads correctly.`,
        { tag: ['@do-not-disable-optimizely'] },
        async ({ rewriteProductPage, page }) => {
          await test.step(`Switch product variants and ensure page loads correctly.`, async () => {
            await rewriteProductPage.unselectedVariants.first().click();
            await expect.poll(async () => getPageType(page), 'ensure we see Product Page type').toBe('product');
            await expect(rewriteProductPage.productImage, `ensure product image is visible`).toBeVisible();
            await expect(rewriteProductPage.addToBag, `ensure add to bag button is visible`).toBeVisible();
          });
        }
      );

      test(
        `${brand}-${market}-${breakpoint}-${env}: Selecting a new product color will update images.`,
        { tag: ['@skip-preview'] },
        async ({ rewriteProductPage }) => {
          if ((await rewriteProductPage.inStockColor.count()) >= 2) {
            await test.step(`Select an alternate color and ensure images are updated.`, async () => {
              await scrollElementToCenter(rewriteProductPage.inStockColor.first());
              await rewriteProductPage.inStockColor.first().click({ force: true });
              await rewriteProductPage.productImage.waitFor();
              console.log('productPage.productImage', await rewriteProductPage.productImage.getAttribute('src'));
              await expect(rewriteProductPage.productImage, `validate image has a source attribute`).toHaveAttribute('src', /[a-zA-Z0-9]*/);
              const initialImage = await rewriteProductPage.productImage.getAttribute('src');
              await scrollElementToCenter(rewriteProductPage.availableColor.first());
              await rewriteProductPage.availableColor.first().click({ force: true });
              await expect(rewriteProductPage.productImage, `ensure the product images are updated`).not.toHaveAttribute('src', initialImage!);
            });
          } else {
            await test.step(`I check that product images appear`, async () => {
              await expect(rewriteProductPage.productImage).toBeVisible();
            });
          }
        }
      );

      test(`${brand}-${market}-${breakpoint}-${env}: Ensure Product Details, Fabric & Care, Fit & Sizing and Size Guide are accessible.`, async ({
        rewriteProductPage,
        page,
      }) => {
        await test.step(`Ensure FIT & SIZING content is accessible`, async () => {
          if (brand?.startsWith('gp')) {
            await scrollToLocator(page, rewriteProductPage.fitSizing);
            await rewriteProductPage.fitSizing.click();
            await expect(rewriteProductPage.fitSizing).toHaveAttribute('aria-expanded', 'true');
          } else {
            await expect(rewriteProductPage.fitSizing).toBeVisible();
          }
        });

        await test.step(`Ensure PRODUCT DETAILS content is accessible`, async () => {
          if (brand?.startsWith('gp')) {
            await scrollToLocator(page, rewriteProductPage.productDetails);
            await rewriteProductPage.productDetails.click({ force: breakpoint === 'mobile' });
            await expect(rewriteProductPage.productDetails).toHaveAttribute('aria-expanded', 'true');
          } else {
            await expect(rewriteProductPage.productDetails).toBeVisible();
          }
        });

        await test.step(`Ensure FABRIC/MATERIALS & CARE content is accessible`, async () => {
          if (brand?.startsWith('gp')) {
            await rewriteProductPage.fabricCare.click();
            await expect(rewriteProductPage.fabricCare).toHaveAttribute('aria-expanded', 'true');
          } else {
            await expect(rewriteProductPage.fabricCare).toBeVisible();
          }
        });

        await test.step(`Click on SIZE GUIDE and validate content is visible`, async () => {
          await rewriteProductPage.sizeGuide.waitFor({ state: 'visible', timeout: 3000 });
          await rewriteProductPage.clickSizeGuide();
          await expect(rewriteProductPage.sizeGuide, `ensure size guide is visible.`).toBeVisible();
        });
      });
    });

    test.describe(`SHIPPING`, { tag: ['@run-gp'] }, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Ensure Shipping & Returns info is visible and accessible.`, async ({ page, rewriteProductPage }) => {
        await test.step(`I click on SHIPPING & RETURNS and validate content is visible`, async () => {
          await scrollToLocator(page, rewriteProductPage.shippingReturns);
          breakpoint === 'mobile' && (await scrollElementToCenter(rewriteProductPage.shippingReturns));
          await rewriteProductPage.shippingReturns.click({ force: breakpoint === 'mobile' });
          await expect(rewriteProductPage.shippingAndReturnsModal, `ensure Shipping & Returns info is visible.`).toBeVisible();
        });
      });
    });

    test.describe(`NAVIGATE`, { tag: ['@run-all'] }, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Navigate to the Category Page using breadcrumb links on Product Page.`, async ({
        page,
        rewriteProductPage,
        categoryPage,
      }) => {
        await test.step(`I land on product page and click on the category breadcrumb link`, async () => {
          await rewriteProductPage.clickCategoryBreadcrumb();
        });

        await test.step(`I ensure that I land on Category page`, async () => {
          await expect.poll(async () => await getPageType(page), 'ensure we see Product Page type').toBe('category');
          await expect(categoryPage.productGrid, `ensure the product grid is visible`).toBeVisible();
        });
      });

      test(
        `${brand}-${market}-${breakpoint}-${env}: Navigate to the Division Page using breadcrumb links on Product Page.`,
        { tag: ['@skip-at'] },
        async ({ page, rewriteProductPage }) => {
          await test.step(`I land on product page and click on the division breadcrumb link`, async () => {
            await rewriteProductPage.clickDivisionBreadcrumb();
          });

          await test.step(`I ensure that I land on Division page`, async () => {
            await page.waitForURL(/cid=/);
            await expect.poll(async () => await getPageType(page), 'ensure we see expected Page type').toMatch(/division|category/);
          });
        }
      );

      test(
        `${brand}-${market}-${breakpoint}-${env}: Navigate to a new Product Page by selecting a recommended product.`,
        { tag: ['@skip-gpf', '@skip-brf', '@skip-on-ca', '@skip-at', '@skip-br'] },
        async ({ page, rewriteProductPage }) => {
          await test.step(`Click a recommended product to go to new page`, async () => {
            const firstURL = page.url();
            await scrollToBottomUntilPageDoesNotGrow(page);
            await rewriteProductPage.selectRecommendedProduct();
            await expect(page).not.toHaveURL(firstURL);
          });

          await test.step(`I check that page has loaded & product images appear`, async () => {
            await expect(rewriteProductPage.productImage).toBeVisible();
          });
        }
      );

      test(
        `${brand}-${market}-${breakpoint}-${env}: Navigate to a new Page using a related items link.`,
        { tag: ['@run-at', '@run-br-ca'] },
        async ({ page, rewriteProductPage, sitewide }) => {
          await test.step(`Click a related link to go to new page`, async () => {
            const firstURL = page.url();
            await rewriteProductPage.clickRelatedItemsLink();
            await expect(page).not.toHaveURL(firstURL);
          });

          await test.step(`I check that page has loaded & Bag Icon appears`, async () => {
            await expect(sitewide.bagIcon).toBeVisible();
          });
        }
      );
    });

    test.describe(`ERROR`, { tag: ['@run-all'] }, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Ensure Add to Bag Error messaging is seen when no size is selected.`, async ({
        page,
        rewriteProductPage,
      }) => {
        await test.step(`I land on product page and click Add to Bag without selecting a size.`, async () => {
          await scrollToLocator(page, rewriteProductPage.addToBag);
          breakpoint === 'mobile' && (await scrollElementToCenter(rewriteProductPage.addToBag));
          await rewriteProductPage.addToBag.click({ force: breakpoint === 'mobile' });
        });

        await test.step(`I ensure that Error messaging is seen when no size is selected.`, async () => {
          await expect(rewriteProductPage.errorMessage).toBeVisible();
        });
      });
    });

    test.describe(`REVIEWS`, { tag: ['@run-all', '@skip-local'] }, () => {
      test(`${brand}-${market}-${breakpoint}-${env}: Ensure Reviews are visible with Review count displayed.`, async ({ rewriteProductPage }) => {
        breakpoint === 'mobile' ? await scrollElementToCenter(rewriteProductPage.addToBag) : {};
        try {
          await test.step(`Ensure Star Rating is seen & validate review count is visible.`, async () => {
            await rewriteProductPage.reviewCount.waitFor({ state: 'visible', timeout: 5000 });
            expect(await rewriteProductPage.getNumberOfReviews()).toMatch(/[0-9]/);
          });
        } catch (e) {
          await test.step(`This product does not have any Reviews yet.`, async () => {
            await expect(rewriteProductPage.reviewCount).toBeHidden();
          });
        }
      });

      test(
        `${brand}-${market}-${breakpoint}-${env}: Ensure the ability to write a Review is available.`,
        { tag: ['@skip-wip-stage'] },
        async ({ rewriteProductPage }) => {
          await test.step(`Click reviews on buy box to scroll down to Review section.`, async () => {
            breakpoint === 'mobile' ? await scrollElementToCenter(rewriteProductPage.addToBag) : {};
            if (await rewriteProductPage.reviewCount.isVisible()) {
              await rewriteProductPage.clickReviews();
              await rewriteProductPage.writeReview.click();
            } else {
              await rewriteProductPage.writeFirstReview.click();
            }
          });

          await test.step(`Ensure all required elements are visible in the Review Modal.`, async () => {
            await expect(rewriteProductPage.reviewHeadline).toBeVisible({ timeout: 30000 });
            await expect(rewriteProductPage.reviewComments).toBeVisible();
            await expect(rewriteProductPage.reviewName).toBeVisible();
            await expect(rewriteProductPage.reviewEmail).toBeVisible();
            await expect(rewriteProductPage.submitReview).toBeVisible();
          });
        }
      );
    });
  });
});
