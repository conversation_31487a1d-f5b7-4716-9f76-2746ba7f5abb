// @ts-check
import fs from 'fs';
import { expect } from '@playwright/test';
import { areWeInNext, scrollElementToCenter, waitForHydration } from '../../utils/helpers';
import test from '../../pom/base.page';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`Shopping Bag`, { tag: ['@run-at', '@run-br', '@run-gp', '@run-gpf', '@run-brf', '@run-on'] }, () => {
  test.beforeEach(async ({ shoppingBagPage, page, sitewide }) => {
    await test.step(`Go to the '${brand}'-brand Shopping Bag page`, async () => {
      await shoppingBagPage.goToShoppingBagPage();
      // eslint-disable-next-line playwright/no-nested-step
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });
      await waitForHydration(page);
    });

    await test.step(`Ensure that Sitewide Components are visible on page.`, async () => {
      await sitewide.checkSiteWideElementsAreOnPage();
    });
  });

  test.afterEach(async ({ shoppingBagPage }) => {
    await test.step(`Clear Shopping Bag`, async () => {
      await shoppingBagPage.clearShoppingBag(false);
    });
  });

  if (!process.env.CI) {
    test.afterAll(async () => {
      await test.step(`Delete all add to catalog data`, async () => {
        try {
          fs.rmSync(`./skus/`, { recursive: true });
          // eslint-disable-next-line no-empty
        } catch (e) {}
      });
    });
  }

  test.describe(`EMPTY BAG`, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Go to Shopping Bag page with no products.`, async ({ page, shoppingBagPage }) => {
      await test.step(`Land on Shopping bag page with zero items in your bag`, async () => {
        await page.waitForLoadState('load');
        await page.waitForURL(/shopping-bag/);
        await page.getByText(/Sign in to see any saved items/i).waitFor({ timeout: 30000 });
        await expect(shoppingBagPage.emptyBagHeader).toHaveText(/Your bag is currently empty/);
      });

      await test.step('I validate that there are no Saved items', async () => {
        await expect(shoppingBagPage.emptySavedBag).toHaveText(/0 items/i);
      });

      await test.step(`I Validate that Sign in button is displayed`, async () => {
        await expect(shoppingBagPage.emptyBagSignIn).toBeVisible();
      });
    });
  });

  test.describe(`PRODUCT DETAILS`, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Check Bag to see if product has been added.`, async ({ page, shoppingBagPage }) => {
      await shoppingBagPage.addStandardProductToBag();

      await test.step(`Ensure product name/title exists and is clickable`, async () => {
        await page.waitForURL(/shopping-bag/);
        // await expect(page).toHaveTitle(/Shopping Bag/);
        await expect(shoppingBagPage.productNameLink).toHaveAttribute('href', /product.do/);
      });

      await test.step('I am on shopping bag page and product size & color are visible', async () => {
        await expect(shoppingBagPage.productSize).toBeVisible();
        await expect(shoppingBagPage.productColor).toBeVisible();
      });

      await test.step('I am on shopping bag page and product price is visible', async () => {
        await expect(shoppingBagPage.productPrice).toBeVisible();
      });

      await test.step('I am on shopping bag page and product quantity is visible', async () => {
        await expect(shoppingBagPage.productQuantity).toBeVisible();
      });

      await test.step('I am on shopping bag page and Delete item button is visible', async () => {
        await expect(shoppingBagPage.deleteProduct).toBeVisible();
      });

      await test.step('I am on shopping bag page and Add Quantity button is visible', async () => {
        await expect(shoppingBagPage.addQuantity).toBeVisible();
      });

      await test.step('I am on shopping bag page and Save for Later button is visible', async () => {
        await expect(shoppingBagPage.saveForLater).toBeVisible();
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Add multiple products to the Shopping Bag.`, { tag: ['@skip-local'] }, async ({ page, shoppingBagPage }) => {
      await shoppingBagPage.addStandardProductToBag(1, false);
      await shoppingBagPage.addStandardProductToBag();

      await test.step(`Ensure first product image, name/title exists and is clickable`, async () => {
        await page.waitForURL(/shopping-bag/);
        await expect(shoppingBagPage.productTitle).toBeVisible();
        await expect(shoppingBagPage.productNameLink).toHaveAttribute('href', /product.do/);
      });

      await test.step(`Ensure second product image, name/title exists and is clickable`, async () => {
        await expect(shoppingBagPage.secondProductTitle).toBeVisible();
        await expect(shoppingBagPage.secondProductNameLink).toHaveAttribute('href', /product.do/);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Save a product for later and and then move back to Bag`, async ({ page, shoppingBagPage }) => {
      await shoppingBagPage.addStandardProductToBag();

      await test.step(`I move the product to Saved For Later`, async () => {
        await waitForHydration(page);
        breakpoint === 'mobile' && (await scrollElementToCenter(shoppingBagPage.saveForLater));
        await shoppingBagPage.saveForLater.click({ force: breakpoint === 'mobile' });
      });

      await test.step(`I validate that my Shopping Bag is empty and the item is saved for later`, async () => {
        await page.getByText(/Sign in to see any saved items/i).waitFor({ timeout: 30000 });
        await expect(shoppingBagPage.emptyBagHeader).toHaveText(/Your bag is currently empty/);
        await expect(shoppingBagPage.savedForLaterItemCount).toHaveText(/1 item/i);
      });

      await test.step(`I move the product back to the bag`, async () => {
        await shoppingBagPage.moveToBag.click();
      });

      await test.step(`I validate that the product is visible in the bag`, async () => {
        await expect(shoppingBagPage.productTitle).toBeVisible();
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Move the product to "Saved For Later" and then remove`, async ({ shoppingBagPage, page }) => {
      await shoppingBagPage.addStandardProductToBag();

      await test.step(`I move the product to Saved For Later`, async () => {
        await waitForHydration(page);
        `${brand}${market}${breakpoint}` === 'gpusmobile' && (await scrollElementToCenter(shoppingBagPage.saveForLater));
        await shoppingBagPage.saveForLater.click({ force: `${brand}${market}${breakpoint}` === 'gpusmobile' });
      });

      await test.step(`I validate that my Shopping Bag is empty and the item is saved for later`, async () => {
        await page.getByText(/Sign in to see any saved items/i).waitFor();
        await expect(shoppingBagPage.emptyBagHeader).toHaveText(/Your bag is currently empty/);
        await expect(shoppingBagPage.savedForLaterItemCount).toHaveText(/1 item/i);
      });

      await test.step('I remove the product from "Saved For Later"', async () => {
        await shoppingBagPage.deleteSaveForLaterProduct.click();
      });

      await test.step('I validate that "Saved For Later" is empty', async () => {
        await expect(shoppingBagPage.savedForLaterSignInLink).toBeVisible();
        await expect(shoppingBagPage.emptySavedBag).toHaveText(/0 items/i);
      });
    });

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that you can Find a Pick-up In Store location`,
      { tag: ['@skip-brf'] },
      async ({ shoppingBagPage }) => {
        await shoppingBagPage.addStandardProductToBag();

        await test.step(`I click the "Find a Store" link to open the modal`, async () => {
          breakpoint === 'mobile' && (await scrollElementToCenter(shoppingBagPage.findAStore));
          await shoppingBagPage.findAStore.click({ force: breakpoint === 'mobile' });
        });

        await test.step(`I enter a zip code to find stores near me`, async () => {
          await shoppingBagPage.enterBopisZip();
        });

        await test.step(`I validate that I can see stores near my zip code`, async () => {
          expect(shoppingBagPage.bopisStoreIsVisible()).toBeTruthy();
        });

        await test.step(`close change store modal by clicking on X button`, async () => {
          await shoppingBagPage.closeFindAStoreModal.click();
        });
      }
    );

    test(`${brand}-${market}-${breakpoint}-${env}: Navigate from bag to product page by clicking the product`, async ({
      shoppingBagPage,
      productPage,
      page,
    }) => {
      await shoppingBagPage.addStandardProductToBag();

      await test.step(`I click the first product image`, async () => {
        await waitForHydration(page);
        await shoppingBagPage.productTitle.click();
      });

      await test.step(`I am on the Product Page & ensure the Add-to-Bag button is visible`, async () => {
        await page.waitForURL(/product.do/);
        await expect(productPage.addToBag).toBeVisible();
      });
    });
  });

  test.describe(`ORDER SUMMARY`, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Ensure Shipping Notification, Subtotal, Total Price & Promo Input are visible.`, async ({
      shoppingBagPage,
    }) => {
      await shoppingBagPage.addStandardProductToBag();

      await test.step(`Ensure that the Bag Subtotal is visible and valid`, async () => {
        await expect(shoppingBagPage.subTotal).toHaveText(/\$\d+\.\d{2}/);
      });

      await test.step(`Ensure that the Bag Total Price is visible and valid`, async () => {
        await expect(shoppingBagPage.estTotal).toHaveText(/\$\d+\.\d{2}/);
      });

      await test.step(`Ensure that the Promo Input is visible`, async () => {
        await expect(shoppingBagPage.promoInput).toBeVisible();
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Click checkout to Navigate from Shopping Bag page to sign-in page`, async ({ shoppingBagPage, page }) => {
      await shoppingBagPage.addStandardProductToBag();

      await test.step(`I click the Checkout button from shopping bag page`, async () => {
        await expect(shoppingBagPage.checkoutButton).not.toHaveClass('disabled');
        brand === 'brf' && (await scrollElementToCenter(shoppingBagPage.checkoutButton));
        await shoppingBagPage.checkoutButton.click({ force: brand === 'brf' || (brand === 'gpf' && breakpoint === 'mobile') });
      });

      await test.step(`I get redirected to Sign In page`, async () => {
        await page.waitForURL(/sign-in/);
        await expect(page).toHaveTitle(/Sign in/);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Increase product quantity and ensure total price is updated`, async ({ shoppingBagPage }) => {
      await shoppingBagPage.addStandardProductToBag();

      const initialTotal = await shoppingBagPage.getTotalPrice();
      await test.step(`I can see that the initial total price is '${initialTotal}'`, async () => {});

      await test.step(`I add product quantity by 1`, async () => {
        await shoppingBagPage.addQuantity.click();
      });

      await test.step(`I validate that the total price has changed after quantity increase`, async () => {
        await shoppingBagPage.shippingItemCount.getByText('2').waitFor();
        expect(await shoppingBagPage.getTotalPrice()).not.toEqual(initialTotal);
      });
    });
  });

  test.describe(`PROMO`, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Ensure that Promo Error messaging is seen when an invalid promo is entered`, async ({ shoppingBagPage }) => {
      await shoppingBagPage.addStandardProductToBag();

      await test.step(`I enter an invalid promo code`, async () => {
        await shoppingBagPage.applyPromoCode('BADPROMO');
      });

      await test.step(`I ensure that there is a promo Error displayed`, async () => {
        await expect(shoppingBagPage.promoErrorMessage).toHaveText(
          /Enter a valid promo code|The code entered is invalid or expired|Unable to apply promo\. Please try again\./i
        );
      });
    });
  });

  test.describe(`CROSS-BRAND BAG`, { tag: ['@skip-stage', '@skip-brf-ca', '@skip-at', '@skip-br', '@skip-on', '@skip-gpf-us'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Add a product from all brands to the Shopping Bag.`, async ({ page, shoppingBagPage }) => {
      let brands = [];
      if (brand === 'gp') {
        brands = ['br', 'on', 'at', 'gp'];
      } else {
        brands = ['brf', 'gpf'];
      }
      for (const currentBrand of brands) {
        await test.step(`I add a product from ${currentBrand} to the Shopping Bag`, async () => {
          await shoppingBagPage.addStandardProductToBag(1, false, currentBrand);
        });
      }

      await test.step(`Refresh the page to see all products in the Shopping Bag`, async () => {
        await page.reload();
      });

      await test.step(`I validate that cross brand products are all in bag.`, async () => {
        await expect(await shoppingBagPage.getAllProductNames()).toHaveCount(brand === 'gpf' ? 2 : 4);
      });
    });
  });

  test.describe(`AI RECOMMENDATIONS`, { tag: ['@run-gp', '@skip-gpf', '@skip-ca', '@skip-for-deployment'] }, () => {
    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that AI Recommendations are visible on the Shopping Bag page`,
      { tag: ['@ai-recs-on'] },
      async ({ page, shoppingBagPage }) => {
        const recommendationsResponsePromise = page.waitForResponse(/\/product_recommendations\/v1/);
        await page.reload();

        await test.step(`I validate that Recommendations are visible`, async () => {
          await waitForHydration(page);
          breakpoint === 'mobile' && (await scrollElementToCenter(shoppingBagPage.aiRecommendations.first()));
          const response = await recommendationsResponsePromise;
          expect(response.status()).toBe(200);
          await expect(shoppingBagPage.aiRecommendationsCard.first()).toBeVisible({ timeout: 30000 });
          await expect(shoppingBagPage.aiRecommendationsImage.first()).toBeVisible();
          if (brand && brand.toLowerCase() !== 'br') {
            // Brand 'br' does not have price in Recommendations
            await expect(shoppingBagPage.aiRecommendationsPrice.first()).toBeVisible();
          }
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that AI Recommendations are not showing on the Shopping Bag page`,
      { tag: ['@ai-recs-off'] },
      async ({ page, shoppingBagPage }) => {
        test.fail();
        const recommendationsResponsePromise = page.waitForResponse(/\/product_recommendations\/v1/);
        await page.reload();

        await test.step(`I validate that Recommendations are visible`, async () => {
          await waitForHydration(page);
          breakpoint === 'mobile' && (await scrollElementToCenter(shoppingBagPage.aiRecommendations.first()));
          const response = await recommendationsResponsePromise;
          expect(response.status()).toBe(200);
        });
      }
    );
  });
});
