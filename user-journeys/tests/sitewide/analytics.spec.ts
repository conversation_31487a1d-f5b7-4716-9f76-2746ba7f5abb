/* eslint-disable playwright/expect-expect */
import { squashPopup } from './gap-factory';
import { checkAdobeAnalytics, checkTealiumAnalytics, registerAnalyticsData, waitForAnalytics } from '@/utils/analytics';
import test from '@/pom/base.page';

const brand = process.env.BRAND;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const market = process.env.MARKET;

test.describe('Sitewide', () => {
  test.describe('Analytics', { tag: ['@analytics'] }, () => {
    squashPopup();

    test.beforeEach(async ({ page }) => {
      await page.goto('');
    });

    registerAnalyticsData();

    test(`${brand}-${market}-${breakpoint}-${env}: Check analytics data on home page`, async ({ page, sitewide }) => {
      await waitForAnalytics(page, sitewide);
      await checkTealiumAnalytics(page, sitewide);
      await checkAdobeAnalytics(page, sitewide);
    });
  });
});
