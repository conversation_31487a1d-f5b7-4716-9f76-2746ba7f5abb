/* eslint-disable no-console */
// @ts-check
import { devices, expect } from '@playwright/test';
import { waitForHydration, areWeInNext } from '../../utils/helpers';
import test from '../../pom/base.page';
import { squashPopup } from './gap-factory';

const brand = process.env.BRAND;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const market = process.env.MARKET;

test.describe(`Sitewide`, () => {
  squashPopup();
  test.describe(`BREAKPOINT SWITCHING`, () => {
    test(
      `${brand}-${market}-${breakpoint}-${env}: Check that on breakpoint change the page renders as expected for desktop`,
      { tag: '@skip-mobile' },
      async ({ hamNav, homePage, page, sitewide }) => {
        await test.step(`Go to the '${brand}'-brand home page`, async () => {
          await homePage.goToHomePage();
          await expect.poll(async () => await areWeInNext(page), 'validate we are in nextjs').toBeTruthy();
          await waitForHydration(page);
          // eslint-disable-next-line playwright/no-wait-for-timeout
          await expect(sitewide.meganavWrapper, 'validate we are seeing MegaNav').toBeVisible({ timeout: 10000 });
          await page.setViewportSize(devices['iPhone 14'].viewport);
          await expect(hamNav.hamNavWrapper, 'validate we are seeing HamNav').toBeVisible({ timeout: 10000 });
        });
      }
    );
    test(
      `${brand}-${market}-${breakpoint}-${env}: Check that on breakpoint change the page renders as expected for mobile`,
      { tag: '@skip-desktop' },
      async ({ hamNav, homePage, page, sitewide }) => {
        await test.step(`Go to the '${brand}'-brand home page`, async () => {
          await homePage.goToHomePage();
          await expect.poll(async () => await areWeInNext(page), 'validate we are in nextjs').toBeTruthy();
          await waitForHydration(page);
          // eslint-disable-next-line playwright/no-wait-for-timeout
          await expect(hamNav.hamNavWrapper, 'validate we are seeing HamNav').toBeVisible({ timeout: 10000 });
          await page.setViewportSize(devices['Desktop Chrome'].viewport);
          await expect(sitewide.meganavWrapper, 'validate we are seeing MegaNav').toBeVisible({ timeout: 10000 });
        });
      }
    );
  });
});
