/* eslint-disable no-console */
// @ts-check
import { expect } from '@playwright/test';
import { waitForHydration, areWeInNext } from '../../utils/helpers';
import test from '../../pom/base.page';
import { squashPopup } from './gap-factory';
import { getBaseURL } from '@/utils/urls';
import { CustomPage } from '@/global';

const brand = process.env.BRAND;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const market = process.env.MARKET;

const megabyteThreshold = 2;

test.describe(`Sitewide`, { tag: '@run-at-us-local' }, () => {
  squashPopup();
  test.describe(`BUNDLE SIZE`, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Check bundle size on home page`, { tag: '@do-not-disable-optimizely' }, async ({ homePage, page }) => {
      await test.step(`Go to the '${brand}'-brand home page`, async () => {
        const customPage = page as CustomPage;
        customPage.currentBundleSize = [];
        customPage.capturedBundleFiles = [];
        page.on('response', async response => {
          if (response.url().includes(getBaseURL()) && response.url().includes('static_content') && response.url().includes('.js')) {
            const headerEntries = Object.entries(response.headers());
            let headerSize = 0;
            for (const [k, v] of headerEntries) {
              headerSize += k.length + (v?.length ?? 0);
            }
            customPage.currentBundleSize.push((await response.body()).byteLength + headerSize);
            customPage.capturedBundleFiles.push({
              url: response.url(),
              size: `${((await response.body()).byteLength + headerSize) / (1024 * 1024)} MB`,
            });
          }
        });

        await homePage.goToHomePage();
        // eslint-disable-next-line playwright/no-nested-step
        await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
          await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        });
        await waitForHydration(page);
        // eslint-disable-next-line playwright/no-wait-for-timeout
        await page.waitForTimeout(15000);

        const resultingBundleSize = (page as CustomPage).currentBundleSize.reduce((accumulator, currentValue) => accumulator + currentValue, 0) / (1024 * 1024);

        console.log('bundle size found:', resultingBundleSize, 'MB');
        console.log(
          'captured bundle js files:',
          (page as CustomPage).capturedBundleFiles.sort((a, b) => a.url.localeCompare(b.url))
        );

        expect(resultingBundleSize, `validate bundle size isn't over ${megabyteThreshold}MB`).not.toBeGreaterThanOrEqual(megabyteThreshold);
      });
    });
  });
});
