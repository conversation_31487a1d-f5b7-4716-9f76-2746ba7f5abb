/* eslint-disable playwright/no-wait-for-timeout */
/* eslint-disable playwright/expect-expect */
// @ts-check
import { Locator, Page, expect } from '@playwright/test';
import { scrollToBottomUntilPageDoesNotGrow, waitForHydration, areWeInNext, scrollElementToCenter, scrollToLocator } from '../../utils/helpers';
import test from '../../pom/base.page';
import { CustomerServicePage } from '../../pom/common/customer-service.page';
import { getCSReturningOrExchangingCid } from '../../test_data/customerServiceOptions';
import { squashPopup } from './gap-factory';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

const checkInfoPageFromFooter = async (
  page: Page,
  link: Locator,
  visibleAssertions: Locator[],
  expandForMobileLocator?: Locator,
  forceClickLink: boolean = false
) => {
  await test.step('Scroll to bottom to force the footer to render', async () => {
    await scrollToLocator(page, expandForMobileLocator ? expandForMobileLocator : link);
  });

  if (expandForMobileLocator) {
    await test.step('Expand section', async () => {
      forceClickLink && (await scrollElementToCenter(expandForMobileLocator));
      await expandForMobileLocator.click({ force: forceClickLink });
    });
  }

  await test.step('Go to footer link', async () => {
    forceClickLink && (await scrollElementToCenter(link));
    await scrollToBottomUntilPageDoesNotGrow(page);
    await link.click({ timeout: 30000, force: forceClickLink });
    await page.waitForLoadState('load');
  });

  await test.step('Verify page loads correctly', async () => {
    const assertions: Promise<void>[] = visibleAssertions.map(locator => expect(locator).toBeVisible({ timeout: 30000 }));
    await Promise.all(assertions);
  });
};

test.describe(`Sitewide`, () => {
  squashPopup();

  test.beforeEach(async ({ homePage, page, sitewide }) => {
    await test.step(`Go to the '${brand}'-brand home page`, async () => {
      await homePage.goToHomePage();
      // eslint-disable-next-line playwright/no-nested-step
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });
      await waitForHydration(page);
    });

    await test.step(`Ensure that Sitewide Components are visible on page.`, async () => {
      await sitewide.checkSiteWideElementsAreOnPage();
    });
  });

  test.describe(`FOOTER`, () => {
    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Customer Service page from footer link.`,
      {
        tag: ['@run-at', '@run-gpf', '@run-on', '@run-brf', '@desktopPr', '@mobilePr'],
      },
      async ({ page, sitewide, customerServicePage }) => {
        await checkInfoPageFromFooter(
          page,
          sitewide.customerServiceLink,
          [brand === 'on' ? customerServicePage.returnOrExchangeLink : customerServicePage.customerServiceHeader, customerServicePage.paymentLink],
          brand === 'gpf' && breakpoint === 'mobile' ? sitewide.expandCustomerService : undefined
        );
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Shipping page by clicking the Shipping link.`,
      { tag: ['@run-at'] },
      async ({ page, sitewide, customerServicePage }) => {
        await checkInfoPageFromFooter(page, sitewide.shippingFooterLink, [
          customerServicePage.customerServiceHeader,
          customerServicePage.shippingAndHandlingHeader,
        ]);
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Returns page by clicking the Free Returns or Order and Returns link.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-on'] },
      async ({ page, sitewide, customerServicePage }) => {
        if (market === 'us' && breakpoint === 'mobile') {
          await test.step('Go to the bottom of the page and verify the Orders & Returns footer link', async () => {
            await scrollToBottomUntilPageDoesNotGrow(page);
            if (/onusmobile/.test(`${brand}${market}${breakpoint}`)) {
              await page.waitForTimeout(5000);
              await page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
              });
            }
            await expect(sitewide.returnsFooterLink).toHaveAttribute('href', /(order-lookup|order-history)/);
          });

          await test.step('Navigate to Returning or Exchanging page', async () => {
            await page.goto(`/customerService/info.do?cid=${getCSReturningOrExchangingCid()}`);
          });

          await test.step('Verify page loaded correctly', async () => {
            await expect(customerServicePage.customerServiceHeader).toBeVisible({ timeout: 30000 });
            await expect(customerServicePage.returningAndExchanging).toBeVisible({
              timeout: 30000,
            });
          });
        } else {
          await checkInfoPageFromFooter(page, sitewide.returnsFooterLink, [
            customerServicePage.customerServiceHeader,
            customerServicePage.returningAndExchanging,
          ]);
        }
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Order Lookup page by clicking the Track Your Order link.`,
      { tag: ['@run-at', '@skip-mobile'] },
      async ({ page, sitewide }) => {
        await test.step(`Check the 'Track Your Order' link from footer`, async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
          await expect(sitewide.trackYourOrderLink).toHaveAttribute('href', /my-account\/order-lookup/);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Gift Cards page by clicking the Gift Cards link.`,
      { tag: ['@run-at', '@run-gp', '@run-gpf', '@run-on', '@skip-mobile'] },
      async ({ page, sitewide }) => {
        await test.step('Scroll to bottom to force the footer to render', async () => {
          await scrollToLocator(page, sitewide.giftCardsFooterLink);
        });

        await test.step('Go to footer link', async () => {
          sitewide.giftCardsFooterLink.click({ timeout: 30000 });
          await page.waitForLoadState('load');
        });

        await test.step('Verify page loads correctly', async () => {
          await page.waitForURL(/info.do/);
          await expect(page).toHaveTitle(/Gift Cards/);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that the mobile Gift Card links are accessible & accurate.`,
      { tag: ['@run-at', '@run-br', '@skip-desktop'] },
      async ({ sitewide, page }) => {
        await test.step(`Expand Gift Cards menu and ensure the 'Gift Card' links have expected values`, async () => {
          await sitewide.expandGiftCardsMobileFooter();
          if (brand === 'br' && market === 'ca') {
            await page.waitForURL(/info.do/);
            await expect(page).toHaveTitle(/Gift/);
          } else {
            await expect(sitewide.buyEGiftCardsLink).toHaveAttribute('href', /(cashstar|https:\/\/www.buyatab.com\/custom\/athleta\/)/);
            market === 'us' && (await expect(sitewide.buyGiftCardsLink).toHaveAttribute('href', /(product.do|info.do)/));
          }
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Size & Fit Customer Service page by clicking the Size & Fit link.`,
      { tag: ['@run-at', '@run-on-us', '@skip-mobile'] },
      async ({ page, sitewide, customerServicePage }) => {
        await checkInfoPageFromFooter(page, sitewide.sizeAndFitGuidesLink, [
          customerServicePage.customerServiceHeader,
          customerServicePage.findingYourSizeHeader,
        ]);
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to The Power of She Collective page by clicking The Power of She Collective link.`,
      { tag: ['@run-at', '@skip-mobile', '@skip-ca'] },
      async ({ page, sitewide }) => {
        await test.step('Scroll to bottom to force the footer to render', async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
        });

        await test.step('Validate the href value', async () => {
          await expect(sitewide.powerOfSheLink).toHaveAttribute('href', /cid=1214438/);
        });
      }
    );

    // FIXME enable for brf when info pages are added
    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Explore Benefits page by clicking the link.`,
      { tag: ['@run-br', '@run-at'] },
      async ({ page, sitewide }) => {
        await test.step('Click Explore Benefits & Verify new page loads correctly', async () => {
          breakpoint === 'mobile' ? await sitewide.expandRewardsMobileFooter() : await scrollToLocator(page, sitewide.exploreBenefits);
          if ((breakpoint === 'desktop' && brand === 'at') || (breakpoint === 'mobile' && brand === 'br')) {
            // eslint-disable-next-line no-param-reassign
            [page] = await Promise.all([page.waitForEvent('popup'), sitewide.exploreBenefits.click()]);
          } else {
            await sitewide.exploreBenefits.click();
          }

          await page.waitForLoadState('load');
          await page.waitForURL(/info.do/);
          await expect(page).toHaveTitle(/Rewards/);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Americans with Disabilities Act page by clicking the link.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-gpf', '@skip-ca', '@run-on'] },
      async ({ page, sitewide, customerServicePage }) => {
        await checkInfoPageFromFooter(page, sitewide.americansDisabilitiesActLink, [customerServicePage.accessibilityTitle], undefined, brand === 'gpf');
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Accessibility for Ontarians with Disabilities Act page by clicking the link.`,
      { tag: ['@run-at', '@run-brf', '@run-gpf', '@skip-us'] },
      async ({ page, sitewide, customerServicePage }) => {
        await checkInfoPageFromFooter(page, sitewide.ontariansDisabilitiesActLink, [customerServicePage.accessibilityTitle]);
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Terms of Use page by clicking the link.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-gpf', '@run-on-us', '@desktopPr', '@mobilePr'] },
      async ({ page, sitewide, termsOfUsePage }) => {
        await checkInfoPageFromFooter(page, sitewide.termsOfUseLink, [termsOfUsePage.termsOfUseTitle], undefined, brand === 'gpf');
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Store Locator page by clicking the Find a Store link.`,
      { tag: ['@run-all', '@skip-br-ca-mobile', '@skip-stage'] },
      async ({ page, sitewide, storeLocatorPage }) => {
        await test.step(`Click the 'Find a Store' link from footer`, async () => {
          await sitewide.clickFindAStoreFooter();
        });

        await test.step(`Land on Store Locator page and can see a list of mapped stores`, async () => {
          await page.waitForLoadState('load');
          await page.waitForURL(/stores/);
          await expect(storeLocatorPage.storesSearchButton).toBeVisible({ timeout: 30000 });
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that the Brand info links are accessible & accurate.`,
      { tag: ['@run-at', '@run-brf', '@run-gpf', '@skip-mobile'] },
      async ({ page, sitewide }) => {
        await test.step(`Ensure the 'Brand info' links have expected values`, async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
          switch (brand) {
            case 'at':
              await expect(sitewide.workAtAthletaLink).toHaveAttribute('href', /(jobs.gapinc|job-search)/);
              await expect(sitewide.sustainabilityLink).toHaveAttribute('href', /info.do|sustainability/);
              break;
            case 'br':
              await expect(sitewide.careers).toHaveAttribute('href', /careers\/banana-republic-careers/);
              await expect(sitewide.downloadOurApp).toHaveAttribute(
                'href',
                /\/\/[^/]+\/browse\/info\.do\?cid=1154071&shortlink=e907381d&c=Ecom_Evergreen_Mobile%20Footer&pid=Ecom&af_channel=Footer/
              );
              break;
            default:
              break;
          }
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that the Rewards links are accessible & accurate.`,
      { tag: ['@run-br', '@run-brf', '@run-gpf', '@run-at'] },
      async ({ page, sitewide }) => {
        await test.step(`Ensure the 'Rewards' links have expected values`, async () => {
          breakpoint === 'mobile' && (await sitewide.expandRewardsMobileFooter());
          await scrollToLocator(page, sitewide.myPointsLink);
          await expect(sitewide.myPointsLink).toHaveAttribute('href', /(loyalty|my-account)/);
          if (market === 'us') {
            await expect(sitewide.payCreditCardLink).toHaveAttribute('href', /payment/);
            brand === 'gpf' || (await expect(sitewide.activateCardLink).toHaveAttribute('href', /activate/));
            await expect(sitewide.applyForCreditCardLink).toHaveAttribute('href', /my-account/);
          }
          await expect(sitewide.joinRewardsLink).toHaveAttribute('href', /my-account/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that the Email Sign-up link is accessible & accurate.`,
      { tag: ['@run-br', '@run-at', '@skip-desktop', '@skip-ca'] },
      async ({ page, sitewide }) => {
        await test.step(`Ensure Email Sign-up link has expected values`, async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
          await expect(sitewide.emailSignUpLink).toHaveAttribute('href', /profile\/info.do/);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Shop other brand links are accessible & accurate.`,
      { tag: ['@run-br', '@run-brf', '@run-gpf', '@run-at', '@skip-desktop'] },
      async ({ page, sitewide }) => {
        await test.step(`Ensure the 'Brand' links in mobile footer have expected values`, async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
          switch (brand) {
            case 'at':
              await sitewide.expandBrandsMobileFooter();
              await expect(sitewide.gapFooterLink).toHaveAttribute('href', /(www.gap.com|www.gapcanada.ca)/);
              await expect(sitewide.onFooterLink).toHaveAttribute('href', /(oldnavy.gap.com|oldnavy.gapcanada.ca)/);
              await expect(sitewide.brFooterLink).toHaveAttribute('href', /(bananarepublic.gap.com|bananarepublic.gapcanada.ca)/);
              break;
            case 'br':
              break;
            default:
              break;
          }
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that the Legal info links are accessible & accurate.`,
      { tag: ['@run-br', '@run-brf', '@run-gpf', '@run-at'] },
      async ({ page, sitewide }) => {
        await test.step(`Ensure the 'Legal info' links have expected values`, async () => {
          await scrollToLocator(page, sitewide.privacyPolicyLink);
          await expect(sitewide.privacyPolicyLink).toHaveAttribute('href', /consumer-privacy-policy/);
          await expect(sitewide.privacyChoicesLink).toBeVisible();
          if (market === 'us') {
            await expect(sitewide.calPrivacyLink).toHaveAttribute('href', /consumer-privacy-policy/);
            await expect(sitewide.aboutGapLink).toHaveAttribute('href', /about/);
            await expect(sitewide.americansDisabilitiesActLink).toHaveAttribute('href', /info.do/);
            await expect(sitewide.caSupplyChainsLink).toHaveAttribute('href', /uk-modern-slavery-act/);
            await expect(sitewide.gapIncPoliciesLink).toHaveAttribute('href', /values/);
          }
          await expect(sitewide.sustainabilityLegalLink).toHaveAttribute('href', /sustainability/);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that the Download Our App link is accessible & accurate.`,
      {
        tag: ['@run-br', '@run-at', '@mobilePr', '@skip-ca', '@skip-desktop'],
      },
      async ({ page, sitewide }) => {
        await test.step(`Ensure the Download Our App link have expected values`, async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
          await expect(sitewide.downloadOurApp).toHaveAttribute('href', /(onelink\.me|info.do)/);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure contact phone number is defined.`,
      { tag: ['@run-br', '@run-brf', '@run-gpf', '@skip-mobile'] },
      async ({ page, sitewide }) => {
        await test.step(`Scroll to bottom & check phone number.`, async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
          expect(sitewide.brPhoneNumber).toBeDefined();
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure Factory Store link is accessible & accurate.`,
      { tag: ['@run-br', '@run-brf', '@skip-mobile', '@skip-brf-ca'] },
      async ({ sitewide, page }) => {
        await test.step(`Scroll to bottom & check Factory Store link.`, async () => {
          await scrollToLocator(page, sitewide.brFactoryStoreLink);
          await expect(sitewide.brFactoryStoreLink).toHaveAttribute('href', /factory.gapfactory.com|.gapfactory.ca|browse\/info\.do/);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that the Stores & Services link is accessible & accurate.`,
      { tag: ['@run-br', '@skip-mobile', '@skip-ca'] },
      async ({ sitewide, page }) => {
        await test.step(`Scroll to bottom & check Stores & Services link.`, async () => {
          await scrollToLocator(page, sitewide.storesAndServices);
          await expect(sitewide.storesAndServices).toHaveAttribute('href', /browse\/info\.do\?cid=/);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Buy Online, Pick Up in Store`,
      { tag: ['@run-on-us', '@skip-at', '@skip-br', '@skip-gp', '@skip-brf', '@skip-gpf', '@skip-mobile'] },
      async ({ customerServicePage, page, sitewide }) => {
        await test.step('Scroll to bottom to force the footer to render', async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
        });

        if (market === 'ca') {
          await test.step(`Click Bopis footer link for market-${market}`, async () => {
            await sitewide.clickBOPISFooter();
            await page.waitForLoadState('load');
          });

          await test.step('Verify CA page loads correctly', async () => {
            const visibleAssertions = [customerServicePage.customerServiceHeader, customerServicePage.buyOnlineHeaderCa];
            const assertions: Promise<void>[] = visibleAssertions.map(locator => expect(locator).toBeVisible({ timeout: 30000 }));
            await Promise.all(assertions);
          });
        } else {
          await test.step(`Click Bopis footer link for market-${market}`, async () => {
            // eslint-disable-next-line no-param-reassign
            page = await sitewide.clickBOPISFooter();
            // eslint-disable-next-line no-param-reassign
            customerServicePage = new CustomerServicePage(page);
            await page.waitForLoadState('load');
          });

          await test.step('Verify US page loads correctly', async () => {
            const visibleAssertions = [
              customerServicePage.customerServiceHeader.or(customerServicePage.bopisImage),
              customerServicePage.buyOnlineHeaderUs.or(customerServicePage.findYourStoreLink),
            ];
            const assertions: Promise<void>[] = visibleAssertions.map(locator => expect(locator).toBeVisible({ timeout: 30000 }));
            await Promise.all(assertions);
          });
        }
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Our Affiliate Program page from the footer link.`,
      { tag: ['@run-on-us', '@skip-mobile'] },
      async ({ page, sitewide, utilityPage }) => {
        await checkInfoPageFromFooter(page, sitewide.ourAffiliateProgram, [utilityPage.ourAffiliateProgramHeader]);
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to Super Cash page from the footer link.`,
      { tag: ['@skip-mobile', '@run-on'] },
      async ({ page, sitewide, utilityPage }) => {
        await checkInfoPageFromFooter(page, sitewide.superCashFooter, [utilityPage.superCashTerms]);
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that the Atelier Styling Service link is accessible & accurate.`,
      { tag: ['@run-br', '@skip-mobile', '@skip-ca'] },
      async ({ sitewide, page }) => {
        await test.step(`Scroll to bottom & check Atelier Styling Service link.`, async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
          await expect(await sitewide.atelierStylingService).toHaveAttribute('href', /browse\/info\.do\?cid/);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that the Baby Stores link is accessible & accurate.`,
      { tag: ['@skip-mobile', '@skip-ca'] },
      async ({ sitewide, page }) => {
        await test.step(`Scroll to bottom & check Baby Stores link.`, async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
          await expect(await sitewide.babyStores).toHaveAttribute('href', /browse\/info\.do\?cid=/);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that the Art Stores link is accessible & accurate.`,
      { tag: ['@skip-mobile', '@skip-ca'] },
      async ({ sitewide, page }) => {
        await test.step(`Scroll to bottom & check Art Stores link.`, async () => {
          await scrollToBottomUntilPageDoesNotGrow(page);
          await expect(await sitewide.artStores).toHaveAttribute('href', /browse\/info\.do\?cid=/);
        });
      }
    );
  });

  test.describe(`FRENCH CONTENT`, { tag: ['@run-br', '@run-brf', '@run-gp', '@skip-us', '@skip-mobile'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Check French language loads correctly.`, async ({ sitewide, homePage, page }) => {
      await test.step('Go to French Customer Service page by clicking footer link', async () => {
        await scrollToLocator(page, sitewide.footerLocaleLink);
        await sitewide.footerLocaleLink.click();
      });

      // eslint-disable-next-line playwright/no-skipped-test
      test.skip(process.env.START_SERVER === 'false', 'Skip this test at local env.');

      await test.step('Ensure that the page has loaded with French language in place.', async () => {
        await page.waitForLoadState('load');
        await expect(page).toHaveURL(/locale=fr_CA/);
        await page.context().clearCookies({ name: 'locale' });
        await page.evaluate('document.cookie="locale=fr_CA|||; path=/"');
        await page.reload();
        await waitForHydration(page);
        await expect(homePage.frenchLangIndicator).toBeVisible();
      });
    });
  });
});
