// @ts-check
import test from '../../pom/base.page';

export const squashPopup = () => {
  if (process.env.BRAND === 'gpf') {
    test.beforeEach(async ({ page, sitewide }) => {
      await page.addLocatorHandler(sitewide.gapFactoryPopup, async () => {
        await sitewide.gapFactoryPopupClose.click();
        page.locator('iframe[title="Sign Up via Text for Offers"]').contentFrame().getByTestId('closeIcon');
      });
    });
  }
};
