// @ts-check
import { expect } from '@playwright/test';
import test from '../../pom/base.page';
import { areWeInNext, waitForHydration } from '../../utils/helpers';
import { squashPopup } from './gap-factory';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const searchText = 'shirts';

test.describe(`Sitewide`, () => {
  squashPopup();

  test.beforeEach(async ({ homePage, hamNav, page }) => {
    await test.step(`Go to the '${brand}'-brand home page`, async () => {
      await homePage.goToHomePage();
      // eslint-disable-next-line playwright/no-nested-step
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });
      await page.waitForLoadState('load');
      await waitForHydration(page);
    });

    await test.step(`Click the HamNav menu to open navigation.`, async () => {
      await hamNav.hamNavOpen.click();
    });
  });

  test.describe(`HAM-NAV`, { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-gpf', '@run-on', '@skip-desktop'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Navigate to category page. `, { tag: ['@skip-brf'] }, async ({ hamNav, page }) => {
      await test.step(`Select a Division.`, async () => {
        await hamNav.divisionLink.click();
      });

      await test.step(`Click a Category.`, async () => {
        await hamNav.categoryLink.click();
      });

      await test.step('Land on the Category page.', async () => {
        await page.waitForLoadState('load');
        await expect(page).toHaveURL(/cid=/);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Enter search text then click search icon.`, { tag: ['@mobilePr'] }, async ({ hamNav, page }) => {
      await test.step(`Open search field inside HamNav.`, async () => {
        await hamNav.accessSearchInput.click();
      });

      await test.step(`Enter Search text and click search submit. @skip-desktop`, async () => {
        await hamNav.searchFor(searchText);
      });

      await test.step('Land on the Search page.', async () => {
        await page.waitForLoadState('load');
        await expect(page).toHaveURL(/browse\/search.do/);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Click sign-in to go to sign-in page.`, { tag: ['@mobilePr'] }, async ({ hamNav, page }) => {
      if (env === 'local') {
        await test.step(`Ensure the Sign-in link has expected values.`, async () => {
          await expect(hamNav.signInOrJoin).toHaveAttribute('href', /my-account/);
        });
      } else {
        await test.step(`Click Sign-In or Join Button.`, async () => {
          await hamNav.signInOrJoin.click();
        });

        await test.step('Land on the Sign In page.', async () => {
          await page.waitForLoadState('load');
          await page.waitForURL(/sign-in/);
          await expect(page).toHaveTitle(/Sign in.*/);
        });
      }
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Ensure that the Customer Service footer links are accessible & accurate.`, async ({ hamNav }) => {
      if (brand === 'at') {
        await test.step(`Expand Gift Cards menu and ensure the 'Customer Service' links have expected values.`, async () => {
          await hamNav.expandAtCustomerServiceLinks();
          await expect(hamNav.ordersReturns).toHaveAttribute('href', /my-account/);
          await expect(hamNav.shippingDelivery).toHaveAttribute('href', /info.do/);
        });
      } else {
        await test.step(`Check Customer Service footer link is correct.`, async () => {
          await expect(hamNav.customerServiceLink).toHaveAttribute('href', /customerService/);
        });
      }
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Ensure that non-expandable footer links are accessible & accurate.`, async ({ hamNav }) => {
      await test.step(`Ensure footer links have expected values`, async () => {
        await expect(hamNav.storeLocator).toHaveAttribute('href', /stores/);
        await expect(hamNav.communicationPreferences).toHaveAttribute('href', /communication-preferences/);
        await expect(hamNav.signInFooter).toHaveAttribute('href', /sign-in/);
      });
    });

    test(
      `${brand}-${market}-${breakpoint}-${env}: Ensure that the Gift Card footer links are accessible & accurate.`,
      { tag: ['@skip-brf'] },
      async ({ hamNav }) => {
        await test.step(`Expand Gift Cards menu and ensure the 'Gift Card' links have expected values`, async () => {
          await hamNav.expandGiftCardLinks();
          await expect(hamNav.buyEGiftCardsLink).toHaveAttribute('href', /(cashstar|buyatab)/);
          await expect(hamNav.buyGiftCardsLink).toHaveAttribute('href', /(product.do|info.do)/);
        });
      }
    );

    test(`${brand}-${market}-${breakpoint}-${env}: Ensure that the Rewards footer links are accessible & accurate.`, async ({ hamNav }) => {
      await test.step(`Expand Rewards menu and ensure the 'Rewards' links have expected values`, async () => {
        await hamNav.expandRewardsLinks();
        await expect(hamNav.myPointsRewards).toHaveAttribute('href', /my-account/);
        await expect(hamNav.exploreBenefits).toHaveAttribute('href', /(Loyalty|Benefits|info)/);
        if (market === 'us') {
          await expect(hamNav.payCreditCardBill).toHaveAttribute('href', /(login|payment)/);
          await expect(hamNav.activateCreditCard).toHaveAttribute('href', /activate/);
        }
        await expect(hamNav.joinRewards).toHaveAttribute('href', /my-account/);
      });
    });
  });
});
