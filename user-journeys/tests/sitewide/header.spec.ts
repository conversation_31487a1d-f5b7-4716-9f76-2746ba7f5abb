// @ts-check
import { expect } from '@playwright/test';
import { areWeInNext, deleteChartisHeaderFromRoute, waitForHydration } from '../../utils/helpers';
import test from '../../pom/base.page';
import { squashPopup } from './gap-factory';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const searchText = 'shirts';

test.describe(`Sitewide`, () => {
  squashPopup();

  test.beforeEach(async ({ homePage, page }) => {
    await test.step(`Go to the '${brand}'-brand home page`, async () => {
      await homePage.goToHomePage();
      // eslint-disable-next-line playwright/no-nested-step
      await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });
      await page.waitForLoadState('load');
      await waitForHydration(page);
    });
  });

  test.describe(`TOP-NAV`, { tag: ['@run-at', '@run-br', '@run-gp', '@run-on'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Ensure Sister Links are visible.`, { tag: ['@desktopPr', '@mobilePr'] }, async ({ sitewide }) => {
      await test.step(`Gap Sister Link is visible.`, async () => {
        const gapSisterLink = await sitewide.getSisterLink('gap');
        await expect(gapSisterLink).toBeInViewport();
      });

      await test.step(`Old Navy Sister Link is visible.`, async () => {
        const gapSisterLink = await sitewide.getSisterLink('on');
        await expect(gapSisterLink).toBeInViewport();
      });

      await test.step(`Banana Republic Sister Link is visible.`, async () => {
        const gapSisterLink = await sitewide.getSisterLink('br');
        await expect(gapSisterLink).toBeInViewport();
      });

      await test.step(`Athleta Sister Link is visible.`, async () => {
        const gapSisterLink = await sitewide.getSisterLink('at');
        await expect(gapSisterLink).toBeInViewport();
      });
    });
  });

  test.describe(`EDFS`, () => {
    // Skipping all EDFS banner right now due to frequent holiday changes. It can likely be restored after Jan '25
    test(`${brand}-${market}-${breakpoint}-${env}: Ensure that the SIGN-IN or JOIN link is accessible & accurate.`, async ({ sitewide }) => {
      await test.step(`EDFS Sign-in link is clickable with correct href.`, async () => {
        await expect(sitewide.edfsSignInJoinLink).toHaveAttribute('href', /my-account/, {
          timeout: 30000,
        });
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Ensure that the DETAILS link is accessible & accurate`, async ({ sitewide }) => {
      await test.step(`EDFS DETAILS link is visible with correct href.`, async () => {
        await expect(sitewide.edfsDetailsLink).toBeVisible({ timeout: 30000 });
      });
    });
  });

  test.describe(`ACCOUNT DROPDOWN`, { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-gpf', '@run-on', '@skip-mobile'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Sign-In, Rewards, Orders & Returns & Gift Card links are accessible & accurate. `, async ({ sitewide }) => {
      await test.step(`Expand Sign-In/Your Account dropdown.`, async () => {
        await sitewide.signInYourAccount.click();
      });

      await test.step(`Verify that SIGN-IN button is accessible & accurate.`, async () => {
        await expect(sitewide.signInButton).toHaveAttribute('href', /my-account/);
      });

      await test.step(`Verify Join Rewards link is accessible & accurate.`, async () => {
        await expect(sitewide.joinRewardsDropDownLink).toHaveAttribute('href', /my-account/);
      });

      await test.step(`Ensure Orders & Returns link is accessible & accurate.`, async () => {
        await expect(sitewide.orderReturnsDropDownLink).toHaveAttribute('href', /order-history/);
      });

      await test.step(`Ensure My Rewards link is accessible & accurate.`, async () => {
        await expect(sitewide.pointsDropDownLink).toHaveAttribute('href', /value-center/);
      });

      await test.step(`Ensure Gift Card Balance link is accessible & accurate.`, async () => {
        await expect(sitewide.giftCardDropDownLink).toHaveAttribute('href', /info.do/);
      });
    });
  });

  test.describe(`BAG`, { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-gpf', '@run-on', '@skip-mobile'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Go to Shopping Bag page by clicking the Bag icon.`, async ({ sitewide, page, shoppingBagPage }) => {
      await test.step(`Click the Shopping bag icon in the top nav.`, async () => {
        await sitewide.clickBagIcon();
      });

      // eslint-disable-next-line playwright/no-skipped-test
      test.skip(process.env.START_SERVER === 'false', 'Skip this test');

      await test.step(`Land on Shopping bag page with zero items in your bag`, async () => {
        await page.waitForLoadState('load');
        await page.waitForURL(/shopping-bag/);
        await page.getByText(/Sign in to see any saved items/i).waitFor({ timeout: 30000 });
        expect(await shoppingBagPage.emptyBagHeaderText()).toContain(`Your bag is currently empty`);
      });
    });
  });

  test.describe(`UTILITY LINKS`, { tag: ['@run-on', '@skip-mobile', '@skip-stage'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Go to Store Locator page by clicking the Find a Store link.`, async ({ sitewide, page }) => {
      await test.step(`Click the Find a Store link from the Utility Links.`, async () => {
        await sitewide.clickFindAStoreLink();
      });

      await test.step(`Land on Store Locator page and can see a list of mapped stores`, async () => {
        await page.waitForLoadState('load');
        await expect(page).toHaveURL(/(info.do|stores)/);
      });
    });

    test(
      `${brand}-${market}-${breakpoint}-${env}: Go to Gift Card page by clicking the Gift Card link.`,
      { tag: '@run-at' },
      async ({ sitewide, page, giftCardPage }) => {
        await test.step(`Click the Gift Card link from the Utility Links.`, async () => {
          await sitewide.clickGiftCardLink();
        });

        await test.step(`Land on Gift Card page and are able to check gift card balance`, async () => {
          await page.waitForLoadState('load');
          await page.waitForURL(/info.do/);
          await expect(giftCardPage.checkBalanceLink).toBeVisible();
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Go to Rewards page by clicking the Rewards utility link.`,
      { tag: '@run-at' },
      async ({ sitewide, page, utilityPage }) => {
        await test.step(`Click the Rewards link from the Utility Links.`, async () => {
          await sitewide.rewardsUtilityLink.click();
        });

        await test.step(`Land on Rewards page and are able to see the rewards image`, async () => {
          await page.waitForLoadState('load');
          await page.waitForURL(/info.do/);
          await expect(utilityPage.mainContent).toContainText(/(Reward)/i);
        });
      }
    );
  });

  test.describe(`SEARCH`, { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-gpf', '@run-on'] }, () => {
    test.beforeEach(async ({ page }) => {
      await deleteChartisHeaderFromRoute(page, 'api.gap.com/commerce/search/products/v2');
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Enter Search keyword and press the ENTER key.`, { tag: ['@skip-mobile'] }, async ({ sitewide, page }) => {
      await test.step(`I enter search text as '${searchText}' and press the ENTER key.`, async () => {
        // Adding a 3 second delay to allow for hydration as there doesn't seem a better way to validate hyrdation here
        // eslint-disable-next-line playwright/no-wait-for-timeout
        await page.waitForTimeout(3000);
        await sitewide.clickSearchBox();
        /atus/.test(`${brand}${market}`) && (await sitewide.searchSuggestions.waitFor({ state: 'visible' }));
        await sitewide.fillSearch(searchText);
        await page.keyboard.press('Enter');
      });

      await test.step(`Search results contain '${searchText}'`, async () => {
        await expect(page).toHaveURL(/browse\/search.do\?searchText=/);
      });
    });

    test(
      `${brand}-${market}-${breakpoint}-${env}: Enter mobile search text then click search icon.`,
      { tag: ['@skip-desktop'] },
      async ({ sitewide, page }) => {
        await test.step(`Open, search input, enter Search text and click search submit.`, async () => {
          await waitForHydration(page);
          await sitewide.searchFor(searchText);
        });

        await test.step('Land on the Search page.', async () => {
          await page.waitForLoadState('load');
          await expect(page).toHaveURL(/browse\/search.do/);
        });
      }
    );

    test(`${brand}-${market}-${breakpoint}-${env}: Enter mobile search text then dismiss search.`, { tag: ['@skip-desktop'] }, async ({ sitewide, page }) => {
      await test.step(`Open, search input, enter Search text and click search submit.`, async () => {
        await waitForHydration(page);
        await sitewide.enterSearchAndDismiss(searchText);
      });

      await test.step('Land on the Search page.', async () => {
        await expect(sitewide.dismissSearch).toBeHidden();
      });
    });
  });

  test.describe(`MEGA-NAV`, { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-gpf', '@run-on', '@skip-mobile'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Go to a Division page from Home page using a Division link.`, async ({ page, sitewide }) => {
      await test.step(`Click a division link from the TopNav to go to a division page.`, async () => {
        await sitewide.selectDivision();
      });

      if (brand === 'br' && market === 'ca') {
        await test.step(`Wait for page to load Category page.`, async () => {
          await page.waitForURL(/cid=/);
        });

        await test.step('Land on the Category page and ensure main content on page is loaded.', async () => {
          await expect(page).toHaveURL(/cid=/);
        });
      } else {
        await test.step(`Wait for page to load Division page.`, async () => {
          await expect(page).toHaveURL(/cid=/);
        });

        await test.step('Land on the Division page and ensure main content on page is loaded.', async () => {
          // expect(await getPageType(page)).toMatch(/division/);
        });
      }
    });

    test(
      `${brand}-${market}-${breakpoint}-${env}: Go to a Category page from Home page using the MegaNav flyout.`,
      { tag: ['@desktopPr'] },
      async ({ page, sitewide }) => {
        await test.step(`Hover over a Division and click a category from MegaNav`, async () => {
          await sitewide.hoverDivision();
          await sitewide.selectCategory();
        });

        await test.step('Land on the Category page and view products in the product grid', async () => {
          await page.waitForLoadState('load');
          await expect(page).toHaveURL(/cid=/);
        });
      }
    );
  });
});
