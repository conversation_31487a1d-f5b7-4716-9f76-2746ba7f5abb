import { expect } from '@playwright/test';
import { areWeInNext, scrollElementToCenter } from '../../utils/helpers';
import test from '../../pom/base.page';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const searchText = market === 'ca' ? 'Toronto' : '10012';

test.describe(`Store Locator > LANDING PAGE`, { tag: ['@run-all', '@skip-stage'] }, () => {
  test.beforeEach(async ({ storeLocatorPage, page, sitewide }) => {
    await test.step(`Go to the ${brand}-brand Store Locator page`, async () => {
      await storeLocatorPage.goToStoreLocatorLandingPage(brand, market);
    });
    await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
      await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
    });

    await test.step(`Ensure that Sitewide Components are visible on page.`, async () => {
      await sitewide.checkSiteWideElementsAreOnPage();
    });
  });

  test(`${brand}-${market}-${breakpoint}-${env}: Enter Search keyword and click the search Icon`, async ({ page, storeLocatorPage }) => {
    await test.step(`Enter search text as '${searchText}' and click the search icon.`, async () => {
      await storeLocatorPage.clickSearchBox();
      await storeLocatorPage.fillSearch(searchText);
    });

    await test.step(`Validate that search result items are visible`, async () => {
      await storeLocatorPage.searchFormResults.isVisible();
    });

    await test.step(`Click the search icon`, async () => {
      await storeLocatorPage.clickSearchIcon();
    });

    await test.step(`Search results contain '${searchText}'`, async () => {
      await expect(page).toHaveURL(new RegExp(`q=${searchText}`));
    });
  });

  test(`${brand}-${market}-${breakpoint}-${env}: Navigate to Store details page and validate it renders correctly`, async ({ storeLocatorPage }) => {
    await test.step(`Click on store details link`, async () => {
      await storeLocatorPage.navigateToStoreDetails();
    });

    await test.step(`Validate that header, page title, breadcrumbs, location section, google map, nearby stores and location description are visible`, async () => {
      await expect(storeLocatorPage.header).toBeVisible();
      await expect(storeLocatorPage.storeDetailsTitle).toBeVisible();
      breakpoint === 'desktop' && (await expect(storeLocatorPage.breadcrumbs).toBeVisible());
      await expect(storeLocatorPage.storeHours).toBeVisible();
      await expect(storeLocatorPage.getDirectionsLink).toBeVisible();
      breakpoint === 'desktop' && (await expect(storeLocatorPage.gmap).toBeVisible());
      await expect(storeLocatorPage.nearbyStoresTitle).toBeVisible();
      breakpoint === 'desktop' && market !== 'ca' && (await expect(storeLocatorPage.nearbyStoresContent).toBeVisible());
      await expect(storeLocatorPage.locationDescription).toBeVisible();
    });
  });

  test(
    `${brand}-${market}-${breakpoint}-${env}: Navigate to get directions page`,
    {
      tag: ['@skip-ca'],
    },
    async ({ page, storeLocatorPage, context }) => {
      let newPage = page;
      await test.step(`Click on get directions link`, async () => {
        const newPagePromise = context.waitForEvent('page');
        breakpoint === 'mobile' && (await scrollElementToCenter(storeLocatorPage.getDirectionsLink));
        await storeLocatorPage.getDirectionsLink.click({ force: breakpoint === 'mobile' });
        newPage = await newPagePromise;
      });

      await test.step(`Should redirect to google maps`, async () => {
        await page.waitForLoadState('load');
        await expect(newPage).toHaveURL(/maps/);
      });
    }
  );

  test(`${brand}-${market}-${breakpoint}-${env}: Validate shop opening hours`, async ({ storeLocatorPage }) => {
    await test.step(`Expand shop opening hours`, async () => {
      breakpoint === 'mobile' && (await scrollElementToCenter(storeLocatorPage.hoursStatus));
      await storeLocatorPage.hoursStatus.click({ force: breakpoint === 'mobile' });
    });

    await test.step(`Shop opening hours should be visible`, async () => {
      await expect(storeLocatorPage.hoursDropdown).toBeVisible();
    });
  });

  test(`${brand}-${market}-${breakpoint}-${env}: Validate browse stores by state`, async ({ storeLocatorPage }) => {
    await test.step(`Click on First State in browser by state section`, async () => {
      breakpoint === 'mobile' && (await storeLocatorPage.expandNearbyStoresSection());
      await storeLocatorPage.clickBrowseByStateLink();
    });

    await test.step(`Validate that google map is visible`, async () => {
      breakpoint === 'desktop' && (await expect(storeLocatorPage.gmap).toBeVisible());
    });

    await test.step(`Click on first location`, async () => {
      await storeLocatorPage.mapListItem.click();
    });

    await test.step(`Validate that header, breadcrumbs, search functionality, google map and location description are visible`, async () => {
      await expect(storeLocatorPage.header).toBeVisible();
      breakpoint === 'desktop' && (await expect(storeLocatorPage.breadcrumbs).toBeVisible());
      await expect(storeLocatorPage.findStoreTitle).toBeVisible();
      await expect(storeLocatorPage.storesSearchButton).toBeVisible();
      await expect(storeLocatorPage.mapSearchForm).toBeVisible();
      await expect(storeLocatorPage.mapList).toBeVisible();
      breakpoint === 'desktop' && (await expect(storeLocatorPage.gmap).toBeVisible());
      market === 'us' && (await expect(storeLocatorPage.locationDescription).toBeVisible());
    });
  });

  test(`${brand}-${market}-${breakpoint}-${env}: Filter stores`, async ({ storeLocatorPage, page }) => {
    await test.step(`Filter by first option`, async () => {
      await storeLocatorPage.filterByFirstOption();
    });

    await test.step(`Should display applied filter message`, async () => {
      await expect(storeLocatorPage.appliedFilterMsg).toHaveText(/Filters .1.|1 Filter Applied/);
    });

    await test.step(`Clear All filters`, async () => {
      await storeLocatorPage.clearAllFilters.click();
    });

    await test.step(`Should remove display applied filter message`, async () => {
      await expect(page.getByText(/Filters .1.|1 Filter Applied/)).toBeHidden();
    });
  });
});

test.describe(`Store Locator > CITY`, { tag: ['@run-all', '@skip-stage'] }, () => {
  test.beforeEach(async ({ storeLocatorPage, page }) => {
    await test.step(`Go to the ${brand}-brand Store Locator page`, async () => {
      await storeLocatorPage.goToStoreLocatorCityPage(brand, market);
    });
    await test.step(`Are we in Next? ${await areWeInNext(page)}`, async () => {
      await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
    });
  });

  test(`${brand}-${market}-${breakpoint}-${env}: Ensure Store Locator page rendered successfully`, async ({ storeLocatorPage }) => {
    await test.step(`Validate that header is visible`, async () => {
      await expect(storeLocatorPage.header).toBeVisible();
    });

    await test.step(`Validate that breadcrumbs, search functionality, google map, location description are visible`, async () => {
      breakpoint === 'desktop' && (await expect(storeLocatorPage.breadcrumbs).toBeVisible());
      await expect(storeLocatorPage.findStoreTitle).toBeVisible();
      await expect(storeLocatorPage.storesSearchButton).toBeVisible();
      await expect(storeLocatorPage.mapSearchForm).toBeVisible();
      await expect(storeLocatorPage.mapList).toBeVisible();
      await expect(storeLocatorPage.hoursStatus).toBeVisible();
      await expect(storeLocatorPage.viewStoreDetailsLink).toBeVisible();
      await expect(storeLocatorPage.getDirectionsLink).toBeVisible();
      breakpoint === 'desktop' && (await expect(storeLocatorPage.gmap).toBeVisible());
      await expect(storeLocatorPage.locationDescription).toBeVisible();
    });
  });

  test(`${brand}-${market}-${breakpoint}-${env}: Verify breadcrumb links`, { tag: ['@skip-mobile'] }, async ({ page, storeLocatorPage }) => {
    await test.step(`Click on breadcrumb link`, async () => {
      await storeLocatorPage.clickFirstBreadcrumbLink();
    });

    await test.step(`Validate page loaded successfully`, async () => {
      await storeLocatorPage.header.isVisible();
      await storeLocatorPage.gmap.isVisible();
      await storeLocatorPage.searchFormResults.isVisible();
      await storeLocatorPage.mapList.isVisible();
      await storeLocatorPage.locationDescription.isVisible();
    });

    await test.step(`Search results contain breadcrumbs query param`, async () => {
      await expect(page).toHaveURL(/bc=true/);
    });
  });
});
