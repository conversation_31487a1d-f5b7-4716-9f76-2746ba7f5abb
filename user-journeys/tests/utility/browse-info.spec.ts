import { expect } from '@playwright/test';
import test from '../../pom/base.page';
import { areWeInNext, useDefaultUserAgent, waitForHydration } from '../../utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`Utility`, () => {
  test.describe(`BROWSE-INFO`, { tag: '@run-gp' }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Validate Linen Moves Page`, { tag: '@skip-ca' }, async ({ page, utilityPage }) => {
      await test.step('Go to Linen Moves page', async () => {
        await page.goto('browse/info.do?cid=3029054');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });

      await test.step('Check page rendered', async () => {
        await Promise.all([expect(utilityPage.linenMovesCards.first()).toBeVisible(), expect(utilityPage.linenMovesTitle).toHaveText(/Linen moves\./)]);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Validate Coupons and Promo Codes Page`, async ({ page, utilityPage }) => {
      await test.step('Go to Coupons and Promo Codes page', async () => {
        await page.goto('browse/info.do?cid=1114393');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });

      await test.step('Check page rendered', async () => {
        await Promise.all([
          expect(utilityPage.couponContainer).toBeVisible(),
          expect(utilityPage.couponsAndPromoCodesHeader).toHaveText(/Offers,\s*Promotions\s*and\s*Coupons/),
        ]);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Validate Gap for Good Page`, async ({ page, utilityPage }) => {
      await test.step('Go to Gap for Good page', async () => {
        await page.goto('browse/info.do?cid=1086537');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });

      await test.step('Check page rendered', async () => {
        await Promise.all([
          expect(utilityPage.gapForGoodLogo).toBeVisible(),
          expect(utilityPage.gapForGoodIntro).toHaveText(/Responsibly made\s*for every generation\./),
        ]);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Validate Holiday Lookbook Page`, async ({ page, utilityPage }) => {
      await test.step('Go to Holiday Lookbook page', async () => {
        await page.goto('browse/info.do?cid=3025753');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });

      await test.step('Check page rendered', async () => {
        await Promise.all([expect(utilityPage.giftedLookbookImg).toBeVisible(), expect(utilityPage.holidayLookbookCard.first()).toBeVisible()]);
      });
    });

    test(
      `${brand}-${market}-${breakpoint}-${env}: Validate Get SMS Text Sign-Up Page`,
      { tag: ['@skip-ca', '@allowattntv'] },
      async ({ page, utilityPage }) => {
        await useDefaultUserAgent(page, 'gap.attn.tv');

        await test.step(`Go to Get SMS Text Sign-Up page`, async () => {
          await page.goto('browse/info.do?cid=1195815');
          await waitForHydration(page);
        });

        await test.step("Check if we're in NextJS", async () => {
          await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
        });

        await test.step('Check page rendered', async () => {
          await Promise.all([expect(utilityPage.smsSignUpHeader).toHaveText(/When You Sign Up For Text/i)]);
        });
      }
    );

    test(`${brand}-${market}-${breakpoint}-${env}: Validate Gap Equality and Belonging Page`, async ({ page, utilityPage }) => {
      await test.step(`Go to Gap Equality and Belonging page`, async () => {
        await page.goto('browse/info.do?cid=1179886');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });

      await test.step('Check page rendered', async () => {
        await Promise.all([expect(utilityPage.equalityAndBelongingHeading).toBeVisible(), expect(utilityPage.equalityPartnerList).toBeVisible()]);
      });
    });
  });

  test.describe(`BROWSE-INFO`, { tag: ['@run-at', '@skip-ca'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Validate This Way to Athleta Preloved Page`, async ({ page }) => {
      await test.step('Go to This Way to Athleta Preloved page', async () => {
        const response = await page.request.get('browse/info.do?cid=1197664');
        await expect(response).toBeOK();
        await expect(async () => {
          expect((await response.body()).length).toBeGreaterThan(100 * 1024);
        }).toPass(); // 100 KB
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Validate Athleta Rewards - Shop & Earn! Page`, async ({ page, utilityPage }) => {
      await test.step('Go to Athleta Rewards - Shop & Earn! page', async () => {
        await page.goto('browse/info.do?cid=1098761');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });

      await test.step('Check page rendered', async () => {
        await expect(utilityPage.rewardsBody).toHaveText(/[a-z]{2,}/i);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Validate Athleta Tights & Leggings Quiz Page`, async ({ page, utilityPage }) => {
      await test.step('Go to Athleta Tights & Leggings Quiz page', async () => {
        await page.goto('browse/info.do?cid=3022466');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });

      await test.step('Check page rendered', async () => {
        await Promise.all([expect(utilityPage.atLeggingsQuiz).toBeVisible(), expect(utilityPage.atLeggingsQuizButton).toBeVisible()]);
      });
    });
  });

  test.describe(`BROWSE-INFO`, { tag: '@run-br' }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Validate Size Charts Page`, { tag: '@skip-ca' }, async ({ page, utilityPage }) => {
      await test.step('Go to Size Charts page', async () => {
        await page.goto('browse/info.do?cid=35404');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });
      await test.step('Check page rendered', async () => {
        await Promise.all([expect(utilityPage.brSizeChartsHeading).toBeVisible(), expect(utilityPage.brMensSizeChart).toBeVisible()]);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Validate blank Page`, { tag: '@skip-ca' }, async ({ page, utilityPage }) => {
      await test.step(`Go to blank page`, async () => {
        await page.goto('browse/info.do?cid=1093750');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });
      await test.step('Check page rendered', async () => {
        await Promise.all([expect(utilityPage.brBlankRegion).toBeVisible()]);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Validate Personal Styling Page`, { tag: '@skip-ca' }, async ({ page, utilityPage }) => {
      await test.step('Go to Personal Styling page', async () => {
        await page.goto('browse/info.do?cid=1194368');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });
      await test.step('Check page rendered', async () => {
        await expect(utilityPage.brPersonalStylingContent).toBeVisible();
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Validate Conscious Luxury Page`, { tag: '@skip-ca' }, async ({ page, utilityPage }) => {
      await test.step('Go to Conscious Luxury page', async () => {
        await page.goto('browse/info.do?cid=1131224');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });
      await test.step('Check page rendered', async () => {
        await Promise.all([expect(utilityPage.brConsciousLuxuryImage).toBeVisible(), expect(utilityPage.brTradeWithThredUp).toBeVisible()]);
      });
    });
  });

  test.describe(`BROWSE-INFO`, { tag: '@run-on' }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Validate Denim Fit Guide page`, async ({ page, utilityPage }) => {
      await test.step('Go to Denim Fit Guide page', async () => {
        await page.goto('browse/info.do?cid=1185579');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });
      await test.step('Check page rendered', async () => {
        await Promise.all([expect(utilityPage.denimFitHeader).toBeVisible(), expect(utilityPage.denimFitCompare).toBeVisible()]);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Validate Navyist Rewards Page`, async ({ page, utilityPage }) => {
      await test.step('Go to Navyist Rewards page', async () => {
        await page.goto('browse/info.do?cid=1095422');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await page.waitForLoadState('load');
        await page.waitForURL(/info.do/);
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });
      await test.step('Check page rendered', async () => {
        await expect(utilityPage.mainContent).toContainText(/(Reward)/i);
      });
    });

    test(`${brand}-${market}-${breakpoint}-${env}: Validate Earn Super Cash In Stores & Online Page`, async ({ page, utilityPage }) => {
      await test.step(`Go to Earn Super Cash In Stores & Online page`, async () => {
        await page.goto('browse/info.do?cid=56526');
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });

      await test.step('Check page rendered', async () => {
        await expect(utilityPage.mainContent).toContainText(/Super Cash/i);
      });
    });
  });

  test.describe(`BROWSE-INFO`, () => {
    test(
      `${brand}-${market}-${breakpoint}-${env}: Validate Coupons & Promo Codes Page`,
      { tag: ['@run-br', '@run-on', '@skip-ca'] },
      async ({ page, utilityPage }) => {
        await test.step('Go to Coupons & Promo Codes page', async () => {
          await page.goto(`browse/info.do?cid=${utilityPage.getCouponAndPromoCodeCID()}`);
          await waitForHydration(page);
        });

        await test.step("Check if we're in NextJS", async () => {
          await expect(async () => {
            expect(await areWeInNext(page)).toBeTruthy();
          }).toPass();
        });
        await test.step('Check page rendered', async () => {
          await Promise.all([expect(utilityPage.onlineCouponsHeading).toBeVisible(), expect(utilityPage.onlineAndIn).toBeVisible()]);
        });
      }
    );

    test(`${brand}-${market}-${breakpoint}-${env}: Validate Gift Cards Page`, { tag: ['@run-at', '@run-on', '@skip-ca'] }, async ({ page, utilityPage }) => {
      await test.step('Go to Gift Cards page', async () => {
        await page.goto(`browse/info.do?cid=${utilityPage.getGiftCardsCID()}`);
        await waitForHydration(page);
      });

      await test.step("Check if we're in NextJS", async () => {
        await expect.poll(async () => await areWeInNext(page)).toBeTruthy();
      });

      await test.step('Check page rendered', async () => {
        await Promise.all([expect(utilityPage.giftCardBenefits).toBeVisible(), expect(utilityPage.giftCardBalance).toBeVisible()]);
      });
    });
  });
});
