/* eslint-disable playwright/no-force-option */
import { Page, expect } from '@playwright/test';
import { Market } from '@ecom-next/utils/server';
import { setTestOptions } from '../../test_data/customerServiceOptions';
import { CustomerServicePage } from '../../pom/common/customer-service.page';
import { scrollDownByN, scrollElementToCenter, scrollToBottomUntilPageDoesNotGrow, scrollToTop, waitForHydration } from '../../utils/helpers';
import { HomePage } from '../../pom/common/home.page';

const test = setTestOptions();
const brand = process.env.BRAND;
const market = process.env.MARKET as Market;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

async function verifyPageLoadsFully(homePage: HomePage, page: Page) {
  await test.step(`Verify header and footer renders correctly`, async () => {
    await scrollToTop(page);
    await expect(homePage.header).toBeVisible({ timeout: 30000 });
    await scrollToBottomUntilPageDoesNotGrow(page);
    await expect(homePage.footer).toBeVisible({ timeout: 30000 });
  });
}

test.describe(`Utility`, () => {
  test.describe(`CUSTOMER SERVICE`, { tag: ['@run-gpf'] }, () => {
    test.beforeEach(async ({ customerServicePage, page, sitewide }) => {
      await test.step(`Go to the '${brand}'-brand customer service info page`, async () => {
        await customerServicePage.goToCustomerServicePage();
        await page.waitForLoadState('load');
        await waitForHydration(page);
      });

      await test.step(`Ensure that Sitewide Components are visible on page.`, async () => {
        await sitewide.checkSiteWideElementsAreOnPage();
      });
    });

    const validateInfoPageContent = async (page: Page, customerServicePage: CustomerServicePage, title: RegExp | string) => {
      await expect(customerServicePage.goBackLink).toBeVisible({ timeout: 30000 });
      await expect(customerServicePage.mainTitle).toHaveText(title, { timeout: 30000 });
      if (breakpoint === 'desktop') {
        await expect(customerServicePage.leftNav).toBeVisible({ timeout: 30000 });
      }
    };

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to change or cancel order and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-ca'] },
      async ({ changeOrCancelTitle, customerServicePage, descriptionMetaChangeCancel, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.changeOrCancelLink);
          await customerServicePage.changeOrCancelLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Changing or Canceling Your Order/i);
          await expect(customerServicePage.sideBar).toBeVisible();
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(changeOrCancelTitle);
          brand !== 'gpf' && (await expect(customerServicePage.descriptionMeta).toHaveAttribute('content', descriptionMetaChangeCancel));
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to get order status and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-ca'] },
      async ({ customerServicePage, descriptionMetaOrderStatus, homePage, orderStatusTitle, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.getOrderStatusLink);
          await customerServicePage.getOrderStatusLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Get the Status of Your Order/i);
          await expect(customerServicePage.sideBar).toBeVisible();
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(orderStatusTitle);
          await expect(customerServicePage.descriptionMeta).toHaveAttribute('content', descriptionMetaOrderStatus);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to payment options page and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-ca'] },
      async ({ customerServicePage, homePage, page, paymentOptionsTitle }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.paymentOptionsLink);
          await customerServicePage.paymentOptionsLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Payment Options/i);
          await expect(customerServicePage.sideBar).toBeVisible();
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(paymentOptionsTitle);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to buy online pickup in store page and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-ca'] },
      async ({ bopisTitle, customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.buyOnlineLink);
          await customerServicePage.buyOnlineLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Buy Online, Pickup In-Store/i);
          await expect(customerServicePage.sideBar).toBeVisible({ timeout: 30000 });
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(bopisTitle);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to manage your account page and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-ca'] },
      async ({ customerServicePage, descriptionMetaManageAccount, homePage, manageAccountTitle, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.manageYourAccountLink);
          await customerServicePage.manageYourAccountLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Managing Your Account/i);
          await expect(customerServicePage.sideBar).toBeVisible();
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(manageAccountTitle);
          brand !== 'gp' && (await expect(customerServicePage.descriptionMeta).toHaveAttribute('content', descriptionMetaManageAccount));
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to chat or call page and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@skip-ca'] },
      async ({ chatOrCallTitle, customerServicePage, descriptionMetaChatOrCall, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.chatOrCallLink);
          await customerServicePage.chatOrCallLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Contact Us/i);
          await expect(customerServicePage.sideBar).toBeVisible();
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(chatOrCallTitle);
          await expect(customerServicePage.descriptionMeta).toHaveAttribute('content', descriptionMetaChatOrCall);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to rewards program page and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-gp', '@run-brf', '@run-br', '@run-on', '@skip-ca'] },
      async ({ customerServicePage, homePage, page, utilityPage }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.rewardsProgramLink);
          await customerServicePage.rewardsProgramLink.click();
        });

        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await expect(utilityPage.rewardsBody).toHaveText(/[a-z]{2,}/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to gift cards page and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-ca'] },
      async ({ customerServicePage, giftCardsTitle, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);

        if (brand === 'br') {
          await test.step(`Navigate to customer service info page`, async () => {
            await scrollElementToCenter(customerServicePage.buyGiftCardsLink);
            await customerServicePage.buyGiftCardsLink.click();
          });
          await test.step(`Ensure the customer service info page loaded successfully`, async () => {
            await expect(customerServicePage.mainContent).toContainText(/Gift/i);
          });
        } else {
          await test.step(`Navigate to customer service info page`, async () => {
            await scrollElementToCenter(customerServicePage.giftCardsLink);
            await customerServicePage.giftCardsLink.click();
          });
          await test.step(`Ensure the customer service info page loaded successfully`, async () => {
            await validateInfoPageContent(page, customerServicePage, /Returning|About gift cards/i);
            await expect(customerServicePage.sideBar).toBeVisible();
          });
          await test.step(`Validate page metadata`, async () => {
            await expect(page).toHaveTitle(giftCardsTitle);
          });
        }
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to return policy faq page and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-ca'] },
      async ({ customerServicePage, homePage, returnsPolicyFaqTitle, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.returnPolicyFaqLink);
          await customerServicePage.returnPolicyFaqLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Return Policy/i);
          await expect(customerServicePage.sideBar).toBeVisible();
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(returnsPolicyFaqTitle);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to buy gift cards page and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@skip-ca'] },
      async ({ buyGiftCardsTitle, customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.buyGiftCardsLink);
          brand === 'gp' && breakpoint === 'mobile' && (await scrollDownByN(page, 100));
          await customerServicePage.buyGiftCardsLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Buying a Gift Card/i);
          await expect(customerServicePage.sideBar).toBeVisible();
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(buyGiftCardsTitle);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to price adjustments page and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-ca'] },
      async ({ customerServicePage, descriptionMetaPriceAdjustments, homePage, page, priceAdjustmentsTitle }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.priceAdjustmentsLink);
          await customerServicePage.priceAdjustmentsLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Price Adjustment/i);
          await expect(customerServicePage.sideBar).toBeVisible();
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(priceAdjustmentsTitle);
          brand !== 'gp' && brand !== 'gpf' && (await expect(customerServicePage.descriptionMeta).toHaveAttribute('content', descriptionMetaPriceAdjustments));
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to managing your account and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-us', '@skip-brf-stage'] },
      async ({ customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.managingYourAccountLink);
          await customerServicePage.managingYourAccountLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Your Account/i);
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(/Your Account|Account Sign Up/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to return or exchange and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-us', '@skip-brf-stage'] },
      async ({ customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.returnOrExchangeLink);
          await customerServicePage.returnOrExchangeLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Return/i);
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(/Return|Exchang/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to your shopping bag and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@skip-us'] },
      async ({ customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.yourShoppingBagLink);
          await customerServicePage.yourShoppingBagLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Shopping Bag/i);
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(/Shopping Bag/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate to in store pickup and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@skip-us'] },
      async ({ customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.inStorePickupLink);
          await customerServicePage.inStorePickupLink.click();
        });

        // eslint-disable-next-line playwright/no-skipped-test
        test.skip(process.env.START_SERVER === 'false', 'Skip this test at local env');

        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await expect(customerServicePage.goBackLink).toBeVisible();
          await expect(customerServicePage.mainTitle).toHaveText(/store Pickup|Pickup In-Store/i);
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(/Store Pickup|Pick-Up In-Store|Online Order/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate payment and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-us', '@skip-brf-stage'] },
      async ({ customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.paymentLink);
          await customerServicePage.paymentLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Payment/i);
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(/Payment|Online Ordering/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Navigate prices, limits and taxes. Ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-us', '@skip-brf-stage'] },
      async ({ customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.pricesLimitsTaxesLink);
          await customerServicePage.pricesLimitsTaxesLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Prices, Limits and Taxes|price adjustment/i);
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(/Prices, Limits and Taxes|Changing Orders/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Open order status page and ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-us', '@skip-brf-stage'] },
      async ({ customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.orderStatusAndTrackingLink);
          await customerServicePage.orderStatusAndTrackingLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Order Status|status of your order/i);
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(/Order Status|Tracking/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Open changes and cancellations page. Ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-us', '@skip-brf-stage'] },
      async ({ customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.changesAndCancellationsLink);
          await customerServicePage.changesAndCancellationsLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Changes & Cancellations|canceling your order/i);
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(/Chang(es|ing)|Cancellations/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Open gift card returns page. Ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@skip-us'] },
      async ({ customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.giftCardReturnsLink);
          await customerServicePage.giftCardReturnsLink.click();
        });
        await test.step(`Ensure the customer service info page loaded successfully`, async () => {
          await validateInfoPageContent(page, customerServicePage, /Gift Card Returns/i);
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(/Gift|Returns/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Open accessibility page. Ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@skip-us'] },
      async ({ customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.accessibilityLink);
          await customerServicePage.accessibilityLink.click();
        });

        await test.step(`Ensure the accessibility page loaded successfully`, async () => {
          await expect(customerServicePage.accessibilityTitle).toHaveText(/Accessible Customer Service Policy/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Open javascript page. Ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@skip-us'] },
      async ({ customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.javascriptLink);
          await customerServicePage.javascriptLink.click();
        });

        await test.step(`Ensure the JavaScript page loaded successfully`, async () => {
          await expect(customerServicePage.goBackLink).toBeVisible();
          await expect(customerServicePage.mainTitle).toHaveText(/JavaScript/i);
        });
      }
    );

    test(
      `${brand}-${market}-${breakpoint}-${env}: Open terms of use page. Ensure it renders correctly.`,
      { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@skip-us'] },
      async ({ customerServicePage, homePage, page }) => {
        await verifyPageLoadsFully(homePage, page);
        await test.step(`Navigate to customer service info page`, async () => {
          await scrollElementToCenter(customerServicePage.termsOfUseLink);
          await customerServicePage.termsOfUseLink.click();
        });
        await test.step(`Ensure the terms of use page loaded successfully`, async () => {
          await expect(customerServicePage.termsOfUseTitle).toHaveText('Terms of Use');
          if (breakpoint === 'desktop' && brand !== 'brf') {
            await expect(customerServicePage.leftNav).toBeVisible();
          }
        });
        await test.step(`Validate page metadata`, async () => {
          await expect(page).toHaveTitle(/Terms of Use/i);
        });
      }
    );
  });
});
