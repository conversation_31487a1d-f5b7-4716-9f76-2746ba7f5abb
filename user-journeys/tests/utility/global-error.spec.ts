import { expect } from '@playwright/test';
import { Market } from '@ecom-next/utils/server';
import { setTestOptions } from '../../test_data/customerServiceOptions';
import { scrollToBottomUntilPageDoesNotGrow } from '../../utils/helpers';

const test = setTestOptions();
const brand = process.env.BRAND!;
const market = process.env.MARKET as Market;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`Global Error`, () => {
  test(
    `${brand}-${market}-${breakpoint}-${env}-en_US: User should be redirected to page not found error page when invalid url is entered`,
    { tag: ['@run-at', '@run-br', '@run-brf', '@run-gp', '@run-on', '@run-gpf'] },
    async ({ errorPage, homePage, page }) => {
      await test.step(`Go to the '${brand}'-brand page that doesn't exist in en_US`, async () => {
        await errorPage.goToPageThatDoesntExist('en_US');
      });
      await test.step(`Verify header and footer renders correctly`, async () => {
        await expect(homePage.header).toBeVisible();
        await scrollToBottomUntilPageDoesNotGrow(page);
        await expect(homePage.footer).toBeVisible();
      });
      await test.step('Validate error page content title', async () => {
        const mainContentPerBrand: { [key: string]: string } = {
          at: `We're sorry.`,
          br: 'Sorry, this page cannot be displayed.',
          brf: 'Sorry, this page cannot be displayed.',
          gp: `We're sorry.`,
          gpf: `Sorry, this page cannot be displayed.`,
          on: `Sorry, this page cannot be displayed.`,
        };
        await expect(errorPage.mainContent).toContainText(mainContentPerBrand[brand]);
      });
    }
  );

  test(
    `${brand}-${market}-${breakpoint}-${env}-fr_CA: User should be redirected to page not found error page when invalid url is entered`,
    { tag: ['@run-br-ca', '@run-gp-ca', '@run-on-ca', '@run-brf-ca'] },
    async ({ errorPage, homePage, page }) => {
      await test.step(`Go to the '${brand}'-brand page that doesn't exist in fr_CA`, async () => {
        await errorPage.goToPageThatDoesntExist('fr_CA');
      });
      await test.step(`Verify header and footer renders correctly`, async () => {
        await expect(homePage.header).toBeVisible();
        await scrollToBottomUntilPageDoesNotGrow(page);
        await expect(homePage.footer).toBeVisible();
      });
      await test.step('Validate error page content title', async () => {
        const mainContentPerBrand: { [key: string]: string } = {
          at: `Nous sommes désolés.`,
          br: 'Nous sommes désolés, cette page ne peut pas être affichée.',
          brf: 'Nous sommes désolés, cette page ne peut pas être affichée.',
          gp: `Nous sommes désolés.`,
          on: `Nous sommes désolés, cette page ne peut pas être affichée.`,
        };
        await expect(errorPage.mainContent).toContainText(mainContentPerBrand[brand]);
      });
    }
  );
});
