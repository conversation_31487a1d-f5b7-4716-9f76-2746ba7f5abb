/* eslint-disable playwright/expect-expect */
/* eslint-disable no-console */
// @ts-check
import _ from 'lodash';
import test from '@/pom/base.page';
import { scrollElementToCenter, waitForHydration } from '@/utils/helpers';
import { compareCanaryToLive } from '@/utils/visualRegressionHelpers';
import Builder from '@/test_data/builder';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`Checkout Page`, () => {
  test.beforeEach(async ({ page, shoppingBagPage, signInPage }) => {
    await test.step(`Go to '${brand}'-brand Shopping Bag Page`, async () => {
      await shoppingBagPage.goToShoppingBagPage();
      await shoppingBagPage.addStandardProductToBag();
    });
    const builder = new Builder();
    const guestUser = builder.guestUser().withProfile();

    await test.step(`I click the Checkout button from shopping bag page`, async () => {
      brand === 'brf' && (await scrollElementToCenter(shoppingBagPage.checkoutButton));
      await shoppingBagPage.checkoutButton.click({ force: brand === 'brf' });
    });

    await test.step(`I get redirected to Sign In page`, async () => {
      await page.waitForURL(/sign-in/);
    });

    await test.step(`I enter guest email '${guestUser.email}' and click Continue as Guest`, async () => {
      await signInPage.signInAsGuest(guestUser.email);
    });

    await test.step(`I'm on the Checkout page`, async () => {
      await page.waitForLoadState('load');
      await page.waitForURL(/checkout/);
    });

    await test.step(`Wait for Hydration`, async () => {
      await waitForHydration(page);
    });
  });

  test.afterEach(async ({ shoppingBagPage }) => {
    await test.step(`Clear Shopping Bag`, async () => {
      await shoppingBagPage.clearShoppingBag(false);
    });
  });

  test.describe(`VISUAL REGRESSION`, { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@run-brf', '@run-gpf', '@visual'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Ensure Checkout Page visual comparison has not changed from baseline.`, async ({ page, checkoutPage }) => {
      await compareCanaryToLive(page, __filename, { maskableLocators: [checkoutPage.sessionEmail], bypassNewSession: true });
    });
  });
});
