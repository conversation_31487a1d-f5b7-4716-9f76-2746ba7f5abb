/* eslint-disable playwright/expect-expect */
/* eslint-disable no-console */
// @ts-check
import _ from 'lodash';
import test from '@/pom/base.page';
import { waitForHydration } from '@/utils/helpers';
import { compareCanaryToLive } from '@/utils/visualRegressionHelpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`Customer Service Page`, () => {
  test.beforeEach(async ({ page, customerServicePage }) => {
    await test.step(`Go to '${brand}'-brand Customer Service page`, async () => {
      await customerServicePage.goToCustomerServicePage();
    });

    await test.step(`Wait for Hydration`, async () => {
      await waitForHydration(page);
    });
  });

  test.describe(`VISUAL REGRESSION`, { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@run-brf', '@run-gpf', '@visual'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Ensure Customer Service Page visual comparison has not changed from baseline.`, async ({
      page,
      sitewide,
    }) => {
      await compareCanaryToLive(page, __filename, { maskableLocators: [sitewide.searchForm] });
    });
  });
});
