/* eslint-disable playwright/expect-expect */
/* eslint-disable no-console */
// @ts-check
import _ from 'lodash';
import test from '@/pom/base.page';
import { waitForHydration } from '@/utils/helpers';
import { compareCanaryToLive } from '@/utils/visualRegressionHelpers';
import Builder from '@/test_data/builder';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`Division Page`, () => {
  test.beforeEach(async ({ page, utilityPage }) => {
    await test.step(`Go to '${brand}'-brand category page`, async () => {
      const division = builder.withDivision();
      await utilityPage.navigateToDivision(division);
    });

    await test.step(`Wait for Hydration`, async () => {
      await waitForHydration(page);
    });
  });

  test.describe(`VISUAL REGRESSION`, { tag: ['@run-at', '@run-br', '@run-gp', '@run-brf', '@run-gpf', '@skip-br-ca', '@visual'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Ensure Division Page visual comparison has not changed from baseline.`, async ({ page, sitewide }) => {
      await compareCanaryToLive(page, __filename, { maxDiffPixelRatio: 0.15, maskableLocators: [sitewide.searchForm] });
    });
  });
});
