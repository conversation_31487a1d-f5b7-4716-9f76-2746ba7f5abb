/* eslint-disable playwright/expect-expect */
/* eslint-disable no-console */
// @ts-check
import _ from 'lodash';
import test from '@/pom/base.page';
import { waitForHydration } from '@/utils/helpers';
import { compareCanaryToLive } from '@/utils/visualRegressionHelpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`Home Page`, () => {
  test.beforeEach(async ({ page }) => {
    await test.step(`Go to '${brand}'-brand home page`, async () => {
      await page.goto('');
    });

    await test.step(`Wait for Hydration`, async () => {
      await waitForHydration(page);
    });
  });

  test.describe(`VISUAL REGRESSION`, { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@run-gpf'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Ensure Home Page visual comparison has not changed from baseline.`, async ({ page, sitewide }) => {
      await compareCanaryToLive(page, __filename, { maxDiffPixelRatio: 0.05, maskableLocators: [sitewide.searchForm] });
    });
  });
});
