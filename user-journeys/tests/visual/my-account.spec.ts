/* eslint-disable playwright/expect-expect */
/* eslint-disable no-console */
// @ts-check
import _ from 'lodash';
import test from '@/pom/base.page';
import { waitForHydration } from '@/utils/helpers';
import { compareCanaryToLive } from '@/utils/visualRegressionHelpers';
import Builder from '@/test_data/builder';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;
const builder = new Builder();

test.describe(`My-Account Page`, () => {
  test.beforeEach(async ({ page, signInPage }) => {
    await test.step(`Go to '${brand}'-brand sign-in then My-Account page`, async () => {
      await signInPage.goToSignInPage();
      await waitForHydration(page);
      const customerInfo = builder.returningCustomer();
      await signInPage.signInWithEmailAndPassword(customerInfo.email, customerInfo.pw);
    });

    await test.step(`Wait for Hydration`, async () => {
      await waitForHydration(page);
    });
  });

  test.describe(`VISUAL REGRESSION`, { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@run-gpf'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Ensure My-Account Page visual comparison has not changed from baseline.`, async ({ page, sitewide }) => {
      await compareCanaryToLive(page, __filename, { maxDiffPixelRatio: 0.05, maskableLocators: [sitewide.searchForm], bypassNewSession: true });
    });
  });
});
