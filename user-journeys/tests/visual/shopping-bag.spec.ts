/* eslint-disable playwright/expect-expect */
/* eslint-disable no-console */
// @ts-check
import _ from 'lodash';
import test from '@/pom/base.page';
import { waitForHydration } from '@/utils/helpers';
import { compareCanaryToLive } from '@/utils/visualRegressionHelpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`Shopping Bag Page`, () => {
  test.beforeEach(async ({ page, shoppingBagPage }) => {
    await test.step(`Go to '${brand}'-brand Shopping Bag page`, async () => {
      await shoppingBagPage.goToShoppingBagPage();
      await shoppingBagPage.addStandardProductToBag();
    });

    await test.step(`Wait for Hydration`, async () => {
      await waitForHydration(page);
    });
  });

  test.afterEach(async ({ shoppingBagPage }) => {
    await test.step(`Clear Shopping Bag`, async () => {
      await shoppingBagPage.clearShoppingBag(false);
    });
  });

  test.describe(`VISUAL REGRESSION`, { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@run-brf', '@run-gpf', '@visual'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Ensure Shopping Bag Page visual comparison has not changed from baseline.`, async ({ page, sitewide }) => {
      await compareCanaryToLive(page, __filename, { maskableLocators: [sitewide.searchForm] });
    });
  });
});
