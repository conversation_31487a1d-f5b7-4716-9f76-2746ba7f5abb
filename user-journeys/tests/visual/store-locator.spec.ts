/* eslint-disable playwright/expect-expect */
/* eslint-disable no-console */
// @ts-check
import _ from 'lodash';
import test from '@/pom/base.page';
import { waitForHydration } from '@/utils/helpers';
import { compareCanaryToLive } from '@/utils/visualRegressionHelpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`Store Locator page`, () => {
  test.beforeEach(async ({ page, storeLocatorPage }) => {
    await test.step(`Go to the ${brand}-brand Store Locator page`, async () => {
      await storeLocatorPage.goToStoreLocatorLandingPage(brand, market);
    });

    await test.step(`Wait for Hydration`, async () => {
      await waitForHydration(page);
    });
  });

  test.describe(`VISUAL REGRESSION`, { tag: ['@run-at', '@run-br', '@run-gp', '@run-on', '@run-brf', '@run-gpf', '@visual'] }, () => {
    test(`${brand}-${market}-${breakpoint}-${env}: Ensure Store Locator page visual comparison has not changed from baseline.`, async ({ page, sitewide }) => {
      await compareCanaryToLive(page, __filename, { maskableLocators: [sitewide.searchForm] });
    });
  });
});
