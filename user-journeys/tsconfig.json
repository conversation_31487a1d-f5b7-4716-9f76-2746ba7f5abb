{"exclude": ["**/*.js"], "compilerOptions": {"composite": true, "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "baseUrl": ".", "noEmit": true, "paths": {"@/*": ["./*"], "test-utils": ["../packages/core/tests/test-utils/index.tsx"], "@mfe-api-types/*": ["../types/generated/*"], "@mui/*": ["../packages/marketing-ui/src/*"], "@sitewide/*": ["../packages/sitewide/src/*"], "@shopping-bag/*": ["../packages/shopping-bag/src/*"], "@checkout/*": ["../packages/checkout/src/*"], "@core/*": ["../packages/core/src/*"], "@core/legacy/link": ["../packages/core/src/components/link"], "@ecom-next/core/*": ["../packages/core/src/*"]}}}