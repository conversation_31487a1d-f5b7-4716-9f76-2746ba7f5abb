import { type Page } from 'playwright';
import { expect } from '@playwright/test';
import test from '@/pom/base.page';
import { Sitewide } from '@/pom/common/sitewide';
import { commonTealiumTags, commonAdobeTags } from '@/test_data/analytics';
import { CommonTealiumTags, CommonAdobeTags } from '@/global';

export const registerAnalyticsData = async () => {
  test.beforeEach(async ({ page, sitewide }) => {
    await test.step('Register Analytics Data', () => {
      page.on('request', req => {
        if (req.url().includes('securemetrics.gap.com') && req.postData()) {
          sitewide.setAdobeAnalyticsData(req.postDataJSON());
        }
        if (req.url().includes('i.gif') && req.postData()) {
          sitewide.setTealiumAnalyticsData(req.postData()!);
        }
      });
    });
  });
};

export const waitForAnalytics = async (page: Page, sitewide: Sitewide) => {
  await test.step('Wait for analytics to send initial request', async () => {
    do {
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await page.waitForTimeout(1000);
    } while (!sitewide.adobeAnalyticsData[0] || !sitewide.tealiumAnalyticsData[0]);
  });
};

export const checkTealiumAnalytics = async (page: Page, sitewide: Sitewide) => {
  const tealiumTags = Object.keys(commonTealiumTags);

  for (let i = 0; i < tealiumTags.length; i++) {
    await test.step(`Check Telium tag ${tealiumTags[i]} to equal ${commonTealiumTags[tealiumTags[i] as keyof CommonTealiumTags]}`, () => {
      expect
        .soft(`${sitewide.tealiumAnalyticsData[0]['data'][tealiumTags[i]]}`, `Telium Tag: ${tealiumTags[i]}`)
        .toEqual(`${commonTealiumTags[tealiumTags[i] as keyof CommonTealiumTags]}`);
    });
  }
};

export const checkAdobeAnalytics = async (page: Page, sitewide: Sitewide) => {
  while (!sitewide.adobeAnalyticsData[0])
    await new Promise(resolve => {
      setTimeout(resolve, 1000);
    });

  const adobeTags = Object.keys(commonAdobeTags);

  for (let i = 0; i < adobeTags.length; i++) {
    await test.step(`Check Adobe tag ${adobeTags[i]} to equal ${commonAdobeTags[adobeTags[i] as keyof CommonAdobeTags]}`, () => {
      expect
        .soft(`${sitewide.adobeAnalyticsData[0][adobeTags[i]]}`, `Adobe Tag: ${adobeTags[i]}`)
        .toEqual(`${commonAdobeTags[adobeTags[i] as keyof CommonAdobeTags]}`);
    });
  }
};
