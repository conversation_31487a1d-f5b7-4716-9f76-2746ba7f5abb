/* eslint-disable no-console */
import { CustomPage } from '@/global';

export const filterConsoleLog = async (page: CustomPage) => {
  await page.addInitScript(
    `
      const unwantedSubstrings = ${JSON.stringify(substrings)};
      const filteredMethods = ['log','warn','error','info','debug'];
      filteredMethods.forEach(method => {
        const originalMethod = window['console'][method];
        window['console'][method] = (...args) => {
          const message = args.join(' ');
          if (!unwantedSubstrings.some(sub => message.includes(sub))) {
            originalMethod.apply(console, args);
          }
        };
      });`
  );
};

const substrings = ["was preloaded using link preload but not used within a few seconds from the window's load event", 'New Relic Warning', '"accountId":"'];
