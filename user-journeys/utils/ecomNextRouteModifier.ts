import { Request, Route } from '@playwright/test';
import * as allure from 'allure-js-commons';
import { getCookie<PERSON><PERSON> } from './helpers';
import { getBaseURL } from './urls';
import { CustomPage } from '@/global';
import test from '@/pom/base.page';

const BRAND = process.env.BRAND;
const BREAKPOINT = process.env.BREAKPOINT;
const CHECKOUT_BYPASS = process.env.CHECKOUT_BYPASS;
const ENV = process.env.ENV;
const FEATURE_FLAGS = process.env.FEATURE_FLAGS;
const MARKET = process.env.MARKET;
const preview_days_ahead = +process.env.PREVIEW_DATE!;
const SEGMENTS = process.env.SEGMENTS;

const randomNumber = Math.round(Math.random() * 1000000);

export const ecomNextRouteModifier = async (page: CustomPage, brand: string = BRAND!) => {
  await page.route(/mp4/, (route: Route) => route.abort('aborted'));
  if (test.info().tags.includes('@visual')) {
    await page.route(/media.amplience.net/, (route: Route) => route.abort('aborted'));
    await page.route(/webcontent/, (route: Route) => route.abort('aborted'));
    await page.route(/findmine/, (route: Route) => route.abort('aborted'));
    await page.route(/bigcontent/, (route: Route) => route.abort('aborted'));
  }

  const cookieBrand = getCookieBrand(brand);

  await page.route(/checkout\/place-order\/xapi|my-account\/xapi|commerce\/credentials\/authorize-flow/i, (route: Route, request: Request) => {
    const headers = request.headers();
    delete headers['user-agent'];
    delete headers['User-Agent'];
    headers['User-Agent'] = 'Gid_Synthetic';
    headers['X-Gap-SyntheticOrder'] = 'true';
    return route.continue({ headers });
  });

  await page.route(
    /^https?:\/\/[a-zA-Z0-9-.]*\.(?:factory-)?(?:gap|gapfactory|gaptecholapps|gaptechol|azeus\.gaptech|gapcanada)(?:olapps|ol)?\.(?:com|ca)(?::\d+)?(?:(?!jpg|svg|png|webm).)*$/,

    async (route: Route, request: Request) => {
      const url = new URL(request.url());
      const headers = request.headers();

      const isRewrite = test.info().titlePath.includes('Rewrite');
      const isPLP = test.info().titlePath.includes('Category') || test.info().titlePath.includes('Search');
      const isPDP = test.info().titlePath.includes('Product');

      if (
        (url.pathname.startsWith('/browse') ||
          url.pathname.startsWith('/shopping-bag') ||
          url.pathname.startsWith('/my-account') ||
          url.pathname.startsWith('/checkout') ||
          url.pathname.startsWith('/stores') ||
          url.pathname.startsWith('/customer-service') ||
          url.pathname.startsWith('/page') ||
          url.pathname.startsWith('/checkout') ||
          url.pathname === '/') &&
        !url.pathname.includes('assets') &&
        !url.pathname.includes('api')
      ) {
        url.searchParams.set('breakakcache', `${randomNumber}`);

        headers['Client-Size-Class'] = BREAKPOINT!.charAt(0).toUpperCase() + BREAKPOINT!.substring(1)!;

        if (!test.info().tags.includes('@do-not-disable-optimizely')) {
          url.searchParams.set('optimizely_disable', 'true');
        }

        const plpRedesignFlag = 'plp-hui-q1-rewrite';
        const pdpRedesignFlag = `pdp-hui-redesign-${MARKET}-${cookieBrand}`;

        if (FEATURE_FLAGS || isRewrite || test.info().tags.includes('@ai-recs-on')) {
          const featureFlags = FEATURE_FLAGS ? FEATURE_FLAGS.split(',').map(flag => flag.trim()) : [];
          test.info().tags.includes('@ai-recs-on') && featureFlags.push(`buy-ui-ai-recs`);
          isRewrite && isPLP && featureFlags.push(plpRedesignFlag);
          isRewrite && isPDP && featureFlags.push(pdpRedesignFlag);
          url.searchParams.set('fffs', JSON.stringify(featureFlags));
          featureFlags.length !== 0 && (await allure.parameter('Feature Flags', JSON.stringify(featureFlags), { excluded: true }));
        }

        let disableFeatureFlags;
        if (!isRewrite) {
          disableFeatureFlags = [plpRedesignFlag, pdpRedesignFlag];
        } else {
          isPLP && (disableFeatureFlags = [pdpRedesignFlag]);
          isPDP && (disableFeatureFlags = [plpRedesignFlag]);
        }
        url.searchParams.set('disable-feature-flags', JSON.stringify(disableFeatureFlags));
        await allure.parameter('Disable Feature Flags', JSON.stringify(disableFeatureFlags), { excluded: true });
      }

      if (ENV === 'local' && url.pathname.startsWith('/browse') && !url.pathname.includes('.do')) {
        const response = await page.request.get(`${getBaseURL(BRAND, MARKET, 'prod').slice(0, -1)}${url.pathname}${url.search}`);
        const newURL = new URL(`${getBaseURL(BRAND, MARKET, 'local').slice(0, -1)}${response.headers()['x-upstream-request-path']}`);
        url.pathname = newURL.pathname;
        url.search = newURL.search;
      }

      if (!page.haveAppliedSegments) {
        page.haveAppliedSegments = true;

        const segments = SEGMENTS ? process.env.SEGMENTS!.split(',').map(flag => flag.replace('-', ':').trim()) : [];

        isRewrite && isPDP && segments.push(`${cookieBrand}223:a`);
        isRewrite && isPLP && segments.push(`${cookieBrand}238:a`);
        (!isRewrite || isPLP) && segments.push(`${cookieBrand}223:x`);
        (!isRewrite || isPDP) && segments.push(`${cookieBrand}228:x`);
        test.info().tags.includes('@ai-recs-on') && segments.push(`xb240:a`);
        test.info().tags.includes('@ai-recs-off') && segments.push(`xb240:x`);
        segments.length !== 0 && (await allure.parameter('Segments', JSON.stringify(segments), { excluded: true }));

        url.searchParams.set('segment', JSON.stringify(segments));

        if (ENV === 'preview' && !!process.env.PREVIEW_DATE) {
          const date = new Date();
          const futureDate = date.getDate() + preview_days_ahead;
          date.setDate(futureDate);
          const formattedDate = date.toISOString().slice(0, 10);
          url.searchParams.delete('previewDate');
          url.search += `&previewDate=${formattedDate}+12:00:00+PST`;
          await allure.parameter('Preview Date', `${formattedDate} 12:00 PST`, { excluded: true });
        }
      }

      if (test.info().tags.includes('@checkout')) {
        headers['X-Gap-SyntheticOrder'] = 'true';
      }

      if (ENV === 'local' && url.pathname.includes('get-bag')) {
        delete headers['Origin'];
        delete headers['origin'];
        delete headers['ORIGIN'];
        headers['origin'] = headers['referer'].endsWith('/') ? headers['referer'].slice(0, -1) : headers['referer'];
      }

      if (((process.env.CANARY !== 'false' && process.env.CI) || ENV === 'stage') && CHECKOUT_BYPASS === 'true') {
        headers['Chartis-Force-Route'] = 'canary';
      }

      if ((ENV === 'preview' || ENV === 'wip-stage') && test.info().tags.includes('@category')) {
        url.searchParams.set('inventoryAware', 'true');
      }

      if ((ENV === 'stage' || ENV === 'local' || ENV === 'wip-stage') && url.pathname.includes('commerce/search/products/v2/cc')) {
        url.searchParams.set('ignoreInventory', 'false');
      }

      if (
        url.pathname.includes('commerce/credentials/authorize-flow') ||
        url.pathname.includes('checkout/place-order/xapi') ||
        url.pathname.includes('my-account/xapi')
      ) {
        delete headers['user-agent'];
        delete headers['User-Agent'];
        headers['User-Agent'] = 'Gid_Synthetic';
        headers['X-Gap-SyntheticOrder'] = 'true';
        return route.continue({ headers });
      }

      // eslint-disable-next-line consistent-return
      return route.continue({
        url: url.href,
        headers,
      });
    }
  );

  if (ENV === 'local') {
    await page.route(/\/commerce\/search\/products\/v2\/(?:style|cc)/, (route: Route, request: Request) => {
      const url = new URL(request.url());
      url.hostname = 'api.azeus.gaptech.com';
      return route.continue({ url: url.href });
    });
  }

  if (!test.info().tags.includes('@do-not-disable-optimizely')) {
    await page.route('**/optimizely_snippets.js', (route: Route) => route.abort('aborted'));
  }

  if (test.info().tags.includes('@allow-tealium-for-athleta-us') && BRAND === 'at' && MARKET === 'us') {
    // Abort New Relic, Full Story, Adobe, and Tealium analytics requests
    await page.route(/^https?:\/\/(?:[a-zA-Z0-9-.]+)?\.?(?:newrelic|fullstory|nr-data)\.(?:com|net)|.*securemetrics\.gap\.com.*/, (route: Route) => {
      return route.abort(test.info().tags.includes('@analytics') ? 'blockedbyclient' : 'aborted');
    });
  } else {
    // Abort New Relic, Full Story, Adobe, and Tealium analytics requests
    await page.route(/^https?:\/\/(?:[a-zA-Z0-9-.]+)?\.?(?:newrelic|fullstory|nr-data)\.(?:com|net)|.*i\.gif.*|.*securemetrics\.gap\.com.*/, (route: Route) => {
      return route.abort(test.info().tags.includes('@analytics') ? 'blockedbyclient' : 'aborted');
    });
  }

  if (!test.info().tags.includes('@allowattntv')) {
    await page.route(/attn\.tv/, (route: Route) => route.abort('aborted'));
  }

  if (ENV === 'local' || ENV === 'stage') {
    await page.route(/^https:\/\/[a-z-.]*\/webcontent|^https:\/\/[a-z-.]*\/asset_archive/i, async (route: Route, request: Request) => {
      const url = new URL(request.url());
      url.hostname = 'www1.assets-gap.com';
      const response = await route.fetch({ url: url.href, headers: {} });
      return route.fulfill({ response });
    });
  }
};
