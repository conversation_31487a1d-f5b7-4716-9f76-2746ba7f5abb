/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-param-reassign */
import { devices, expect, test, Locator, type Page } from '@playwright/test';

const brand = process.env.BRAND;
const env = process.env.ENV;

declare global {
  interface Window {
    ujt_ready: boolean | undefined;
  }
}

export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export async function scrollToBottom(page: Page) {
  let count = 0;
  while ((await footerIsZeroHeighth(page)) || (!(await isPageFullyScrolled(page)) && count < 120)) {
    await page.mouse.wheel(0, 1000);
    await delay(100);
    count += 1;
  }
  await page.locator('#sitewide-footer:has(div)').scrollIntoViewIfNeeded();
}

export async function scrollSlight(page: Page) {
  if (brand === 'brf') {
    await page.evaluate(() => window.scrollTo(0, document.documentElement.scrollHeight));
  }
}

function isPageFullyScrolled(page: Page) {
  return page.evaluate(() => window.scrollY + window.innerHeight === document.documentElement.scrollHeight);
}

function footerIsZeroHeighth(page: Page) {
  return page.evaluate(() => {
    const footer = document.querySelector('#sitewide-footer:has(div)');
    const alternateFooter = document.querySelector('#footer');
    return (!!footer && footer.clientHeight === 0) || (!!alternateFooter && alternateFooter.clientHeight === 0);
  });
}

export async function scrollToTop(page: Page) {
  await page.evaluate(() => {
    window.scrollTo(0, 0);
  });
}

export async function scrollElementToCenter(element: Locator) {
  await element.waitFor();
  await element.evaluate(element => {
    element.scrollIntoView({ block: 'center', inline: 'center' });
  });
}

export async function scrollDownByN(page: Page, scrollDownBy: number) {
  await page.evaluate(scrollDownBy => {
    window.scrollTo({
      top: document.documentElement.scrollTop + scrollDownBy,
      left: document.documentElement.scrollLeft,
    });
  }, scrollDownBy);
}

export async function scrollToLocator(page: Page, locatorToWaitFor: Locator) {
  const maxTries = 20;
  let currentTry = 1;
  while (!(await locatorToWaitFor.isVisible()) && currentTry < maxTries) {
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await page.waitForTimeout(1000);
    await page.evaluate(() => {
      window.scrollTo({ top: document.documentElement.scrollHeight + document.documentElement.scrollTop, behavior: 'smooth' });
    });
    currentTry++;
  }
  await expect(locatorToWaitFor).toBeVisible({ timeout: 1000 });
}

export async function scrollToBottomUntilPageDoesNotGrow(page: Page, specificWaitTime: number = 1000) {
  const maxTries = 20;
  let currentTry = 1;

  // Keep track of multiple previous dimensions to detect growth over several cycles
  const dimensionsHistory = [];
  let currentDimensions = await page.evaluate(() => document.documentElement.scrollHeight);
  dimensionsHistory.push(currentDimensions - 200);
  let comparisonIndex = Math.max(0, dimensionsHistory.length - 4);

  do {
    await page.evaluate(() => {
      window.scrollTo({ top: document.documentElement.scrollHeight + document.documentElement.scrollTop, behavior: 'smooth' });
    });
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await page.waitForTimeout(specificWaitTime);

    currentDimensions = await page.evaluate(() => document.documentElement.scrollHeight);
    dimensionsHistory.push(currentDimensions);

    // Keep only the last 4 measurements (current + 3 previous)
    if (dimensionsHistory.length > 4) {
      dimensionsHistory.shift();
    }

    currentTry++;

    // Compare current dimensions with dimensions from 3 cycles ago (if available)
    comparisonIndex = Math.max(0, dimensionsHistory.length - 4);
  } while (currentDimensions > dimensionsHistory[comparisonIndex] && currentTry < maxTries);
}

export function cacheBuster(url: string): string {
  const randomNumber = Math.round(Math.random() * 1000000);
  const returnUrl = new URL(url);
  returnUrl.searchParams.delete('breakakcache');
  returnUrl.searchParams.append('breakakcache', `${randomNumber}`);
  return returnUrl.href;
}

export async function hidePreviewButton(page: Page) {
  const previewButton = page.locator(`#preview-button`);
  // eslint-disable-next-line no-empty
  if (env === 'preview') {
    await page.addLocatorHandler(previewButton, async () => {
      await test.step(`I am hiding the Preview Button`, async () => {
        try {
          await previewButton.waitFor({ state: 'attached' });
          await page.evaluate(`window.document.getElementById('preview-button').style.display = 'none';`);
          // eslint-disable-next-line no-empty
        } catch {}
      });
    });
  }
}

export function escapeRegExp(text: string) {
  return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
}

export function getFullBrand() {
  switch (brand) {
    case 'at':
      return 'Athleta';
    case 'br':
      return 'Banana Republic';
    case 'brf':
      return 'Banana Republic Factory Store';
    case 'gp':
      return 'Gap';
    case 'gpf':
      return 'Gap Factory Store';
    case 'on':
      return 'Old Navy';
    default:
      return 'Not Found';
  }
}

export async function waitForHydration(page: Page) {
  await page.waitForFunction(() => !!window.ujt_ready, undefined, { timeout: 40000 });
}

export async function deleteChartisHeaderFromRoute(page: Page, url: string | RegExp) {
  url = typeof url === 'string' ? new RegExp(escapeRegExp(url)) : url;
  await page.route(url, async route => {
    const headers = route.request().headers();
    delete headers['Chartis-Force-Route'];
    delete headers['chartis-force-route'];
    await route.continue({ headers });
  });
}

export async function useDefaultUserAgent(page: Page, url: string | RegExp) {
  url = typeof url === 'string' ? new RegExp(escapeRegExp(url)) : url;
  await page.route(url, async route => {
    const headers = route.request().headers();
    delete headers['User-Agent'];
    delete headers['user-agent'];
    headers['User-Agent'] = devices['Desktop Chrome'].userAgent;
    await route.continue({ headers });
  });
}

export function areWeInNext(page: Page): Promise<boolean> {
  return page.evaluate('!!window.gap && !!window.gap.next');
}

export async function updateABSegCookie(page: Page, cookieValue: string) {
  await page.context().clearCookies();

  const newABSegCookie = [
    {
      name: 'ABSeg',
      value: `${cookieValue}`,
      domain: '.gap.com',
      path: '/',
      expires: -1,
      httpOnly: false,
      secure: false,
    },
  ];

  await page.context().addCookies(newABSegCookie);
  await page.reload();
}

export async function closeOneTrustModal(page: Page) {
  const oneTrustCloseButton = page.locator(`button.onetrust-close-btn-handler`).first();
  if (await oneTrustCloseButton.isVisible()) {
    await test.step(`Closing OneTrust Modal.`, async () => {
      await oneTrustCloseButton.click();
    });
  }
}

export function getCookieBrand(brand: string): string {
  switch (brand) {
    case 'br':
      return 'br';
    case 'brf':
      return 'brfs';
    case 'gp':
      return 'gap';
    case 'gpf':
      return 'gapfs';
    case 'on':
      return 'on';
    default:
      return 'at';
  }
}

export function getPLPCookieBrand(brand: string): string {
  return getCookieBrand(brand) === 'gpfs' ? 'gapfs' : getCookieBrand(brand);
}

export async function getPageType(page: Page): Promise<string> {
  return page.evaluate(`window.gap ? gap.pageType : 'gap object not created yet'`);
}
