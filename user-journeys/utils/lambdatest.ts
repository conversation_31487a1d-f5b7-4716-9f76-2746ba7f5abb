// LambdaTest capabilities
const capabilities = {
  browserName: 'Chrome',
  browserVersion: 'latest',
  'LT:Options': {
    platform: 'Windows 10',
    build: 'Ecom Next',
    name: 'Playwright Test',
    user: process.env.LT_USERNAME,
    accessKey: process.env.LT_ACCESS_KEY,
    network: true,
    video: true,
    console: true,
    timezone: 'Los_Angeles',
    tunnel: false, // Add tunnel configuration if testing locally hosted webpage
    tunnelName: '', // Optional
    geoLocation: 'US', // country code can be fetched from https://www.lambdatest.com/capabilities-generator/
  },
};

// Patching the capabilities dynamically according to the project name.
export const modifyCapabilities = (configName: string, buildName: string, testName: string) => {
  const config = configName.split('@lambdatest')[0];
  const [browserName, browserVersion, platform] = config.split(':');
  capabilities.browserName = browserName ? browserName : capabilities.browserName;
  capabilities.browserVersion = browserVersion ? browserVersion : capabilities.browserVersion;
  capabilities['LT:Options']['platform'] = platform ? platform : capabilities['LT:Options']['platform'];
  capabilities['LT:Options']['build'] = buildName;
  capabilities['LT:Options']['name'] = testName;
  return capabilities;
};
