import jsonfile from 'jsonfile';
import { Page } from 'playwright';
/**
 * Set sessionStorage from "brand_storageState.json"
 * @param {import('@playwright/test').Page} page
 */

export async function setSessionStorage(page: Page) {
  const stateJSON = jsonfile.readFileSync(`./storageStates/${process.env.BRAND!.toLowerCase()}_storageState.json`); // path must be from the root
  const sessionStorage = stateJSON.sessionStorage;

  if (!sessionStorage || sessionStorage.length === 0) return;

  for (const entity of sessionStorage) {
    // eslint-disable-next-line @typescript-eslint/no-shadow
    await page.context().addInitScript((entity: { [s: string]: unknown } | ArrayLike<unknown>) => {
      for (const [key, value] of Object.entries(entity) as [string, string][]) {
        window.sessionStorage.setItem(key, value);
      }
    }, entity);
  }
}
