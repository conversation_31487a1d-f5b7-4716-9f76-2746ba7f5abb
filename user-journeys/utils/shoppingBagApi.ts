import { Page } from 'playwright';
import { expect } from '@playwright/test';
import _ from 'lodash';
import { getSecureURL } from './urls';

const { BRAND, MARKET } = process.env;

export async function clearShoppingBag(page: Page) {
  const [headerBrand] = getHeaderBrand(BRAND!.toUpperCase());
  const market = MARKET!.toUpperCase();
  const domain = getSecureURL();

  while ((await getShoppingBagQuantity(page)) !== 0) {
    const lineItemId = await getLineItemId(page);

    const response = await page.request.post(`${domain}shopping-bag-xapi/delete-bag-item`, {
      headers: {
        ContentType: 'application/json',
        Accept: '*/*',
        guest: 'true',
        market,
        brand: headerBrand,
        brandtype: 'specialty',
        channel: 'WEB',
        locale: 'en_US',
        clientRequestId: '7430d7f0-f9ce-4a99-a54a-67864d41745b',
      },
      data: {
        lineItemId,
      },
    });

    expect(response.status(), `ShoppingBagApi: call to 'clearShopping<PERSON>ag' did not return a 200 response`).toBe(200);
  }
}

export async function getShoppingBag(page: Page) {
  const [headerBrand] = getHeaderBrand(BRAND!.toUpperCase());
  const market = MARKET!.toUpperCase();
  const domain = getSecureURL();

  const response = await page.request.get(`${domain}shopping-bag-xapi/get-bag`, {
    headers: {
      ContentType: 'application/json',
      Accept: '*/*',
      guest: 'true',
      market,
      brand: headerBrand,
      brandtype: 'specialty',
      channel: 'WEB',
      locale: 'en_US',
      clientRequestId: '7430d7f0-f9ce-4a99-a54a-67864d41745b',
    },
  });

  expect(response.status(), `ShoppingBagApi: 'get-bag' did not return a 200 response`).toBe(200);
  return response;
}

async function getShoppingBagQuantity(page: Page) {
  const response = await getShoppingBag(page);
  const responseBody = JSON.parse(await response.text());
  return responseBody.productList.length;
}

async function getLineItemId(page: Page) {
  const response = await getShoppingBag(page);
  const responseBody = JSON.parse(await response.text());
  expect(_.isEmpty(responseBody.productList), `ShoppingBagApi: get-bag shows no line items`).toBe(false);
  return responseBody.productList[0].id;
}

// Moved to shopping-bag.page.ts
// export async function addToBag(page: Page, productId: string, productQty: string, productLocationId = null) {
//   switch (ENV) {
//     case 'prod':
//       return addToBagProd(page, productId, productQty);
//     case 'preview':
//       return addToBagPreview(page, productId, productQty, productLocationId);
//     default:
//       return undefined;
//   }
// }

// export async function addToBagProd(page: Page, productId: string, productQty: string) {
//   const [brand] = getHeaderBrand(BRAND!.toUpperCase());
//   const market = MARKET!.toUpperCase();

//   let addToBagURL;

//   switch (`${BRAND}${MARKET}`) {
//     case 'atus':
//     case 'brus':
//     case 'gpus':
//     case 'onus':
//       addToBagURL = 'https://api.gap.com/commerce/shopping-bags/items/summary?locale=en_US';
//       break;
//     case 'brfus':
//     case 'gpfus':
//       addToBagURL = 'https://api.gapfactory.com/commerce/shopping-bags/items/summary?locale=en_US';
//       break;
//     case `${BRAND}ca`:
//       addToBagURL = 'https://api.gapcanada.ca/commerce/shopping-bags/items/summary?locale=en_CA';
//       break;
//     default:
//       break;
//   }

//   const response = await page.request.post(addToBagURL!, {
//     headers: {
//       ContentType: 'application/json',
//       Accept: '*/*',
//       guest: 'true',
//       market,
//       brand,
//       brandtype: 'specialty',
//       channel: 'WEB',
//       locale: 'en_US',
//       'X-Gap-Apimode': 'leapfrog',
//       Clientid: 'PDP',
//       Origin: getBaseURL(),
//       Referer: getBaseURL(),
//       'request-host': getBaseURL(),
//     },
//     data: {
//       brand,
//       market,
//       items: [
//         {
//           sku: productId,
//           quantity: productQty,
//         },
//       ],
//     },
//   });

//   const responseBody = JSON.parse(await response.text());

//   expect(response.status(), `ShoppingBagApi: call to 'addToBag' did not return a 200 response`).toBe(200);

//   expect(_.isEmpty(responseBody.outOfStockItems), `If expect fails, ShoppingBagApi: addToBag shows '${productId}' "Out of Stock"`).toBe(true);

//   return response;
// }

// export async function addToBagPreview(page: Page, productId: string, productQty: string, productLocationId: null) {
//   const [brand] = getHeaderBrand(BRAND!.toUpperCase());
//   const market = MARKET!.toUpperCase();
//   const domain = getAzeusAPIURL();
//   const locale = await getLocale(page);

//   const nonBopisPayload = {
//     brand,
//     items: [
//       {
//         sku: productId,
//         quantity: productQty,
//       },
//     ],
//     market,
//     partialReservation: true,
//   };
//   const bopisPayload = {
//     brand,
//     items: [
//       {
//         sku: productId,
//         quantity: productQty,
//         fulfillment: {
//           deliveryLocationId: productLocationId,
//           type: 'PICKUP',
//         },
//       },
//     ],
//     market,
//     partialReservation: true,
//   };

//   const response = await page.request.post(`${domain}commerce/shopping-bags/items/summary?locale=${locale}`, {
//     headers: {
//       ContentType: 'application/json',
//       Accept: '*/*',
//       guest: 'true',
//       market,
//       brand,
//       brandtype: 'specialty',
//       channel: 'WEB',
//       Previewtype: 'WIP',
//       locale,
//       Clientid: 'PDP',
//       Origin: getBaseURL(),
//       Referer: getBaseURL(),
//       'X-Gap-Apimode': 'leapfrog',
//     },
//     data: productLocationId ? bopisPayload : nonBopisPayload,
//   });
//   expect(response.status(), `ShoppingBagApi: call to 'addToBag' did not return a 200 response`).toBe(200);
//   const responseBody = JSON.parse(await response.text());
//   expect(_.isEmpty(responseBody.outOfStockItems), `If expect fails, ShoppingBagApi: addToBag shows '${productId}' "Out of Stock"`).toBe(true);

//   return response;
// }

// async function getLocale(page: Page) {
//   const cookies = await page.context().cookies();
//   const localeCookie = cookies.find(cookie => cookie.name === 'locale');
//   return localeCookie!.value.replace(/\|/g, '');
// }

function getHeaderBrand(brand: string) {
  let headerBrand;

  if (brand === 'BRF' || brand === 'GPF') {
    if (brand === 'BRF') {
      headerBrand = 'BF';
    } else {
      headerBrand = 'GF';
    }
    // eslint-disable-next-line no-param-reassign
    brand += 'S';
  } else {
    headerBrand = brand;
  }
  return [brand, headerBrand];
}
