/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-shadow */

import { BrowserContextOptions, Page } from 'playwright';
import { getBaseURL, getSecureURL } from './urls';

declare global {
  interface Window {
    pageURL: () => Promise<string>;
  }
}

interface StorageState {
  cookies: Array<{
    domain: string;
    expires: number;
    httpOnly: boolean;
    name: string;
    path: string;
    sameSite: 'Strict' | 'Lax' | 'None';
    secure: boolean;
    value: string;
  }>;
  origins: Array<{
    localStorage: Array<{
      name: string;
      value: string;
    }>;
    origin: string;
  }>;
}

/**
 * Set storageState from playwright.config.js
 * @param {import('@playwright/test').Page} page
 */
export async function setStorageState(page: Page, storageState: BrowserContextOptions['storageState'], registerPageURLBinding = true) {
  // eslint-disable-next-line global-require
  storageState = typeof storageState === 'string' ? (require(`../${storageState}`) as StorageState) : storageState;
  registerPageURLBinding && (await page.context().exposeBinding('pageURL', ({ page }) => page.url()));

  await page.addInitScript(
    async ({ storageState }) => {
      const url = await window.pageURL();
      const expirationDate = new Date();
      expirationDate.setFullYear(expirationDate.getFullYear() + 1);
      storageState!['cookies'].forEach(cookie => {
        const secure = cookie['secure'] ? 'secure; ' : '';
        document.cookie = `${cookie['name']}=${cookie['value']}; domain=${cookie['domain']}; path=${
          cookie['path']
        }; expires=${expirationDate}; ${secure}samesite=${cookie['sameSite'].toLowerCase()}`;
      });
      const relevantStorage = storageState!.origins.filter(localstorageObject => url.includes(localstorageObject['origin']));

      relevantStorage.forEach(storage => {
        storage.localStorage.forEach(item => {
          if (item['name'] === 'emailPop_AT_Desktop-expiry' || item['name'] === 'emailPop_AT_Mobile-expiry')
            item['value'] = `{"expiry":${Date.now() + 86400000}}`;
          localStorage.setItem(item['name'], item['value']);
        });
      });
    },
    { storageState }
  );
}

export function buildStorageState(brand = process.env.BRAND, env = process.env.ENV) {
  const brandStorageState = require(`../storageStates/${brand!.toLowerCase()}_storageState.json`);
  const storageState = require('../storageStates/storageState.json');

  storageState.cookies = storageState.cookies.concat(brandStorageState.cookies);
  storageState.origins = storageState.origins.concat(brandStorageState.origins);
  const currentTime = new Date().getTime().toString(); // Suppresses Survey Pop-up by making declined date current.
  storageState.origins[0].localStorage.push({ name: 'DECLINED_DATE', value: currentTime });

  const splitURL = getBaseURL(brand, process.env.MARKET, env)!.split('.');
  const cookieDomain = `.${
    splitURL
      .slice(splitURL.length - 2)
      .join('.')
      .replace('/', '')
      .split(':')[0]
  }`;
  storageState.cookies.forEach((_: string, index: string | number) => (storageState.cookies[index].domain = cookieDomain));
  storageState.origins.forEach((_: string, index: string | number) => {
    storageState.origins[index].origin = getBaseURL(brand, process.env.MARKET, env);
    const secureUrlCopy = structuredClone(storageState.origins[index]);
    secureUrlCopy.origin = getSecureURL(brand, process.env.MARKET, env);
    storageState.origins.push(secureUrlCopy);
  });

  return storageState;
}
