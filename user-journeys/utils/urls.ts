const BRAND = process.env.BRAND;
const ENV = process.env.ENV;
const MARKET = process.env.MARKET;

interface Brand {
  at: Market;
  br: Market;
  brf: Market;
  gp: Market;
  gpf: Market;
  on: Market;
}
interface Market {
  ca?: Environments;
  us: Environments;
}
interface Environments {
  aksStage?: Urls;
  aksTest?: Urls;
  local?: Urls;
  preview: Urls;
  prod: Urls;
  stage: Urls;
  'wip-stage'?: Urls;
}
interface Urls {
  azeusAPIUrl?: string;
  baseUrl: string;
  secureUrl?: string;
}

const envConfig: Brand = {
  gp: {
    us: {
      prod: {
        baseUrl: 'https://www.gap.com/',
        secureUrl: 'https://secure-www.gap.com/',
      },
      preview: {
        baseUrl: 'https://www.wip.prod.gaptecholapps.com/',
        secureUrl: 'https://secure.www.wip.prod.gaptecholapps.com/',
        azeusAPIUrl: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.com/',
      },
      'wip-stage': {
        baseUrl: 'https://www.app.stage.gaptecholapps.com/',
      },
      stage: {
        baseUrl: 'https://www.stage.gaptechol.com/',
        secureUrl: 'https://secure-www.stage.gaptechol.com/',
      },
      local: {
        baseUrl: 'http://www.local.gaptechol.com:3000/',
      },
    },
    ca: {
      prod: {
        baseUrl: 'https://www.gapcanada.ca/',
        secureUrl: 'https://secure-www.gapcanada.ca/',
      },
      preview: {
        baseUrl: 'https://www.wip.prod.gaptecholapps.ca/',
        secureUrl: 'https://secure.www.wip.prod.gaptecholapps.ca/',
        azeusAPIUrl: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.ca/',
      },
      'wip-stage': {
        baseUrl: 'https://www.app.stage.gaptecholapps.ca/',
      },
      stage: {
        baseUrl: 'https://www.stage.gaptechol.ca/',
        secureUrl: 'https://secure-www.stage.gaptechol.ca/',
      },
      local: {
        baseUrl: 'http://www.local.gaptechol.ca:3000/',
      },
    },
  },
  gpf: {
    us: {
      prod: {
        baseUrl: 'https://www.gapfactory.com/',
        secureUrl: 'https://secure-www.gapfactory.com/',
      },
      preview: {
        baseUrl: 'https://www.wip.prod.factory-gaptecholapps.com/',
        secureUrl: 'https://secure.www.wip.prod.factory-gaptecholapps.com/',
        azeusAPIUrl: 'https://internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.com/',
      },
      'wip-stage': {
        baseUrl: 'https://www.app.stage.factory-gaptecholapps.com/',
      },
      stage: {
        baseUrl: 'https://www.stage.factory-gaptechol.com/',
        secureUrl: 'https://secure-www.stage.factory-gaptechol.com/',
      },
      local: {
        baseUrl: 'http://gfol.local.factory-gaptechol.com:3000/',
      },
    },
  },
  on: {
    us: {
      prod: {
        baseUrl: 'https://oldnavy.gap.com/',
        secureUrl: 'https://secure-oldnavy.gap.com/',
      },
      preview: {
        baseUrl: 'https://onol.wip.prod.gaptecholapps.com/',
        secureUrl: 'https://secure.onol.wip.prod.gaptecholapps.com/',
        azeusAPIUrl: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.com/',
      },
      'wip-stage': {
        baseUrl: 'https://onol.app.stage.gaptecholapps.com/',
      },
      stage: {
        baseUrl: 'https://onol.stage.gaptechol.com/',
        secureUrl: 'https://secure-onol.stage.gaptechol.com/',
      },
      local: {
        baseUrl: 'http://onol.local.gaptechol.com:3000/',
      },
    },
    ca: {
      prod: {
        baseUrl: 'https://oldnavy.gapcanada.ca/',
        secureUrl: 'https://secure-oldnavy.gapcanada.ca/',
      },
      preview: {
        baseUrl: 'https://onol.wip.prod.gaptecholapps.ca/',
        secureUrl: 'https://secure.onol.wip.prod.gaptecholapps.ca/',
        azeusAPIUrl: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.ca/',
      },
      'wip-stage': {
        baseUrl: 'https://onol.app.stage.gaptecholapps.ca/',
      },
      stage: {
        baseUrl: 'https://onol.stage.gaptechol.ca/',
        secureUrl: 'https://secure-onol.stage.gaptechol.ca/',
      },
      local: {
        baseUrl: 'http://onol.local.gaptechol.ca:3000/',
      },
    },
  },
  at: {
    us: {
      prod: {
        baseUrl: 'https://athleta.gap.com/',
        secureUrl: 'https://secure-athleta.gap.com/',
      },
      preview: {
        baseUrl: 'https://atol.wip.prod.gaptecholapps.com/',
        secureUrl: 'https://secure.atol.wip.prod.gaptecholapps.com/',
        azeusAPIUrl: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.com/',
      },
      'wip-stage': {
        baseUrl: 'https://atol.app.stage.gaptecholapps.com/',
      },
      stage: {
        baseUrl: 'https://atol.stage.gaptechol.com/',
        secureUrl: 'https://secure-atol.stage.gaptechol.com/',
      },
      aksStage: {
        baseUrl: 'https://atol-next.aks.stage.azeus.gaptech.com/',
      },
      aksTest: {
        baseUrl: 'https://atol-next.aks.test.azeus.gaptech.com/',
      },
      local: {
        baseUrl: 'https://atol.stage.gaptechol.com/',
        secureUrl: 'https://secure-atol.stage.gaptechol.com/',
      },
    },
    ca: {
      prod: {
        baseUrl: 'https://athleta.gapcanada.ca/',
        secureUrl: 'https://secure-athleta.gapcanada.ca/',
      },
      preview: {
        baseUrl: 'https://atol.wip.prod.gaptecholapps.ca/',
        secureUrl: 'https://secure.atol.wip.prod.gaptecholapps.ca/',
        azeusAPIUrl: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.ca/',
      },
      'wip-stage': {
        baseUrl: 'https://atol.app.stage.gaptecholapps.ca/',
      },
      stage: {
        baseUrl: 'https://atol.stage.gaptechol.ca/',
        secureUrl: 'https://secure-atol.stage.gaptechol.ca/',
      },
      local: {
        baseUrl: 'http://atol.local.gaptechol.ca:3000/',
      },
    },
  },
  br: {
    us: {
      prod: {
        baseUrl: 'https://bananarepublic.gap.com/',
        secureUrl: 'https://secure-bananarepublic.gap.com/',
      },
      preview: {
        baseUrl: 'https://brol.wip.prod.gaptecholapps.com/',
        secureUrl: 'https://secure.brol.wip.prod.gaptecholapps.com/',
        azeusAPIUrl: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.com/',
      },
      'wip-stage': {
        baseUrl: 'https://brol.app.stage.gaptecholapps.com/',
      },
      stage: {
        baseUrl: 'https://brol.stage.gaptechol.com/',
        secureUrl: 'https://secure-brol.stage.gaptechol.com/',
      },
      aksTest: {
        baseUrl: 'https://brol-next.aks.test.azeus.gaptech.com',
      },
      local: {
        baseUrl: 'http://brol.local.gaptechol.com:3000/',
      },
    },
    ca: {
      prod: {
        baseUrl: 'https://bananarepublic.gapcanada.ca/',
        secureUrl: 'https://secure-bananarepublic.gapcanada.ca/',
      },
      preview: {
        baseUrl: 'https://brol.wip.prod.gaptecholapps.ca/',
        secureUrl: 'https://secure.brol.wip.prod.gaptecholapps.ca/',
        azeusAPIUrl: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.ca/',
      },
      'wip-stage': {
        baseUrl: 'https://brol.app.stage.gaptecholapps.ca/',
      },
      stage: {
        baseUrl: 'https://brol.stage.gaptechol.ca/',
        secureUrl: 'https://secure-brol.stage.gaptechol.ca/',
      },
      local: {
        baseUrl: 'http://brol.local.gaptechol.ca:3000/',
      },
    },
  },
  brf: {
    us: {
      prod: {
        baseUrl: 'https://bananarepublicfactory.gapfactory.com/',
        secureUrl: 'https://secure-bananarepublicfactory.gapfactory.com/',
      },
      preview: {
        baseUrl: 'https://brfol.wip.prod.factory-gaptecholapps.com/',
        secureUrl: 'https://secure.brfol.wip.prod.factory-gaptecholapps.com/',
        azeusAPIUrl: 'https://internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.com/',
      },
      'wip-stage': {
        baseUrl: 'https://brfol.app.stage.factory-gaptecholapps.com/',
      },
      stage: {
        baseUrl: 'https://brfol.stage.factory-gaptechol.com/',
        secureUrl: 'https://secure-brfol.stage.factory-gaptechol.com/',
      },
      local: {
        baseUrl: 'http://brfol.local.factory-gaptechol.com:3000/',
      },
    },
    ca: {
      prod: {
        baseUrl: 'https://bananarepublicfactory.gapfactory.ca/',
        secureUrl: 'https://secure-bananarepublicfactory.gapfactory.ca/',
      },
      preview: {
        baseUrl: 'https://brfol.wip.prod.factory-gaptecholapps.ca/',
        secureUrl: 'https://secure.brfol.wip.prod.factory-gaptecholapps.ca/',
        azeusAPIUrl: 'https://internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.ca/',
      },
      'wip-stage': {
        baseUrl: 'https://brfol.app.stage.factory-gaptecholapps.ca/',
      },
      stage: {
        baseUrl: 'https://brfol.stage.factory-gaptechol.ca/',
        secureUrl: 'https://secure-brfol.stage.factory-gaptechol.ca/',
      },
      local: {
        baseUrl: 'http://brfol.local.factory-gaptechol.ca:3000/',
      },
    },
  },
};

export function getBaseURL(brand: string | undefined = BRAND, market: string | undefined = MARKET, env: string | undefined = ENV): string {
  return envConfig[brand as keyof Brand]?.[market as keyof Market]?.[env as keyof Environments]?.baseUrl || '';
}

export function getSecureURL(brand: string | undefined = BRAND, market: string | undefined = MARKET, env: string | undefined = ENV): string {
  return envConfig[brand as keyof Brand]?.[market as keyof Market]?.[env as keyof Environments]?.secureUrl || '';
}

export function getAzeusAPIURL(): string {
  return envConfig[BRAND as keyof Brand]?.[MARKET as keyof Market]?.[ENV as keyof Environments]?.azeusAPIUrl || '';
}
