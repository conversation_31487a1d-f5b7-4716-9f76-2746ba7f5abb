/* eslint-disable playwright/no-wait-for-timeout */
import fs from 'fs';
import os from 'os';
import { expect, Locator, type Page } from '@playwright/test';
import { waitForHydration, scrollToBottomUntilPageDoesNotGrow, scrollToTop } from './helpers';
import test from '@/pom/base.page';
import { CategoryPage } from '@/pom/common/category.page';

const brand = process.env.BRAND;
const env = process.env.ENV;
const market = process.env.MARKET;

export const compareCanaryToLive = async (
  page: Page,
  testFilename: string,
  params?: {
    bypassNewSession?: boolean;
    maskableLocators?: Locator[];
    maxDiffPixelRatio?: number;
    waitForLocator?: Locator;
  }
) => {
  await waitForHydration(page);
  await page.addStyleTag({
    content: 'div:has(> div > div.recommendations-carousel), .pdp-reviews-widget, .mui-certona-recs-container { display: none; }',
  });
  await scrollToBottomUntilPageDoesNotGrow(page, 3000);
  await scrollToTop(page);
  await page.waitForFunction(() => window.scrollY === 0);

  params && params.waitForLocator && (await params.waitForLocator.waitFor({ timeout: 60000 }));

  const maskedLocators = [
    ...(params && params.maskableLocators ? params.maskableLocators : []),
    page.locator('video'),
    page.locator('.slick-slider'),
    page.locator(`span[data-testid="timer"]`),
    page.locator(`div[data-testid="ska-mobileExposedSearch"]`),
  ];

  const liveBuildVersion = await page.evaluate('window.gap.buildVersion.nextApp');
  test.info().annotations.push({ type: 'LIVE Build Version', description: `${liveBuildVersion!}` });
  await test.step(`LIVE Build Version: ${liveBuildVersion}`, async () => {});

  const pageSnapshot = await page.screenshot({
    fullPage: true,
    mask: maskedLocators,
    scale: 'css',
    timeout: 40000,
  });
  const filenamePrefix = `pageSnapshot-${test.info().retry}-${brand}-${market}-${env}`;
  const filename = `${filenamePrefix}-${test.info().project.name.replace(' ', '-')}-${os.platform()}.png`;
  const directory = `${testFilename}-snapshots/`;
  try {
    fs.unlinkSync(`${directory}${filename}`);
    // eslint-disable-next-line no-empty
  } catch {}

  fs.mkdirSync(directory, { recursive: true });
  fs.writeFileSync(`${directory}${filename}`, pageSnapshot);

  const preserveredUrl = page.url();
  await page.setExtraHTTPHeaders({ 'chartis-force-route': 'canary' });
  const parsedPreserveredUrl = new URL(preserveredUrl);
  parsedPreserveredUrl.searchParams.set('breakakcache', `${Math.floor(Math.random() * 100000)}`);

  if (params && !params.bypassNewSession) {
    const cookies = await page.context().cookies();
    const ABSegCookie = cookies.find(cookie => cookie.name === 'ABSeg')!;
    const unknownShopperIdCookie = cookies.find(cookie => cookie.name === 'unknownShopperId')!;
    // Clear cookies
    await page.context().clearCookies();
    // Clear local storage
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    await page.context().addCookies([ABSegCookie, unknownShopperIdCookie]);
    await page.goto(parsedPreserveredUrl.href);
    if (test.info().titlePath.includes('Category Page')) {
      await waitForHydration(page);
      await new CategoryPage(page).goToPriceHighLowSort();
    }
  } else {
    await page.goto(parsedPreserveredUrl.href);
  }

  const canaryBuildVersion = await page.evaluate('window.gap.buildVersion.nextApp');
  test.info().annotations.push({ type: 'CANARY Build Version', description: `${canaryBuildVersion!}` });
  await test.step(`CANARY Build Version: ${canaryBuildVersion}`, async () => {});

  await waitForHydration(page);
  await page.addStyleTag({ content: 'div:has(> div > div.recommendations-carousel), .pdp-reviews-widget { display: none; }' });
  await scrollToBottomUntilPageDoesNotGrow(page, 3000);
  await scrollToTop(page);
  await page.waitForFunction(() => window.scrollY === 0);

  params && params.waitForLocator && (await params.waitForLocator.waitFor({ timeout: 60000 }));

  await expect(page).toHaveScreenshot(`${filenamePrefix}.png`, {
    maxDiffPixelRatio: params && params.maxDiffPixelRatio ? params.maxDiffPixelRatio : 0.1,
    fullPage: true,
    mask: maskedLocators,
  });
};
